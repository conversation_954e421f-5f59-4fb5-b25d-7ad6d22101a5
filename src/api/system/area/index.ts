import request from '@/config/axios'

export interface AreaVO {
  label: string;
  value: number;
}
// 获得地区树
export const getAreaTree = async () => {
  return await request.get({ url: '/system/area/tree' })
}

// 获得地区树-省市
export const getAreaTreeCity = async () => {
  return await request.get({ url: '/system/area/treeCity' })
}

export const getOrderCity = async () => {
  return await request.get({ url: '/system/area/orderCity' })
}
export const getOrderCityArea = async () => {
  return await request.get({ url: '/system/area/orderCityArea' })
}

// 获得 IP 对应的地区名
export const getAreaByIp = async (ip: string) => {
  return await request.get({ url: '/system/area/get-by-ip?ip=' + ip })
}
