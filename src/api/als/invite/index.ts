import request from '@/config/axios'

// 邀请 VO
export interface InviteVO {
  inviteId: number // 邀请ID
  memberId: number // 用户ID
  inviteMemberId: number // 邀请人ID
  inviteMemberType: number // 邀请人类型
  inviteTime: Date // 邀请时间
  awardStatus: number // 状态
  price: number // 现金
  walletTransactionId: number // 钱包流水ID
  lessonPeriod: number // 课时数
  customerPackageId: number // 课时包ID
  remark: string // 备注
}

// 邀请 API
export const InviteApi = {
  // 查询邀请分页
  getInvitePage: async (params: any) => {
    return await request.get({ url: `/als/invite/page`, params })
  },

  // 查询邀请详情
  getInvite: async (id: number) => {
    return await request.get({ url: `/als/invite/get?id=` + id })
  },

  // 新增邀请
  createInvite: async (data: InviteVO) => {
    return await request.post({ url: `/als/invite/create`, data })
  },

  // 修改邀请
  updateInvite: async (data: InviteVO) => {
    return await request.put({ url: `/als/invite/update`, data })
  },

  // 删除邀请
  deleteInvite: async (id: number) => {
    return await request.delete({ url: `/als/invite/delete?id=` + id })
  },

  // 导出邀请 Excel
  exportInvite: async (params) => {
    return await request.download({ url: `/als/invite/export-excel`, params })
  }
}