import request from '@/config/axios'

// 问题反馈 VO
export interface FeedbackVO {
  feedbackId: number // 反馈ID
  memberType: number // 用户类型
  memberId: number // 用户ID
  feedbackType: number // 反馈类型
  title: string // 反馈标题
  content: string // 反馈内容
  picUrl: string // 图片地址
  feedbackStatus: number // 状态
  remark: string // 备注
}

// 问题反馈 API
export const FeedbackApi = {
  // 查询问题反馈分页
  getFeedbackPage: async (params: any) => {
    return await request.get({ url: `/als/feedback/page`, params })
  },

  // 查询问题反馈详情
  getFeedback: async (id: number) => {
    return await request.get({ url: `/als/feedback/get?id=` + id })
  },

  // 新增问题反馈
  createFeedback: async (data: FeedbackVO) => {
    return await request.post({ url: `/als/feedback/create`, data })
  },

  // 修改问题反馈
  updateFeedback: async (data: FeedbackVO) => {
    return await request.put({ url: `/als/feedback/update`, data })
  },

  // 删除问题反馈
  deleteFeedback: async (id: number) => {
    return await request.delete({ url: `/als/feedback/delete?id=` + id })
  },

  // 导出问题反馈 Excel
  exportFeedback: async (params) => {
    return await request.download({ url: `/als/feedback/export-excel`, params })
  }
}