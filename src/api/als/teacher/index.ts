import request from '@/config/axios'

// 老师类 VO
export interface TeacherVO {
  teacherId: number // 老师ID
  teacherName: string // 老师姓名
  teacherPhone: string // 手机号
  teacherSex: number // 性别
  wechat: string // 微信号
  qq: string // QQ
  idNumber: string // 身份证号
  birth: Date // 出生月份
  politicalStatus: number // 政治面貌
  nativeAreaId: number // 籍贯
  orderCityId: number // 接单城市
  orderAreaId: string // 接单区域
  universityName: string // 大学名称
  campus: string // 校区
  universityCityId: number // 高校城市
  schoolStatus: number // 在校状态
  profession: string // 专业
  degree: number // 学历
  entryYear: number // 入学年份
  workCertificateNo: string // 上岗证编号
  teachScope: string // 授课范围
  teachTimeRange: string // 时间范围
  expValue: number // 经验值
  creditValue: number // 信用值
  startOrderTime: Date // 开始接单日期
  isHaveExperience: number // 有无经验
  address: string // 本市现住址
  acceptableTime: number // 可接受单程车程
  registerTime: Date // 注册时间
  teacherChannel: number // 来源渠道
  trackingTime: Date // 跟踪时间
  trackingRemark: string // 跟踪备注
  orderTimes: number // 抢单次数
  auditTime: Date // 审核时间
  isEnable: number // 是否启用
  isAcceptOrder: number // 是否可接单
  idTags: string // 身份标签
  openId: string // openId
  lastServiceTime: Date // 最后服务时间
  lastActiveTime: Date // 最后登录时间
  acceptOrderTime: Date // 最近抢单时间
  operationRemark: string // 运营备注
  interviewTime: Date // 预约面试时间
}

// 老师类 API
export const TeacherApi = {
  // 查询老师类分页
  getTeacherPage: async (params: any) => {
    return await request.get({ url: `/als/teacher/page`, params })
  },

  // 查询老师类分页
  getBindTeacherPage: async (params: any) => {
    return await request.get({ url: `/als/teacher/bindPage`, params })
  },

  // 查询老师类分页
  queryByKeywords: async (params: any) => {
    return await request.get({ url: `/als/teacher/queryByKeywords`, params })
  },


  // 查询老师类详情
  getTeacher: async (id: number) => {
    return await request.get({ url: `/als/teacher/get?id=` + id })
  },

  // 查询老师陪学详情
  getDetail: async (id: number) => {
    return await request.get({ url: `/als/teacher/detail?id=` + id })
  },

  // 新增老师类
  createTeacher: async (data: TeacherVO) => {
    return await request.post({ url: `/als/teacher/create`, data })
  },

  // 修改老师类
  updateTeacher: async (data: TeacherVO) => {
    return await request.put({ url: `/als/teacher/update`, data })
  },

  // 删除老师类
  deleteTeacher: async (id: number) => {
    return await request.delete({ url: `/als/teacher/delete?id=` + id })
  },

  // 导出老师类 Excel
  exportTeacher: async (params) => {
    return await request.download({ url: `/als/teacher/export-excel`, params })
  },

  // 查询老师简历
  getResume: async (id: number) => {
    return await request.get({ url: `/als/teacher/getResume?id=` + id })
  },

  // 更新坐标
  updateLocal: async (id: number) => {
    return await request.get({ url: `/als/teacher/updateLocal?id=` + id })
  },

}
