import request from '@/config/axios'

export interface ServiceFailedVo {
  customerName: string // 家长姓名
  leaveReason: string // 请假原因
  changeReason: string // 换老师原因
  serviceRemark: string // 正在服务备注
  zeroReason: string// 课时用尽原因
}

export interface ServiceVo {
  customerName: string // 家长姓名
  serviceClassHour: string // 服务课时数
  lessonRecordNum: string // 上门次数
  goodNum: string // 好评次数
  mediumNum: string // 好评次数
  badNum: string // 差评次数
  leaveNum: string // 请假次数
  leaveRadio: string // 请假率
  childInfo: string // 孩子阶段+孩子学校+陪学类型
  lastServiceTime: string // 最后服务时间
}
