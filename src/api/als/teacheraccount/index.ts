import request from '@/config/axios'

// 老师账户 VO
export interface TeacherAccountVO {
  teacherAccountId: number // 账户ID
  teacherId: number // 老师ID
  balance: number // 余额
  isCashOut: number // 是否可提现
}

// 老师账户 API
export const TeacherAccountApi = {
  // 查询老师账户分页
  getTeacherAccountPage: async (params: any) => {
    return await request.get({ url: `/als/teacher-account/page`, params })
  },

  // 查询老师账户详情
  getTeacherAccount: async (id: number) => {
    return await request.get({ url: `/als/teacher-account/get?id=` + id })
  },

  // 新增老师账户
  createTeacherAccount: async (data: TeacherAccountVO) => {
    return await request.post({ url: `/als/teacher-account/create`, data })
  },

  // 修改老师账户
  updateTeacherAccount: async (data: TeacherAccountVO) => {
    return await request.put({ url: `/als/teacher-account/update`, data })
  },

  // 删除老师账户
  deleteTeacherAccount: async (id: number) => {
    return await request.delete({ url: `/als/teacher-account/delete?id=` + id })
  },

  // 导出老师账户 Excel
  exportTeacherAccount: async (params) => {
    return await request.download({ url: `/als/teacher-account/export-excel`, params })
  }
}