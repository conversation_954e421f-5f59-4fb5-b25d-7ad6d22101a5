import request from '@/config/axios'

// 大学信息 VO
export interface UniversityVO {
  universityId: number // 大学ID
  universityName: string // 大学名称
  universityTag: string // 标签
  logo: string // 校徽
  logoNew: string // 校徽新地址
  province: string // 所在省份
  location: string // 城市
}

// 大学信息 API
export const UniversityApi = {
  // 查询大学信息分页
  getUniversityPage: async (params: any) => {
    return await request.get({ url: `/als/university/page`, params })
  },

  // 查询大学信息详情
  getUniversity: async (id: number) => {
    return await request.get({ url: `/als/university/get?id=` + id })
  },

  // 新增大学信息
  createUniversity: async (data: UniversityVO) => {
    return await request.post({ url: `/als/university/create`, data })
  },

  // 修改大学信息
  updateUniversity: async (data: UniversityVO) => {
    return await request.put({ url: `/als/university/update`, data })
  },

  // 删除大学信息
  deleteUniversity: async (id: number) => {
    return await request.delete({ url: `/als/university/delete?id=` + id })
  },

  // 导出大学信息 Excel
  exportUniversity: async (params) => {
    return await request.download({ url: `/als/university/export-excel`, params })
  }
}