import request from '@/config/axios'

// 已购课时包记录 VO
export interface CustomerPackageVO {
  customerPackageId: number // 购买记录ID
  customerId: number // 家长ID
  customerName: string // 家长姓名
  customerPhone: string // 家长手机号
  headMarketUser: string // 运营负责人
  headOperateUser: string // 市场负责人
  packageName: string // 课时包名称
  packageType: number // 课时包类型
  lessonPeriod: number // 总课时数
  actualAmount: number // 总课时数
  lessonPeriodUsed: number // 已使用课时数
  lessonPeriodRemain: number // 剩余课时
  useStatus: number // 使用状态
  buyTimes: number // 第几次购买
  customerAddApplyId: number // 添加课时申请ID
}

// 已购课时包记录 API
export const CustomerPackageApi = {
  // 查询已购课时包记录分页
  getCustomerPackagePage: async (params: any) => {
    return await request.get({ url: `/als/customer-package/page`, params })
  },

  // 查询已购课时包记录详情
  getCustomerPackage: async (id: number) => {
    return await request.get({ url: `/als/customer-package/get?id=` + id })
  },

  // 新增已购课时包记录
  createCustomerPackage: async (data: CustomerPackageVO) => {
    return await request.post({ url: `/als/customer-package/create`, data })
  },

  // 修改已购课时包记录
  updateCustomerPackage: async (data: CustomerPackageVO) => {
    return await request.put({ url: `/als/customer-package/update`, data })
  },

  // 删除已购课时包记录
  deleteCustomerPackage: async (id: number) => {
    return await request.delete({ url: `/als/customer-package/delete?id=` + id })
  },

  // 导出已购课时包记录 Excel
  exportCustomerPackage: async (params) => {
    return await request.download({ url: `/als/customer-package/export-excel`, params })
  }
}
