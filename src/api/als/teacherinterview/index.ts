import request from '@/config/axios'

// 老师面试 VO
export interface TeacherInterviewVO {
  teacherInterviewId: number // 主键
  teacherId: number // 老师ID
  teacherName: string // 老师姓名
  teacherPhone: string // 手机
  level: number // 老师等级
  interviewer: number // 面试官
  interviewerName: string // 面试官
  interviewerEvaluate: string // 面试官评价
  interviewTime: Date // 面试时间
  teacherRemark: string // 师资备注
  qualityBasic: string // 基本素质
  qualityComprehensive: string // 综合素质评分
  qualityLecture: string // 试讲评分
  finallyScore: number // 综合评分
}

// 老师面试 API
export const TeacherInterviewApi = {
  // 查询老师面试分页
  getTeacherInterviewPage: async (params: any) => {
    return await request.get({ url: `/als/teacher-interview/page`, params })
  },

  // 查询老师面试详情
  getTeacherInterview: async (id: number) => {
    return await request.get({ url: `/als/teacher-interview/get?id=` + id })
  },

  // 预约面试
  reservationInterview: async (data: TeacherInterviewVO) => {
    return await request.post({ url: `/als/teacher-interview/reservation`, data })
  },

  // 新增老师面试
  createTeacherInterview: async (data: TeacherInterviewVO) => {
    return await request.post({ url: `/als/teacher-interview/create`, data })
  },

  // 修改老师面试
  updateTeacherInterview: async (data: TeacherInterviewVO) => {
    return await request.put({ url: `/als/teacher-interview/update`, data })
  },

  // 删除老师面试
  deleteTeacherInterview: async (id: number) => {
    return await request.delete({ url: `/als/teacher-interview/delete?id=` + id })
  },

  // 导出老师面试 Excel
  exportTeacherInterview: async (params) => {
    return await request.download({ url: `/als/teacher-interview/export-excel`, params })
  }
}
