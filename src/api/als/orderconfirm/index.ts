import request from '@/config/axios'

// 接单确认 VO
export interface OrderConfirmVO {
  orderConfirmId: number // 主键
  orderId: number // 订单ID
  customerId: number // 家长ID
  customerName: string // 家长
  teacherId: number // 老师ID
  teacherName: string // 老师
  dealStatus: number // 处理状态
  rejectReasonId: number // 拒绝原因ID
  customReason: string // 自定义拒绝原因ID
  dealTime: Date // 处理时间
  dealUserId: number // 处理人
}

export interface RejectReqVO {
  orderConfirmId: number | undefined // 主键
  rejectReasonId: number  | undefined// 拒绝原因ID
  customReason: string  | undefined// 自定义拒绝原因
}

// 接单确认 API
export const OrderConfirmApi = {
  // 查询接单确认分页
  getOrderConfirmPage: async (params: any) => {
    return await request.get({ url: `/als/order-confirm/page`, params })
  },

  // 查询接单确认详情
  getOrderConfirm: async (id: number) => {
    return await request.get({ url: `/als/order-confirm/get?id=` + id })
  },

  // 新增接单确认
  createOrderConfirm: async (data: OrderConfirmVO) => {
    return await request.post({ url: `/als/order-confirm/create`, data })
  },

  // 修改接单确认
  updateOrderConfirm: async (data: OrderConfirmVO) => {
    return await request.put({ url: `/als/order-confirm/update`, data })
  },

  // 删除接单确认
  deleteOrderConfirm: async (id: number) => {
    return await request.delete({ url: `/als/order-confirm/delete?id=` + id })
  },

  // 导出接单确认 Excel
  exportOrderConfirm: async (params) => {
    return await request.download({ url: `/als/order-confirm/export-excel`, params })
  },

  // 确认接单
  confirm: async (id: number) => {
    return await request.get({ url: `/als/order-confirm/confirm?id=` + id })
  },

  // 拒绝接单
  reject: async (data: RejectReqVO) => {
    return await request.post({ url: `/als/order-confirm/reject`, data })
  }
}
