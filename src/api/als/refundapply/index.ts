import request from '@/config/axios'
import {AuditFormData} from "@/api/als/audit";

// 家长退款申请 VO
export interface RefundApplyVO {
  refundApplyId: number // 退款ID
  customerPackageId: number // 购买记录ID
  customerId: number // 家长ID
  refundAmount: number // 退款金额
  refundReasonType: number // 退款理由选择
  refundReason: string // 退款理由
  refundStatus: number // 退款状态
  dealUserId: number // 处理人
  refundType: number // 退款种类
  remark: string // 备注
  remarkTime: Date // 备注时间
  remarkUserId: number // 备注人
  replay: string // 解决方案及复盘
}

export interface AuditRefundApplyVO{
  auditStatus: number
  auditRemark: string
  refundApplyId: number
}

// 家长退款申请 API
export const RefundApplyApi = {
  // 查询家长退款申请分页
  getRefundApplyPage: async (params: any) => {
    return await request.get({ url: `/als/refund-apply/page`, params })
  },

  // 查询家长退款申请详情
  getRefundApply: async (id: number) => {
    return await request.get({ url: `/als/refund-apply/get?id=` + id })
  },

  // 新增家长退款申请
  createRefundApply: async (data: RefundApplyVO) => {
    return await request.post({ url: `/als/refund-apply/create`, data })
  },

  // 修改家长退款申请
  updateRefundApply: async (data: RefundApplyVO) => {
    return await request.put({ url: `/als/refund-apply/update`, data })
  },

  // 删除家长退款申请
  deleteRefundApply: async (id: number) => {
    return await request.delete({ url: `/als/refund-apply/delete?id=` + id })
  },

  // 导出家长退款申请 Excel
  exportRefundApply: async (params) => {
    return await request.download({ url: `/als/refund-apply/export-excel`, params })
  },
  // 审核
  auditSubmit: async (data: AuditFormData) => {
    return await request.put({ url: `/als/refund-apply/audit`, data })
  }
}
