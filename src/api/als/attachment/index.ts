import request from '@/config/axios'

// 附件 VO
export interface AttachmentVO {
  attachmentId: number // 附件ID
  attachmentType: number // 附件类型
  bizType: number // 业务类型
  bizId: number // 业务ID
  url: string // 附件url
}

export interface TeacherAttachmentVo{
  teacherId: number | undefined

  // 1个人照片 2身份证 3学生证 4毕业证书 5学信网学籍 6四级证书 7六级证书 8教师资格证 9钢琴证书 10合作协议 11承诺书 12上岗证 13其他
  image1Url: string | undefined // 个人照片
  image2Url: string | undefined // 身份证
  image3Url: string | undefined // 学生证
  image4Url: string | undefined // 毕业证书
  image5Url: string | undefined // 学信网学籍
  image6Url: string | undefined // 四级证书
  image7Url: string | undefined // 六级证书
  image8Url: string | undefined // 教师资格证
  image9Url: string | undefined // 钢琴证书
  image10Url: string | undefined // 合作协议
  image11Url: string | undefined // 承诺书
  image12Url: string | undefined // 上岗证
  image13Url: string | undefined // 雅思
  image14Url: string | undefined // 托福
  image15Url: string | undefined // 其他

  image1Id : number | undefined // 个人照片
  image2Id : number | undefined // 身份证
  image3Id : number | undefined // 学生证
  image4Id : number | undefined // 毕业证书
  image5Id : number | undefined // 学信网学籍
  image6Id : number | undefined // 四级证书
  image7Id : number | undefined // 六级证书
  image8Id : number | undefined // 教师资格证
  image9Id : number | undefined // 钢琴证书
  image10Id :  number | undefined // 合作协议
  image11Id :  number | undefined // 承诺书
  image12Id :  number | undefined // 上岗证
  image13Id :  number | undefined // 雅思
  image14Id :  number | undefined // 托福
  image15Id :  number | undefined // 其他
}

// 附件 API
export const AttachmentApi = {
  // 查询附件分页
  getAttachmentPage: async (params: any) => {
    return await request.get({ url: `/als/attachment/page`, params })
  },

  // 查询附件详情
  getAttachment: async (id: number) => {
    return await request.get({ url: `/als/attachment/get?id=` + id })
  },

  // 新增附件
  createAttachment: async (data: AttachmentVO) => {
    return await request.post({ url: `/als/attachment/create`, data })
  },

  // 修改附件
  updateAttachment: async (data: AttachmentVO) => {
    return await request.put({ url: `/als/attachment/update`, data })
  },

  // 删除附件
  deleteAttachment: async (id: number) => {
    return await request.delete({ url: `/als/attachment/delete?id=` + id })
  },

  // 导出附件 Excel
  exportAttachment: async (params) => {
    return await request.download({ url: `/als/attachment/export-excel`, params })
  },

  updateTeacherAttachment: async (data: TeacherAttachmentVo) => {
    return await request.put({ url: `/als/teacher/uploadAttachment`, data })
  },

  queryTeacherAttachment: async (id: number) => {
    return await request.get({ url: `/als/teacher/queryAttachment?id=` + id })
  },
}
