import request from '@/config/axios'
import {AuditFormData} from "@/api/als/audit";

// 课时添加申请 VO
export interface CustomerAddApplyVO {
  customerAddApplyId: number // 申请ID
  customerId: number // 家长ID
  packageName: string // 课时包名称
  packageType: number // 课时包类型
  discountAmount: number // 优惠金额
  actualAmount: number // 实付金额
  applyMethod: number // 支付方式
  blockAmount: number // 封存流转金额
  directionFrom: number // 资金流转来源
  applyReason: string // 申请理由
  lessonPeriod: number // 课时数
  salePrice: number // 售价
  auditStatus: number // 审核状态
  creatorName: string // 申请人姓名
  auditUserName: string // 审核人姓名
}

// 课时添加申请 API
export const CustomerAddApplyApi = {
  // 查询课时添加申请分页
  getCustomerAddApplyPage: async (params: any) => {
    return await request.get({ url: `/als/customer-add-apply/page`, params })
  },

  // 查询课时添加申请详情
  getCustomerAddApply: async (id: number) => {
    return await request.get({ url: `/als/customer-add-apply/get?id=` + id })
  },

  // 新增课时添加申请
  createCustomerAddApply: async (data: CustomerAddApplyVO) => {
    return await request.post({ url: `/als/customer-add-apply/create`, data })
  },

  // 修改课时添加申请
  updateCustomerAddApply: async (data: CustomerAddApplyVO) => {
    return await request.put({ url: `/als/customer-add-apply/update`, data })
  },

  // 删除课时添加申请
  deleteCustomerAddApply: async (id: number) => {
    return await request.delete({ url: `/als/customer-add-apply/delete?id=` + id })
  },

  // 导出课时添加申请 Excel
  exportCustomerAddApply: async (params) => {
    return await request.download({ url: `/als/customer-add-apply/export-excel`, params })
  },

  // 审核
  auditSubmit: async (data: AuditFormData) => {
    return await request.put({ url: `/als/customer-add-apply/audit`, data })
  }

}
