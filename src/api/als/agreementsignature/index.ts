import request from '@/config/axios'

// 协议签署记录 VO
export interface AgreementSignatureVO {
  agreementSignatureId: number // 主键ID
  agreementId: number // 协议ID
  agreementKey: string // 协议唯一标识
  agreementVersion: string // 签署时的协议版本
  userId: number // 用户ID
  signatureUrl: string // 签名图片URL
  ipAddress: string // 签署IP地址
  userAgent: string // 用户代理信息
  signedAt: Date // 签署时间
}

// 协议签署记录 API
export const AgreementSignatureApi = {
  // 查询协议签署记录分页
  getAgreementSignaturePage: async (params: any) => {
    return await request.get({ url: `/als/agreement-signature/page`, params })
  },

  // 查询协议签署记录详情
  getAgreementSignature: async (id: number) => {
    return await request.get({ url: `/als/agreement-signature/get?id=` + id })
  },

  // 新增协议签署记录
  createAgreementSignature: async (data: AgreementSignatureVO) => {
    return await request.post({ url: `/als/agreement-signature/create`, data })
  },

  // 修改协议签署记录
  updateAgreementSignature: async (data: AgreementSignatureVO) => {
    return await request.put({ url: `/als/agreement-signature/update`, data })
  },

  // 删除协议签署记录
  deleteAgreementSignature: async (id: number) => {
    return await request.delete({ url: `/als/agreement-signature/delete?id=` + id })
  },

  // 导出协议签署记录 Excel
  exportAgreementSignature: async (params) => {
    return await request.download({ url: `/als/agreement-signature/export-excel`, params })
  }
}