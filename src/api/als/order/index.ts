import request from '@/config/axios'

// 陪学订单 VO
export interface OrderVO {
  orderId: number // 主键
  orderNo: string // 订单编号
  orderType: number // 订单类型
  customerId: number // 家长ID
  customerPhone: string // 手机号
  orderAreaId: number // 区域
  orderAreaName: string // 区域名称
  orderAddress: string // 详细地址
  parseAddress: string // 解析地址
  isSuspend: number // 是否暂停接单
  isGroupChat: number // 是否已建群
  orderRemarkExtra: string // 跟踪备注补充
  headCurrent: number // 当前负责人
  headOperate: number  // 运营负责人
  headMarket: number  // 市场负责人
  orgNeedsContent: string // 原家长陪学需求
  requireSex: number // 老师性别要求
  hardRequireAbility: string // 老师能力硬性要求
  requireAbilityExtra: string // 老师能力要求补充
  needsTags: string // 需求标签
  needsFocusTags: string // 需求侧重点
  demandContent: string // 陪学要求
  timesWeek: number // 周次
  isOnWeekend: number // 是否是周末订单
  timeRange: string // 陪学时间范围
  trackingTime: Date // 跟踪时间
  expTime: Date // 体验时间
  isConfirmExpTime: number // 是否确认体验时间
  kidSex: number // 孩子性别
  kidStage: number // 孩子年级阶段
  communicatePre: string // 沟通前提条件
  communicateResult: number // 沟通结果
  whyNeed: string // 为什么需要陪学
  primaryEducator: string // 主要教育者
  kidNickName: string // 孩子称呼
  relationship: string // 家长与孩子关系
  schoolName: string // 学校名称
  schoolNature: number // 学校性质
  ranking: number // 大致排名
  kidChr: string // 孩子性格
  kidChrExtra: string // 孩子性格补充
  kidInt: string // 孩子兴趣
  kidIntExtra: string // 孩子兴趣补充
  inviterId: number // 邀请人
}

export interface AddTrackOrderVO {
  orderId: number // 主键
  trackingTime: Date // 跟踪时间
}

export interface AddTeacherOrderVO {
  orderId: number // 主键
  addReason: string // 跟踪时间
}

export interface ChangeTeacherOrderVO {
  orderId: number // 主键
  changeReason: string // 被换原因
  changeTeacherId: number // 被换老师ID
  goodComment: string // 好评
  badComment: string // 差评
}

export interface LocationsVO{
   longitude: string
   latitude: string
   orderId: number
   releaseTime: Date
   orderAddress: string
   customerId: number
   customerName: string
   customerPhone: string
   lessonPeriodRemain: string
   headOperateName: string
   confirmCount: number
   sourceChannel: string
   sourceChannelDesc: string
   requireSexDesc: string
   demandContent: string
}

// 陪学订单 API
export const OrderApi = {
  // 查询陪学订单分页
  getOrderPage: async (params: any) => {
    return await request.get({ url: `/als/order/page`, params })
  },

  // 查询陪学订单详情
  getOrder: async (id: number) => {
    return await request.get({ url: `/als/order/get?id=` + id })
  },

  // 新增陪学订单
  createOrder: async (data: OrderVO) => {
    return await request.post({ url: `/als/order/create`, data })
  },

  // 修改陪学订单
  updateOrder: async (data: OrderVO) => {
    return await request.put({ url: `/als/order/update`, data })
  },

  // 删除陪学订单
  deleteOrder: async (id: number) => {
    return await request.delete({ url: `/als/order/delete?id=` + id })
  },

  // 更新坐标
  updateLocal: async (id: number) => {
    return await request.get({ url: `/als/order/updateLocal?id=` + id })
  },

  // 审核订单
  auditOrder: async (id: number) => {
    return await request.get({ url: `/als/order/audit?id=` + id })
  },

  // 暂停订单
  pauseOrder: async (id: number) => {
    return await request.get({ url: `/als/order/pause?id=` + id })
  },
  // 开启接单
  startOrder: async (id: number) => {
    return await request.get({ url: `/als/order/start?id=` + id })
  },

  // 发布陪学订单
  releaseOrder: async (id: number) => {
    return await request.get({ url: `/als/order/release?id=` + id })
  },

  // 导出陪学订单 Excel
  exportOrder: async (params) => {
    return await request.download({ url: `/als/order/export-excel`, params })
  },

  // 添加跟踪时间
  addTrackDate: async (data: AddTrackOrderVO) => {
    return await request.put({ url: `/als/order/addTrackDate`, data })
  },
  // 添加老师
  addTeacher: async (data: AddTeacherOrderVO) => {
    return await request.put({ url: `/als/order/addTeacher`, data })
  },
  // 换老师
  changeTeacher: async (data: ChangeTeacherOrderVO) => {
    return await request.put({ url: `/als/order/changeTeacher`, data })
  },

  getOrderMap: async (params: any) => {
    return await request.get({ url: `/als/order/map`, params })
  },
}
