import request from '@/config/axios'

// 常见问题解答 VO
export interface FaqVO {
  faqId: number // 常见问题ID
  faqWho: number // 可见方
  faqType: number // 问题分类
  faqQuestion: string // 问题
  faqAnswer: string // 解答
  faqStatus: number // 状态
  remark: string // 备注
}

// 常见问题解答 API
export const FaqApi = {
  // 查询常见问题解答分页
  getFaqPage: async (params: any) => {
    return await request.get({ url: `/als/faq/page`, params })
  },

  // 查询常见问题解答详情
  getFaq: async (id: number) => {
    return await request.get({ url: `/als/faq/get?id=` + id })
  },

  // 新增常见问题解答
  createFaq: async (data: FaqVO) => {
    return await request.post({ url: `/als/faq/create`, data })
  },

  // 修改常见问题解答
  updateFaq: async (data: FaqVO) => {
    return await request.put({ url: `/als/faq/update`, data })
  },

  // 删除常见问题解答
  deleteFaq: async (id: number) => {
    return await request.delete({ url: `/als/faq/delete?id=` + id })
  },

  // 导出常见问题解答 Excel
  exportFaq: async (params) => {
    return await request.download({ url: `/als/faq/export-excel`, params })
  }
}