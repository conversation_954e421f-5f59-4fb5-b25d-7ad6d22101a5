import request from '@/config/axios'

// 评价 VO
export interface EvaluationVO {
  evaluationId: number // 评价ID
  customerId: number // 家长ID
  teacherId: number // 老师ID
  score: number // 评分
  level: string // 评分等级
  module: number // 模块
  content: string // 评价内容
  dealStatus: number // 处理状态
  dealUserId: number // 处理人
  remark: string // 跟踪备注
  remarkTime: Date // 备注时间
}

// 评价 API
export const EvaluationApi = {
  // 查询评价分页
  getEvaluationPage: async (params: any) => {
    return await request.get({ url: `/als/evaluation/page`, params })
  },

  // 查询评价详情
  getEvaluation: async (id: number) => {
    return await request.get({ url: `/als/evaluation/get?id=` + id })
  },

  // 新增评价
  createEvaluation: async (data: EvaluationVO) => {
    return await request.post({ url: `/als/evaluation/create`, data })
  },

  // 修改评价
  updateEvaluation: async (data: EvaluationVO) => {
    return await request.put({ url: `/als/evaluation/update`, data })
  },

  // 处理评价
  dealEvaluation: async (data: EvaluationVO) => {
    return await request.put({ url: `/als/evaluation/deal`, data })
  },

  // 删除评价
  deleteEvaluation: async (id: number) => {
    return await request.delete({ url: `/als/evaluation/delete?id=` + id })
  },

  // 导出评价 Excel
  exportEvaluation: async (params) => {
    return await request.download({ url: `/als/evaluation/export-excel`, params })
  }
}
