import request from '@/config/axios'
import {AuditFormData} from "@/api/als/audit";

// 绑定 VO
export interface BindVO {
  bindId: number // 绑定表ID
  teacherId: number // 老师ID
  customerId: number // 家长ID
  bindUserId: number // 绑定人
  unbindApplyTime: Date // 解绑申请时间
  bindStatus: number // 绑定状态
  orderId: number // 订单ID
  bindTime: Date // 绑定时间
  unbindTime: Date // 解绑时间
  unbindUserId: number // 解绑人
  unbindReason: string // 解绑原因
  unbindAuditStatus: number // 解绑审核状态
}

export interface UnbindAuditReqVO{
  id: number;
  auditStatus: number;
  auditRemark: string;
}

export interface FormData {
  teacherId: number | undefined;
  customerId: number | undefined;
  customerName: number | undefined;
  orderId: number | undefined;
  isAuthenticated : number | undefined; // 是否已认证家长需求
  isGroupChat : number | undefined;
  lessonType : number | undefined;
  promisedLastServiceDate: Date | undefined;
  isHaveExperience: number | undefined;
  experienceExtra: string | undefined;
}
export interface QueryParams{
  pageNo: number;
  pageSize: number;
  customerId: number | undefined;
  teacherId: number | undefined;
  teacherName: string | undefined;
  teacherPhone: string | undefined;
}

// 绑定 API
export const BindApi = {
  // 查询绑定分页
  getBindPage: async (params: any) => {
    return await request.get({ url: `/als/bind/page`, params })
  },

  // 查询绑定详情
  getBind: async (id: number) => {
    return await request.get({ url: `/als/bind/get?id=` + id })
  },

  // 新增绑定
  createBind: async (data: BindVO) => {
    return await request.post({ url: `/als/bind/create`, data })
  },

  // 修改绑定
  updateBind: async (data: BindVO) => {
    return await request.put({ url: `/als/bind/update`, data })
  },

  // 删除绑定
  deleteBind: async (id: number) => {
    return await request.delete({ url: `/als/bind/delete?id=` + id })
  },

  // 解绑申请
  unbind: async (id: number) => {
    return await request.get({ url: `/als/bind/unbind?id=` + id })
  },

  // 解绑审核
  unbindAudit: async (data: AuditFormData) => {
    return await request.put({ url: `/als/bind/unbindAudit`, data})
  },

  // 导出绑定 Excel
  exportBind: async (params) => {
    return await request.download({ url: `/als/bind/export-excel`, params })
  }
}
