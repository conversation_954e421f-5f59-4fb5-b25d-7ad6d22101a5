import request from '@/config/axios'
import {AuditFormData} from "@/api/als/audit";

// 提现申请 VO
export interface WithdrawApplyVO {
  withdrawApplyId: number // 主键
  memberId: number // 会员ID
  amount: number // 提现金额
  fee: number // 提现手续费
  type: number // 提现类型
  accountNo: string // 账号
  accountQrCodeUrl: string // 收款码
  applyTime: Date // 申请时间
  auditStatus: number // 审核状态
  auditTime: Date // 审核时间
  auditUserId: number // 审核人
  auditRemark: string // 审核备注
  withdrawStatus: number // 提现状态
}

// 提现申请 API
export const WithdrawApplyApi = {
  // 查询提现申请分页
  getWithdrawApplyPage: async (params: any) => {
    return await request.get({ url: `/als/withdraw-apply/page`, params })
  },

  // 查询提现申请详情
  getWithdrawApply: async (id: number) => {
    return await request.get({ url: `/als/withdraw-apply/get?id=` + id })
  },

  // 新增提现申请
  createWithdrawApply: async (data: WithdrawApplyVO) => {
    return await request.post({ url: `/als/withdraw-apply/create`, data })
  },

  // 修改提现申请
  updateWithdrawApply: async (data: WithdrawApplyVO) => {
    return await request.put({ url: `/als/withdraw-apply/update`, data })
  },

  // 审核
  audit: async (data: AuditFormData) => {
    return await request.put({ url: `/als/withdraw-apply/audit`, data })
  },

  // 支付
  pay: async (id: number) => {
    return await request.delete({ url: `/als/withdraw-apply/pay?id=` + id })
  },

  // 删除提现申请
  deleteWithdrawApply: async (id: number) => {
    return await request.delete({ url: `/als/withdraw-apply/delete?id=` + id })
  },

  // 导出提现申请 Excel
  exportWithdrawApply: async (params) => {
    return await request.download({ url: `/als/withdraw-apply/export-excel`, params })
  }
}
