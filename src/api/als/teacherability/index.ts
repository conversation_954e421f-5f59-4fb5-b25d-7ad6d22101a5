import request from '@/config/axios'

// 老师能力 VO
export interface TeacherAbilityVO {
  teacherAbilityId: number // 主键
  foreignLanguage: [] // 外语
  foreignCertificate: string // 外语证明
  gradeFour: number // 四级分数
  gradeSix: number // 六级分数
  englishScore: number // 高考英语
  ieltsScore: number // 雅思
  toeflScore: number // 托福
  pianoLevel: string // 钢琴等级
  pianoCertificateIssuer: string // 钢琴证书颁证方
  otherCertificate: string // 其他技能证书及获奖情况
  schoolAwards: string // 在校获奖情况
  schoolAwardsExtra: string // 补充奖项
  forte: [] // 特长
  forteExtra: string // 其他特长
  experience: string // 家教经历
  teachingMethod: string // 教学方法
  teachScopeRank: [] // 作业辅导科目擅长排序
}

// 老师能力 API
export const TeacherAbilityApi = {
  // 查询老师能力分页
  getTeacherAbilityPage: async (params: any) => {
    return await request.get({ url: `/als/teacher-ability/page`, params })
  },

  // 查询老师能力详情
  getTeacherAbility: async (id: number) => {
    return await request.get({ url: `/als/teacher-ability/get?id=` + id })
  },

  // 新增老师能力
  createTeacherAbility: async (data: TeacherAbilityVO) => {
    return await request.post({ url: `/als/teacher-ability/create`, data })
  },

  // 修改老师能力
  updateTeacherAbility: async (data: TeacherAbilityVO) => {
    return await request.put({ url: `/als/teacher-ability/update`, data })
  },

  // 删除老师能力
  deleteTeacherAbility: async (id: number) => {
    return await request.delete({ url: `/als/teacher-ability/delete?id=` + id })
  },

  // 导出老师能力 Excel
  exportTeacherAbility: async (params) => {
    return await request.download({ url: `/als/teacher-ability/export-excel`, params })
  }
}
