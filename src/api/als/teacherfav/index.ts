import request from '@/config/axios'

// 老师收藏 VO
export interface TeacherFavVO {
  teacherFavId: number // 主键
  orderId: number // 订单ID
  type: number // 类型
  teacherId: number // 老师ID
}

// 老师收藏 API
export const TeacherFavApi = {
  // 查询老师收藏分页
  getTeacherFavPage: async (params: any) => {
    return await request.get({ url: `/als/teacher-fav/page`, params })
  },

  // 查询老师收藏详情
  getTeacherFav: async (id: number) => {
    return await request.get({ url: `/als/teacher-fav/get?id=` + id })
  },

  // 新增老师收藏
  createTeacherFav: async (data: TeacherFavVO) => {
    return await request.post({ url: `/als/teacher-fav/create`, data })
  },

  // 修改老师收藏
  updateTeacherFav: async (data: TeacherFavVO) => {
    return await request.put({ url: `/als/teacher-fav/update`, data })
  },

  // 删除老师收藏
  deleteTeacherFav: async (id: number) => {
    return await request.delete({ url: `/als/teacher-fav/delete?id=` + id })
  },

  // 导出老师收藏 Excel
  exportTeacherFav: async (params) => {
    return await request.download({ url: `/als/teacher-fav/export-excel`, params })
  }
}