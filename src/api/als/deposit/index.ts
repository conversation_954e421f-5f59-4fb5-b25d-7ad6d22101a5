import request from '@/config/axios'

// 课时押金 VO
export interface DepositVO {
  depositId: number // 押金id
  lessonHourId: number // 课时记录ID
  customerId: number // 家长ID
  teacherId: number // 老师ID
  classHour: number // 扣押课时
  depositStatus: number // 押金状态
  dealMethod: number // 押金处理方式
  dealUserId: number // 处理人
  dealTime: Date // 处理时间
  customerGet: number // 家长获得课时-上课时长(h)
  teacherGet: number // 老师获得课时-上课时长(h)
  platformGet: number // 平台获得课时-上课时长(h)
  remarkTime: Date // 备注时间
  remarkUserId: number // 备注人
  remark: string // 备注
}

// 课时押金 API
export const DepositApi = {
  // 查询课时押金分页
  getDepositPage: async (params: any) => {
    return await request.get({ url: `/als/deposit/page`, params })
  },

  // 查询课时押金详情
  getDeposit: async (id: number) => {
    return await request.get({ url: `/als/deposit/get?id=` + id })
  },

  // 新增课时押金
  createDeposit: async (data: DepositVO) => {
    return await request.post({ url: `/als/deposit/create`, data })
  },

  // 修改课时押金
  updateDeposit: async (data: DepositVO) => {
    return await request.put({ url: `/als/deposit/update`, data })
  },

  // 删除课时押金
  deleteDeposit: async (id: number) => {
    return await request.delete({ url: `/als/deposit/delete?id=` + id })
  },

  // 导出课时押金 Excel
  exportDeposit: async (params) => {
    return await request.download({ url: `/als/deposit/export-excel`, params })
  }
}