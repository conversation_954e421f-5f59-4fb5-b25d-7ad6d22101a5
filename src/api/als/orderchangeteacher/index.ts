import request from '@/config/axios'
import {AuditFormData} from "@/api/als/audit";

// 更换老师 VO
export interface OrderChangeTeacherVO {
  orderChangeTeacherId: number // 主键
  orderId: number // 订单ID
  teacherId: number // 老师ID
  customerId: number // 家长ID
  reasonTags: number // 被换原因标签
  reason: string // 具体原因
  goodComment: string // 对老师-好评
  badComment: string // 对老师-差评
  applyTime: Date // 申请时间
  auditStatus: number // 审核状态
  auditTime: Date // 审核时间
  auditUserId: number // 审核人
  auditRemark: string // 审核备注
}

// 更换老师 API
export const OrderChangeTeacherApi = {
  // 查询更换老师分页
  getOrderChangeTeacherPage: async (params: any) => {
    return await request.get({ url: `/als/order-change-teacher/page`, params })
  },

  // 查询更换老师详情
  getOrderChangeTeacher: async (id: number) => {
    return await request.get({ url: `/als/order-change-teacher/get?id=` + id })
  },

  // 新增更换老师
  createOrderChangeTeacher: async (data: OrderChangeTeacherVO) => {
    return await request.post({ url: `/als/order-change-teacher/create`, data })
  },

  // 修改更换老师
  updateOrderChangeTeacher: async (data: OrderChangeTeacherVO) => {
    return await request.put({ url: `/als/order-change-teacher/update`, data })
  },

  // 删除更换老师
  deleteOrderChangeTeacher: async (id: number) => {
    return await request.delete({ url: `/als/order-change-teacher/delete?id=` + id })
  },

  // 审核更换老师
  auditOrderChangeTeacher: async (data: AuditFormData) => {
    return await request.put({ url: `/als/order-change-teacher/audit` ,data })
  },
  

  // 导出更换老师 Excel
  exportOrderChangeTeacher: async (params) => {
    return await request.download({ url: `/als/order-change-teacher/export-excel`, params })
  }
}