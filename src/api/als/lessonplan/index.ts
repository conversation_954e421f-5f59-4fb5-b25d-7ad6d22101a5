import request from '@/config/axios'
import {AuditFormData} from "@/api/als/audit";

// 陪学计划 VO
export interface LessonPlanVO {
  lessonPlanId: number // 陪学计划ID
  orderId: number // 订单ID
  customerId: number // 家长ID
  teacherId: number // 老师ID
  planHour: number // 计划课时数
  planTarget: string // 总体目标
  customerExpect: string // 家长期望
  stageQ1Target: string // Q1阶段目标
  stageQ2Target: string // Q2阶段目标
  stageQ3Target: string // Q3阶段目标
  stageQ4Target: string // Q4阶段目标
  stageQ1Score: number // Q1打分
  stageQ2Score: number // Q2打分
  stageQ3Score: number // Q3打分
  stageQ4Score: number // Q4打分
  planAuditStatus: number // 审核状态
  planAuditTime: Date // 审核时间
  planAuditUserId: number // 审核人
  planAuditRemark: string // 审核备注
}

export interface AuditReqVO{
  id: number;
  auditStatus: number;
  auditRemark: string;
}

// 陪学计划 API
export const LessonPlanApi = {
  // 查询陪学计划分页
  getLessonPlanPage: async (params: any) => {
    return await request.get({ url: `/als/lesson-plan/page`, params })
  },

  // 查询陪学计划详情
  getLessonPlan: async (id: number) => {
    return await request.get({ url: `/als/lesson-plan/get?id=` + id })
  },

  // 新增陪学计划
  createLessonPlan: async (data: LessonPlanVO) => {
    return await request.post({ url: `/als/lesson-plan/create`, data })
  },

  // 修改陪学计划
  updateLessonPlan: async (data: LessonPlanVO) => {
    return await request.put({ url: `/als/lesson-plan/update`, data })
  },

  // 删除陪学计划
  deleteLessonPlan: async (id: number) => {
    return await request.delete({ url: `/als/lesson-plan/delete?id=` + id })
  },

  // 导出陪学计划 Excel
  exportLessonPlan: async (params) => {
    return await request.download({ url: `/als/lesson-plan/export-excel`, params })
  },

  // 审核
  audit: async (data: AuditFormData) => {
    return await request.put({ url: `/als/lesson-plan/audit`, data})
  },
}
