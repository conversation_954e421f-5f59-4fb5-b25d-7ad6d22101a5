import request from '@/config/axios'

// 老师证书 VO
export interface TeacherCertificateVO {
  certificateId: number // 证书ID
  teacherId: number // 老师ID
  teacherName: string // 老师姓名
  teacherSex: string // 性别
  picUrl: string // 证书照片URL
  certificateNo: string // 证书编号
  certificateStatus: number // 证书状态 0有效 1无效-已过期 2无效-已注销
  validTime: Date // 有效期
  remark: string // 备注
  teacherIdNumber: string // 身份证号码
}

// 老师证书 API
export const TeacherCertificateApi = {
  // 查询老师证书分页
  getTeacherCertificatePage: async (params: any) => {
    return await request.get({ url: `/als/teacher-certificate/page`, params })
  },

  // 查询老师证书详情
  getTeacherCertificate: async (id: number) => {
    return await request.get({ url: `/als/teacher-certificate/get?id=` + id })
  },

  // 查询老师证书详情
  getByTeacherId: async (teacherId: number) => {
    return await request.get({ url: `/als/teacher-certificate/getByTeacherId?teacherId=` + teacherId })
  },  

  // 新增老师证书
  createTeacherCertificate: async (data: TeacherCertificateVO) => {
    return await request.post({ url: `/als/teacher-certificate/create`, data })
  },

  // 新增老师证书
  createOrUpdateTeacherCertificate: async (data: TeacherCertificateVO) => {
    return await request.post({ url: `/als/teacher-certificate/createOrUpdate`, data })
  },

  // 修改老师证书
  updateTeacherCertificate: async (data: TeacherCertificateVO) => {
    return await request.put({ url: `/als/teacher-certificate/update`, data })
  },

  // 删除老师证书
  deleteTeacherCertificate: async (id: number) => {
    return await request.delete({ url: `/als/teacher-certificate/delete?id=` + id })
  },

  // 导出老师证书 Excel
  exportTeacherCertificate: async (params) => {
    return await request.download({ url: `/als/teacher-certificate/export-excel`, params })
  }
}
