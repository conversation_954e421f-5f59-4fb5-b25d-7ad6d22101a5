import request from '@/config/axios'

// 老师账户变更记录 VO
export interface TeacherAccountChangeVO {
  teacherAccountChangeId: number // 变更ID
  teacherAccountId: number // 账户ID
  teacherId: number // 老师ID
  amount: number // 金额
  balance: number // 变更后余额
  remark: string // 变更备注
  businessType: number // 变更业务类型
  businessId: number // 业务ID
}

// 老师账户变更记录 API
export const TeacherAccountChangeApi = {
  // 查询老师账户变更记录分页
  getTeacherAccountChangePage: async (params: any) => {
    return await request.get({ url: `/als/teacher-account-change/page`, params })
  },

  // 查询老师账户变更记录详情
  getTeacherAccountChange: async (id: number) => {
    return await request.get({ url: `/als/teacher-account-change/get?id=` + id })
  },

  // 新增老师账户变更记录
  createTeacherAccountChange: async (data: TeacherAccountChangeVO) => {
    return await request.post({ url: `/als/teacher-account-change/create`, data })
  },

  // 修改老师账户变更记录
  updateTeacherAccountChange: async (data: TeacherAccountChangeVO) => {
    return await request.put({ url: `/als/teacher-account-change/update`, data })
  },

  // 删除老师账户变更记录
  deleteTeacherAccountChange: async (id: number) => {
    return await request.delete({ url: `/als/teacher-account-change/delete?id=` + id })
  },

  // 导出老师账户变更记录 Excel
  exportTeacherAccountChange: async (params) => {
    return await request.download({ url: `/als/teacher-account-change/export-excel`, params })
  }
}