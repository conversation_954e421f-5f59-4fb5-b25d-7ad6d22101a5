import request from '@/config/axios'
import {AuditFormData} from "@/api/als/audit";

// 老师提现申请 VO
export interface TeacherWithdrawApplyVO {
  teacherWithdrawApplyId: number // 主键
  teacherId: number // 老师ID
  teacherName: string // 老师姓名
  toAccountAmount: number // 到账金额
  fee: number // 手续费
  totalAmount: number // 总金额
  applyTime: Date // 申请时间
  auditStatus: number // 审核状态
  auditTime: Date // 审核时间
  auditUserId: number // 审核人
  auditRemark: string // 审核备注
}

// 老师提现申请 API
export const TeacherWithdrawApplyApi = {
  // 查询老师提现申请分页
  getTeacherWithdrawApplyPage: async (params: any) => {
    return await request.get({ url: `/als/teacher-withdraw-apply/page`, params })
  },

  // 查询老师提现申请详情
  getTeacherWithdrawApply: async (id: number) => {
    return await request.get({ url: `/als/teacher-withdraw-apply/get?id=` + id })
  },

  // 新增老师提现申请
  createTeacherWithdrawApply: async (data: TeacherWithdrawApplyVO) => {
    return await request.post({ url: `/als/teacher-withdraw-apply/create`, data })
  },

  // 修改老师提现申请
  updateTeacherWithdrawApply: async (data: TeacherWithdrawApplyVO) => {
    return await request.put({ url: `/als/teacher-withdraw-apply/update`, data })
  },

  // 删除老师提现申请
  deleteTeacherWithdrawApply: async (id: number) => {
    return await request.delete({ url: `/als/teacher-withdraw-apply/delete?id=` + id })
  },

  // 导出老师提现申请 Excel
  exportTeacherWithdrawApply: async (params) => {
    return await request.download({ url: `/als/teacher-withdraw-apply/export-excel`, params })
  },

  // 审核
  audit: async (data: AuditFormData) => {
    return await request.put({ url: `/als/teacher-withdraw-apply/audit`, data })
  },
}
