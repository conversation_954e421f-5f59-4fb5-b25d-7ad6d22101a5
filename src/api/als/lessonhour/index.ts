import request from '@/config/axios'
import {AuditReqVO} from "@/api/als/lessonplan";
import {AuditFormData} from "@/api/als/audit";

// 课时记录 VO
export interface LessonHourVO {
  lessonHourId: number // 课时记录ID
  lessonRecordId: number // 陪学记录ID
  customerPackageId: number // 购买套餐ID
  lessonType: number // 上课类型
  childNumber: number // 人数
  customerId: number // 家长ID
  teacherId: number // 老师ID
  timeHour: number // 上课时长(h) 押金时拆分
  multiple: number // 倍数
  classHour: number // 总课时
  teacherPrice: number // 老师-课时单价
  teacherHourPrice: number // 老师-小时单价(课
  teacherExtraCharge: number // 老师-附加总费用
  teacherAmount: number // 老师-总薪资
  customerPrice: number // 家长-课时单价
  customerCost: number // 家长-扣费金额
  nextTime: Date // 下次上课时间
  recordStatus: number // 课时记录状态
  auditStatus: number // 审核状态
  auditTime: Date // 审核时间
  auditUserId: number // 审核人
  auditRemark: string // 审核备注
  dealMethod: number // 押金处理方式
  dealUserId: number // 处理人
  dealTime: Date // 处理时间
  customerGet: number // 家长获得课时-上课时长(h)
  teacherGet: number // 老师获得课时-上课时长(h)
  platformGet: number // 平台获得课时-上课时长(h)
  remarkTime: Date // 备注时间
  remarkUserId: number // 备注人
  remark: string // 备注
}

// 课时记录 API
export const LessonHourApi = {
  // 查询课时记录分页
  getLessonHourPage: async (params: any) => {
    return await request.get({ url: `/als/lesson-hour/page`, params })
  },

  // 查询课时记录详情
  getLessonHour: async (id: number) => {
    return await request.get({ url: `/als/lesson-hour/get?id=` + id })
  },

  // 新增课时记录
  createLessonHour: async (data: LessonHourVO) => {
    return await request.post({ url: `/als/lesson-hour/create`, data })
  },

  // 修改课时记录
  updateLessonHour: async (data: LessonHourVO) => {
    return await request.put({ url: `/als/lesson-hour/update`, data })
  },

  // 删除课时记录
  deleteLessonHour: async (id: number) => {
    return await request.delete({ url: `/als/lesson-hour/delete?id=` + id })
  },

  // 导出课时记录 Excel
  exportLessonHour: async (params) => {
    return await request.download({ url: `/als/lesson-hour/export-excel`, params })
  },

  // 修改课时记录
  auditLessonHour: async (data: AuditFormData) => {
    return await request.put({ url: `/als/lesson-hour/auditLessonHour`, data })
  },
}
