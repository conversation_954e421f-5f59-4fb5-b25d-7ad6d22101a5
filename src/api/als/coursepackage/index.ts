import request from '@/config/axios'

// 课时包 VO
export interface CoursePackageVO {
  coursePackageId: number // 课时包ID
  packageName: string // 课时包名称
  packageType: number // 课时包类型
  lessonPeriod: number // 总课时数
  salePrice: number // 售价
  isEnable: number // 是否启用
}

// 课时包 API
export const CoursePackageApi = {
  // 查询课时包分页
  getCoursePackagePage: async (params: any) => {
    return await request.get({ url: `/als/course-package/page`, params })
  },

  // 查询课时包详情
  getCoursePackage: async (id: number) => {
    return await request.get({ url: `/als/course-package/get?id=` + id })
  },

  // 新增课时包
  createCoursePackage: async (data: CoursePackageVO) => {
    return await request.post({ url: `/als/course-package/create`, data })
  },

  // 修改课时包
  updateCoursePackage: async (data: CoursePackageVO) => {
    return await request.put({ url: `/als/course-package/update`, data })
  },

  // 删除课时包
  deleteCoursePackage: async (id: number) => {
    return await request.delete({ url: `/als/course-package/delete?id=` + id })
  },

  // 导出课时包 Excel
  exportCoursePackage: async (params) => {
    return await request.download({ url: `/als/course-package/export-excel`, params })
  },

  // 查询全量课时包
  getAllCoursePackage: async () => {
    return await request.get({ url: `/als/course-package/all` })
  },
}
