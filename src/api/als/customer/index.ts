import request from '@/config/axios'

// 家长 VO
export interface CustomerVO {
  customerId: number // 家长ID
  customerName: string // 家长姓名
  customerSex: number // 家长性别
  remainPeriod: number
  relationship: string // 孩子关系
  customerPhone: string // 手机号
  openId: string // openId
  serviceStatus: number // 服务状态
  sourceChannel: number // 获客渠道
  serviceTags: string // 服务标签
  operationTags: string // 运营标签
  levelTags: string // 分级标签
  registerTime: Date // 注册时间
  lastLoginTime: Date // 最近登录时间
  headOperateUserId: number // 运营负责人
  headMarketUserId: number // 市场负责人
  customerRemark: string // 家长备注
}

// 家长 API
export const CustomerApi = {
  // 查询家长分页
  getCustomerPage: async (params: any) => {
    return await request.get({ url: `/als/customer/page`, params })
  },

  // 获取家长
  getCustomer: async (id: number) => {
    return await request.get({ url: `/als/customer/get?id=` + id })
  },

  // 新增家长
  createCustomer: async (data: CustomerVO) => {
    return await request.post({ url: `/als/customer/create`, data })
  },

  // 修改家长
  updateCustomer: async (data: CustomerVO) => {
    return await request.put({ url: `/als/customer/update`, data })
  },

  // 删除家长
  deleteCustomer: async (id: number) => {
    return await request.delete({ url: `/als/customer/delete?id=` + id })
  },

  // 导出家长 Excel
  exportCustomer: async (params) => {
    return await request.download({ url: `/als/customer/export-excel`, params })
  },

  // 查询家长详情
  getCustomerDetail: async (id: number) => {
    return await request.get({ url: `/als/customer/detail?id=` + id })
  },
}
