import request from '@/config/axios'

// 协议 VO
export interface AgreementVO {
  agreementId: number // 主键ID
  key: string // 协议唯一标识
  title: string // 协议标题
  content: string // 协议内容
  version: string // 协议版本号
}

// 协议 API
export const AgreementApi = {
  // 查询协议分页
  getAgreementPage: async (params: any) => {
    return await request.get({ url: `/als/agreement/page`, params })
  },

  // 查询协议详情
  getAgreement: async (id: number) => {
    return await request.get({ url: `/als/agreement/get?id=` + id })
  },

  // 新增协议
  createAgreement: async (data: AgreementVO) => {
    return await request.post({ url: `/als/agreement/create`, data })
  },

  // 修改协议
  updateAgreement: async (data: AgreementVO) => {
    return await request.put({ url: `/als/agreement/update`, data })
  },

  // 删除协议
  deleteAgreement: async (id: number) => {
    return await request.delete({ url: `/als/agreement/delete?id=` + id })
  },

  // 导出协议 Excel
  exportAgreement: async (params) => {
    return await request.download({ url: `/als/agreement/export-excel`, params })
  }
}