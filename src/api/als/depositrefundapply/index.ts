import request from '@/config/axios'

// 退押申请 VO
export interface DepositRefundApplyVO {
  depositRefundApplyId: number // 申请ID
  teacherId: number // 申请人
  lessonHourId: number // 课时记录ID
  applyTime: Date // 申请时间
  applyReason: string // 申请备注
  dealStatus: number // 处理状态
}

export interface DepositRefundDealVO {
  lessonHourId: number // 课时记录ID
  dealMethod: number // 处理方式
  customerGet: number // 家长分得
  teacherGet: number // 老师分得
  platformGet: number // 平台分得
}

// 退押申请 API
export const DepositRefundApplyApi = {
  // 查询退押申请分页
  getDepositRefundApplyPage: async (params: any) => {
    return await request.get({ url: `/als/deposit-refund-apply/page`, params })
  },

  // 查询退押申请详情
  getDepositRefundApply: async (id: number) => {
    return await request.get({ url: `/als/deposit-refund-apply/get?id=` + id })
  },

  // 新增退押申请
  createDepositRefundApply: async (data: DepositRefundApplyVO) => {
    return await request.post({ url: `/als/deposit-refund-apply/create`, data })
  },

  // 修改退押申请
  updateDepositRefundApply: async (data: DepositRefundApplyVO) => {
    return await request.put({ url: `/als/deposit-refund-apply/update`, data })
  },

  // 删除退押申请
  deleteDepositRefundApply: async (id: number) => {
    return await request.delete({ url: `/als/deposit-refund-apply/delete?id=` + id })
  },

  // 导出退押申请 Excel
  exportDepositRefundApply: async (params) => {
    return await request.download({ url: `/als/deposit-refund-apply/export-excel`, params })
  },

  // 处理押金
  dealDepositRefund: async (data: DepositRefundDealVO) => {
    return await request.put({ url: `/als/deposit-refund-apply/dealDepositRefund`, data })
  },
}
