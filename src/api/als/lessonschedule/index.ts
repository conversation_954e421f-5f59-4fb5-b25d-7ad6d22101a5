import request from '@/config/axios'

// 时间规划执行 VO
export interface LessonScheduleVO {
  lessonScheduleId: number // 时间规划执行ID
  lessonRecordId: number // 陪学记录ID
  startTime: Date // 开始时间
  endTime: Date // 结束时间
  periodMinute: number // 间隔：分钟
  realEndTime: Date // 实际结束时间
  errorMinute: number // 实际结束时间误差：分钟
  taskContent: string // 任务名称
  taskStatus: number // 状态
  reason: string // 原因
  programme: string // 方案
}

// 时间规划执行 API
export const LessonScheduleApi = {
  // 查询时间规划执行分页
  getLessonSchedulePage: async (params: any) => {
    return await request.get({ url: `/als/lesson-schedule/page`, params })
  },

  // 查询时间规划执行详情
  getLessonSchedule: async (id: number) => {
    return await request.get({ url: `/als/lesson-schedule/get?id=` + id })
  },

  // 新增时间规划执行
  createLessonSchedule: async (data: LessonScheduleVO) => {
    return await request.post({ url: `/als/lesson-schedule/create`, data })
  },

  // 修改时间规划执行
  updateLessonSchedule: async (data: LessonScheduleVO) => {
    return await request.put({ url: `/als/lesson-schedule/update`, data })
  },

  // 删除时间规划执行
  deleteLessonSchedule: async (id: number) => {
    return await request.delete({ url: `/als/lesson-schedule/delete?id=` + id })
  },

  // 导出时间规划执行 Excel
  exportLessonSchedule: async (params) => {
    return await request.download({ url: `/als/lesson-schedule/export-excel`, params })
  }
}