import request from '@/config/axios'

// 请假申请列表 VO
export interface LeaveApplyVO {
  leaveApplyId: number // 请假申请ID
  orderRecordId: number // 课时记录ID
  orderId: number // 订单ID
  customerId: number // 家长ID
  teacherId: number // 老师ID
  stage: number // 陪学阶段
  whoLeave: number // 请假方
  planTime: Date // 原定上课时间
  newTime: Date // 调整后上课时间
  applyTime: Date // 申请时间
  applyRemark: string // 申请备注
  auditStatus: number // 审核状态
  auditTime: Date // 审核时间
  auditUserId: number // 审核人
  auditRemark: string // 审核备注
}

// 请假申请列表 API
export const LeaveApplyApi = {
  // 查询请假申请列表分页
  getLeaveApplyPage: async (params: any) => {
    return await request.get({ url: `/als/leave-apply/page`, params })
  },

  // 查询请假申请列表详情
  getLeaveApply: async (id: number) => {
    return await request.get({ url: `/als/leave-apply/get?id=` + id })
  },

  // 新增请假申请列表
  createLeaveApply: async (data: LeaveApplyVO) => {
    return await request.post({ url: `/als/leave-apply/create`, data })
  },

  // 修改请假申请列表
  updateLeaveApply: async (data: LeaveApplyVO) => {
    return await request.put({ url: `/als/leave-apply/update`, data })
  },

  // 删除请假申请列表
  deleteLeaveApply: async (id: number) => {
    return await request.delete({ url: `/als/leave-apply/delete?id=` + id })
  },

  // 导出请假申请列表 Excel
  exportLeaveApply: async (params) => {
    return await request.download({ url: `/als/leave-apply/export-excel`, params })
  }
}