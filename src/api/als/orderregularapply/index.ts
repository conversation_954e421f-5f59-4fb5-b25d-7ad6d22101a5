import request from '@/config/axios'
import {AuditReqVO} from "@/api/als/lessonplan";
import {AuditFormData} from "@/api/als/audit";

// 正式课申请 VO
export interface OrderRegularApplyVO {
  orderRegularApplyId: number // 正式课申请ID
  orderId: number // 订单ID
  customerId: number // 家长ID
  teacherId: number // 老师ID
  feedbackDesc: string // 体验课反馈简述
  feedbackUrl: string // 体验课反馈表
  planUrl: string // 正式课陪学计划表
  planLessonId: number // 陪学计划ID
  agreedUrl: string // 陪学公约
  feedbackAuditStatus: number // 反馈表审核状态
  feedbackAuditTime: Date // 反馈表审核时间
  feedbackAuditUserId: number // 反馈表审核人
  feedbackAuditRemark: string // 反馈表审核备注
  customerEvaluation: number // 来自家长的评价
  planAuditStatus: number // 正式课陪学计划审核状态
  planAuditTime: Date // 正式课陪学计划审核时间
  planAuditUserId: number // 正式课陪学计划审核人
  planAuditRemark: string // 正式课陪学计划审核备注
}
export interface RegularApplyAuditReqVO{
  id: number;
  feedbackAuditStatus: number;
  feedbackAuditRemark: string;

  planAuditStatus: number;
  planAuditRemark: string;
}

// 正式课申请 API
export const OrderRegularApplyApi = {
  // 查询正式课申请分页
  getOrderRegularApplyPage: async (params: any) => {
    return await request.get({ url: `/als/order-regular-apply/page`, params })
  },

  // 查询正式课申请详情
  getOrderRegularApply: async (id: number) => {
    return await request.get({ url: `/als/order-regular-apply/get?id=` + id })
  },

  // 新增正式课申请
  createOrderRegularApply: async (data: OrderRegularApplyVO) => {
    return await request.post({ url: `/als/order-regular-apply/create`, data })
  },

  // 修改正式课申请
  updateOrderRegularApply: async (data: OrderRegularApplyVO) => {
    return await request.put({ url: `/als/order-regular-apply/update`, data })
  },

  // 删除正式课申请
  deleteOrderRegularApply: async (id: number) => {
    return await request.delete({ url: `/als/order-regular-apply/delete?id=` + id })
  },

  // 导出正式课申请 Excel
  exportOrderRegularApply: async (params) => {
    return await request.download({ url: `/als/order-regular-apply/export-excel`, params })
  },

  // 反馈表审核
  auditFeedback: async (data: AuditFormData) => {
    return await request.put({ url: `/als/order-regular-apply/audit/feedback`, data })
  },

  // 陪学计划审核
  auditPlan: async (data: AuditFormData) => {
    return await request.put({ url: `/als/order-regular-apply/audit/plan`, data })
  }
}
