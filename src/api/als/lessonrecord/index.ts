import request from '@/config/axios'

// 陪学记录 VO
export interface LessonRecordVO {
  lessonRecordId: number // 陪学记录ID
  customerPackageId: number // 购买记录ID
  customerId: number // 家长ID
  teacherId: number // 老师ID
  lessonType: number // 上课类型
  childNumber: number // 陪学人数
  lessonContent: string // 陪学内容
  recordStatus: number // 陪学记录状态
  process: number // 填写进度
  prepareScore: string // 课前准备项打分
  summaryScore: string // 课后打分
  prepareItem: string // 课前准备事项
  summaryItem: string // 课后总结事项
  weakSpot: string // 薄弱点记录
  showEvaluate: string // 行为表现评价
  reflect: string // 陪学反思
  leaveWord: string // 给家长留言
  startTime: Date // 开始打卡时间
  endTime: Date // 结束打卡时间
  scheduleHour: number // 陪学时长
  nextTime: Date // 下次上课时间
  commitTime: Date // 提交时间
}

// 陪学记录 API
export const LessonRecordApi = {
  // 查询陪学记录分页
  getLessonRecordPage: async (params: any) => {
    return await request.get({ url: `/als/lesson-record/page`, params })
  },

  // 查询陪学记录详情
  getLessonRecord: async (id: number) => {
    return await request.get({ url: `/als/lesson-record/get?id=` + id })
  },

  // 新增陪学记录
  createLessonRecord: async (data: LessonRecordVO) => {
    return await request.post({ url: `/als/lesson-record/create`, data })
  },

  // 修改陪学记录
  updateLessonRecord: async (data: LessonRecordVO) => {
    return await request.put({ url: `/als/lesson-record/update`, data })
  },

  // 删除陪学记录
  deleteLessonRecord: async (id: number) => {
    return await request.delete({ url: `/als/lesson-record/delete?id=` + id })
  },

  // 导出陪学记录 Excel
  exportLessonRecord: async (params) => {
    return await request.download({ url: `/als/lesson-record/export-excel`, params })
  }
}
