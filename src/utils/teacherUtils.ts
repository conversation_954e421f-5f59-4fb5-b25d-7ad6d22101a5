import { ref } from 'vue'
import { TeacherA<PERSON> } from '@/api/als/teacher'

/**
 * 老师详情弹窗工具类
 */
export class TeacherDetailModal {
  private static instance: TeacherDetailModal | null = null
  private dialogVisible = ref(false)
  private dialogTitle = ref('老师详情')
  private detail = ref({
    teacherVo: {
      teacherName: '',
      teacherPhone: '',
      isAttention: true,
      attentionPlat: []
    },
    serviceVo: {
      serviceTimes: 0,
      serviceClassHour: 0,
      serviceCustomerNum: 0,
      commitLessonRecordNum: 0,
      lastServiceTime: '',
      lastOrderConfirmTime: '',
      successOrderNum: 0,
      failOrderNum: 0,
      withdrawAmount: 0
    },
    bindList: []
  })

  /**
   * 获取单例实例
   */
  public static getInstance(): TeacherDetailModal {
    if (!TeacherDetailModal.instance) {
      TeacherDetailModal.instance = new TeacherDetailModal()
    }
    return TeacherDetailModal.instance
  }

  /**
   * 打开老师详情弹窗
   * @param teacherId 老师ID
   */
  public async open(teacherId?: number) {
    if (!teacherId) {
      console.warn('teacherId is required')
      return
    }
    
    try {
      this.dialogVisible.value = true
      this.detail.value = await TeacherApi.getDetail(teacherId)
    } catch (error) {
      console.error('Failed to load teacher detail:', error)
      this.dialogVisible.value = false
    }
  }

  /**
   * 关闭弹窗
   */
  public close() {
    this.dialogVisible.value = false
  }

  /**
   * 获取弹窗状态
   */
  public get visible() {
    return this.dialogVisible
  }

  /**
   * 获取弹窗标题
   */
  public get title() {
    return this.dialogTitle
  }

  /**
   * 获取详情数据
   */
  public get data() {
    return this.detail
  }
}

// 全局实例
const globalModal = TeacherDetailModal.getInstance()

/**
 * 打开老师详情弹窗的便捷函数
 * @param teacherId 老师ID
 */
export const openTeacherDetail = (teacherId?: number) => {
  return globalModal.open(teacherId)
}

/**
 * 创建老师详情弹窗的组合式函数
 * 用于在组件中使用
 */
export const useTeacherDetail = () => {
  return {
    visible: globalModal.visible,
    title: globalModal.title,
    detail: globalModal.data,
    open: globalModal.open.bind(globalModal),
    close: globalModal.close.bind(globalModal)
  }
}
