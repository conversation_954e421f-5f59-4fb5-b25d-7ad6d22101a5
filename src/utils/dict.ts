/**
 * 数据字典工具类
 */
import { useDictStoreWithOut } from '@/store/modules/dict'
import { ElementPlusInfoType } from '@/types/elementPlus'

const dictStore = useDictStoreWithOut()

/**
 * 获取 dictType 对应的数据字典数组
 *
 * @param dictType 数据类型
 * @returns {*|Array} 数据字典数组
 */
export interface DictDataType {
  dictType: string
  label: string
  value: string | number | boolean
  colorType: ElementPlusInfoType | ''
  cssClass: string
}

export interface NumberDictDataType extends DictDataType {
  value: number
}

export interface StringDictDataType extends DictDataType {
  value: string
}

export const getDictOptions = (dictType: string) => {
  return dictStore.getDictByType(dictType) || []
}

/**
 * 获取字典项，里面是Integer类型
 */
export const getIntDictOptions = (dictType: string): NumberDictDataType[] => {
  // 获得通用的 DictDataType 列表
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  // 转换成 number 类型的 NumberDictDataType 类型
  // why 需要特殊转换：避免 IDEA 在 v-for="dict in getIntDictOptions(...)" 时，el-option 的 key 会告警
  const dictOption: NumberDictDataType[] = []
  dictOptions.forEach((dict: DictDataType) => {
    dictOption.push({
      ...dict,
      value: parseInt(dict.value + '')
    })
  })
  return dictOption
}

/**
 * 获取字典项，里面是String类型
 * 注意：还有getIntDictOptions，获取Integer类型
 * @param dictType
 */
export const getStrDictOptions = (dictType: string) => {
  // 获得通用的 DictDataType 列表
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  // 转换成 string 类型的 StringDictDataType 类型
  // why 需要特殊转换：避免 IDEA 在 v-for="dict in getStrDictOptions(...)" 时，el-option 的 key 会告警
  const dictOption: StringDictDataType[] = []
  dictOptions.forEach((dict: DictDataType) => {
    dictOption.push({
      ...dict,
      value: dict.value + ''
    })
  })
  return dictOption
}

export const getBoolDictOptions = (dictType: string) => {
  const dictOption: DictDataType[] = []
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  dictOptions.forEach((dict: DictDataType) => {
    dictOption.push({
      ...dict,
      value: dict.value + '' === 'true'
    })
  })
  return dictOption
}

/**
 * 获取指定字典类型的指定值对应的字典对象
 * @param dictType 字典类型
 * @param value 字典值
 * @return DictDataType 字典对象
 */
export const getDictObj = (dictType: string, value: any): DictDataType | undefined => {
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  for (const dict of dictOptions) {
    if (dict.value === value + '') {
      return dict
    }
  }
}

/**
 * 获得字典数据的文本展示
 *
 * @param dictType 字典类型
 * @param value 字典数据的值
 * @return 字典名称
 */
export const getDictLabel = (dictType: string, value: any): string => {
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  const dictLabel = ref('')
  dictOptions.forEach((dict: DictDataType) => {
    if (dict.value === value + '') {
      dictLabel.value = dict.label
    }
  })
  return dictLabel.value
}

export enum DICT_TYPE {
  USER_TYPE = 'user_type',
  COMMON_STATUS = 'common_status',
  TERMINAL = 'terminal', // 终端
  DATE_INTERVAL = 'date_interval', // 数据间隔

  // ========== SYSTEM 模块 ==========
  SYSTEM_USER_SEX = 'system_user_sex',
  SYSTEM_MENU_TYPE = 'system_menu_type',
  SYSTEM_ROLE_TYPE = 'system_role_type',
  SYSTEM_DATA_SCOPE = 'system_data_scope',
  SYSTEM_NOTICE_TYPE = 'system_notice_type',
  SYSTEM_LOGIN_TYPE = 'system_login_type',
  SYSTEM_LOGIN_RESULT = 'system_login_result',
  SYSTEM_SMS_CHANNEL_CODE = 'system_sms_channel_code',
  SYSTEM_SMS_TEMPLATE_TYPE = 'system_sms_template_type',
  SYSTEM_SMS_SEND_STATUS = 'system_sms_send_status',
  SYSTEM_SMS_RECEIVE_STATUS = 'system_sms_receive_status',
  SYSTEM_ERROR_CODE_TYPE = 'system_error_code_type',
  SYSTEM_OAUTH2_GRANT_TYPE = 'system_oauth2_grant_type',
  SYSTEM_MAIL_SEND_STATUS = 'system_mail_send_status',
  SYSTEM_NOTIFY_TEMPLATE_TYPE = 'system_notify_template_type',
  SYSTEM_SOCIAL_TYPE = 'system_social_type',

  // ========== INFRA 模块 ==========
  INFRA_BOOLEAN_STRING = 'infra_boolean_string',
  INFRA_JOB_STATUS = 'infra_job_status',
  INFRA_JOB_LOG_STATUS = 'infra_job_log_status',
  INFRA_API_ERROR_LOG_PROCESS_STATUS = 'infra_api_error_log_process_status',
  INFRA_CONFIG_TYPE = 'infra_config_type',
  INFRA_CODEGEN_TEMPLATE_TYPE = 'infra_codegen_template_type',
  INFRA_CODEGEN_FRONT_TYPE = 'infra_codegen_front_type',
  INFRA_CODEGEN_SCENE = 'infra_codegen_scene',
  INFRA_FILE_STORAGE = 'infra_file_storage',
  INFRA_OPERATE_TYPE = 'infra_operate_type',

  // ========== BPM 模块 ==========
  BPM_MODEL_FORM_TYPE = 'bpm_model_form_type',
  BPM_TASK_CANDIDATE_STRATEGY = 'bpm_task_candidate_strategy',
  BPM_PROCESS_INSTANCE_STATUS = 'bpm_process_instance_status',
  BPM_TASK_STATUS = 'bpm_task_status',
  BPM_OA_LEAVE_TYPE = 'bpm_oa_leave_type',
  BPM_PROCESS_LISTENER_TYPE = 'bpm_process_listener_type',
  BPM_PROCESS_LISTENER_VALUE_TYPE = 'bpm_process_listener_value_type',

  // ========== PAY 模块 ==========
  PAY_CHANNEL_CODE = 'pay_channel_code', // 支付渠道编码类型
  PAY_ORDER_STATUS = 'pay_order_status', // 商户支付订单状态
  PAY_REFUND_STATUS = 'pay_refund_status', // 退款订单状态
  PAY_NOTIFY_STATUS = 'pay_notify_status', // 商户支付回调状态
  PAY_NOTIFY_TYPE = 'pay_notify_type', // 商户支付回调状态
  PAY_TRANSFER_STATUS = 'pay_transfer_status', // 转账订单状态
  PAY_TRANSFER_TYPE = 'pay_transfer_type', // 转账订单状态

  // ========== MP 模块 ==========
  MP_AUTO_REPLY_REQUEST_MATCH = 'mp_auto_reply_request_match', // 自动回复请求匹配类型
  MP_MESSAGE_TYPE = 'mp_message_type', // 消息类型

  // ========== Member 会员模块 ==========
  MEMBER_POINT_BIZ_TYPE = 'member_point_biz_type', // 积分的业务类型
  MEMBER_EXPERIENCE_BIZ_TYPE = 'member_experience_biz_type', // 会员经验业务类型
  ALS_USER_TYPE = 'als_user_type', // 用户类型
  ALS_FEEDBACK_TYPE = 'als_feedback_type', // 用户反馈类型

  // ========== MALL - 商品模块 ==========
  PRODUCT_SPU_STATUS = 'product_spu_status', //商品状态

  // ========== MALL - 交易模块 ==========
  EXPRESS_CHARGE_MODE = 'trade_delivery_express_charge_mode', //快递的计费方式
  TRADE_AFTER_SALE_STATUS = 'trade_after_sale_status', // 售后 - 状态
  TRADE_AFTER_SALE_WAY = 'trade_after_sale_way', // 售后 - 方式
  TRADE_AFTER_SALE_TYPE = 'trade_after_sale_type', // 售后 - 类型
  TRADE_ORDER_TYPE = 'trade_order_type', // 订单 - 类型
  TRADE_ORDER_STATUS = 'trade_order_status', // 订单 - 状态
  TRADE_ORDER_ITEM_AFTER_SALE_STATUS = 'trade_order_item_after_sale_status', // 订单项 - 售后状态
  TRADE_DELIVERY_TYPE = 'trade_delivery_type', // 配送方式
  BROKERAGE_ENABLED_CONDITION = 'brokerage_enabled_condition', // 分佣模式
  BROKERAGE_BIND_MODE = 'brokerage_bind_mode', // 分销关系绑定模式
  BROKERAGE_BANK_NAME = 'brokerage_bank_name', // 佣金提现银行
  BROKERAGE_WITHDRAW_TYPE = 'brokerage_withdraw_type', // 佣金提现类型
  BROKERAGE_RECORD_BIZ_TYPE = 'brokerage_record_biz_type', // 佣金业务类型
  BROKERAGE_RECORD_STATUS = 'brokerage_record_status', // 佣金状态
  BROKERAGE_WITHDRAW_STATUS = 'brokerage_withdraw_status', // 佣金提现状态

  // ========== MALL - 营销模块 ==========
  PROMOTION_DISCOUNT_TYPE = 'promotion_discount_type', // 优惠类型
  PROMOTION_PRODUCT_SCOPE = 'promotion_product_scope', // 营销的商品范围
  PROMOTION_COUPON_TEMPLATE_VALIDITY_TYPE = 'promotion_coupon_template_validity_type', // 优惠劵模板的有限期类型
  PROMOTION_COUPON_STATUS = 'promotion_coupon_status', // 优惠劵的状态
  PROMOTION_COUPON_TAKE_TYPE = 'promotion_coupon_take_type', // 优惠劵的领取方式
  PROMOTION_ACTIVITY_STATUS = 'promotion_activity_status', // 优惠活动的状态
  PROMOTION_CONDITION_TYPE = 'promotion_condition_type', // 营销的条件类型枚举
  PROMOTION_BARGAIN_RECORD_STATUS = 'promotion_bargain_record_status', // 砍价记录的状态
  PROMOTION_COMBINATION_RECORD_STATUS = 'promotion_combination_record_status', // 拼团记录的状态
  PROMOTION_BANNER_POSITION = 'promotion_banner_position', // banner 定位

  // ========== CRM - 客户管理模块 ==========
  CRM_AUDIT_STATUS = 'crm_audit_status', // CRM 审批状态
  CRM_BIZ_TYPE = 'crm_biz_type', // CRM 业务类型
  CRM_BUSINESS_END_STATUS_TYPE = 'crm_business_end_status_type', // CRM 商机结束状态类型
  CRM_RECEIVABLE_RETURN_TYPE = 'crm_receivable_return_type', // CRM 回款的还款方式
  CRM_CUSTOMER_INDUSTRY = 'crm_customer_industry', // CRM 客户所属行业
  CRM_CUSTOMER_LEVEL = 'crm_customer_level', // CRM 客户级别
  CRM_CUSTOMER_SOURCE = 'crm_customer_source', // CRM 客户来源
  CRM_PRODUCT_STATUS = 'crm_product_status', // CRM 商品状态
  CRM_PERMISSION_LEVEL = 'crm_permission_level', // CRM 数据权限的级别
  CRM_PRODUCT_UNIT = 'crm_product_unit', // CRM 产品单位
  CRM_FOLLOW_UP_TYPE = 'crm_follow_up_type', // CRM 跟进方式

  // ========== ERP - 企业资源计划模块  ==========
  ERP_AUDIT_STATUS = 'erp_audit_status', // ERP 审批状态
  ERP_STOCK_RECORD_BIZ_TYPE = 'erp_stock_record_biz_type', // 库存明细的业务类型

  // ========== AI - 人工智能模块  ==========
  AI_PLATFORM = 'ai_platform', // AI 平台
  AI_IMAGE_STATUS = 'ai_image_status', // AI 图片状态
  AI_MUSIC_STATUS = 'ai_music_status', // AI 音乐状态
  AI_GENERATE_MODE = 'ai_generate_mode', // AI 生成模式
  AI_WRITE_TYPE = 'ai_write_type', // AI 写作类型
  AI_WRITE_LENGTH = 'ai_write_length', // AI 写作长度
  AI_WRITE_FORMAT = 'ai_write_format', // AI 写作格式
  AI_WRITE_TONE = 'ai_write_tone', // AI 写作语气
  AI_WRITE_LANGUAGE = 'ai_write_language', // AI 写作语言

  // =========== als - 陪学 ==========
  ALS_COMMUNICATE_PRE = 'als_communicate_pre', // als沟通前条件
  ALS_SOURCE_CHANNEL = 'als_source_channel', // als订单来源渠道
  ALS_COMMUNICATE_RESULT = 'als_communicate_result', // als沟通结果
  ALS_KID_CHR = 'als_kid_chr', // als孩子性格
  ALS_TIME_RANGE = 'als_time_range', // als陪学时间范围
  ALS_CHANGED_REASON_TAGS = 'als_changed_reason_tags', // als换老师原因
  ALS_KID_STAGE = 'als_kid_stage', // als孩子年级阶段
  ALS_HARD_REQUIRE_ABILITY = 'als_hard_require_ability', // als能力硬性要求
  ALS_NEEDS_FOCUS_TAGS = 'als_needs_focus_tags', // als侧重点
  ALS_NEEDS_TAGS = 'als_needs_tags', // als需求标签
  ALS_ACTIVE_TAGS = 'als_active_tags', // als需求标签
  ALS_ORDER_TYPE = 'als_order_type', // als订单类型
  ALS_SCHOOL_NATURE = 'als_school_nature', // als学校性质
  ALS_RANKING = 'als_ranking', // als大致排名
  ALS_TRACE_NOTE = 'als_trace_note', // als跟踪备注
  ALS_LEVER_TAGS = 'als_lever_tags', // als分级标签
  ALS_SERVICE_TAGS = 'als_service_tags', // als服务标签
  ALS_CUSTOMER_CHANNEL = 'als_customer_channel', // als获客渠道
  ALS_BLACKLIST_STATUS = 'als_blacklist_status', // als拉黑状态
  ALS_CUSTOMER_STATUS = 'als_customer_status', // als最新订单进程
  ALS_SEX = 'als_sex', // als性别
  ALS_IS_ON_WEEKEND = 'als_is_on_weekend', // als是否是周末订单
  ALS_YES_OR_ON = 'als_yes_or_on', // als是否
  ALS_CERTIFICATE_STATUS = 'als_certificate_status',
  ALS_INVITE_MEMBER_TYPE =  'als_invite_member_type', // als邀请会员类型
  ALS_AWARD_STATUS = 'als_award_status',// als奖励状态

  ALS_REQUIRE_SEX = 'als_require_sex', // als老师性别要求
  ALS_ORDER_PROCESS = 'als_order_process', // als订单进程
  ALS_RELEASE_STATUS = 'als_release_status', // als发布状态
  ALS_ORDER_STATUS = 'als_order_status', // als订单状态
  ALS_ORDER_CHANNEL = 'als_order_channel',
  ALS_BIND_STATUS='als_bind_status', // als绑定状态
  ALS_CUSTOMER_EVALUATION = 'als_customer_evaluation',// als家长评价
  ALS_ATTACHMENT_TYPE = 'als_attachment_type',// 附件类型
  ALS_BIZ_TYPE = 'als_biz_type',// 附件业务类型
  ALS_WHO_LEAVE = 'als_who_leave',// 请假方
  ALS_LEAVE_STAGE = 'als_leave_stage',// 请假时阶段
  ALS_FAQ_TYPE = 'als_faq_type',  // FAQ类型
  ALS_FAQ_WHO = 'als_faq_who',  // FAQ可见方

  // =========== als - 家长 ==========
  ALS_OPERATION_TAGS='als_operation_tags',// 运营标签
  ALS_LEVEL_TAGS='als_level_tags', // 分级标签
  ALS_SERVICE_STATUS ='als_service_status',//服务状态
  ALS_USE_STATUS = 'als_use_status', // 使用状态
  ALS_REFUND_REASON_TYPE = 'als_refund_reason_type', // 退款理由选择
  ALS_REFUND_STATUS = 'als_refund_status', // 退款状态
  ALS_REFUND_TYPE = 'als_refund_type', // 退款种类
  ALS_EVALUATION_MODULE = 'als_evaluation_module',// 评价模块

  // =========== als - 课程包 ==========
  ALS_PACKAGE_TYPE="als_package_type",// 课时包类型
  ALS_PAYMENT_METHOD="als_payment_method",// 支付方式
  ALS_DIRECTION_FROM="als_direction_from",// 资金流转来源
  ALS_AUDIT_STATUS="als_audit_status",//审批状态

  // =========== als - 老师 ==========
  ALS_POLITICAL_STATUS = 'als_political_status', // als政治面貌
  ALS_SCHOOL_STATUS = 'als_school_status', // als学校状态
  ALS_DEGREE = 'als_degree', // als学历
  ALS_TEACHER_LEVEL = 'als_teacher_level', // als老师级别
  ALS_SCHOOL_AWARDS = 'als_school_awards', // als学校奖项
  ALS_SCHOOL_AWARDS_LEVEL = 'als_school_awards_level', // 等级
  ALS_FORTE = 'als_forte', // als forte
  ALS_ID_TAGS = 'als_id_tags', // als身份标签
  ALS_FOREIGN_LANGUAGE = 'als_foreign_language', // als外语能力
  ALS_TEACHER_ABILITY_SPOKEN = 'als_teacher_ability_spoken', // als外语能力
  ALS_QUALITY_BASIC = 'als_quality_basic', // als基础能力
  ALS_QUALITY_LECTURE = 'als_quality_lecture',// 试讲
  ALS_QUALITY_COMPREHENSIVE = 'als_quality_comprehensive',// 综合素质
  ALS_PREPARE_SCORE = 'als_prepare_score',// als课前打分项
  ALS_SUMMARY_SCORE = 'als_summary_score',// als课后打分项
  ALS_TEACHER_ACCOUNT_BUSINESS_TYPE = 'als_teacher_account_business_type',// 账务变更业务类型
  ALS_WITHDRAW_STATUS="als_withdraw_status",//提现状态
  ALS_WITHDRAW_ACCOUNT_TYPE = "als_withdraw_account_type",//请选择提现类型
  ALS_FAV_TYPE = "als_fav_type", // 收藏类型

  // =========== als - 课时记录 ==========
  ALS_LESSON_TYPE = 'als_lesson_type',// 上课类型
  ALS_RECORD_SOURCE = 'als_record_source',// 课时来源
  ALS_CHILD_NUMBER = 'als_child_number',// 陪学人数
  ALS_RECORD_STATUS = 'als_record_status',// 课时记录状态
  ALS_PROCESS = 'als_process',// 填写进度
  ALS_PREPARE_ITEM = 'als_prepare_item',// 课前准备事项
  ALS_SUMMARY_ITEM = 'als_summary_item',// 课后总结事项
  ALS_LESSON_RECORD_STATUS = 'als_lesson_record_status',// 课时记录状态
  ALS_DEPOSIT_STATUS='als_deposit_status',// 押金状态
  ALS_DEPOSIT_DEAL_METHOD = 'als_deposit_deal_method',// 押金处理方式
  ALS_DEPOSIT_DEAL_STATUS = 'als_deposit_deal_status',// 押金处理状态
  ALS_SCHEDULE_TASK_STATUS = 'als_schedule_task_status',// 排课任务状态
  // ALS_DEAL_METHOD = 'als_deal_method', // 押金处理方式

  // =========== als - 订单 ==========
  ALS_CONFIRM_STATUS = 'als_confirm_status',// 订单确认
  ALS_DEAL_STATUS = 'als_deal_status',// 订单确认
  ALS_REJECT_REASON = 'als_reject_reason',// 拒绝原因ID

}
