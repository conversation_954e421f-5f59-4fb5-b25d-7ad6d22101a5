<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="70%">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="110px"
      v-loading="formLoading"
      inline
    >
      <el-form-item label="陪学记录ID" prop="lessonRecordId" class="!w-230px">
        <el-input v-model="formData.lessonRecordId" placeholder="请输入陪学记录ID" />
      </el-form-item>
      <el-form-item label="购买套餐ID" prop="customerPackageId" class="!w-230px">
        <el-input v-model="formData.customerPackageId" placeholder="请输入购买套餐ID" />
      </el-form-item>
      <el-form-item label="上课类型" prop="lessonType" class="!w-230px">
        <el-select v-model="formData.lessonType" placeholder="请选择上课类型">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_LESSON_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="人数" prop="childNumber" class="!w-230px">
        <el-select v-model="formData.childNumber" placeholder="请选择人数">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_CHILD_NUMBER)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="家长ID" prop="customerId" class="!w-230px">
        <el-input v-model="formData.customerId" placeholder="请输入家长ID" />
      </el-form-item>
      <el-form-item label="老师ID" prop="teacherId" class="!w-230px">
        <el-input v-model="formData.teacherId" placeholder="请输入老师ID" />
      </el-form-item>
      <el-form-item label="上课时长(h) " prop="timeHour" class="!w-230px">
        <el-input v-model="formData.timeHour" placeholder="押金时拆分" />
      </el-form-item>
      <el-form-item label="倍数" prop="multiple" class="!w-230px">
        <el-input v-model="formData.multiple" placeholder="请输入倍数" />
      </el-form-item>
      <el-form-item label="计算课时" prop="lessonHour" class="!w-230px">
        <el-input v-model="formData.lessonHour" placeholder="请输入总课时" />
      </el-form-item>
      <el-form-item label="总课时" prop="classHour" class="!w-230px">
        <el-input v-model="formData.classHour" placeholder="请输入总课时" />
      </el-form-item>
      <el-form-item label="老师-课时单价" prop="teacherPrice" class="!w-230px">
        <el-input v-model="formData.teacherPrice" placeholder="请输入老师-课时单价" />
      </el-form-item>
      <el-form-item label="老师-小时单价" prop="teacherHourPrice" class="!w-230px">
        <el-input v-model="formData.teacherHourPrice" placeholder="请输入老师-小时单价" />
      </el-form-item>
      <el-form-item label="老师-附加费用" prop="teacherExtraCharge" class="!w-230px">
        <el-input v-model="formData.teacherExtraCharge" placeholder="请输入老师-附加总费用" />
      </el-form-item>
      <el-form-item label="老师-总薪资" prop="teacherAmount" class="!w-230px">
        <el-input v-model="formData.teacherAmount" placeholder="请输入老师-总薪资" />
      </el-form-item>
      <el-form-item label="家长-课时单价" prop="customerPrice" class="!w-230px">
        <el-input v-model="formData.customerPrice" placeholder="请输入家长-课时单价" />
      </el-form-item>
      <el-form-item label="家长-扣费金额" prop="customerCost" class="!w-230px">
        <el-input v-model="formData.customerCost" placeholder="请输入家长-扣费金额" />
      </el-form-item>
      <el-form-item label="下次上课时间" prop="nextTime">
        <el-date-picker
          v-model="formData.nextTime"
          type="datetime"
          value-format="x"
          placeholder="选择下次上课时间"
          class="!w-230px"
        />
      </el-form-item>
      <el-form-item label="课时记录状态" prop="recordStatus" class="!w-230px">
        <el-select v-model="formData.recordStatus" placeholder="请选择课时记录状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_LESSON_RECORD_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态" prop="auditStatus" class="!w-230px">
        <el-select v-model="formData.auditStatus" placeholder="请选择审核状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_AUDIT_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审核时间" prop="auditTime">
        <el-date-picker
          v-model="formData.auditTime"
          type="datetime"
          value-format="x"
          placeholder="选择审核时间"
          class="!w-230px"
        />
      </el-form-item>
      <el-form-item label="审核人" prop="auditUserId" class="!w-230px">
        <el-input v-model="formData.auditUserId" placeholder="请输入审核人" />
      </el-form-item>
      <el-form-item label="审核备注" prop="auditRemark" class="!w-100%">
        <el-input v-model="formData.auditRemark" placeholder="请输入审核备注" type="textarea" rows="1" show-word-limit maxlength="50" />
      </el-form-item>

      <el-form-item label="押金处理方式" prop="dealMethod" class="!w-260px" clas>
        <el-select v-model="formData.dealMethod" placeholder="请选择押金处理方式" clearable filterable>
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_DEPOSIT_DEAL_METHOD)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>

      </el-form-item>
      <el-form-item label="押金处理方式ID" prop="dealMethod" class="!w-230px">
        <el-input v-model="formData.dealMethod" placeholder="押金处理方式" />
      </el-form-item>
      <el-form-item label="处理人" prop="dealUserId" class="!w-230px">
        <el-input v-model="formData.dealUserId" placeholder="请输入处理人" />
      </el-form-item>
      <el-form-item label="处理时间" prop="dealTime">
        <el-date-picker
          v-model="formData.dealTime"
          type="date"
          value-format="x"
          placeholder="选择处理时间"
          class="!w-230px"
        />
      </el-form-item>
      <el-form-item label="家长获得课时" prop="customerGet" class="!w-230px">
        <el-input v-model="formData.customerGet" placeholder="押金处理家长获得课时-上课时长(h)" />
      </el-form-item>
      <el-form-item label="老师获得课时" prop="teacherGet" class="!w-230px">
        <el-input v-model="formData.teacherGet" placeholder="押金处理老师获得课时-上课时长(h)" />
      </el-form-item>
      <el-form-item label="平台获得课时" prop="platformGet" class="!w-230px">
        <el-input v-model="formData.platformGet" placeholder="押金处理平台获得课时-上课时长(h)" />
      </el-form-item>
      <el-form-item label="备注时间" prop="remarkTime">
        <el-date-picker
          v-model="formData.remarkTime"
          type="date"
          value-format="x"
          placeholder="选择备注时间"
          class="!w-230px"
        />
      </el-form-item>
      <el-form-item label="备注人" prop="remarkUserId" class="!w-230px">
        <el-input v-model="formData.remarkUserId" placeholder="请输入备注人" />
      </el-form-item>
      <el-form-item label="备注" prop="remark" class="!w-100%">
        <el-input v-model="formData.remark" placeholder="请输入备注" type="textarea" show-word-limit maxlength="200" rows="4"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { LessonHourApi, LessonHourVO } from '@/api/als/lessonhour'

/** 课时记录 表单 */
defineOptions({ name: 'LessonHourForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  lessonHourId: undefined,
  lessonRecordId: undefined,
  customerPackageId: undefined,
  lessonType: undefined,
  childNumber: undefined,
  customerId: undefined,
  teacherId: undefined,
  timeHour: 0,
  multiple: 0,
  classHour: 0,
  lessonHour: 0,
  teacherPrice: undefined,
  teacherHourPrice: undefined,
  teacherExtraCharge: undefined,
  teacherAmount: undefined,
  customerPrice: undefined,
  customerCost: undefined,
  nextTime: undefined,
  recordStatus: undefined,
  auditStatus: undefined,
  auditTime: undefined,
  auditUserId: undefined,
  auditRemark: undefined,
  dealMethod: 0,
  dealUserId: undefined,
  dealTime: undefined,
  customerGet: undefined,
  teacherGet: undefined,
  platformGet: undefined,
  remarkTime: undefined,
  remarkUserId: undefined,
  remark: undefined
})
const formRules = reactive({
  lessonRecordId: [{ required: true, message: '陪学记录ID不能为空', trigger: 'blur' }],
  customerPackageId: [{ required: true, message: '购买套餐ID不能为空', trigger: 'blur' }],
  lessonType: [{ required: true, message: '上课类型不能为空', trigger: 'change' }],
  childNumber: [{ required: true, message: '人数不能为空', trigger: 'change' }],
  customerId: [{ required: true, message: '家长ID不能为空', trigger: 'blur' }],
  teacherId: [{ required: true, message: '老师ID不能为空', trigger: 'blur' }],
  timeHour: [{ required: true, message: '上课时长(h) 押金时拆分不能为空', trigger: 'blur' }],
  multiple: [{ required: true, message: '倍数不能为空', trigger: 'blur' }],
  nextTime: [{ required: true, message: '下次上课时间不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await LessonHourApi.getLessonHour(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as LessonHourVO
    if (formType.value === 'create') {
      await LessonHourApi.createLessonHour(data)
      message.success(t('common.createSuccess'))
    } else {
      await LessonHourApi.updateLessonHour(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    lessonHourId: undefined,
    lessonRecordId: undefined,
    customerPackageId: undefined,
    lessonType: undefined,
    childNumber: undefined,
    customerId: undefined,
    teacherId: undefined,
    timeHour: 0,
    multiple: 0,
    classHour: 0,
    lessonHour: 0,
    teacherPrice: undefined,
    teacherHourPrice: undefined,
    teacherExtraCharge: undefined,
    teacherAmount: undefined,
    customerPrice: undefined,
    customerCost: undefined,
    nextTime: undefined,
    recordStatus: undefined,
    auditStatus: undefined,
    auditTime: undefined,
    auditUserId: undefined,
    auditRemark: undefined,
    dealMethod: 0,
    dealUserId: undefined,
    dealTime: undefined,
    customerGet: undefined,
    teacherGet: undefined,
    platformGet: undefined,
    remarkTime: undefined,
    remarkUserId: undefined,
    remark: undefined
  }
  formRef.value?.resetFields()
}

const classHour = computed(() => {
  // const result = Math.ceil(formData.value.timeHour *  formData.value.multiple * 100) / 100; // 向上取整并保留两位小数
  if (formData.value.multiple != 0){
    const result = Math.ceil(formData.value.lessonHour /  formData.value.multiple * 100)/100
    return result.toFixed(2);
  }
  return "0";
});

// 如果需要将计算结果赋值给 formData
watch(classHour, (newValue) => {
  formData.value.timeHour = parseFloat(newValue); // 更新 formData 中的 lessonHour
});
</script>
