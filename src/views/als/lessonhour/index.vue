<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="陪学记录ID" prop="lessonRecordId" >
        <el-input
          v-model="queryParams.lessonRecordId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      <el-form-item label="扣费课时包ID" prop="customerPackageId">
        <el-input
          v-model="queryParams.customerPackageId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      <el-form-item label="上课类型" prop="lessonType">
        <el-select
          v-model="queryParams.lessonType"
          clearable
          class="!w-150px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_LESSON_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="人数" prop="childNumber">
        <el-select
          v-model="queryParams.childNumber"
          clearable
          class="!w-150px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_CHILD_NUMBER)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="家长ID" prop="customerId">
        <el-input
          v-model="queryParams.customerId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      
      <CollapseWithButton
        v-model:collapsed="isCollapsed"
        expand-text="展开"
        collapse-text="收起"
        expand-icon="ep:arrow-down"
        collapse-icon="ep:arrow-up"
      >
      <el-form-item label="老师ID" prop="teacherId">
        <el-input
          v-model="queryParams.teacherId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      <el-form-item label="倍数" prop="multiple">
        <el-input
          v-model="queryParams.multiple"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      <el-form-item label="总课时" prop="classHour">
        <el-input
          v-model="queryParams.classHour"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      <el-form-item label="课时记录状态" prop="recordStatus">
        <el-select
          v-model="queryParams.recordStatus"
          clearable
          class="!w-150px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_LESSON_RECORD_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>


      <el-form-item label="记录审核状态" prop="auditStatus">
        <el-select
          v-model="queryParams.auditStatus"
          clearable
          class="!w-150px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_AUDIT_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审核时间" prop="auditTime">
        <el-date-picker
          v-model="queryParams.auditTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="审核人" prop="auditUserId">
        <el-input
          v-model="queryParams.auditUserId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      <el-form-item label="审核备注" prop="auditRemark">
        <el-input
          v-model="queryParams.auditRemark"
          placeholder="模糊搜索"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>

      <el-form-item label="押金处理方式" prop="dealMethod">
        <el-select
          v-model="queryParams.dealMethod"
          clearable
          class="!w-150px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_DEPOSIT_DEAL_METHOD)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="下次上课时间" prop="nextTime">
        <el-date-picker
          v-model="queryParams.nextTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-200px"
        />
      </el-form-item>

      <el-form-item label="处理时间" prop="dealTime">
        <el-date-picker
          v-model="queryParams.dealTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-200px"
        />
      </el-form-item>

      <el-form-item label="处理人" prop="dealUserId">
        <el-input
          v-model="queryParams.dealUserId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>


      <el-form-item label="备注人" prop="remarkUserId">
        <el-input
          v-model="queryParams.remarkUserId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="queryParams.remark"
          placeholder="模糊搜索"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      <el-form-item label="备注时间" prop="remarkTime">
        <el-date-picker
          v-model="queryParams.remarkTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-200px"
        />
      </el-form-item>
        <!-- 操作按钮放在折叠组件的after-button插槽中 -->
        <template #after-button>
          <div class="search-button-container ml-10px">
            <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
            <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
            <el-button
              type="primary"
              plain
              @click="openForm('create')"
              v-hasPermi="['als:lesson-hour:create']"
            >
              <Icon icon="ep:plus" class="mr-5px" /> 新增
            </el-button>
            <el-button
              type="success"
              plain
              @click="handleExport"
              :loading="exportLoading"
              v-hasPermi="['als:lesson-hour:export']"
            >
              <Icon icon="ep:download" class="mr-5px" /> 导出
            </el-button>
         </div>
        </template>
      </CollapseWithButton>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" border size="small">
      <el-table-column label="ID" align="center" prop="lessonHourId"  width="70" />
      <el-table-column label="老师信息" header-align="left" align="left" width="200" >
        <template #default="scope">
          <div>
            <span class="column-label">老师ID：</span>
            <span>{{ scope.row.teacherId }}</span>
          </div>
          <div>
            <span class="column-label">姓名：</span>
            <span>{{ scope.row.teacherName }}</span>
          </div>
          <div>
            <span class="column-label">手机：</span>
            <span>{{ scope.row.teacherPhone }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="家长信息" header-align="left" align="left" width="200" >
        <template #default="scope">
          <div>
            <span class="column-label">家长ID：</span>
            <span>{{ scope.row.customerId }}</span>
          </div>
          <div>
            <span class="column-label">姓名：</span>
            <span>{{ scope.row.customerName }}</span>
          </div>
          <div>
            <span class="column-label">手机：</span>
            <span>{{ scope.row.customerPhone }}</span>
          </div>
          
        </template>
      </el-table-column>

      <el-table-column label="扣费课时包" align="center" prop="customerPackageId" width="100"/>
      <el-table-column label="课时记录状态" align="center" prop="recordStatus" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ALS_LESSON_RECORD_STATUS" :value="scope.row.recordStatus" />
        </template>
      </el-table-column>
      <el-table-column label="陪学内容" align="left" prop="lessonType" width="250">
        <template #default="scope">
          <div>
            <span>陪学记录ID：</span>
            <span>{{ scope.row.lessonRecordId }}</span>
            <el-button  link type="warning" size="small" class="ml-5px" @click="openDetail(scope.row.lessonRecordId)">查看详情</el-button>
          </div>
          <div>
            <span>下次上课时间：</span>
            <span>{{ formatDate(scope.row.nextTime)}}</span>
          </div>
          <div>
            <span>上课类型：</span>
            <dict-tag-text :type="DICT_TYPE.ALS_LESSON_TYPE" :value="scope.row.lessonType" />
          </div>
          <div>
            <span class="column-label">人数：</span>
            <dict-tag-text :type="DICT_TYPE.ALS_CHILD_NUMBER" :value="scope.row.childNumber" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="上课时长" align="left" prop="lessonType" width="140">
        <template #default="scope">
          <div>
            <span>上课时长：</span>
            <span>{{ scope.row.timeHour }}</span> h
          </div>
          <div>
            <span>倍数：</span>
            <span>{{ scope.row.multiple }}</span> 倍
          </div>
          <div>
            <span>合计：</span>
            <span>{{ scope.row.lessonHour }}</span> 课时
          </div>
        </template>
      </el-table-column>

      <el-table-column label="老师-薪资" align="left" prop="lessonType" width="150">
        <template #default="scope">
          <div>
            <span>课时单价：</span>
            <span>{{ scope.row.teacherPrice }}</span> 元
          </div>
          <div>
            <span>小时单价：</span>
            <span>{{ scope.row.teacherHourPrice }}</span> 元
          </div>
          <div>
            <span>附加费用：</span>
            <span>{{ scope.row.teacherExtraCharge }}</span> 元
          </div>
          <div>
            <span>总薪资：</span>
            <span>{{ scope.row.teacherAmount }}</span> 元
          </div>
        </template>
      </el-table-column>
      <el-table-column label="总课时" align="center" prop="classHour" width="100">
        <template #default="scope">
          <span>{{ scope.row.classHour }} 课时</span>
        </template>
      </el-table-column>

      <el-table-column label="家长-课时包" align="left" prop="lessonType" width="140">
        <template #default="scope">
          <div>
            <span>扣费课时包：</span>
            <span>{{ scope.row.customerPackageId }}</span>
          </div>
          <div>
            <span>课时单价：</span>
            <span>{{ scope.row.customerPrice }}</span> 元
          </div>
          <div>
            <span>扣费金额：</span>
            <span>{{ scope.row.customerCost }}</span> 元
          </div>
        </template>
      </el-table-column>

      <el-table-column label="审核信息" align="left" width="350">
        <template #default="scope">
          <div class="flex flex-justify-between">
            <div>
              <span>审核状态：</span>
              <span v-if="scope.row.auditStatus == 0">-</span>
              <span v-else><dict-tag :type="DICT_TYPE.ALS_AUDIT_STATUS" :value="scope.row.auditStatus" /></span>
            </div>
           <div>
             <span class="right">审核人：</span>
             <span>{{ scope.row.auditUserId }}</span>
           </div>
          </div>
          <div>
            <span>审核时间：</span>
            <span>{{ formatDate(scope.row.auditTime) }}</span>
          </div>
          <div>

          </div>
          <div class="h-15 overflow-y-auto">
            <span>审核备注：</span>
            <span>{{ scope.row.auditRemark }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="押金" align="left" width="200">
        <template #default="scope">
          <div>
            <span>状态：</span>
            <span v-if="scope.row.deposit.depositStatus == 0">-</span>
            <span v-else><dict-tag :type="DICT_TYPE.ALS_DEPOSIT_STATUS" :value="scope.row.deposit.depositStatus" /></span>
          </div>
          <div>
            <span>处理方式：</span>
            <span v-if="scope.row.deposit.dealMethod == 0">-</span>
            <span v-else><dict-tag :type="DICT_TYPE.ALS_DEPOSIT_DEAL_METHOD" :value="scope.row.deposit.dealMethod" /></span>
          </div>
          <div>
            <span>处理时间：</span>
            <span>{{ formatDate(scope.row.deposit.dealTime) }}</span>
          </div>
          <div>
            <span class="right">处理人：</span>
            <span>{{ scope.row.deposit.dealUserId }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="备注" align="left" width="300">
        <template #default="scope">
          <div class="flex flex-justify-between">
           <div>
             <span>备注人：</span>
             <span>{{ scope.row.remarkUserId }}</span>
           </div>
            <div>
              <span>备注时间：</span>
              <span>{{ formatDate(scope.row.remarkTime) }}</span>
            </div>
          </div>
          <div class="h-20 overflow-y-auto">
            <span>备注内容：</span>
            <span>{{ scope.row.remark }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" fixed="right" width="250">
        <template #default="scope">
          <el-button
            plain
            size="small"
            type="primary"
            @click="openForm('update', scope.row.lessonHourId)"
            v-hasPermi="['als:lesson-hour:update']"
          >
            编辑
          </el-button>
<!--          审核状态 1审核中 2审核通过 3驳回-->
          <el-button
            plain
            size="small"
            type="primary"
            @click="openForm1('update', scope.row.lessonHourId)"
            v-hasPermi="['als:lesson-hour:update']"
            :disabled="scope.row.auditStatus > 1"
          >
            审核
          </el-button>

            <el-button
              plain
              size="small"
              type="primary"
              @click="openForm2( scope.row.lessonHourId,scope.row.deposit.classHour)"
              v-hasPermi="['als:lesson-hour:update']"
              v-if="scope.row.recordStatus == 3"
            >
              押金处理
            </el-button>

          <el-button
            plain
            size="small"
            type="danger"
            @click="handleDelete(scope.row.lessonHourId)"
            v-hasPermi="['als:lesson-hour:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <LessonHourForm ref="formRef" @success="getList" />
  <!--  审核-->
  <AuditForm ref="formRef1" @audit-call-back="auditSubmit"/>
  <!--  处理押金弹窗：处理押金-->
  <DealDepositForm ref="formRef2" @success="getList" />

</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import {dateFormatter, formatDate} from '@/utils/formatTime'
import download from '@/utils/download'
import { LessonHourApi, LessonHourVO } from '@/api/als/lessonhour'
import LessonHourForm from './LessonHourForm.vue'
import DealDepositForm from '../depositrefundapply/DealDepositForm.vue'
import AuditForm from "@/views/als/audit/AuditForm.vue";
import {AuditReqVO} from "@/api/als/lessonplan";

/** 课时记录 列表 */
defineOptions({ name: 'LessonHour' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const isCollapsed = ref(true); // 添加折叠状态控制

const loading = ref(true) // 列表的加载中
const list = ref<LessonHourVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  lessonRecordId: undefined,
  customerPackageId: undefined,
  lessonType: undefined,
  childNumber: undefined,
  customerId: undefined,
  teacherId: undefined,
  timeHour: undefined,
  multiple: undefined,
  classHour: undefined,
  teacherPrice: undefined,
  teacherHourPrice: undefined,
  teacherExtraCharge: undefined,
  teacherAmount: undefined,
  customerPrice: undefined,
  customerCost: undefined,
  nextTime: [],
  recordStatus: [],
  auditStatus: [],
  auditTime: [],
  auditUserId: undefined,
  auditRemark: undefined,
  dealMethod: undefined,
  dealUserId: undefined,
  dealTime: [],
  remarkTime: [],
  remarkUserId: undefined,
  remark: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await LessonHourApi.getLessonHourPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

// 处理押金
const formRef2 = ref()
const openForm2 = (id?: number, hour?: number) => {
  formRef2.value.open( id,hour)
}

/** 审核 */
const formRef1 = ref()
const openForm1 = (type: string, id?: number) => {
  formRef1.value.open(type, id)
}
const closeForm1 = async () => {
  formRef1.value.close()
}

const auditSubmit = async (result: any) => {
  try {
    await LessonHourApi.auditLessonHour(result)
    message.success("审核完成")
  }finally {
    await closeForm1()
    await getList()
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await LessonHourApi.deleteLessonHour(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await LessonHourApi.exportLessonHour(queryParams)
    download.excel(data, '课时记录.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 打开课时记录详情 */
const { push } = useRouter()
const openDetail = (id: number) => {
  push({ name: 'LessonRecord2', params: { id } })
}

const route = useRoute()
const customerId = Number(route.params.customerId)

/** 初始化 **/
onMounted(() => {
  if (customerId) {
    queryParams.customerId = customerId
  }
  getList()
})
</script>
