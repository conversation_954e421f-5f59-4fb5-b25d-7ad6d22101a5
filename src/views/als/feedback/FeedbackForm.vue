<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="用户类型" prop="memberType">
        <el-select v-model="formData.memberType" placeholder="请选择用户类型">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_USER_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="用户ID" prop="memberId">
        <el-input v-model="formData.memberId" placeholder="请输入用户ID" />
      </el-form-item>
      <el-form-item label="反馈类型" prop="feedbackType">
        <el-select v-model="formData.feedbackType" placeholder="请选择反馈类型">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_FEEDBACK_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="反馈标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入反馈标题" />
      </el-form-item>
      <el-form-item label="反馈内容" prop="content">
        <Editor v-model="formData.content" height="150px" />
      </el-form-item>
      <el-form-item label="图片地址" prop="picUrl">
        <el-input v-model="formData.picUrl" placeholder="请输入图片地址" />
      </el-form-item>
      <el-form-item label="状态" prop="feedbackStatus">
        <el-radio-group v-model="formData.feedbackStatus">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_DEAL_STATUS)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { FeedbackApi, FeedbackVO } from '@/api/als/feedback'

/** 问题反馈 表单 */
defineOptions({ name: 'FeedbackForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  feedbackId: undefined,
  memberType: undefined,
  memberId: undefined,
  feedbackType: undefined,
  title: undefined,
  content: undefined,
  picUrl: undefined,
  feedbackStatus: undefined,
  remark: undefined
})
const formRules = reactive({
  memberType: [{ required: true, message: '用户类型不能为空', trigger: 'change' }],
  memberId: [{ required: true, message: '用户ID不能为空', trigger: 'blur' }],
  feedbackType: [{ required: true, message: '反馈类型不能为空', trigger: 'change' }],
  title: [{ required: true, message: '反馈标题不能为空', trigger: 'blur' }],
  content: [{ required: true, message: '反馈内容不能为空', trigger: 'blur' }],
  picUrl: [{ required: true, message: '图片地址不能为空', trigger: 'blur' }],
  feedbackStatus: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
  remark: [{ required: true, message: '备注不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await FeedbackApi.getFeedback(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as FeedbackVO
    if (formType.value === 'create') {
      await FeedbackApi.createFeedback(data)
      message.success(t('common.createSuccess'))
    } else {
      await FeedbackApi.updateFeedback(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    feedbackId: undefined,
    memberType: undefined,
    memberId: undefined,
    feedbackType: undefined,
    title: undefined,
    content: undefined,
    picUrl: undefined,
    feedbackStatus: undefined,
    remark: undefined
  }
  formRef.value?.resetFields()
}
</script>