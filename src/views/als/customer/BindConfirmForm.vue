<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="80px"
      v-loading="formLoading"
      :inline="true"
    >
      <el-form-item label="家长ID" prop="customerId">
        <el-input v-model="formData.customerId" disabled/>
      </el-form-item>

      <el-form-item label="家长姓名">
        <el-input v-model="customerName" disabled/>
      </el-form-item>

      <div class="flex flex-justify-center">
        <el-divider style="margin:15px 0;width: 40%;">
          <el-icon><Lock /></el-icon>
        </el-divider>
      </div>


      <el-form-item label="老师ID" prop="teacherId">
        <el-input v-model="formData.teacherId" disabled />
      </el-form-item>

      <el-form-item label="老师姓名">
        <el-input v-model="teacherName" disabled />
      </el-form-item>

      <el-form-item label="绑定原因" prop="bindReason" class="w-100%">
        <el-input v-model="formData.bindReason"
                  type="textarea"
                  placeholder="请简要说明绑定原因"
                  rows="4"
                  maxlength="100"
                  show-word-limit/>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { BindApi, BindVO,FormData } from '@/api/als/bind'
import {CustomerApi, CustomerVO} from "@/api/als/customer";
import {TeacherApi, TeacherVO} from "@/api/als/teacher";
import {Lock} from "@element-plus/icons-vue";

/** 绑定 表单 */
defineOptions({ name: 'BindConfirmForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const customerName = ref<string>('');
const teacherName = ref<string>('');

const formData = ref<FormData>({
  teacherId: undefined,
  customerId: undefined,
  bindReason: undefined
})
const formRules = reactive({
  teacherId: [{ required: true, message: '老师ID不能为空', trigger: 'blur' }],
  customerId: [{ required: true, message: '家长ID不能为空', trigger: 'blur' }],
  bindReason: [{ required: true, message: '绑定原因不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

const customerObj = ref<CustomerVO>() // 客户
const teacherObj = ref<TeacherVO>() // 客户

/** 打开弹窗 */
const open = async (customerId?: number, teacherId?: number) => {
  dialogVisible.value = true
  dialogTitle.value = "绑定确认"

  formData.value.bindReason = undefined

  formData.value.customerId = customerId
  formData.value.teacherId = teacherId

  if (customerId) {
    customerObj.value = await CustomerApi.getCustomer(customerId)
    if (customerObj.value){
      customerName.value = customerObj.value.customerName
    }
  }

  if (teacherId) {
    teacherObj.value = await TeacherApi.getTeacher(teacherId)
    if (teacherObj.value){
      teacherName.value = teacherObj.value.teacherName
    }
  }

  // 修改时，设置数据
  // if (id) {
  //   formLoading.value = true
  //   try {
  //     formData.value = await BindApi.getBind(id)
  //   } finally {
  //     formLoading.value = false
  //   }
  // }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as BindVO
    await BindApi.createBind(data)
    message.success("绑定成功")
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
</script>
