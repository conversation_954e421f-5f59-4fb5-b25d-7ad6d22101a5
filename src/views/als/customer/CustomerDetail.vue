<script setup lang="ts">
import { useTagsViewStore } from '@/store/modules/tagsView'
import {CustomerApi} from "@/api/als/customer";
import {DICT_TYPE} from "@/utils/dict";
import { formatDate } from '@/utils/formatTime'
import {BindApi} from "@/api/als/bind";
import TeacherDetail from "@/views/als/teacher/TeacherDetail.vue";

defineOptions({ name: 'CustomerDetail' })

const message = useMessage() // 消息弹窗
const detail = ref({
  customerRespVO:{
    customerId: 0,
    customerName: '',
    customerPhone: '',
    levelTags:[],
    serviceTeacherNameList:[],
    customerRemark: '',
    buyTimes:0,
    lessonPeriod:0,
    lessonPeriodRemain:0,
    lessonPeriodUsed:0,
    lastBuyTime:''
  },
  customerOrderDetailVO: {
    orderCount: 0,
    serviceCount: 0,
    recordCount: 0,
    refundCount: 0
  },
  lastOrder:{
    demandContent:'',
    orderAddress:'',
    sourceChannel:0,
    registerTime: ''
  },
  bindRespVOList:[{
    bindId:0,
    bindStatus:0,
    bindTime:'',
    unbindAuditStatus:0,
    unbindTime:'',
    teacherId:0,
    teacherName:'',
    universityName:'',
    lastLessonRecordDate:'',
    orderCount: 0
  }]
})

const route = useRoute()
const id = Number(route.params.id) // 编号

/** 初始化 */
const { delView } = useTagsViewStore() // 视图操作
const { currentRoute } = useRouter() // 路由
onMounted(async () => {
  if (!id) {
    delView(unref(currentRoute))
    return
  }
  await teacherDetail(id)
})


const toDateTime = (datatimes) => {
  if (!datatimes) {
    return ''; // 如果没有传入日期，返回空字符串
  }
  return formatDate(datatimes); // 将字符串转换为 Date 对象
}

const teacherDetail = async (id: number) => {
  console.log('查看详情',id)
  try {
    detail.value = await CustomerApi.getCustomerDetail(id)
  }catch (e){
    message.error("获取详情失败",e)
  }
}

/**
 * 解除绑定
 * @param bindId
 */
const unbind = async (bindId: number) => {
  try {
    // 删除的二次确认
    await message.confirm("确认要解除绑定吗？")
    // 发起删除
    await BindApi.unbind(bindId)
    message.success("提交申请成功")
    // 刷新当前页面
    await teacherDetail(id)
  } catch {
    console.log("解绑失败")
  }
}

/** 详情 */
const formRef2 = ref()
const openForm2 = (id?: number) => {
  formRef2.value.open( id)
}

const { push } = useRouter()
/**
 * 体验单数
 * @param customerId
 */
const openDetail = async (customerId: number) => {
  await push({ name: 'ExpOrder3', params: { customerId } });
}

const toLessonHourList = async (customerId: number) => {
  await push({ name: 'CustomerLessonHour', params: { customerId } });
}

const toRefundApplyList = async (customerId: number) => {
  await push({ name: 'CustomerRefundApply', params: { customerId } });
}

</script>

<template>
  <div class="content">
    <div class="left">
      <el-space fill
                direction="vertical"
                :fill-ratio="100"
                style="width: 100%">
        <el-card class="box-card">
          <el-row :gutter="50">
            <el-col :span="6">
              <el-card shadow="hover" class="text-center hover-pointer" @click="openDetail(detail.customerRespVO.customerId)">
                <div class="font-bold font-size-4">{{ detail.customerOrderDetailVO.orderCount }}</div>
                <div>体验单数</div>
              </el-card>
            </el-col>

            <el-col :span="6">
              <el-card shadow="hover" class="text-center hover-pointer" @click="openDetail(detail.customerRespVO.customerId)">
                <div class="font-bold font-size-4">{{ detail.customerOrderDetailVO.serviceCount }}</div>
                <div>正在服务</div>
              </el-card>
            </el-col>

            <el-col :span="6">
              <el-card shadow="hover" class="text-center hover-pointer" @click="toLessonHourList(detail.customerRespVO.customerId)">
                <div class="font-bold font-size-4">{{ detail.customerOrderDetailVO.recordCount }}</div>
                <div>上门陪学次数</div>
              </el-card>
            </el-col>

            <el-col :span="6">
              <el-card shadow="hover" class="text-center hover-pointer" @click="toRefundApplyList(detail.customerRespVO.customerId)">
                <div class="font-bold font-size-4">{{ detail.customerOrderDetailVO.refundCount }}</div>
                <div>退款次数</div>
              </el-card>
            </el-col>
          </el-row>
        </el-card>

        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span class="font-600">家长信息</span>
<!--              <el-button class="button" text>查看</el-button>-->
            </div>
          </template>
          <el-descriptions column="3" border direction="horizontal" class="my-desc">
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">ID</div>
              </template>
              {{ detail.customerRespVO.customerId }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">家长姓名</div>
              </template>
              {{ detail.customerRespVO.customerName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">手机</div>
              </template>
              {{ detail.customerRespVO.customerPhone }}
            </el-descriptions-item>
          </el-descriptions>
          <el-descriptions column="1" border direction="horizontal" class="my-desc">
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">标签</div>
              </template>
              <div>
                <span v-for="tag in detail.customerRespVO.levelTags" :key="tag" class="mr-2">
                  <dict-tag :type="DICT_TYPE.ALS_LEVEL_TAGS" :value="tag" />
                </span>
              </div>
            </el-descriptions-item>

            <el-descriptions-item>
              <template #label>
                <div class="cell-item">服务过的老师</div>
              </template>
              <div class="auto-wrap">
                <span v-for="(teacher,index) in detail.customerRespVO.serviceTeacherNameList" :key="index" class="mr-1">
                  {{ teacher }}<span v-if="index < detail.customerRespVO.serviceTeacherNameList.length - 1"> /</span>
                </span>
              </div>
            </el-descriptions-item>

            <el-descriptions-item>
              <template #label>
                <div class="cell-item">最新备注</div>
              </template>
              <div class="auto-wrap">
                <span>  {{ detail.customerRespVO.customerRemark }}</span>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span class="font-600">最新体验单</span>
<!--              <el-button class="button" text>更多</el-button>-->
            </div>
          </template>
          <el-descriptions column="1" border direction="horizontal" class="my-desc">
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">最新需求</div>
              </template>
              <div class="auto-wrap">
                <span>{{ detail.lastOrder?.demandContent || '-' }}</span>
              </div>
            </el-descriptions-item>

            <el-descriptions-item>
              <template #label>
                <div class="cell-item">最新地址</div>
              </template>
              {{ detail.lastOrder?.orderAddress || '-' }}
            </el-descriptions-item>

            <el-descriptions-item>
              <template #label>
                <div class="cell-item">最新渠道</div>
              </template>
              <dict-tag v-if="detail.lastOrder?.sourceChannel" :type="DICT_TYPE.ALS_SOURCE_CHANNEL" :value="detail.lastOrder.sourceChannel" />
            </el-descriptions-item>

            <el-descriptions-item>
              <template #label>
                <div class="cell-item">下单时间</div>
              </template>
              {{ detail.lastOrder?.registerTime ? toDateTime(detail.lastOrder.registerTime) : '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span class="font-600">购买信息</span>
<!--              <el-button class="button" text>查看</el-button>-->
            </div>
          </template>
          <el-descriptions title="" column="5" border direction="vertical">
            <el-descriptions-item label-align="center" align="center" label="购买次数">
              <span v-if="detail.customerRespVO.buyTimes > 0">{{ detail.customerRespVO.buyTimes }}</span>
            </el-descriptions-item>
            <el-descriptions-item label-align="center" align="center" label="总课时数">
              {{ detail.customerRespVO.lessonPeriod }}
            </el-descriptions-item>
            <el-descriptions-item label-align="center" align="center" label="使用课时数"> {{ detail.customerRespVO.lessonPeriodUsed }}</el-descriptions-item>
            <el-descriptions-item label-align="center" align="center" label="剩余课时"> {{ detail.customerRespVO.lessonPeriodRemain }}</el-descriptions-item>
            <el-descriptions-item label-align="center" align="center" label="最近购买时间"> {{ toDateTime(detail.customerRespVO.lastBuyTime) }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-space>
    </div>

    <div class="right">
      <el-card class="box-card" v-if="detail.bindRespVOList.length > 0">
        <template #header>
          <div class="card-header">
            <span class="font-600">匹配老师</span>
            <!--              <el-button class="button" text>更多</el-button>-->
          </div>
        </template>
<!--      <ContentWrap>  :color="item.bindStatus == 3 ? 'grey' : '--el-color-success'" -->
        <el-timeline
          v-for="item in detail.bindRespVOList"
          :key="item.bindId">
          <el-timeline-item  placement="top"
                             type="success"
                             :color="item.bindStatus == 3 ? '#A8ABB2' : '--el-color-success'"
                            :timestamp="`绑定时间: ${ formatDate(item.bindTime) }`">
            <template #timestamp>
              <div class="text-sm">
                <span class="text-gray-400">匹配老师：</span>
                {{ item.teacherName }}
              </div>
            </template>
            <el-card v-if="item.bindStatus==1 || item.bindStatus==2" class="teacher-card">
              <div class="match-teacher">
                <div>
                  <span>当前绑定老师：</span>
                  <el-link :underline="false" type="warning" @click="openForm2(item.teacherId)" style="vertical-align: text-bottom;font-size: 12px" >
                    {{ item.teacherName }}
                  </el-link>
                </div>
                <div>{{ item.universityName }}</div>
                <div>接单次数：{{ item.orderCount }}</div>
              </div>
              <div class="match-teacher">
                <div>最近陪学时间： {{ toDateTime(item.lastLessonRecordDate) }}</div>
                <el-button v-if="item.unbindAuditStatus == 0" type="primary" size="small" plain @click="unbind(item.bindId)">申请解绑</el-button>
                <el-button v-if="item.unbindAuditStatus == 1" type="primary" size="small" plain disabled >审核中</el-button>
              </div>
            </el-card>

            <el-card v-if="item.bindStatus == 3" class=" history-teacher-card">
              <div class="match-teacher" >
                <div>
                  <span>历史绑定老师：</span>
                  <el-link :underline="false" type="warning" @click="openForm2(item.teacherId)" style="vertical-align: text-bottom;font-size: 12px" >
                    {{ item.teacherName }}
                  </el-link>
                </div>
                <div>{{ item.universityName }}</div>
                <div>接单次数：{{ item.orderCount }}</div>
              </div>
              <div class="match-teacher">
                <div>解绑时间： {{ formatDate(new Date(item.unbindTime)) }}</div>
                <el-button type="info" size="small" disabled >已解绑</el-button>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
<!--      </ContentWrap>-->
      </el-card>
      <el-card v-else-if="detail.bindRespVOList.length == 0">
      <ContentWrap>
        <div class="text-center">暂无绑定老师</div>
      </ContentWrap>
      </el-card>
    </div>
  </div>

  <!-- 老师详情弹窗 -->
  <TeacherDetail ref="formRef2" />
</template>

<style scoped lang="scss">
.content {
  display: flex;
  justify-content: space-between;
  width: 100%;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left{
  width: 59%;
}
.right{
  min-width: 40%;
}

.text {
  font-size: 14px;
}

.item {
  margin-bottom: 18px;
}

.box-card {
  width: 560px;
}
.teacher-card{
  //background: linear-gradient(to right, #4FADFF, #f7f7f7);
  //background: linear-gradient(to right, #67c23a, #f7f7f7);
  box-shadow: var(--el-box-shadow-light);
}
.history-teacher-card{
  background: linear-gradient(to right, #dadcde, #f7f7f7);
  box-shadow: var(--el-box-shadow-light);
  color: #7d8085;
  //font-weight: bold;
}
//:deep(.bind-line > .teacher-card > .el-timeline-item__timestamp) {
//  color: green;
//}

.my-label{
  width: 20px; /* 设置 label 的宽度 */
  text-align: right; /* 设置 label 文本右对齐 */
}
.cell-item {
  display: flex;
  justify-content: center;
  width: 90px;
}
.auto-wrap {
  //width: 23%;
  //border: 1px solid #ccc;
  //padding: 10px;
}

.auto-wrap span {
  white-space: normal; /* 允许文本换行 */
  word-wrap: break-word; /* 允许长单词或 URL 地址换行 */
}
:deep(.el-descriptions__label) {
  width: 50px;
}
:deep(.el-card__header){
  padding-bottom: 5px;
  padding-top: 5px;
  font-size: 14px;
}
.hover-pointer {
  font-size: 15px;
}
.hover-pointer:hover {
  cursor: pointer;
  background: #67c23a;
  //background: linear-gradient(to bottom, #dadcde, #f7f7f7);
  background: linear-gradient(to bottom, #67c23a, #f7f7f7);
}
:deep(.el-card__body){
  padding: 10px;
  font-size: 12px !important;
}
:deep(.el-descriptions__body .el-descriptions__table.is-bordered .el-descriptions__cell){
  padding: 6px 11px;
  font-size: 12px;
}
.match-teacher{
  display: flex;
  justify-content: space-between;
  padding: 8px 5px 0 5px;
}
</style>
