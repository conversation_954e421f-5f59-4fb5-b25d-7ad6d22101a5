<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="85px"
    >
      <el-form-item label="家长ID" prop="customerId">
        <el-input
          v-model="queryParams.customerId"
          placeholder="请输入家长ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-160px"
        />
      </el-form-item>
      <el-form-item label="家长姓名" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入家长姓名"
          clearable
          @keyup.enter="handleQuery"
          class="!w-160px"
        />
      </el-form-item>
      <el-form-item label="剩余课时" prop="lessonPeriodRemain">
        <el-input
          v-model="queryParams.lessonPeriodRemain"
          placeholder="请输入"
          clearable
          @keyup.enter="handleQuery"
          class="!w-160px"
        />
      </el-form-item>
      <el-form-item label="家长性别" prop="customerSex">
        <el-select
          v-model="queryParams.customerSex"
          placeholder="请选择家长性别"
          clearable
          class="!w-160px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_SEX)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="孩子关系" prop="relationship">
        <el-input
          v-model="queryParams.relationship"
          placeholder="请输入孩子关系"
          clearable
          @keyup.enter="handleQuery"
          class="!w-160px"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="customerPhone">
        <el-input
          v-model="queryParams.customerPhone"
          placeholder="请输入手机号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-160px"
        />
      </el-form-item>
      <!-- <el-form-item label="openId" prop="openId">
        <el-input
          v-model="queryParams.openId"
          placeholder="请输入openId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-160px"
        />
      </el-form-item> -->
      <el-form-item label="服务状态" prop="serviceStatus">
        <el-select
          v-model="queryParams.serviceStatus"
          placeholder="请选择服务状态"
          clearable
          class="!w-160px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_SERVICE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="获客渠道" prop="sourceChannel">
        <el-select
          v-model="queryParams.sourceChannel"
          placeholder="请选择获客渠道"
          clearable
          class="!w-160px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_SOURCE_CHANNEL)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="运营负责人" prop="headOperateUserId">
        <el-select v-model="queryParams.headOperateUserId"
                   clearable
                   filterable
                   class="!w-160px"
                   placeholder="运营负责人">
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>


      <el-form-item label="市场负责人" prop="headMarketUserId">
        <el-select v-model="queryParams.headMarketUserId"
                   clearable
                   filterable
                   class="!w-160px"
                   placeholder="市场负责人">
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="注册时间" prop="registerTime">
        <el-date-picker
          v-model="queryParams.registerTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="最近登录时间" prop="lastLoginTime" label-width="100">
        <el-date-picker
          v-model="queryParams.lastLoginTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>

      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['als:customer:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
<!--        <el-button-->
<!--          type="success"-->
<!--          plain-->
<!--          @click="handleExport"-->
<!--          :loading="exportLoading"-->
<!--          v-hasPermi="['als:customer:export']"-->
<!--        >-->
<!--          <Icon icon="ep:download" class="mr-5px" /> 导出-->
<!--        </el-button>-->
        <el-button
          type="success"
          plain
          @click="openRegisterForm('create')"
          v-hasPermi="['als:customer:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 注册
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading"
              :data="list"
              :stripe="true"
              border
              highlight-current-row
              fit
              size="small"
    >
      <el-table-column label="家长ID" align="center" prop="customerId" width="80"  />
<!--      <el-table-column label="家长姓名" align="center" prop="customerName"  width="120"/>-->

      <el-table-column label="家长姓名" align="left" prop="customerName"  width="200" class="cursor-pointer,">
        <template #default="scope">
          <div>
            <span>姓名：</span>
            <span>{{ scope.row.customerName }}</span>
            <el-button link type="warning" size="small" class="ml-5px" @click="openDetail(scope.row.customerId)">查看详情</el-button>
          </div>
          <div>
            <span>手机：</span>
            <span>{{ scope.row.customerPhone }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="剩余课时" align="center" width="120">
        <template #default="scope">
          <span v-if="scope.row.remainPeriod === -1" style="color: red; font-weight: bold; font-size: large">
            -1
          </span>
          <span v-else>
            {{ scope.row.remainPeriod }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="关系" align="center" prop="relationship" width="100">
        <template #default="scope">
          <div>
            <span>{{ scope.row.relationship }}</span>
            <span class="ml-5px"><dict-tag :type="DICT_TYPE.ALS_SEX" :value="scope.row.customerSex" /></span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="服务状态" align="center" prop="serviceStatus" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ALS_SERVICE_STATUS" :value="scope.row.serviceStatus" />
        </template>
      </el-table-column>
      <el-table-column label="获客渠道" align="center" prop="sourceChannel" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ALS_SOURCE_CHANNEL" :value="scope.row.sourceChannel" />
        </template>
      </el-table-column>

      <el-table-column label="负责人信息" header-align="left" align="left" width="130" >
        <template #default="scope">
          <div>
            <span class="column-label">运营：</span>
            <span>{{ scope.row.headOperateUserName }}</span>
          </div>
          <div>
            <span class="column-label">市场：</span>
            <span>{{ scope.row.headMarketUserName }}</span>
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="openId" align="center" width="200">
        <template #default="scope">
          <el-tooltip 
            v-if="scope.row.openId" 
            :content="scope.row.openId" 
            placement="top"
            effect="dark"
          >
            <span>{{ scope.row.socialUsers && scope.row.socialUsers.length > 0 ? scope.row.socialUsers[0].nickname : scope.row.openId }}</span>
          </el-tooltip>
        </template>
      </el-table-column> -->

      <el-table-column label="社交账号" align="center" width="300">
        <template #default="scope">
          <div v-if="scope.row.socialUsers && scope.row.socialUsers.length > 0">
            <div v-for="(item, index) in scope.row.socialUsers" :key="index" class="inline-block text-center mr-10px">
              <el-popover
                placement="top"
                trigger="hover"
                :width="200"
              >
                <template #reference>
                  <div>
                    <el-avatar 
                      :src="item.avatar" 
                      :size="30"
                      class="mb-2px"
                    />
                    <div class="text-xs">{{ item.socialTypeName }}</div>
                  </div>
                </template>
                <div class="social-user-popover">
                  <div class="mb-5px">
                    <el-avatar :src="item.avatar" :size="50" class="mr-10px" />
                    <span class="font-bold">{{ item.nickname }}</span>
                  </div>
                  <div class="mb-5px">
                    <span class="text-gray-500">类型：</span>
                    <span>{{ item.socialTypeName }}</span>
                  </div>
                  <div>
                    <span class="text-gray-500">OpenID：</span>
                    <span class="text-xs">{{ item.openid }}</span>
                  </div>
                </div>
              </el-popover>
            </div>
            <el-tooltip 
              v-if="scope.row.socialUsers.length > 3" 
              :content="`共 ${scope.row.socialUsers.length} 个社交账号`" 
              placement="top"
            >
              <el-tag size="small" class="ml-5px">+{{ scope.row.socialUsers.length - 3 }}</el-tag>
            </el-tooltip>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column label="时间点" header-align="left" align="left" width="230" >
        <template #default="scope">
          <div>
            <span class="column-label">最近登录时间：</span>
            <span>{{ formatDate(scope.row.lastLoginTime) }}</span>
          </div>
          <div>
            <span class="column-label">更新时间：</span>
            <span>{{ formatDate(scope.row.updateTime) }}</span>
          </div>
          <div>
            <span class="column-label">注册时间：</span>
            <span>{{ formatDate(scope.row.registerTime) }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="家长备注" align="left" header-align="center" prop="customerRemark" width="500">
        <template #default="scope">
          <div class="max-h-20 overflow-y-auto">
            <span>{{ scope.row.customerRemark }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" fixed="right" width="200">
        <template #default="scope">
          <el-button
            plain
            size="small"
            type="primary"
            @click="openForm('update', scope.row.customerId)"
            v-hasPermi="['als:customer:update']"
          >
            编辑
          </el-button>

          <el-button
            plain
            size="small"
            type="danger"
            @click="handleDelete(scope.row.customerId)"
            v-hasPermi="['als:customer:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <CustomerForm ref="formRef" @success="getList" />

  <!-- 表单弹窗：注册 -->
  <CustomerRegisterForm ref="formRef2" @success="getList" />

  <!-- 绑定老师弹窗 -->
  <BindTeacher ref="bindFormRef" @success="getList" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { formatDate} from '@/utils/formatTime'
import download from '@/utils/download'
import { CustomerApi, CustomerVO } from '@/api/als/customer'
import CustomerForm from './CustomerForm.vue'
import BindTeacher from './BindTeacher.vue'
import CustomerRegisterForm from './CustomerRegisterForm.vue'
import * as UserApi from "@/api/system/user";
import * as AreaApi from "@/api/system/area";

/** 家长 列表 */
defineOptions({ name: 'Customer' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const userList = ref<UserApi.UserVO[]>([]) // 用户列表
const areaList = ref([]) // 地区列表
const loading = ref(true) // 列表的加载中
const list = ref<CustomerVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  customerId: undefined,
  customerName: undefined,
  lessonPeriodRemain:undefined,
  customerSex: undefined,
  relationship: undefined,
  customerPhone: undefined,
  openId: undefined,
  serviceStatus: undefined,
  sourceChannel: undefined,
  serviceTags: undefined,
  operationTags: undefined,
  levelTags: undefined,
  registerTime: [],
  lastLoginTime: [],
  headOperateUserId: undefined,
  headMarketUserId: undefined,
  customerRemark: undefined,
  createTime: [],
  updateTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await CustomerApi.getCustomerPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
  // 区域列表
  areaList.value = await AreaApi.getAreaTree()
// 获得用户列表
  userList.value = await UserApi.getSimpleUserList()
}

/** 打开详情 */
const { push } = useRouter()
const openDetail = (id: number) => {
  push({ name: 'CustomerDetail', params: { id } })
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 添加/修改操作 */
const bindFormRef = ref()
const openBindForm = (type: string, id?: number) => {
  bindFormRef.value.open(type, id)
}


/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await CustomerApi.deleteCustomer(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await CustomerApi.exportCustomer(queryParams)
    download.excel(data, '家长.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 注册 */
const formRef2 = ref()
const openRegisterForm = (type: string) => {
  formRef2.value.open( type )
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
