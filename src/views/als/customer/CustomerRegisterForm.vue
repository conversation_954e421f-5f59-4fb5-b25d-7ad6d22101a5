<template>
  <Dialog title="注册" v-model="dialogVisible" width="1000px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
      inline
    >
      <el-form-item label="家长姓名" prop="customerName" class="!w-250px">
        <el-input v-model="formData.customerName" placeholder="请输入家长姓名" />
      </el-form-item>
      <el-form-item label="家长性别" prop="customerSex" class="!w-300px">
        <el-radio-group v-model="formData.customerSex">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_SEX)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="孩子关系" prop="relationship" class="!w-250px">
        <el-input v-model="formData.relationship" placeholder="请输入孩子关系" />
      </el-form-item>
      <el-form-item label="手机号" prop="customerPhone" class="!w-250px">
        <el-input v-model="formData.customerPhone" placeholder="请输入手机号" />
      </el-form-item>
<!--      <el-form-item label="区域" prop="areaId">-->
<!--        <el-cascader-->
<!--          v-model="formData.areaId"-->
<!--          :options="areaList"-->
<!--          class="!w-160px"-->
<!--          clearable-->
<!--          filterable-->
<!--          placeholder="城市"-->
<!--        />-->
<!--      </el-form-item>-->
      <el-form-item label="areaId" prop="areaId" class="!w-300px">
        <el-input v-model="formData.areaId" placeholder="请输入openId" />
      </el-form-item>
      <el-form-item label="openId" prop="openId" class="!w-300px">
        <el-input v-model="formData.openId" placeholder="请输入openId" />
      </el-form-item>
      <el-form-item label="服务状态" prop="serviceStatus" class="!w-250px">
        <el-select v-model="formData.serviceStatus" placeholder="请选择服务状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_SERVICE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="获客渠道" prop="sourceChannel" class="!w-250px">
        <el-select v-model="formData.sourceChannel" placeholder="请选择获客渠道">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_SOURCE_CHANNEL)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="服务标签" prop="serviceTags" class="!w-621px">
        <el-select v-model="formData.serviceTags" multiple clearable>
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_SERVICE_TAGS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="运营标签" prop="operationTags" class="!w-100%">
        <el-select v-model="formData.operationTags" multiple clearable>
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_OPERATION_TAGS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="分级标签" prop="levelTags" class="!w-100%">
        <el-checkbox-group v-model="formData.levelTags">
          <el-checkbox
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_LEVEL_TAGS)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="注册时间" prop="registerTime">
        <el-date-picker
          v-model="formData.registerTime"
          type="datetime"
          value-format="x"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="最近登录时间" prop="lastLoginTime">
        <el-date-picker
          v-model="formData.lastLoginTime"
          type="datetime"
          value-format="x"
          class="!w-200px"
        />
      </el-form-item>

      <el-form-item label="运营负责人" prop="headOperateUserId">
        <el-select v-model="formData.headOperateUserId"
                   clearable
                   filterable
                   class="!w-200px"
                   placeholder="请输入当前负责人">
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="市场负责人" prop="headMarketUserId">
        <el-select v-model="formData.headMarketUserId"
                   clearable
                   filterable
                   class="!w-200px"
                   placeholder="请输入当前负责人">
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="家长备注" prop="customerRemark" class="!w-100%">
        <el-input v-model="formData.customerRemark" type="textarea" :rows="4" maxlength="500" show-word-limit placeholder="请输入家长备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { CustomerApi, CustomerVO } from '@/api/als/customer'
import * as UserApi from "@/api/system/user";
import * as AreaApi from "@/api/system/area";
import {defaultProps} from "@/utils/tree";

/** 家长 表单 */
defineOptions({ name: 'CustomerForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const userList = ref<UserApi.UserVO[]>([]) // 用户列表
const areaList = ref([]) // 地区列表
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  customerId: undefined,
  customerName: undefined,
  customerSex: undefined,
  relationship: undefined,
  customerPhone: undefined,
  areaId: undefined,
  openId: undefined,
  serviceStatus: undefined,
  sourceChannel: undefined,
  serviceTags: [],
  operationTags: [],
  levelTags: [],
  registerTime: undefined,
  lastLoginTime: undefined,
  headOperateUserId: undefined,
  headMarketUserId: undefined,
  customerRemark: undefined
})
const formRules = reactive({
  customerName: [{ required: true, message: '家长姓名不能为空', trigger: 'blur' }],
  customerSex: [{ required: true, message: '家长性别不能为空', trigger: 'blur' }],
  relationship: [{ required: true, message: '孩子关系不能为空', trigger: 'blur' }],
  customerPhone: [{ required: true, message: '手机号不能为空', trigger: 'blur' }],
  openId: [{ required: true, message: 'openId不能为空', trigger: 'blur' }],
  serviceStatus: [{ required: true, message: '服务状态不能为空', trigger: 'change' }],
  sourceChannel: [{ required: true, message: '获客渠道不能为空', trigger: 'change' }],
  serviceTags: [{ required: true, message: '服务标签不能为空', trigger: 'blur' }],
  operationTags: [{ required: true, message: '运营标签不能为空', trigger: 'blur' }],
  levelTags: [{ required: true, message: '分级标签不能为空', trigger: 'blur' }],
  headOperateUserId: [{ required: true, message: '运营负责人不能为空', trigger: 'blur' }],
  headMarketUserId: [{ required: true, message: '市场负责人不能为空', trigger: 'blur' }],
  customerRemark: [{ required: true, message: '家长备注不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await CustomerApi.getCustomer(id)
    } finally {
      formLoading.value = false
    }
  }

  // 获得地区列表
  areaList.value = await AreaApi.getAreaTree()
  userList.value = await UserApi.getSimpleUserList()

}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as CustomerVO
    if (formType.value === 'create') {
      await CustomerApi.createCustomer(data)
      message.success(t('common.createSuccess'))
    } else {
      await CustomerApi.updateCustomer(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    customerId: undefined,
    customerName: undefined,
    customerSex: undefined,
    relationship: undefined,
    customerPhone: undefined,
    openId: undefined,
    serviceStatus: undefined,
    sourceChannel: undefined,
    serviceTags: [],
    operationTags: [],
    levelTags: [],
    registerTime: undefined,
    lastLoginTime: undefined,
    headOperateUserId: undefined,
    headMarketUserId: undefined,
    customerRemark: undefined
  }
  formRef.value?.resetFields()
}
</script>
