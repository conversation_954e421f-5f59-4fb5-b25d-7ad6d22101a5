<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="80%">
    <ContentWrap>
      <!-- 搜索工作栏 -->
      <el-form
        class="-mb-15px"
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="75px"
      >
        <el-form-item label="老师编号" prop="teacherId">
          <el-input
            v-model="queryParams.teacherId"
            placeholder="请输入"
            clearable
            @keyup.enter="handleQuery"
            class="!w-150px"
          />
        </el-form-item>
        <el-form-item label="老师姓名" prop="teacherName">
          <el-input
            v-model="queryParams.teacherName"
            placeholder="模糊搜索"
            clearable
            @keyup.enter="handleQuery"
            class="!w-200px"
          />
        </el-form-item>
        <el-form-item label="手机号" prop="teacherPhone">
          <el-input
            v-model="queryParams.teacherPhone"
            placeholder="模糊搜索"
            clearable
            @keyup.enter="handleQuery"
            class="!w-200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
          <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        </el-form-item>
      </el-form>
    </ContentWrap>

    <ContentWrap bodyStyle="padding:10px;font-weight:bold;text-align:center;">
      <span style="margin-left: 30px">家长ID：{{ formData.customerId }}</span>
      <span style="margin-left: 30px">家长姓名：{{ customerName }}</span>
    </ContentWrap>

    <!-- 列表 -->
    <ContentWrap>
      <el-table v-loading="loading" :data="list" :stripe="true"  border highlight-current-row size="small">
        <el-table-column label="基本信息" header-align="left" align="left" width="170" >
          <template #default="scope">
            <div>
              <span class="column-label">老师编号：</span>
              <span>{{ scope.row.teacherId }}</span>

            </div>
            <div>
              <span class="column-label">姓名：</span>
              <span> {{ scope.row.teacherName }}</span>
              <span class="ml-10px"><dict-tag :type="DICT_TYPE.ALS_SEX" :value="scope.row.teacherSex" /></span>
            </div>
            <div>
              <span class="column-label">手机号：</span>
              <span>{{ scope.row.teacherPhone }} </span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="身份信息" header-align="left" align="left" width="200" >
          <template #default="scope">
            <div>
              <span class="column-label">身份证号：</span>
              <span>{{ scope.row.idNumber }}</span>
            </div>
            <div>
              <span class="column-label">出生年月：</span>
              <span>{{ formatDate(scope.row.birth,'YYYY-MM-DD') }} </span>
            </div>
            <div>
              <span class="column-label">籍贯：</span>
              <span>{{ scope.row.nativeAreaName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="面试信息" header-align="left" align="left" width="250" >
            <div>
              <span class="column-label">面试结果：</span>
              <span>合格</span>
            </div>
            <div>
              <span class="column-label">面试评价：</span>
              <span>萨克斯京东客服金阿奎随机发；啊会计师；都快放假啊收款方久啊；发啦卡机；的国家是；空间发手机；方案设计；发神经发顺丰</span>
            </div>
        </el-table-column>
        <el-table-column label="大学信息" header-align="left" align="left" width="160" >
          <template #default="scope">
            <div>
              <span class="column-label">年级：</span>
              <span>{{ scope.row.entryYear }}
            <dict-tag :type="DICT_TYPE.ALS_DEGREE" :value="scope.row.degree" />
            </span>
            </div>
            <div>
              <span class="column-label">大学名称：</span>
              <span>{{ scope.row.universityName }}</span>
            </div>
            <div>
              <span class="column-label">专业：</span>
              <span>{{ scope.row.profession }} </span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="接单条件" header-align="left" align="left" width="150" >
          <template #default="scope">
            <div>
              <span class="column-label">是否启用：</span>
              <span> <dict-tag :type="DICT_TYPE.ALS_YES_OR_ON" :value="scope.row.isEnable" /></span>
            </div>
            <div>
              <span class="column-label">是否可接单：</span>
              <span> <dict-tag :type="DICT_TYPE.ALS_YES_OR_ON" :value="scope.row.isAcceptOrder" /></span>
            </div>
            <div>
              <span class="column-label">开始接单日期：</span>
              <span style="display: block">{{ formatDate(scope.row.startOrderTime) }} </span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="绑定关系" header-align="left" align="left" width="250" >
          <template #default="scope">
            <div>
              <span class="column-label">绑定状态：</span>
              <span> <dict-tag :type="DICT_TYPE.ALS_BIND_STATUS" :value="scope.row.bindStatus" /></span>
            </div>
            <div>
              <span class="column-label">申请时间：</span>
              <span> {{ formatDate(scope.row.bindApplyTime) }} </span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" fixed="right">
          <template #default="scope">
            <el-tooltip
              class="box-item"
              effect="dark"
              content="已有绑定关系，无法操作"
              placement="top-start"
              :disabled="!scope.row.banBindButton"
            >
              <el-button
                type="primary"
                @click="confirmForm(scope.row.teacherId)"
                v-hasPermi="['als:teacher:delete']"
                :disabled="scope.row.banBindButton"
              >
                绑定
              </el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 表单弹窗：添加/修改 -->
      <BindConfirmForm ref="formRef" @success="getList" />
    </ContentWrap>
  </Dialog>
</template>
<script setup lang="ts">
import { TeacherApi, TeacherVO } from '@/api/als/teacher'
import * as UserApi from "@/api/system/user";
import * as AreaApi from "@/api/system/area";
import {DICT_TYPE} from "@/utils/dict";
import {AreaVO} from "@/api/system/area";
import {formatDate} from "@/utils/formatTime";
import BindConfirmForm from "@/views/als/customer/BindConfirmForm.vue";
import { FormData,QueryParams } from '@/api/als/bind'
import {CustomerApi, CustomerVO} from "@/api/als/customer";

/** 家长 表单 */
defineOptions({ name: 'BindTeacher' })

const userList = ref<UserApi.UserVO[]>([]) // 用户列表
const areaList = ref([]) // 地区列表
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const customerName = ref<string>('');

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const orderCityList = ref<AreaVO[]>([]) // 接单城市列表
const getOrderCityArea = ref([]) // 地区列表
const customerObj = ref<CustomerVO>() // 客户

const loading = ref(false) // 列表的加载中
const list = ref<TeacherVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数


const formData = ref<FormData>({
  teacherId: undefined,
  customerId: undefined,
  bindReason: undefined
})

const queryParams = reactive<QueryParams>({
  pageNo: 1,
  pageSize: 10,
  customerId: undefined,
  teacherId: undefined,
  teacherName: undefined,
  teacherPhone: undefined,
})
const queryFormRef = ref() // 搜索的表单

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    queryParams.customerId = formData.value.customerId
    const data = await TeacherApi.getBindTeacherPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
  areaList.value = await AreaApi.getAreaTree()
  orderCityList.value = await AreaApi.getOrderCity()
  getOrderCityArea.value = await AreaApi.getOrderCityArea()
}

/** 添加/修改操作 */
const formRef = ref()
const confirmForm = (teacherId?: number) => {
  formRef.value.open(formData.value.customerId, teacherId)
}

/** 打开弹窗 */
const open = async (type: string, customerId?: number) => {
  dialogVisible.value = true
  dialogTitle.value = "选择老师"
  formType.value = type
  formData.value.customerId = customerId

  // 重置 queryParams
  resetQueryParams();
  if (customerId) {
    customerObj.value = await CustomerApi.getCustomer(customerId)
    if (customerObj.value){
      customerName.value = customerObj.value.customerName
    }
  }
  formData.value.teacherId = ''
  list.value = []
  // 修改时，设置数据
  if (customerId) {
    formLoading.value = true
    try {
    } finally {
      formLoading.value = false
    }
  }
  // 获得地区列表
  areaList.value = await AreaApi.getAreaTree()
  userList.value = await UserApi.getSimpleUserList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

const resetQueryParams = () => {
  Object.assign(queryParams, {
    pageNo: 1,
    pageSize: 10,
    customerId: undefined,
    teacherId: undefined,
    teacherName: undefined,
    teacherPhone: undefined,
  });
};
</script>

<style scoped lang="scss">
//:deep(.el-card__body){
//  padding: 10px !important;
//}

</style>
