<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="85px"
    >
      <el-form-item label="变更ID" prop="teacherAccountChangeId">
        <el-input
          v-model="queryParams.teacherAccountChangeId"
          placeholder="请输入变更ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="账户ID" prop="teacherAccountId">
        <el-input
          v-model="queryParams.teacherAccountId"
          placeholder="请输入账户ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="老师ID" prop="teacherId">
        <el-input
          v-model="queryParams.teacherId"
          placeholder="请输入老师ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="金额" prop="amount">
        <el-input
          v-model="queryParams.amount"
          placeholder="请输入金额"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="变更后余额" prop="balance">
        <el-input
          v-model="queryParams.balance"
          placeholder="请输入变更后余额"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="业务类型" prop="businessType">
        <el-select
          v-model="queryParams.businessType"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_TEACHER_ACCOUNT_BUSINESS_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="业务ID" prop="businessId">
        <el-input
          v-model="queryParams.businessId"
          placeholder="请输入业务ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['als:teacher-account-change:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['als:teacher-account-change:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" border size="small">
      <el-table-column label="变更ID" align="center" prop="teacherAccountChangeId" width="100"/>
      <el-table-column label="老师ID" align="center" prop="teacherId" width="100"/>
      <el-table-column label="金额" align="center" prop="amount" width="100"/>
      <el-table-column label="变更后余额" align="center" prop="balance" width="100" />
      <el-table-column label="业务类型" align="center" prop="businessType" width="180">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ALS_TEACHER_ACCOUNT_BUSINESS_TYPE" :value="scope.row.businessType" />
        </template>
      </el-table-column>
      <el-table-column label="变更备注" align="left" header-align="center" prop="remark">
        <template #default="scope">
          <div class="max-h-15 overflow-y-auto">
            <span>{{ scope.row.remark }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="业务ID" align="center" prop="businessId" width="100"/>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" fixed="right" width="150">
        <template #default="scope">
          <el-button
            plain
            size="small"
            type="primary"
            @click="openForm('update', scope.row.teacherAccountChangeId)"
            v-hasPermi="['als:teacher-account-change:update']"
          >
            编辑
          </el-button>
          <el-button
            plain
            size="small"
            type="danger"
            @click="handleDelete(scope.row.teacherAccountChangeId)"
            v-hasPermi="['als:teacher-account-change:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <TeacherAccountChangeForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { TeacherAccountChangeApi, TeacherAccountChangeVO } from '@/api/als/teacheraccountchange'
import TeacherAccountChangeForm from './TeacherAccountChangeForm.vue'

/** 老师账户变更记录 列表 */
defineOptions({ name: 'TeacherAccountChange' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<TeacherAccountChangeVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  teacherAccountChangeId: undefined,
  teacherAccountId: undefined,
  teacherId: undefined,
  amount: undefined,
  balance: undefined,
  remark: undefined,
  businessType: undefined,
  businessId: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TeacherAccountChangeApi.getTeacherAccountChangePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TeacherAccountChangeApi.deleteTeacherAccountChange(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TeacherAccountChangeApi.exportTeacherAccountChange(queryParams)
    download.excel(data, '老师账户变更记录.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
