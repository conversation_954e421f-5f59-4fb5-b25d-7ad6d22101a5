<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="老师ID" prop="teacherId">
        <el-input v-model="formData.teacherId" placeholder="请输入老师ID" />
      </el-form-item>
      <el-form-item label="金额" prop="amount">
        <el-input v-model="formData.amount" placeholder="请输入金额" />
      </el-form-item>
      <el-form-item label="变更后余额" prop="balance">
        <el-input v-model="formData.balance" placeholder="请输入变更后余额" />
      </el-form-item>
      <el-form-item label="变更备注" prop="remark">
        <el-input v-model="formData.remark" type="textarea" placeholder="请输入变更备注" />
      </el-form-item>
      <el-form-item label="变更业务类型" prop="businessType">
        <el-select v-model="formData.businessType" placeholder="请选择变更业务类型">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_TEACHER_ACCOUNT_BUSINESS_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="业务ID" prop="businessId">
        <el-input v-model="formData.businessId" placeholder="请输入业务ID" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { TeacherAccountChangeApi, TeacherAccountChangeVO } from '@/api/als/teacheraccountchange'

/** 老师账户变更记录 表单 */
defineOptions({ name: 'TeacherAccountChangeForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  teacherAccountChangeId: undefined,
  teacherId: undefined,
  amount: undefined,
  balance: undefined,
  remark: undefined,
  businessType: undefined,
  businessId: undefined
})
const formRules = reactive({
  teacherId: [{ required: true, message: '老师ID不能为空', trigger: 'blur' }],
  amount: [{ required: true, message: '金额不能为空', trigger: 'blur' }],
  balance: [{ required: true, message: '变更后余额不能为空', trigger: 'blur' }],
  remark: [{ required: true, message: '变更备注不能为空', trigger: 'blur' }],
  businessType: [{ required: true, message: '变更业务类型不能为空', trigger: 'change' }],
  businessId: [{ required: true, message: '业务ID不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await TeacherAccountChangeApi.getTeacherAccountChange(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TeacherAccountChangeVO
    if (formType.value === 'create') {
      await TeacherAccountChangeApi.createTeacherAccountChange(data)
      message.success(t('common.createSuccess'))
    } else {
      await TeacherAccountChangeApi.updateTeacherAccountChange(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    teacherAccountChangeId: undefined,
    teacherAccountId: undefined,
    teacherId: undefined,
    amount: undefined,
    balance: undefined,
    remark: undefined,
    businessType: undefined,
    businessId: undefined
  }
  formRef.value?.resetFields()
}
</script>
