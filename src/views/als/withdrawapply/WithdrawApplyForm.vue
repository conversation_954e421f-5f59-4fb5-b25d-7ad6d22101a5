<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="会员ID" prop="memberId">
        <el-input v-model="formData.memberId" placeholder="请输入会员ID" />
      </el-form-item>
      <el-form-item label="提现金额" prop="amount">
        <el-input v-model="formData.amount" placeholder="请输入提现金额" />
      </el-form-item>
      <el-form-item label="提现手续费" prop="fee">
        <el-input v-model="formData.fee" placeholder="请输入提现手续费" />
      </el-form-item>
      <el-form-item label="提现账户类型" prop="type">
        <el-select v-model="formData.type" placeholder="请选择提现账户类型">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_WITHDRAW_ACCOUNT_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="账号" prop="accountNo">
        <el-input v-model="formData.accountNo" placeholder="请输入账号" />
      </el-form-item>
      <el-form-item label="收款码" prop="accountQrCodeUrl">
        <el-input v-model="formData.accountQrCodeUrl" placeholder="请输入收款码" />
      </el-form-item>
      <el-form-item label="申请时间" prop="applyTime">
        <el-date-picker
          v-model="formData.applyTime"
          type="date"
          value-format="x"
          placeholder="选择申请时间"
        />
      </el-form-item>
      <el-form-item label="审核状态" prop="auditStatus">
        <el-select v-model="formData.auditStatus" placeholder="请选择审核状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_AUDIT_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审核时间" prop="auditTime">
        <el-date-picker
          v-model="formData.auditTime"
          type="date"
          value-format="x"
          placeholder="选择审核时间"
        />
      </el-form-item>
      <el-form-item label="审核人" prop="auditUserId">
        <el-input v-model="formData.auditUserId" placeholder="请输入审核人" />
      </el-form-item>
      <el-form-item label="审核备注" prop="auditRemark">
        <el-input v-model="formData.auditRemark" type="textarea" placeholder="请输入审核备注" />
      </el-form-item>
      <el-form-item label="提现状态" prop="withdrawStatus">
        <el-select v-model="formData.withdrawStatus" placeholder="请选择提现状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_WITHDRAW_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { WithdrawApplyApi, WithdrawApplyVO } from '@/api/als/withdrawapply'

/** 提现申请 表单 */
defineOptions({ name: 'WithdrawApplyForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  withdrawApplyId: undefined,
  memberId: undefined,
  amount: undefined,
  fee: undefined,
  type: undefined,
  accountNo: undefined,
  accountQrCodeUrl: undefined,
  applyTime: undefined,
  auditStatus: undefined,
  auditTime: undefined,
  auditUserId: undefined,
  auditRemark: undefined,
  withdrawStatus: undefined
})
const formRules = reactive({
  memberId: [{ required: true, message: '会员ID不能为空', trigger: 'blur' }],
  amount: [{ required: true, message: '提现金额不能为空', trigger: 'blur' }],
  fee: [{ required: true, message: '提现手续费不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '提现类型不能为空', trigger: 'change' }],
  auditStatus: [{ required: true, message: '审核状态不能为空', trigger: 'change' }],
  auditUserId: [{ required: true, message: '审核人不能为空', trigger: 'blur' }],
  auditRemark: [{ required: true, message: '审核备注不能为空', trigger: 'blur' }],
  withdrawStatus: [{ required: true, message: '提现状态不能为空', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await WithdrawApplyApi.getWithdrawApply(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as WithdrawApplyVO
    if (formType.value === 'create') {
      await WithdrawApplyApi.createWithdrawApply(data)
      message.success(t('common.createSuccess'))
    } else {
      await WithdrawApplyApi.updateWithdrawApply(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    withdrawApplyId: undefined,
    memberId: undefined,
    amount: undefined,
    fee: undefined,
    type: undefined,
    accountNo: undefined,
    accountQrCodeUrl: undefined,
    applyTime: undefined,
    auditStatus: undefined,
    auditTime: undefined,
    auditUserId: undefined,
    auditRemark: undefined,
    withdrawStatus: undefined
  }
  formRef.value?.resetFields()
}
</script>
