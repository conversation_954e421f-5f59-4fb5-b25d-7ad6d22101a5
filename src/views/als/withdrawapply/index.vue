<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="会员ID" prop="memberId">
        <el-input
          v-model="queryParams.memberId"
          placeholder="请输入会员ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="提现金额" prop="amount">
        <el-input
          v-model="queryParams.amount"
          placeholder="请输入提现金额"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="提现手续费" prop="fee">
        <el-input
          v-model="queryParams.fee"
          placeholder="请输入提现手续费"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="提现类型" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择提现类型"
          clearable
          class="!w-240px"
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="账号" prop="accountNo">
        <el-input
          v-model="queryParams.accountNo"
          placeholder="请输入账号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="收款码" prop="accountQrCodeUrl">
        <el-input
          v-model="queryParams.accountQrCodeUrl"
          placeholder="请输入收款码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="申请时间" prop="applyTime">
        <el-date-picker
          v-model="queryParams.applyTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="审核状态" prop="auditStatus">
        <el-select
          v-model="queryParams.auditStatus"
          placeholder="请选择审核状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_AUDIT_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审核人" prop="auditUserId">
        <el-input
          v-model="queryParams.auditUserId"
          placeholder="请输入审核人"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="提现状态" prop="withdrawStatus">
        <el-select
          v-model="queryParams.withdrawStatus"
          placeholder="请选择提现状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_WITHDRAW_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['als:withdraw-apply:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['als:withdraw-apply:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" border size="small">
      <el-table-column label="主键" align="center" prop="withdrawApplyId" />
      <el-table-column label="会员ID" align="center" prop="memberId" />
      <el-table-column label="提现金额" align="center" prop="amount" />
      <el-table-column label="提现手续费" align="center" prop="fee" />
      <el-table-column label="提现类型" align="center" prop="type" />
      <el-table-column label="账号" align="center" prop="accountNo" width="100"/>
<!--      <el-table-column label="收款码" align="center" prop="accountQrCodeUrl" />-->

      <el-table-column label="收款码" header-align="left" align="center" width="160" >
        <template #default="scope">
          <div class="image_contain">
            <el-image
              :src="scope.row.accountQrCodeUrl"
              :preview-src-list="scope.row.accountQrCodeUrl"
              :initial-index="0"
              preview-teleported
              lazy
              fit="cover"
            />
          </div>
        </template>
      </el-table-column>
      
      <el-table-column
        label="申请时间"
        align="center"
        prop="applyTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="审核状态" align="center" prop="auditStatus" width="90">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ALS_AUDIT_STATUS" :value="scope.row.auditStatus" />
        </template>
      </el-table-column>
      <el-table-column
        label="审核时间"
        align="center"
        prop="auditTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="审核人" align="center" prop="auditUserId" />
      <el-table-column label="审核备注" align="center" prop="auditRemark" />
      <el-table-column label="提现状态" align="center" prop="withdrawStatus">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ALS_WITHDRAW_STATUS" :value="scope.row.withdrawStatus" />
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="180px" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.withdrawApplyId)"
            v-hasPermi="['als:withdraw-apply:update']"
          >
            编辑
          </el-button>
          <el-button
            plain size="small"
            type="primary"
            v-if="scope.row.withdrawStatus == 0"
            @click="openForm1('update', scope.row.withdrawApplyId)"
            v-hasPermi="['als:teacher-withdraw-apply:update']"
          >
            审核
          </el-button>
          <el-button
            plain size="small"
            type="primary"
            v-if="scope.row.withdrawStatus == 1"
            @click="pay(scope.row.withdrawApplyId)"
            v-hasPermi="['als:teacher-withdraw-apply:update']"
          >
            付款
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.withdrawApplyId)"
            v-hasPermi="['als:withdraw-apply:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <WithdrawApplyForm ref="formRef" @success="getList" />

  <!--  审核-->
  <AuditForm ref="formRef1" @audit-call-back="auditSubmit"/>
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { WithdrawApplyApi, WithdrawApplyVO } from '@/api/als/withdrawapply'
import WithdrawApplyForm from './WithdrawApplyForm.vue'
import AuditForm from "@/views/als/audit/AuditForm.vue";

/** 提现申请 列表 */
defineOptions({ name: 'WithdrawApply' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<WithdrawApplyVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  memberId: undefined,
  amount: undefined,
  fee: undefined,
  type: undefined,
  accountNo: undefined,
  accountQrCodeUrl: undefined,
  applyTime: [],
  auditStatus: undefined,
  auditUserId: undefined,
  auditRemark: undefined,
  withdrawStatus: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await WithdrawApplyApi.getWithdrawApplyPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

const formRef1 = ref()
const openForm1 = (type: string, id?: number) => {
  formRef1.value.open(type, id)
}
const closeForm1 = async () => {
  formRef1.value.close()
}

const auditSubmit = async (result: any) => {
  try {
    await WithdrawApplyApi.audit(result)
    message.success("审核完成")
  }finally {
    await closeForm1()
    await getList()
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await WithdrawApplyApi.deleteWithdrawApply(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

// 支付
const pay = async (id: number) => {
  try {
    await message.confirm("确认支付")
    await WithdrawApplyApi.pay(id)
    message.success("支付成功")
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await WithdrawApplyApi.exportWithdrawApply(queryParams)
    download.excel(data, '提现申请.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
