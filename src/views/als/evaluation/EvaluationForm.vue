<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="1000px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
      inline
    >
      <el-form-item label="家长ID" prop="customerId" class="!w-250px">
        <el-input v-model="formData.customerId" placeholder="请输入家长ID" />
      </el-form-item>
      <el-form-item label="老师ID" prop="teacherId" class="!w-250px">
        <el-input v-model="formData.teacherId" placeholder="请输入老师ID" />
      </el-form-item>
      <el-form-item label="评分" prop="score" class="!w-250px">
        <el-input v-model="formData.score" placeholder="请输入评分" />
      </el-form-item>
      <el-form-item label="评分等级" prop="level" class="!w-250px">
        <el-input v-model="formData.level" placeholder="请输入评分等级" />
      </el-form-item>
      <el-form-item label="模块" prop="module" class="!w-250px">
        <el-select v-model="formData.module" placeholder="请选择模块">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_EVALUATION_MODULE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="评价内容" prop="content" class="!w-100%">
        <el-input v-model="formData.content" type="textarea" placeholder="请输入评价内容" />
      </el-form-item>
      <el-form-item label="处理状态" prop="dealStatus" class="!w-250px">
        <el-select v-model="formData.dealStatus">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_DEAL_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="处理人" prop="dealUserId" class="!w-250px">
        <el-input v-model="formData.dealUserId" placeholder="请输入处理人" />
      </el-form-item>
      <el-form-item label="跟踪备注" prop="remark" class="!w-100%">
        <el-input v-model="formData.remark" type="textarea" placeholder="请输入跟踪备注" />
      </el-form-item>
      <el-form-item label="备注时间" prop="remarkTime" class="200px">
        <el-date-picker
          v-model="formData.remarkTime"
          type="datetime"
          value-format="x"
          placeholder="选择备注时间"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { EvaluationApi, EvaluationVO } from '@/api/als/evaluation'

/** 评价 表单 */
defineOptions({ name: 'EvaluationForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  evaluationId: undefined,
  customerId: undefined,
  teacherId: undefined,
  score: undefined,
  level: undefined,
  module: undefined,
  content: undefined,
  dealStatus: undefined,
  dealUserId: undefined,
  remark: undefined,
  remarkTime: undefined
})
const formRules = reactive({
  customerId: [{ required: true, message: '家长ID不能为空', trigger: 'blur' }],
  teacherId: [{ required: true, message: '老师ID不能为空', trigger: 'blur' }],
  score: [{ required: true, message: '评分不能为空', trigger: 'blur' }],
  level: [{ required: true, message: '评分等级不能为空', trigger: 'blur' }],
  module: [{ required: true, message: '模块不能为空', trigger: 'change' }],
  content: [{ required: true, message: '评价内容不能为空', trigger: 'blur' }],
  // dealStatus: [{ required: true, message: '处理状态不能为空', trigger: 'change' }],
  // dealUserId: [{ required: true, message: '处理人不能为空', trigger: 'blur' }],
  // remark: [{ required: true, message: '跟踪备注不能为空', trigger: 'blur' }],
  // remarkTime: [{ required: true, message: '备注时间不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await EvaluationApi.getEvaluation(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as EvaluationVO
    if (formType.value === 'create') {
      await EvaluationApi.createEvaluation(data)
      message.success(t('common.createSuccess'))
    } else {
      await EvaluationApi.updateEvaluation(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    evaluationId: undefined,
    customerId: undefined,
    teacherId: undefined,
    score: undefined,
    level: undefined,
    module: undefined,
    content: undefined,
    dealStatus: undefined,
    dealUserId: undefined,
    remark: undefined,
    remarkTime: undefined
  }
  formRef.value?.resetFields()
}
</script>
