<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="家长ID" prop="customerId">
        <el-input
          v-model="queryParams.customerId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="老师ID" prop="teacherId">
        <el-input
          v-model="queryParams.teacherId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="评分" prop="score">
        <el-input
          v-model="queryParams.score"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="评分等级" prop="level">
        <el-input
          v-model="queryParams.level"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="模块" prop="module">
        <el-select
          v-model="queryParams.module"
          clearable
          class="!w-200px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_EVALUATION_MODULE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="处理状态" prop="dealStatus">
        <el-select
          v-model="queryParams.dealStatus"
          clearable
          class="!w-200px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_DEAL_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="处理人" prop="dealUserId">
        <el-input
          v-model="queryParams.dealUserId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="备注时间" prop="remarkTime">
        <el-date-picker
          v-model="queryParams.remarkTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="提交时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['als:evaluation:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['als:evaluation:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" border size="small">
      <el-table-column label="评价ID" align="center" prop="evaluationId" width="80px"/>

      <el-table-column label="家长信息" header-align="left" align="left" width="200" >
        <template #default="scope">
          <div>
            <span class="column-label">家长ID：</span>
            <span>{{ scope.row.customerId }}</span>
          </div>
          <div>
            <span class="column-label">姓名：</span>
            <span>{{ scope.row.customerName }}</span>
            <el-button link type="warning" size="small" class="ml-5px" @click="openDetail(scope.row.customerId)">查看详情</el-button>
          </div>
          <div>
            <span class="column-label">手机：</span>
            <span>{{ scope.row.customerPhone }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="老师信息" header-align="left" align="left" width="150" >
        <template #default="scope">
          <div>
            <span class="column-label">老师ID：</span>
            <span>{{ scope.row.teacherId }}</span>
          </div>
          <div>
            <span class="column-label">姓名：</span>
            <span>{{ scope.row.teacherName }}</span>
          </div>
          <div>
            <span class="column-label">手机：</span>
            <span>{{ scope.row.teacherPhone }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        label="提交时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="140px"
      />
      <el-table-column label="评分" align="center" prop="score" width="50px"/>
      <el-table-column label="评分等级" align="center" prop="level" width="80px"/>
      <el-table-column label="评价模块" align="center" prop="module" width="120px">
        <template #default="scope">
          <dict-tag-text :type="DICT_TYPE.ALS_EVALUATION_MODULE" :value="scope.row.module" />
        </template>
      </el-table-column>
      <el-table-column label="评价内容" align="left" prop="content" width="250px">
        <template #default="scope">
          <div class="h-26 overflow-y-auto">
            {{ scope.row.content }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="处理状态" align="center" prop="dealStatus" width="100px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ALS_DEAL_STATUS" :value="scope.row.dealStatus" />
        </template>
      </el-table-column>
      <el-table-column label="备注" align="left" width="300">
        <template #default="scope">
          <div class="flex flex-justify-between">
            <div>
              <span>备注人：</span>
              <span>{{ scope.row.dealUserName }}</span>
            </div>
            <div>
              <span>时间：</span>
              <span>{{ formatDate(scope.row.remarkTime) }}</span>
            </div>
          </div>
          <div class="h-20 overflow-y-auto">
            <span>备注：</span>
            <span>{{ scope.row.remark }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="200px" fixed="right">
        <template #default="scope">
          <el-button
            plain size="small"
            type="primary"
            class="op-btn"
            @click="openForm('update', scope.row.evaluationId)"
            v-hasPermi="['als:evaluation:update']"
          >
            编辑
          </el-button>

          <el-button
            plain size="small"
            type="primary"
            class="op-btn"
            @click="openForm2('update', scope.row.evaluationId)"
            v-hasPermi="['als:evaluation:update']"
          >
            处理
          </el-button>

          <el-button
            plain size="small"
            type="danger"
            class="op-btn"
            @click="handleDelete(scope.row.evaluationId)"
            v-hasPermi="['als:evaluation:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <EvaluationForm ref="formRef" @success="getList" />

  <!-- 表单弹窗：添加/修改 -->
  <DealEvaluationForm ref="formRef2" @success="getList" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import {dateFormatter, formatDate} from '@/utils/formatTime'
import download from '@/utils/download'
import { EvaluationApi, EvaluationVO } from '@/api/als/evaluation'
import EvaluationForm from './EvaluationForm.vue'
import DealEvaluationForm from './DealEvaluationForm.vue'

/** 评价 列表 */
defineOptions({ name: 'Evaluation' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<EvaluationVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  customerId: undefined,
  teacherId: undefined,
  score: undefined,
  level: undefined,
  module: undefined,
  content: undefined,
  dealStatus: undefined,
  dealUserId: undefined,
  remark: undefined,
  remarkTime: [],
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await EvaluationApi.getEvaluationPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 打开详情 */
const { push } = useRouter()
const openDetail = (id: number) => {
  push({ name: 'CustomerDetail', params: { id } })
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 添加/修改操作 */
const formRef2 = ref()
const openForm2 = (type: string, id?: number) => {
  formRef2.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await EvaluationApi.deleteEvaluation(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await EvaluationApi.exportEvaluation(queryParams)
    download.excel(data, '评价.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
