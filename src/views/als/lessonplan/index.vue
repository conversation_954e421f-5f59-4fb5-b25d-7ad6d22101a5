<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      inline
      label-width="85px"
    >
      <el-form-item label="订单ID" prop="orderId">
        <el-input
          v-model="queryParams.orderId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="家长ID" prop="customerId">
        <el-input
          v-model="queryParams.customerId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="家长姓名" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="模糊搜索"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="老师ID" prop="teacherId">
        <el-input
          v-model="queryParams.teacherId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="老师姓名" prop="teacherName">
        <el-input
          v-model="queryParams.teacherName"
          placeholder="模糊搜索"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="计划课时数" prop="planHour">
        <el-input
          v-model="queryParams.planHour"
          placeholder="大于等于"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="审核状态" prop="planAuditStatus">
        <el-select
          v-model="queryParams.planAuditStatus"
          clearable
          class="!w-200px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_AUDIT_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审核时间" prop="planAuditTime">
        <el-date-picker
          v-model="queryParams.planAuditTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="审核人ID" prop="planAuditUserId">
        <el-input
          v-model="queryParams.planAuditUserId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['als:lesson-plan:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['als:lesson-plan:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" border size="small">
      <el-table-column label="陪学计划ID" align="center" prop="lessonPlanId" width="100px"/>
      <el-table-column label="订单信息" header-align="left" align="left" width="150" >
        <template #default="scope">
          <div>
            <span class="column-label">订单ID：</span>
            <span>{{ scope.row.orderId }} </span>
            <el-button  link type="warning" size="small" class="ml-5px" @click="openDetail(scope.row.orderId)">查看详情</el-button>
          </div>
          <div>
            <span class="column-label">家长ID：</span>
            <span>{{ scope.row.customerId }}</span>
          </div>
          <div>
            <span class="column-label">家长姓名：</span>
            <span>{{ scope.row.customerName }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="陪学老师信息" header-align="left" align="left" width="150" >
        <template #default="scope">
          <div>
            <span class="column-label">老师ID：</span>
            <span>{{ scope.row.teacherId }}</span>
          </div>
          <div>
            <span class="column-label">老师姓名：</span>
            <span>{{ scope.row.teacherName }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="计划课时数" align="center" prop="planHour" />
      <el-table-column label="总体目标" header-align="center" align="left" width="300" >
        <template #default="scope">
          <div class="h-25 overflow-y-auto">
            <span>{{ scope.row.planTarget }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="家长期望" header-align="center" align="left" width="300" >
        <template #default="scope">
          <div class="h-25 overflow-y-auto">
            <span>{{ scope.row.customerExpect }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="Q1阶段" header-align="left" align="left" width="300" >
        <template #default="scope">
          <div>
            <span class="column-label">Q1打分：</span>
            <span>{{ scope.row.stageQ1Score }}</span>
          </div>
          <div class="h-23 overflow-y-auto">
            <span class="column-label">Q1阶段目标：</span>
            <span>{{ scope.row.stageQ1Target }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="Q2阶段" header-align="left" align="left" width="300" >
        <template #default="scope">
          <div>
            <span class="column-label">Q1打分：</span>
            <span>{{ scope.row.stageQ2Score }}</span>
          </div>
          <div class="h-23 overflow-y-auto">
            <span class="column-label">Q2阶段目标：</span>
            <span>{{ scope.row.stageQ2Target }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="Q3阶段" header-align="left" align="left" width="300" >
        <template #default="scope">
          <div>
            <span class="column-label">Q3打分：</span>
            <span>{{ scope.row.stageQ3Score }}</span>
          </div>
          <div class="h-23 overflow-y-auto">
            <span class="column-label">Q3阶段目标：</span>
            <span>{{ scope.row.stageQ3Target }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="Q4阶段" header-align="left" align="left" width="300" >
        <template #default="scope">
          <div>
            <span class="column-label">Q4打分：</span>
            <span>{{ scope.row.stageQ4Score }}</span>
          </div>
          <div class="h-23 overflow-y-auto">
            <span class="column-label">Q4阶段目标：</span>
            <span>{{ scope.row.stageQ4Target }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="审核信息" header-align="left" align="left" width="200" >
        <template #default="scope">
          <div>
            <span class="column-label">审核状态：</span>
            <dict-tag :type="DICT_TYPE.ALS_AUDIT_STATUS" :value="scope.row.planAuditStatus" />
          </div>
          <div>
            <span class="column-label">审核人：</span>
            <span>{{ scope.row.planAuditUserId }}</span>
          </div>
          <div>
            <span class="column-label">审核时间：</span>
            <span>{{ formatDate(scope.row.planAuditTime) }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="审核备注" header-align="center" align="left" width="200" >
        <template #default="scope">
          <div class="h-23 overflow-y-auto">
            <span>{{ scope.row.planAuditRemark }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" fixed="right" width="200px">
        <template #default="scope">
          <el-button
            plain size="small"
            type="primary"
            @click="openForm('update', scope.row.lessonPlanId)"
            v-hasPermi="['als:lesson-plan:update']"
          >
            编辑
          </el-button>

          <el-button
            plain size="small"
            type="primary"
            @click="openForm1('audit', scope.row.lessonPlanId)"
            v-hasPermi="['als:bind:update']"
            :disabled="scope.row.planAuditStatus !== 1"
          >
            审核
          </el-button>

          <el-button
            plain size="small"
            type="danger"
            @click="handleDelete(scope.row.lessonPlanId)"
            v-hasPermi="['als:lesson-plan:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <LessonPlanForm ref="formRef" @success="getList" />

  <AuditForm ref="formRef1" @audit-call-back="auditSubmit"/>
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import {dateFormatter, formatDate} from '@/utils/formatTime'
import download from '@/utils/download'
import { LessonPlanApi, LessonPlanVO} from '@/api/als/lessonplan'
import LessonPlanForm from './LessonPlanForm.vue'
import AuditForm from "@/views/als/audit/AuditForm.vue";

/** 陪学计划 列表 */
defineOptions({ name: 'LessonPlan' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<LessonPlanVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  orderId: undefined,
  customerId: undefined,
  customerName: undefined,
  teacherId: undefined,
  teacherName: undefined,
  planHour: undefined,
  planAuditStatus: undefined,
  planAuditTime: [],
  planAuditUserId: undefined,
  createTime: []
})

const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await LessonPlanApi.getLessonPlanPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 审核 */
const formRef1 = ref()
const openForm1 = (type: string, id?: number) => {
  formRef1.value.open(type, id)
}

const closeForm1 = async () => {
  formRef1.value.close()
}

const auditSubmit = async (result: any) => {
  try {
    await LessonPlanApi.audit(result)
  }finally {
    await closeForm1()
    await getList()
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await LessonPlanApi.deleteLessonPlan(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await LessonPlanApi.exportLessonPlan(queryParams)
    download.excel(data, '陪学计划.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const { currentRoute, push } = useRouter()
const openDetail = (id: number) => {
  // debugger
  push({ name: 'ExpOrder2', params: { id } })
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
