<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="70%">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
      inline
    >
      <el-form-item label="订单ID" prop="orderId" class="!w-250px">
        <el-input v-model="formData.orderId" placeholder="请输入订单ID" />
      </el-form-item>
      <el-form-item label="家长ID" prop="customerId" class="!w-250px">
        <el-input v-model="formData.customerId" placeholder="请输入家长ID" />
      </el-form-item>
      <el-form-item label="老师ID" prop="teacherId" class="!w-250px">
        <el-input v-model="formData.teacherId" placeholder="请输入老师ID" />
      </el-form-item>
      <el-form-item label="计划课时数" prop="planHour" class="!w-250px">
        <el-input v-model="formData.planHour" placeholder="请输入计划课时数" />
      </el-form-item>
      <el-form-item label="总体目标" prop="planTarget" class="!w-100%">
        <el-input v-model="formData.planTarget" type="textarea" rows="5" placeholder="请输入总体目标" />
      </el-form-item>
      <el-form-item label="家长期望" prop="customerExpect" class="!w-100%">
        <el-input v-model="formData.customerExpect" type="textarea" rows="5" placeholder="请输入家长期望" />
      </el-form-item>
      <el-form-item label="Q1阶段目标" prop="stageQ1Target" class="!w-47%">
        <el-input v-model="formData.stageQ1Target" type="textarea" rows="5" placeholder="请输入Q1阶段目标" />
      </el-form-item>
      <el-form-item label="Q2阶段目标" prop="stageQ2Target" class="!w-47%">
        <el-input v-model="formData.stageQ2Target" type="textarea" rows="5" placeholder="请输入Q2阶段目标" />
      </el-form-item>
      <el-form-item label="Q3阶段目标" prop="stageQ3Target" class="!w-47%">
        <el-input v-model="formData.stageQ3Target" type="textarea" rows="5" placeholder="请输入Q3阶段目标" />
      </el-form-item>
      <el-form-item label="Q4阶段目标" prop="stageQ4Target" class="!w-47%">
        <el-input v-model="formData.stageQ4Target" type="textarea" rows="5" placeholder="请输入Q4阶段目标" />
      </el-form-item>
      <el-form-item label="Q1打分" prop="stageQ1Score" class="!w-250px">
        <el-input v-model="formData.stageQ1Score" placeholder="请输入Q1打分" />
      </el-form-item>
      <el-form-item label="Q2打分" prop="stageQ2Score" class="!w-250px">
        <el-input v-model="formData.stageQ2Score" placeholder="请输入Q2打分" />
      </el-form-item>
      <el-form-item label="Q3打分" prop="stageQ3Score" class="!w-250px">
        <el-input v-model="formData.stageQ3Score" placeholder="请输入Q3打分" />
      </el-form-item>
      <el-form-item label="Q4打分" prop="stageQ4Score" class="!w-250px">
        <el-input v-model="formData.stageQ4Score" placeholder="请输入Q4打分" />
      </el-form-item>
      <el-form-item label="审核状态" prop="planAuditStatus" class="!w-250px" >
        <el-select v-model="formData.planAuditStatus" placeholder="请选择审核状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_AUDIT_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审核时间" prop="planAuditTime" class="!w-250px">
        <el-date-picker
          v-model="formData.planAuditTime"
          type="date"
          value-format="x"
          placeholder="选择审核时间"
        />
      </el-form-item>
      <el-form-item label="审核人" prop="planAuditUserId" class="!w-250px">
        <el-input v-model="formData.planAuditUserId" placeholder="请输入审核人" />
      </el-form-item>
      <el-form-item label="审核备注" prop="planAuditRemark" class="!w-100%">
        <el-input v-model="formData.planAuditRemark" type="textarea" rows="4" placeholder="请输入审核备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { LessonPlanApi, LessonPlanVO } from '@/api/als/lessonplan'

/** 陪学计划 表单 */
defineOptions({ name: 'LessonPlanForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  lessonPlanId: undefined,
  orderId: undefined,
  customerId: undefined,
  teacherId: undefined,
  planHour: undefined,
  planTarget: undefined,
  customerExpect: undefined,
  stageQ1Target: undefined,
  stageQ2Target: undefined,
  stageQ3Target: undefined,
  stageQ4Target: undefined,
  stageQ1Score: undefined,
  stageQ2Score: undefined,
  stageQ3Score: undefined,
  stageQ4Score: undefined,
  planAuditStatus: undefined,
  planAuditTime: undefined,
  planAuditUserId: undefined,
  planAuditRemark: undefined
})
const formRules = reactive({
  orderId: [{ required: true, message: '订单ID不能为空', trigger: 'blur' }],
  customerId: [{ required: true, message: '家长ID不能为空', trigger: 'blur' }],
  teacherId: [{ required: true, message: '老师ID不能为空', trigger: 'blur' }],
  planHour: [{ required: true, message: '计划课时数不能为空', trigger: 'blur' }],
  planTarget: [{ required: true, message: '总体目标不能为空', trigger: 'blur' }],
  customerExpect: [{ required: true, message: '家长期望不能为空', trigger: 'blur' }],
  stageQ1Target: [{ required: true, message: 'Q1阶段目标不能为空', trigger: 'blur' }],
  stageQ2Target: [{ required: true, message: 'Q2阶段目标不能为空', trigger: 'blur' }],
  stageQ3Target: [{ required: true, message: 'Q3阶段目标不能为空', trigger: 'blur' }],
  stageQ4Target: [{ required: true, message: 'Q4阶段目标不能为空', trigger: 'blur' }],
  stageQ1Score: [{ required: true, message: 'Q1打分不能为空', trigger: 'blur' }],
  stageQ2Score: [{ required: true, message: 'Q2打分不能为空', trigger: 'blur' }],
  stageQ3Score: [{ required: true, message: 'Q3打分不能为空', trigger: 'blur' }],
  stageQ4Score: [{ required: true, message: 'Q4打分不能为空', trigger: 'blur' }],
  planAuditStatus: [{ required: true, message: '审核状态不能为空', trigger: 'change' }],
  planAuditUserId: [{ required: true, message: '审核人不能为空', trigger: 'blur' }],
  planAuditRemark: [{ required: true, message: '审核备注不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await LessonPlanApi.getLessonPlan(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as LessonPlanVO
    if (formType.value === 'create') {
      await LessonPlanApi.createLessonPlan(data)
      message.success(t('common.createSuccess'))
    } else {
      await LessonPlanApi.updateLessonPlan(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    lessonPlanId: undefined,
    orderId: undefined,
    customerId: undefined,
    teacherId: undefined,
    planHour: undefined,
    planTarget: undefined,
    customerExpect: undefined,
    stageQ1Target: undefined,
    stageQ2Target: undefined,
    stageQ3Target: undefined,
    stageQ4Target: undefined,
    stageQ1Score: undefined,
    stageQ2Score: undefined,
    stageQ3Score: undefined,
    stageQ4Score: undefined,
    planAuditStatus: undefined,
    planAuditTime: undefined,
    planAuditUserId: undefined,
    planAuditRemark: undefined
  }
  formRef.value?.resetFields()
}
</script>
