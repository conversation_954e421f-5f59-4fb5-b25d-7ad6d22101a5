<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="85px"
    >
      <el-form-item label="课时记录ID" prop="lessonRecordId">
        <el-input
          v-model="queryParams.lessonRecordId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      <el-form-item label="购买记录ID" prop="customerPackageId">
        <el-input
          v-model="queryParams.customerPackageId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      <el-form-item label="家长ID" prop="customerId">
        <el-input
          v-model="queryParams.customerId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      <el-form-item label="老师ID" prop="teacherId">
        <el-input
          v-model="queryParams.teacherId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      <el-form-item label="上课类型" prop="lessonType">
        <el-select
          v-model="queryParams.lessonType"
          clearable
          class="!w-150px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_LESSON_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="来源" prop="recordSource">
        <el-select
          v-model="queryParams.recordSource"
          clearable
          class="!w-150px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_RECORD_SOURCE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="陪学人数" prop="childNumber">
        <el-select
          v-model="queryParams.childNumber"
          clearable
          class="!w-150px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_CHILD_NUMBER)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="记录状态" prop="recordStatus">
        <el-select
          v-model="queryParams.recordStatus"
          clearable
          class="!w-150px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_RECORD_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="填写进度" prop="process">
        <el-select
          v-model="queryParams.process"
          clearable
          class="!w-150px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_PROCESS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="开始打卡时间" prop="startTime" label-width="100px">
        <el-date-picker
          v-model="queryParams.startTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="结束打卡时间" prop="endTime" label-width="100px">
        <el-date-picker
          v-model="queryParams.endTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="陪学时长" prop="scheduleHour">
        <el-input
          v-model="queryParams.scheduleHour"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      <el-form-item label="下次上课时间" prop="nextTime" label-width="100px">
        <el-date-picker
          v-model="queryParams.nextTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="提交时间" prop="commitTime" label-width="100px">
        <el-date-picker
          v-model="queryParams.commitTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-200px"
        />
      </el-form-item>
<!--      <el-form-item label="创建时间" prop="createTime">-->
<!--        <el-date-picker-->
<!--          v-model="queryParams.createTime"-->
<!--          value-format="YYYY-MM-DD HH:mm:ss"-->
<!--          type="daterange"-->
<!--          start-placeholder="开始日期"-->
<!--          end-placeholder="结束日期"-->
<!--          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"-->
<!--          class="!w-200px"-->
<!--        />-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['als:lesson-record:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['als:lesson-record:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" border size="small">
      <el-table-column label="陪学记录ID" align="center" prop="lessonRecordId" width="100" />
      <el-table-column label="购买记录ID" align="center" prop="customerPackageId" width="100" />
      <el-table-column label="家长ID" align="center" prop="customerId" />
      <el-table-column label="老师ID" align="center" prop="teacherId" />
      <el-table-column label="上课内容" align="left" prop="lessonType" width="200">
        <template #default="scope">
          <div>
            <span>上课类型：</span>
            <dict-tag :type="DICT_TYPE.ALS_LESSON_TYPE" :value="scope.row.lessonType" />
          </div>
          <div>
            <span>来源：</span>
            <dict-tag :type="DICT_TYPE.ALS_RECORD_SOURCE" :value="scope.row.recordSource" />
          </div>
          <div>
            <span>陪学人数：</span>
            <dict-tag :type="DICT_TYPE.ALS_CHILD_NUMBER" :value="scope.row.childNumber" />
          </div>
          <div>
            <span class="column-label">陪学内容：</span>
            <span>{{ scope.row.lessonContent }}</span>
          </div>
          <div>
            <span class="column-label">时间规划：</span>
            <el-button  link type="warning" size="small" class="ml-5px" @click="openDetail(scope.row.lessonRecordId)">查看详情</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="时间点" align="left" prop="recordStatus" width="250">
        <template #default="scope">
          <div>
            <span class="column-label">开始打卡时间：</span>
            <span>{{ formatDate(scope.row.startTime )}}</span>
          </div>
          <div>
            <span class="column-label">结束打卡时间：</span>
            <span>{{ formatDate(scope.row.endTime )}}</span>
          </div>
          <div class="mt-1">
            <span class="column-label">下次时间：</span>
            <span>{{ formatDate(scope.row.nextTime )}}</span>
          </div>
          <div>
            <span class="column-label">提交时间：</span>
            <span>{{ formatDate(scope.row.commitTime )}}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="陪学时长（h）" align="center" prop="scheduleHour" width="110"/>
      <el-table-column label="状态" align="left" prop="recordStatus" width="120">
        <template #default="scope">
          <div>
            <span class="column-label">状态：</span>
            <dict-tag :type="DICT_TYPE.ALS_RECORD_STATUS" :value="scope.row.recordStatus" />
          </div>
          <div class="mt-1">
            <span class="column-label">进度：</span>
            <dict-tag :type="DICT_TYPE.ALS_PROCESS" :value="scope.row.process" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="薄弱点记录" align="center" prop="weakSpot" width="300">
        <template #default="scope">
          <div class="h-23 overflow-y-auto">
            <span>{{ scope.row.weakSpot }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="行为表现评价" align="center" prop="showEvaluate" width="300">
        <template #default="scope">
          <div class="h-23 overflow-y-auto">
            <span>{{ scope.row.showEvaluate }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="陪学反思" align="center" prop="reflect" width="300" >
        <template #default="scope">
          <div class="h-23 overflow-y-auto">
            <span>{{ scope.row.reflect }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="给家长留言" align="center" prop="leaveWord" width="300">
        <template #default="scope">
          <div class="h-23 overflow-y-auto">
            <span>{{ scope.row.leaveWord }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" fixed="right" width="200px">
        <template #default="scope">
          <el-button
            plain
            size="small"
            type="primary"
            @click="openForm('update', scope.row.lessonRecordId)"
            v-hasPermi="['als:lesson-record:update']"
          >
            编辑
          </el-button>
          <el-button
            plain
            size="small"
            type="danger"
            @click="handleDelete(scope.row.lessonRecordId)"
            v-hasPermi="['als:lesson-record:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <LessonRecordForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import {dateFormatter, formatDate} from '@/utils/formatTime'
import download from '@/utils/download'
import { LessonRecordApi, LessonRecordVO } from '@/api/als/lessonrecord'
import LessonRecordForm from './LessonRecordForm.vue'

/** 陪学记录 列表 */
defineOptions({ name: 'LessonRecord' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<LessonRecordVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  lessonRecordId: undefined,
  customerPackageId: undefined,
  customerId: undefined,
  teacherId: undefined,
  lessonType: undefined,
  recordSource: undefined,
  childNumber: undefined,
  lessonContent: undefined,
  recordStatus: undefined,
  process: undefined,
  prepareScore: undefined,
  prepareItem: undefined,
  summaryItem: undefined,
  weakSpot: undefined,
  showEvaluate: undefined,
  reflect: undefined,
  leaveWord: undefined,
  startTime: [],
  endTime: [],
  scheduleHour: undefined,
  nextTime: [],
  commitTime: [],
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await LessonRecordApi.getLessonRecordPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

const { push } = useRouter()
const openDetail = (id: number) => {
  push({ name: 'LessonSchedule2', params: { id } })
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await LessonRecordApi.deleteLessonRecord(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await LessonRecordApi.exportLessonRecord(queryParams)
    download.excel(data, '陪学记录.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const route = useRoute()
const id = Number(route.params.id)

/** 初始化 **/
onMounted(() => {
  if (id) {
    queryParams.lessonRecordId = id
  }
  getList()
})
</script>
