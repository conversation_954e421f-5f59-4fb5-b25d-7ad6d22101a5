<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="70%">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
      inline
    >
      <el-form-item label="购买记录ID" prop="customerPackageId" class="form-item">
        <el-input v-model="formData.customerPackageId" placeholder="请输入购买记录ID" />
      </el-form-item>
      <el-form-item label="家长ID" prop="customerId" class="form-item">
        <el-input v-model="formData.customerId" placeholder="请输入家长ID" />
      </el-form-item>
      <el-form-item label="老师ID" prop="teacherId" class="form-item">
        <el-input v-model="formData.teacherId" placeholder="请输入老师ID" />
      </el-form-item>
      <el-form-item label="上课类型" prop="lessonType" class="form-item">
        <el-select v-model="formData.lessonType" placeholder="请选择上课类型">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_LESSON_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="陪学人数" prop="childNumber" class="form-item">
        <el-select v-model="formData.childNumber" placeholder="请选择陪学人数">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_CHILD_NUMBER)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="陪学内容" prop="lessonContent" class="!w-100%">
        <el-input v-model="formData.lessonContent" type="textarea" placeholder="请输入陪学内容" />
      </el-form-item>
      <el-form-item label="记录状态" prop="recordStatus" class="form-item">
        <el-select v-model="formData.recordStatus" placeholder="请选择陪学记录状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_RECORD_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="填写进度" prop="process" class="form-item" >
        <el-select v-model="formData.process" placeholder="请选择填写进度">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_PROCESS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <div class="flex flex-items-start">
        <div>
          <el-form-item label="" prop="prepareScore" style="align-items: center;" label-width="0" >
            <el-table :data="getStrDictOptions(DICT_TYPE.ALS_PREPARE_SCORE)" width="100%" border stripe>
              <el-table-column prop="label" label="课前项" width="200" align="center" />
              <el-table-column label="打分"  width="330" align="center">
                <template #default="scope">
                  <el-rate v-model="formData.prepareScore[scope.$index]" />
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="" prop="summaryScore" style="align-items: center;" label-width="0">
            <el-table :data="getStrDictOptions(DICT_TYPE.ALS_SUMMARY_SCORE)" width="100%" border stripe>
              <el-table-column prop="label" label="课后项" width="200" align="center" />
              <el-table-column label="打分"  width="330" align="center">
                <template #default="scope">
                  <el-rate v-model="formData.summaryScore[scope.$index]" />
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </div>
      </div>

      <el-form-item label="准备项" prop="prepareItem" class="!w-100%">
        <el-checkbox-group v-model="formData.prepareItem">
          <el-checkbox
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_PREPARE_ITEM)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="总结项" prop="summaryItem" class="!w-100%">
        <el-checkbox-group v-model="formData.summaryItem">
          <el-checkbox
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_SUMMARY_ITEM)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="薄弱点记录" prop="weakSpot" class="!w-47%">
        <el-input v-model="formData.weakSpot" type="textarea" rows="5" maxlength="500" show-word-limit placeholder="请输入薄弱点记录" />
      </el-form-item>
      <el-form-item label="行为表现评价" prop="showEvaluate" class="!w-47%">
        <el-input v-model="formData.showEvaluate" type="textarea" rows="5" maxlength="500" show-word-limit placeholder="请输入行为表现评价" />
      </el-form-item>
      <el-form-item label="陪学反思" prop="reflect" class="!w-47%">
        <el-input v-model="formData.reflect" type="textarea" rows="5" maxlength="500" show-word-limit placeholder="请输入陪学反思" />
      </el-form-item>
      <el-form-item label="给家长留言" prop="leaveWord" class="!w-47%">
        <el-input v-model="formData.leaveWord" type="textarea" rows="5" maxlength="500" show-word-limit placeholder="请输入给家长留言" />
      </el-form-item>
      <el-form-item label="开始打卡时间" prop="startTime" class="!w-300px" label-width="110px">
        <el-date-picker
          v-model="formData.startTime"
          type="datetime"
          value-format="x"
          placeholder="选择开始打卡时间"
        />
      </el-form-item>
      <el-form-item label="结束打卡时间" prop="endTime" class="!w-300px" label-width="110px">
        <el-date-picker
          v-model="formData.endTime"
          type="datetime"
          value-format="x"
          placeholder="选择结束打卡时间"
        />
      </el-form-item>
      <el-form-item label="陪学时长" prop="scheduleHour" class="form-item">
        <el-input v-model="formData.scheduleHour" placeholder="请输入陪学时长" />
      </el-form-item>
      <el-form-item label="下次上课时间" prop="nextTime" class="!w-300px" label-width="110px">
        <el-date-picker
          v-model="formData.nextTime"
          type="datetime"
          value-format="x"
          placeholder="选择下次上课时间"
        />
      </el-form-item>
      <el-form-item label="提交时间" prop="commitTime" class="!w-300px" label-width="110px">
        <el-date-picker
          v-model="formData.commitTime"
          type="datetime"
          value-format="x"
          placeholder="选择提交时间"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { LessonRecordApi, LessonRecordVO } from '@/api/als/lessonrecord'

/** 陪学记录 表单 */
defineOptions({ name: 'LessonRecordForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  lessonRecordId: undefined,
  customerPackageId: undefined,
  customerId: undefined,
  teacherId: undefined,
  lessonType: undefined,
  childNumber: undefined,
  lessonContent: undefined,
  recordStatus: undefined,
  process: undefined,
  prepareScore: [],
  summaryScore: [],
  prepareItem: [],
  summaryItem: [],
  weakSpot: undefined,
  showEvaluate: undefined,
  reflect: undefined,
  leaveWord: undefined,
  startTime: undefined,
  endTime: undefined,
  scheduleHour: undefined,
  nextTime: undefined,
  commitTime: undefined
})
const formRules = reactive({
  customerPackageId: [{ required: true, message: '购买记录ID不能为空', trigger: 'blur' }],
  customerId: [{ required: true, message: '家长ID不能为空', trigger: 'blur' }],
  teacherId: [{ required: true, message: '老师ID不能为空', trigger: 'blur' }],
  lessonType: [{ required: true, message: '上课类型不能为空', trigger: 'change' }],
  childNumber: [{ required: true, message: '陪学人数不能为空', trigger: 'change' }],
  lessonContent: [{ required: true, message: '陪学内容不能为空', trigger: 'blur' }],
  recordStatus: [{ required: true, message: '陪学记录状态不能为空', trigger: 'change' }],
  process: [{ required: true, message: '填写进度不能为空', trigger: 'change' }],
  prepareScore: [{ required: true, message: '课前准备项打分不能为空', trigger: 'blur' }],
  prepareItem: [{ required: true, message: '课前准备事项不能为空', trigger: 'blur' }],
  summaryItem: [{ required: true, message: '课后总结事项不能为空', trigger: 'blur' }],
  weakSpot: [{ required: true, message: '薄弱点记录不能为空', trigger: 'blur' }],
  showEvaluate: [{ required: true, message: '行为表现评价不能为空', trigger: 'blur' }],
  reflect: [{ required: true, message: '陪学反思不能为空', trigger: 'blur' }],
  leaveWord: [{ required: true, message: '给家长留言不能为空', trigger: 'blur' }],
  startTime: [{ required: true, message: '开始打卡时间不能为空', trigger: 'blur' }],
  endTime: [{ required: true, message: '结束打卡时间不能为空', trigger: 'blur' }],
  scheduleHour: [{ required: true, message: '陪学时长不能为空', trigger: 'blur' }],
  nextTime: [{ required: true, message: '下次上课时间不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await LessonRecordApi.getLessonRecord(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as LessonRecordVO
    if (formType.value === 'create') {
      await LessonRecordApi.createLessonRecord(data)
      message.success(t('common.createSuccess'))
    } else {
      await LessonRecordApi.updateLessonRecord(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    lessonRecordId: undefined,
    customerPackageId: undefined,
    customerId: undefined,
    teacherId: undefined,
    lessonType: undefined,
    childNumber: undefined,
    lessonContent: undefined,
    recordStatus: undefined,
    process: undefined,
    prepareScore: [],
    summaryScore: [],
    prepareItem: [],
    summaryItem: [],
    weakSpot: undefined,
    showEvaluate: undefined,
    reflect: undefined,
    leaveWord: undefined,
    startTime: undefined,
    endTime: undefined,
    scheduleHour: undefined,
    nextTime: undefined,
    commitTime: undefined
  }
  formRef.value?.resetFields()
}
</script>

<style lang="scss" scoped>
.form-item {
  width: 253px !important;
}
</style>
