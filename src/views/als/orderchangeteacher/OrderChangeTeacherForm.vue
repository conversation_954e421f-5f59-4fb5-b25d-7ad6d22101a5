<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="订单ID" prop="orderId">
        <el-input v-model="formData.orderId" placeholder="请输入订单ID" />
      </el-form-item>
      <el-form-item label="老师ID" prop="teacherId">
        <el-input v-model="formData.teacherId" placeholder="请输入老师ID" />
      </el-form-item>
      <el-form-item label="家长ID" prop="customerId">
        <el-input v-model="formData.customerId" placeholder="请输入家长ID" />
      </el-form-item>
      <el-form-item label="被换原因标签" prop="reasonTags">
        <el-select v-model="formData.reasonTags" placeholder="请选择被换原因标签">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_CHANGED_REASON_TAGS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="具体原因" prop="reason">
        <el-input v-model="formData.reason" type="textarea" placeholder="请输入具体原因" />
      </el-form-item>
      <el-form-item label="对老师-好评" prop="goodComment">
        <el-input v-model="formData.goodComment" type="textarea" placeholder="请输入对老师-好评" />
      </el-form-item>
      <el-form-item label="对老师-差评" prop="badComment">
        <el-input v-model="formData.badComment" type="textarea" placeholder="请输入对老师-差评" />
      </el-form-item>
      <el-form-item label="申请时间" prop="applyTime">
        <el-date-picker
          v-model="formData.applyTime"
          type="date"
          value-format="x"
          placeholder="选择申请时间"
        />
      </el-form-item>
      <el-form-item label="审核状态" prop="auditStatus">
        <el-select v-model="formData.auditStatus" placeholder="请选择审核状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_AUDIT_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审核时间" prop="auditTime">
        <el-date-picker
          v-model="formData.auditTime"
          type="date"
          value-format="x"
          placeholder="选择审核时间"
        />
      </el-form-item>
      <el-form-item label="审核人" prop="auditUserId">
        <el-input v-model="formData.auditUserId" placeholder="请输入审核人" />
      </el-form-item>
      <el-form-item label="审核备注" prop="auditRemark">
        <el-input v-model="formData.auditRemark" type="textarea" placeholder="请输入审核备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { OrderChangeTeacherApi, OrderChangeTeacherVO } from '@/api/als/orderchangeteacher'

/** 更换老师 表单 */
defineOptions({ name: 'OrderChangeTeacherForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  orderChangeTeacherId: undefined,
  orderId: undefined,
  teacherId: undefined,
  customerId: undefined,
  reasonTags: undefined,
  reason: undefined,
  goodComment: undefined,
  badComment: undefined,
  applyTime: undefined,
  auditStatus: undefined,
  auditTime: undefined,
  auditUserId: undefined,
  auditRemark: undefined
})
const formRules = reactive({
  orderId: [{ required: true, message: '订单ID不能为空', trigger: 'blur' }],
  teacherId: [{ required: true, message: '老师ID不能为空', trigger: 'blur' }],
  customerId: [{ required: true, message: '家长ID不能为空', trigger: 'blur' }],
  reasonTags: [{ required: true, message: '被换原因标签不能为空', trigger: 'change' }],
  reason: [{ required: true, message: '具体原因不能为空', trigger: 'blur' }],
  goodComment: [{ required: true, message: '对老师-好评不能为空', trigger: 'blur' }],
  badComment: [{ required: true, message: '对老师-差评不能为空', trigger: 'blur' }],
  auditStatus: [{ required: true, message: '审核状态不能为空', trigger: 'change' }],
  auditUserId: [{ required: true, message: '审核人不能为空', trigger: 'blur' }],
  auditRemark: [{ required: true, message: '审核备注不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await OrderChangeTeacherApi.getOrderChangeTeacher(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as OrderChangeTeacherVO
    if (formType.value === 'create') {
      await OrderChangeTeacherApi.createOrderChangeTeacher(data)
      message.success(t('common.createSuccess'))
    } else {
      await OrderChangeTeacherApi.updateOrderChangeTeacher(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    orderChangeTeacherId: undefined,
    orderId: undefined,
    teacherId: undefined,
    customerId: undefined,
    reasonTags: undefined,
    reason: undefined,
    goodComment: undefined,
    badComment: undefined,
    applyTime: undefined,
    auditStatus: undefined,
    auditTime: undefined,
    auditUserId: undefined,
    auditRemark: undefined
  }
  formRef.value?.resetFields()
}
</script>