<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="85px"
    >
      <el-form-item label="家长ID" prop="customerId">
        <el-input
          v-model="queryParams.customerId"
          placeholder="请输入家长ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="课时包名称" prop="packageName">
        <el-input
          v-model="queryParams.packageName"
          placeholder="请输入课时包名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="总课时数" prop="lessonPeriod">
        <el-input
          v-model="queryParams.lessonPeriod"
          placeholder="请输入总课时数"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="已使用课时数" prop="lessonPeriodUsed" label-width="100">
        <el-input
          v-model="queryParams.lessonPeriodUsed"
          placeholder="请输入已使用课时数"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="剩余课时" prop="lessonPeriodRemain">
        <el-input
          v-model="queryParams.lessonPeriodRemain"
          placeholder="请输入剩余课时"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="使用状态" prop="useStatus">
        <el-select
          v-model="queryParams.useStatus"
          placeholder="请选择使用状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_USE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="第几次购买" prop="buyTimes">
        <el-input
          v-model="queryParams.buyTimes"
          placeholder="请输入第几次购买"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['als:customer-package:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['als:customer-package:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true"  border  size="small">
      <el-table-column label="购买记录ID" align="center" prop="customerPackageId" width="100"/>
      <el-table-column label="家长信息" header-align="left" align="left" width="200" >
        <template #default="scope">
          <div>
            <span class="column-label">编号：</span>
            <span>{{ scope.row.customerId }}</span>
          </div>
          <div>
            <span class="column-label">姓名：</span>
            <span>{{ scope.row.customerName }}</span>
          </div>
          <div>
            <span class="column-label">手机：</span>
            <span>{{ scope.row.customerPhone }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="课时包名称" align="center" prop="packageName" width="150"/>
      <el-table-column label="课时包类型" align="center" prop="packageType" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ALS_PACKAGE_TYPE" :value="scope.row.packageType" />
        </template>
      </el-table-column>
      <el-table-column label="实付金额" align="center" prop="actualAmount" width="100">
        <template #default="scope">
           ¥{{ scope.row.actualAmount }}
        </template>
      </el-table-column>
      <el-table-column label="总课时数" align="center" prop="lessonPeriod" width="100"/>
      <el-table-column label="已使用课时数" align="center" prop="lessonPeriodUsed" width="100"/>
      <el-table-column label="剩余课时" align="center" prop="lessonPeriodRemain" width="100"/>
      <el-table-column label="使用状态" align="center" prop="useStatus" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ALS_USE_STATUS" :value="scope.row.useStatus" />
        </template>
      </el-table-column>
      <el-table-column label="第几次购买" align="center" prop="buyTimes" width="100"/>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="负责人信息" header-align="left" align="left" width="200" >
        <template #default="scope">
          <div>
            <span class="column-label">运营负责人：</span>
            <span>{{ scope.row.headOperateUser }}</span>
          </div>
          <div>
            <span class="column-label">市场负责人：</span>
            <span>{{ scope.row.headMarketUser }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" fixed="right" width="200px">
        <template #default="scope">
          <el-button
            plain
            size="small"
            type="primary"
            @click="openForm('update', scope.row.customerPackageId)"
            v-hasPermi="['als:customer-package:update']"
          >
            编辑
          </el-button>
          <el-button
            plain
            size="small"
            type="danger"
            @click="handleDelete(scope.row.customerPackageId)"
            v-hasPermi="['als:customer-package:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <CustomerPackageForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import download from '@/utils/download'
import { CustomerPackageApi, CustomerPackageVO } from '@/api/als/customerpackage'
import CustomerPackageForm from './CustomerPackageForm.vue'
import {dateFormatter} from "@/utils/formatTime";

/** 已购课时包记录 列表 */
defineOptions({ name: 'CustomerPackage' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<CustomerPackageVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  customerId: undefined,
  packageName: undefined,
  lessonPeriod: undefined,
  lessonPeriodUsed: undefined,
  lessonPeriodRemain: undefined,
  useStatus: undefined,
  buyTimes: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // debugger
    const data = await CustomerPackageApi.getCustomerPackagePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await CustomerPackageApi.deleteCustomerPackage(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await CustomerPackageApi.exportCustomerPackage(queryParams)
    download.excel(data, '已购课时包记录.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
