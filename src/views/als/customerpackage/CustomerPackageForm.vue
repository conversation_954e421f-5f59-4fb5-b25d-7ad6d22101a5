<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="家长ID" prop="customerId">
        <el-input v-model="formData.customerId" placeholder="请输入家长ID" />
      </el-form-item>
      <el-form-item label="课时包名称" prop="packageName">
        <el-input v-model="formData.packageName" placeholder="请输入课时包名称" />
      </el-form-item>
      <el-form-item label="实付金额" prop="actualAmount">
        <el-input v-model="formData.actualAmount" placeholder="请输入实付金额" />
      </el-form-item>
      <el-form-item label="总课时数" prop="lessonPeriod">
        <el-input v-model="formData.lessonPeriod" placeholder="请输入总课时数" />
      </el-form-item>
      <el-form-item label="已使用课时数" prop="lessonPeriodUsed">
        <el-input v-model="formData.lessonPeriodUsed" placeholder="请输入已使用课时数" />
      </el-form-item>
      <el-form-item label="剩余课时" prop="lessonPeriodRemain">
        <el-input v-model="formData.lessonPeriodRemain" placeholder="请输入剩余课时" />
      </el-form-item>
      <el-form-item label="使用状态" prop="useStatus">
        <el-radio-group v-model="formData.useStatus">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_USE_STATUS)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="第几次购买" prop="buyTimes">
        <el-input v-model="formData.buyTimes" placeholder="请输入第几次购买" />
      </el-form-item>
      <el-form-item label="添加课时申请ID" prop="customerAddApplyId">
        <el-input v-model="formData.customerAddApplyId" placeholder="请输入添加课时申请ID" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { CustomerPackageApi, CustomerPackageVO } from '@/api/als/customerpackage'

/** 已购课时包记录 表单 */
defineOptions({ name: 'CustomerPackageForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  customerPackageId: undefined,
  customerId: undefined,
  packageName: undefined,
  actualAmount: undefined,
  lessonPeriod: undefined,
  lessonPeriodUsed: undefined,
  lessonPeriodRemain: undefined,
  useStatus: undefined,
  buyTimes: undefined,
  customerAddApplyId: undefined
})
const formRules = reactive({
  customerId: [{ required: true, message: '家长ID不能为空', trigger: 'blur' }],
  packageName: [{ required: true, message: '课时包名称不能为空', trigger: 'blur' }],
  actualAmount: [{ required: true, message: '实付金额不能为空', trigger: 'blur' }],
  lessonPeriod: [{ required: true, message: '总课时数不能为空', trigger: 'blur' }],
  lessonPeriodUsed: [{ required: true, message: '已使用课时数不能为空', trigger: 'blur' }],
  lessonPeriodRemain: [{ required: true, message: '剩余课时不能为空', trigger: 'blur' }],
  useStatus: [{ required: true, message: '使用状态不能为空', trigger: 'blur' }],
  buyTimes: [{ required: true, message: '第几次购买不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await CustomerPackageApi.getCustomerPackage(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as CustomerPackageVO
    if (formType.value === 'create') {
      await CustomerPackageApi.createCustomerPackage(data)
      message.success(t('common.createSuccess'))
    } else {
      await CustomerPackageApi.updateCustomerPackage(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    customerPackageId: undefined,
    customerId: undefined,
    packageName: undefined,
    lessonPeriod: undefined,
    lessonPeriodUsed: undefined,
    lessonPeriodRemain: undefined,
    useStatus: undefined,
    buyTimes: undefined,
    customerAddApplyId: undefined
  }
  formRef.value?.resetFields()
}
</script>
