<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="85px"
    >
      <el-form-item label="订单ID" prop="orderId">
        <el-input
          v-model="queryParams.orderId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      <el-form-item label="家长ID" prop="customerId">
        <el-input
          v-model="queryParams.customerId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      <el-form-item label="老师ID" prop="teacherId">
        <el-input
          v-model="queryParams.teacherId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      <el-form-item label="陪学计划ID" prop="planLessonId">
        <el-input
          v-model="queryParams.planLessonId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      <el-form-item label="家长评价" prop="customerEvaluation" label-width="140px">
        <el-select
          v-model="queryParams.customerEvaluation"
          clearable
          class="!w-150px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_CUSTOMER_EVALUATION)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="反馈表审核状态" prop="feedbackAuditStatus" label-width="140px">
        <el-select
          v-model="queryParams.feedbackAuditStatus"
          clearable
          class="!w-150px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_AUDIT_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="反馈表审核时间" prop="feedbackAuditTime" label-width="140px">
        <el-date-picker
          v-model="queryParams.feedbackAuditTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="反馈表审核人" prop="feedbackAuditUserId" label-width="140px">
        <el-input
          v-model="queryParams.feedbackAuditUserId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      <el-form-item label="反馈表审核备注" prop="feedbackAuditRemark" label-width="140px">
        <el-input
          v-model="queryParams.feedbackAuditRemark"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>

      <el-form-item label="陪学计划审核状态" prop="planAuditStatus" label-width="140px">
        <el-select
          v-model="queryParams.planAuditStatus"
          clearable
          class="!w-150px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_AUDIT_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="陪学计划审核时间" prop="planAuditTime" label-width="140px">
        <el-date-picker
          v-model="queryParams.planAuditTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="陪学计划审核人" prop="planAuditUserId" label-width="140px">
        <el-input
          v-model="queryParams.planAuditUserId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      <el-form-item label="陪学计划审核备注" prop="planAuditRemark" label-width="140px">
        <el-input
          v-model="queryParams.planAuditRemark"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['als:order-regular-apply:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['als:order-regular-apply:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" border size="small">
      <el-table-column label="正式课申请ID" align="center" prop="orderRegularApplyId" width="100px"/>
      <el-table-column label="订单ID" align="center" prop="orderId" />
      <el-table-column label="家长评价" align="center" prop="customerEvaluation">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ALS_CUSTOMER_EVALUATION" :value="scope.row.customerEvaluation" />
        </template>
      </el-table-column>
      <el-table-column label="家长ID" align="center" prop="customerId" />
      <el-table-column label="老师ID" align="center" prop="teacherId" />
      <el-table-column label="陪学公约" align="center" prop="agreedUrl" width="120px">
        <template #default="scope">
          <div class="image_contain">
            <el-image
              :src="scope.row.agreedUrl"
              :preview-src-list="getPreviewSrcList(scope.row.agreedUrl)"
              :initial-index="0"
              preview-teleported
              lazy
              fit="cover"
            />
          </div>
        </template>
      </el-table-column>

      <el-table-column label="体验课反馈简述" header-align="center" align="left" width="200" >
        <template #default="scope">
          <div class="h-26 overflow-y-auto">
            <span>{{ scope.row.feedbackDesc }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="体验课反馈表" align="center" prop="feedbackUrl" width="120px">
        <template #default="scope">
          <div class="image_contain">
            <el-image
              :src="scope.row.feedbackUrl"
              :preview-src-list="getPreviewSrcList(scope.row.feedbackUrl)"
              :initial-index="0"
              preview-teleported
              lazy
              fit="cover"
            />
          </div>
        </template>
      </el-table-column>

      <el-table-column label="反馈表审核" align="left" width="260">
        <template #default="scope">
          <div class="flex flex-justify-between">
            <div>
              <span>审核状态：</span>
              <span><dict-tag :type="DICT_TYPE.ALS_AUDIT_STATUS" :value="scope.row.feedbackAuditStatus" /></span>
            </div>
            <div>
              <span class="right">审核人：</span>
              <span>{{ scope.row.feedbackAuditUserId }}</span>
            </div>
          </div>
          <div>
            <span>审核时间：</span>
            <span>{{ formatDate(scope.row.feedbackAuditTime) }}</span>
          </div>
          <div>

          </div>
          <div class="h-15 overflow-y-auto">
            <span>审核备注：</span>
            <span>{{ scope.row.feedbackAuditRemark }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="陪学计划ID" align="center" prop="planLessonId" width="100px"/>

      <el-table-column label="陪学计划表" align="center" prop="planUrl" width="120px">
        <template #default="scope">
          <div class="image_contain">
            <el-image
              :src="scope.row.planUrl"
              :preview-src-list="getPreviewSrcList(scope.row.planUrl)"
              :initial-index="0"
              preview-teleported
              lazy
              fit="cover"
            />
          </div>
        </template>
      </el-table-column>

      <el-table-column label="陪学计划审核" align="left" width="260">
        <template #default="scope">
          <div class="flex flex-justify-between">
            <div>
              <span>审核状态：</span>
              <span><dict-tag :type="DICT_TYPE.ALS_AUDIT_STATUS" :value="scope.row.planAuditStatus" /></span>
            </div>
            <div>
              <span class="right">审核人：</span>
              <span>{{ scope.row.planAuditUserId }}</span>
            </div>
          </div>
          <div>
            <span>审核时间：</span>
            <span>{{ formatDate(scope.row.planAuditTime) }}</span>
          </div>
          <div>

          </div>
          <div class="h-15 overflow-y-auto">
            <span>审核备注：</span>
            <span>{{ scope.row.planAuditRemark }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="150px"
      />
      <el-table-column label="操作" align="left" header-align="center" fixed="right" width="250px">
        <template #default="scope">

          <el-button
            plain size="small"
            type="primary"
            @click="openForm1('audit', scope.row.orderRegularApplyId)"
            v-hasPermi="['als:bind:update']"
            :disabled="scope.row.feedbackAuditStatus !== 1"
          >
            反馈表审核
          </el-button>

          <el-button
            plain size="small"
            type="primary"
            @click="openForm2('audit', scope.row.orderRegularApplyId)"
            v-hasPermi="['als:bind:update']"
            :disabled="scope.row.planAuditStatus !== 1"
          >
            陪学计划审核
          </el-button>

          <el-button
            plain
            size="small"
            type="primary"
            @click="openForm('update', scope.row.orderRegularApplyId)"
            v-hasPermi="['als:order-regular-apply:update']"
          >
            编辑
          </el-button>

          <el-button
            plain
            size="small"
            type="danger"
            @click="handleDelete(scope.row.orderRegularApplyId)"
            v-hasPermi="['als:order-regular-apply:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <OrderRegularApplyForm ref="formRef" @success="getList" />
  <!--  反馈表审核-->
  <AuditForm ref="formRef1" @audit-call-back="feedbackAuditSubmit"/>
  <!--  正式课陪学计划审核-->
  <AuditForm ref="formRef2" @audit-call-back="planAuditSubmit"/>
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import {dateFormatter, formatDate} from '@/utils/formatTime'
import download from '@/utils/download'
import {
  OrderRegularApplyApi,
  OrderRegularApplyVO
} from '@/api/als/orderregularapply'
import OrderRegularApplyForm from './OrderRegularApplyForm.vue'
import AuditForm from "@/views/als/audit/AuditForm.vue";

/** 正式课申请 列表 */
defineOptions({ name: 'OrderRegularApply' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<OrderRegularApplyVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  orderId: undefined,
  customerId: undefined,
  teacherId: undefined,
  feedbackDesc: undefined,
  planLessonId: undefined,
  feedbackAuditStatus: undefined,
  feedbackAuditTime: [],
  feedbackAuditUserId: undefined,
  feedbackAuditRemark: undefined,
  customerEvaluation: undefined,
  planAuditStatus: undefined,
  planAuditTime: [],
  planAuditUserId: undefined,
  planAuditRemark: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await OrderRegularApplyApi.getOrderRegularApplyPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 反馈表审核 */
const formRef1 = ref()
const openForm1 = (type: string, id?: number) => {
  formRef1.value.open(type, id)
}
const closeForm1 = async () => {
  formRef1.value.close()
}

const feedbackAuditSubmit = async (result: any) => {
  try {
    await OrderRegularApplyApi.auditFeedback(result)
    message.success("审核完成")
  }finally {
    await closeForm1()
    await getList()
  }
}

/** 陪学计划审核 */
const formRef2 = ref()
const openForm2 = (type: string, id?: number) => {
  formRef2.value.open(type, id)
}
const closeForm2 = async () => {
  formRef2.value.close()
}

const planAuditSubmit = async (result: any) => {
  try {
    await OrderRegularApplyApi.auditPlan(result)
    message.success("审核完成")
  }finally {
    await closeForm2()
    await getList()
  }
}



/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await OrderRegularApplyApi.deleteOrderRegularApply(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await OrderRegularApplyApi.exportOrderRegularApply(queryParams)
    download.excel(data, '正式课申请.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const getPreviewSrcList = (urlString) => {
  // 假设 urlString 是以逗号分隔的字符串
  return urlString.split(',').map(url => url.trim());
};

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style scoped>
.image_contain{
  width: 80%;
  height: 100px;
}
</style>
