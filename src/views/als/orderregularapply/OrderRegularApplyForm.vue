<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="70%">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="140px"
      v-loading="formLoading"
      inline
    >
      <el-form-item label="订单ID" prop="orderId" class="!w-230px">
        <el-input v-model="formData.orderId" placeholder="请输入订单ID"/>
      </el-form-item>
      <el-form-item label="家长ID" prop="customerId" class="!w-140px" label-width="70px">
        <el-input v-model="formData.customerId" placeholder="请输入家长ID" />
      </el-form-item>
      <el-form-item label="老师ID" prop="teacherId" class="!w-200px" label-width="100px">
        <el-input v-model="formData.teacherId" placeholder="请输入老师ID" />
      </el-form-item>
      <el-form-item label="陪学计划ID" prop="planLessonId" class="!w-200px" label-width="100px">
        <el-input v-model="formData.planLessonId" placeholder="请输入陪学计划ID" />
      </el-form-item>
      <el-form-item label="家长评价" prop="customerEvaluation" class="!w-200px" label-width="100px">
        <el-select v-model="formData.customerEvaluation" placeholder="请选择来自家长的评价">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_CUSTOMER_EVALUATION)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="体验课反馈简述" prop="feedbackDesc" class="!w-100%">
        <el-input v-model="formData.feedbackDesc" type="textarea" rows="4" maxlength="500" show-word-limit placeholder="请输入体验课反馈简述" />
      </el-form-item>
      <el-form-item label="体验课反馈表" prop="feedbackUrl">
        <UploadImg v-model="formData.feedbackUrl" />
      </el-form-item>
      <el-form-item label="陪学计划表" prop="planUrl">
        <UploadImg v-model="formData.planUrl" />
      </el-form-item>

      <el-form-item label="陪学公约" prop="agreedUrl">
        <UploadImg v-model="formData.agreedUrl" />
      </el-form-item>
      <el-form-item label="反馈表审核状态" prop="feedbackAuditStatus" class="!w-250px">
        <el-select v-model="formData.feedbackAuditStatus" placeholder="请选择反馈表审核状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_AUDIT_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="反馈表审核时间" prop="feedbackAuditTime">
        <el-date-picker
          v-model="formData.feedbackAuditTime"
          type="datetime"
          value-format="x"
          placeholder="选择反馈表审核时间"
        />
      </el-form-item>
      <el-form-item label="反馈表审核人" prop="feedbackAuditUserId" class="!w-250px">
        <el-input v-model="formData.feedbackAuditUserId" placeholder="请输入反馈表审核人" />
      </el-form-item>
      <el-form-item label="反馈表审核备注" prop="feedbackAuditRemark" class="!w-100%">
        <el-input v-model="formData.feedbackAuditRemark" type="textarea" rows="2" maxlength="100" show-word-limit placeholder="请输入反馈表审核备注" />
      </el-form-item>

      <el-form-item label="陪学计划审核状态" prop="planAuditStatus" class="!w-250px">
        <el-select v-model="formData.planAuditStatus" placeholder="请选择正式课陪学计划审核状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_AUDIT_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="陪学计划审核时间" prop="planAuditTime">
        <el-date-picker
          v-model="formData.planAuditTime"
          type="datetime"
          value-format="x"
        />
      </el-form-item>
      <el-form-item label="陪学计划审核人" prop="planAuditUserId" class="!w-250px">
        <el-input v-model="formData.planAuditUserId" placeholder="请输入正式课陪学计划审核人" />
      </el-form-item>
      <el-form-item label="陪学计划审核备注" prop="planAuditRemark" class="!w-100%">
        <el-input v-model="formData.planAuditRemark" type="textarea" rows="2" maxlength="100" show-word-limit placeholder="请输入正式课陪学计划审核备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { OrderRegularApplyApi, OrderRegularApplyVO } from '@/api/als/orderregularapply'

/** 正式课申请 表单 */
defineOptions({ name: 'OrderRegularApplyForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  orderRegularApplyId: undefined,
  orderId: undefined,
  customerId: undefined,
  teacherId: undefined,
  feedbackDesc: undefined,
  feedbackUrl: undefined,
  planUrl: undefined,
  planLessonId: undefined,
  agreedUrl: undefined,
  feedbackAuditStatus: undefined,
  feedbackAuditTime: undefined,
  feedbackAuditUserId: undefined,
  feedbackAuditRemark: undefined,
  customerEvaluation: undefined,
  planAuditStatus: undefined,
  planAuditTime: undefined,
  planAuditUserId: undefined,
  planAuditRemark: undefined
})
const formRules = reactive({
  orderId: [{ required: true, message: '订单ID不能为空', trigger: 'blur' }],
  customerId: [{ required: true, message: '家长ID不能为空', trigger: 'blur' }],
  teacherId: [{ required: true, message: '老师ID不能为空', trigger: 'blur' }],
  feedbackDesc: [{ required: true, message: '体验课反馈简述不能为空', trigger: 'blur' }],
  feedbackUrl: [{ required: true, message: '体验课反馈表不能为空', trigger: 'blur' }],
  planUrl: [{ required: true, message: '正式课陪学计划表不能为空', trigger: 'blur' }],
  planLessonId: [{ required: true, message: '陪学计划ID不能为空', trigger: 'blur' }],
  agreedUrl: [{ required: true, message: '陪学公约不能为空', trigger: 'blur' }],
  feedbackAuditStatus: [{ required: true, message: '反馈表审核状态不能为空', trigger: 'change' }],
  feedbackAuditUserId: [{ required: true, message: '反馈表审核人不能为空', trigger: 'blur' }],
  feedbackAuditRemark: [{ required: true, message: '反馈表审核备注不能为空', trigger: 'blur' }],
  customerEvaluation: [{ required: true, message: '来自家长的评价不能为空', trigger: 'change' }],
  planAuditStatus: [{ required: true, message: '正式课陪学计划审核状态不能为空', trigger: 'change' }],
  planAuditUserId: [{ required: true, message: '正式课陪学计划审核人不能为空', trigger: 'blur' }],
  planAuditRemark: [{ required: true, message: '正式课陪学计划审核备注不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await OrderRegularApplyApi.getOrderRegularApply(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as OrderRegularApplyVO
    if (formType.value === 'create') {
      await OrderRegularApplyApi.createOrderRegularApply(data)
      message.success(t('common.createSuccess'))
    } else {
      await OrderRegularApplyApi.updateOrderRegularApply(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    orderRegularApplyId: undefined,
    orderId: undefined,
    customerId: undefined,
    teacherId: undefined,
    feedbackDesc: undefined,
    feedbackUrl: undefined,
    planUrl: undefined,
    planLessonId: undefined,
    agreedUrl: undefined,
    feedbackAuditStatus: undefined,
    feedbackAuditTime: undefined,
    feedbackAuditUserId: undefined,
    feedbackAuditRemark: undefined,
    customerEvaluation: undefined,
    planAuditStatus: undefined,
    planAuditTime: undefined,
    planAuditUserId: undefined,
    planAuditRemark: undefined
  }
  formRef.value?.resetFields()
}
</script>
