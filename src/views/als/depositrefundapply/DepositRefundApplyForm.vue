<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="申请人" prop="teacherId">
        <el-input v-model="formData.teacherId" placeholder="请输入申请人" />
      </el-form-item>
      <el-form-item label="课时记录ID" prop="lessonHourId">
        <el-input v-model="formData.lessonHourId" placeholder="请输入课时记录ID" />
      </el-form-item>
      <el-form-item label="申请时间" prop="applyTime">
        <el-date-picker
          v-model="formData.applyTime"
          type="date"
          value-format="x"
          placeholder="选择申请时间"
        />
      </el-form-item>
      <el-form-item label="申请备注" prop="applyReason">
        <el-input v-model="formData.applyReason" type="textarea" placeholder="请输入申请备注" />
      </el-form-item>
      <el-form-item label="处理状态" prop="dealStatus">
        <el-select v-model="formData.dealStatus" placeholder="请选择处理状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_DEPOSIT_DEAL_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { DepositRefundApplyApi, DepositRefundApplyVO } from '@/api/als/depositrefundapply'

/** 退押申请 表单 */
defineOptions({ name: 'DepositRefundApplyForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  depositRefundApplyId: undefined,
  teacherId: undefined,
  lessonHourId: undefined,
  applyTime: undefined,
  applyReason: undefined,
  dealStatus: undefined
})
const formRules = reactive({
  teacherId: [{ required: true, message: '申请人不能为空', trigger: 'blur' }],
  lessonHourId: [{ required: true, message: '课时记录ID不能为空', trigger: 'blur' }],
  applyReason: [{ required: true, message: '申请备注不能为空', trigger: 'blur' }],
  dealStatus: [{ required: true, message: '处理状态不能为空', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await DepositRefundApplyApi.getDepositRefundApply(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as DepositRefundApplyVO
    if (formType.value === 'create') {
      await DepositRefundApplyApi.createDepositRefundApply(data)
      message.success(t('common.createSuccess'))
    } else {
      await DepositRefundApplyApi.updateDepositRefundApply(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    depositRefundApplyId: undefined,
    teacherId: undefined,
    lessonHourId: undefined,
    applyTime: undefined,
    applyReason: undefined,
    dealStatus: undefined
  }
  formRef.value?.resetFields()
}
</script>