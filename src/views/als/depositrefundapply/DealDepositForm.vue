<template>
  <Dialog title="押金处理" v-model="dialogVisible" width="40%">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="处理方式" prop="dealMethod" label-width="100px">
        <el-radio-group v-model="formData.dealMethod">
          <el-radio-button class="mr-2 mt-1"
                           v-for="dict in getIntDictOptions(DICT_TYPE.ALS_DEPOSIT_DEAL_METHOD)"
                           :key="dict.value"
                           :label="dict.value"
                           @click="dealMethodChange(dict.value)">
            {{ dict.label }}
          </el-radio-button>
        </el-radio-group>
      </el-form-item>


      <el-form-item label="押金课时" prop="customerGet" label-width="100px" class="!w-100%">
          <span style="font-size: 16px" class="font-bold color-blue">{{ depositAmount }}</span>
      </el-form-item>

        <el-form-item label="家长分得" prop="customerGet" label-width="100px" class="!w-100%">
          <el-input-number controls-position="right" v-model="formData.customerGet" :precision="2" :step="0.1" min=0 :disabled="formData.dealMethod < 7"/>
        </el-form-item>
        <el-form-item label="老师分得" prop="teacherGet" label-width="100px" class="!w-100%">
          <el-input-number controls-position="right" v-model="formData.teacherGet" :precision="2" :step="0.1" min=0 :disabled="formData.dealMethod < 7"/>
        </el-form-item>
        <el-form-item label="平台分得" prop="platformGet" label-width="100px" class="!w-100%">
          <el-input-number controls-position="right" v-model="formData.platformGet" :precision="2" :step="0.1" min=0 :disabled="formData.dealMethod < 7"/>
        </el-form-item>

    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import {
  DepositRefundApplyApi,
  DepositRefundDealVO
} from "@/api/als/depositrefundapply";

/** 押金处理 表单 */
defineOptions({ name: 'DealDepositForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref<DepositRefundDealVO>({
  lessonHourId:0,
  dealMethod:0,
  customerGet:0,
  teacherGet:0,
  platformGet:0
});
const depositAmount = ref(0)
// 剩余
const depositAmountRemain = ref(0)
const formRules = reactive({
  dealMethod: [{ required: true, message: '处理方式不能为空', trigger: 'change' }],
})

/** 重置表单 */
const resetForm = () => {
  formData.value.customerGet = 0;
  formData.value.teacherGet = 0;
  formData.value.platformGet = 0;
}
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (lessonHourId: number,classHour?: number) => {
  dialogVisible.value = true
  // 修改时，设置数据
  formData.value.lessonHourId = lessonHourId
  formData.value.dealMethod = 7 // 模式选择全部退还
  depositAmount.value = classHour
  resetForm()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as DepositRefundDealVO
    depositAmountRemain.value = (depositAmount.value * 100 - data.customerGet * 100 - data.teacherGet * 100 - data.platformGet * 100)/100
    if (depositAmountRemain.value > 0){
      return message.error('剩余押金课时：'+depositAmountRemain.value)
    }
    await DepositRefundApplyApi.dealDepositRefund(data)
    message.success(t('common.updateSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

// 处理方式监听器
const dealMethodChange = async (dealMethod: number) =>{
  switch (dealMethod) {
    case 1:
      // 全退还老师
      formData.value.customerGet = 0;
      formData.value.teacherGet = depositAmount.value;
      formData.value.platformGet = 0;
      break;
    case 2:
      // 全扣留平台
      formData.value.customerGet = 0;
      formData.value.teacherGet = 0;
      formData.value.platformGet = depositAmount.value;
      break;
    case 3:
      // 全补偿家长
      formData.value.customerGet = depositAmount.value;
      formData.value.teacherGet = 0;
      formData.value.platformGet = 0;
      break;
    case 4:
      // 家长老师平分
      formData.value.teacherGet = depositAmount.value / 2;
      formData.value.customerGet = depositAmount.value / 2;
      formData.value.platformGet = 0;
      break;
    case 5:
      // 家长平台平分
      formData.value.customerGet = depositAmount.value / 2;
      formData.value.teacherGet = 0;
      formData.value.platformGet = depositAmount.value / 2;
      break;
    case 6:
      // 老师平台平分
      formData.value.customerGet = 0;
      formData.value.teacherGet = depositAmount.value / 2;
      formData.value.platformGet = depositAmount.value / 2;
      break;
    case 7:
      // 自定义退押金额
      formData.value.customerGet = 0;
      formData.value.teacherGet = 0;
      formData.value.platformGet = 0;
      break;
    default:
      break;
  }
}

</script>

<style scoped lang="scss">
:deep(.el-radio-button__inner){
  border-left: var(--el-border);
  border-radius: var(--el-border-radius-base) !important;
  box-shadow: none !important;
  font-weight: normal;
}
:deep(.el-form-item--default){
  margin-bottom: 10px;
}
:deep(.el-input-number.is-controls-right .el-input__inner){
  padding-left: 1px !important;
  padding-right: 1px !important;
}
</style>
