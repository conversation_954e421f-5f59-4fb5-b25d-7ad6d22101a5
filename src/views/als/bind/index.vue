<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="75px"
    >
      <el-form-item label="老师ID" prop="teacherId">
        <el-input
          v-model="queryParams.teacherId"
          placeholder="请输入老师ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="家长ID" prop="customerId">
        <el-input
          v-model="queryParams.customerId"
          placeholder="请输入家长ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="绑定人" prop="bindUserId">
        <el-input
          v-model="queryParams.bindUserId"
          placeholder="请输入绑定人"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="解绑申请时间" prop="unbindApplyTime">
        <el-date-picker
          v-model="queryParams.unbindApplyTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="绑定状态" prop="bindStatus">
        <el-select
          v-model="queryParams.bindStatus"
          placeholder="请选择绑定状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_BIND_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="订单ID" prop="orderId">
        <el-input
          v-model="queryParams.orderId"
          placeholder="请输入订单ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="绑定时间" prop="bindTime">
        <el-date-picker
          v-model="queryParams.bindTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="解绑时间" prop="unbindTime">
        <el-date-picker
          v-model="queryParams.unbindTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="解绑人" prop="unbindUserId">
        <el-input
          v-model="queryParams.unbindUserId"
          placeholder="请输入解绑人"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="解绑原因" prop="unbindReason">
        <el-input
          v-model="queryParams.unbindReason"
          placeholder="请输入解绑原因"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="解绑审核状态" prop="unbindAuditStatus">
        <el-select
          v-model="queryParams.unbindAuditStatus"
          placeholder="请选择解绑审核状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_AUDIT_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['als:bind:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['als:bind:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" border size="small">
      <el-table-column label="绑定表ID" align="center" prop="bindId" />

      <el-table-column label="老师信息" header-align="left" align="left" width="200" >
        <template #default="scope">
          <div>
            <span class="column-label">老师ID：</span>
            <span>{{ scope.row.teacherId }}</span>
          </div>
          <div>
            <span class="column-label">姓名：</span>
            <span>{{ scope.row.teacherName }}</span>
          </div>
          <div>
            <span class="column-label">手机：</span>
            <span>{{ scope.row.teacherPhone }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="家长信息" header-align="left" align="left" width="200" >
        <template #default="scope">
          <div>
            <span class="column-label">家长ID：</span>
            <span>{{ scope.row.customerId }}</span>
          </div>
          <div>
            <span class="column-label">姓名：</span>
            <span>{{ scope.row.customerName }}</span>
          </div>
          <div>
            <span class="column-label">手机：</span>
            <span>{{ scope.row.customerPhone }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="绑定人" align="center" prop="bindUserId" />
      <el-table-column label="绑定状态" align="center" prop="bindStatus" width="100px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ALS_BIND_STATUS" :value="scope.row.bindStatus" />
        </template>
      </el-table-column>
      <el-table-column label="订单ID" align="center" prop="orderId" />
      <el-table-column
        label="绑定时间"
        align="center"
        prop="bindTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column
        label="解绑申请时间"
        align="center"
        prop="unbindApplyTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="解绑审核状态" align="center" prop="unbindAuditStatus" width="120px">
        <template #default="scope">
          <dict-tag v-if="scope.row.unbindAuditStatus > 0" :type="DICT_TYPE.ALS_AUDIT_STATUS" :value="scope.row.unbindAuditStatus" />
          <span v-if="scope.row.unbindAuditStatus == 0">-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="解绑审核时间"
        align="center"
        prop="unbindTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="解绑人" align="center" prop="unbindUserId" />
      <el-table-column label="解绑原因" align="center" prop="unbindReason" width="200px"/>

      <el-table-column label="操作" header-align="center" align="left" fixed="right" width="250px">
        <template #default="scope">
          <el-button
            plain
            size="small"
            type="primary"
            @click="openForm('update', scope.row.bindId)"
            v-hasPermi="['als:bind:update']"
          >
            编辑
          </el-button>

          <el-button
            plain
            size="small"
            type="primary"
            @click="unbind(scope.row.bindId)"
            v-hasPermi="['als:bind:update']"
            :disabled="scope.row.bindStatus != 1 || scope.row.unbindAuditStatus > 0"
          >
            申请解绑
          </el-button>

          <el-button
            plain
            size="small"
            type="primary"
            @click="openForm1('audit', scope.row.bindId)"
            v-hasPermi="['als:bind:update']"
          >
            解绑审核
          </el-button>

          <el-button
            plain
            size="small"
            type="danger"
            @click="handleDelete(scope.row.bindId)"
            v-hasPermi="['als:bind:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <BindForm ref="formRef" @success="getList" />
<!--  解绑审核-->
  <AuditForm ref="formRef1" @audit-call-back="auditSubmit"/>
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import {BindApi, BindVO, UnbindAuditReqVO} from '@/api/als/bind'
import BindForm from './BindForm.vue'
import AuditForm from "@/views/als/audit/AuditForm.vue";
import {AuditFormData} from "@/api/als/audit";

/** 绑定 列表 */
defineOptions({ name: 'Bind' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<BindVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  teacherId: undefined,
  customerId: undefined,
  bindUserId: undefined,
  unbindApplyTime: [],
  bindStatus: undefined,
  orderId: undefined,
  bindTime: [],
  unbindTime: [],
  unbindUserId: undefined,
  unbindReason: undefined,
  unbindAuditStatus: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const auditParams = ref({
  id: undefined,
  auditStatus: undefined,
  auditRemark: undefined
})
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await BindApi.getBindPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 审核 */
const formRef1 = ref()
const openForm1 = (type: string, id?: number) => {
  formRef1.value.open(type, id)
}
const closeForm1 = async () => {
  formRef1.value.close()
}

const auditSubmit = async (result: any) => {
  try {
     await BindApi.unbindAudit(result)
  }finally {
    await closeForm1()
    await getList()
  }
}

/**
 * 解除绑定
 * @param bindId
 */
const unbind = async (bindId: number) => {
  try {
    // 删除的二次确认
    await message.confirm("确认要解除绑定吗？")
    // 发起删除
    await BindApi.unbind(bindId)
    message.success("提交申请成功")
    // 刷新列表
    await getList()
  } catch {}
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await BindApi.deleteBind(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await BindApi.exportBind(queryParams)
    download.excel(data, '绑定.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
