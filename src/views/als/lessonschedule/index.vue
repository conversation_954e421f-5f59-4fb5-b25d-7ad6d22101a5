<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="陪学记录ID" prop="lessonRecordId">
        <el-input
          v-model="queryParams.lessonRecordId"
          placeholder="请输入陪学记录ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="开始时间" prop="startTime">
        <el-date-picker
          v-model="queryParams.startTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="结束时间" prop="endTime">
        <el-date-picker
          v-model="queryParams.endTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-200px"
        />
      </el-form-item>

      <el-form-item label="任务名称" prop="taskContent">
        <el-input
          v-model="queryParams.taskContent"
          placeholder="请输入任务名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="状态" prop="taskStatus">
        <el-select
          v-model="queryParams.taskStatus"
          placeholder="请选择状态"
          clearable
          class="!w-200px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_SCHEDULE_TASK_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="实际结束时间" prop="realEndTime">
        <el-date-picker
          v-model="queryParams.realEndTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['als:lesson-schedule:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['als:lesson-schedule:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" border size="small">
      <el-table-column label="时间规划执行ID" align="center" prop="lessonScheduleId" width="120"/>
      <el-table-column label="陪学记录ID" align="center" prop="lessonRecordId" />
      <el-table-column label="计划时间" width="250">
        <template #default="scope">
          <div>
            <span>计划开始时间：</span>
            <span>{{ formatDate(scope.row.startTime) }}</span>
          </div>
          <div>
            <span>计划结束时间：</span>
            <span>{{ formatDate(scope.row.endTime) }}</span>
          </div>
          <div>
            <span>间隔</span>
            <span>{{ scope.row.periodMinute }}</span> 分钟
          </div>
        </template>
      </el-table-column>
      <el-table-column label="实际时间" width="250">
        <template #default="scope">
          <div>
            <span>状态：</span>
            <dict-tag :type="DICT_TYPE.ALS_SCHEDULE_TASK_STATUS" :value="scope.row.taskStatus" />
          </div>
          <div>
            <span>实际结束时间：</span>
            <span>{{ formatDate(scope.row.realEndTime) }}</span>
          </div>
          <div>
            <span>误差：</span>
            <span>{{ scope.row.errorMinute }}</span> 分钟
          </div>
        </template>
      </el-table-column>
      <el-table-column label="任务名称" align="center" prop="taskContent" width="200"/>

      <el-table-column label="原因" align="left" prop="reason" width="300" >
        <template #default="scope">
          <div class="h-20 overflow-y-auto">
            <span>{{ scope.row.reason }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="方案" align="left" prop="programme" width="300" >
        <template #default="scope">
          <div class="h-20 overflow-y-auto">
            <span>{{ scope.row.programme }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" fixed="right" width="200">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.lessonScheduleId)"
            v-hasPermi="['als:lesson-schedule:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.lessonScheduleId)"
            v-hasPermi="['als:lesson-schedule:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <LessonScheduleForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import {dateFormatter, formatDate} from '@/utils/formatTime'
import download from '@/utils/download'
import { LessonScheduleApi, LessonScheduleVO } from '@/api/als/lessonschedule'
import LessonScheduleForm from './LessonScheduleForm.vue'

/** 时间规划执行 列表 */
defineOptions({ name: 'LessonSchedule' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<LessonScheduleVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  lessonRecordId: undefined,
  startTime: [],
  endTime: [],
  periodMinute: undefined,
  realEndTime: [],
  errorMinute: undefined,
  taskContent: undefined,
  taskStatus: undefined,
  reason: undefined,
  programme: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await LessonScheduleApi.getLessonSchedulePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await LessonScheduleApi.deleteLessonSchedule(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await LessonScheduleApi.exportLessonSchedule(queryParams)
    download.excel(data, '时间规划执行.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const route = useRoute()
const id = Number(route.params.id)

/** 初始化 **/
onMounted(() => {
  if (id) {
    // debugger
    queryParams.lessonRecordId = id
  }
  getList()
})
</script>
