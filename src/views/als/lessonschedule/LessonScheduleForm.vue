<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="陪学记录ID" prop="lessonRecordId">
        <el-input v-model="formData.lessonRecordId" placeholder="请输入陪学记录ID" />
      </el-form-item>
      <el-form-item label="开始时间" prop="startTime">
        <el-date-picker
          v-model="formData.startTime"
          type="date"
          value-format="x"
          placeholder="选择开始时间"
        />
      </el-form-item>
      <el-form-item label="结束时间" prop="endTime">
        <el-date-picker
          v-model="formData.endTime"
          type="date"
          value-format="x"
          placeholder="选择结束时间"
        />
      </el-form-item>
      <el-form-item label="间隔：分钟" prop="periodMinute">
        <el-input v-model="formData.periodMinute" placeholder="请输入间隔：分钟" />
      </el-form-item>
      <el-form-item label="实际结束时间" prop="realEndTime">
        <el-date-picker
          v-model="formData.realEndTime"
          type="date"
          value-format="x"
          placeholder="选择实际结束时间"
        />
      </el-form-item>
      <el-form-item label="实际结束时间误差：分钟" prop="errorMinute">
        <el-input v-model="formData.errorMinute" placeholder="请输入实际结束时间误差：分钟" />
      </el-form-item>
      <el-form-item label="任务名称" prop="taskContent">
        <el-input v-model="formData.taskContent" placeholder="请输入任务名称" />
      </el-form-item>
      <el-form-item label="状态" prop="taskStatus">
        <el-select v-model="formData.taskStatus" placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_SCHEDULE_TASK_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="原因" prop="reason">
        <el-input v-model="formData.reason" type="textarea" placeholder="请输入原因" />
      </el-form-item>
      <el-form-item label="方案" prop="programme">
        <el-input v-model="formData.programme" type="textarea" placeholder="请输入方案" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { LessonScheduleApi, LessonScheduleVO } from '@/api/als/lessonschedule'

/** 时间规划执行 表单 */
defineOptions({ name: 'LessonScheduleForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  lessonScheduleId: undefined,
  lessonRecordId: undefined,
  startTime: undefined,
  endTime: undefined,
  periodMinute: undefined,
  realEndTime: undefined,
  errorMinute: undefined,
  taskContent: undefined,
  taskStatus: undefined,
  reason: undefined,
  programme: undefined
})
const formRules = reactive({
  lessonRecordId: [{ required: true, message: '陪学记录ID不能为空', trigger: 'blur' }],
  periodMinute: [{ required: true, message: '间隔：分钟不能为空', trigger: 'blur' }],
  errorMinute: [{ required: true, message: '实际结束时间误差：分钟不能为空', trigger: 'blur' }],
  taskContent: [{ required: true, message: '任务名称不能为空', trigger: 'blur' }],
  taskStatus: [{ required: true, message: '状态不能为空', trigger: 'change' }],
  reason: [{ required: true, message: '原因不能为空', trigger: 'blur' }],
  programme: [{ required: true, message: '方案不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await LessonScheduleApi.getLessonSchedule(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as LessonScheduleVO
    if (formType.value === 'create') {
      await LessonScheduleApi.createLessonSchedule(data)
      message.success(t('common.createSuccess'))
    } else {
      await LessonScheduleApi.updateLessonSchedule(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    lessonScheduleId: undefined,
    lessonRecordId: undefined,
    startTime: undefined,
    endTime: undefined,
    periodMinute: undefined,
    realEndTime: undefined,
    errorMinute: undefined,
    taskContent: undefined,
    taskStatus: undefined,
    reason: undefined,
    programme: undefined
  }
  formRef.value?.resetFields()
}
</script>