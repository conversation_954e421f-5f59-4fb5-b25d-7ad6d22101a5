<script setup lang="ts">
import {LocationsVO, OrderApi} from "@/api/als/order";

defineOptions({ name: 'MapContainer' })

import { onMounted, onUnmounted, ref } from "vue";
import AMapLoader from "@amap/amap-jsapi-loader";
import {formatDate} from "@/utils/formatTime";
import PosterView from "@/views/als/order/PosterVue.vue";

const message = useMessage() // 消息弹窗
let map = null;
let searchInput = ref('');
let searchMarker = null;
let markers = [];
let placeSearch = null;
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
});
const locations = ref<LocationsVO[]>([]) // 列表的数据
const performSearch = (keyword) => {
  if (map) {
    // 清除之前的搜索结果
    if (markers.length > 0) {
      map.remove(markers); // 移除之前的标记
      markers = []; // 清空标记数组
    }

    if (keyword == undefined || keyword === ''){
      document.getElementById('panel').innerHTML = '';
      map.remove(markers)
      return
    }
     placeSearch = new AMap.PlaceSearch({
      pageSize: 2, //单页显示结果条数
      pageIndex: 1, //页码
      city: "025", //兴趣点城市
      citylimit: true, //是否强制限制在设置的城市内搜索
      // map: map, //展现结果的地图实例
      panel: "panel", //参数值为你页面定义容器的 id，结果列表将在此容器中进行展示。
      autoFitView: true, //是否自动调整地图视野使绘制的 Marker 点都处于视口的可见范围
    });

    placeSearch.search(keyword, (status, result) => {
      if (status === 'complete' && result.info === 'OK') {
        // 在地图上标记搜索结果
        result.poiList.pois.forEach(poi => {
          searchMarker = new AMap.Marker({
            map: map,
            position: poi.location,
            title: poi.name,
            // icon: '//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png',
            offset: new AMap.Pixel(-13, -30)
          });
          markers.push(searchMarker);
        });
      } else {
        console.error("Search failed:",status, result);
        message.error("地图查询错误："+result)
      }
    });
  }
};

const getList = async () => {
  try {
    const data = await OrderApi.getOrderMap(queryParams)
    if (data === null){
      return
    }
    locations.value = data
    // debugger
    console.log(111,locations.value.length)
  } finally {
  }
}

const { push } = useRouter()

const openDetail = async (id: number) => {
  console.log('openDetail::',id)
  // await nextTick(); // 确保 DOM 更新
  await push({ name: 'ExpOrder2', params: { id } });
}
const openDetail2 = (id: number) => {
  console.log('openDetail2')
  push({ name: 'CustomerDetail', params: { id } })
}
const openDetail3 = (id: number) => {
  console.log('openDetail3')
  push({ name: 'CustomerDetail', params: { id } })
}

const posterRef = ref()
const openPoster = (type: string, id?: number) => {
  posterRef.value.open(type, id)
}

onMounted(async () => {
  await getList();
  window._AMapSecurityConfig = {
    securityJsCode: 'b3423bc381c356358ce06f25ce697e53',
  };
  AMapLoader.load({
    key: "fcdb5fda7abca2df4985c82a20c30bf0",
    version: "2.0",
    plugins: ['AMap.CircleMarker', 'AMap.LngLat', 'AMap.InfoWindow', 'AMap.PlaceSearch'],
  })
    .then((AMap) => {

      map = new AMap.Map("container", {
        viewMode: "3D",
        zoom: 12,
        center: [118.796624, 32.059344],
        resizeEnable: true,
      });
      // [118.867779, 32.011673],
      //   [118.962043, 32.085367]
      // const locations = [
      //   {
      //     "lng":"118.867779",
      //     "lat":"32.011673",
      //     "orderId":"118763",
      //     "releaseTime":"2024-02-01 12:00:00",
      //     "orderAddress":"江苏省南京市江宁路AAAA1111发的所发生的",
      //     "customerId":1,
      //     "customerName":"张三",
      //     "customerPhone":"17309875432",
      //     "lessonPeriodRemain":"0.05",
      //     "headOperateName":"季康",
      //     "confirmCount":"5",
      //     "sourceChannel":"增加老师",
      //     "requireSex":"男",
      //     "demandContent":"1111神鼎飞丹砂防守打法的说法手打放大说法是代发的算法的算法是打发打撒粉色污染玩儿玩儿玩儿完i偶偶欧沃UR哦污染111"
      //   }
      // ];

      const radius = 10; //单位:px

      console.log("locations",locations.value.length)
      locations.value.forEach(location => {
        const center = new AMap.LngLat(location.longitude, location.latitude);
        const circleMarker = new AMap.CircleMarker({
          center,
          radius,
          strokeColor: "white",
          strokeWeight: 2,
          strokeOpacity: 0.5,
          fillColor: "rgba(0,0,255,1)",
          fillOpacity: 0.5,
          zIndex: 11,
          cursor: "pointer",
        });

        const content = `
          <div class="font-size-12px !w-240px">
            <div>
                <a href="#" id="btnOrder-${location.orderId}">` + location.orderId + `</a>
                <span class="ml-5px">` + formatDate(location.releaseTime)  + `</span>
            </div>
            <div>
                <span>` + location.orderAddress + `</span>
            </div>
            <div>
                <span>` + location.customerName + `</span>
                <span>` + location.customerPhone + `</span>
            </div>
            <div>
                <span>剩余课时：</span>
                <span>` + location.lessonPeriodRemain + `</span>
                <span>负责人：</span>
                <span>` + location.headOperateName + `</span>
            </div>
            <div>
                <span><a href="#" id="btnConfirm-${location.orderId}">`+ location.confirmCount +`人接单待确认</a></span>
                <span class="ml-5px"><a href="#" id="btnCopyOrder-${location.orderId}">复制订单</a></span>
            </div>
            <div>
                <span>获客渠道:</span>
                <span>` + location.sourceChannelDesc + `</span>
                <span class="ml-5px">期望性别:</span>
                <span>` + location.requireSexDesc + `</span>

            </div>
            <div>
                <div style="height: 1px; background-color: #bbbaba;"></div>
                <span class="color-warmgray">` + location.demandContent + `</span>
            </div>

          </div>
        `;

        const infoWindow = new AMap.InfoWindow({
          content:content,
          anchor: "top-left",
        });

        circleMarker.on("click", () => {
          infoWindow.open(map, center);
          // 添加事件监听器
          setTimeout(() => {
            const button = document.getElementById(`btnOrder-${location.orderId}`);
            const btnConfirm = document.getElementById(`btnConfirm-${location.orderId}`);
            const btnCopyOrder = document.getElementById(`btnCopyOrder-${location.orderId}`);
            if (button) {
              button.onclick = () => {
                openDetail(location.orderId);
              }
            }
            if (btnConfirm) {
              btnConfirm.onclick = () => openDetail2(location.orderId);
            }
            if (btnCopyOrder) {
              btnCopyOrder.onclick = () => openPoster('订单海报',location.orderId);
            }
          }, 0); // 确保按钮已添加到 DOM
        });

        circleMarker.setMap(map);
      });
    })
    .catch((e) => {
      message.error(e)
    });
});

onUnmounted(() => {
  map?.destroy();
});
</script>

<template>
  <div id="container"></div>
  <div id="panel"></div>
  <el-input
    v-model="searchInput"
    placeholder="搜索地点"
    @keyup.enter="performSearch(searchInput)"
    @blur="performSearch(searchInput)"
    class="searchInput"
    clearable
  />

  <PosterView ref="posterRef" @success="getList" />
</template>

<style scoped lang="scss">
#container {
  width: 100%;
  height: 800px;
}
.searchInput{
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 1000;
  width: 200px;
}
#panel {
  position: absolute;
  background-color: white;
  max-height: 90%;
  overflow-y: auto;
  top: 50px;
  left: 10px;
  width: 280px;
}
</style>
