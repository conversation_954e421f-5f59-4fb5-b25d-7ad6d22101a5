<script setup lang="ts">
import { AuditFormData} from '@/api/als/audit'
defineOptions({ name: 'AuditForm' })

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref<AuditFormData>({})
// 表单验证规则
const formRules = reactive({
  auditStatus: [{ required: true, message: '审核状态不能为空', trigger: 'blur' }],
  auditRemark: formData.value.auditStatus === 3 ? [{ required: true, message: '驳回原因不能为空', trigger: 'blur' }] : []
})
const formRef = ref() // 表单 Ref

const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  resetForm()
  formData.value.id = id;
}
const close = async () => {
  dialogVisible.value = false
  resetForm()
}
defineExpose({ open, close }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['auditCallBack']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()

  emit('auditCallBack', formData.value)
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    auditStatus: undefined,
    auditRemark: undefined,
  }
  formRef.value?.resetFields()
}
</script>

<template>
  <Dialog title="审批" v-model="dialogVisible" width="600">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      v-loading="formLoading"
      label-width="80px"
      label-position="right"
      size="default"
    >
      <el-form-item label="审核状态" prop="auditStatus">
          <el-radio-group v-model="formData.auditStatus">
            <el-radio value = 2 size="default" border>通过</el-radio>
            <el-radio value = 3 size="default" border>驳回</el-radio>
          </el-radio-group>
      </el-form-item>
      <el-form-item label="备注" prop="auditRemark">
        <el-input v-model="formData.auditRemark" placeholder="请简要描述" type="textarea" rows="4" show-word-limit maxlength="50"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<style scoped lang="scss">

</style>
