<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="130px"
      v-loading="formLoading"
    >
      <el-form-item label="老师ID" prop="teacherId">
        <el-input v-model="formData.teacherId" placeholder="请输入老师ID" />
      </el-form-item>
      <el-form-item label="老师姓名" prop="teacherName">
        <el-input v-model="formData.teacherName" placeholder="请输入老师姓名" />
      </el-form-item>
      <el-form-item label="性别" prop="teacherSex">
        <el-select v-model="formData.teacherSex" placeholder="请选择性别">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.ALS_SEX)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="证书照片URL" prop="picUrl">
        <el-input v-model="formData.picUrl" placeholder="请输入证书照片URL" />
      </el-form-item>
      <el-form-item label="证书编号" prop="certificateNo">
        <el-input v-model="formData.certificateNo" placeholder="请输入证书编号" />
      </el-form-item>
      <el-form-item label="身份证号码" prop="teacherIdNumber">
        <el-input v-model="formData.teacherIdNumber" placeholder="请输入身份证号码" />
      </el-form-item>
      <el-form-item label="证书状态" prop="certificateStatus">
        <el-select v-model="formData.certificateStatus" placeholder="请选择证书状态 0有效 1无效-已过期 2无效-已注销">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_CERTIFICATE_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="有效期" prop="validTime">
        <el-date-picker
          v-model="formData.validTime"
          type="date"
          value-format="x"
          placeholder="选择有效期"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { TeacherCertificateApi, TeacherCertificateVO } from '@/api/als/teachercertificate'

/** 老师证书 表单 */
defineOptions({ name: 'TeacherCertificateForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  certificateId: undefined,
  teacherId: undefined,
  teacherName: undefined,
  teacherSex: undefined,
  picUrl: undefined,
  certificateNo: undefined,
  certificateStatus: undefined,
  validTime: undefined,
  remark: undefined,
  teacherIdNumber: undefined
})
const formRules = reactive({
  teacherId: [{ required: true, message: '老师ID不能为空', trigger: 'blur' }],
  teacherName: [{ required: true, message: '老师姓名不能为空', trigger: 'blur' }],
  teacherSex: [{ required: true, message: '性别不能为空', trigger: 'change' }],
  picUrl: [{ required: true, message: '证书照片URL不能为空', trigger: 'blur' }],
  certificateNo: [{ required: true, message: '证书编号不能为空', trigger: 'blur' }],
  certificateStatus: [{ required: true, message: '证书状态 0有效 1无效-已过期 2无效-已注销不能为空', trigger: 'change' }],
  validTime: [{ required: true, message: '有效期不能为空', trigger: 'blur' }],
  remark: [{ required: true, message: '备注不能为空', trigger: 'blur' }],
  teacherIdNumber: [{ required: true, message: '身份证号码不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await TeacherCertificateApi.getTeacherCertificate(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TeacherCertificateVO
    if (formType.value === 'create') {
      await TeacherCertificateApi.createTeacherCertificate(data)
      message.success(t('common.createSuccess'))
    } else {
      await TeacherCertificateApi.updateTeacherCertificate(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    certificateId: undefined,
    teacherId: undefined,
    teacherName: undefined,
    teacherSex: undefined,
    picUrl: undefined,
    certificateNo: undefined,
    certificateStatus: undefined,
    validTime: undefined,
    remark: undefined,
    teacherIdNumber: undefined
  }
  formRef.value?.resetFields()
}
</script>
