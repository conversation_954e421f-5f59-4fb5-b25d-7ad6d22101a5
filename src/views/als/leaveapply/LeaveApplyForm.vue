<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="60%">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
      inline
    >
      <el-form-item label="课时记录ID" prop="orderRecordId">
        <el-input v-model="formData.orderRecordId" placeholder="请输入课时记录ID" />
      </el-form-item>
      <el-form-item label="订单ID" prop="orderId">
        <el-input v-model="formData.orderId" placeholder="请输入订单ID" />
      </el-form-item>
      <el-form-item label="家长ID" prop="customerId">
        <el-input v-model="formData.customerId" placeholder="请输入家长ID" />
      </el-form-item>
      <el-form-item label="老师ID" prop="teacherId">
        <el-input v-model="formData.teacherId" placeholder="请输入老师ID" />
      </el-form-item>
      <el-form-item label="陪学阶段" prop="stage" class="!w-290px">
        <el-select v-model="formData.stage" placeholder="请选择陪学阶段">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_LEAVE_STAGE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="请假方" prop="whoLeave" class="!w-290px">
        <el-select v-model="formData.whoLeave" placeholder="请选择请假方">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_WHO_LEAVE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="原定上课时间" prop="planTime">
        <el-date-picker
          v-model="formData.planTime"
          type="datetime"
          value-format="x"
          placeholder="选择原定上课时间"
        />
      </el-form-item>
      <el-form-item label="调整后上课时间" prop="newTime" label-width="150px">
        <el-date-picker
          v-model="formData.newTime"
          type="datetime"
          value-format="x"
          placeholder="请选择时间"
        />
      </el-form-item>
      <el-form-item label="申请时间" prop="applyTime">
        <el-date-picker
          v-model="formData.applyTime"
          type="datetime"
          value-format="x"
          placeholder="选择申请时间"
        />
      </el-form-item>
      <el-form-item label="申请备注" prop="applyRemark" class="!w-100%">
        <el-input v-model="formData.applyRemark" type="textarea" rows="2" maxlength="50" show-word-limit placeholder="请输入申请备注" />
      </el-form-item>
      <el-form-item label="审核状态" prop="auditStatus" class="!w-290px">
        <el-select v-model="formData.auditStatus" placeholder="请选择审核状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_AUDIT_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审核时间" prop="auditTime">
        <el-date-picker
          v-model="formData.auditTime"
          type="datetime"
          value-format="x"
          placeholder="选择审核时间"
          class="!w-190px"
        />
      </el-form-item>
      <el-form-item label="审核人" prop="auditUserId" class="!w-250px">
        <el-input v-model="formData.auditUserId" placeholder="请输入审核人" />
      </el-form-item>
      <el-form-item label="审核备注" prop="auditRemark" class="!w-100%">
        <el-input v-model="formData.auditRemark" type="textarea" rows="2" maxlength="50" show-word-limit placeholder="请输入审核备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { LeaveApplyApi, LeaveApplyVO } from '@/api/als/leaveapply'

/** 请假申请列表 表单 */
defineOptions({ name: 'LeaveApplyForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  leaveApplyId: undefined,
  orderRecordId: undefined,
  orderId: undefined,
  customerId: undefined,
  teacherId: undefined,
  stage: undefined,
  whoLeave: undefined,
  planTime: undefined,
  newTime: undefined,
  applyTime: undefined,
  applyRemark: undefined,
  auditStatus: undefined,
  auditTime: undefined,
  auditUserId: undefined,
  auditRemark: undefined
})
const formRules = reactive({
  orderRecordId: [{ required: true, message: '课时记录ID不能为空', trigger: 'blur' }],
  orderId: [{ required: true, message: '订单ID不能为空', trigger: 'blur' }],
  customerId: [{ required: true, message: '家长ID不能为空', trigger: 'blur' }],
  teacherId: [{ required: true, message: '老师ID不能为空', trigger: 'blur' }],
  stage: [{ required: true, message: '陪学阶段不能为空', trigger: 'change' }],
  whoLeave: [{ required: true, message: '请假方不能为空', trigger: 'change' }],
  planTime: [{ required: true, message: '原定上课时间不能为空', trigger: 'blur' }],
  newTime: [{ required: true, message: '调整后上课时间不能为空', trigger: 'blur' }],
  applyTime: [{ required: true, message: '申请时间不能为空', trigger: 'blur' }],
  applyRemark: [{ required: true, message: '申请备注不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await LeaveApplyApi.getLeaveApply(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as LeaveApplyVO
    if (formType.value === 'create') {
      await LeaveApplyApi.createLeaveApply(data)
      message.success(t('common.createSuccess'))
    } else {
      await LeaveApplyApi.updateLeaveApply(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    leaveApplyId: undefined,
    orderRecordId: undefined,
    orderId: undefined,
    customerId: undefined,
    teacherId: undefined,
    stage: undefined,
    whoLeave: undefined,
    planTime: undefined,
    newTime: undefined,
    applyTime: undefined,
    applyRemark: undefined,
    auditStatus: undefined,
    auditTime: undefined,
    auditUserId: undefined,
    auditRemark: undefined
  }
  formRef.value?.resetFields()
}
</script>
