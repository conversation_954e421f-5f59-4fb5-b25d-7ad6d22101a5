<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="90px"
    >
      <el-form-item label="请假申请ID" prop="leaveApplyId">
        <el-input
          v-model="queryParams.leaveApplyId"
          placeholder="请输入请假申请ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="课时记录ID" prop="orderRecordId">
        <el-input
          v-model="queryParams.orderRecordId"
          placeholder="请输入课时记录ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="订单ID" prop="orderId">
        <el-input
          v-model="queryParams.orderId"
          placeholder="请输入订单ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="家长ID" prop="customerId">
        <el-input
          v-model="queryParams.customerId"
          placeholder="请输入家长ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="老师ID" prop="teacherId">
        <el-input
          v-model="queryParams.teacherId"
          placeholder="请输入老师ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="陪学阶段" prop="stage">
        <el-select
          v-model="queryParams.stage"
          placeholder="请选择陪学阶段"
          clearable
          class="!w-200px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_LEAVE_STAGE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="请假方" prop="whoLeave">
        <el-select
          v-model="queryParams.whoLeave"
          placeholder="请选择请假方"
          clearable
          class="!w-200px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_WHO_LEAVE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="原定上课时间" prop="planTime" label-width="100px">
        <el-date-picker
          v-model="queryParams.planTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="调整后时间" prop="newTime">
        <el-date-picker
          v-model="queryParams.newTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="申请时间" prop="applyTime">
        <el-date-picker
          v-model="queryParams.applyTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="申请原因" prop="applyRemark">
        <el-input
          v-model="queryParams.applyRemark"
          placeholder="请输入申请原因"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>

      <el-form-item label="审核状态" prop="auditStatus">
        <el-select
          v-model="queryParams.auditStatus"
          placeholder="请选择审核状态"
          clearable
          class="!w-200px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_AUDIT_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审核时间" prop="auditTime">
        <el-date-picker
          v-model="queryParams.auditTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="审核人" prop="auditUserId">
        <el-input
          v-model="queryParams.auditUserId"
          placeholder="请输入审核人"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="审核备注" prop="auditRemark">
        <el-input
          v-model="queryParams.auditRemark"
          placeholder="请输入审核备注"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['als:leave-apply:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['als:leave-apply:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" border size="small">
      <el-table-column label="请假申请ID" align="center" prop="leaveApplyId" />
      <el-table-column label="课时记录ID" align="center" prop="orderRecordId" />
      <el-table-column label="订单ID" align="center" prop="orderId" />
      <el-table-column label="家长ID" align="center" prop="customerId" />
      <el-table-column label="老师ID" align="center" prop="teacherId" />
      <el-table-column label="陪学阶段" align="center" prop="stage">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ALS_LEAVE_STAGE" :value="scope.row.stage" />
        </template>
      </el-table-column>

      <el-table-column label="请假信息" align="left" prop="applyRemark"  width="300">
        <template #default="scope">
          <div>
            <div>
              <span class="right">请假方：</span>
              <dict-tag :type="DICT_TYPE.ALS_WHO_LEAVE" :value="scope.row.whoLeave" />
            </div>
            <div>
              <span>原定上课时间：</span>
              <span>{{ formatDate(scope.row.planTime) }}</span>
            </div>
            <div>
              <span>调整后时间：</span>
              <span>{{ formatDate(scope.row.newTime) }}</span>
            </div>
          </div>
        </template>
      </el-table-column>


      <el-table-column label="申请信息" align="left" prop="applyRemark"  width="300">
        <template #default="scope">
          <div>
            <span>申请时间：</span>
            <span>{{ formatDate(scope.row.applyTime) }}</span>
          </div>

          <div class="h-20 overflow-y-auto">
            <span>原因：</span>
            {{ scope.row.applyRemark }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="陪学计划审核" align="left" width="260">
        <template #default="scope">
          <div class="flex flex-justify-between">
            <div>
              <span>审核状态：</span>
              <span><dict-tag :type="DICT_TYPE.ALS_AUDIT_STATUS" :value="scope.row.auditStatus" /></span>
            </div>
            <div>
              <span class="right">审核人：</span>
              <span>{{ scope.row.auditUserId }}</span>
            </div>
          </div>
          <div>
            <span>审核时间：</span>
            <span>{{ formatDate(scope.row.auditTime) }}</span>
          </div>
          <div>

          </div>
          <div class="h-15 overflow-y-auto">
            <span>审核备注：</span>
            <span>{{ scope.row.auditRemark }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" fixed="right" width="150px">
        <template #default="scope">
          <el-button
            plain size="small"
            type="primary"
            @click="openForm('update', scope.row.leaveApplyId)"
            v-hasPermi="['als:leave-apply:update']"
          >
            编辑
          </el-button>
          <el-button
            plain size="small"
            type="danger"
            @click="handleDelete(scope.row.leaveApplyId)"
            v-hasPermi="['als:leave-apply:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <LeaveApplyForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import {dateFormatter, formatDate} from '@/utils/formatTime'
import download from '@/utils/download'
import { LeaveApplyApi, LeaveApplyVO } from '@/api/als/leaveapply'
import LeaveApplyForm from './LeaveApplyForm.vue'

/** 请假申请列表 列表 */
defineOptions({ name: 'LeaveApply' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<LeaveApplyVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  leaveApplyId: undefined,
  orderRecordId: undefined,
  orderId: undefined,
  customerId: undefined,
  teacherId: undefined,
  stage: undefined,
  whoLeave: undefined,
  planTime: [],
  newTime: [],
  applyTime: [],
  applyRemark: undefined,
  auditStatus: undefined,
  auditTime: [],
  auditUserId: undefined,
  auditRemark: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await LeaveApplyApi.getLeaveApplyPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await LeaveApplyApi.deleteLeaveApply(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await LeaveApplyApi.exportLeaveApply(queryParams)
    download.excel(data, '请假申请列表.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
