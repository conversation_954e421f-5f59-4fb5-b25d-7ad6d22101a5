<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="60%">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="130px"
      v-loading="formLoading"
      inline
    >
      <el-form-item label="课时记录ID" prop="lessonHourId">
        <el-input v-model="formData.lessonHourId" placeholder="请输入课时记录ID" class="!w-100px"/>
      </el-form-item>
      <el-form-item label="家长ID" prop="customerId">
        <el-input v-model="formData.customerId" placeholder="请输入家长ID" class="!w-100px"/>
      </el-form-item>
      <el-form-item label="老师ID" prop="teacherId">
        <el-input v-model="formData.teacherId" placeholder="请输入老师ID" class="!w-100px"/>
      </el-form-item>
      <el-form-item label="扣押课时">
        <el-input v-model="formData.classHour" placeholder="请输入扣押课时" class="!w-100px"/>
      </el-form-item>
      <el-form-item label="押金状态" >
        <el-select v-model="formData.depositStatus" class="!w-150px">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_DEPOSIT_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="押金处理方式" >
        <el-select v-model="formData.dealMethod" class="!w-150px">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_DEPOSIT_DEAL_METHOD)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="处理人" >
        <el-input v-model="formData.dealUserId" class="!w-100px" />
      </el-form-item>
      <el-form-item label="处理时间" >
        <el-date-picker
          v-model="formData.dealTime"
          type="datetime"
          value-format="x"
          placeholder="选择处理时间"
        />
      </el-form-item>
      <el-form-item label="家长获得课时" >
        <el-input v-model="formData.customerGet" class="!w-100px" />
      </el-form-item>
      <el-form-item label="老师获得课时" >
        <el-input v-model="formData.teacherGet" class="!w-100px" />
      </el-form-item>
      <el-form-item label="平台获得课时" >
        <el-input v-model="formData.platformGet" class="!w-100px" />
      </el-form-item>
      <el-form-item label="备注时间" >
        <el-date-picker
          v-model="formData.remarkTime"
          type="datetime"
          value-format="x"
          placeholder="选择备注时间"
        />
      </el-form-item>
      <el-form-item label="备注人">
        <el-input v-model="formData.remarkUserId" class="!w-100px" />
      </el-form-item>
      <el-form-item label="备注">
        <el-input v-model="formData.remark" type="textarea" class="!w-400px"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { DepositApi, DepositVO } from '@/api/als/deposit'

/** 课时押金 表单 */
defineOptions({ name: 'DepositForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  depositId: undefined,
  lessonHourId: undefined,
  customerId: undefined,
  teacherId: undefined,
  classHour: undefined,
  depositStatus: undefined,
  dealMethod: undefined,
  dealUserId: undefined,
  dealTime: undefined,
  customerGet: undefined,
  teacherGet: undefined,
  platformGet: undefined,
  remarkTime: undefined,
  remarkUserId: undefined,
  remark: undefined
})
const formRules = reactive({
  lessonHourId: [{ required: true, message: '课时记录ID不能为空', trigger: 'blur' }],
  customerId: [{ required: true, message: '家长ID不能为空', trigger: 'blur' }],
  teacherId: [{ required: true, message: '老师ID不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await DepositApi.getDeposit(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as DepositVO
    if (formType.value === 'create') {
      await DepositApi.createDeposit(data)
      message.success(t('common.createSuccess'))
    } else {
      await DepositApi.updateDeposit(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    depositId: undefined,
    lessonHourId: undefined,
    customerId: undefined,
    teacherId: undefined,
    classHour: undefined,
    depositStatus: undefined,
    dealMethod: undefined,
    dealUserId: undefined,
    dealTime: undefined,
    customerGet: undefined,
    teacherGet: undefined,
    platformGet: undefined,
    remarkTime: undefined,
    remarkUserId: undefined,
    remark: undefined
  }
  formRef.value?.resetFields()
}
</script>
