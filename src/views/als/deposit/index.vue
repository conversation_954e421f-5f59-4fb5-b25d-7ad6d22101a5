<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="85px"
    >
      <el-form-item label="课时记录ID" prop="lessonHourId">
        <el-input
          v-model="queryParams.lessonHourId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-100px"
        />
      </el-form-item>
      <el-form-item label="家长ID" prop="customerId">
        <el-input
          v-model="queryParams.customerId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-100px"
        />
      </el-form-item>
      <el-form-item label="老师ID" prop="teacherId">
        <el-input
          v-model="queryParams.teacherId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-100px"
        />
      </el-form-item>
      <el-form-item label="扣押课时" prop="classHour">
        <el-input
          v-model="queryParams.classHour"
          clearable
          @keyup.enter="handleQuery"
          class="!w-100px"
        />
      </el-form-item>
      <el-form-item label="押金状态" prop="depositStatus">
        <el-select
          v-model="queryParams.depositStatus"
          clearable
          class="!w-100px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_DEPOSIT_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="押金处理" prop="dealMethod">
        <el-select
          v-model="queryParams.dealMethod"
          clearable
          class="!w-100px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_DEPOSIT_DEAL_METHOD)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="处理人" prop="dealUserId">
        <el-input
          v-model="queryParams.dealUserId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-100px"
        />
      </el-form-item>
      <el-form-item label="处理时间" prop="dealTime">
        <el-date-picker
          v-model="queryParams.dealTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="备注时间" prop="remarkTime">
        <el-date-picker
          v-model="queryParams.remarkTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item label="备注人" prop="remarkUserId">
        <el-input
          v-model="queryParams.remarkUserId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-100px"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="queryParams.remark"
          clearable
          @keyup.enter="handleQuery"
          class="!w-100px"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['als:deposit:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['als:deposit:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true" border>
      <el-table-column label="押金id" align="center" prop="depositId" width="80px" />
      <el-table-column label="课时记录ID" align="center" prop="lessonHourId" width="100px" />
      <el-table-column label="家长ID" align="center" prop="customerId" />
      <el-table-column label="老师ID" align="center" prop="teacherId" />
      <el-table-column label="扣押课时" align="center" prop="classHour"  width="100px" />
      <el-table-column label="押金状态" align="center" prop="depositStatus"  width="100px" >
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ALS_DEPOSIT_STATUS" :value="scope.row.depositStatus" />
        </template>
      </el-table-column>
      <el-table-column label="押金处理方式" align="center" prop="dealMethod"  width="120px" >
        <template #default="scope">
          <span v-if="!scope.row.dealMethod">-</span>
          <dict-tag v-else :type="DICT_TYPE.ALS_DEPOSIT_DEAL_METHOD" :value="scope.row.dealMethod" />
        </template>
      </el-table-column>
      <el-table-column label="处理人" align="center" prop="dealUserId" />
      <el-table-column
        label="处理时间"
        align="center"
        prop="dealTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="处理方式（课时）" align="left" width="150">
        <template #default="scope">
          <div>
            <span>家长获得：</span>
            <span>{{ scope.row.customerGet }}</span> 
          </div>
          <div>
            <span>老师获得：</span>
            <span>{{ scope.row.teacherGet }}</span> 
          </div>
          <div>
            <span>平台获得：</span>
            <span>{{ scope.row.platformGet }}</span> 
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="备注" align="left" width="300">
        <template #default="scope">
          <div class="flex flex-justify-between">
            <div>
              <span>备注人：</span>
              <span>{{ scope.row.remarkUserId }}</span>
            </div>
            <div>
              <span>备注时间：</span>
              <span>{{ formatDate(scope.row.remarkTime) }}</span>
            </div>
          </div>
          <div class="h-20 overflow-y-auto">
            <span>备注内容：</span>
            <span>{{ scope.row.remark }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="120px" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.depositId)"
            v-hasPermi="['als:deposit:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.depositId)"
            v-hasPermi="['als:deposit:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <DepositForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import {dateFormatter, formatDate} from '@/utils/formatTime'
import download from '@/utils/download'
import { DepositApi, DepositVO } from '@/api/als/deposit'
import DepositForm from './DepositForm.vue'

/** 课时押金 列表 */
defineOptions({ name: 'Deposit' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<DepositVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  lessonHourId: undefined,
  customerId: undefined,
  teacherId: undefined,
  classHour: undefined,
  depositStatus: undefined,
  dealMethod: undefined,
  dealUserId: undefined,
  dealTime: [],
  customerGet: undefined,
  teacherGet: undefined,
  platformGet: undefined,
  remarkTime: [],
  remarkUserId: undefined,
  remark: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await DepositApi.getDepositPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await DepositApi.deleteDeposit(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await DepositApi.exportDeposit(queryParams)
    download.excel(data, '课时押金.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
