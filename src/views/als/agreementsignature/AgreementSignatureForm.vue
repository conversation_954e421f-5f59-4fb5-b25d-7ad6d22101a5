<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="协议ID" prop="agreementId">
        <el-input v-model="formData.agreementId" placeholder="请输入协议ID" />
      </el-form-item>
      <el-form-item label="协议唯一标识" prop="agreementKey">
        <el-input v-model="formData.agreementKey" placeholder="请输入协议唯一标识" />
      </el-form-item>
      <el-form-item label="签署时的协议版本" prop="agreementVersion">
        <el-input v-model="formData.agreementVersion" placeholder="请输入签署时的协议版本" />
      </el-form-item>
      <el-form-item label="用户ID" prop="userId">
        <el-input v-model="formData.userId" placeholder="请输入用户ID" />
      </el-form-item>
      <el-form-item label="签名图片URL" prop="signatureUrl">
        <el-input v-model="formData.signatureUrl" placeholder="请输入签名图片URL" />
      </el-form-item>
      <el-form-item label="签署IP地址" prop="ipAddress">
        <el-input v-model="formData.ipAddress" placeholder="请输入签署IP地址" />
      </el-form-item>
      <el-form-item label="用户代理信息" prop="userAgent">
        <el-input v-model="formData.userAgent" placeholder="请输入用户代理信息" />
      </el-form-item>
      <el-form-item label="签署时间" prop="signedAt">
        <el-date-picker
          v-model="formData.signedAt"
          type="date"
          value-format="x"
          placeholder="选择签署时间"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { AgreementSignatureApi, AgreementSignatureVO } from '@/api/als/agreementsignature'

/** 协议签署记录 表单 */
defineOptions({ name: 'AgreementSignatureForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  agreementSignatureId: undefined,
  agreementId: undefined,
  agreementKey: undefined,
  agreementVersion: undefined,
  userId: undefined,
  signatureUrl: undefined,
  ipAddress: undefined,
  userAgent: undefined,
  signedAt: undefined
})
const formRules = reactive({
  agreementId: [{ required: true, message: '协议ID不能为空', trigger: 'blur' }],
  agreementKey: [{ required: true, message: '协议唯一标识不能为空', trigger: 'blur' }],
  agreementVersion: [{ required: true, message: '签署时的协议版本不能为空', trigger: 'blur' }],
  userId: [{ required: true, message: '用户ID不能为空', trigger: 'blur' }],
  signatureUrl: [{ required: true, message: '签名图片URL不能为空', trigger: 'blur' }],
  signedAt: [{ required: true, message: '签署时间不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await AgreementSignatureApi.getAgreementSignature(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as AgreementSignatureVO
    if (formType.value === 'create') {
      await AgreementSignatureApi.createAgreementSignature(data)
      message.success(t('common.createSuccess'))
    } else {
      await AgreementSignatureApi.updateAgreementSignature(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    agreementSignatureId: undefined,
    agreementId: undefined,
    agreementKey: undefined,
    agreementVersion: undefined,
    userId: undefined,
    signatureUrl: undefined,
    ipAddress: undefined,
    userAgent: undefined,
    signedAt: undefined
  }
  formRef.value?.resetFields()
}
</script>