<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="85px"
    >
      <el-form-item label="老师ID" prop="teacherId">
        <el-input
          v-model="queryParams.teacherId"
          placeholder="请输入老师ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="老师姓名" prop="teacherName">
        <el-input
          v-model="queryParams.teacherName"
          placeholder="模糊搜索"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="teacherPhone">
        <el-input
          v-model="queryParams.teacherPhone"
          placeholder="模糊搜索"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="老师等级" prop="level">
        <el-select
          v-model="queryParams.level"
          placeholder="请选择老师等级"
          clearable
          class="!w-200px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_TEACHER_LEVEL)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="面试官" prop="interviewer">
        <el-input
          v-model="queryParams.interviewer"
          placeholder="请输入面试官"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="面试官" prop="interviewer">
        <el-input
          v-model="queryParams.interviewerName"
          placeholder="请输入面试官"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="面试时间" prop="interviewTime">
        <el-date-picker
          v-model="queryParams.interviewTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['als:teacher-interview:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['als:teacher-interview:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" border highlight-current-row size="small">
<!--      <el-table-column label="面试ID" align="center" prop="teacherInterviewId" width="70"/>-->
      <el-table-column label="基本信息" header-align="left" align="left" width="200" >
        <template #default="scope">
          <div>
            <span class="column-label">老师ID：</span>
            <span>{{ scope.row.teacherId }}</span>
          </div>
          <div>
            <span class="column-label">姓名：</span>
            <span>{{ scope.row.teacherName }}</span>
          </div>
          <div>
            <span class="column-label">手机号：</span>
            <span>{{ scope.row.teacherPhone }} </span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="面试官信息" header-align="left" align="left" width="150" >
        <template #default="scope">
          <div>
            <span class="column-label">面试官ID：</span>
            <span>{{ scope.row.interviewer }}</span>
          </div>
          <div>
            <span class="column-label">姓名：</span>
            <span>{{ scope.row.interviewerName }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="面试结果" align="center" prop="level" width="100">
        <template #default="scope">
          <dict-tag v-if="scope.row.level > 0" :type="DICT_TYPE.ALS_TEACHER_LEVEL" :value="scope.row.level" />
          <span v-if="scope.row.level == 0">未面试</span>
        </template>
      </el-table-column>

      <el-table-column label="面试官评价" header-align="left" align="left" >
        <template #default="scope">
          <div>
            <span class="column-label">面试时间：</span>
            <span>{{ formatDate(scope.row.interviewTime) }}</span>
          </div>
          <div class="h-15 overflow-y-auto">
            <span class="column-label">评价：</span>
            <span>{{ scope.row.interviewerEvaluate }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="师资备注" header-align="left" align="left" width="300" >
        <template #default="scope">
          <div class="h-15 overflow-y-auto">
            <span>{{ scope.row.teacherRemark }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="200px"
      />
      <el-table-column label="操作" align="center" width="200" fixed="right">
        <template #default="scope">
          <el-button
            plain size="small"
            type="primary"
            @click="openForm('update', scope.row.teacherId)"
            v-hasPermi="['als:teacher-interview:update']"
          >
            编辑
          </el-button>
          <el-button
            plain size="small"
            type="primary"
            @click="openForm2('update', scope.row.teacherId)"
            v-hasPermi="['als:teacher-interview:update']"
          >
            面试评价
          </el-button>
          <el-button
            plain size="small"
            type="danger"
            @click="handleDelete(scope.row.teacherId)"
            v-hasPermi="['als:teacher-interview:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <TeacherInterviewForm ref="formRef" @success="getList" />

  <TeacherInterviewEditForm ref="formRef1" @success="getList" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import {dateFormatter, formatDate} from '@/utils/formatTime'
import download from '@/utils/download'
import { TeacherInterviewApi, TeacherInterviewVO } from '@/api/als/teacherinterview'
import TeacherInterviewForm from './TeacherInterviewForm.vue'
import TeacherInterviewEditForm from "./TeacherInterviewEditForm.vue";

/** 老师面试 列表 */
defineOptions({ name: 'TeacherInterview' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<TeacherInterviewVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  teacherId: undefined,
  teacherName: undefined,
  teacherPhone: undefined,
  level: undefined,
  interviewer: undefined,
  interviewerName: undefined,
  interviewerEvaluate: undefined,
  interviewTime: [],
  teacherRemark: undefined,
  qualityBasic: undefined,
  qualityComprehensive: undefined,
  qualityLecture: undefined,
  finallyScore: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TeacherInterviewApi.getTeacherInterviewPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

const formRef1 = ref()
const openForm2 = (type: string, id?: number) => {
  formRef1.value.open(type, id)
}
/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TeacherInterviewApi.deleteTeacherInterview(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TeacherInterviewApi.exportTeacherInterview(queryParams)
    download.excel(data, '老师面试.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
