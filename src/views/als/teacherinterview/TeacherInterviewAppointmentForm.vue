<template>
  <Dialog title="预约面试" v-model="dialogVisible" width="600">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="110px"
      v-loading="formLoading"
    >
      <el-form-item label="老师ID" prop="teacherId">
        <el-input v-model="formData.teacherId" placeholder="请输入老师ID" disabled />
      </el-form-item>
      <el-form-item label="面试官" prop="interviewer">
        <el-select
v-model="formData.interviewer" 
                  clearable
                  filterable
                  placeholder="请选择面试官">
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="预约时间" prop="interviewTime">
        <el-date-picker
          v-model="formData.interviewTime"
          type="datetime"
          value-format="x"
          placeholder="选择面试时间"
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark" style="width: 100%">
        <el-input type="textarea" show-word-limit :maxlength="500" rows="5" v-model="formData.remark" placeholder="请输入备注信息" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { TeacherInterviewApi, TeacherInterviewVO } from '@/api/als/teacherinterview'
import * as UserApi from '@/api/system/user'

/** 老师面试预约 表单 */
defineOptions({ name: 'TeacherInterviewAppointmentForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const userList = ref<UserApi.UserVO[]>([]) // 用户列表
const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  teacherId: undefined as number | undefined,
  interviewer: undefined as number | undefined,
  interviewTime: undefined as number | undefined,
  remark: undefined as string | undefined
})
const formRules = reactive({
  interviewer: [{ required: true, message: '面试官不能为空', trigger: 'change' }],
  interviewTime: [{ required: true, message: '预约时间不能为空', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  formType.value = type
  resetForm()
  
  if (id) {
    formData.value.teacherId = id
    
    // 通过 API 获取已有的面试数据
    formLoading.value = true
    try {
      const data = await TeacherInterviewApi.getTeacherInterview(id)
      // 如果已有数据，填充到表单中
      if (data) {
        formData.value.interviewer = data.interviewer
        formData.value.interviewTime = data.interviewTime ? new Date(data.interviewTime).getTime() : undefined
        formData.value.remark = data.teacherRemark
      }
    } catch (error) {
      console.error('获取数据失败', error)
    } finally {
      formLoading.value = false
    }
  }
  
  // 获取用户列表
  userList.value = await UserApi.getSimpleUserList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data: Partial<TeacherInterviewVO> = {
      teacherId: formData.value.teacherId,
      interviewer: formData.value.interviewer,
      interviewTime: formData.value.interviewTime ? new Date(formData.value.interviewTime) : undefined,
      teacherRemark: formData.value.remark
    }
    await TeacherInterviewApi.reservationInterview(data as TeacherInterviewVO)
    message.success('预约面试成功')
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    teacherId: undefined,
    interviewer: undefined,
    interviewTime: undefined,
    remark: undefined
  }
  formRef.value?.resetFields()
}
</script> 