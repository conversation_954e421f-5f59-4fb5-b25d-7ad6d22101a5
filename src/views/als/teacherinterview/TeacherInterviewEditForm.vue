<template>
  <Dialog title="面试结果" v-model="dialogVisible" width="900">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="110px"
      inline
      v-loading="formLoading"
    >
      <el-form-item label="面试结果" prop="level">
        <el-radio-group v-model="formData.level" placeholder="请选择老师等级" is-button>
          <el-radio-button
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_TEACHER_LEVEL)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-radio-group>
      </el-form-item>

      <el-form-item label="面试官评价" prop="interviewerEvaluate" style="width: 100%">
        <el-input type="textarea" show-word-limit :maxlength="500" rows="5" v-model="formData.interviewerEvaluate" placeholder="请输入面试官评价" />
      </el-form-item>
      <el-form-item label="综合评分" prop="finallyScore">
        <el-input-number controls-position="right" v-model="formData.finallyScore" :min="1" :max="10" />
        <span class="color-red">（满分10分）</span>
      </el-form-item>

      <el-form-item label="面试时间" prop="interviewTime">
        <el-date-picker
          v-model="formData.interviewTime"
          type="datetime"
          value-format="x"
          placeholder="选择面试时间"
        />
      </el-form-item>
      <el-form-item label="" prop="qualityBasic" style="align-items: center;" label-width="0">
        <el-table :data="getStrDictOptions(DICT_TYPE.ALS_QUALITY_BASIC)" width="100%" border stripe>
          <el-table-column prop="label" label="基本素质" width="510" align="center" />
          <el-table-column label="打分"  width="330" align="center">
            <template #default="scope">
              <el-radio-group v-model="formData.qualityBasic[scope.$index]">
                <el-radio border :label="1">优秀</el-radio>
                <el-radio border :label="2">良好</el-radio>
                <el-radio border :label="3">差</el-radio>
              </el-radio-group>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item label="" prop="qualityComprehensive" style="align-items: center;" label-width="0" >
        <el-table :data="getStrDictOptions(DICT_TYPE.ALS_QUALITY_COMPREHENSIVE)" width="100%" border stripe>
          <el-table-column prop="label" label="综合素质" width="510" align="center" />
          <el-table-column label="打分" width="330"  align="center">
            <template #default="scope">
              <el-radio-group v-model="formData.qualityComprehensive[scope.$index]">
                <el-radio border :label="1">优秀</el-radio>
                <el-radio border :label="2">良好</el-radio>
                <el-radio border :label="3">差</el-radio>
              </el-radio-group>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item label="" prop="qualityLecture" style="align-items: center;" label-width="0" >
        <el-table :data="getStrDictOptions(DICT_TYPE.ALS_QUALITY_LECTURE)" width="100%" border stripe>
          <el-table-column prop="label" label="试讲" width="510" align="center" />
          <el-table-column label="打分"  width="330"  align="center">
            <template #default="scope">
              <el-radio-group v-model="formData.qualityLecture[scope.$index]">
                <el-radio border :label="1">优秀</el-radio>
                <el-radio border :label="2">良好</el-radio>
                <el-radio border :label="3">差</el-radio>
              </el-radio-group>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>

<!--      <el-form-item label="师资备注" prop="teacherRemark" style="width: 100%;">-->
<!--        <el-input type="textarea" show-word-limit :maxlength="500" rows="4" v-model="formData.teacherRemark" placeholder="请输入师资备注" />-->
<!--      </el-form-item>-->
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { TeacherInterviewApi, TeacherInterviewVO } from '@/api/als/teacherinterview'

/** 老师面试 表单 */
defineOptions({ name: 'TeacherInterviewEditForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  teacherInterviewId: undefined,
  teacherId: undefined,
  level: undefined,
  interviewer: undefined,
  interviewerEvaluate: undefined,
  interviewTime: undefined,
  teacherRemark: undefined,
  qualityBasic: [],
  qualityComprehensive: [],
  qualityLecture: [],
  finallyScore: undefined
})
const formRules = reactive({
  teacherId: [{ required: true, message: '老师ID不能为空', trigger: 'blur' }],
  level: [{ required: true, message: '老师等级不能为空', trigger: 'change' }],
  interviewer: [{ required: true, message: '面试官不能为空', trigger: 'blur' }],
  interviewerEvaluate: [{ required: true, message: '面试官评价不能为空', trigger: 'blur' }],
  teacherRemark: [{ required: true, message: '师资备注不能为空', trigger: 'blur' }],
  qualityBasic: [{ required: true, message: '基本素质不能为空', trigger: 'blur' }],
  qualityComprehensive: [{ required: true, message: '综合素质评分不能为空', trigger: 'blur' }],
  qualityLecture: [{ required: true, message: '试讲评分不能为空', trigger: 'blur' }],
  finallyScore: [{ required: true, message: '综合评分不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await TeacherInterviewApi.getTeacherInterview(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TeacherInterviewVO
    if (formType.value === 'create') {
      await TeacherInterviewApi.createTeacherInterview(data)
      message.success(t('common.createSuccess'))
    } else {
      await TeacherInterviewApi.updateTeacherInterview(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    teacherInterviewId: undefined,
    teacherId: undefined,
    level: undefined,
    interviewer: undefined,
    interviewerEvaluate: undefined,
    interviewTime: undefined,
    teacherRemark: undefined,
    qualityBasic: [],
    qualityComprehensive: [],
    qualityLecture: [],
    finallyScore: undefined
  }
  formRef.value?.resetFields()
}
</script>

<style scoped lang="scss">

:deep(.el-input-number.is-controls-right .el-input__inner){
  padding-left: 20px !important;
  padding-right: 22px !important;
}
</style>
