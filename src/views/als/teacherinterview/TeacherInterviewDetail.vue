<template>
  <Dialog title="面试结果" v-model="dialogVisible" width="1100px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="110px"
      inline
      v-loading="formLoading"
    >
      <el-form-item label="老师ID" prop="teacherId">
        <span>{{ formData.teacherId }}</span>
      </el-form-item>
      <el-form-item label="面试官" prop="interviewer">
        <span>{{ formData.interviewer }}</span>
      </el-form-item>
      <el-form-item label="面试时间" prop="interviewer">
        <span>{{ formatDate(formData.interviewTime) }}</span>
      </el-form-item>
      <el-form-item label="面试结果" prop="level">
        <dict-tag v-if="formData.level > 0" :type="DICT_TYPE.ALS_TEACHER_LEVEL" :value="formData.level" />
        <span v-else>未面试</span>
      </el-form-item>
      <el-form-item label="综合评分" prop="finallyScore">
        <span>{{ formData.finallyScore }} 分（满分10分）</span>
      </el-form-item>

      <el-form-item label="面试官评价" prop="interviewerEvaluate" style="width: 100%" >
        <span>{{ formData.interviewerEvaluate }}</span>
      </el-form-item>
      <el-form-item label="师资备注" prop="interviewerEvaluate" style="width: 100%" >
        <span>{{ formData.teacherRemark }}</span>
      </el-form-item>

      <div class="flex flex-items-start">
      <el-form-item style="align-items: center;" label-width="0">
        <el-table :data="getStrDictOptions(DICT_TYPE.ALS_QUALITY_BASIC)" width="100%" border stripe>
          <el-table-column prop="label" label="基本素质" width="210" align="center" label-class-name="table-label"/>
          <el-table-column label="评分" width="100" align="center">
            <template #default="scope">
              <span>{{ getScoreLabel(formData.qualityBasic[scope.$index]) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>

      <el-form-item label="" style="align-items: center;" label-width="0">
        <el-table :data="getStrDictOptions(DICT_TYPE.ALS_QUALITY_COMPREHENSIVE)" width="100%" border stripe>
          <el-table-column prop="label" label="综合素质" width="210" align="center" />
          <el-table-column label="评分" width="100" align="center">
            <template #default="scope">
              <span>{{ getScoreLabel(formData.qualityComprehensive[scope.$index]) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>

      <el-form-item label="" style="align-items: center;" label-width="0">
        <el-table :data="getStrDictOptions(DICT_TYPE.ALS_QUALITY_LECTURE)" width="100%" border stripe>
          <el-table-column prop="label" label="试讲" width="210" align="center" />
          <el-table-column label="评分" width="100" align="center">
            <template #default="scope">
              <span>{{ getScoreLabel(formData.qualityLecture[scope.$index]) }}</span>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      </div>
    </el-form>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { TeacherInterviewApi, TeacherInterviewVO } from '@/api/als/teacherinterview'
import { formatDate} from '@/utils/formatTime'

/** 老师面试 表单 */
defineOptions({ name: 'TeacherInterviewDetail' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  teacherInterviewId: undefined,
  teacherId: undefined,
  level: undefined,
  interviewer: undefined,
  interviewerEvaluate: undefined,
  interviewTime: undefined,
  teacherRemark: undefined,
  qualityBasic: [],
  qualityComprehensive: [],
  qualityLecture: [],
  finallyScore: undefined
})
const formRules = reactive({
  teacherId: [{ required: true, message: '老师ID不能为空', trigger: 'blur' }],
  level: [{ required: true, message: '老师等级不能为空', trigger: 'change' }],
  interviewer: [{ required: true, message: '面试官不能为空', trigger: 'blur' }],
  interviewerEvaluate: [{ required: true, message: '面试官评价不能为空', trigger: 'blur' }],
  teacherRemark: [{ required: true, message: '师资备注不能为空', trigger: 'blur' }],
  qualityBasic: [{ required: true, message: '基本素质不能为空', trigger: 'blur' }],
  qualityComprehensive: [{ required: true, message: '综合素质评分不能为空', trigger: 'blur' }],
  qualityLecture: [{ required: true, message: '试讲评分不能为空', trigger: 'blur' }],
  finallyScore: [{ required: true, message: '综合评分不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async ( id?: number) => {
  dialogVisible.value = true
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await TeacherInterviewApi.getTeacherInterview(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

// 获取等级标签
const getLevelLabel = (level) => {
  const levelDict = getIntDictOptions(DICT_TYPE.ALS_TEACHER_LEVEL)
  const found = levelDict.find(item => item.value === level)
  return found ? found.label : '未面试'
}
const getScoreLabel = (score) => {
  const scoreDict = {
    1: '优秀',
    2: '良好',
    3: '差'
  }
  return scoreDict[score] || '未评分'
}
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TeacherInterviewVO
    if (formType.value === 'create') {
      await TeacherInterviewApi.createTeacherInterview(data)
      message.success(t('common.createSuccess'))
    } else {
      await TeacherInterviewApi.updateTeacherInterview(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    teacherInterviewId: undefined,
    teacherId: undefined,
    level: undefined,
    interviewer: undefined,
    interviewerEvaluate: undefined,
    interviewTime: undefined,
    teacherRemark: undefined,
    qualityBasic: [],
    qualityComprehensive: [],
    qualityLecture: [],
    finallyScore: undefined
  }
  formRef.value?.resetFields()
}
</script>

<style scoped lang="scss">
.table-label {
  font-weight: bold;
}
:deep(.el-table thead tr:first-child th) {
  background-color: #ece9e9; /* 设置背景色为灰色 */
  color: #7c7a7a; /* 可选：设置文字颜色 */
  font-weight: bold;
}
:deep(.el-table__row){
  font-size: 12px;
}
:deep(.el-form-item--default){
  font-size: 12px;
}
</style>
