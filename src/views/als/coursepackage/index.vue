<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="85px"
    >
      <el-form-item label="课时包ID" prop="coursePackageId">
        <el-input
          v-model="queryParams.coursePackageId"
          placeholder="请输入课时包ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="课时包名称" prop="packageName">
        <el-input
          v-model="queryParams.packageName"
          placeholder="请输入课时包名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="课时包类型" prop="packageType">
        <el-select
          v-model="queryParams.packageType"
          placeholder="请选择课时包类型"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_PACKAGE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="总课时数" prop="lessonPeriod">
        <el-input
          v-model="queryParams.lessonPeriod"
          placeholder="请输入总课时数"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="售价" prop="salePrice">
        <el-input
          v-model="queryParams.salePrice"
          placeholder="请输入售价"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="是否启用" prop="isEnable">
        <el-input
          v-model="queryParams.isEnable"
          placeholder="请输入是否启用"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['als:course-package:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['als:course-package:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" border size="small">
      <el-table-column label="课时包ID" align="center" prop="coursePackageId" width="100"/>
      <el-table-column label="课时包名称" align="center" prop="packageName" width="250"/>
      <el-table-column label="课时包类型" align="center" prop="packageType" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ALS_PACKAGE_TYPE" :value="scope.row.packageType" />
        </template>
      </el-table-column>
      <el-table-column label="总课时数" align="center" prop="lessonPeriod" width="100" />
      <el-table-column label="售价" align="center" prop="salePrice" width="100">
        <template #default="scope">
          {{ scope.row.salePrice }}元
        </template>
      </el-table-column>
      <el-table-column label="单价" align="center">
        <template #default="scope">
          {{ (scope.row.salePrice/scope.row.lessonPeriod).toFixed(2) }}元/课时
        </template>
      </el-table-column>
      <el-table-column label="是否启用" align="center" prop="isEnable" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ALS_YES_OR_ON" :value="scope.row.isEnable" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.coursePackageId)"
            v-hasPermi="['als:course-package:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.coursePackageId)"
            v-hasPermi="['als:course-package:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <CoursePackageForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import download from '@/utils/download'
import { CoursePackageApi, CoursePackageVO } from '@/api/als/coursepackage'
import CoursePackageForm from './CoursePackageForm.vue'

/** 课时包 列表 */
defineOptions({ name: 'CoursePackage' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<CoursePackageVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  coursePackageId: undefined,
  packageName: undefined,
  packageType: undefined,
  lessonPeriod: undefined,
  salePrice: undefined,
  isEnable: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await CoursePackageApi.getCoursePackagePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await CoursePackageApi.deleteCoursePackage(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await CoursePackageApi.exportCoursePackage(queryParams)
    download.excel(data, '课时包.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
