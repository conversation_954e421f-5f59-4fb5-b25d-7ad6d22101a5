<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="课时包名称" prop="packageName">
        <el-input v-model="formData.packageName" placeholder="请输入课时包名称" />
      </el-form-item>
      <el-form-item label="课时包类型" prop="packageType">
        <el-select v-model="formData.packageType" placeholder="请选择课时包类型">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_PACKAGE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="总课时数" prop="lessonPeriod">
        <el-input v-model="formData.lessonPeriod" placeholder="请输入总课时数" />
      </el-form-item>
      <el-form-item label="售价" prop="salePrice">
        <el-input v-model="formData.salePrice" placeholder="请输入售价" />
      </el-form-item>
      <el-form-item label="是否启用" prop="isEnable">
        <template #default>
          <el-switch
            v-model="formData.isEnable"
            inline-prompt
            active-text="是"
            inactive-text="否"
            :active-value="1"
            :inactive-value="0"
          />
        </template>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { CoursePackageApi, CoursePackageVO } from '@/api/als/coursepackage'

/** 课时包 表单 */
defineOptions({ name: 'CoursePackageForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  coursePackageId: undefined,
  packageName: undefined,
  packageType: undefined,
  lessonPeriod: undefined,
  salePrice: undefined,
  isEnable: undefined
})
const formRules = reactive({
  packageName: [{ required: true, message: '课时包名称不能为空', trigger: 'blur' }],
  packageType: [{ required: true, message: '课时包类型不能为空', trigger: 'change' }],
  lessonPeriod: [{ required: true, message: '总课时数不能为空', trigger: 'blur' }],
  salePrice: [{ required: true, message: '售价不能为空', trigger: 'blur' }],
  isEnable: [{ required: true, message: '是否启用不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await CoursePackageApi.getCoursePackage(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as CoursePackageVO
    if (formType.value === 'create') {
      await CoursePackageApi.createCoursePackage(data)
      message.success(t('common.createSuccess'))
    } else {
      await CoursePackageApi.updateCoursePackage(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    coursePackageId: undefined,
    packageName: undefined,
    packageType: undefined,
    lessonPeriod: undefined,
    salePrice: undefined,
    isEnable: undefined
  }
  formRef.value?.resetFields()
}
</script>
