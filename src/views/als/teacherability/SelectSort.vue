<script setup lang="ts">
defineOptions({ name: 'SelectSort' })

interface Props {
  teachScopeRank: []; // 输入参数
}

const props = defineProps<Props>()

// 将 props.teachScopeRank 直接作为响应式
// 注意：此处不生效通过下面watch 监听弥补
const teachScopeRank = ref<string[]>([...props.teachScopeRank]); // 直接复制 props.teachScopeRank

// 监视 props.teachScopeRank 的变化
watch(
  () => props.teachScopeRank,
  (newVal) => {
    teachScopeRank.value = [...newVal]; // 更新本地变量
  }
);
const emit = defineEmits('[updateRank]');
// 科目选项
const subjects = [
  { label: '语文', value: '语文' },
  { label: '数学', value: '数学' },
  { label: '英语', value: '英语' },
];

// 切换科目选择
const toggleSubject = (value: string) => {
  const index = teachScopeRank.value.indexOf(value);
  if (index === -1) {
    teachScopeRank.value.push(value);
  } else {
    teachScopeRank.value.splice(index, 1);
  }
  emit('updateRank', teachScopeRank.value); // 发送更新事件
};

// 检查科目是否被选中
const isSelected = (value: string) => {
  return props.teachScopeRank.includes(value);
};

</script>

<template>
  <div>
    <div>
      <el-button
        v-for="subject in subjects"
        :key="subject.value"
        @click="toggleSubject(subject.value)"
        :type="isSelected(subject.value) ? 'primary' : 'default'"
      >
        {{ subject.label }}
      </el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
/* 添加一些样式 */
ul {
  display: flex;
  list-style-type: none;
  padding: 0;
}
li {
  margin: 5px 10px;
}
</style>
