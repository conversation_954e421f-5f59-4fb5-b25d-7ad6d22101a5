<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="1200px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="80px"
      v-loading="formLoading"
      inline
    >
      <div>
      <el-form-item label="外语" prop="foreignLanguage" class="!w-800px border">
        <div v-for="(item,index) in formData.foreignLanguage" :key="index" class="flex mb-1 !w-400px">
          <el-select v-model="formData.foreignLanguage[index]">
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_FOREIGN_LANGUAGE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          <el-radio-group v-model="formData.foreignLanguageSpoken[index]" is-button class="!w-500px ml-3">
            <el-radio-button
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_TEACHER_ABILITY_SPOKEN)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-radio-group>
          <div>
            <el-button
              type="danger"
              circle
              size="small"
              @click="() => removeNews(index)"
            >
              <Icon icon="ep:delete" />
            </el-button>
          </div>

        </div>
        <div class="flex !w-400px flex-justify-center ope-row">
          <el-row justify="center" >
            <el-button
              type="primary"
              @click="plusNews"
            > 点击添加
              <Icon icon="ep:plus" />
            </el-button>
          </el-row>
        </div>
      </el-form-item>
      </div>
      <el-form-item label="外语证明" prop="foreignCertificate" class="!w-400px">
        <el-input v-model="formData.foreignCertificate" placeholder="请输入外语证明" />
      </el-form-item>
      <el-form-item label="四级分数" prop="gradeFour" class="!w-180px" >
        <el-input v-model="formData.gradeFour" />
      </el-form-item>
      <el-form-item label="六级分数" prop="gradeSix" class="!w-180px">
        <el-input v-model="formData.gradeSix" />
      </el-form-item>
      <el-form-item label="高考英语" prop="englishScore" class="!w-180px">
        <el-input v-model="formData.englishScore"  />
      </el-form-item>
      <el-form-item label="雅思" prop="ieltsScore" class="!w-180px">
        <el-input v-model="formData.ieltsScore"  />
      </el-form-item>
      <el-form-item label="托福" prop="toeflScore" class="!w-180px">
        <el-input v-model="formData.toeflScore"  />
      </el-form-item>
      <div>
        <el-form-item label="钢琴等级" prop="pianoLevel" class="!w-180px">
          <el-input v-model="formData.pianoLevel" placeholder="请输入钢琴等级" />
        </el-form-item>
        <el-form-item label="钢琴证书颁证方" prop="pianoCertificateIssuer" class="!w-500px" label-width="120px">
          <el-input v-model="formData.pianoCertificateIssuer" placeholder="请输入钢琴证书颁证方" />
        </el-form-item>
      </div>

      <el-form-item label="其他技能证书及获奖情况：" prop="otherCertificate" class="!w-100%" label-width="100px">
        <el-input v-model="formData.otherCertificate" type="textarea" rows="3" maxlength="200" show-word-limit placeholder="请输入其他技能证书及获奖情况" />
      </el-form-item>

      <el-form-item label="在校获奖" prop="schoolAwards" class="!w-800px">
        <div v-for="(item,index) in formData.schoolAwards" :key="index" class="flex mb-1 !w-400px">
          <el-select v-model="formData.schoolAwards[index]">
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_SCHOOL_AWARDS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          <el-radio-group v-model="formData.schoolAwardsLevel[index]" is-button class="!w-800px ml-2">
            <el-radio-button
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_SCHOOL_AWARDS_LEVEL)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-radio-group>
          <div class="ml-2">
            <el-button
              type="danger"
              circle
              size="small"
              @click="() => removeAwards(index)"
            >
              <Icon icon="ep:delete" />
            </el-button>
          </div>

        </div>
        <div class="flex !w-400px flex-justify-center ope-row">
          <el-row justify="center" >
            <el-button
              type="primary"
              @click="plusAwards"
            > 点击添加
              <Icon icon="ep:plus" />
            </el-button>
          </el-row>
        </div>
      </el-form-item>
      <el-form-item label="补充奖项" prop="schoolAwardsExtra" class="!w-50%">
        <el-input v-model="formData.schoolAwardsExtra" placeholder="请输入补充奖项" />
      </el-form-item>
      <div>
        <el-form-item label="特长" prop="forte" class="!w-500px">
          <el-select v-model="formData.forte" placeholder="请选择特长" multiple>
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_FORTE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="其他特长" prop="forteExtra" class="!w-500px">
          <el-input v-model="formData.forteExtra" placeholder="请输入其他特长" />
        </el-form-item>
      </div>

      <div>
        <el-form-item label="作业辅导科目擅长排序" prop="teachScopeRank" label-width="200px">
          <el-input v-model="formData.teachScopeRank" class="!w-300px" />
        </el-form-item>
        <el-form-item label-width="0px" size="small">
          <SelectSort :teachScopeRank="formData.teachScopeRank" @update-rank="updateTeachScopeRank" />
          <span class="ml-10px color-red">请依次选择擅长科目</span>
        </el-form-item>
      </div>

      <el-form-item label="家教经历" prop="experience" class="!w-45%">
        <el-input v-model="formData.experience" type="textarea" rows="5" maxlength="500" show-word-limit placeholder="请输入家教经历" />
      </el-form-item>
      <el-form-item label="教学方法" prop="teachingMethod" class="!w-45%">
        <el-input v-model="formData.teachingMethod" type="textarea" rows="5" maxlength="500" show-word-limit placeholder="请输入教学方法" />
      </el-form-item>

    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { TeacherAbilityApi, TeacherAbilityVO } from '@/api/als/teacherability'
import SelectSort from "./SelectSort.vue";
import {createEmptyNewsItem} from "@/views/mp/draft/components";

/** 老师能力 表单 */
defineOptions({ name: 'TeacherAbilityForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  teacherAbilityId: undefined,
  foreignLanguage: [],
  foreignLanguageSpoken: [],
  foreignCertificate: undefined,
  gradeFour: undefined,
  gradeSix: undefined,
  englishScore: undefined,
  ieltsScore: undefined,
  toeflScore: undefined,
  pianoLevel: undefined,
  pianoCertificateIssuer: undefined,
  otherCertificate: undefined,
  schoolAwards: [],
  schoolAwardsLevel: [],
  schoolAwardsExtra: undefined,
  forte: [],
  forteExtra: undefined,
  experience: undefined,
  teachingMethod: undefined,
  teachScopeRank: []
})
const formRules = reactive({
  foreignLanguage: [{ required: true, message: '外语不能为空', trigger: 'change' }],
  foreignCertificate: [{ required: true, message: '外语证明不能为空', trigger: 'blur' }],
  // schoolAwards: [{ required: true, message: '在校获奖情况不能为空', trigger: 'change' }],
  // schoolAwardsExtra: [{ required: true, message: '补充奖项不能为空', trigger: 'blur' }],
  forte: [{ required: true, message: '特长不能为空', trigger: 'change' }],
  // forteExtra: [{ required: true, message: '其他特长不能为空', trigger: 'blur' }],
  experience: [{ required: true, message: '家教经历不能为空', trigger: 'blur' }],
  teachingMethod: [{ required: true, message: '教学方法不能为空', trigger: 'blur' }],
  teachScopeRank: [{ required: true, message: '作业辅导科目擅长排序不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await TeacherAbilityApi.getTeacherAbility(id)
    } finally {
      formLoading.value = false
    }
  }
}

const checkValid1 = async () => {
  // 校验奖项等级
  const schoolAwards = formData.value.schoolAwards.filter(item => item !== '')
  const schoolAwardsLevel = formData.value.schoolAwardsLevel.filter(item => item !== '')
  if (schoolAwards.length !== schoolAwardsLevel.length) {
    message.error('【在校获奖】填写不全，请检查')
    return false
  }
  return true
}

const checkValid2 = async () => {
  // 外语和等级
  // 去掉空值
  const foreignLanguage = formData.value.foreignLanguage.filter(item => item !== '')
  const foreignLanguageSpoken = formData.value.foreignLanguageSpoken.filter(item => item !== '')
  if (foreignLanguage.length !== foreignLanguageSpoken.length) {
    message.error('【外语】填写不全，请检查')
    return false
  }
  return true
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  let boolean1 = await checkValid1();
  if (!boolean1){
    return
  }
  let boolean2 = await checkValid2()
  if (!boolean2){
    return
  }

  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TeacherAbilityVO
    if (formType.value === 'create') {

      await TeacherAbilityApi.createTeacherAbility(data)
      message.success(t('common.createSuccess'))
    } else {
      await TeacherAbilityApi.updateTeacherAbility(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

const updateTeachScopeRank = (newTeachScopeRank) => {
  formData.value.teachScopeRank = newTeachScopeRank;
};

const plusNews = () => {
  if (formData.value.foreignLanguage.length == 0){
    formData.value.foreignLanguage = [1]
    formData.value.foreignLanguageSpoken = []
  }else {
    if (formData.value.foreignLanguage.length > 9){
      return message.error("最多只能添加10个")
    }
    formData.value.foreignLanguage.push([])
    formData.value.foreignLanguageSpoken.push([])
  }

}
const removeNews = async (index: number) => {
  if (formData.value.foreignLanguage.length == 1){
    // return message.error("至少保留一条")
  }
  formData.value.foreignLanguage.splice(index, 1)
  formData.value.foreignLanguageSpoken.splice(index, 1)
}

const plusAwards = () => {
  if (formData.value.schoolAwards == undefined){
    formData.value.schoolAwards = [1]
    formData.value.schoolAwardsLevel = [1]
  }else {
    if (formData.value.schoolAwards.length > 2){
      return message.error("最多只能添加3个")
    }
    formData.value.schoolAwards.push([1])
    formData.value.schoolAwardsLevel.push([1])
  }

}
const removeAwards = async (index: number) => {
  if (formData.value.schoolAwards.length == 1){
    // return message.error("至少保留一条")
  }
  formData.value.schoolAwards.splice(index, 1)
  formData.value.schoolAwardsLevel.splice(index, 1)
}


/** 重置表单 */
const resetForm = () => {
  formData.value = {
    teacherAbilityId: undefined,
    foreignLanguage: [],
    foreignCertificate: undefined,
    gradeFour: undefined,
    gradeSix: undefined,
    englishScore: undefined,
    ieltsScore: undefined,
    toeflScore: undefined,
    pianoLevel: undefined,
    pianoCertificateIssuer: undefined,
    otherCertificate: undefined,
    schoolAwards: [],
    schoolAwardsExtra: undefined,
    forte: [],
    forteExtra: undefined,
    experience: undefined,
    teachingMethod: undefined,
    teachScopeRank: []
  }
  formRef.value?.resetFields()
}
</script>

<style lang="scss" scoped>
.ope-row {
  padding-top: 5px;
  margin-top: 5px;
  text-align: center;
  border-top: 1px solid #eaeaea;
}
</style>
