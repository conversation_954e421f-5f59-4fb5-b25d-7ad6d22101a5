<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="60px"
    >
      <el-form-item label="外语" prop="foreignLanguage">
        <el-select
          :v-show="true"
          v-model="queryParams.foreignLanguage"
          clearable
          class="!w-130px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.ALS_FOREIGN_LANGUAGE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="外语证明" prop="foreignCertificate">
        <el-input
          v-model="queryParams.foreignCertificate"
          clearable
          @keyup.enter="handleQuery"
          class="!w-130px"
        />
      </el-form-item>
      <el-form-item label="四级分数" prop="gradeFour">
        <el-input
          v-model="queryParams.gradeFour"
          clearable
          @keyup.enter="handleQuery"
          class="!w-130px"
        />
      </el-form-item>
      <el-form-item label="六级分数" prop="gradeSix">
        <el-input
          v-model="queryParams.gradeSix"
          clearable
          @keyup.enter="handleQuery"
          class="!w-130px"
        />
      </el-form-item>
      <el-form-item label="高考英语" prop="englishScore">
        <el-input
          v-model="queryParams.englishScore"
          clearable
          @keyup.enter="handleQuery"
          class="!w-130px"
        />
      </el-form-item>
      <el-form-item label="雅思" prop="ieltsScore">
        <el-input
          v-model="queryParams.ieltsScore"
          clearable
          @keyup.enter="handleQuery"
          class="!w-130px"
        />
      </el-form-item>

      <CollapseWithButton 
        v-model:collapsed="isCollapsed"
        expand-text="展开"
        collapse-text="收起"
        expand-icon="ep:arrow-down"
        collapse-icon="ep:arrow-up"
      >
        <el-form-item label="托福" prop="toeflScore">
          <el-input
            v-model="queryParams.toeflScore"
            clearable
            @keyup.enter="handleQuery"
            class="!w-130px"
          />
        </el-form-item>
        <el-form-item label="钢琴等级" prop="pianoLevel">
          <el-input
            v-model="queryParams.pianoLevel"
            clearable
            @keyup.enter="handleQuery"
            class="!w-130px"
          />
        </el-form-item>
        <el-form-item label="钢琴证书颁证方" prop="pianoCertificateIssuer" label-width="100">
          <el-input
            v-model="queryParams.pianoCertificateIssuer"
            clearable
            @keyup.enter="handleQuery"
            class="!w-130px"
          />
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker
            v-model="queryParams.createTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-180px"
          />
        </el-form-item>
      
        <template #after-button>
          <div class="search-button-container ml-10px">
            <el-button @click="handleQuery" type="primary" plain><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
            <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
            <el-button @click="openForm('create')" v-hasPermi="['als:teacher-ability:create']">
              <Icon icon="ep:plus" class="mr-5px" /> 新增
            </el-button>
            <el-button
              type="success"
              plain
              @click="handleExport"
              :loading="exportLoading"
              v-hasPermi="['als:teacher-ability:export']"
            >
              <Icon icon="ep:download" class="mr-5px" /> 导出
            </el-button>
          </div>
        </template>
      </CollapseWithButton>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" border size="small">
      <el-table-column label="主键" align="center" prop="teacherAbilityId" />
      <el-table-column label="外语" header-align="center" align="left" prop="foreignLanguage" width="150px">
        <template #default="scope">
          <div>
            <span>外语：</span>
            <span v-for="(tag,index) in scope.row.foreignLanguage" :key="index" class="mr-1">
              <dict-tag-text :type="DICT_TYPE.ALS_FOREIGN_LANGUAGE" :value="tag" />
              <span>-</span>
              <dict-tag-text :type="DICT_TYPE.ALS_TEACHER_ABILITY_SPOKEN" :value="scope.row.foreignLanguageSpoken[index]" />
            </span>
          </div>
          <div>
            <span>证明：</span>
            <span>{{ scope.row.foreignCertificate }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="四级分数" align="center" prop="gradeFour" />
      <el-table-column label="六级分数" align="center" prop="gradeSix" />
      <el-table-column label="高考英语" align="center" prop="englishScore" />
      <el-table-column label="雅思" align="center" prop="ieltsScore" width="100px">
        <template #default="scope">
          <span>{{ scope.row.ieltsScore }}</span>
          <div style="color: red">(满分：9分)</div>
        </template>
      </el-table-column>
      <el-table-column label="托福" align="center" prop="toeflScore" width="100px">
        <template #default="scope">
          <span>{{ scope.row.toeflScore }}</span>
          <div style="color: red">(满分：120分)</div>
        </template>
      </el-table-column>
      <el-table-column label="钢琴等级" align="center" prop="pianoLevel" />
      <el-table-column label="钢琴证书颁证方" align="center" prop="pianoCertificateIssuer" width="120px"/>
      <el-table-column label="其他技能证书及获奖情况" header-align="center" align="left" prop="otherCertificate" width="180px">
        <template #default="scope">
          <div class="h-30 overflow-y-auto">
            <span>{{ scope.row.otherCertificate }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="在校获奖情况" header-align="center" align="left" prop="schoolAwards" width="150px">
        <template #default="scope">
           <span v-for="(tag,index) in scope.row.schoolAwards" :key="index" class="mr-1">
             <div class="h-0">
               <dict-tag-text :type="DICT_TYPE.ALS_SCHOOL_AWARDS" :value="scope.row.schoolAwards[index]" />
               <span>-</span>
               <dict-tag-text :type="DICT_TYPE.ALS_SCHOOL_AWARDS_LEVEL" :value="tag" />
             </div>
           </span>
          <div>
            <span>补充：</span>
            <span>{{ scope.row.schoolAwardsExtra }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="特长" header-align="center" align="left" prop="forte" width="150px">
        <template #default="scope">
          <div>
            <span v-for="tag in scope.row.forte" :key="tag" class="mr-1">
              <dict-tag :type="DICT_TYPE.ALS_FORTE" :value="tag" />
            </span>
            <div>
              <span>补充：</span>
              <span>{{ scope.row.forteExtra }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="家教经历" align="center" prop="experience" width="300px">
        <template #default="scope">
          <div class="h-30 overflow-y-auto">
            <span>{{ scope.row.experience }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="教学方法" align="center" prop="teachingMethod" width="300px">
        <template #default="scope">
          <div class="h-30 overflow-y-auto">
            <span>{{ scope.row.teachingMethod }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="作业辅导科目擅长排序" align="center" prop="teachScopeRank" width="200px" />
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" fixed="right" width="150">
        <template #default="scope">
          <el-button
            plain
            size="small"
            type="primary"
            @click="openForm('update', scope.row.teacherAbilityId)"
            v-hasPermi="['als:teacher-ability:update']"
          >
            编辑
          </el-button>
          <el-button
            plain
            size="small"
            type="danger"
            @click="handleDelete(scope.row.teacherAbilityId)"
            v-hasPermi="['als:teacher-ability:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <TeacherAbilityForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { TeacherAbilityApi, TeacherAbilityVO } from '@/api/als/teacherability'
import TeacherAbilityForm from './TeacherAbilityForm.vue'
import { CollapseTransition, CollapseWithButton } from '@/components/Collapse'

/** 老师能力 列表 */
defineOptions({ name: 'TeacherAbility' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const isCollapsed = ref(true); // 控制折叠状态
const loading = ref(true) // 列表的加载中
const list = ref<TeacherAbilityVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  foreignLanguage: undefined,
  foreignCertificate: undefined,
  gradeFour: undefined,
  gradeSix: undefined,
  englishScore: undefined,
  ieltsScore: undefined,
  toeflScore: undefined,
  pianoLevel: undefined,
  pianoCertificateIssuer: undefined,
  otherCertificate: undefined,
  schoolAwards: undefined,
  schoolAwardsExtra: undefined,
  forte: undefined,
  forteExtra: undefined,
  experience: undefined,
  teachingMethod: undefined,
  teachScopeRank: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TeacherAbilityApi.getTeacherAbilityPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TeacherAbilityApi.deleteTeacherAbility(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TeacherAbilityApi.exportTeacherAbility(queryParams)
    download.excel(data, '老师能力.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style scoped>
.search-button-container{
  display: flex;
  justify-content: center;
}
:deep(.el-card__body){
  padding: 10px !important;
}
:deep(.el-form-item__label){
  font-size: 12px;
}
:deep(.el-select__wrapper){
  font-size: 12px;
}
:deep(.el-button){
  font-size: 12px;
}
</style>
