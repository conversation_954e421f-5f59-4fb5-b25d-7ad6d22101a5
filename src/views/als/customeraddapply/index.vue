<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="家长ID" prop="customerId">
        <el-input
          v-model="queryParams.customerId"
          placeholder="请输入家长ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="课时包名称" prop="packageName">
        <el-input
          v-model="queryParams.packageName"
          placeholder="模糊搜索"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="支付方式" prop="applyMethod">
        <el-select
          v-model="queryParams.applyMethod"
          placeholder="请选择支付方式"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_PAYMENT_METHOD)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="资金流转来源" prop="directionFrom">
        <el-select
          v-model="queryParams.directionFrom"
          placeholder="请选择资金流转来源"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_DIRECTION_FROM)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="申请时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="申请人" prop="creator">
        <el-input
          v-model="queryParams.creator"
          placeholder="请输入申请人"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="审核状态" prop="auditStatus">
        <el-select
          v-model="queryParams.auditStatus"
          placeholder="请选择审核状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_AUDIT_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审核时间" prop="auditTime">
        <el-date-picker
          v-model="queryParams.auditTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="审核人" prop="auditUserId">
        <el-input
          v-model="queryParams.auditUserId"
          placeholder="请输入审核人"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['als:customer-add-apply:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['als:customer-add-apply:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true"  border highlight-current-row size="small">
      <el-table-column label="申请ID" align="center" prop="customerAddApplyId" width="70"/>
      <el-table-column label="家长信息" header-align="left" align="left" width="200" >
        <template #default="scope">
          <div>
            <span class="column-label">编号：</span>
            <span>{{ scope.row.customerId }}</span>
          </div>
          <div>
            <span class="column-label">姓名：</span>
            <span>{{ scope.row.customerName }}</span>
          </div>
          <div>
            <span class="column-label">手机：</span>
            <span>{{ scope.row.customerPhone }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="课时包名称" align="center" prop="packageName" width="120"/>
      <el-table-column label="课时包类型" align="center" prop="packageType" width="90">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ALS_PACKAGE_TYPE" :value="scope.row.packageType" />
        </template>
      </el-table-column>
      <el-table-column label="总课时数" align="center" prop="lessonPeriod" width="70"/>
      <el-table-column label="售价" align="center" prop="salePrice" width="70"/>
      <el-table-column label="优惠金额" align="center" prop="discountAmount" width="100"/>
      <el-table-column label="实付金额" align="center" prop="actualAmount" width="100"/>
      <el-table-column label="支付方式" align="center" prop="applyMethod">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ALS_PAYMENT_METHOD" :value="scope.row.applyMethod" />
        </template>
      </el-table-column>
      <el-table-column label="封存流转金额" align="center" prop="blockAmount" width="100">
        <template #default="scope">
          <span v-if="scope.row.blockAmount">
            {{ scope.row.blockAmount}}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="资金流转来源" align="center" prop="directionFrom"  width="100">
        <template #default="scope">
          <span v-if="scope.row.directionFrom">
            <dict-tag :type="DICT_TYPE.ALS_DIRECTION_FROM" :value="scope.row.directionFrom" />
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column label="申请信息" align="left" width="350">
        <template #default="scope">
          <div class="flex flex-justify-between">
            <div>
              <span>申请时间：</span>
              <span>{{ formatDate(scope.row.createTime) }}</span>
            </div>
            <div>
              <span class="right">申请人：</span>
              <span>{{ scope.row.creatorName }}</span>
            </div>
          </div>
          <div>

          </div>
          <div class="h-15 overflow-y-auto">
            <span>申请理由：</span>
            <span>{{ scope.row.applyReason }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="审核信息" align="left" width="350">
        <template #default="scope">
          <div class="flex flex-justify-between">
            <div>
              <span>审核状态：</span>
              <span v-if="scope.row.auditStatus == 0">-</span>
              <span v-else><dict-tag :type="DICT_TYPE.ALS_AUDIT_STATUS" :value="scope.row.auditStatus" /></span>
            </div>
            <div>
              <span class="right">审核人：</span>
              <span>{{ scope.row.auditUserName }}</span>
            </div>
          </div>
          <div>
            <span>审核时间：</span>
            <span>{{ formatDate(scope.row.auditTime) }}</span>
          </div>
          <div>

          </div>
          <div class="h-15 overflow-y-auto">
            <span>审核备注：</span>
            <span>{{ scope.row.auditRemark }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="200" fixed="right">
        <template #default="scope">
          <el-button
            size="small"
            type="primary"
            :disabled="scope.row.auditStatus == 1"
            @click="openForm('update', scope.row.customerAddApplyId)"
            v-hasPermi="['als:customer-add-apply:update']"
          >
            编辑
          </el-button>

          <el-tooltip class="item" effect="dark" content="已审核，无需处理" placement="top" :disabled="scope.row.auditStatus <= 1">
            <el-button
              size="small"
              type="primary"
              :disabled="scope.row.auditStatus > 1"
              @click="openForm1('audit', scope.row.customerAddApplyId)"
            >
              审核
            </el-button>
          </el-tooltip>

          <el-button
            size="small"
            type="danger"
            @click="handleDelete(scope.row.customerAddApplyId)"
            v-hasPermi="['als:customer-add-apply:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <CustomerAddApplyForm ref="formRef" @success="getList" />
<!--  审核-->
  <AuditForm ref="formRef1" @audit-call-back="auditSubmit"/>
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { formatDate} from '@/utils/formatTime'
import download from '@/utils/download'
import {CustomerAddApplyApi, CustomerAddApplyVO} from '@/api/als/customeraddapply'
import CustomerAddApplyForm from './CustomerAddApplyForm.vue'
import AuditForm from '../audit/AuditForm.vue';

/** 课时添加申请 列表 */
defineOptions({ name: 'CustomerAddApply' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<CustomerAddApplyVO[]>([]) // 列表的数据

const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  customerId: undefined,
  packageName: undefined,
  applyMethod: undefined,
  directionFrom: undefined,
  createTime: [],
  creator: undefined,
  auditStatus: undefined,
  auditTime: [],
  auditUserId: undefined
})

const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await CustomerAddApplyApi.getCustomerAddApplyPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 审核 */
const formRef1 = ref()
const openForm1 = (type: string, id?: number) => {
  formRef1.value.open(type, id)
}
const closeForm1 = async () => {
  formRef1.value.close()
}

const auditSubmit = async (result: any) => {
  try {
    await CustomerAddApplyApi.auditSubmit(result)
  }finally {
    await closeForm1()
    await getList()
  }
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await CustomerAddApplyApi.deleteCustomerAddApply(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await CustomerAddApplyApi.exportCustomerAddApply(queryParams)
    download.excel(data, '课时添加申请.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
