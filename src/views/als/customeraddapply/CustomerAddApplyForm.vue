<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="家长ID" prop="customerId">
        <el-input v-model="formData.customerId" placeholder="请输入家长ID" />
      </el-form-item>
      <el-form-item label="课时包名称" prop="packageName">
        <el-input v-model="formData.packageName" placeholder="请输入课时包ID" />
      </el-form-item>
      <el-form-item label="优惠金额" prop="discountAmount">
        <el-input v-model="formData.discountAmount" placeholder="请输入优惠金额" />
      </el-form-item>
      <el-form-item label="实付金额" prop="actualAmount">
        <el-input v-model="formData.actualAmount" placeholder="请输入实付金额" />
      </el-form-item>
      <el-form-item label="支付方式" prop="applyMethod">
        <el-select v-model="formData.applyMethod" placeholder="请选择支付方式">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_PAYMENT_METHOD)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="封存流转金额" prop="blockAmount">
        <el-input v-model="formData.blockAmount" placeholder="请输入封存流转金额" />
      </el-form-item>
      <el-form-item label="资金流转来源" prop="directionFrom">
        <el-select v-model="formData.directionFrom" placeholder="请选择资金流转来源">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_DIRECTION_FROM)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="申请理由" prop="applyReason">
        <el-input v-model="formData.applyReason" placeholder="请输入申请理由" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { CustomerAddApplyApi, CustomerAddApplyVO } from '@/api/als/customeraddapply'

/** 课时添加申请 表单 */
defineOptions({ name: 'CustomerAddApplyForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  customerAddApplyId: undefined,
  customerId: undefined,
  packageName: undefined,
  discountAmount: undefined,
  actualAmount: undefined,
  applyMethod: undefined,
  blockAmount: undefined,
  directionFrom: undefined,
  applyReason: undefined
})
const formRules = reactive({
  customerId: [{ required: true, message: '家长ID不能为空', trigger: 'blur' }],
  packageName: [{ required: true, message: '课时包名称不能为空', trigger: 'blur' }],
  discountAmount: [{ required: true, message: '优惠金额不能为空', trigger: 'blur' }],
  actualAmount: [{ required: true, message: '实付金额不能为空', trigger: 'blur' }],
  applyMethod: [{ required: true, message: '支付方式不能为空', trigger: 'change' }],
  applyReason: [{ required: true, message: '申请理由不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await CustomerAddApplyApi.getCustomerAddApply(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as CustomerAddApplyVO
    if (formType.value === 'create') {
      await CustomerAddApplyApi.createCustomerAddApply(data)
      message.success(t('common.createSuccess'))
    } else {
      await CustomerAddApplyApi.updateCustomerAddApply(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    customerAddApplyId: undefined,
    customerId: undefined,
    coursePackageId: undefined,
    discountAmount: undefined,
    actualAmount: undefined,
    applyMethod: undefined,
    blockAmount: undefined,
    directionFrom: undefined,
    applyReason: undefined
  }
  formRef.value?.resetFields()
}
</script>
