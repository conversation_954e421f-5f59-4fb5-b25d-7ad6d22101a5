<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="60%">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="80px"
      v-loading="formLoading"
      inline
    >
      <el-form-item label="购买记录ID" prop="customerPackageId" label-width="100px" class="!w-200px">
        <el-input v-model="formData.customerPackageId" />
      </el-form-item>
      <el-form-item label="家长ID" prop="customerId" class="!w-200px">
        <el-input v-model="formData.customerId" />
      </el-form-item>
      <el-form-item label="退款课时数" prop="refundClassHour" class="!w-200px" label-width="100px">
        <el-input v-model="formData.refundClassHour"/>
      </el-form-item>
      <el-form-item label="退款金额" prop="refundAmount" class="!w-200px">
        <el-input v-model="formData.refundAmount"/>
      </el-form-item>
      <el-form-item label="退款理由" prop="refundReasonType" class="!w-240px">
        <el-select v-model="formData.refundReasonType">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_REFUND_REASON_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="申请时间" prop="remarkTime">
        <el-date-picker
          v-model="formData.applyTime"
          type="datetime"
          value-format="x"
          placeholder="选择申请时间"
        />
      </el-form-item>
      <div>
        <el-form-item label="具体理由" prop="refundReason" class="!w-100%">
          <el-input v-model="formData.refundReason" type="textarea" rows="4" maxlength="500" show-word-limit/>
        </el-form-item>
      </div>
      <el-form-item label="退款状态" prop="refundStatus" class="!w-200px">
        <el-select v-model="formData.refundStatus">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_REFUND_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="处理人" prop="auditUserId" class="!w-200px">
        <el-input v-model="formData.auditUserId" />
      </el-form-item>
      <el-form-item label="退款种类" prop="refundType" class="!w-300px">
        <el-select v-model="formData.refundType">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_REFUND_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <div>
        <el-form-item label="备注" prop="remark" class="w-100%">
          <el-input v-model="formData.remark" type="textarea" rows="4" maxlength="500" show-word-limit />
        </el-form-item>
      </div>
      <el-form-item label="备注人" prop="remarkUserId" class="!w-200px">
        <el-input v-model="formData.remarkUserId"/>
      </el-form-item>
      <el-form-item label="备注时间" prop="remarkTime">
        <el-date-picker
          v-model="formData.remarkTime"
          type="datetime"
          value-format="x"
          placeholder="选择备注时间"
        />
      </el-form-item>

      <div>
        <el-form-item label="解决方案及复盘" prop="replay" class="!w-100%">
          <el-input v-model="formData.replay" type="textarea" show-word-limit maxlength="250" rows="4"/>
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { RefundApplyApi, RefundApplyVO } from '@/api/als/refundapply'

/** 家长退款申请 表单 */
defineOptions({ name: 'RefundApplyForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  refundApplyId: undefined,
  customerPackageId: undefined,
  customerId: undefined,
  refundClassHour: undefined,
  refundAmount: undefined,
  refundReasonType: undefined,
  applyTime: undefined,
  refundReason: undefined,
  refundStatus: undefined,
  auditUserId: undefined,
  refundType: undefined,
  remark: undefined,
  remarkTime: undefined,
  remarkUserId: undefined,
  replay: undefined
})
const formRules = reactive({
  customerPackageId: [{ required: true, message: '购买记录ID不能为空', trigger: 'blur' }],
  customerId: [{ required: true, message: '家长ID不能为空', trigger: 'blur' }],
  refundAmount: [{ required: true, message: '退款金额不能为空', trigger: 'blur' }],
  refundReasonType: [{ required: true, message: '退款理由选择不能为空', trigger: 'change' }],
  refundReason: [{ required: true, message: '退款理由不能为空', trigger: 'blur' }],
  refundStatus: [{ required: true, message: '退款状态不能为空', trigger: 'change' }],
  auditUserId: [{ required: true, message: '处理人不能为空', trigger: 'blur' }],
  refundType: [{ required: true, message: '退款种类不能为空', trigger: 'change' }],
  remark: [{ required: true, message: '备注不能为空', trigger: 'blur' }],
  remarkTime: [{ required: true, message: '备注时间不能为空', trigger: 'blur' }],
  remarkUserId: [{ required: true, message: '备注人不能为空', trigger: 'blur' }],
  replay: [{ required: true, message: '解决方案及复盘不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await RefundApplyApi.getRefundApply(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as RefundApplyVO
    if (formType.value === 'create') {
      await RefundApplyApi.createRefundApply(data)
      message.success(t('common.createSuccess'))
    } else {
      await RefundApplyApi.updateRefundApply(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    refundApplyId: undefined,
    customerPackageId: undefined,
    customerId: undefined,
    refundAmount: undefined,
    refundReasonType: undefined,
    refundReason: undefined,
    refundStatus: undefined,
    auditUserId: undefined,
    refundType: undefined,
    remark: undefined,
    remarkTime: undefined,
    remarkUserId: undefined,
    replay: undefined
  }
  formRef.value?.resetFields()
}
</script>
