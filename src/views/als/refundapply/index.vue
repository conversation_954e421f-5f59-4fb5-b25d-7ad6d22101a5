<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="80px"
    >
      <el-form-item label="退款ID" prop="refundApplyId">
        <el-input
          v-model="queryParams.refundApplyId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="购买记录ID" prop="customerPackageId">
        <el-input
          v-model="queryParams.customerPackageId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="家长ID" prop="customerId">
        <el-input
          v-model="queryParams.customerId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="退款金额" prop="refundAmount">
        <el-input
          v-model="queryParams.refundAmount"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="退款理由选择" prop="refundReasonType">
        <el-select
          v-model="queryParams.refundReasonType"
          clearable
          class="!w-200px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_REFUND_REASON_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="退款状态" prop="refundStatus">
        <el-select
          v-model="queryParams.refundStatus"
          clearable
          class="!w-200px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_REFUND_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="处理人" prop="auditUserId">
        <el-input
          v-model="queryParams.auditUserId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="退款种类" prop="refundType">
        <el-select
          v-model="queryParams.refundType"
          clearable
          class="!w-200px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_REFUND_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="备注时间" prop="remarkTime">
        <el-date-picker
          v-model="queryParams.remarkTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="备注人" prop="remarkUserId">
        <el-input
          v-model="queryParams.remarkUserId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item label="申请时间" prop="applyTime">
        <el-date-picker
          v-model="queryParams.applyTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-200px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['als:refund-apply:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['als:refund-apply:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" border size="small">
      <el-table-column label="退款ID" align="center" prop="refundApplyId" />
      <el-table-column label="购买记录ID" align="center" prop="customerPackageId" />
      <el-table-column label="家长ID" align="center" prop="customerId" />
      <el-table-column label="退款课时数" align="center" prop="refundClassHour" />
      <el-table-column label="退款金额" align="center" prop="refundAmount">
        <template #default="scope">
          <span>￥{{ scope.row.refundAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="退款理由" align="left" header-align="center" prop="refundReasonType" width="250px">
        <template #default="scope">
          <div>
            <span>理由选择：</span>
            <dict-tag :type="DICT_TYPE.ALS_REFUND_REASON_TYPE" :value="scope.row.refundReasonType" />
          </div>
          <div class="max-h-25 overflow-y-auto">
            <span>理由：</span>
            <span> {{ scope.row.refundReason }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="申请时间"
        align="center"
        prop="applyTime"
        :formatter="dateFormatter"
        width="150px"
      />
      <el-table-column label="等待天数" align="center" prop="waitDays" width="90">
        <template #default="scope">
          <span>{{ scope.row.waitDays }} 天</span>
        </template>
      </el-table-column>
      <el-table-column label="退款状态" align="center" prop="refundStatus">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ALS_REFUND_STATUS" :value="scope.row.refundStatus" />
        </template>
      </el-table-column>

      <el-table-column label="审核信息" align="left" width="280">
        <template #default="scope">
          <div class="flex flex-justify-between">
            <div>
              <span>审核时间：</span>
              <span>{{ formatDate(scope.row.auditTime) }}</span>
            </div>
            <div>
              <span class="right">审核人：</span>
              <span>{{ scope.row.auditUserId }}</span>
            </div>
          </div>
          <div class="max-h-20 overflow-y-auto">
            <span>审核备注：</span>
            <span>{{ scope.row.auditRemark }}</span>
          </div>
        </template>
      </el-table-column>


      <el-table-column label="处理人" align="center" prop="auditUserId" />
      <el-table-column label="退款种类" align="center" prop="refundType">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ALS_REFUND_TYPE" :value="scope.row.refundType" />
        </template>
      </el-table-column>
      <el-table-column label="运营备注信息" align="center" prop="refundReasonType" width="300px">
        <template #default="scope">
          <div class="flex flex-justify-between">
            <div>
              <span>备注时间：</span>
              <span> {{ formatDate(scope.row.remarkTime) }}</span>
            </div>
            <div>
              <span>备注人：</span>
              <span> {{ scope.row.remarkUserId }}</span>
            </div>
          </div>

          <div class="max-h-25 overflow-y-auto">
            <span>跟踪备注：</span>
            <span> {{ scope.row.remark }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="解决方案及复盘" align="center" prop="replay" width="200px">
        <template #default="scope">
          <div class="max-h-30 overflow-y-auto">
            <span> {{ scope.row.replay }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="left" fixed="right" width="200px">
        <template #default="scope">
          <el-button
            plain
            size="small"
            type="primary"
            class="op-btn"
            @click="openForm('update', scope.row.refundApplyId)"
            v-hasPermi="['als:refund-apply:update']"
          >
            编辑
          </el-button>
          <el-button
            plain
            size="small"
            type="success"
            class="op-btn"
            @click="openForm2('update', scope.row.refundApplyId)"
            v-hasPermi="['als:refund-apply:update']"
          >
            审核
          </el-button>
          <el-button
            plain
            size="small"
            type="primary"
            class="op-btn"
            @click="openForm('update', scope.row.refundApplyId)"
            v-hasPermi="['als:refund-apply:update']"
          >
            修改
          </el-button>
          <el-button
            plain
            size="small"
            type="danger"
            class="op-btn"
            @click="handleDelete(scope.row.refundApplyId)"
            v-hasPermi="['als:refund-apply:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <RefundApplyForm ref="formRef" @success="getList" />
<!--  审核-->
  <AuditForm ref="formRef2"  @audit-call-back="auditSubmit"/>
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import {dateFormatter, formatDate} from '@/utils/formatTime'
import download from '@/utils/download'
import {RefundApplyApi, RefundApplyVO} from '@/api/als/refundapply'
import RefundApplyForm from './RefundApplyForm.vue'
import AuditForm from "@/views/als/audit/AuditForm.vue";

/** 家长退款申请 列表 */
defineOptions({ name: 'RefundApply' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<RefundApplyVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  refundApplyId: undefined,
  customerPackageId: undefined,
  customerId: undefined,
  refundAmount: undefined,
  refundReasonType: undefined,
  refundReason: undefined,
  refundStatus: undefined,
  auditUserId: undefined,
  refundType: undefined,
  remark: undefined,
  remarkTime: [],
  remarkUserId: undefined,
  replay: undefined,
  applyTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await RefundApplyApi.getRefundApplyPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 退款审核 */
const formRef2 = ref()
const openForm2 = (type: string, id?: number) => {
  formRef2.value.open(type, id)
}
const closeForm2 = async () => {
  formRef2.value.close()
}
const auditSubmit = async (result: any) => {
  try {
     await RefundApplyApi.auditSubmit(result)
    message.success("审核成功")
  }finally {
    await closeForm2()
    await getList()
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await RefundApplyApi.deleteRefundApply(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}


/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await RefundApplyApi.exportRefundApply(queryParams)
    download.excel(data, '家长退款申请.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const route = useRoute()
const customerId = Number(route.params.customerId)

/** 初始化 **/
onMounted(() => {
  if (!isNaN(customerId)) {
    queryParams.customerId = customerId
  }
  getList()
})
</script>
