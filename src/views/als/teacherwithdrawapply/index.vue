<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="80px"
    >
      <el-form-item label="主键" prop="teacherWithdrawApplyId">
        <el-input
          v-model="queryParams.teacherWithdrawApplyId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-180px"
        />
      </el-form-item>
      <el-form-item label="老师ID" prop="teacherId">
        <el-input
          v-model="queryParams.teacherId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-180px"
        />
      </el-form-item>
      <el-form-item label="老师姓名" prop="teacherName">
        <el-input
          v-model="queryParams.teacherName"
          clearable
          @keyup.enter="handleQuery"
          class="!w-180px"
        />
      </el-form-item>
      <el-form-item label="到账金额" prop="toAccountAmount">
        <el-input
          v-model="queryParams.toAccountAmount"
          clearable
          @keyup.enter="handleQuery"
          class="!w-180px"
        />
      </el-form-item>
      <el-form-item label="手续费" prop="fee">
        <el-input
          v-model="queryParams.fee"
          clearable
          @keyup.enter="handleQuery"
          class="!w-180px"
        />
      </el-form-item>
      <el-form-item label="总金额" prop="totalAmount">
        <el-input
          v-model="queryParams.totalAmount"
          clearable
          @keyup.enter="handleQuery"
          class="!w-180px"
        />
      </el-form-item>
      <el-form-item label="申请时间" prop="applyTime">
        <el-date-picker
          v-model="queryParams.applyTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-180px"
        />
      </el-form-item>
      <el-form-item label="审核状态" prop="auditStatus">
        <el-select
          v-model="queryParams.auditStatus"
          clearable
          class="!w-180px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_AUDIT_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审核时间" prop="auditTime">
        <el-date-picker
          v-model="queryParams.auditTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-180px"
        />
      </el-form-item>
      <el-form-item label="审核人" prop="auditUserId">
        <el-input
          v-model="queryParams.auditUserId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-180px"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-180px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['als:teacher-withdraw-apply:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['als:teacher-withdraw-apply:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" border size="small">
      <el-table-column label="主键" align="center" prop="teacherWithdrawApplyId" />
      <el-table-column label="老师ID" align="center" prop="teacherId" />
      <el-table-column label="老师姓名" align="center" prop="teacherName" />

      <el-table-column label="申请提现金额" align="center" prop="totalAmount" width="90" >
        <template #default="scope">
          <span v-if="scope.row.totalAmount > 0">￥</span>
          <span>{{ scope.row.totalAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="手续费" align="center" prop="fee" width="80">
        <template #default="scope">
          <span v-if="scope.row.fee > 0">￥</span>
          <span>{{ scope.row.fee }}</span>
        </template>
      </el-table-column>
      <el-table-column label="到账金额" align="center" prop="toAccountAmount" width="80">
        <template #default="scope">
          <span v-if="scope.row.toAccountAmount > 0">￥</span>
          <span>{{ scope.row.toAccountAmount }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="申请时间"
        align="center"
        prop="applyTime"
        :formatter="dateFormatter"
        width="140px"
      />
      <el-table-column label="本次等待天数" align="center" prop="waitDays" width="90">
        <template #default="scope">
          <span>{{ scope.row.waitDays }} 天</span>
        </template>
      </el-table-column>
      <el-table-column label="上次等待天数" align="center" prop="lastWaitDays" width="90">
        <template #default="scope">
          <span v-if="scope.row.lastWaitDays >= 0">{{ scope.row.lastWaitDays }} 天</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center" prop="auditStatus" width="90">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ALS_AUDIT_STATUS" :value="scope.row.auditStatus" />
        </template>
      </el-table-column>
      <el-table-column label="审核信息" align="left" width="280">
        <template #default="scope">
          <div class="flex flex-justify-between">
            <div>
              <span>审核时间：</span>
              <span>{{ formatDate(scope.row.auditTime) }}</span>
            </div>
            <div>
              <span class="right">审核人：</span>
              <span>{{ scope.row.auditUserId }}</span>
            </div>
          </div>
          <div class="h-10 overflow-y-auto">
            <span>审核备注：</span>
            <span>{{ scope.row.auditRemark }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" fixed="right" width="250px">
        <template #default="scope">
          <el-button
            plain size="small"
            type="primary"
            @click="openForm('update', scope.row.teacherWithdrawApplyId)"
            v-hasPermi="['als:teacher-withdraw-apply:update']"
          >
            编辑
          </el-button>

          <el-button
            plain size="small"
            type="primary"
            @click="openForm1('update', scope.row.teacherWithdrawApplyId)"
            v-hasPermi="['als:teacher-withdraw-apply:update']"
          >
            审核
          </el-button>

          <el-button
            plain size="small"
            type="danger"
            @click="handleDelete(scope.row.teacherWithdrawApplyId)"
            v-hasPermi="['als:teacher-withdraw-apply:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <TeacherWithdrawApplyForm ref="formRef" @success="getList" />
<!--  审核-->
  <AuditForm ref="formRef1" @audit-call-back="auditSubmit"/>

</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import {dateFormatter, formatDate} from '@/utils/formatTime'
import download from '@/utils/download'
import { TeacherWithdrawApplyApi, TeacherWithdrawApplyVO } from '@/api/als/teacherwithdrawapply'
import TeacherWithdrawApplyForm from './TeacherWithdrawApplyForm.vue'
import AuditForm from "@/views/als/audit/AuditForm.vue";

/** 老师提现申请 列表 */
defineOptions({ name: 'TeacherWithdrawApply' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<TeacherWithdrawApplyVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  teacherWithdrawApplyId: undefined,
  teacherId: undefined,
  teacherName: undefined,
  toAccountAmount: undefined,
  fee: undefined,
  totalAmount: undefined,
  applyTime: [],
  auditStatus: undefined,
  auditTime: [],
  auditUserId: undefined,
  auditRemark: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TeacherWithdrawApplyApi.getTeacherWithdrawApplyPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TeacherWithdrawApplyApi.deleteTeacherWithdrawApply(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TeacherWithdrawApplyApi.exportTeacherWithdrawApply(queryParams)
    download.excel(data, '老师提现申请.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const formRef1 = ref()
const openForm1 = (type: string, id?: number) => {
  formRef1.value.open(type, id)
}
const closeForm1 = async () => {
  formRef1.value.close()
}

const auditSubmit = async (result: any) => {
  try {
    await TeacherWithdrawApplyApi.audit(result)
    message.success("审核完成")
  }finally {
    await closeForm1()
    await getList()
  }
}


/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
