<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="60%">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
      inline
    >
      <el-form-item label="老师ID" prop="teacherId">
        <el-input v-model="formData.teacherId" />
      </el-form-item>
      <el-form-item label="老师姓名" prop="teacherName">
        <el-input v-model="formData.teacherName" />
      </el-form-item>
      <el-form-item label="总金额" prop="totalAmount">
        <el-input v-model="formData.totalAmount"  />
      </el-form-item>
      <el-form-item label="手续费" prop="fee">
        <el-input v-model="formData.fee"  />
      </el-form-item>
      <el-form-item label="到账金额" prop="toAccountAmount">
        <el-input v-model="formData.toAccountAmount" />
      </el-form-item>
      <el-form-item label="申请时间" prop="applyTime">
        <el-date-picker
          v-model="formData.applyTime"
          type="datetime"
          value-format="x"
        />
      </el-form-item>
      <el-form-item label="审核状态" prop="auditStatus" class="!w-270px">
        <el-select v-model="formData.auditStatus" placeholder="请选择审核状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_AUDIT_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审核时间" prop="auditTime">
        <el-date-picker
          v-model="formData.auditTime"
          type="datetime"
          value-format="x"
          placeholder="选择审核时间"
        />
      </el-form-item>
      <el-form-item label="审核人" prop="auditUserId">
        <el-input v-model="formData.auditUserId" placeholder="请输入审核人" />
      </el-form-item>
      <el-form-item label="审核备注" prop="auditRemark" class="!w-100%">
        <el-input v-model="formData.auditRemark" type="textarea" rows="3" maxlength="50" show-word-limit />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { TeacherWithdrawApplyApi, TeacherWithdrawApplyVO } from '@/api/als/teacherwithdrawapply'

/** 老师提现申请 表单 */
defineOptions({ name: 'TeacherWithdrawApplyForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  teacherWithdrawApplyId: undefined,
  teacherId: undefined,
  teacherName: undefined,
  toAccountAmount: undefined,
  fee: undefined,
  totalAmount: undefined,
  applyTime: undefined,
  auditStatus: undefined,
  auditTime: undefined,
  auditUserId: undefined,
  auditRemark: undefined
})
const formRules = reactive({
  teacherId: [{ required: true, message: '老师ID不能为空', trigger: 'blur' }],
  teacherName: [{ required: true, message: '老师姓名不能为空', trigger: 'blur' }],
  toAccountAmount: [{ required: true, message: '到账金额不能为空', trigger: 'blur' }],
  fee: [{ required: true, message: '手续费不能为空', trigger: 'blur' }],
  totalAmount: [{ required: true, message: '总金额不能为空', trigger: 'blur' }],
  auditStatus: [{ required: true, message: '审核状态不能为空', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await TeacherWithdrawApplyApi.getTeacherWithdrawApply(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TeacherWithdrawApplyVO
    if (formType.value === 'create') {
      await TeacherWithdrawApplyApi.createTeacherWithdrawApply(data)
      message.success(t('common.createSuccess'))
    } else {
      await TeacherWithdrawApplyApi.updateTeacherWithdrawApply(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    teacherWithdrawApplyId: undefined,
    teacherId: undefined,
    teacherName: undefined,
    toAccountAmount: undefined,
    fee: undefined,
    totalAmount: undefined,
    applyTime: undefined,
    auditStatus: undefined,
    auditTime: undefined,
    auditUserId: undefined,
    auditRemark: undefined
  }
  formRef.value?.resetFields()
}
</script>
