<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="订单ID" prop="orderId">
        <el-input
          v-model="queryParams.orderId"
          placeholder="请输入订单ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_FAV_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="老师ID" prop="teacherId">
        <el-input
          v-model="queryParams.teacherId"
          placeholder="请输入老师ID"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['als:teacher-fav:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['als:teacher-fav:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true" border size="small">
      <el-table-column label="主键" align="center" prop="teacherFavId" width="100" />
      <el-table-column label="订单信息" align="left" prop="orderId" width="200">
        <template #default="scope">
          <div>
            <span class="column-label">订单ID：</span>
            <span class="column-value">{{ scope.row.orderId }}</span>
          </div>
          <div>
            <span class="column-label">订单号：</span>
            <span class="column-value link" @click="openOrderList(scope.row.orderId)">{{ scope.row.orderNo }}</span>
          </div>
          <div>
            <span class="column-label">家长：</span>
            <span class="column-value link" @click="openDetail(scope.row.customerId)">{{ scope.row.customerName }}</span>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="类型" align="center" prop="level" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ALS_FAV_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      
      <el-table-column label="老师信息" align="left" prop="teacherId" >
        <template #default="scope">
          <div>
            <span class="column-label">老师ID：</span>
            <span class="column-value" >{{ scope.row.teacherId }}</span>
            <el-button  link type="warning" size="small" class="ml-5px" @click="openTeacherResume(scope.row.teacherId)">查看简历</el-button>
          </div>
          <div>
            <span class="column-label">老师姓名：</span>
            <span class="column-value link" @click="openTeacherList(scope.row.teacherId)">{{ scope.row.teacherName }}</span>
          </div>
          <div>
            <span class="column-label">老师手机：</span>
            <span class="column-value">{{ scope.row.teacherPhone }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" width="120px" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.teacherFavId)"
            v-hasPermi="['als:teacher-fav:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.teacherFavId)"
            v-hasPermi="['als:teacher-fav:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <TeacherFavForm ref="formRef" @success="getList" />

  <!-- 老师详情弹窗 -->
  <TeacherDetail ref="formRef2" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { TeacherFavApi, TeacherFavVO } from '@/api/als/teacherfav'
import TeacherFavForm from './TeacherFavForm.vue'
import {DICT_TYPE, getIntDictOptions} from "@/utils/dict";
import TeacherDetail from "@/views/als/teacher/TeacherDetail.vue";

/** 老师收藏 列表 */
defineOptions({ name: 'TeacherFav' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<TeacherFavVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  orderId: undefined,
  type: undefined,
  teacherId: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TeacherFavApi.getTeacherFavPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 打开详情 */
const { push } = useRouter()
const openDetail = (id: number) => {
  push({ name: 'CustomerDetail', params: { id } })
}
const openOrderList = (id: number) => {
  // debugger
  push({ name: 'ExpOrder2', params: { id } })
}

/** 详情 */
const formRef2 = ref()
const openTeacherList = (id?: number) => {
  formRef2.value.open( id)
}

const openTeacherResume = (id: number) => {
  push({ name: 'TeacherResume', params: { id } })
}


/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TeacherFavApi.deleteTeacherFav(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TeacherFavApi.exportTeacherFav(queryParams)
    download.excel(data, '老师收藏.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const route = useRoute()
const orderId = Number(route.params.orderId)
const type = Number(route.params.type)

/** 初始化 **/
onMounted(() => {
  debugger
  if (orderId){
    queryParams.orderId = orderId
  }
  if (type >=0){
    queryParams.type = type
  }
  getList()
})
</script>
