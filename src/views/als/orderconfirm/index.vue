<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="85px"
    >
      <el-form-item label="体验单ID" prop="orderId">
        <el-input
          v-model="queryParams.orderId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>

      <el-form-item label="家长ID" prop="customerId">
        <el-input
          v-model="queryParams.customerId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      <el-form-item label="家长姓名" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      <el-form-item label="老师ID" prop="teacherId">
        <el-input
          v-model="queryParams.teacherId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      <el-form-item label="老师姓名" prop="teacherName">
        <el-input
          v-model="queryParams.teacherName"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      <el-form-item label="处理状态" prop="dealStatus">
        <el-select
          v-model="queryParams.dealStatus"
          clearable
          class="!w-150px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_CONFIRM_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="拒绝原因" prop="rejectReasonId">
        <el-select
          v-model="queryParams.rejectReasonId"
          clearable
          class="!w-150px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_REJECT_REASON)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="自定义原因" prop="customReason">
        <el-input
          v-model="queryParams.customReason"
          placeholder="模糊搜索"
          clearable
          @keyup.enter="handleQuery"
          class="!w-150px"
        />
      </el-form-item>
      <el-form-item label="处理人" prop="dealUserId">
        <el-select
v-model="queryParams.dealUserId"
                   clearable
                   filterable
                   class="!w-150px"
                   @keyup.enter="handleQuery">
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="处理时间" prop="dealTime">
        <el-date-picker
          v-model="queryParams.dealTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>

      <el-form-item label="抢单时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item >
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['als:order-confirm:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['als:order-confirm:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" border size="small">
      <el-table-column label="主键" align="center" prop="orderConfirmId" width="100"/>
      <el-table-column label="暂停接单" align="center" prop="isSuspend" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ALS_YES_OR_ON" :value="scope.row.isSuspend" />
        </template>
      </el-table-column>
      <el-table-column label="抢单老师信息" header-align="left" align="left" width="200" >
        <template #default="scope">
          <div>
            <span class="column-label">老师ID：</span>
            <span class="column-value">{{ scope.row.teacherId }}</span>
          </div>
          <div>
            <span class="column-label">抢单老师：</span>
            <span class="column-value link" @click="toTeacherList(scope.row.teacherId)">{{ scope.row.teacherName }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="体验单信息" header-align="left" align="left" width="200" >
        <template #default="scope">
          <div>
            <span class="column-label">体验单ID：</span>
            <span class="column-value">{{ scope.row.orderId }}</span>
          </div>
          <div>
            <span class="column-label">家长ID：</span>
            <span class="column-value">{{ scope.row.customerId }}</span>
          </div>
          <div>
            <span class="column-label">家长姓名：</span>
            <span class="column-value link" @click="openDetail(scope.row.customerId)">{{ scope.row.customerName }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="处理状态" align="center" prop="dealStatus" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.ALS_CONFIRM_STATUS" :value="scope.row.dealStatus" />
        </template>
      </el-table-column>

      <el-table-column label="处理信息" header-align="left" align="left" width="250" >
        <template #default="scope">
          <div>
            <span class="column-label">处理人：</span>
            <span class="column-value">{{ scope.row.dealUserName }}</span>
          </div>
          <div>
            <span class="column-label">处理时间：</span>
            <span class="column-value">{{ formatDate(scope.row.dealTime) }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="拒绝原因" header-align="left" align="left">
        <template #default="scope">
          <div>
             <div>
              <dict-tag v-if="scope.row.rejectReasonId > 0" :type="DICT_TYPE.ALS_REJECT_REASON" :value="scope.row.rejectReasonId" />
            </div>
            <div class="h-20 overflow-y-auto">
              <span class="column-value">{{ scope.row.customReason}}</span>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" fixed="right" width="250">
        <template #default="scope">
          <div class="flex flex-justify-start flex-wrap" >
            <el-button
              plain
              size="small"
              type="primary"
              class="op-btn"
              @click="openForm('update', scope.row.orderConfirmId)"
              v-hasPermi="['als:order-confirm:update']"
            >
              编辑
            </el-button>

            <el-button
              plain
              size="small"
              type="primary"
              class="op-btn"
              @click="confirm( scope.row.orderConfirmId)"
              :disabled="scope.row.dealStatus > 0"
              v-hasPermi="['als:order-confirm:update']"
            >
              确认接单
            </el-button>

            <el-button
              plain
              size="small"
              type="danger"
              class="op-btn"
              @click="reject(scope.row.orderConfirmId)"
              :disabled="scope.row.dealStatus > 0"
              v-hasPermi="['als:order-confirm:update']"
            >
              拒绝接单
            </el-button>
            <el-button
              plain
              size="small"
              type="primary"
              class="op-btn"
              @click="pause(scope.row.orderId)"
              v-hasPermi="['als:order-confirm:update']"
              v-if="scope.row.isSuspend == 0"
            >
              暂停接单
            </el-button>
            <el-button
              plain
              size="small"
              type="success"
              class="mb-1 !ml-1"
              @click="start(scope.row.orderId)"
              v-hasPermi="['als:order-confirm:update']"
              v-if="scope.row.isSuspend == 1"
            >
              开启接单
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <OrderConfirmForm ref="formRef" @success="getList" />
  <!--  拒绝-->
  <RejectForm ref="formRef1" @success="getList" />
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { formatDate} from '@/utils/formatTime'
import download from '@/utils/download'
import { OrderConfirmApi, OrderConfirmVO } from '@/api/als/orderconfirm'
import OrderConfirmForm from './OrderConfirmForm.vue'
import RejectForm from './RejectForm.vue'
import {OrderApi} from "@/api/als/order";
import * as UserApi from "@/api/system/user";

/** 接单确认 列表 */
defineOptions({ name: 'OrderConfirm' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<OrderConfirmVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const userList = ref<UserApi.UserVO[]>([]) // 用户列表
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  orderConfirmId: undefined,
  orderId: undefined,
  customerId: undefined,
  customerName: undefined,
  teacherId: undefined,
  teacherName: undefined,
  dealStatus: undefined,
  rejectReasonId: undefined,
  customReason: undefined,
  dealTime: [],
  dealUserId: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await OrderConfirmApi.getOrderConfirmPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
  userList.value = await UserApi.getSimpleUserList()
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 确认接单 */
const confirm = async (orderConfirmId: number) => {
  try {
    await message.confirm("是否确认接单？")
    // 发起删除
    await OrderConfirmApi.confirm(orderConfirmId)
    message.success("确认成功")
    // 刷新列表
    await getList()
  } catch {}
}

/** 拒绝接单 */
const formRef1 = ref()
const reject = (orderConfirmId?: number) => {
  formRef1.value.open(orderConfirmId)
}

/** 暂停接单 */
const pause = async (orderId: number) => {
  try {
    await message.confirm("是否暂停接单？")
    await OrderApi.pauseOrder(orderId)
    message.success("操作成功")
    // 刷新列表
    await getList()
  } catch {}
}

const start = async (orderId: number) => {
  try {
    await message.confirm("是否开启接单？")
    await OrderApi.startOrder(orderId)
    message.success("操作成功")
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await OrderConfirmApi.exportOrderConfirm(queryParams)
    download.excel(data, '接单确认.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 打开详情 */
const { push } = useRouter()
const openDetail = (id: number) => {
  push({ name: 'CustomerDetail', params: { id } })
}

const route = useRoute()
const orderId = Number(route.params.orderId)

const router = useRouter()
const toTeacherList = (teacherId: number) => {
  router.push({ name: 'Teacher', query: { teacherId: teacherId }})
}

/** 初始化 **/
onMounted(() => {
  if (orderId) {
    queryParams.orderId = orderId
  }
  getList()
})
</script>

<style scoped lang="scss">
.column-label{
  color: #939191;
}

.clickable-teacher:hover {
  color: #66b1ff;
}

.text-wrapper {
  white-space: pre-wrap;
  width: 100%;
  overflow: hidden auto;
}

:deep(.el-form-item--default){
  --font-size: 12px;
  margin-bottom: 5px;
}
:deep(.el-form--inline .el-form-item){
  margin-right:5px;
}
</style>
