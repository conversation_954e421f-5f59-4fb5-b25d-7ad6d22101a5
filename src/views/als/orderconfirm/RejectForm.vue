<template>
  <Dialog title="拒绝接单" v-model="dialogVisible" width="40%">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="拒绝原因" prop="rejectReasonId" >
        <el-radio-group v-model="formData.rejectReasonId" size="medium">
          <el-radio-button class="ml-2 mt-1"
                           v-for="dict in getIntDictOptions(DICT_TYPE.ALS_REJECT_REASON)"
                           :key="dict.value"
                           :label="dict.value">
            {{ dict.label }}
          </el-radio-button>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="自定义原因" prop="customReason" v-if="showCustomReason">
        <el-input class="ml-2"
                  type="textarea"
                  maxlength="100"
                  show-word-limit
                  rows="4"
                  v-model="formData.customReason"
                  placeholder="拒绝该老师接单原因" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import {OrderConfirmApi, RejectReqVO} from '@/api/als/orderconfirm'

/** 接单确认 表单 */
defineOptions({ name: 'RejectForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref<RejectReqVO>({
  orderConfirmId: undefined,
  dealStatus: undefined,
  rejectReasonId: undefined,
  customReason: undefined,
});
const formRules = reactive({
  dealStatus: [{ required: true, message: '处理状态不能为空', trigger: 'change' }],
  rejectReasonId: [{ required: true, message: '拒绝原因不能为空', trigger: 'change' }],
  customReason: [{ required: true, message: '请输入自定义原因推送老师', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref
const showCustomReason = computed(() => formData.value.rejectReasonId == 8) // 自定义


/** 打开弹窗 */
const open = async (orderConfirmId?: number) => {
  dialogVisible.value = true
  resetForm()
  // 修改时，设置数据
  formData.value.orderConfirmId = orderConfirmId
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as RejectReqVO
    debugger
    await OrderConfirmApi.reject(data)
    message.success(t('common.updateSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    orderConfirmId: undefined,
    dealStatus: undefined,
    rejectReasonId: undefined,
    customReason: undefined,
  }
  formRef.value?.resetFields()
}
</script>

<style scoped lang="scss">
:deep(.el-radio-button__inner){
  border-left: var(--el-border);
  border-radius: var(--el-border-radius-base) !important;
  box-shadow: none !important;
  font-weight: normal;
}
</style>
