<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-form-item label="订单ID" prop="orderId">
        <el-input v-model="formData.orderId" placeholder="请输入订单ID" />
      </el-form-item>
      <el-form-item label="家长ID" prop="customerId">
        <el-input v-model="formData.customerId" placeholder="请输入家长ID" />
      </el-form-item>
      <el-form-item label="老师ID" prop="teacherId">
        <el-input v-model="formData.teacherId" placeholder="请输入老师ID" />
      </el-form-item>
      <el-form-item label="处理状态" prop="dealStatus">
        <el-select v-model="formData.dealStatus" placeholder="请选择处理状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_CONFIRM_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="拒绝原因" prop="rejectReasonId" >
        <el-select v-model="formData.rejectReasonId" placeholder="请选择拒绝原因">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_REJECT_REASON)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="自定义拒绝原因" prop="customReason" width="100%">
        <el-input type="textarea" rows="3" v-model="formData.customReason" placeholder="请输入自定义拒绝原因" />
      </el-form-item>
      <el-form-item label="处理时间" prop="dealTime" >
        <el-date-picker
          v-model="formData.dealTime"
          type="date"
          value-format="x"
          placeholder="选择处理时间"
        />
      </el-form-item>
      <el-form-item label="处理人" prop="dealUserId" >
        <el-input v-model="formData.dealUserId" placeholder="请输入处理人" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { OrderConfirmApi, OrderConfirmVO } from '@/api/als/orderconfirm'

/** 接单确认 表单 */
defineOptions({ name: 'OrderConfirmForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  orderConfirmId: undefined,
  orderId: undefined,
  customerId: undefined,
  teacherId: undefined,
  dealStatus: undefined,
  rejectReasonId: undefined,
  customReason: undefined,
  dealTime: undefined,
  dealUserId: undefined
})
const formRules = reactive({
  orderId: [{ required: true, message: '订单ID不能为空', trigger: 'blur' }],
  customerId: [{ required: true, message: '家长ID不能为空', trigger: 'blur' }],
  teacherId: [{ required: true, message: '老师ID不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await OrderConfirmApi.getOrderConfirm(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as OrderConfirmVO
    if (formType.value === 'create') {
      await OrderConfirmApi.createOrderConfirm(data)
      message.success(t('common.createSuccess'))
    } else {
      await OrderConfirmApi.updateOrderConfirm(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    orderConfirmId: undefined,
    orderId: undefined,
    customerId: undefined,
    teacherId: undefined,
    dealStatus: undefined,
    rejectReasonId: undefined,
    customReason: undefined,
    dealTime: undefined,
    dealUserId: undefined
  }
  formRef.value?.resetFields()
}
</script>
