<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="大学名称" prop="universityName">
        <el-input v-model="formData.universityName" placeholder="请输入大学名称" />
      </el-form-item>
      <el-form-item label="标签" prop="universityTag">
        <el-input v-model="formData.universityTag" placeholder="请输入标签" />
      </el-form-item>
      <el-form-item label="校徽" prop="logo">
        <el-input v-model="formData.logo" placeholder="请输入校徽" />
      </el-form-item>
      <el-form-item label="校徽新地址" prop="logoNew">
        <el-input v-model="formData.logoNew" placeholder="请输入校徽新地址" />
      </el-form-item>
      <el-form-item label="所在省份" prop="province">
        <el-input v-model="formData.province" placeholder="请输入所在省份" />
      </el-form-item>
      <el-form-item label="城市" prop="location">
        <el-input v-model="formData.location" placeholder="请输入城市" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { UniversityApi, UniversityVO } from '@/api/als/university'

/** 大学信息 表单 */
defineOptions({ name: 'UniversityForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  universityId: undefined,
  universityName: undefined,
  universityTag: undefined,
  logo: undefined,
  logoNew: undefined,
  province: undefined,
  location: undefined
})
const formRules = reactive({
  universityName: [{ required: true, message: '大学名称不能为空', trigger: 'blur' }],
  universityTag: [{ required: true, message: '标签不能为空', trigger: 'blur' }],
  logo: [{ required: true, message: '校徽不能为空', trigger: 'blur' }],
  logoNew: [{ required: true, message: '校徽新地址不能为空', trigger: 'blur' }],
  province: [{ required: true, message: '所在省份不能为空', trigger: 'blur' }],
  location: [{ required: true, message: '城市不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await UniversityApi.getUniversity(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as UniversityVO
    if (formType.value === 'create') {
      await UniversityApi.createUniversity(data)
      message.success(t('common.createSuccess'))
    } else {
      await UniversityApi.updateUniversity(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    universityId: undefined,
    universityName: undefined,
    universityTag: undefined,
    logo: undefined,
    logoNew: undefined,
    province: undefined,
    location: undefined
  }
  formRef.value?.resetFields()
}
</script>