<script setup lang="ts">

import {DICT_TYPE, getIntDictOptions} from "@/utils/dict";

defineOptions({ name: 'PosterView' })

import { OrderApi } from '@/api/als/order'
import html2canvas from "html2canvas";
import Divider from "@/components/DiyEditor/components/mobile/Divider/index.vue";

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref({
  orderId: undefined,
  orderNo: undefined,
  orderType: undefined,
  customerId: undefined,
  customerPhone: undefined,
  orderAreaId: undefined,
  orderAddress: undefined,
  parseAddress: undefined,
  isSuspend: undefined,
  isGroupChat: undefined,
  orderRemarkExtra: undefined,
  orderRemark: undefined,
  headCurrent: undefined,
  orgNeedsContent: undefined,
  requireSex: undefined,
  hardRequireAbility: [],
  requireAbilityExtra: undefined,
  needsTags: [],
  needsFocusTags: [],
  demandContent: undefined,
  timesWeek: undefined,
  isOnWeekend: undefined,
  timeRange: [],
  trackingTime: undefined,
  expTime: undefined,
  isConfirmExpTime: undefined,
  kidSex: undefined,
  kidStage: undefined,
  communicatePre: [],
  communicateResult: undefined,
  whyNeed: undefined,
  primaryEducator: undefined,
  kidNickName: undefined,
  relationship: undefined,
  schoolName: undefined,
  schoolNature: undefined,
  ranking: undefined,
  kidChr: undefined,
  kidChrExtra: undefined,
  kidInt: undefined,
  kidIntExtra: undefined,
  inviterId: undefined,
  trackingRemarkTags:undefined
})
const copyPosterLoading = ref(false)
/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  // 修改时，设置数据
  formData.value = await OrderApi.getOrder(id)
}

const generatePoster = () => {
  copyPosterLoading.value = false
  // 获取要生成海报的DOM元素
  const element = document.getElementById("poster-container");
  html2canvas(element).then((canvas) => {
    canvas.toBlob((blob) => {
      if ('clipboard' in navigator) {
          navigator.clipboard.write([
            new ClipboardItem({'image/png': blob})
          ]).then(() => {
            message.success(t('common.copySuccess'))
            copyPosterLoading.value = false
            console.log('图片已成功复制到剪贴板');
          }).catch((error) => {
            // copyPosterLoading.value = false
            console.error('复制图片到剪贴板失败:', error);
        });
      } else {
          // 在不支持 ClipboardItem 的浏览器中，使用其他方法复制到剪贴板
          const data = [new File([blob], 'poster.png', {type: 'image/png'})];
          const clipboardData = new DataTransfer();
          clipboardData.items.add(data[0]);
          navigator.clipboard.setData('image/png', clipboardData).then(() => {
            copyPosterLoading.value = false
            console.log('图片已成功复制到剪贴板');
          }).catch((error) => {
            copyPosterLoading.value = false
            console.error('复制图片到剪贴板失败:', error);
          });
      }
    }, 'image/png');
  })
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>

<template>
  <Dialog title="订单海报" v-model="dialogVisible" width="60%" class="content" >
    <div id="poster-container" class="container">
      <div class="title">
        <div>辅导单详情</div>
      </div>
      <div class="head">
        <div class="!mt-15px head-container">
          <span class="label">订单编号：</span>
          <span class="content">{{ formData.orderNo }}</span>
        </div>
        <div class="head-container">
          <span class="label">授课地点：</span>
          <span class="content">{{ formData.parseAddress }}</span>
        </div>
        <div class="head-container">
          <span class="label">陪学性别：</span>
          <span class="content"
                v-for="dict in getIntDictOptions(DICT_TYPE.ALS_REQUIRE_SEX).filter(dict => dict.value === formData.requireSex)"
                :key=dict.value>{{ dict.label}}</span>
        </div>
        <div class="head-container">
          <span class="label">孩子信息：</span>
          <span class="content"
                v-for="dict in getIntDictOptions(DICT_TYPE.ALS_KID_STAGE).filter(dict => dict.value === formData.kidStage)"
                :key=dict.value>{{ dict.label}}</span>
          <span class="content"
                v-for="dict in getIntDictOptions(DICT_TYPE.ALS_REQUIRE_SEX).filter(dict => dict.value === formData.kidSex)"
                :key=dict.value>{{ dict.label }}</span>
        </div>
        <div class="head-container">
          <span class="label">家长要求：</span>
          <span class="content">
            {{ formData.demandContent }}
          </span>
        </div>
        <div class="head-container">
          <span class="label">侧重点：</span>
          <span class="content"
                v-for="dict in getIntDictOptions(DICT_TYPE.ALS_NEEDS_FOCUS_TAGS).filter(dict => formData.needsFocusTags.includes(dict.value))"
                :key=dict.value>{{ dict.label }}</span>
        </div>
      </div>

      <div class="foot">
        <div class="foot-left">
          <div class="foot-left-up">扫描二维码</div>
          <div class="foot-left-down">接单</div>
        </div>
        <div class="foot-right">
          <img src="@/assets/imgs/qrcode.png" alt="" class="qr-code"/>
        </div>
        <div class="foot-desc">扫描进去转发给其他同学体验成功获得20元奖励</div>
      </div>

      <div class="bg">
        <img src="@/assets/imgs/poster.png" alt="" class="bg"/>
      </div>
    </div>
    <div class="text-center">
      <el-button size="large" type="primary" @click="generatePoster" :loading="copyPosterLoading" class="mt-16px"><Icon icon="ep:share" class="mr-8px"/> 复制分享 </el-button>
      <el-button size="large" type="primary" @click="dialogVisible = false" class="mt-16px ml-20px">关 闭</el-button>
    </div>
  </Dialog>
</template>

<style scoped lang="scss">

#poster-container {
  width: 350px;
  height: 650px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.bg {
  width: 350px;
  height: 650px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}
.container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}
.title{
  display: flex;
  flex-direction: column; // 列，主轴是列
  justify-content: center;// 主轴两端对齐
  align-items: center;// 交叉轴对齐
  width: 200px;
  height: 40px;
  font-size: 25px;
  font-weight: bold;
  background: white;
  border: 2px solid #e39d07;
  border-radius:10px;

}
.head{
  width: 350px;
  height: 450px;
  display: flex;
  justify-content: flex-start;
  flex-direction: column;
  align-items: flex-start;
  flex-wrap: wrap;
  align-content: space-between;
  background: white;
  border: 2px solid #e39d07;
  border-radius:10px;
}
.head-container{
  width: 330px;
  margin:0 0 5px 10px; // 上右下左
  //display: flex;
  //flex-direction: column;
  //justify-content: space-between;
  //align-items: center;
}
.label {
  font-size: 14px;
  font-weight: 500;
  color: #6a6a6a;
}
.content {
  font:14px / 1.5 'PingFang SC,Microsoft YaHei,Helvetica Neue,Helvetica,Hiragino Sans GB,SimSun,sans-serif';
  font-size: 14px;
  font-weight: 500;
  //color: rgba(0, 0, 0, .85);
  //background: #fff;
  //height: 100%;
  //font-size: 14px;

  color: #42B0F3;
  color: #333;
  font: 14px/1.5 Microsoft YaHei, arial, sans-serif;
  background-color: #f5f7fa;
}
.foot{
  width: 350px;
  height: 130px;
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  flex-wrap: wrap;
  background: white;
  border: 2px solid #e39d07;
  border-radius:10px;


}
.foot-left{
  width: 200px;
}
.foot-left-up{
  font-size: 25px;
  font-weight: bold;
  color: #e39d07;
}
.foot-left-down{
  font-size: 25px;
  font-weight: bold;
  color: #e39d07;
}
.foot-desc{
  font-size: 15px;
  font-weight: bold;
  color: #e39d07;
}
.qr-code {
  width: 60px;
  height: 60px;
}
</style>
