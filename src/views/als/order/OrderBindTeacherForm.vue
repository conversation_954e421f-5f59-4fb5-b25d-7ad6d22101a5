<script setup lang="ts">
import {<PERSON><PERSON><PERSON>, Teacher<PERSON>} from "@/api/als/teacher";

const message = useMessage() // 消息弹窗

import {BindApi, BindVO, FormData} from "@/api/als/bind";
import {CustomerApi} from "@/api/als/customer";
import {DICT_TYPE, getIntDictOptions} from "@/utils/dict";

defineOptions({ name: 'BindForm' })
const dialogVisible = ref(false)
const formLoading = ref(false)
const formRef = ref()
const formData = ref<FormData>({
  orderId: undefined,
  customerId: undefined,
  teacherId: undefined,
  isGroupChat: undefined,
  promisedLastServiceTime: undefined
})
const customerData = ref({
  customerId: undefined,
  customerName: undefined,
  customerSex: undefined,
  relationship: undefined,
  customerPhone: undefined,
  openId: undefined,
  serviceStatus: undefined,
  sourceChannel: undefined,
  serviceTags: [],
  operationTags: [],
  levelTags: [],
  registerTime: undefined,
  lastLoginTime: undefined,
  headOperateUserId: undefined,
  headMarketUserId: undefined,
  customerRemark: undefined
})

const formRules = reactive({
  customerId: [{ required: true, message: '家长ID不能为空', trigger: ['blur', 'change'] }],
  teacherId: [{ required: true, message: '老师ID不能为空', trigger: ['blur', 'change'] }],
})

/** 打开弹窗 */
const open = async (customerId: number, orderId?: number) => {
  dialogVisible.value = true
  formData.value.orderId = orderId
  formData.value.customerId = customerId
  customerData.value = await CustomerApi.getCustomer( customerId );
}

defineExpose({ open })
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as BindVO
    await BindApi.createBind(data)
    message.success("绑定成功")
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
const loading = ref(true) // 列表的加载中
const options = ref<TeacherVO[]>([])
const DELAY_MS = 500; // 使用常量表示延迟时间

const keyWords = reactive({
  teacherName: undefined
});
// 提取延迟函数
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

const remoteMethod = async (query: any) => {
  if (!query){
    return
  }

  if (query.length < 1){
    return;
  }

  // 使用 Promise 处理延迟
  await delay(DELAY_MS);
  try {
    keyWords.teacherName = query;
    loading.value = false;
    options.value = await TeacherApi.queryByKeywords(keyWords);
  } catch (error) {
    loading.value = false; // 确保 loading 在请求失败时也设置为 false
  }
}
</script>

<template>
  <Dialog title="绑定老师" v-model="dialogVisible" width="800px">
    <ContentWrap>
      <!-- 搜索工作栏 -->
      <el-form
        class="-mb-15px"
        :model="formData"
        :rules="formRules"
        ref="formRef"
        label-width="140px"
        inline
      >
        <el-form-item label="订单ID：" >
          <span>{{ formData.orderId }}</span>
        </el-form-item>

        <el-form-item label="家长ID：">
          <span>{{ formData.customerId }}</span>
        </el-form-item>
        <el-form-item label="家长姓名：">
          <span>{{ customerData.customerName }}</span>
        </el-form-item>

        <el-form-item label="选择老师" prop="teacherId" class="!w-100%">
          <el-input
            v-model="formData.teacherId"
            placeholder="填写老师ID"
            clearable
            class="!w-120px"
          />
          <el-select
            v-model="formData.teacherId"
            filterable
            clearable
            remote
            reserve-keyword
            placeholder="按姓名搜索"
            :remote-method="remoteMethod"
            :loading="loading"
            class="!w-200px"
          >
            <el-option
              v-for="item in options"
              :key="item.teacherId"
              :label="item.teacherName"
              :value="item.teacherId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="承诺最后服务时间" prop="promisedLastServiceDate" class="!w-300px">
          <el-date-picker
            v-model="formData.promisedLastServiceDate"
            type="date"
            value-format="x"
            class="!w-200px"
          />
        </el-form-item>
        <el-form-item label="陪学类型" prop="lessonType" class="!w-300px">
          <el-select v-model="formData.lessonType">
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_LESSON_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否已建群" prop="isGroupChat" class="!w-300px">
          <el-radio-group v-model="formData.isGroupChat">
            <el-radio
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_YES_OR_ON)"
              :key="dict.value"
              :label="dict.value"
            >
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="该年级服务经验" prop="isHaveExperience" class="!w-100%">
          <el-radio-group v-model="formData.isHaveExperience" class="!w-300px">
            <el-radio
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_YES_OR_ON)"
              :key="dict.value"
              :label="dict.value"
            >
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="是否已认证家长需求" prop="isAuthenticated" class="!w-300px">
          <el-radio-group v-model="formData.isAuthenticated">
            <el-radio
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_YES_OR_ON)"
              :key="dict.value"
              :label="dict.value"
            >
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="服务经验补充" class="!w-100%">
          <el-input v-model="formData.experienceExtra" type="textarea" maxlength="250" show-word-limit rows="4"/>
        </el-form-item>
      </el-form>
    </ContentWrap>

    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<style scoped lang="scss">

</style>
