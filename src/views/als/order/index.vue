<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="85px"
    >
      <el-form-item label="活动标签" prop="activeTags">
        <el-select
          v-model="queryParams.activeTags"
          clearable
          class="!w-160px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_ACTIVE_TAGS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="体验单ID" prop="orderId">
        <el-input
          v-model="queryParams.orderId"
          placeholder="ID为数字"
          clearable
          @keyup.enter="handleQuery"
          class="!w-160px"
        />
      </el-form-item>
      <el-form-item label="体验单号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="体验单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-160px"
        />
      </el-form-item>
      <el-form-item label="体验单类型" prop="orderType">
        <el-select
          v-model="queryParams.orderType"
          placeholder="体验单类型"
          clearable
          class="!w-160px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_ORDER_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="课程类型" prop="lessonType">
        <el-select
          v-model="queryParams.lessonType"
          placeholder="课程类型"
          clearable
          class="!w-160px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_LESSON_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
     

      <!-- 使用折叠组件包装额外的搜索条件 -->
      <CollapseWithButton 
        v-model:collapsed="isCollapsed"
        expand-text="展开"
        collapse-text="收起"
        expand-icon="ep:arrow-down"
        collapse-icon="ep:arrow-up"
      >
        <el-form-item label="家长ID" prop="customerId">
          <el-input
            v-model="queryParams.customerId"
            placeholder="家长ID"
            clearable
            @keyup.enter="handleQuery"
            class="!w-160px"
          />
        </el-form-item>

        <el-form-item label="手机号" prop="customerPhone">
          <el-input
            v-model="queryParams.customerPhone"
            placeholder="手机号"
            clearable
            @keyup.enter="handleQuery"
            class="!w-160px"
          />
        </el-form-item>
        <el-form-item label="区域" prop="areaId">
          <el-cascader
            v-model="queryParams.orderAreaId"
            :options="areaList"
            :props="defaultProps"
            class="!w-160px"
            clearable
            filterable
            placeholder="城市"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="详细地址" prop="orderAddress">
          <el-input
            v-model="queryParams.orderAddress"
            placeholder="模糊搜索"
            clearable
            @keyup.enter="handleQuery"
            class="!w-160px"
          />
        </el-form-item>
        <el-form-item label="发布人" prop="releaseUserId">
          <el-input
            v-model="queryParams.releaseUser"
            placeholder="模糊搜索"
            clearable
            @keyup.enter="handleQuery"
            class="!w-160px"
          />
        </el-form-item>
        <el-form-item label="暂停接单" prop="isSuspend">
          <el-select
            v-model="queryParams.isSuspend"
            placeholder="是否暂停接单"
            clearable
            class="!w-160px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_YES_OR_ON)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否已建群" prop="isGroupChat">
          <el-select
            v-model="queryParams.isGroupChat"
            placeholder="是否已建群"
            clearable
            class="!w-160px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_YES_OR_ON)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="当前负责人" prop="headCurrent">
          <el-select
v-model="queryParams.headCurrent"
                     clearable
                     filterable
                     class="!w-160px"
                     placeholder="当前负责人">
            <el-option
              v-for="item in userList"
              :key="item.id"
              :label="item.nickname"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="运营负责人" prop="headOperate">
          <el-select
v-model="queryParams.headOperate"
                     clearable
                     filterable
                     class="!w-160px"
                     placeholder="运营负责人">
            <el-option
              v-for="item in userList"
              :key="item.id"
              :label="item.nickname"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="市场负责人" prop="headMarket">
          <el-select
v-model="queryParams.headMarket"
                     clearable
                     filterable
                     class="!w-160px"
                     placeholder="市场负责人">
            <el-option
              v-for="item in userList"
              :key="item.id"
              :label="item.nickname"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="老师性别" prop="requireSex">
          <el-select
            v-model="queryParams.requireSex"
            placeholder="老师性别要求"
            clearable
            class="!w-160px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_REQUIRE_SEX)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="周次" prop="timesWeek">
          <el-input
            v-model="queryParams.timesWeek"
            placeholder="周次"
            clearable
            @keyup.enter="handleQuery"
            class="!w-160px"
          />
        </el-form-item>
        <el-form-item label="周内/周末" prop="isOnWeekend">
          <el-select
            v-model="queryParams.isOnWeekend"
            placeholder="选择周内周末"
            clearable
            class="!w-160px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_IS_ON_WEEKEND)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="体验确认" prop="isConfirmExpTime">
          <el-select
            v-model="queryParams.isConfirmExpTime"
            placeholder="是否确认体验时间"
            clearable
            class="!w-160px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_YES_OR_ON)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="孩子性别" prop="kidSex">
          <el-select
            v-model="queryParams.kidSex"
            placeholder="孩子性别"
            clearable
            class="!w-160px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_SEX)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="孩子年级" prop="kidStage">
          <el-select
            v-model="queryParams.kidStage"
            placeholder="孩子年级阶段"
            clearable
            class="!w-160px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_KID_STAGE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="沟通结果" prop="communicateResult">
          <el-select
            v-model="queryParams.communicateResult"
            placeholder="沟通结果"
            clearable
            class="!w-160px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_COMMUNICATE_RESULT)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="学校名称" prop="schoolName">
          <el-input
            v-model="queryParams.schoolName"
            placeholder="模糊搜索"
            clearable
            @keyup.enter="handleQuery"
            class="!w-160px"
          />
        </el-form-item>
        <el-form-item label="学校性质" prop="schoolNature">
          <el-select
            v-model="queryParams.schoolNature"
            placeholder="请选择"
            clearable
            class="!w-160px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_SCHOOL_NATURE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="孩子性格" prop="kidChr">
          <el-select
            v-model="queryParams.kidChr"
            placeholder="孩子性格"
            clearable
            class="!w-160px"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.ALS_KID_CHR)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="订单来源" prop="sourceChannel">
          <el-select
            v-model="queryParams.sourceChannel"
            placeholder="请选择"
            clearable
            class="!w-160px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_ORDER_CHANNEL)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="邀请人" prop="inviterId">
          <el-input
            v-model="queryParams.inviterId"
            placeholder="邀请人"
            clearable
            @keyup.enter="handleQuery"
            class="!w-160px"
          />
        </el-form-item>
        <el-form-item label="抢单老师" prop="matchTeacherId">
          <el-input
            v-model="queryParams.matchTeacherId"
            placeholder="老师Id"
            clearable
            @keyup.enter="handleQuery"
            class="!w-100px"
          />
          <el-select
            v-model="queryParams.matchTeacherId"
            filterable
            clearable
            remote
            reserve-keyword
            placeholder="模糊搜索"
            :remote-method="remoteMethod"
            :loading="loading"
            class="!w-120px"
          >
            <el-option
              v-for="item in options"
              :key="item.teacherId"
              :label="item.teacherName"
              :value="item.teacherId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="被换老师" prop="changedTeacherId">
          <el-input
            v-model="queryParams.changedTeacherId"
            placeholder="老师Id"
            clearable
            @keyup.enter="handleQuery"
            class="!w-100px"
          />
          <el-select
            v-model="queryParams.changedTeacherId"
            filterable
            clearable
            remote
            reserve-keyword
            placeholder="模糊搜索"
            :remote-method="remoteMethod"
            :loading="loading"
            class="!w-120px"
          >
            <el-option
              v-for="item in options"
              :key="item.teacherId"
              :label="item.teacherName"
              :value="item.teacherId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="被换原因" prop="changedReasonTags">
          <el-select
            v-model="queryParams.changedReasonTags"
            placeholder="请选择"
            clearable
            class="!w-160px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_CHANGED_REASON_TAGS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="手工创建" prop="isManualCrate">
          <el-select
            v-model="queryParams.isManualCrate"
            placeholder="是否手工创建"
            clearable
            class="!w-160px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_YES_OR_ON)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="发布时间" prop="releaseTime">
          <el-date-picker
            v-model="queryParams.releaseTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-200px"
          />
        </el-form-item>
        <el-form-item label-width="100px" label="注册下单时间" prop="registerTime">
          <el-date-picker
            v-model="queryParams.registerTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-200px"
          />
        </el-form-item>
        <el-form-item label-width="100px" label="实际跟踪时间" prop="actualTrackingTime">
          <el-date-picker
            v-model="queryParams.actualTrackingTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-200px"
          />
        </el-form-item>
        <el-form-item label="体验时间" prop="expTime">
          <el-date-picker
            v-model="queryParams.expTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-200px"
          />
        </el-form-item>
        <el-form-item label="跟踪时间" prop="trackingTime">
          <el-date-picker
            v-model="queryParams.trackingTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-200px"
          />
        </el-form-item>
        <el-form-item label-width="130px" label="承诺最后服务时间" prop="promisedLastServiceTime">
          <el-date-picker
            v-model="queryParams.promisedLastServiceTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-200px"
          />
        </el-form-item>
        
        <!-- 操作按钮放在折叠组件的after-button插槽中 -->
        <template #after-button>
          <div class="search-button-container ml-10px">
            <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
            <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
            <el-button
              type="primary"
              plain
              @click="openForm('create')"
              v-hasPermi="['als:order:create']"
            >
              <Icon icon="ep:plus" class="mr-5px" /> 新增
            </el-button>
            <el-button
              type="success"
              plain
              @click="handleExport"
              :loading="exportLoading"
              v-hasPermi="['als:order:export']"
            >
              <Icon icon="ep:download" class="mr-5px" /> 导出
            </el-button>
          </div>
        </template>
      </CollapseWithButton>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true"  border highlight-current-row size="small">
      <el-table-column label="订单基本信息" header-align="left" align="left" width="250" >
        <template #default="scope">
          <div>
            <span class="column-label">体验单ID：</span>
            <span class="column-value">{{ scope.row.orderId }}</span>
          </div>
          <div>
            <span class="column-label">单号：</span>
            <span class="column-value">{{ scope.row.orderNo }}</span>
          </div>
          <div>
            <span class="column-label">类型：</span>
            <span><dict-tag-text :type="DICT_TYPE.ALS_ORDER_TYPE" :value="scope.row.orderType" /></span>
          </div>
          <div>
            <span class="column-label">下单时间：</span>
            <span class="column-value">{{ formatDate(scope.row.registerTime) }}</span>
          </div>
          <div>
            <span class="column-label">活动标签：</span>
            <span v-if="scope.row.activeTags.length > 0">
              <dict-tag :type="DICT_TYPE.ALS_ACTIVE_TAGS" :value="scope.row.activeTags" />
            </span>
            <span v-else>无</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="订单状态" align="left" prop="orderStatus" width="200">
        <template #default="scope">
          <div>
            <span class="column-label">订单状态：</span>
            <dict-tag :type="DICT_TYPE.ALS_ORDER_STATUS" :value="scope.row.orderStatus" />
          </div>
          <div class="mt-5px">
            <span class="column-label">当前进程：</span>
            <span class="column-value"><dict-tag-text :type="DICT_TYPE.ALS_ORDER_PROCESS" :value="scope.row.orderProcess" /></span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="家长信息" header-align="left" align="left" width="200" >
        <template #default="scope">
          <div>
            <span class="column-label">家长编号：</span>
            <span class="column-value">{{ scope.row.customerId }}</span>
          </div>
          <div>
            <span class="column-label">姓名：</span>
            <span class="column-value link" @click="openDetail(scope.row.customerId)">
              {{ scope.row.customerName }}
            </span>
          </div>
          <div>
            <span class="column-label">手机：</span>
            <span class="column-value">{{ scope.row.customerPhone }}</span>
          </div>
          <div>
            <span class="column-label">区域：</span>
            <span class="column-value">{{ scope.row.orderAreaName }}</span>
          </div>
          <div>
            <span class="column-label">地址：</span>
            <span class="column-value">{{ scope.row.orderAddress }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="需求信息" header-align="left" align="left" width="200" >
        <template #default="scope">
          <div>
            <span class="column-label">老师性别要求：</span>
            <span> <dict-tag :type="DICT_TYPE.ALS_REQUIRE_SEX" :value="scope.row.requireSex" /></span>
          </div>
          <div>
            <span class="column-label">陪学时间：</span>
            <span><dict-tag-text :type="DICT_TYPE.ALS_IS_ON_WEEKEND" :value="scope.row.isOnWeekend" /></span>
          </div>
          <div>
            <span class="column-label">周次：</span>
            <span class="column-value" v-if="scope.row.timesWeek>0">每周{{ scope.row.timesWeek }}次</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="家长需求" align="left" header-align="left" prop="demandContent" width="400">
        <template #default="scope">
          <div class="h-30 overflow-y-auto">
            <span class="column-value">{{ scope.row.demandContent }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="跟进信息" header-align="left" align="left" width="260" >
        <template #default="scope">
          <div>
            <span class="column-label">是否拉群：</span>
            <span> <dict-tag-text :type="DICT_TYPE.ALS_YES_OR_ON" :value="scope.row.isGroupChat" /></span>
          </div>
          <div>
            <span class="column-label">下次跟踪时间：</span>
            <span class="column-value">{{ formatDate(scope.row.trackingTime) }}</span>
          </div>
          <div>
            <span class="column-label">实际跟踪时间：</span>
            <span class="column-value">{{ formatDate(scope.row.actualTrackingTime) }}</span>
          </div>
          <div>
            <span class="column-label">沟通结果：</span>
            <span> <dict-tag :type="DICT_TYPE.ALS_COMMUNICATE_RESULT" :value="scope.row.communicateResult" /></span>
          </div>
          <div>
            <span class="column-label">跟踪备注补充：</span>
            <span class="column-value">{{ scope.row.orderRemarkExtra }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="确认信息" header-align="left" align="left" width="250" >
        <template #default="scope">
          <div>
            <span class="column-label">体验时间确认：</span>
            <span><dict-tag :type="DICT_TYPE.ALS_YES_OR_ON" :value="scope.row.isConfirmExpTime" /></span>
          </div>
          <div>
            <span class="column-label">体验时间：</span>
            <span class="column-value">{{ formatDate(scope.row.expTime)}}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="发布信息" header-align="left" align="left" width="200" >
        <template #default="scope">
          <div>
            <span class="column-label">发布状态：</span>
            <span><dict-tag :type="DICT_TYPE.ALS_RELEASE_STATUS" :value="scope.row.releaseStatus" /></span>
          </div>
          <div>
            <span class="column-label">确认人：</span>
            <span class="column-value">{{ scope.row.releaseUser }}</span>
          </div>
          <div>
            <span class="column-label">确认时间：</span>
            <span class="column-value">{{ formatDate(scope.row.releaseTime) }}</span>
          </div>
          <div>
            <span class="column-label">暂停接单：</span>
            <span><dict-tag :type="DICT_TYPE.ALS_YES_OR_ON" :value="scope.row.isSuspend" /></span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="负责人信息" header-align="left" align="left" width="150" >
        <template #default="scope">
          <div>
            <span class="column-label">当前负责人：</span>
            <span class="column-value">{{ scope.row.headCurrentName }}</span>
          </div>
          <div>
            <span class="column-label">运营负责人：</span>
            <span class="column-value">{{ scope.row.headOperateName }}</span>
          </div>
          <div>
            <span class="column-label">市场负责人：</span>
            <span class="column-value">{{ scope.row.headMarketName }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="孩子信息" header-align="left" align="left" width="200" >
        <template #default="scope">
          <div>
            <span class="column-label">称呼：</span>
            <span class="column-value">{{ scope.row.kidNickName }} <dict-tag :type="DICT_TYPE.ALS_SEX" :value="scope.row.kidSex" /></span>
          </div>

          <div>
            <span class="column-label">性格：</span>
            <span><dict-tag-text :type="DICT_TYPE.ALS_KID_CHR" :value="scope.row.kidChr" /></span>
          </div>
          <div>
            <span class="column-label">阶段：</span>
            <span><dict-tag-text :type="DICT_TYPE.ALS_KID_STAGE" :value="scope.row.kidStage" /></span>
          </div>
          <div>
            <span class="column-label">学校名称：</span>
            <span class="column-value">{{ scope.row.schoolName }}</span>
          </div>
          <div>
            <span class="column-label">学校性质：</span>
            <span> <dict-tag :type="DICT_TYPE.ALS_SCHOOL_NATURE" :value="scope.row.schoolNature" /></span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="邀请人" align="center" prop="inviterId">
        <template #default="scope">
          <span class="column-value">{{ scope.row.inviterId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="体验单来源" align="left" prop="sourceChannel" width="95">
        <template #default="scope">
          <span v-if="scope.row.sourceChannel > 0">
            <dict-tag :type="DICT_TYPE.ALS_ORDER_CHANNEL" :value="scope.row.sourceChannel" />
          </span>
          <span v-else>
            无
          </span>
        </template>
      </el-table-column>

      <el-table-column label="抢单信息" header-align="left" align="left" width="200" >
        <template #default="scope">
          <div>
            <span class="column-label">申请接单：</span>
            <span class="column-value link"  @click="toOrderConfirm(scope.row.orderId)">{{ scope.row.confirmCount }}  人</span>
          </div>
          <div>
            <span class="column-label">抢单老师ID：</span>
            <span class="column-value">{{ scope.row.matchTeacherId }}</span>
          </div>
          <div>
            <span class="column-label">抢单老师：</span>
            <span class="column-value">{{ scope.row.matchTeacher }}</span>
          </div>
          <div>
            <span class="column-label">手机号：</span>
            <span class="column-value">{{ scope.row.matchTeacherPhone }}</span>
          </div>
          <div>
            <span class="column-label">承诺最后服务时间：</span>
            <span class="column-value">{{ formatDate(scope.row.promisedLastServiceTime,"YYYY-MM-DD") }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="老师变更信息" header-align="left" align="left" width="200" >
        <template #default="scope">
          <div>
            <span class="column-label">被换老师ID：</span>
            <span class="column-value">{{ scope.row.changedTeacherId }}</span>
          </div>
          <div>
            <span class="column-label">被换老师：</span>
            <span class="column-value">{{ scope.row.changedTeacher }}</span>
          </div>
          <div>
            <span class="column-label">手机号：</span>
            <span class="column-value">{{ scope.row.changedTeacherPhone }}</span>
          </div>
          <div>
            <span class="column-label">被换原因：</span>
            <span><dict-tag :type="DICT_TYPE.ALS_CHANGED_REASON_TAGS" :value="scope.row.changedReasonTags" /></span>
          </div>
          <div>
            <span class="column-label">具体原因：</span>
            <span class="column-value">{{ scope.row.changedReason }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="接单信息" header-align="left" align="left" width="200" >
        <template #default="scope">
          <div>
            <span class="column-label">收藏订单：</span>
            <span class="column-value link"  @click="toOrderFav(scope.row.orderId,0)">{{ scope.row.favCollectionCount }} 人</span>
          </div>
          <div>
            <span class="column-label">查看订单：</span>
            <span class="column-value link"  @click="toOrderFav(scope.row.orderId,1)">{{ scope.row.favReadCount }} 人</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="270" fixed="right">
         <template #default="scope">
            <div class="flex items-start justify-start flex-wrap" >
              <el-button
                plain
                size="small"
                type="primary"
                class="op-btn"
                @click="openForm('update', scope.row.orderId)"
                v-hasPermi="['als:order:update']"
              >
               完善信息
              </el-button>

              <el-tooltip
                content="体验单状态已审核"
                placement="top"
                :disabled="scope.row.orderStatus < 2"
              >
                <el-button
                  plain
                  size="small"
                  type="primary"
                  class="op-btn"
                  @click="handleAuditOrder(scope.row.orderId)"
                  v-hasPermi="['als:order:update']"
                  :disabled="scope.row.orderStatus >= 2"
                >
                  直接通过
                </el-button>
              </el-tooltip>

              <el-tooltip
                content="体验单状态不是待确认，无法操作"
                placement="top"
                :disabled="scope.row.orderStatus === 2 "
              >
                <el-button
                  plain
                  size="small"
                  type="primary"
                  class="op-btn"
                  @click="handleReleaseOrder(scope.row.orderId)"
                  v-hasPermi="['als:order:update']"
                  :disabled = "scope.row.orderStatus != 2"
                >
                 发布确认
                </el-button>
               </el-tooltip>

              <el-button
                plain
                size="small"
                type="primary"
                class="op-btn"
                @click="openPoster('update', scope.row.orderId)"
                v-hasPermi="['als:order:update']"
              >
                订单海报
              </el-button>
              <el-button
                plain
                size="small"
                type="primary"
                class="op-btn"
                @click="openBindForm(scope.row.customerId, scope.row.orderId)"
                v-hasPermi="['als:order:update']"
              >
                绑定老师
              </el-button>

              <el-tooltip
                content="已暂停接单"
                placement="top"
                :disabled="scope.row.isSuspend == 0 "
              >
                <el-button
                  plain
                  size="small"
                  type="primary"
                  class="op-btn"
                  @click="pause(scope.row.orderId)"
                  :disabled="scope.row.isSuspend == 1"
                  v-hasPermi="['als:order-confirm:update']"
                >
                  暂停接单
                </el-button>
              </el-tooltip>
            <el-dropdown
              @command="(command) => handleCommand(command, scope.row)"
              v-hasPermi="[
                      'als:order:update'
                    ]"
            >
            <el-button link type="primary"  class="op-btn">更多<Icon icon="ep:d-arrow-right" />
            </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="updateLocal">
                    <Icon icon="ep:coffee" />更新坐标
                  </el-dropdown-item>
                  <el-dropdown-item command="handleAddTrackingTime">
                    <Icon icon="ep:coffee" />添加跟踪日期
                  </el-dropdown-item>
                  <el-dropdown-item command="handleAddTeacher">
                    <Icon icon="ep:plus" />增加老师
                  </el-dropdown-item>
                  <el-dropdown-item command="handleChangeTeacher">
                    <Icon icon="ep:lollipop" />换老师
                  </el-dropdown-item>
                  <el-dropdown-item command="handleAddLessonPeriod">
                    <Icon icon="ep:money" />添加课时
                  </el-dropdown-item>
                  <el-dropdown-item command="handleDelete" v-if="checkPermi(['als:order:delete'])">
                    <Icon icon="ep:delete" />删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
          </el-dropdown>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <OrderForm ref="formRef" @success="getList" />

  <PosterView ref="posterRef" @success="getList" />
  <!-- 订单跟踪时间 -->
  <OrderTrackForm ref="formRef2"/>
  <!-- 增加老师 -->
  <AddTeacherForm ref="formRef3"/>
  <!-- 换老师 -->
  <ChangeTeacherForm ref="formRef4"/>
  <!-- 添加课时 -->
  <AddLessonPeriodForm ref="formRef5"/>
  <!-- 绑定老师-->
  <BindForm ref="formRef6"/>
</template>

<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { formatDate} from '@/utils/formatTime'
import download from '@/utils/download'
import { OrderApi, OrderVO } from '@/api/als/order'
import OrderForm from './OrderForm.vue'
import PosterView from './PosterVue.vue'
import OrderTrackForm from './OrderTrackForm.vue'
import AddTeacherForm from './AddTeacherForm.vue'
import AddLessonPeriodForm from './AddLessonPeriodForm.vue'
import ChangeTeacherForm from './ChangeTeacherForm.vue'
import BindForm from './OrderBindTeacherForm.vue'
import {defaultProps} from "@/utils/tree";
import * as AreaApi from "@/api/system/area";
import * as UserApi from "@/api/system/user";
import {checkPermi} from "@/utils/permission";
import {TeacherApi, TeacherVO} from "@/api/als/teacher";
import { CollapseWithButton } from '@/components/Collapse'

/** 体验单列表 */
defineOptions({ name: 'ExpOrder' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const isCollapsed = ref(true); // 添加折叠状态控制
const userList = ref<UserApi.UserVO[]>([]) // 用户列表
const areaList = ref([]) // 地区列表
const loading = ref(true) // 列表的加载中
const list = ref<OrderVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  sourceChannel: undefined,
  orderId: undefined,
  orderNo: undefined,
  orderType: undefined,
  lessonType: undefined,
  customerId: undefined,
  customerPhone: undefined,
  orderAreaId: undefined,
  orderAddress: undefined,
  orderStatus: undefined,
  releaseStatus: undefined,
  releaseUser: undefined,
  releaseTime: [],
  orderProcess: undefined,
  isSuspend: undefined,
  isGroupChat: undefined,
  headCurrent: undefined,
  headOperate: undefined,
  headMarket: undefined,
  requireSex: undefined,
  hardRequireAbility: undefined,
  requireAbilityExtra: undefined,
  needsTags: undefined,
  needsFocusTags: undefined,
  demandContent: undefined,
  timesWeek: undefined,
  isOnWeekend: undefined,
  timeRange: undefined,
  trackingTime: [],
  actualTrackingTime: [],
  trackingRemarkTags: undefined,
  expTime: [],
  isConfirmExpTime: undefined,
  kidSex: undefined,
  kidStage: undefined,
  communicateResult: undefined,
  schoolName: undefined,
  schoolNature: undefined,
  kidChr: undefined,
  inviterId: undefined,
  matchTeacherId: undefined,
  promisedLastServiceTime: [],
  changedTeacherId: undefined,
  changedReasonTags: undefined,
  isManualCrate: undefined,
  registerTime: [],
  activeTags: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const DELAY_MS = 500; // 使用常量表示延迟时间
// 获得地区列表
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await OrderApi.getOrderPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
  // 区域列表
  areaList.value = await AreaApi.getAreaTree()
  // 获得用户列表
  userList.value = await UserApi.getSimpleUserList()

}
const options = ref<TeacherVO[]>([])

const keyWords = reactive({
  teacherName: undefined
});
const remoteMethod = async (query: any) => {
  if (!query){
    return
  }

  if (query.length < 1){
    return;
  }

  // 使用 Promise 处理延迟
  await delay(DELAY_MS);
  try {
    keyWords.teacherName = query;
    loading.value = false;
    options.value = await TeacherApi.queryByKeywords(keyWords);
  } catch (error) {
    loading.value = false; // 确保 loading 在请求失败时也设置为 false
  }
}

// 提取延迟函数
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

const posterRef = ref()
const openPoster = (type: string, id?: number) => {
  posterRef.value.open(type, id)
}

const formRef2 = ref()
const openForm2 = (type: string, id?: number) => {
  formRef2.value.open(type, id)
}

/** 增加老师 */
const formRef3 = ref()
const openForm3 = (type: string, id?: number) => {
  formRef3.value.open(type, id)
}

/** 换老师 */
const formRef4 = ref()
const openForm4 = (type: string, id?: number) => {
  formRef4.value.open(type, id)
}

/** 添加课时 */
const formRef5 = ref()
const openForm5 = (type: string, id?: number) => {
  formRef5.value.open(type, id)
}

/** 绑定老师 */
const formRef6 = ref()
const openBindForm = (customerId?: number, orderId?: number) => {
  formRef6.value.open(customerId, orderId)
}

/** 打开详情 */
const openDetail = (id: number) => {
  push({ name: 'CustomerDetail', params: { id } })
}

/** 暂停接单 */
const pause = async (orderId: number) => {
  try {
    await message.confirm("是否暂停接单？")
    await OrderApi.pauseOrder(orderId)
    message.success("暂停接单成功")
    // 刷新列表
    await getList()
  } catch {}
}

const handleReleaseOrder = async (id: number) => {
  try {
    // 发布的二次确认
    await message.confirm('是否要发布体验单?')
    // 发布
    await OrderApi.releaseOrder(id)
    message.success(t('发布完成'))
    // 刷新列表
    await getList()
  } catch {}
}

const handleAuditOrder = async (id: number) => {
  try {
    // 直接通过二次确认
    await message.confirm('是否要直接通过?')
    //
    await OrderApi.auditOrder(id)
    message.success(t('审核通过'))
    // 刷新列表
    await getList()
  } catch {}
}

const { push } = useRouter()
const toOrderConfirm = (orderId: number) => {
  push({ name: 'OrderConfirm2', params: { orderId } })
}
const toOrderFav = (orderId: number, type: number) => {
  push({ name: 'TeacherFav2', params: { orderId:orderId, type:type } })
}

/** 操作分发 */
const handleCommand = (command: string, row: OrderVO) => {
  switch (command) {
    case 'handleDelete':
      handleDelete(row.orderId)
      break
    case 'handleAddTrackingTime':
      openForm2('update',row.orderId)
      break
    case 'handleAddTeacher':
      openForm3('update',row.orderId)
      break
    case 'handleChangeTeacher':
      openForm4('update',row.orderId)
      break
    case 'handleAddLessonPeriod':
      openForm5('create',row.customerId)
      break
    case 'updateLocal':
      updateLocal(row.orderId)
      break
    default:
      break
  }
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await OrderApi.deleteOrder(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 删除按钮操作 */
const updateLocal = async (id: number) => {
  try {
    // 发起删除
    await OrderApi.updateLocal(id)
    message.success(t('common.updateSuccess'))
    // 刷新列表
    // await getList()
  } catch {}
}


/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await OrderApi.exportOrder(queryParams)
    download.excel(data, '陪学体验单.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const route = useRoute()
const id = Number(route.params.id)
const customerId = Number(route.params.customerId)

/** 初始化 **/
onMounted(() => {
  if (!isNaN(id)) {
    queryParams.orderId = id
  }
  if (!isNaN(customerId)) {
    queryParams.customerId = customerId
  }
  getList()
})
</script>


<style scoped lang="scss">
.search-button-container {
  display: flex;
  gap: 8px;
}

:deep(.el-form-item--default){
  --font-size: 12px;
  margin-bottom: 5px;
}
:deep(.el-form--inline .el-form-item){
  margin-right:5px;
}
</style>

