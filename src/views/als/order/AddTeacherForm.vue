<script setup lang="ts">/** 添加老师 表单 */
defineOptions({ name: 'AddTeacherForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

import {AddTeacherOrderVO, OrderApi} from '@/api/als/order'

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formRef = ref() // 表单 Ref
const formData = ref({
  orderId: undefined,
  addReason: undefined
})
const formRules = reactive({
  addReason: [{ required: true, message: '原因不能为空', trigger: 'change' }]
})

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  formData.value.orderId = id
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as AddTeacherOrderVO
    await OrderApi.addTeacher(data)
    message.success(t('common.createSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
</script>

<template>
  <Dialog title="增加老师" v-model="dialogVisible" width="36%" center>
    <ContentWrap>
      <!-- 搜索工作栏 -->
      <el-form
        class="-mb-15px"
        :model="formData"
        ref="formRef"
        :inline="true"
        label-width="85px"
        label-position="top"
      >
        <el-form-item label="增加原因" prop="orderId">
          <el-input
            type="textarea"
            :rows="5"
            v-model="formData.addReason"
            placeholder="请简要说明原因"
            maxlength="100"
            show-word-limit
            class="!w-500px"
          />
        </el-form-item>
      </el-form>
    </ContentWrap>

    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<style scoped lang="scss">

</style>
