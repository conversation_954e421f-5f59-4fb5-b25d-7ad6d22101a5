<template>
  <Dialog title="添加课时" v-model="dialogVisible" width="700">
    <el-form
      :inline="true"
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >

      <el-form-item label="家长ID">
        <el-text class="item-input">{{formData.customerId}}</el-text>
      </el-form-item>
      <el-form-item label="课时包" prop="coursePackageId">
        <el-select v-model="formData.coursePackageId" @change="handleChange" placeholder="请选择" clearable class="item-input"	>
          <el-option
            v-for="dict in allCoursePackageList"
            :key="dict.coursePackageId"
            :label="dict.packageName"
            :value="dict.coursePackageId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="课时原价" prop="salePrice">
        <el-input v-model="formData.salePrice" placeholder="自动填写" clearable class="item-input" >
          <template #append>元</template>
        </el-input>
      </el-form-item>
      <el-form-item label="课时数" prop="salePrice">
        <el-input v-model="formData.lessonPeriod" placeholder="自动填写" clearable class="item-input"/>
      </el-form-item>
<!--      <el-form-item label="优惠金额" prop="discountAmount">-->
<!--        <el-input v-model="formData.discountAmount" placeholder="请输入优惠金额" />-->
<!--      </el-form-item>-->
      <el-form-item label="实付金额" prop="actualAmount">
        <el-input v-model="formData.actualAmount" placeholder="请输入" class="item-input">
          <template #append>元</template>
        </el-input>
      </el-form-item>
      <el-form-item label="支付方式" prop="applyMethod">
        <el-select v-model="formData.applyMethod" placeholder="请选择" clearable class="item-input">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_PAYMENT_METHOD)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="封存流转金额" prop="blockAmount">
        <el-input-number controls-position="right" v-model="formData.blockAmount" placeholder="请输入" class="item-input"/>
      </el-form-item>
      <el-form-item label="资金流转来源" prop="directionFrom" >
        <el-select v-model="formData.directionFrom" placeholder="请选择" clearable class="item-input">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_DIRECTION_FROM)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="申请理由" prop="applyReason">
        <el-input type="textarea"
                  v-model="formData.applyReason"
                  placeholder="简要说明"
                  maxlength="100"
                  :rows="4"
                  show-word-limit
                  style="width: 490px;"	/>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { CustomerAddApplyApi, CustomerAddApplyVO } from '@/api/als/customeraddapply'
import {CoursePackageApi, CoursePackageVO} from "@/api/als/coursepackage";

/** 课时添加申请 表单 */
defineOptions({ name: 'AddLessonPeriodForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const allCoursePackageList = ref<CoursePackageVO[]>([]) // 列表的数据
const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  customerId: undefined as number | undefined,
  coursePackageId: undefined,
  discountAmount: undefined,
  actualAmount: undefined,
  applyMethod: undefined,
  blockAmount: undefined,
  directionFrom: undefined,
  applyReason: undefined,
  lessonPeriod: undefined as number | undefined,
  salePrice: undefined as number | undefined,
})
const formRules = reactive({
  customerId: [{ required: true, message: '家长ID不能为空', trigger: 'blur' }],
  coursePackageId: [{ required: true, message: '课时包ID不能为空', trigger: 'blur' }],
  actualAmount: [{ required: true, message: '实付金额不能为空', trigger: 'blur' }],
  applyMethod: [{ required: true, message: '支付方式不能为空', trigger: 'change' }],
  applyReason: [{ required: true, message: '申请理由不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, customerId?: number) => {
  dialogVisible.value = true
  formType.value = type
  resetForm()
  allCoursePackageList.value = await CoursePackageApi.getAllCoursePackage()
  formData.value.customerId = customerId
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as CustomerAddApplyVO
    // 计算优惠价
    data.discountAmount = data.salePrice - data.actualAmount
    if (formType.value === 'create') {
      await CustomerAddApplyApi.createCustomerAddApply(data)
      message.success(t('common.createSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

const handleChange = (value) => {
  const selectedPackage = allCoursePackageList.value.find(dict => dict.coursePackageId === value);
  formData.value.salePrice = selectedPackage?.salePrice
  formData.value.lessonPeriod = selectedPackage?.lessonPeriod
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    lessonPeriod: undefined,
    salePrice: undefined,
    customerId: undefined,
    coursePackageId: undefined,
    discountAmount: undefined,
    actualAmount: undefined,
    applyMethod: undefined,
    blockAmount: undefined,
    directionFrom: undefined,
    applyReason: undefined
  }
  formRef.value?.resetFields()
}
</script>
<style scoped lang="scss">
.item-input {
  width: 180px;
}
:deep(.el-input-number.is-controls-right .el-input__inner){
  padding-left: 1px !important;
  padding-right: 1px !important;
}
</style>
