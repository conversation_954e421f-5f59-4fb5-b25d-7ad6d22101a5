<script setup lang="ts">/** 陪学订单 表单 */
defineOptions({ name: 'OrderTrackForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

import {AddTrackOrderVO, OrderApi} from '@/api/als/order'

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formRef = ref() // 表单 Ref
const formData = ref({
  orderId: undefined,
  trackingTime: undefined
})
const formRules = reactive({
  trackingTime: [{ required: true, message: '跟踪时间不能为空', trigger: 'change' }]
})
const shortcuts = [
  {
    text: '今天',
    value: new Date(),
  },
  {
    text: '明天',
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() + 3600 * 1000 * 24)
      return date
    },
  },
  {
    text: '后天',
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() + 3600 * 1000 * 24 * 2)
      return date
    },
  },
  {
    text: '3天后',
    value: () => {
      const date = new Date()
      date.setTime(date.getTime() + 3600 * 1000 * 24 * 3)
      return date
    },
  },
]

/**
 * 昨天以前
 * @param time
 */
const disabledDate = (time: Date) => {
  return time.getTime() + 3600 * 1000 * 24 < Date.now()
}
/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  formData.value.orderId = id
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as AddTrackOrderVO
    console.log(data)
    await OrderApi.addTrackDate(data)
    message.success(t('common.createSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
</script>

<template>
  <Dialog title="添加下次跟踪日期" v-model="dialogVisible" width="60%" center>
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="180px"
      v-loading="formLoading"
    >
      <el-form-item label="跟踪日期" prop="trackingTime">
        <el-date-picker
          v-model="formData.trackingTime"
          type="datetime"
          placeholder="选择下次跟踪日期"
          :disabled-date="disabledDate"
          :shortcuts="shortcuts"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<style scoped lang="scss">

</style>
