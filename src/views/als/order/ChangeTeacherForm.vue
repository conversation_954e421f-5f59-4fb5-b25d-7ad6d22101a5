<script setup lang="ts">/** 添加老师 表单 */
import {DICT_TYPE, getIntDictOptions} from "@/utils/dict";

defineOptions({ name: 'ChangeTeacherForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

import {AddTeacherOrderVO, OrderApi} from '@/api/als/order'

const dialogVisible = ref<boolean>(false) // 弹窗的是否展示
  // 重置表单数据
const formLoading = ref<boolean>(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref<'create' | 'update'>('') // 表单的类型：create - 新增；update - 修改
const formRef = ref<FormInstance>() // 表单 Ref
const formData = reactive<{
  orderId?: number
  changedReasonTags?: number
  changedTeacherId?: number
  changedTeacher?: string
  changedReason: string
  goodComment?: string
  badComment?: string
}>({
  orderId: undefined,
  changedReasonTags: undefined,
  changedTeacherId: undefined,
  changedReason: '',
  goodComment: '',
  badComment: ''
})
const formRules = reactive({
  changedReasonTags: [{ required: true, message: '请选择被换原因类型', trigger: 'change' }],
  changedTeacherId: [{ required: true, message: '请选择被换老师', trigger: 'change' }],
  changedReason: [{ required: true, message: '具体原因不能为空', trigger: 'change' }],
  goodComment: [{ required: false, message: '请输入好评', trigger: 'blur' }],
  badComment: [{ required: false, message: '请输入差评', trigger: 'blur' }]
})

/** 打开弹窗 */
const open = async (type: 'create' | 'update', id?: number) => {
  dialogVisible.value = true
  formType.value = type
  formData.orderId = id
  // 获取订单信息
  try {
    const res = await OrderApi.getOrder(id);
    debugger
    formData.changedTeacher = res.matchTeacher
    formData.changedTeacherId = res.matchTeacherId
    console.log(formData)
  } catch (error) {
    console.error(error)
  }
  // 重置表单数据
  formData.changedReasonTags = undefined
  formData.changedReason = ''
  formData.goodComment = ''
  formData.badComment = ''
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗
/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData as unknown as AddTeacherOrderVO
    await OrderApi.changeTeacher(data)
    message.success(t('common.createSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
</script>

<template>
  <Dialog title="换老师" v-model="dialogVisible" width="50%" center>
    <ContentWrap>
      <!-- 搜索工作栏 -->
      <el-form
        class="-mb-15px"
        :model="formData"
        ref="formRef"
        label-width="100px"
      >
        <el-form-item label="被换老师" prop="changedTeacherId" size="large">
          <span>{{ formData.changedTeacher }}</span>
        </el-form-item>
        <el-form-item label="被换原因类型" prop="changedReasonTags">
          <el-select v-model="formData.changedReasonTags" placeholder="请选择订单类型">
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_CHANGED_REASON_TAGS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="具体原因" prop="changedReason">
          <el-input
            type="textarea"
            :rows="5"
            v-model="formData.changedReason"
            placeholder="请简要说明原因"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="被换老师评价" size="large">
          <el-input v-model="formData.goodComment" placeholder="请对被换老师进行评价">
            <template #prepend>好 评</template>
          </el-input>
          <el-input v-model="formData.badComment" placeholder="请对被换老师进行评价" class="mt-5px" >
            <template #prepend>差 评</template>
          </el-input>
        </el-form-item>
      </el-form>
    </ContentWrap>

    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
