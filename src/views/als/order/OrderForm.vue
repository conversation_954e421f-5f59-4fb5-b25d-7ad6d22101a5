<template>
  <Dialog title="完善信息" v-model="dialogVisible" width="70%" :closeOnClickModal=false>
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
      inline
    >
      <el-form-item label="订单编号" prop="orderNo" class="form-item">
        <el-input v-model="formData.orderNo" placeholder="请输入订单编号" />
      </el-form-item>
      <el-form-item label="订单类型" prop="orderType"  class="form-item">
        <el-select v-model="formData.orderType" placeholder="请选择订单类型">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_ORDER_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="课程类型" prop="lessonType"  class="form-item">
        <el-select v-model="formData.lessonType" placeholder="请选择订单类型">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_LESSON_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="家长ID" prop="customerId" class="form-item">
        <el-input v-model="formData.customerId" placeholder="请输入家长ID" />
      </el-form-item>
      <el-form-item label="手机号" prop="customerPhone" class="form-item">
        <el-input v-model="formData.customerPhone" placeholder="请输入手机号" />
      </el-form-item>

      <el-form-item label="地址" prop="orderAreaId" class="!w-380px">
        <el-cascader
          v-model="formData.orderAreaId"
          :options="areaList"
          :props="defaultProps"
          class="w-1/1"
          clearable
          filterable
          placeholder="请选择城市"
        />
      </el-form-item>

      <el-form-item label="详细地址" prop="orderAddress" class="!w-700px">
        <el-input v-model="formData.orderAddress" placeholder="请输入详细地址" />
      </el-form-item>
      <el-form-item label="主要教育者" prop="primaryEducator"  class="form-item">
        <el-input v-model="formData.primaryEducator" placeholder="请输入主要教育者" />
      </el-form-item>
      <el-form-item label="陪学原因" prop="whyNeed"  class="!w-72%">
        <el-input v-model="formData.whyNeed" show-word-limit placeholder="为什么需要陪学" />
      </el-form-item>

      <el-form-item label="暂停接单" prop="isSuspend">
        <el-switch
          v-model="formData.isSuspend"
          inline-prompt
          :active-value="1"
          :inactive-value="0"
          active-text="Y"
          inactive-text="N"
        />
      </el-form-item>

      <el-form-item label="是否已建群" prop="isGroupChat">
        <el-switch
          v-model="formData.isGroupChat"
          inline-prompt
          :active-value="1"
          :inactive-value="0"
          active-text="Y"
          inactive-text="N"
        />
      </el-form-item>

      <el-form-item label="当前负责人" prop="headCurrent">
        <el-select v-model="formData.headCurrent"
                   clearable
                   filterable
                   class="!w-240px"
                   placeholder="请输入当前负责人">
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="运营负责人" prop="headOperate">
        <el-select v-model="formData.headOperate"
                   clearable
                   filterable
                   class="!w-240px"
                   placeholder="请输入运营负责人">
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="市场负责人" prop="headMarket">
        <el-select v-model="formData.headMarket"
                   clearable
                   filterable
                   class="!w-240px"
                   placeholder="请输入市场负责人">
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="原陪学需求" prop="orgNeedsContent" class="!w-100%" >
        <el-input v-model="formData.orgNeedsContent" type="textarea" rows="4" placeholder="请输入原家长陪学需求" disabled />
      </el-form-item>
      <el-form-item label="性别要求" prop="requireSex" class="!w-400px">
        <el-radio-group v-model="formData.requireSex" class="item-content">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_REQUIRE_SEX)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="老师能力硬性要求" prop="hardRequireAbility">
        <el-checkbox-group v-model="formData.hardRequireAbility" class="item-content">
          <el-checkbox
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_HARD_REQUIRE_ABILITY)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="老师能力要求补充" prop="requireAbilityExtra" class="!w-100%">
        <el-input type="textarea" rows="2" v-model="formData.requireAbilityExtra" placeholder="请输入老师能力要求补充" />
      </el-form-item>
      <el-form-item label="需求标签" prop="needsTags">
        <el-checkbox-group v-model="formData.needsTags" class="item-content">
          <el-checkbox
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_NEEDS_TAGS)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="需求侧重点" prop="needsFocusTags">
        <el-checkbox-group v-model="formData.needsFocusTags" class="item-content">
          <el-checkbox
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_NEEDS_FOCUS_TAGS)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="陪学要求" prop="demandContent" class="!w-100%">
        <el-input v-model="formData.demandContent" type="textarea" rows="5" maxlength="1000"  show-word-limit placeholder="请输入陪学要求" />
      </el-form-item>
      <el-form-item label="周次" prop="timesWeek" class="form-item">
        <el-input v-model="formData.timesWeek" placeholder="请输入周次" />
      </el-form-item>
      <el-form-item label="陪学时间类型" prop="isOnWeekend" label-width="100px" class="form-item">
        <el-radio-group v-model="formData.isOnWeekend">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_IS_ON_WEEKEND)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="陪学时间范围" prop="timeRange" class="!w-100%" label-width="110px">
        <el-checkbox-group v-model="formData.timeRange" class="!w-500px">
          <el-checkbox-button
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_TIME_RANGE)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-checkbox-button>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="下次跟踪时间" prop="trackingTime">
        <el-date-picker
          v-model="formData.trackingTime"
          type="datetime"
          value-format="x"
          placeholder="选择跟踪时间"
        />
      </el-form-item>
      <el-form-item label="体验时间" prop="expTime">
        <el-date-picker
          v-model="formData.expTime"
          type="datetime"
          value-format="x"
          placeholder="选择体验时间"
        />
      </el-form-item>
      <el-form-item label="是否确认体验时间" prop="isConfirmExpTime" label-width="130px" class="ml-10">
        <el-radio-group v-model="formData.isConfirmExpTime">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_YES_OR_ON)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="沟通结果" prop="communicateResult" class="!w-100%">
        <el-radio-group v-model="formData.communicateResult">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_COMMUNICATE_RESULT)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="孩子性别" prop="kidSex" >
        <el-radio-group v-model="formData.kidSex" class="item-content">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_SEX)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="孩子年级阶段" prop="kidStage" class="form-item ml-10px" label-width="120px">
        <el-select v-model="formData.kidStage" placeholder="请选择孩子年级阶段">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_KID_STAGE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="沟通前提条件" prop="communicatePre" class="w!-100%">
        <el-checkbox-group v-model="formData.communicatePre" class="item-content">
          <el-checkbox
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_COMMUNICATE_PRE)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="孩子称呼" prop="kidNickName" class="form-item">
        <el-input v-model="formData.kidNickName" placeholder="请输入孩子称呼" />
      </el-form-item>
      <el-form-item label="与孩子关系" prop="relationship" class="form-item">
        <el-input v-model="formData.relationship" placeholder="请输入家长与孩子关系" />
      </el-form-item>
      <el-form-item label="学校名称" prop="schoolName" class="form-item">
        <el-input v-model="formData.schoolName" placeholder="请输入学校名称" />
      </el-form-item>
      <el-form-item label="学校性质" prop="schoolNature" class="form-item">
        <el-select v-model="formData.schoolNature" placeholder="请选择学校性质">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_SCHOOL_NATURE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="大致排名" prop="ranking" class="form-item">
        <el-select v-model="formData.ranking" placeholder="请选择大致排名">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_RANKING)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="孩子性格" prop="kidChr" >
        <el-segmented v-model="formData.kidChr" :options="getStrDictOptions(DICT_TYPE.ALS_KID_CHR)" />
      </el-form-item>

      <el-form-item label="性格补充" prop="kidChrExtra" class="!w-538px">
        <el-input v-model="formData.kidChrExtra" placeholder="请输入孩子性格补充" />
      </el-form-item>
      <el-form-item label="孩子兴趣" prop="kidInt">
        <el-input v-model="formData.kidInt" placeholder="请输入孩子兴趣" />
      </el-form-item>
      <el-form-item label="兴趣补充" prop="kidIntExtra" class="!w-510px">
        <el-input v-model="formData.kidIntExtra" placeholder="请输入孩子兴趣补充" />
      </el-form-item>
      <el-form-item label="邀请人" prop="inviterId">
        <el-input v-model="formData.inviterId" placeholder="请输入邀请人" />
      </el-form-item>
      <el-form-item label="跟踪备注标签">
        <el-checkbox-group v-model="formData.trackingRemarkTags" class="item-content">
          <el-checkbox
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_TRACE_NOTE)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="跟踪备注补充" prop="orderRemarkExtra" class="!w-100%">
        <el-input v-model="formData.orderRemarkExtra" type="textarea" rows="5" maxlength="200"  show-word-limit placeholder="请输入跟踪备注补充" />
      </el-form-item>
      <el-form-item label="订单备注" prop="orderRemark" class="!w-100%">
        <el-input v-model="formData.orderRemark" type="textarea" rows="5" maxlength="200"  show-word-limit placeholder="订单备注仅对内部运营人员展示" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm(false)" type="primary" :disabled="formLoading">保存并关闭</el-button>
      <el-button @click="submitForm(true)" type="primary" :disabled="formLoading">保存</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { OrderApi, OrderVO } from '@/api/als/order'
import {defaultProps} from "@/utils/tree";
import * as AreaApi from "@/api/system/area";
import * as UserApi from "@/api/system/user";

/** 陪学订单 表单 */
defineOptions({ name: 'OrderForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const userList = ref<UserApi.UserVO[]>([]) // 用户列表
const areaList = ref([]) // 地区列表
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  orderId: undefined,
  orderNo: undefined,
  orderType: undefined,
  lessonType: undefined,
  customerId: undefined,
  customerPhone: undefined,
  orderAreaId: undefined,
  orderAddress: undefined,
  isSuspend: undefined,
  isGroupChat: undefined,
  orderRemarkExtra: undefined,
  orderRemark: undefined,
  headCurrent: undefined,
  headOperate:  undefined,
  headMarket:  undefined,
  orgNeedsContent: undefined,
  requireSex: undefined,
  hardRequireAbility: [],
  requireAbilityExtra: undefined,
  needsTags: [],
  needsFocusTags: [],
  demandContent: undefined,
  timesWeek: undefined,
  isOnWeekend: undefined,
  timeRange: [],
  trackingTime: undefined,
  expTime: undefined,
  isConfirmExpTime: undefined,
  kidSex: undefined,
  kidStage: undefined,
  communicatePre: [],
  communicateResult: undefined,
  whyNeed: undefined,
  primaryEducator: undefined,
  kidNickName: undefined,
  relationship: undefined,
  schoolName: undefined,
  schoolNature: undefined,
  ranking: undefined,
  kidChr: undefined,
  kidChrExtra: undefined,
  kidInt: undefined,
  kidIntExtra: undefined,
  inviterId: undefined,
  trackingRemarkTags:undefined
})
const formRules = reactive({
  orderType: [{ required: true, message: '订单类型不能为空', trigger: 'change' }],
  lessonType: [{ required: true, message: '课程类型不能为空', trigger: 'change' }],
  customerId: [{ required: true, message: '家长ID不能为空', trigger: 'blur' }],
  customerPhone: [{ required: true, message: '手机号不能为空', trigger: 'blur' }],
  orderAreaId: [{ required: true, message: '区域不能为空', trigger: 'blur' }],
  orderAddress: [{ required: true, message: '详细地址不能为空', trigger: 'blur' }],
  isSuspend: [{ required: true, message: '是否暂停接单不能为空', trigger: 'blur' }],
  isGroupChat: [{ required: true, message: '是否已建群不能为空', trigger: 'blur' }],
  requireSex: [{ required: true, message: '老师性别要求不能为空', trigger: 'change' }],
  hardRequireAbility: [{ required: true, message: '老师能力硬性要求不能为空', trigger: 'blur' }],
  needsTags: [{ required: true, message: '需求标签不能为空', trigger: 'blur' }],
  demandContent: [{ required: true, message: '陪学要求不能为空', trigger: 'blur' }],
  timeRange: [{ required: true, message: '陪学时间范围不能为空', trigger: 'blur' }],
  kidSex: [{ required: true, message: '孩子性别不能为空', trigger: 'blur' }],
  kidStage: [{ required: true, message: '孩子年级阶段不能为空', trigger: 'change' }],
  communicateResult: [{ required: true, message: '沟通结果不能为空', trigger: 'blur' }],
  kidNickName: [{ required: true, message: '孩子称呼不能为空', trigger: 'blur' }],
  relationship: [{ required: true, message: '家长与孩子关系不能为空', trigger: 'blur' }],
  schoolNature: [{ required: true, message: '学校性质不能为空', trigger: 'change' }],
  kidChr: [{ required: true, message: '孩子性格不能为空', trigger: 'change' }],
  headOperate : [{ required: true, message: '运营负责人不能为空', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await OrderApi.getOrder(id)
    } finally {
      formLoading.value = false
    }
  }
  // 获得地区列表
  areaList.value = await AreaApi.getAreaTree()
  userList.value = await UserApi.getSimpleUserList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用n于操作成功后的回调
const submitForm = async (closeDialog: boolean) => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as OrderVO
    if (formType.value === 'create') {
      await OrderApi.createOrder(data)
      message.success(t('common.createSuccess'))
    } else {
      await OrderApi.updateOrder(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = closeDialog
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    orderId: undefined,
    orderNo: undefined,
    orderType: undefined,
    lessonType: undefined,
    customerId: undefined,
    customerPhone: undefined,
    orderAreaId: undefined,
    orderAddress: undefined,
    isSuspend: undefined,
    isGroupChat: undefined,
    orderRemarkExtra: undefined,
    orderRemark: undefined,
    headCurrent: undefined,
    headOperate:  undefined,
    headMarket:  undefined,
    orgNeedsContent: undefined,
    requireSex: undefined,
    hardRequireAbility: [],
    requireAbilityExtra: undefined,
    needsTags: [],
    needsFocusTags: [],
    demandContent: undefined,
    timesWeek: undefined,
    isOnWeekend: undefined,
    timeRange: [],
    trackingTime: undefined,
    expTime: undefined,
    isConfirmExpTime: undefined,
    kidSex: undefined,
    kidStage: undefined,
    communicatePre: [],
    communicateResult: undefined,
    whyNeed: undefined,
    primaryEducator: undefined,
    kidNickName: undefined,
    relationship: undefined,
    schoolName: undefined,
    schoolNature: undefined,
    ranking: undefined,
    kidChr: undefined,
    kidChrExtra: undefined,
    kidInt: undefined,
    kidIntExtra: undefined,
    inviterId: undefined,
    trackingRemarkTags:undefined
  }
  formRef.value?.resetFields()
}
</script>

<style lang="scss" scoped>
.form-item {
  width: 253px !important;
}
:deep(.el-form-item){
  margin-right: 0;
}

.item-content{
  border: 1px dashed #87bdff;
  padding: 10px;
}
:deep(.el-checkbox-button__inner){
  border-left: var(--el-border);
  border-right: var(--el-border);
  border-radius: var(--el-border-radius-base);
  margin: 2px 2px;
}
</style>
