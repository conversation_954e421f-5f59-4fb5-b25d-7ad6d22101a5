<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="用户ID" prop="memberId">
        <el-input v-model="formData.memberId" placeholder="请输入用户ID" />
      </el-form-item>
      <el-form-item label="邀请人ID" prop="inviteMemberId">
        <el-input v-model="formData.inviteMemberId" placeholder="请输入邀请人ID" />
      </el-form-item>
      <el-form-item label="邀请人类型" prop="inviteMemberType">
        <el-select v-model="formData.inviteMemberType" placeholder="请选择邀请人类型">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_INVITE_MEMBER_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="邀请时间" prop="inviteTime">
        <el-date-picker
          v-model="formData.inviteTime"
          type="date"
          value-format="x"
          placeholder="选择邀请时间"
        />
      </el-form-item>
      <el-form-item label="状态" prop="awardStatus">
        <el-select v-model="formData.awardStatus" placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_AWARD_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="现金" prop="price">
        <el-input v-model="formData.price" placeholder="请输入现金" />
      </el-form-item>
      <el-form-item label="钱包流水ID" prop="walletTransactionId">
        <el-input v-model="formData.walletTransactionId" placeholder="请输入钱包流水ID" />
      </el-form-item>
      <el-form-item label="课时数" prop="lessonPeriod">
        <el-input v-model="formData.lessonPeriod" placeholder="请输入课时数" />
      </el-form-item>
      <el-form-item label="课时包ID" prop="customerPackageId">
        <el-input v-model="formData.customerPackageId" placeholder="请输入课时包ID" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { InviteApi, InviteVO } from '@/api/als/invite'

/** 邀请 表单 */
defineOptions({ name: 'InviteForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  inviteId: undefined,
  memberId: undefined,
  inviteMemberId: undefined,
  inviteMemberType: undefined,
  inviteTime: undefined,
  awardStatus: undefined,
  price: undefined,
  walletTransactionId: undefined,
  lessonPeriod: undefined,
  customerPackageId: undefined,
  remark: undefined
})
const formRules = reactive({
  inviteMemberType: [{ required: true, message: '邀请人类型不能为空', trigger: 'change' }],
  inviteTime: [{ required: true, message: '邀请时间不能为空', trigger: 'blur' }],
  awardStatus: [{ required: true, message: '状态不能为空', trigger: 'change' }],
  remark: [{ required: true, message: '备注不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await InviteApi.getInvite(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as InviteVO
    if (formType.value === 'create') {
      await InviteApi.createInvite(data)
      message.success(t('common.createSuccess'))
    } else {
      await InviteApi.updateInvite(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    inviteId: undefined,
    memberId: undefined,
    inviteMemberId: undefined,
    inviteMemberType: undefined,
    inviteTime: undefined,
    awardStatus: undefined,
    price: undefined,
    walletTransactionId: undefined,
    lessonPeriod: undefined,
    customerPackageId: undefined,
    remark: undefined
  }
  formRef.value?.resetFields()
}
</script>