<script setup lang="ts">
defineOptions({ name: 'WeeklySchedule' })
const weekDays = ref([ '周一', '周二', '周三', '周四', '周五', '周六', '周日'])
const timeSlots = ref(['上午', '下午', '晚上'])
interface ScheduleItem {
  0: number; // day
  1: number; // time slot
}

interface Props {
  scheduleData: ScheduleItem[]
}

const props = defineProps<Props>()

const getScheduleStatus = (day, time) => {
  const scheduleItem = props.scheduleData.find(item => item[0] === day && item[1] === time);
  // 可使用圈 否则使用杠
  // return scheduleItem ? '⭕️' : '❌'; 
  return scheduleItem ? '⭕️' : '—'; 
  
}
</script>

<template>
  <div v-for="(day, index) in weekDays" :key="index">
    <span class="column-label">{{ day }}：</span>
    <span class="column-value mr-2" v-for="(slot, timeIndex) in timeSlots" :key="timeIndex">
      {{ slot }} : {{ getScheduleStatus(index + 1, timeIndex + 1) }}
    </span>
  </div>
</template>

<style scoped lang="scss">
.weekly-schedule {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}
.day-schedule {
  //border: 1px solid #ddd;
  //padding: 10px;
  //border-radius: 5px;
}
.time-slots {
  display: flex;
  flex-direction: row;
}
</style>
