<script setup lang="ts">
import {useTagsViewStore} from "@/store/modules/tagsView";
import ResumeWeeklySchedule from "@/views/als/teacher/ResumeWeeklySchedule.vue";
import {DICT_TYPE, getStrDictOptions} from "@/utils/dict";
import {TeacherApi} from "@/api/als/teacher";
import {formatToDate, formatToDateTime} from "@/utils/dateUtil";

defineOptions({ name: 'TeacherResume' })

const route = useRoute()
const message = useMessage()
const id = Number(route.params.id) // 编号
const timeRange = [11,12,13,72]
/** 初始化 */
const { delView } = useTagsViewStore() // 视图操作
const { currentRoute } = useRouter() // 路由
onMounted(async () => {
  if (!id) {
    delView(unref(currentRoute))
    return
  }
  const res = await TeacherApi.getResume(id)
  // 合并默认值，防止缺字段
  detail.value = {
    ...detail.value,
    ...res,
    abilityInfo: {
      ...detail.value.abilityInfo,
      ...(res.abilityInfo || {})
    },
    baseInfo: {
      ...detail.value.baseInfo,
      ...(res.baseInfo || {})
    },
    interviewInfo: {
      ...detail.value.interviewInfo,
      ...(res.interviewInfo || {})
    },
    orderInfo: {
      ...detail.value.orderInfo,
      ...(res.orderInfo || {})
    },
    serviceSummaryList: res.serviceSummaryList || detail.value.serviceSummaryList
  }
})
const tableRowClassName = ({ row, rowIndex }) => {
  if (rowIndex === 0) {
    return 'red-row';
  }
  return '';
}

const detail = ref({
  "baseInfo":{
    "teacherName":'',
    "universityName":'',
    "profession":'',
    "degree":0,
    "entryYear":0,
    "nativeAreaName":"",
    "acceptableTime":"",
    "address":"",
    "startOrderTime":"",
    "operationRemark":"" // 运营负责人备注
  },
  "abilityInfo":{
    "englishSpoken":"", // 英语口语
    "foreignLanguage":[],// 外语
    "foreignLanguageSpoken":[],// 外语口语
    "schoolAwards":[],// 在校获奖情况
    "schoolAwardsLevel":[],// 在校获奖情况级别
    "schoolAwardsExtra":"", //获奖补充
    "forte":[],// 特长
    "forteExtra":"", // 特长补充
    "teacherCertificateImgUrl":"", // 教师资格证
    "teachScopeRank":[],// 擅长
    "teachingMethod":"",// 教学方法
  },
  "interviewInfo":{
    "interviewTime":"",
    "interviewerName":"",
    "interviewerEvaluate":"",
    "level":0,
    "qualityBasic": [],
    "qualityComprehensive": [],
    "qualityLecture": [],
  },
  "orderInfo":{
    "orderCount":0,
    "failCount":0,
    "successRate":"",
  },
  "serviceSummaryList":[{
    "customerName": "",
    "serviceClassHour": 0,
    "lessonRecordNum": 0,
    "goodNum": "",
    "mediumNum": "",
    "badNum": "",
    "leaveNum": "",
    "leaveRadio": "",
    "childInfo": "小学三年级+实验小学+是似懂非懂地方鼎折覆餗阿斯顿发斯蒂芬阿斯顿发山东",
    "kidStage":0,
    "schoolName":"",
    "demandContent":"",
    "lastServiceTime": ""
  }]
})

const getScoreLabel = (score) => {
  const scoreDict = {
    1: '优秀',
    2: '良好',
    3: '差'
  }
  return scoreDict[score] || '未评分'
}


const failList = [
  {
    "customerName": "张三",
    "leaveReason": "因家中有事，需请假一天",
    "changeReason": "希望换一个风格的法师法师大法师懂法守法手打老师",
    "serviceRemark": "正在跟进家长的需求",
    "zeroReason": "课时已用尽，需重新阿斯顿法师打发的说法手打购买"
  },
  {
    "customerName": "李四",
    "leaveReason": "孩子生病，无法上课",
    "changeReason": "希望换老师以获发生的发多少得不同的教学方法",
    "serviceRemark": "已联系家长，确认后阿斯顿法师打发续安排",
    "zeroReason": "课时阿斯顿法师打发斯蒂芬已用完，等待续费"
  }
]
</script>

<template>
  <div class="!w-100% flex flex-justify-center" >
  <ContentWrap class="!w-1100px">
    <div class="flex flex-justify-center">
      <div class="body">
<!--        个人信息-->
        <div class="item">
          <div class="resume-title"><Icon icon="ep:add-location" /> 基础信息</div>
          <div class="resume-content">
            <div class="person-img"><el-image fit="contain" :src="detail.baseInfo?.personImgUrl"/></div>
            <div class="person-info">
              <div class="teacher-name">{{ detail.baseInfo?.teacherName }}</div>
              <div class="teacher-detail">
                <div>
                  <span class="person-info-label">学校：</span>
                  <span class="person-info-value">{{ detail.baseInfo?.universityName }}</span>
                </div>
                <div>
                  <span class="person-info-label">年级：</span>
                  <span class="person-info-value">
                     <dict-tag-text :type="DICT_TYPE.ALS_DEGREE" :value="detail.baseInfo?.degree" />
                    {{ detail.baseInfo?.entryYear }}
                  </span>
                </div>
                <div>
                  <span class="person-info-label">专业：</span>
                  <span class="person-info-value">{{ detail.baseInfo?.profession }}</span>
                </div>
                <div>
                  <span class="person-info-label">籍贯：</span>
                  <span class="person-info-value">{{ detail.baseInfo?.nativeAreaName }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
<!--        能力信息-->
        <div class="item">
          <div class="resume-title"><Icon icon="ep:video-camera" /> 能力信息</div>
          <div class="ability-content">
            <div class="ability-item">
              <div class="ability-label">外语：</div>
              <div class="ability-value">
                <span v-for="(tag,index) in detail.abilityInfo?.foreignLanguage" :key="tag" class="mr-1">
                    <dict-tag-text :type="DICT_TYPE.ALS_FOREIGN_LANGUAGE" :value="tag" />
                    <span>-</span>
                    <dict-tag-text :type="DICT_TYPE.ALS_TEACHER_ABILITY_SPOKEN" :value="detail.abilityInfo?.foreignLanguageSpoken[index]" />
                </span>
              </div>
            </div>

            <div class="ability-item">
              <div class="ability-label">在校获奖情况：</div>
              <div class="ability-value">
                <div v-for="(tag,index) in detail.abilityInfo?.schoolAwards" :key="index">
                  <span>
                     <dict-tag-text :type="DICT_TYPE.ALS_SCHOOL_AWARDS" :value="tag" />
                     <span>-</span>
                     <dict-tag-text :type="DICT_TYPE.ALS_SCHOOL_AWARDS_LEVEL" :value="detail.abilityInfo?.schoolAwardsLevel[index]" />
                  </span>
                </div>
                <div>
                  <span>{{ detail.abilityInfo?.schoolAwardsExtra }}</span>
                </div>
              </div>
            </div>

            <div class="ability-item">
              <div class="ability-label">特长：</div>
              <div class="ability-value">
                 <span v-for="tag in detail.abilityInfo?.forte" :key="tag" class="mr-1">
                   <dict-tag-text :type="DICT_TYPE.ALS_FORTE" :value="tag" />
                 </span>
                 <div><span>{{ detail.abilityInfo?.forteExtra }}</span></div>
              </div>
            </div>

            <div class="ability-item">
              <div class="ability-label">教师资格证：</div>
              <div class="ability-value">
                <div class="person-img" v-if="detail.abilityInfo?.teacherCertificateImgUrl">
                  <el-image fit="contain" :src="detail.abilityInfo?.teacherCertificateImgUrl"/>
                </div>
                <div v-else>-</div>
              </div>
            </div>
          </div>

          <div class="ability-rank">
            <span class="ability-label">作业辅导科目擅长顺序：</span>
            <span>{{ detail.abilityInfo?.teachScopeRank?.join(' > ') }}</span>
          </div>
        </div>
<!--        授课信息-->
        <div class="item">
          <div class="resume-title"><Icon icon="ep:briefcase" /> 授课信息</div>
          <div class="teach_content">
            <div class="teach_item">
              <span class="teach_item_label">本市常驻地址：</span>
              <span class="teach_item_value">{{ detail.baseInfo?.address }}</span>
            </div>

            <div class="teach_item">
              <span class="teach_item_label">可接受单程车程：</span>
              <span class="teach_item_value">{{ detail.baseInfo?.acceptableTime }} min</span>
            </div>

            <div class="teach_item">
              <span class="teach_item_label">开始接单时间：</span>
              <span class="teach_item_value">{{ formatToDate(detail.baseInfo?.startOrderTime) }}</span>
            </div>
          </div>

          <div class="teach_time">
            <span class="teach_item_label">可授课时间：</span>
            <div><ResumeWeeklySchedule :scheduleData="detail.baseInfo?.teachTimeRange" /></div>
          </div>
        </div>
<!--        教学方法-->
        <div class="item">
          <div class="resume-title"><Icon icon="ep:briefcase" /> 教学方法</div>
          <div class="teach-method">
            <span>{{ detail.abilityInfo?.teachingMethod }}</span>
          </div>
        </div>
        <!-- 面试信息-->
        <div class="item">
          <div class="resume-title"><Icon icon="ep:briefcase" /> 面试信息</div>
          <div class="interview_content">
            <div class="interview_item">
              <div>
                <span class="interview_item_label">面试时间：</span>
                <span class="interview_item_value">{{ formatToDateTime(detail.interviewInfo?.interviewTime) }}</span>
              </div>
              <div>
                <span class="interview_item_label ml-30px">面试官：</span>
                <span class="interview_item_value">{{ detail.interviewInfo?.interviewerName }}</span>
              </div>
            </div>

            <div class="interview_item">
              <div class="interview_item_label"><span>面试官评价：</span></div>
              <div class="interview_item_value"><span>{{ detail.interviewInfo?.interviewerEvaluate }}</span></div>
            </div>
            <div class="interview_item">
              <div class="interview_item_label"><span>负责人评价：</span></div>
              <div class="interview_item_value"><span>{{ detail.baseInfo?.operationRemark }}</span></div>
            </div>
          </div>
        </div>
        <!-- 面试分数-->
        <div class="item">
          <div class="resume-title"><Icon icon="ep:briefcase" /> 面试分数</div>
          <div class="flex">
            <div class="s-title">老师成绩：</div>
            <span class="s-interview-level"><dict-tag-text :type="DICT_TYPE.ALS_TEACHER_LEVEL" :value="detail.interviewInfo?.level" /></span>
          </div>
          <div>
            <div class="s-title">基本素质</div>
            <div class="s-interview-score">
              <el-table :data="getStrDictOptions(DICT_TYPE.ALS_QUALITY_BASIC)" width="100%" border stripe>
                <el-table-column prop="label" label="基本素质" width="510" align="center" />
                <el-table-column label="打分" width="110" align="center">
                  <template #default="scope">
                    <span>{{ getScoreLabel(detail.interviewInfo?.qualityBasic[scope.$index]) }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="补充说明" align="center" />
              </el-table>
            </div>
          </div>

          <div class="item-lr">
            <div class="item-lr-l">综合素质打分：</div>
            <div class="item-lr-r">
              <span v-for="(tag,index) in getStrDictOptions(DICT_TYPE.ALS_QUALITY_COMPREHENSIVE)" :key="tag" class="mr-8">
                {{ tag.label }}-{{ getScoreLabel(detail.interviewInfo?.qualityBasic[index]) }}
              </span>
            </div>
          </div>

          <div class="item-lr">
            <div class="item-lr-l">试讲打分：</div>
            <div class="item-lr-r">
               <span v-for="(tag,index) in getStrDictOptions(DICT_TYPE.ALS_QUALITY_LECTURE)" :key="tag" class="mr-8">
                {{ tag.label }}-{{ getScoreLabel(detail.interviewInfo?.qualityLecture[index]) }}
              </span>
            </div>
          </div>
        </div>
<!--        平台服务记录-->
        <div class="item">
          <div class="resume-title"><Icon icon="ep:basketball" />平台服务记录</div>
          <div class="s-content-record">
             <div>
               <div class="s-title"><span>体验课情况：</span></div>
               <div class="s-content">
                 <div class="s-content-1">
                   <div class="s-content-num">{{ detail.orderInfo?.orderCount }}</div>
                   <div class="s-content-name">上门数量</div>
                 </div>
                 <div class="s-content-1">
                   <div class="s-content-num">{{ detail.orderInfo?.failCount }}</div>
                   <div class="s-content-name">失败数量</div>
                 </div>
                 <div class="s-content-1">
                   <div class="s-content-num">{{ detail.orderInfo?.successRate }}</div>
                   <div class="s-content-name">成功率</div>
                 </div>
               </div>
               <div class="s-reason">
                 <div class="s-reason-title"><span>体验课失败原因：</span></div>
                 <div class="s-reason-value"><span></span></div>
               </div>
             </div>

              <div>
                <div class="s-title">服务异常汇总：</div>
                <div class="s-fail-table">
                   <el-table :data="failList" border stripe width="100%" size="small" empty-text="无">
                     <el-table-column prop="customerName" label="家长姓名" width="70" header-align="center" align="center"/>
                     <el-table-column prop="leaveReason" label="请假原因" width="180" header-align="center"/>
                     <el-table-column prop="changeReason" label="换老师原因" width="180" header-align="center"/>
                     <el-table-column prop="serviceRemark" label="正在服务备注" header-align="center"/>
                     <el-table-column prop="zeroReason" label="课时用尽原因" width="180" header-align="center"/>
                   </el-table>
                </div>
              </div>

            <div>
              <div class="s-title">服务情况汇总：</div>
              <div class="s-fail-table">
                <el-table :data="detail.serviceSummaryList" border stripe width="100%" size="small" empty-text="无" :row-class-name="tableRowClassName">
                  <el-table-column prop="customerName" label="家长姓名" width="70" header-align="center" align="center"/>
                  <el-table-column prop="serviceClassHour" label="服务课时数" width="80" header-align="center" align="center"/>
                  <el-table-column prop="lessonRecordNum" label="上门次数" width="70" header-align="center" align="center"/>
                  <el-table-column prop="goodNum" label="好评次数" width="70" header-align="center" align="center"/>
                  <el-table-column prop="mediumNum" label="中评次数" width="70" header-align="center" align="center"/>
                  <el-table-column prop="badNum" label="差评次数" width="70" header-align="center" align="center"/>
                  <el-table-column prop="leaveNum" label="请假次数" width="70" header-align="center" align="center"/>
                  <el-table-column prop="leaveRadio" label="请假率" width="60" header-align="center" align="center"/>
                  <el-table-column prop="childInfo" label="孩子信息" width="250" header-align="center" align="left">
                    <template #default="scope">
                      <dict-tag-text :type="DICT_TYPE.ALS_KID_STAGE" :value="scope.row.kidStage" />
<!--                      <span v-for="tag in scope.row.teachScope" :key="tag" class="tag-wrapper">-->
<!--                        <dict-tag :type="DICT_TYPE.ALS_NEEDS_TAGS" :value="tag" />-->
<!--                      </span>-->
                      / {{ scope.row.schoolName }}
                      <div class="max-h-10 overflow-auto">
                        {{ scope.row.demandContent }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="lastServiceTime" label="最后服务时间" width="150" header-align="center" align="center">
                    <template #default="scope">
                      {{ formatToDateTime(scope.row.lastServiceTime) }}
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>

            <div>
              <div class="s-title">家长评价：</div>
              <div class="s-evaluation-content">
                <div>
                  <span class="s-evaluation-label">评价标签：</span>
                  <span class="s-evaluation-value">5星 逻辑框架啊佛挡杀佛阿斯顿发生大法师</span>
                </div>
                <div>
                  <span class="s-evaluation-label">评价内容：</span>
                  <span class="s-evaluation-value">阿斯顿法师打发的说法手打大幅度发</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ContentWrap>
  </div>
</template>

<style scoped lang="scss">
.body{
  width: 1000px;
  //border: #6a6a6a 1px solid;
}
.item{
  margin-bottom: 60px;
}
.resume-title{
  font-weight: bold;
  font-size: 20px;
  padding: 0 0 20px 0;
}
.resume-content{
  padding: 20px 0 40px 40px;
  width: 100%;
  display: flex;
}
.person-img{
  width: 120px;
  height: 160px;
}
.teacher-name{
  font-weight: bold;
  font-size: 28px;
}
.teacher-detail{
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  font-size: 18px;
  height: 100px;
}
.person-info{
  height: 150px;
  padding: 0 30px;
}
.person-info-label{
  font-weight: bold;
  color: #1e83e9;
}
.person-info-value{
  font-weight: bold;
  padding: 0 40px 0 0;
}
.s-content{
  display: flex;
  font-weight: bold;
  height: 80px;
  align-items: center;
  padding: 10px 0 20px 0;
}
.s-content-1{
  width: 100px;
  text-align: center;
}
.s-title{
  font-weight: bold;
  font-size: 18px;
  color: #1e83e9;
  padding: 20px;
}
.s-content-num{
  padding: 10px;
}
.s-content-name{
  font-size: 14px;
  color: #1e83e9;
}
.s-reason{
  padding: 20px 0 40px 20px;
  font-size: 14px;
  display: flex;
}
.s-reason-title{
  font-size: 14px;
  font-weight: bold;
  width: 130px;
  color: #1e83e9;
}
.s-reason-value{
  padding: 0 0 20px 0;
  width: 100%;
}
.s-fail-table{
  padding: 40px 20px;
}
.s-evaluation-content{
  padding: 20px 20px;
}
.s-evaluation-label{
  font-size: 15px;
  color: #1e83e9;
}
.s-evaluation-value{
  font-size: 14px;
}
.ability-content{
  display: flex;
}
.ability-item{
  width: 20%;
  padding: 10px;
}
.ability-rank{
  padding: 0 0 40px 20px;
}
.ability-label{
  font-size: 16px;
  color: #1e83e9;
  font-weight: bold;
}
.ability-value{
  font-size: 14px;
}
.teach_content{
  display: flex;
  padding: 40px 0 0 20px;
}
.teach_item{
  display: flex;
  margin-right: 20px;
}
.teach_time{
  display: flex;
  padding: 10px 0 40px 20px;
}
.teach_item_label{
  font-size: 16px;
  color: #1e83e9;
  font-weight: bold;
}
.teach_item_value{
  font-size: 14px;
}
.interview_content{
  padding: 20px 20px 40px 20px;
}
.interview_item{
  display: flex;
  margin-bottom: 20px;
}
.teach-method{
  padding: 20px 20px 40px 20px;
  font-size: 14px;
}
.interview_item_label{
  width: 120px;
  font-size: 16px;
  color: #1e83e9;
}
.interview_item_value{
  font-size: 14px;
  width: 100%;
}
.s-interview-score{
  padding: 0 20px 20px 20px;
}
.s-interview-level{
  font-size: 16px;
  color: #1e83e9;
  font-weight: bold;
  padding: 20px 0;
  margin-left: -15px;
}
.item-lr{
  display: flex;
  padding: 20px;
}
.item-lr-l{
  font-size: 16px;
  color: #1e83e9;
  font-weight: bold;
  width: 130px;
}
.item-lr-r{
  font-size: 16px;
  font-weight: bold;
  width: 100%;
}
.s-content-record{
  padding: 20px 0;
}
</style>
