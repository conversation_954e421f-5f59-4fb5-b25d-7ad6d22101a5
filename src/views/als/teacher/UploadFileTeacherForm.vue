<script setup lang="ts">

import {Attachment<PERSON><PERSON>, TeacherAttachmentVo} from "@/api/als/attachment";

defineOptions({ name: 'UploadFileTeacherForm' })

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref<TeacherAttachmentVo>({
  teacherId: undefined,
  image1Url: undefined,
  image2Url: undefined,
  image3Url: undefined,
  image4Url: undefined,
  image5Url: undefined,
  image6Url: undefined,
  image7Url: undefined,
  image8Url: undefined,
  image9Url: undefined,
  image10Url: undefined,
  image11Url: undefined,
  image12Url: undefined,
  image13Url: undefined,
  image14Url: undefined,
  image15Url: undefined,

  image1Id: undefined,
  image2Id: undefined,
  image3Id: undefined,
  image4Id: undefined,
  image5Id: undefined,
  image6Id: undefined,
  image7Id: undefined,
  image8Id: undefined,
  image9Id: undefined,
  image10Id: undefined,
  image11Id: undefined,
  image12Id: undefined,
  image13Id: undefined,
  image14Id: undefined,
  image15Id: undefined
});
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (id?: number) => {
  dialogVisible.value = true
  if (id) {
    formLoading.value = true
    try {
      formData.value = await AttachmentApi.queryTeacherAttachment(id);
      debugger
    } finally {
      formLoading.value = false
    }
  }
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TeacherAttachmentVo
    await AttachmentApi.updateTeacherAttachment(data)
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
</script>

<template>
  <Dialog title="上传资料" v-model="dialogVisible" width="80%">
    <el-form
      ref="formRef"
      :model="formData"
      label-width="100px"
      v-loading="formLoading"
    >
      <div style="display: flex;width: 100%;flex-wrap: wrap">
        <el-form-item label="个人照片" prop="image1Url">
          <UploadImgCompress v-model="formData.image1Url"/>
        </el-form-item>

        <el-form-item label="身份证" prop="image2Url">
          <UploadImgCompress v-model="formData.image2Url"/>
        </el-form-item>

        <el-form-item label="学生证" prop="image3Url">
          <UploadImgCompress v-model="formData.image3Url"/>
        </el-form-item>

        <el-form-item label="毕业证书" prop="image4Url">
          <UploadImgCompress v-model="formData.image4Url"/>
        </el-form-item>

        <el-form-item label="学信网学籍" prop="image5Url">
          <UploadImgCompress v-model="formData.image5Url"/>
        </el-form-item>
      </div>
      <div style="display: flex;width: 100%;flex-wrap: wrap">
        <el-form-item label="四级证书" prop="image6Url">
          <UploadImgCompress v-model="formData.image6Url"/>
        </el-form-item>

        <el-form-item label="六级证书" prop="image7Url">
          <UploadImgCompress v-model="formData.image7Url"/>
        </el-form-item>

        <el-form-item label="教师资格证" prop="image8Url">
          <UploadImgCompress v-model="formData.image8Url"/>
        </el-form-item>

        <el-form-item label="钢琴证书" prop="image9Url">
          <UploadImgCompress v-model="formData.image9Url"/>
        </el-form-item>
        
        <el-form-item label="合作协议" prop="image10Url">
          <UploadImgCompress v-model="formData.image10Url"/>
        </el-form-item>

        <el-form-item label="承诺书" prop="image11Url">
          <UploadImgCompress v-model="formData.image11Url"/>
        </el-form-item>

        <el-form-item label="上岗证" prop="image12Url">
          <UploadImgCompress v-model="formData.image12Url"/>
        </el-form-item>

        <el-form-item label="雅思证书" prop="image13Url">
          <UploadImgCompress v-model="formData.image13Url"/>
        </el-form-item>

        <el-form-item label="托福证书" prop="image14Url">
          <UploadImgCompress v-model="formData.image14Url"/>
        </el-form-item>

        <el-form-item label="其他" prop="image15Url">
          <UploadImgCompress v-model="formData.image15Url"/>
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<style scoped lang="scss">

</style>
