<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      inline
      label-width="85px"
    >
      <el-form-item label="老师ID" prop="teacherId">
        <el-input
          v-model="queryParams.teacherId"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="老师姓名" prop="teacherName">
        <el-input
          v-model="queryParams.teacherName"
          placeholder="模糊搜索"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="teacherPhone">
        <el-input
          v-model="queryParams.teacherPhone"
          placeholder="模糊搜索"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="性别" prop="teacherSex">
        <el-select
          v-model="queryParams.teacherSex"
          placeholder="请选择"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_SEX)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      
      <CollapseWithButton
        v-model:collapsed="isCollapsed"
        expand-text="展开"
        collapse-text="收起"
        expand-icon="ep:arrow-down"
        collapse-icon="ep:arrow-up"
      >
      <el-form-item label="微信号" prop="wechat">
        <el-input
          v-model="queryParams.wechat"
          placeholder="模糊搜索"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="身份证号" prop="idNumber">
        <el-input
          v-model="queryParams.idNumber"
          placeholder="模糊搜索"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>

     
        <el-form-item label="出生日期" prop="birth">
          <el-date-picker
            v-model="queryParams.birth"
            value-format="YYYY-MM-DD"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-220px"
          />
        </el-form-item>
        <el-form-item label="政治面貌" prop="politicalStatus">
          <el-select
            v-model="queryParams.politicalStatus"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_POLITICAL_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="籍贯" prop="nativeAreaId">
          <el-cascader
            v-model="queryParams.nativeAreaId"
            :options="areaList"
            :props="areaProps"
            class="!w-240px"
            clearable
            filterable
            :collapse-tags="true"
            :show-all-levels="false"
            placeholder="省市区全检索"
            @keyup.enter="handleQuery"
          />
        </el-form-item>

        <el-form-item label="现住址" prop="address">
          <el-input
            v-model="queryParams.address"
            placeholder="模糊搜索"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>


<!--        <el-form-item label="接单城市" prop="orderCityId">-->
<!--          <el-select-->
<!--v-model="queryParams.orderCityId" class="!w-240px"-->
<!--                     placeholder="请选择" size="default" clearable filterable-->
<!--                     @keyup.enter="handleQuery">-->
<!--            <el-option-->
<!--              v-for="item in orderCityList"-->
<!--              :key="item.value"-->
<!--              :label="item.label"-->
<!--              :value="item.value"-->
<!--            />-->
<!--          </el-select>-->
<!--        </el-form-item>-->
        <el-form-item label="接单区域" prop="orderAreaId">
          <el-cascader
            v-model="queryParams.orderAreaId"
            :options="orderCityAreaOptions"
            :props="orderAreaIdProps"
            class="!w-240px"
            clearable
            filterable
            :collapse-tags="true"
            :show-all-levels="false"
            placeholder="多选，模糊搜索"
            @keyup.enter="handleQuery"
          />
        </el-form-item>

        <el-form-item label="大学名称" prop="universityName">
          <el-input
            v-model="queryParams.universityName"
            placeholder="模糊搜索"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
<!--      <el-form-item label="高校城市" prop="universityCityId">-->
<!--        <el-input-->
<!--          v-model="queryParams.universityCityId"-->
<!--          placeholder="请输入高校城市"-->
<!--          clearable-->
<!--          @keyup.enter="handleQuery"-->
<!--          class="!w-240px"-->
<!--        />-->
<!--      </el-form-item>-->
        <el-form-item label="在校状态" prop="schoolStatus">
          <el-select
            v-model="queryParams.schoolStatus"
            placeholder="请选择"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_SCHOOL_STATUS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="专业" prop="profession">
          <el-input
            v-model="queryParams.profession"
            placeholder="模糊搜索"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>

        <el-form-item label="授课范围" prop="teachScope">
          <el-select
            v-model="queryParams.teachScope"
            placeholder="多选，模糊搜索"
            clearable
            multiple
            collapse-tags
            collapse-tags-tooltip
            filterable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_NEEDS_TAGS)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="学历" prop="degree">
          <el-select
            v-model="queryParams.degree"
            placeholder="请选择"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_DEGREE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="入学年份" prop="entryYear">
          <el-input
            v-model="queryParams.entryYear"
            placeholder="请输入，如：2024"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>

        <el-form-item label="有无经验" prop="isHaveExperience">
          <el-select
            v-model="queryParams.isHaveExperience"
            placeholder="请选择"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_YES_OR_ON)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="可接时间" prop="teachTimeRange">
          <el-select
            v-model="queryParams.teachTimeRange"
            placeholder="时间范围，多选，模糊搜索"
            clearable
            multiple
            collapse-tags
            collapse-tags-tooltip
            filterable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_TIME_RANGE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="经验值" prop="expValue">
          <el-input
            v-model="queryParams.expValue"
            placeholder="大于等于"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="信用值" prop="creditValue">
          <el-input
            v-model="queryParams.creditValue"
            placeholder="大于等于"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>

        <el-form-item label="可接受单程" prop="acceptableTime">
          <el-input
            v-model="queryParams.acceptableTime"
            placeholder="大于等于"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          >
            <template #append>分钟</template>
          </el-input>
        </el-form-item>

        <el-form-item label="开始接单" prop="startOrderTime">
          <el-date-picker
            v-model="queryParams.startOrderTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-220px"
          />
        </el-form-item>


        <el-form-item label="来源渠道 " prop="teacherChannel">
          <el-select
            v-model="queryParams.teacherChannel"
            placeholder="请选择 "
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.ALS_SOURCE_CHANNEL)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="跟踪时间" prop="trackingTime">
          <el-date-picker
            v-model="queryParams.trackingTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-220px"
          />
        </el-form-item>
        <el-form-item label="跟踪备注" prop="trackingRemark">
          <el-input
            v-model="queryParams.trackingRemark"
            placeholder="模糊搜索"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>

        <el-form-item label="运营备注" prop="operationRemark">
          <el-input
            v-model="queryParams.operationRemark"
            placeholder="模糊搜索"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>

        <el-form-item label="抢单次数" prop="orderTimes">
          <el-input
            v-model="queryParams.orderTimes"
            placeholder="大于等于"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
        <el-form-item label="审核时间" prop="auditTime">
          <el-date-picker
            v-model="queryParams.auditTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-220px"
          />
        </el-form-item>
        <el-form-item label="是否启用" prop="isEnable">
          <el-select
            v-model="queryParams.isEnable"
            placeholder="请选择"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_YES_OR_ON)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否可接单" prop="isAcceptOrder">
          <el-select
            v-model="queryParams.isAcceptOrder"
            placeholder="请选择"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="dict in getIntDictOptions(DICT_TYPE.ALS_YES_OR_ON)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="注册时间" prop="registerTime">
          <el-date-picker
            v-model="queryParams.registerTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-220px"
          />
        </el-form-item>
        <el-form-item label="最近服务" prop="lastServiceTime">
          <el-date-picker
            v-model="queryParams.lastServiceTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-220px"
          />
        </el-form-item>
        <el-form-item label="最近登录" prop="lastActiveTime">
          <el-date-picker
            v-model="queryParams.lastActiveTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-220px"
          />
        </el-form-item>
        <el-form-item label="最近抢单" prop="acceptOrderTime">
          <el-date-picker
            v-model="queryParams.acceptOrderTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
            class="!w-220px"
          />
        </el-form-item>
        <el-form-item label="openId" prop="openId">
          <el-input
            v-model="queryParams.openId"
            placeholder="请输入"
            clearable
            @keyup.enter="handleQuery"
            class="!w-240px"
          />
        </el-form-item>
<!--      <el-form-item label="创建时间" prop="createTime">-->
<!--        <el-date-picker-->
<!--          v-model="queryParams.createTime"-->
<!--          value-format="YYYY-MM-DD HH:mm:ss"-->
<!--          type="daterange"-->
<!--          start-placeholder="开始日期"-->
<!--          end-placeholder="结束日期"-->
<!--          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"-->
<!--          class="!w-240px"-->
<!--        />-->
<!--      </el-form-item>-->
        <template #after-button>
          <div class="search-button-container ml-10px">
            <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
            <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
            <el-button
              type="primary"
              plain
              @click="openForm('create')"
              v-hasPermi="['als:teacher:create']"
            >
              <Icon icon="ep:plus" class="mr-5px" /> 新增
            </el-button>
            <el-button
              type="success"
              plain
              @click="handleExport"
              :loading="exportLoading"
              v-hasPermi="['als:teacher:export']"
            >
              <Icon icon="ep:download" class="mr-5px" /> 导出
            </el-button>
          </div>
        </template>
      </CollapseWithButton>

    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true"  border highlight-current-row size="small">
      <el-table-column label="基本信息" header-align="left" align="left" width="250" >
        <template #default="scope">
          <div>
            <span class="column-label">老师ID：</span>
            <span class="column-value">{{ scope.row.teacherId }}</span>
          </div>
          <div>
            <span class="column-label">姓名：</span>
            <span class="column-value link" @click="openForm2(scope.row.teacherId)">{{ scope.row.teacherName }}</span>
            <span class="column-value ml-20px" v-if="scope.row.teacherSex > 0">
              <dict-tag-text :type="DICT_TYPE.ALS_SEX" :value="scope.row.teacherSex" />
            </span>
          </div>
          <div>
            <span class="column-label">手机号：</span>
            <span class="column-value">{{ scope.row.teacherPhone }} </span>
          </div>

          <div>
            <span class="column-label">住址：</span>
            <span class="column-value">{{ scope.row.address }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="认证资料" header-align="left" align="center" width="160" >
        <template #default="scope">
          <div class="image_contain" v-if="scope.row.srcList">
            <el-image
              :src="scope.row.url"
              :preview-src-list="scope.row.srcList"
              :initial-index="0"
              preview-teleported
              lazy
              fit="cover"
            />
          </div>
        </template>
      </el-table-column>


      <el-table-column label="身份信息" header-align="left" align="left" width="250" >
        <template #default="scope">
          <div>
            <span class="column-label">身份证号：</span>
            <span class="column-value">{{ scope.row.idNumber }}</span>
          </div>
          <div>
            <span class="column-label">出生日期：</span>
            <span class="column-value">{{ formatDate(scope.row.birth,'YYYY-MM-DD') }} </span>
          </div>
          <div>
            <span class="column-label">籍贯：</span>
            <span class="column-value">{{ scope.row.nativeAreaName }}</span>
          </div>
          <div>
            <span class="column-label">政治面貌：</span>
            <span class="column-value"><dict-tag-text :type="DICT_TYPE.ALS_POLITICAL_STATUS" :value="scope.row.politicalStatus" /></span>
          </div>
          <div>
            <span class="column-label">在校状态：</span>
            <span class="column-value ml-10px" v-if="scope.row.schoolStatus > 0">
               <dict-tag-text :type="DICT_TYPE.ALS_SCHOOL_STATUS" :value="scope.row.schoolStatus" />
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="面试信息" header-align="left" align="left" width="250" >
        <template #default="scope">
          <div>
            <span class="column-label">面试结果：</span>
            <span>
              <span class="column-value link" @click="openForm4(scope.row.teacherId)" v-if="scope.row.interviewerLevel > 0">
                <dict-tag-text :type="DICT_TYPE.ALS_TEACHER_LEVEL" :value="scope.row.interviewerLevel" />
              </span>
            </span>
          </div>

          <div v-if="scope.row.interviewerLevel <= 0">
            <span class="column-label">面试时间：</span>
            <span class="column-value">
              <span v-if="scope.row.interviewTime">{{ formatDate(scope.row.interviewTime) }}</span>
              <span v-else>未预约</span>
            </span>
            <span v-if="scope.row.interviewerLevel > 0">
              <el-button  link type="warning" size="small" class="ml-5px" @click="openForm4(scope.row.teacherId)">查看详情</el-button>
            </span>
          </div>


          <div class="max-h-30 overflow-y-auto">
            <span class="column-label">面试评价：</span>
            <span class="column-value">{{ scope.row.interviewerEvaluate }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="大学信息" header-align="left" align="left" width="200" >
        <template #default="scope">
          <div>
            <span class="column-label">年级：</span>
            <span class="column-value">{{ scope.row.entryYear }}
            <dict-tag-text :type="DICT_TYPE.ALS_DEGREE" :value="scope.row.degree" />
            </span>
          </div>
          <div>
            <span class="column-label">大学名称：</span>
            <span class="column-value">{{ scope.row.universityName }}</span>
          </div>
          <div>
            <span class="column-label">校区：</span>
            <span class="column-value">{{ scope.row.campus }} </span>
          </div>
          <div>
            <span class="column-label">大学城市：</span>
            <span class="column-value">{{ scope.row.universityCity }} </span>
          </div>
          <div>
            <span class="column-label">专业：</span>
            <span class="column-value">{{ scope.row.profession }} </span>
          </div>
          <div>
            <span class="column-label">标签：</span>
            <span v-for="tag in scope.row.idTags" :key="tag" class="column-value mr-2">
              <dict-tag-text :type="DICT_TYPE.ALS_ID_TAGS" :value="tag" />
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="老师经验" header-align="left" align="left" width="250" >
        <template #default="scope">
          <div>
            <span class="column-label">是否有经验：</span>
            <span>   <dict-tag-text :type="DICT_TYPE.ALS_YES_OR_ON" :value="scope.row.isHaveExperience" /></span>
          </div>
          <div>
            <span class="column-label">经验值：</span>
            <span class="column-value">{{scope.row.expValue}}</span>
          </div>
          <div>
            <span class="column-label">抢单次数：</span>
            <span class="column-value">{{scope.row.orderTimes}}</span>
          </div>
          <div>
            <span class="column-label">最近服务时间：</span>
            <span class="column-value">{{ formatDate(scope.row.lastServiceTime) }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="老师状态" header-align="left" align="left" width="150" >
        <template #default="scope">
          <div>
            <span class="column-label">是否启用：</span>
            <span class="column-value"> <dict-tag-text :type="DICT_TYPE.ALS_YES_OR_ON" :value="scope.row.isEnable" /></span>
          </div>
          <div>
            <span class="column-label">是否可接单：</span>
            <span class="column-value"> <dict-tag-text :type="DICT_TYPE.ALS_YES_OR_ON" :value="scope.row.isAcceptOrder" /></span>
          </div>
          <div>
            <span class="column-label">信用值：</span>
            <span class="column-value"> {{scope.row.creditValue}}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="接单条件" header-align="left" align="left" width="300" >
        <template #default="scope">
          <div>
            <span class="column-label">可接单城市：</span>
            <span class="column-value">{{ scope.row.orderCity }} </span>
          </div>
          <div>
            <span class="column-label">可接单区域：</span>
            <span class="column-value">{{ scope.row.orderArea }} </span>
          </div>
          <div>
            <span class="column-label">可接受单程车程：</span>
            <span class="column-value">{{ scope.row.acceptableTime }} </span>
            <span class="ml-5px" v-if="scope.row.acceptableTime">分钟</span>
          </div>
          <div>
            <span class="column-label">开始接单日期：</span>
            <span class="column-value">{{ formatDate(scope.row.startOrderTime) }} </span>
          </div>
          <div>
            <span class="column-label">上岗证编号：</span>
            <span class="column-value"> {{scope.row.workCertificateNo}}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="可接时间范围" header-align="left" align="left" width="250">
        <template #default="scope">
         <WeeklySchedule :scheduleData="parseScheduleData( scope.row.teachTimeRange) " />
        </template>
      </el-table-column>

      <el-table-column label="授课范围" header-align="left" align="left" width="250" >
        <template #default="scope">
          <div>
            <span class="column-label">授课范围：</span>
            <span class="column-value mr-2" v-for="tag in scope.row.teachScope" :key="tag">
              <dict-tag-text :type="DICT_TYPE.ALS_NEEDS_TAGS" :value="tag" />
            </span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="其他信息" header-align="left" align="left" width="250" >
        <template #default="scope">
          <div>
            <span class="column-label">来源：</span>
            <span class="column-value"><dict-tag-text :type="DICT_TYPE.ALS_SOURCE_CHANNEL" :value="scope.row.teacherChannel" /></span>
          </div>
          <div>
            <span class="column-label">微信号：</span>
            <span class="column-value">{{ scope.row.wechat }}</span>
          </div>
          <div>
            <span class="column-label">QQ：</span>
            <span class="column-value">{{ scope.row.qq }}</span>
          </div>
          <div>
            <span class="column-label">openId：</span>
            <span class="column-value">{{ scope.row.openId }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="跟踪记录" header-align="left" align="left" width="250" >
        <template #default="scope">
          <div>
            <span class="column-label">跟踪时间：</span>
            <span class="column-value">{{ formatDate(scope.row.trackingTime) }}</span>
          </div>
          <div>
            <span class="column-label">跟踪备注：</span>
            <span class="column-value">{{ scope.row.trackingRemark }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="时间点" header-align="left" align="left" width="250" >
        <template #default="scope">
          <div>
            <span class="column-label">注册时间：</span>
            <span class="column-value">{{ formatDate(scope.row.registerTime) }}</span>
          </div>
          <div>
            <span class="column-label">审核时间：</span>
            <span class="column-value">{{ formatDate(scope.row.auditTime) }}</span>
          </div>
          <div>
            <span class="column-label">最近抢单时间：</span>
            <span class="column-value">{{ formatDate(scope.row.acceptOrderTime) }}</span>
          </div>
          <div>
            <span class="column-label">最近登录时间：</span>
            <span class="column-value">{{ formatDate(scope.row.lastActiveTime) }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="备注" header-align="left" align="left" width="250" >
        <template #default="scope">
          <div>
            <span class="column-label">运营备注：</span>
            <span class="column-value">{{ scope.row.operationRemark }} </span>
          </div>
          <div>
            <span class="column-label">师资备注：</span>
            <span class="column-value"> </span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="300" fixed="right">
        <template #default="scope">
          <el-button
            plain
            size="small"
            type="primary"
            @click="openForm('update', scope.row.teacherId)"
            v-hasPermi="['als:teacher:update']"
          >
            编辑
          </el-button>
          <el-button
            plain
            size="small"
            type="primary"
            @click="openForm3(scope.row.teacherId)"
            v-hasPermi="['als:teacher:update']"
            class="mb-1px"
          >
            上传资料
          </el-button>

          <el-button
            plain
            size="small"
            type="primary"
            @click="openInterviewAppointment(scope.row.teacherId)"
            v-hasPermi="['als:teacher:update']"
          >
            预约面试
          </el-button>

          <el-button
            plain
            size="small"
            type="primary"
            @click="openForm5( scope.row.teacherId)"
            v-hasPermi="['als:teacher:update']"
          >
            老师简历
          </el-button>

          <el-button
            plain
            size="small"
            type="success"
            @click="handleIssueCertificate(scope.row.teacherId)"
            v-hasPermi="['als:teacher:update']"
          >
            {{ scope.row.workCertificateNo ? '查看上岗证' : '发放上岗证' }}
          </el-button>

          <el-button
            plain
            size="small"
            type="primary"
            @click="updateLocal(scope.row.teacherId)"
          >
            更新坐标
          </el-button>

          <el-button
            plain
            size="small"
            type="danger"
            @click="handleDelete(scope.row.teacherId)"
            v-hasPermi="['als:teacher:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <TeacherForm ref="formRef" @success="getList" />

  <!-- 老师详情弹窗 -->
  <TeacherDetail ref="formRef2" @success="getList" />

  <!-- 上传资料 -->
  <UploadFileTeacherForm ref="formRef3" @success="getList" />

  <!-- 面试资料 -->
  <TeacherInterviewDetail ref="formRef4" @success="getList" />

  <!-- 预约面试 -->
  <TeacherInterviewAppointmentForm ref="interviewAppointmentRef" @success="getList" />

  <!-- 发放上岗证表单 -->
  <TeacherCertificateForm ref="certificateFormRef" @success="getList" />
</template>

<script setup lang="ts">
import * as AreaApi from "@/api/system/area";

import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { formatDate } from '@/utils/formatTime'
import download from '@/utils/download'
import { TeacherApi, TeacherVO } from '@/api/als/teacher'
import TeacherForm from './TeacherForm.vue'
import TeacherDetail from './TeacherDetail.vue'
import UploadFileTeacherForm from './UploadFileTeacherForm.vue'
import TeacherInterviewDetail from '../teacherinterview/TeacherInterviewDetail.vue'
import TeacherResume from './TeacherResume.vue'
import WeeklySchedule from './WeeklySchedule.vue'
import {AreaVO, getOrderCityArea} from "@/api/system/area";
import TeacherInterviewAppointmentForm from '../teacherinterview/TeacherInterviewAppointmentForm.vue'
import { CollapseWithButton } from '@/components/Collapse'
import { TeacherCertificateApi } from '@/api/als/teachercertificate'
import TeacherCertificateForm from './TeacherCertificateForm.vue'

/** 老师类 列表 */
defineOptions({ name: 'Teacher' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const isCollapsed = ref(true); // 新增折叠状态控制

const areaList = ref([]) // 地区列表
const orderCityList = ref<AreaVO[]>([]) // 接单城市列表
const orderCityAreaOptions = ref([]) // 修改：原 getOrderCityArea ref

const loading = ref(true) // 列表的加载中
const list = ref<TeacherVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  teacherId: undefined,
  teacherName: undefined,
  teacherPhone: undefined,
  teacherSex: undefined,
  wechat: undefined,
  idNumber: undefined,
  birth: [], // 修改： undefined to []
  politicalStatus: undefined,
  nativeAreaId: undefined,
  orderCityId: undefined,
  orderAreaId: [],
  universityName: undefined,
  universityCityId: undefined,
  schoolStatus: undefined,
  profession: undefined,
  degree: undefined,
  entryYear: undefined,
  teachScope: undefined,
  teachTimeRange: [],
  expValue: undefined,
  creditValue: undefined,
  workCertificateNo:undefined,
  startOrderTime: [], // 修改： undefined to []
  isHaveExperience: undefined,
  address: undefined,
  acceptableTime: undefined,
  registerTime: [], // 修改： undefined to []
  teacherChannel: undefined,
  trackingTime: [], // 修改： undefined to []
  trackingRemark: undefined,
  orderTimes: undefined,
  auditTime: [], // 修改： undefined to []
  isEnable: undefined,
  isAcceptOrder: undefined,
  idTags: undefined,
  openId: undefined,
  lastServiceTime: [], // 修改： undefined to []
  lastActiveTime: [], // 修改： undefined to []
  acceptOrderTime: [], // 修改： undefined to []
  operationRemark: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const parseScheduleData = (data: any[]): any => {
  // const data = JSON.parse(dataString);
  return data.map(item => {
    const day = Math.floor(item / 10);
    const timeSlot = item % 10;
    return [day, timeSlot];
  });
}
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await TeacherApi.getTeacherPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
  areaList.value = await AreaApi.getAreaTree()
  orderCityList.value = await AreaApi.getOrderCity()
  orderCityAreaOptions.value = await AreaApi.getOrderCityArea() // 修改：使用新的 ref 名
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 详情 */
const formRef2 = ref()
const openForm2 = (id?: number) => {
  formRef2.value.open( id)
}

/** 上传资料 */
const formRef3 = ref()
const openForm3 = (id?: number) => {
  formRef3.value.open(id)
}

/** 面试详情 */
const formRef4 = ref()
const openForm4 = (id?: number) => {
  formRef4.value.open(id)
}

/** 预约面试 */
const interviewAppointmentRef = ref()
const openInterviewAppointment = (id?: number) => {
  interviewAppointmentRef.value.open('create', id)
}

const { push } = useRouter()
const openForm5 = (id: number) => {
  push({ name: 'TeacherResume', params: { id } })
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TeacherApi.deleteTeacher(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 更新坐标按钮操作 */
const updateLocal = async (id: number) => {
  try {
    await TeacherApi.updateLocal(id)
    message.success('更新成功')
  } catch {
    message.error('更新失败')
  }
}

/** 发放上岗证相关变量 */
const certificateFormRef = ref()

/** 打开发放上岗证表单 */
const handleIssueCertificate = (teacherId: number) => {
  // 打开上岗证表单
  certificateFormRef.value.open(teacherId)
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await TeacherApi.exportTeacher(queryParams)
    download.excel(data, '老师类.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}
const route = useRoute()
const teacherId = route.query.teacherId
/** 初始化 **/
onMounted(() => {
  if (teacherId) {
    queryParams.teacherId = teacherId
  }
  getList()
})

const areaProps = {
  expandTrigger: 'hover' as const,
  children: 'children',
  label: 'name',
  value: 'id',
  isLeaf: 'leaf',
  emitPath: true, // true 返回所有节点 ，若设置 false，则只返回该节点的值
}

const orderAreaIdProps = {
  expandTrigger: 'hover' as const,
  children: 'children',
  label: 'name',
  value: 'id',
  isLeaf: 'leaf',
  emitPath: false, // 用于 cascader 组件：在选中节点改变时，是否返回由该节点所在的各级菜单的值所组成的数组，若设置 false，则只返回该节点的值
  multiple:true
}
</script>

<style scoped lang="scss">
.image_contain {
  width: 100px;
  height: 100%;
}

.tag-wrapper {
  margin-right: 2px;
}

.search-button-container {
  display: flex;
  justify-content: center;
}

:deep(.el-form-item--default){
  --font-size: 12px;
  margin-bottom: 5px;
}
:deep(.el-form--inline .el-form-item){
  margin-right:5px;
}
</style>
