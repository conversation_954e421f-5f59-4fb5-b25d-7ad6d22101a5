<template>
  <Dialog title="编辑老师信息" v-model="dialogVisible" width="1350px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
      inline
    >
      <el-form-item label="老师姓名" prop="teacherName" class="form-item">
        <el-input v-model="formData.teacherName" />
      </el-form-item>
      <el-form-item label="手机号" prop="teacherPhone" class="form-item">
        <el-input v-model="formData.teacherPhone" />
      </el-form-item>

      <el-form-item label="微信号" prop="wechat"  class="form-item">
        <el-input v-model="formData.wechat" clearable/>
      </el-form-item>
      <el-form-item label="QQ号" prop="qq"  class="form-item">
        <el-input v-model="formData.qq" clearable/>
      </el-form-item>
      <el-form-item label="身份证号" prop="idNumber"  class="form-item">
        <el-input
          v-model="formData.idNumber"
          autocomplete="off"
          maxlength="18"
          placeholder="18位身份证号"
          @input="sfzhChange"
        />
      </el-form-item>
      <el-form-item label="性别" prop="teacherSex" class="form-item">
        <el-radio-group v-model="formData.teacherSex">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_SEX)"
            :key="dict.value"
            :label="dict.value"
            class="!w-35px"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="出生日期" prop="startOrderTime" class="form-item" >
        <el-date-picker
          v-model="formData.birth"
          type="date"
          value-format="x"
        />
      </el-form-item>

      <el-form-item label="政治面貌" prop="politicalStatus" class="form-item">
        <el-select v-model="formData.politicalStatus">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_POLITICAL_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="籍贯" prop="nativeAreaId">
        <el-cascader
          v-model="formData.nativeAreaId"
          :options="areaList"
          :props="areaProps"
          class="!w-230px"
          clearable
          filterable
          placeholder="籍贯"
        />
      </el-form-item>

      <el-form-item label="接单城市" prop="orderCityId"  class="form-item">
        <el-select v-model="formData.orderCityId" class="!w-160px" size="default" clearable filterable >
          <el-option
            v-for="item in orderCityList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="接单区域" prop="orderAreaId" class="!w-100%">
        <el-cascader
          v-model="formData.orderAreaId"
          :options="getOrderCityArea"
          :props="orderAreaIdProps"
          class="!w-100%"
          clearable
          filterable
        />
      </el-form-item>

      <el-form-item label="大学名称" prop="universityName"  class="form-item">
        <el-input v-model="formData.universityName" />
      </el-form-item>
      <el-form-item label="校区" prop="campus"  class="form-item">
        <el-input v-model="formData.campus" />
      </el-form-item>
      <el-form-item label="高校城市" prop="universityCityId"  class="form-item">
        <el-cascader
          v-model="formData.universityCityId"
          :options="areaList"
          :props="areaProps"
          class="!w-250px"
          clearable
          filterable
        />
      </el-form-item>
      <el-form-item label="在校状态" prop="schoolStatus" class="form-item">
        <el-select v-model="formData.schoolStatus" >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_SCHOOL_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="专业" prop="profession" class="form-item">
        <el-input v-model="formData.profession" />
      </el-form-item>
      <el-form-item label="学历" prop="degree"  class="form-item">
        <el-select v-model="formData.degree" >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_DEGREE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="入学年份" prop="entryYear"  class="form-item">
        <el-input v-model="formData.entryYear" />
      </el-form-item>
      <el-form-item label="授课范围" prop="teachScope">
        <el-checkbox-group v-model="formData.teachScope">
          <el-checkbox-button
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_NEEDS_TAGS)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-checkbox-button>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="陪学时间" prop="teachTimeRange">
        <el-checkbox-group v-model="formData.teachTimeRange" style="width: 500px">
          <el-checkbox-button
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_TIME_RANGE)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-checkbox-button>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="可接受单程车程" prop="acceptableTime" class="!w-350px" label-width="180px">
        <el-input
          v-model="formData.acceptableTime"
          clearable
          class="!w-400px"
        >
          <template #append>分钟</template>
        </el-input>
      </el-form-item>
      <el-form-item label="信用值" prop="creditValue">
        <el-input-number controls-position="right" v-model="formData.creditValue" />
      </el-form-item>
      <el-form-item label="经验值" prop="expValue"  class="form-item">
        <el-input-number controls-position="right" v-model="formData.expValue" />
      </el-form-item>
      <el-form-item label="开始接单日期" prop="startOrderTime"  class="form-item">
        <el-date-picker
          v-model="formData.startOrderTime"
          type="date"
          value-format="x"
        />
      </el-form-item>
      <el-form-item label="有无经验" prop="isHaveExperience"  class="form-item">
        <el-radio-group v-model="formData.isHaveExperience">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_YES_OR_ON)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="现住址（区）" prop="addressAreaId">
        <el-cascader
          v-model="formData.addressAreaId"
          :options="areaList"
          :props="areaProps"
          class="!w-230px"
          clearable
          filterable
          placeholder="区"
        />
      </el-form-item>
      
      <el-form-item label="详细住址" prop="address"  class="!w-680px">
        <el-input v-model="formData.address" />
      </el-form-item>

      <el-form-item label="注册时间" prop="registerTime" class="form-item">
        <el-date-picker
          v-model="formData.registerTime"
          type="date"
          value-format="x"
        />
      </el-form-item>
      <el-form-item label="来源渠道 " prop="teacherChannel" class="form-item">
        <el-select v-model="formData.teacherChannel">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.ALS_SOURCE_CHANNEL)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="跟踪备注" prop="trackingRemark" class="!w-900px">
        <el-input type="textarea" rows="5" maxlength="500" show-word-limit v-model="formData.trackingRemark" />
      </el-form-item>
      <el-form-item label="跟踪时间" prop="trackingTime" class="form-item">
        <el-date-picker
          v-model="formData.trackingTime"
          type="datetime"
          value-format="x"
        />
      </el-form-item>
      <el-form-item label="抢单次数" prop="orderTimes" class="form-item">
        <el-input v-model="formData.orderTimes" />
      </el-form-item>
      <el-form-item label="审核时间" prop="auditTime" class="form-item">
        <el-date-picker
          v-model="formData.auditTime"
          type="datetime"
          value-format="x"
        />
      </el-form-item>
      <el-form-item label="是否启用" prop="isEnable">
        <template #default>
          <el-switch
            v-model="formData.isEnable"
            inline-prompt
            active-text="是"
            inactive-text="否"
            :active-value="1"
            :inactive-value="0"
          />
        </template>
      </el-form-item>

      <el-form-item label="是否可接单" prop="isAcceptOrder" class="form-item">
        <el-select
          v-model="formData.isAcceptOrder"
          clearable
          class="!w-200px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_YES_OR_ON)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="身份标签" prop="idTags" class="!w-100%">
        <el-checkbox-group v-model="formData.idTags">
          <el-checkbox
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_ID_TAGS)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="openId" prop="openId" class="form-item">
        <el-input v-model="formData.openId" placeholder="请输入openId" />
      </el-form-item>
      <el-form-item label="最后服务时间" prop="lastServiceTime" class="form-item">
        <el-date-picker
          v-model="formData.lastServiceTime"
          type="datetime"
          value-format="x"
        />
      </el-form-item>
      <el-form-item label="最后登录时间" prop="lastActiveTime" class="form-item">
        <el-date-picker
          v-model="formData.lastActiveTime"
          type="datetime"
          value-format="x"
        />
      </el-form-item>
      <el-form-item label="最近抢单时间" prop="acceptOrderTime" class="form-item">
        <el-date-picker
          v-model="formData.acceptOrderTime"
          type="datetime"
          value-format="x"
        />
      </el-form-item>
      <el-form-item label="运营备注" prop="operationRemark" class="!w-100%">
        <el-input  type="textarea" rows="5" maxlength="500" show-word-limit v-model="formData.operationRemark" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm(false)" type="primary" :disabled="formLoading">保存并关闭</el-button>
      <el-button @click="submitForm(true)" type="primary" :disabled="formLoading">保存</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { TeacherApi, TeacherVO } from '@/api/als/teacher'
import * as AreaApi from "@/api/system/area";
import {AreaVO} from "@/api/system/area";
import {defaultProps} from "@/utils/tree";

/** 老师类 表单 */
defineOptions({ name: 'TeacherForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const areaList = ref([]) // 地区列表
const orderCityList = ref<AreaVO[]>([]) // 接单城市列表
const getOrderCityArea = ref([]) // 地区列表

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  teacherId: undefined,
  teacherName: undefined,
  teacherPhone: undefined,
  teacherSex: undefined,
  wechat: undefined,
  qq: undefined,
  idNumber: undefined,
  birth: undefined,
  politicalStatus: undefined,
  nativeAreaId: undefined,
  orderCityId: undefined,
  orderAreaId: undefined,
  universityName: undefined,
  campus: undefined,
  universityCityId: undefined,
  schoolStatus: undefined,
  profession: undefined,
  degree: undefined,
  entryYear: undefined,
  teachScope: undefined,
  teachTimeRange:[],
  expValue: undefined,
  creditValue: undefined,
  startOrderTime: undefined,
  isHaveExperience: undefined,
  address: undefined,
  addressAreaId: undefined,
  acceptableTime: undefined,
  registerTime: undefined,
  teacherChannel: undefined,
  trackingTime: undefined,
  trackingRemark: undefined,
  orderTimes: undefined,
  auditTime: undefined,
  isEnable: undefined,
  isAcceptOrder: undefined,
  idTags: [],
  openId: undefined,
  lastServiceTime: undefined,
  lastActiveTime: undefined,
  acceptOrderTime: undefined,
  operationRemark: undefined
})
const formRules = reactive({
  teacherName: [{ required: true, message: '老师姓名不能为空', trigger: 'blur' }],
  teacherPhone: [{ required: true, message: '手机号不能为空', trigger: 'blur' }],
  wechat: [{ required: true, message: '微信号不能为空', trigger: 'blur' }],
  qq: [{ required: true, message: 'QQ不能为空', trigger: 'blur' }],
  idNumber: [{ required: true, message: '身份证不能为空', trigger: 'blur' }],
  teacherSex: [{ required: true, message: '性别不能为空', trigger: 'blur' }],
  nativeAreaId: [{ required: true, message: '籍贯不能为空', trigger: 'blur' }],
  orderCityId: [{ required: true, message: '接单城市不能为空', trigger: 'blur' }],
  orderAreaId: [{ required: true, message: '接单区域不能为空', trigger: 'blur' }],
  universityName: [{ required: true, message: '大学名称不能为空', trigger: 'blur' }],
  universityCityId: [{ required: true, message: '高校城市不能为空', trigger: 'blur' }],
  schoolStatus: [{ required: true, message: '在校状态不能为空', trigger: 'change' }],
  profession: [{ required: true, message: '专业不能为空', trigger: 'blur' }],
  degree: [{ required: true, message: '学历不能为空', trigger: 'change' }],
  entryYear: [{ required: true, message: '入学年份不能为空', trigger: 'blur' }],
  teachScope: [{ required: true, message: '授课范围不能为空', trigger: 'blur' }],
  teachTimeRange: [{ required: true, message: '时间范围不能为空', trigger: 'blur' }],
  expValue: [{ required: true, message: '经验值不能为空', trigger: 'blur' }],
  creditValue: [{ required: true, message: '信用值不能为空', trigger: 'blur' }],
  isHaveExperience: [{ required: true, message: '有无经验不能为空', trigger: 'blur' }],
  address: [{ required: true, message: '本市现住址不能为空', trigger: 'blur' }],
  addressAreaId: [{ required: true, message: '住址区域不能为空', trigger: 'blur' }],
  acceptableTime: [{ required: true, message: '可接受单程车程不能为空', trigger: 'blur' }],
  teacherChannel: [{ required: true, message: '来源渠道 不能为空', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await TeacherApi.getTeacher(id)
    } finally {
      formLoading.value = false
    }
  }

  areaList.value = await AreaApi.getAreaTree()
  orderCityList.value = await AreaApi.getOrderCity()
  getOrderCityArea.value = await AreaApi.getOrderCityArea()

}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async (closeDialog: boolean) => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TeacherVO
    if (formType.value === 'create') {
      await TeacherApi.createTeacher(data)
      message.success(t('common.createSuccess'))
    } else {
      await TeacherApi.updateTeacher(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = closeDialog
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    teacherId: undefined,
    teacherName: undefined,
    teacherPhone: undefined,
    teacherSex: undefined,
    wechat: undefined,
    qq: undefined,
    idNumber: undefined,
    politicalStatus: undefined,
    nativeAreaId: undefined,
    orderCityId: undefined,
    orderAreaId: undefined,
    universityName: undefined,
    campus: undefined,
    universityCityId: undefined,
    schoolStatus: undefined,
    profession: undefined,
    degree: undefined,
    entryYear: undefined,
    teachScope: undefined,
    teachTimeRange: [],
    expValue: undefined,
    creditValue: undefined,
    startOrderTime: undefined,
    isHaveExperience: undefined,
    address: undefined,
    acceptableTime: undefined,
    registerTime: undefined,
    teacherChannel: undefined,
    trackingTime: undefined,
    trackingRemark: undefined,
    orderTimes: undefined,
    auditTime: undefined,
    isEnable: undefined,
    isAcceptOrder: undefined,
    idTags: [],
    openId: undefined,
    lastServiceTime: undefined,
    lastActiveTime: undefined,
    acceptOrderTime: undefined,
    operationRemark: undefined
  }
  formRef.value?.resetFields()
}

// 籍贯
const areaProps = {
  expandTrigger: 'hover' as const,
  children: 'children',
  label: 'name',
  value: 'id',
  isLeaf: 'leaf',
  emitPath: false, // 用于 cascader 组件：在选中节点改变时，是否返回由该节点所在的各级菜单的值所组成的数组，若设置 false，则只返回该节点的值
  checkStrictly: true
}

// 接单区域
const orderAreaIdProps = {
  expandTrigger: 'hover' as const,
  children: 'children',
  label: 'name',
  value: 'id',
  isLeaf: 'leaf',
  emitPath: false, // 用于 cascader 组件：在选中节点改变时，是否返回由该节点所在的各级菜单的值所组成的数组，若设置 false，则只返回该节点的值
  multiple:true
}

const sfzhChange = () => {
  // 效验身份证号格式
  const reg = /^[1-9]\d{5}(19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
  debugger
  const id = formData.value.idNumber;
  if (reg.test(id)) {
    //获取出生日期
    formData.value.birth = id.substring(6, 10) + '-' +id.substring(10, 12) + '-' + id.substring(12, 14);
    //获取性别
    if(id.substr(16, 1) % 2 == 1){
      formData.value.teacherSex = 1; // 男
    }else{
      formData.value.teacherSex = 2; // 女
    }
  }
};
</script>

<style lang="scss" scoped>
.form-item {
  width: 280px !important;
}
.form-item-baseline{
  vertical-align:baseline;
}
:deep(.el-checkbox-button__inner){
  border-left: var(--el-border);
  box-shadow: none !important;
  border-radius: var(--el-border-radius-base);
  margin: 1px 2px;
}
:deep(.el-form-item--default){
  margin-bottom: 10px;
}
:deep(.el-input-number.is-controls-right .el-input__inner){
  padding-left: 1px !important;
  padding-right: 1px !important;
}
</style>
