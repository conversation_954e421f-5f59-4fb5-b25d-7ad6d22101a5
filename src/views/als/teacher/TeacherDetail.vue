<script setup lang="ts">
import {TeacherApi} from "@/api/als/teacher";
import {formatDate} from "@/utils/formatTime";

defineOptions({ name: 'TeacherDetail' })

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('老师详情') // 弹窗的标题
const detail = ref({
  teacherVo:{
    teacherName: '',//老师
    teacherPhone: '',
    isAttention: true, // 是否关注公众号
    attentionPlat:[]
  },
  serviceVo:{
    serviceTimes: 0 , // 服务次数
    serviceClassHour: 0, // 服务课时总数
    serviceCustomerNum:0, // 服务过家长数
    commitLessonRecordNum:0, // 提交过日志数
    lastServiceTime: '',// 最近一次服务时间
    lastOrderConfirmTime: '',//最近一次抢单时间
    successOrderNum:0,// 体验成功次数
    failOrderNum:0,// 体验失败次数
    withdrawAmount:0// 提现金额
  },
  bindList:[{
    customerName: '',// 家长姓名
    customerPhone: '',// 手机
    headOperate: '',// 运营负责人
    lastLessonRecordDate: '',// 最近服务时间
    orderCount: 0// 体验课数
  }]
})
/** 打开弹窗 */
const open = async (id?: number) => {
  dialogVisible.value = true
  detail.value = await TeacherApi.getDetail(id)
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

const toDateTime = (datatimes) => {
  if (!datatimes) {
    return ''; // 如果没有传入日期，返回空字符串
  }
  return formatDate(datatimes); // 将字符串转换为 Date 对象
}
</script>

<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="800px">
    <div class="font-bold">
      <span>{{ detail.teacherVo.teacherName }}</span>
      <span class="ml-20px">{{ detail.teacherVo.teacherPhone }}</span>
      <span class="ml-20px">
        <span v-if="!detail.teacherVo.isAttention">未关注公众号</span>
        <span v-if="detail.teacherVo.attentionPlat && detail.teacherVo.attentionPlat.length > 0">
          已关注平台：({{ detail.teacherVo.attentionPlat.join(', ') }})
        </span>
      </span>
    </div>

    <el-descriptions
      direction="horizontal"
      :column="2"
      border
      size="small"
    >
      <el-descriptions-item class="desc-item-class" label="服务次数" label-align="center">{{ detail.serviceVo.serviceTimes }}</el-descriptions-item>
      <el-descriptions-item class="desc-item-class" label="服务课时总数" label-align="center">{{ detail.serviceVo.serviceClassHour }}</el-descriptions-item>
      <el-descriptions-item class="desc-item-class" label="服务过家长数" label-align="center">{{ detail.serviceVo.serviceCustomerNum }}</el-descriptions-item>
      <el-descriptions-item class="desc-item-class" label="提交过日志数" label-align="center">{{ detail.serviceVo.commitLessonRecordNum }}</el-descriptions-item>
      <el-descriptions-item class="desc-item-class" label="最近一次服务时间" label-align="center">
        {{ toDateTime(detail.serviceVo.lastServiceTime) }}
      </el-descriptions-item>
      <el-descriptions-item class="desc-item-class" label="最近一次抢单时间" label-align="center">
        {{ toDateTime(detail.serviceVo.lastOrderConfirmTime) }}
      </el-descriptions-item>
      <el-descriptions-item class="desc-item-class" label="体验成功次数" label-align="center">{{ detail.serviceVo.successOrderNum }}</el-descriptions-item>
      <el-descriptions-item class="desc-item-class" label="体验失败次数" label-align="center">{{ detail.serviceVo.failOrderNum }}</el-descriptions-item>
      <el-descriptions-item class="desc-item-class" label="已提现金额" label-align="center">
        <span v-if="detail.serviceVo.withdrawAmount > 0">￥</span>
        {{ detail.serviceVo.withdrawAmount }}
      </el-descriptions-item>
    </el-descriptions>

    <div style="height: 30px;margin-top: 20px;">
      <span class="font-bold">已绑定家长</span>
    </div>
    <el-table :data="detail.bindList" border size="small">
      <el-table-column align="center" prop="customerName" label="家长姓名" />
      <el-table-column align="center" prop="customerPhone" label="手机" />
      <el-table-column align="center" prop="headOperate" label="运营负责人" />
      <el-table-column align="center" prop="lastLessonRecordDate" label="最后服务时间" >
        <template #default="scope">
          {{ toDateTime(scope.row.lastLessonRecordDate) }}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="orderCount" label="体验课数" />
    </el-table>
  </Dialog>
</template>

<style scoped lang="scss">
.el-descriptions {
  margin-top: 20px;
}
:deep(.el-descriptions__label){
  width: 150px;
}
:deep(.el-table thead tr:first-child th) {
  background-color: #ece9e9; /* 设置背景色为灰色 */
  color: #7c7a7a; /* 可选：设置文字颜色 */
  font-weight: bold;
}
</style>
