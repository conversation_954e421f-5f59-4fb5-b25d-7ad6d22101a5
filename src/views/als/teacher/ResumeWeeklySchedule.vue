<script setup lang="ts">
defineOptions({ name: 'ResumeWeeklySchedule' })
const days = ref([ '周一', '周二', '周三', '周四', '周五', '周六', '周日'])
const times = ref(['上午(8:00-12:00)', '下午(14:00-18:00)', '晚上(18:00-21:00)'])
interface Props {
  scheduleData: string[]
}

const props = defineProps<Props>()

const isMarked = (dayIndex, timeIndex) =>{
  const day = dayIndex + 1; // 星期几从1开始
  const time = timeIndex + 1; // 时间从1开始
  return props.scheduleData?.some(item => item === day * 10 + time);
}
</script>

<template>
  <div class="schedule-table">
    <table>
      <thead>
      <tr>
        <th></th>
        <th class="!w-50px" v-for="day in days" :key="day">{{ day }}</th>
      </tr>
      </thead>
      <tbody>
      <tr v-for="(time, index) in times" :key="index">
        <td>{{ time }}</td>
        <td v-for="(day, dayIndex) in days" :key="dayIndex">
          <span v-if="isMarked(dayIndex, index)">✅</span>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<style scoped lang="scss">
.schedule-table {
  width: 100%;
  //margin: 20px auto;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: center;
}

th {
  background-color: #f2f2f2;
}
</style>
