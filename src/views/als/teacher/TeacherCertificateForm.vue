<template>
 <Dialog title="编辑老师信息" v-model="dialogVisible" width="600px">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="老师ID" prop="teacherId">
        <el-input v-model="form.teacherId" readonly />
      </el-form-item>
      <el-form-item label="老师姓名" prop="teacherName">
        <el-input v-model="form.teacherName" readonly />
      </el-form-item>
      <el-form-item label="老师性别" prop="teacherSex">
        <el-input v-model="form.teacherSex" readonly />
      </el-form-item>
      <el-form-item label="证书编号" v-if="form.certificateNo">
        <el-input v-model="form.certificateNo" placeholder="自动生成，无需填写" />
      </el-form-item>
      <el-form-item label="省份证号" prop="teacherIdNumber">
        <el-input v-model="form.teacherIdNumber" placeholder="请输入" readonly />
      </el-form-item>
      <el-form-item label="证书照" prop="picUrl" v-if="form.certificateNo">
        <UploadImg v-model="form.picUrl" :height="'120px'" :width="'120px'" />
      </el-form-item>
  
      <el-form-item label="有效期" prop="validTime" v-if="form.certificateNo">
        <el-date-picker
          v-model="form.validTime"
          type="datetime"
          placeholder="选择有效期"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item label="证书状态" prop="certificateStatus" v-if="form.certificateNo">
        <el-select v-model="form.certificateStatus" placeholder="请选择证书状态">
          <el-option label="有效" :value="0" />
          <el-option label="无效-已过期" :value="1" />
          <el-option label="无效-已注销" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          placeholder="请输入备注"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="loading">确 定</el-button>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useMessage } from '@/hooks/web/useMessage'
import { TeacherCertificateApi } from '@/api/als/teachercertificate/index'
import { UploadImg } from '@/components/UploadFile'

const emit = defineEmits(['success']) 
const message = useMessage() 

// 表单相关变量
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref()
const isLoading = ref(false) // 添加标志位防止重复调用API

// 表单数据
const defaultFormData = {
  certificateId: 0,
  teacherId: 0,
  teacherName: '',
  teacherSex: '',
  picUrl: '',
  teacherIdNumber: '',
  certificateNo: '',
  certificateStatus: 0, // 默认有效
  validTime: '',
  remark: ''
}

const form = reactive({...defaultFormData})

// 表单校验规则
const rules = {
  certificateNo: [{ required: true, message: '请输入证书编号', trigger: 'blur' }],
  teacherSex: [{ required: true, message: '请输入老师性别', trigger: 'blur' }],
  teacherId: [{ required: true, message: '老师ID', trigger: 'blur' }],
  teacherIdNumber: [{ required: true, message: '身份证号', trigger: 'blur' }],
  teacherName: [{ required: true, message: '老师姓名', trigger: 'blur' }],
  certificateStatus: [{ required: true, message: '请选择证书状态', trigger: 'change' }]
}

/**
 * 打开表单
 * @param teacherId 老师ID
 */
const open = async (teacherId: number) => {
  // 防止重复调用
  if (isLoading.value) return

  // 重置表单
  Object.assign(form, defaultFormData)
  form.teacherId = teacherId

  // 打开对话框（放在前面，不要等API调用）
  dialogVisible.value = true

  if (teacherId) {
    try {
      isLoading.value = true
      const result = await TeacherCertificateApi.getByTeacherId(teacherId)
      console.log('API完整响应:', result)
      if (result) {
        // 确保certificateStatus是数字类型
        form.teacherName = result.teacherName
        form.teacherSex = result.teacherSex
        form.teacherIdNumber = result.teacherIdNumber
        form.picUrl = result.picUrl
        form.validTime = formatDatetime(result.validTime)
        form.certificateNo = result.certificateNo
        form.certificateStatus = Number(result.certificateStatus) // 确保是数字类型
        form.remark = result.remark
      }
    } catch (error) {
      console.error('获取老师信息失败:', error)
    } finally {
      isLoading.value = false
    }
  }
}

// 格式化日期时间
const formatDatetime = (timestamp) => {
  if (!timestamp) return '';
  
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false
}

/** 提交表单 */
const submitForm = async () => {
  if (!formRef.value) return
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  try {
    loading.value = true
    // 创建一个提交用的数据对象，处理日期类型
    const submitData = {
      ...form,
      validTime: form.validTime ? new Date(form.validTime) : new Date()
    }
    await TeacherCertificateApi.createOrUpdateTeacherCertificate(submitData)
    message.success('发放上岗证成功')
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('发放上岗证失败:', error)
  } finally {
    loading.value = false
  }
}

// 对外暴露方法
defineExpose({
  open
})
</script> 
