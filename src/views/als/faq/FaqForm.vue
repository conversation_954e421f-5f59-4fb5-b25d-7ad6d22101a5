<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="可见方" prop="faqWho">
        <el-select v-model="formData.faqWho" placeholder="请选择">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_FAQ_WHO)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="问题分类" prop="faqType">
        <el-select v-model="formData.faqType" placeholder="请选择">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.ALS_FAQ_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="问题" prop="faqQuestion">
        <el-input v-model="formData.faqQuestion" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="解答" prop="faqAnswer">
        <el-input v-model="formData.faqAnswer" type="textarea" placeholder="请输入解答" rows="6"/>
      </el-form-item>
      <el-form-item label="状态" prop="faqStatus">
        <el-radio-group v-model="formData.faqStatus">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { FaqApi, FaqVO } from '@/api/als/faq'

/** 常见问题解答 表单 */
defineOptions({ name: 'FaqForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  faqId: undefined,
  faqWho: undefined,
  faqType: undefined,
  faqQuestion: undefined,
  faqAnswer: undefined,
  faqStatus: undefined,
  remark: undefined
})
const formRules = reactive({
  faqWho: [{ required: true, message: '可见方不能为空', trigger: 'blur' }],
  faqType: [{ required: true, message: '问题分类不能为空', trigger: 'change' }],
  faqQuestion: [{ required: true, message: '问题不能为空', trigger: 'blur' }],
  faqAnswer: [{ required: true, message: '解答不能为空', trigger: 'blur' }],
  faqStatus: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await FaqApi.getFaq(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as FaqVO
    if (formType.value === 'create') {
      await FaqApi.createFaq(data)
      message.success(t('common.createSuccess'))
    } else {
      await FaqApi.updateFaq(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    faqId: undefined,
    faqWho: undefined,
    faqType: undefined,
    faqQuestion: undefined,
    faqAnswer: undefined,
    faqStatus: undefined,
    remark: undefined
  }
  formRef.value?.resetFields()
}
</script>
