<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div v-if="detail.teacherVo">
      <!-- 老师基本信息 -->
      <el-card class="mb-4">
        <template #header>
          <span>基本信息</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="label">老师姓名：</span>
              <span class="value">{{ detail.teacherVo.teacherName }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">手机号：</span>
              <span class="value">{{ detail.teacherVo.teacherPhone }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 服务统计信息 -->
      <el-card class="mb-4">
        <template #header>
          <span>服务统计</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">服务次数：</span>
              <span class="value">{{ detail.serviceVo.serviceTimes }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">服务课时：</span>
              <span class="value">{{ detail.serviceVo.serviceClassHour }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">服务家长数：</span>
              <span class="value">{{ detail.serviceVo.serviceCustomerNum }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="mt-3">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">体验成功次数：</span>
              <span class="value">{{ detail.serviceVo.successOrderNum }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">体验失败次数：</span>
              <span class="value">{{ detail.serviceVo.failOrderNum }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">提现金额：</span>
              <span class="value">{{ detail.serviceVo.withdrawAmount }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="mt-3">
          <el-col :span="12">
            <div class="info-item">
              <span class="label">最近服务时间：</span>
              <span class="value">{{ formatDate(detail.serviceVo.lastServiceTime) }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">最近抢单时间：</span>
              <span class="value">{{ formatDate(detail.serviceVo.lastOrderConfirmTime) }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 绑定家长信息 -->
      <el-card v-if="detail.bindList && detail.bindList.length > 0">
        <template #header>
          <span>绑定家长信息</span>
        </template>
        <el-table :data="detail.bindList" border>
          <el-table-column prop="customerName" label="家长姓名" />
          <el-table-column prop="customerPhone" label="手机号" />
          <el-table-column prop="headOperate" label="运营负责人" />
          <el-table-column prop="orderCount" label="体验课数" />
          <el-table-column prop="lastLessonRecordDate" label="最近服务时间">
            <template #default="scope">
              {{ formatDate(scope.row.lastLessonRecordDate) }}
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <template #footer>
      <el-button @click="close">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { useTeacherDetail } from '@/utils/teacherUtils'
import { formatDate } from '@/utils/formatTime'

defineOptions({ name: 'GlobalTeacherDetail' })

const { visible, title, detail, close } = useTeacherDetail()
</script>

<style scoped lang="scss">
.info-item {
  margin-bottom: 8px;
  
  .label {
    color: #606266;
    font-weight: 500;
  }
  
  .value {
    color: #303133;
  }
}

.mt-3 {
  margin-top: 12px;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
