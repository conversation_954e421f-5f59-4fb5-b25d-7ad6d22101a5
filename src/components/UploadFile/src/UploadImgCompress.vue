<template>
  <div class="upload-box">
    <el-upload
      :id="uuid"
      :accept="fileType.join(',')"
      :action="uploadUrl"
      :before-upload="beforeUpload"
      :class="['upload', drag ? 'no-border' : '']"
      :disabled="disabled"
      :drag="drag"
      :http-request="httpRequestWithCompress"
      :multiple="multiple"
      :on-error="uploadError"
      :on-success="uploadSuccess"
      :show-file-list="showFileList"
    >
      <template v-if="modelValue">
        <img :src="modelValue" class="upload-image" />
        <div class="upload-handle" @click.stop>
          <div v-if="!disabled" class="handle-icon" @click="editImg">
            <Icon icon="ep:edit" />
            <span v-if="showBtnText">{{ t('action.edit') }}</span>
          </div>
          <div class="handle-icon" @click="imagePreview(modelValue)">
            <Icon icon="ep:zoom-in" />
            <span v-if="showBtnText">{{ t('action.detail') }}</span>
          </div>
          <div v-if="showDelete && !disabled" class="handle-icon" @click="deleteImg">
            <Icon icon="ep:delete" />
            <span v-if="showBtnText">{{ t('action.del') }}</span>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="upload-empty">
          <slot name="empty">
            <Icon icon="ep:plus" />
          </slot>
        </div>
      </template>
    </el-upload>
    
    <!-- 压缩进度条 -->
    <div v-if="isCompressing" class="compress-progress">
      <div class="progress-info">
        <span class="progress-text">{{ compressStatus }}</span>
        <span class="progress-percent">{{ compressProgress }}%</span>
      </div>
      <el-progress 
        :percentage="compressProgress" 
        :stroke-width="4"
        :show-text="false"
        status="success"
      />
    </div>
    
    <div class="el-upload__tip">
      <slot name="tip"></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { UploadProps, UploadRequestOptions } from 'element-plus'
import { generateUUID } from '@/utils'
import { propTypes } from '@/utils/propTypes'
import { createImageViewer } from '@/components/ImageViewer'
import { useUpload } from '@/components/UploadFile/src/useUpload'

defineOptions({ name: 'UploadImgCompress' })

type FileTypes =
  | 'image/apng'
  | 'image/bmp'
  | 'image/gif'
  | 'image/jpeg'
  | 'image/pjpeg'
  | 'image/png'
  | 'image/svg+xml'
  | 'image/tiff'
  | 'image/webp'
  | 'image/x-icon'

// 接受父组件参数
const props = defineProps({
  modelValue: propTypes.string.def(''),
  drag: propTypes.bool.def(true),
  disabled: propTypes.bool.def(false),
  fileSize: propTypes.number.def(5),
  fileType: propTypes.array.def(['image/jpeg', 'image/png', 'image/gif']),
  height: propTypes.string.def('150px'),
  width: propTypes.string.def('150px'),
  borderradius: propTypes.string.def('8px'),
  showDelete: propTypes.bool.def(true),
  showBtnText: propTypes.bool.def(true),
  multiple: propTypes.bool.def(false),
  showFileList: propTypes.bool.def(false),
  // 新增压缩相关配置
  compress: propTypes.bool.def(true), // 是否压缩
  compressOptions: {
    type: Object as () => { quality: number; maxWidth: number; maxHeight: number; maxSize?: number },
    default: () => ({
      quality: 0.75, // 压缩质量 0-1，提高默认质量保证图片清晰度
      maxWidth: 1600, // 最大宽度，适当提高默认值
      maxHeight: 1600, // 最大高度，适当提高默认值
      maxSize: 1 // 最大文件大小(MB)，如果压缩后仍然超过此大小，会继续降低质量压缩
    })
  }
})
const { t } = useI18n()
const message = useMessage()
const uuid = ref('id-' + generateUUID())

// 进度条相关状态
const isCompressing = ref(false)
const compressProgress = ref(0)
const compressStatus = ref('准备压缩...')

const imagePreview = (imgUrl: string) => {
  createImageViewer({
    zIndex: 9999999,
    urlList: [imgUrl]
  })
}
const emit = defineEmits(['update:modelValue'])
const deleteImg = () => {
  emit('update:modelValue', '')
}
const { uploadUrl, httpRequest } = useUpload()
const editImg = () => {
  const dom = document.querySelector(`#${uuid.value} .el-upload__input`)
  dom && dom.dispatchEvent(new MouseEvent('click'))
}
const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
  const imgSize = rawFile.size / 1024 / 1024 < props.fileSize
  const imgType = props.fileType
  if (!imgType.includes(rawFile.type as FileTypes))
    message.notifyWarning('上传图片不符合所需的格式！')
  if (!imgSize) message.notifyWarning(`上传图片大小不能超过 ${props.fileSize}M！`)
  return imgType.includes(rawFile.type as FileTypes) && imgSize
}
// 图片压缩方法
function compressImage(file: File, options: { quality: number; maxWidth: number; maxHeight: number; maxSize?: number }): Promise<File> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = function (event) {
      const img = new window.Image()
      img.src = event.target?.result as string
      img.onload = function () {
        let { maxWidth, maxHeight, quality, maxSize } = options
        let { width, height } = img
        
        // 更新进度 - 开始压缩
        compressStatus.value = '正在压缩图片...'
        compressProgress.value = 10
        
        // 计算原始文件大小（MB）
        const originalSizeMB = file.size / 1024 / 1024
        
        // 如果原图尺寸小于最大限制，但文件大小超过限制，则适度缩小尺寸
        if (originalSizeMB > (maxSize || 1) && (width <= maxWidth && height <= maxHeight)) {
          const scale = Math.sqrt((maxSize || 1) / originalSizeMB) * 0.9 // 适度缩小，保证质量
          width = Math.round(width * scale)
          height = Math.round(height * scale)
          console.log(`文件过大，适度缩小尺寸: ${img.width}x${img.height} -> ${width}x${height}`)
        }
        // 等比压缩尺寸
        else if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height)
          width = Math.round(width * ratio)
          height = Math.round(height * ratio)
          console.log(`尺寸过大，等比缩小: ${img.width}x${img.height} -> ${width}x${height}`)
        }
        
        // 如果文件仍然很大，进一步缩小尺寸
        if (originalSizeMB > (maxSize || 1) * 1.5) {
          const additionalScale = Math.sqrt((maxSize || 1) / originalSizeMB) * 0.8
          width = Math.round(width * additionalScale)
          height = Math.round(height * additionalScale)
          console.log(`文件仍然过大，进一步缩小尺寸: -> ${width}x${height}`)
        }
        
        // 更新进度 - 尺寸处理完成
        compressProgress.value = 30
        
        const canvas = document.createElement('canvas')
        canvas.width = width
        canvas.height = height
        const ctx = canvas.getContext('2d')!
        ctx.drawImage(img, 0, 0, width, height)
        
        // 更新进度 - Canvas绘制完成
        compressProgress.value = 50
        
        // 递归压缩函数，如果文件大小仍然超过限制则继续压缩
        const compressWithQuality = (currentQuality: number, lastSize?: number): void => {
          canvas.toBlob(
            (blob) => {
              if (blob) {
                const compressedFile = new File([blob], file.name, { type: file.type })
                const compressedSizeMB = compressedFile.size / 1024 / 1024
                
                console.log(`压缩中 - 质量: ${currentQuality.toFixed(2)}, 大小: ${compressedSizeMB.toFixed(2)}MB`)
                
                // 更新进度 - 质量压缩
                const qualityProgress = 50 + (1 - currentQuality) * 40 // 50-90%
                compressProgress.value = Math.round(qualityProgress)
                compressStatus.value = `压缩中... 质量: ${(currentQuality * 100).toFixed(0)}%`
                
                // 检查文件大小是否有变化
                if (lastSize !== undefined && Math.abs(compressedFile.size - lastSize) < 1024) {
                  // 如果文件大小没有变化，但还没有达到目标大小，继续降低质量
                  if (maxSize && compressedFile.size > maxSize * 1024 * 1024 && currentQuality > 0.1) {
                    const newQuality = Math.max(0.1, currentQuality - 0.1)
                    console.log(`文件大小无变化，继续降低质量到: ${newQuality.toFixed(2)}`)
                    compressWithQuality(newQuality, compressedFile.size)
                    return
                  } else {
                    console.log('文件大小无明显变化，停止压缩')
                    compressProgress.value = 100
                    compressStatus.value = '压缩完成'
                    resolve(compressedFile)
                    return
                  }
                }
                
                // 如果设置了最大文件大小限制，且压缩后仍然超过限制，则继续压缩
                if (maxSize && compressedFile.size > maxSize * 1024 * 1024 && currentQuality > 0.1) {
                  // 降低质量继续压缩
                  const newQuality = Math.max(0.1, currentQuality - 0.1)
                  compressWithQuality(newQuality, compressedFile.size)
                } else {
                  // 如果压缩后反而变大了，返回原文件
                  if (compressedFile.size > file.size) {
                    console.log('压缩后文件变大，返回原文件')
                    compressProgress.value = 100
                    compressStatus.value = '压缩完成'
                    resolve(file)
                  } else {
                    compressProgress.value = 100
                    compressStatus.value = '压缩完成'
                    resolve(compressedFile)
                  }
                }
              } else {
                reject(new Error('图片压缩失败'))
              }
            },
            file.type,
            currentQuality
          )
        }
        
        compressWithQuality(quality)
      }
      img.onerror = reject
    }
    reader.onerror = reject
  })
}
// 重写httpRequest，增加压缩逻辑
const httpRequestWithCompress = async (options: UploadRequestOptions) => {
  let file = options.file
  if (props.compress && file.type.startsWith('image/')) {
    try {
      // 显示进度条
      isCompressing.value = true
      compressProgress.value = 0
      compressStatus.value = '准备压缩...'
      
      const originalSize = file.size
      file = await compressImage(file, props.compressOptions as { quality: number; maxWidth: number; maxHeight: number; maxSize?: number })
      const compressedSize = file.size
      const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(1)
      console.log(`图片压缩完成: ${(originalSize / 1024 / 1024).toFixed(2)}MB -> ${(compressedSize / 1024 / 1024).toFixed(2)}MB (压缩率: ${compressionRatio}%)`)
      
      // 更新状态为上传中
      compressStatus.value = '正在上传...'
      compressProgress.value = 95
      
      // 兼容UploadRawFile类型，补充uid属性
      if (!(file as any).uid) {
        (file as any).uid = Date.now() + Math.random()
      }
      options.file = file
    } catch (e) {
      message.notifyError('图片压缩失败，已上传原图')
    } finally {
      // 隐藏进度条
      setTimeout(() => {
        isCompressing.value = false
        compressProgress.value = 0
      }, 1000) // 延迟1秒隐藏，让用户看到完成状态
    }
  }
  return httpRequest(options)
}
const uploadSuccess: UploadProps['onSuccess'] = (res: any): void => {
  message.success('上传成功')
  emit('update:modelValue', res.data)
}
const uploadError = () => {
  message.notifyError('图片上传失败，请您重新上传！')
}
</script>
<style lang="scss" scoped>
.is-error {
  .upload {
    :deep(.el-upload),
    :deep(.el-upload-dragger) {
      border: 1px dashed var(--el-color-danger) !important;

      &:hover {
        border-color: var(--el-color-primary) !important;
      }
    }
  }
}

:deep(.disabled) {
  .el-upload,
  .el-upload-dragger {
    cursor: not-allowed !important;
    background: var(--el-disabled-bg-color);
    border: 1px dashed var(--el-border-color-darker) !important;

    &:hover {
      border: 1px dashed var(--el-border-color-darker) !important;
    }
  }
}

.upload-box {
  .no-border {
    :deep(.el-upload) {
      border: none !important;
    }
  }

  :deep(.upload) {
    .el-upload {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: v-bind(width);
      height: v-bind(height);
      overflow: hidden;
      border: 1px dashed var(--el-border-color-darker);
      border-radius: v-bind(borderradius);
      transition: var(--el-transition-duration-fast);

      &:hover {
        border-color: var(--el-color-primary);

        .upload-handle {
          opacity: 1;
        }
      }

      .el-upload-dragger {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        padding: 0;
        overflow: hidden;
        background-color: transparent;
        border: 1px dashed var(--el-border-color-darker);
        border-radius: v-bind(borderradius);

        &:hover {
          border: 1px dashed var(--el-color-primary);
        }
      }

      .el-upload-dragger.is-dragover {
        background-color: var(--el-color-primary-light-9);
        border: 2px dashed var(--el-color-primary) !important;
      }

      .upload-image {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .upload-empty {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        line-height: 30px;
        color: var(--el-color-info);

        .el-icon {
          font-size: 28px;
          color: var(--el-text-color-secondary);
        }
      }

      .upload-handle {
        position: absolute;
        top: 0;
        right: 0;
        display: flex;
        width: 100%;
        height: 100%;
        cursor: pointer;
        background: rgb(0 0 0 / 60%);
        opacity: 0;
        box-sizing: border-box;
        transition: var(--el-transition-duration-fast);
        align-items: center;
        justify-content: center;

        .handle-icon {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 0 6%;
          color: aliceblue;

          .el-icon {
            margin-bottom: 40%;
            font-size: 130%;
            line-height: 130%;
          }

          span {
            font-size: 85%;
            line-height: 85%;
          }
        }
      }
    }
  }

  .el-upload__tip {
    line-height: 18px;
    text-align: center;
  }
  
  // 压缩进度条样式
  .compress-progress {
    margin-top: 12px;
    padding: 12px;
    background: var(--el-fill-color-light);
    border-radius: 6px;
    
    .progress-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      
      .progress-text {
        font-size: 14px;
        color: var(--el-text-color-primary);
      }
      
      .progress-percent {
        font-size: 12px;
        color: var(--el-text-color-secondary);
        font-weight: 500;
      }
    }
    
    :deep(.el-progress) {
      .el-progress-bar__outer {
        background-color: var(--el-border-color-lighter);
      }
      
      .el-progress-bar__inner {
        background: linear-gradient(90deg, var(--el-color-primary) 0%, var(--el-color-success) 100%);
        transition: width 0.3s ease;
      }
    }
  }
}
</style> 
