<template>
  <div class="collapse-container">
    <div
      ref="contentRef"
      class="collapse-content"
      :style="{
        height: isCollapsed ? '0px' : contentHeight + 'px',
        overflow: 'hidden',
        transition: 'height 0.3s ease'
      }"
    >
      <div ref="innerContentRef">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick, onUnmounted } from 'vue'

defineOptions({ name: 'CollapseTransition' })

const props = defineProps({
  isCollapsed: {
    type: Boolean,
    default: true
  }
})

const contentRef = ref<HTMLElement | null>(null)
const innerContentRef = ref<HTMLElement | null>(null)
const contentHeight = ref(0)

// 计算内容高度
const updateContentHeight = () => {
  if (innerContentRef.value) {
    contentHeight.value = innerContentRef.value.offsetHeight
  }
}

// 监听折叠状态变化，如果展开则更新高度
watch(
  () => props.isCollapsed,
  (newVal, oldVal) => {
    if (!newVal && oldVal) {
      // 从折叠到展开时，先更新内容高度
      nextTick(() => {
        updateContentHeight()
      })
    }
  }
)

// 在组件挂载时计算内容高度
onMounted(() => {
  updateContentHeight()

  // 监听窗口大小变化，更新高度
  window.addEventListener('resize', updateContentHeight)
})

// 组件销毁时清理事件监听
onUnmounted(() => {
  window.removeEventListener('resize', updateContentHeight)
})
</script>

<style scoped lang="scss">
.collapse-container {
  width: 100%;
}
</style> 