<template>
  <div class="collapse-with-button">
    <CollapseTransition :is-collapsed="isCollapsed">
      <slot></slot>
    </CollapseTransition>
    
    <div class="collapse-button-container">
      <slot name="before-button"></slot>
      
      <el-button @click="toggle" :plain="true" v-bind="buttonProps">
        <span v-if="isCollapsed">
          <Icon :icon="expandIcon" class="mr-5px" />{{ expandText }}
        </span>
        <span v-else>
          <Icon :icon="collapseIcon" class="mr-5px" />{{ collapseText }}
        </span>
      </el-button>
      
      <slot name="after-button"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { CollapseTransition } from './index'

defineOptions({ name: 'CollapseWithButton' })

const props = defineProps({
  // 初始状态是否折叠
  defaultCollapsed: {
    type: Boolean,
    default: true
  },
  // 展开时的文本
  expandText: {
    type: String,
    default: '展开'
  },
  // 收起时的文本
  collapseText: {
    type: String,
    default: '收起'
  },
  // 展开时的图标
  expandIcon: {
    type: String,
    default: 'ep:arrow-down'
  },
  // 收起时的图标
  collapseIcon: {
    type: String,
    default: 'ep:arrow-up'
  },
  // 按钮的其他属性
  buttonProps: {
    type: Object,
    default: () => ({})
  },
  // 按钮容器的类名
  buttonContainerClass: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:collapsed', 'toggle'])

// 控制折叠状态
const isCollapsed = ref(props.defaultCollapsed)

// 切换折叠状态
const toggle = () => {
  isCollapsed.value = !isCollapsed.value
  emit('update:collapsed', isCollapsed.value)
  emit('toggle', isCollapsed.value)
}
</script>

<style scoped lang="scss">
.collapse-button-container {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
</style> 