<script lang="tsx">
import { defineComponent, PropType, ref } from 'vue'
import { DictDataType, getDictOptions } from '@/utils/dict'

export default defineComponent({
  name: 'DictTagText',
  props: {
    type: {
      type: String as PropType<string>,
      required: true
    },
    value: {
      type: [String, Number, Boolean] as PropType<string | number | boolean>,
      required: true
    }
  },
  setup(props) {
    const dictData = ref<DictDataType>()
    const getDictObj = (dictType: string, value: string) => {
      const dictOptions = getDictOptions(dictType)
      dictOptions.forEach((dict: DictDataType) => {
        if (dict.value === value) {
          dictData.value = dict
        }
      })
    }
    const readerDictTag = () => {
      if (!props.type) {
        return null
      }
      // 解决自定义字典标签值为零时标签不渲染的问题
      if (props.value === undefined || props.value === null) {
        return null
      }
      getDictObj(props.type, props.value.toString())
      // 直接返回文本
      return dictData.value?.label
    }
    return () => readerDictTag()
  }
})
</script>
