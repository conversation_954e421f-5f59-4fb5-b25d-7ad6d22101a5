import{by as e,__tla as s}from"./index-BUSn51wb.js";let r,m=Promise.all([(()=>{try{return s}catch{}})()]).then(async()=>{r={getCustomerSummaryByDate:t=>e.get({url:"/crm/statistics-customer/get-customer-summary-by-date",params:t}),getCustomerSummaryByUser:t=>e.get({url:"/crm/statistics-customer/get-customer-summary-by-user",params:t}),getFollowUpSummaryByDate:t=>e.get({url:"/crm/statistics-customer/get-follow-up-summary-by-date",params:t}),getFollowUpSummaryByUser:t=>e.get({url:"/crm/statistics-customer/get-follow-up-summary-by-user",params:t}),getFollowUpSummaryByType:t=>e.get({url:"/crm/statistics-customer/get-follow-up-summary-by-type",params:t}),getContractSummary:t=>e.get({url:"/crm/statistics-customer/get-contract-summary",params:t}),getPoolSummaryByDate:t=>e.get({url:"/crm/statistics-customer/get-pool-summary-by-date",params:t}),getPoolSummaryByUser:t=>e.get({url:"/crm/statistics-customer/get-pool-summary-by-user",params:t}),getCustomerDealCycleByDate:t=>e.get({url:"/crm/statistics-customer/get-customer-deal-cycle-by-date",params:t}),getCustomerDealCycleByUser:t=>e.get({url:"/crm/statistics-customer/get-customer-deal-cycle-by-user",params:t}),getCustomerDealCycleByArea:t=>e.get({url:"/crm/statistics-customer/get-customer-deal-cycle-by-area",params:t}),getCustomerDealCycleByProduct:t=>e.get({url:"/crm/statistics-customer/get-customer-deal-cycle-by-product",params:t})}});export{r as S,m as __tla};
