import{d as B,b4 as R,p as v,b as U,r as y,at as j,aI as b,o as r,c,g as o,F as g,k as q,i as t,w as V,H as F,a8 as H,a as k,l as L,a9 as M,_ as P,aN as T,B as X,__tla as z}from"./index-BUSn51wb.js";import{E as D,__tla as G}from"./el-image-BjHZRFih.js";import{b as J,__tla as K}from"./spu-CW3JGweV.js";import{_ as O,__tla as Q}from"./SpuTableSelect.vue_vue_type_script_setup_true_lang-lBhZ06IN.js";import{__tla as W}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as Y}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{__tla as Z}from"./el-card-CJbXGyyg.js";import{__tla as $}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{__tla as aa}from"./index-Cch5e1V0.js";import{__tla as la}from"./el-tree-select-CBuha0HW.js";import"./tree-BMa075Oj.js";import{__tla as ea}from"./category-WzWM3ODe.js";let w,ta=Promise.all([(()=>{try{return z}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return K}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return W}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return Z}catch{}})(),(()=>{try{return $}catch{}})(),(()=>{try{return aa}catch{}})(),(()=>{try{return la}catch{}})(),(()=>{try{return ea}catch{}})()]).then(async()=>{let i,n;i={class:"flex flex-wrap items-center gap-8px"},n={class:"relative h-full w-full"},w=X(B({name:"SpuShowcase",__name:"SpuShowcase",props:{modelValue:R([Number,Array]).isRequired,limit:v.number.def(Number.MAX_VALUE),disabled:v.bool.def(!1)},emits:["update:modelValue","change"],setup(_,{emit:x}){const e=_,C=U(()=>!e.disabled&&(!e.limit||l.value.length<e.limit)),l=y([]);j(()=>e.modelValue,async()=>{const a=b(e.modelValue)?e.modelValue:e.modelValue?[e.modelValue]:[];a.length!==0?(l.value.length===0||l.value.some(p=>!a.includes(p.id)))&&(l.value=await J(a)):l.value=[]},{immediate:!0});const m=y(),S=()=>{m.value.open(l.value)},A=a=>{l.value=b(a)?a:[a],d()},s=x,d=()=>{if(e.limit===1){const a=l.value.length>0?l.value[0]:null;s("update:modelValue",(a==null?void 0:a.id)||0),s("change",a)}else s("update:modelValue",l.value.map(a=>a.id)),s("change",l.value)};return(a,p)=>{const N=D,f=P,h=T;return r(),c(g,null,[o("div",i,[(r(!0),c(g,null,q(k(l),(u,E)=>(r(),c("div",{key:u.id,class:"select-box spu-pic"},[t(h,{content:u.name},{default:V(()=>[o("div",n,[t(N,{src:u.picUrl,class:"h-full w-full"},null,8,["src"]),F(t(f,{class:"del-icon",icon:"ep:circle-close-filled",onClick:sa=>(I=>{l.value.splice(I,1),d()})(E)},null,8,["onClick"]),[[H,!_.disabled]])])]),_:2},1032,["content"])]))),128)),k(C)?(r(),L(h,{key:0,content:"\u9009\u62E9\u5546\u54C1"},{default:V(()=>[o("div",{class:"select-box",onClick:S},[t(f,{icon:"ep:plus"})])]),_:1})):M("",!0)]),t(O,{ref_key:"spuTableSelectRef",ref:m,multiple:_.limit!=1,onChange:A},null,8,["multiple"])],64)}}}),[["__scopeId","data-v-f5b96300"]])});export{ta as __tla,w as default};
