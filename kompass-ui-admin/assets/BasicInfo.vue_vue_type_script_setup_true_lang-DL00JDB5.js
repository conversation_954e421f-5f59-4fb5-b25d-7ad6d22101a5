import{_ as B,__tla as U}from"./XButton-BjahQbul.js";import{_ as j,__tla as F}from"./Form-DJa9ov9B.js";import{d as N,e as R,f as d,r as A,C as E,o as M,c as P,i as l,w as o,j as g,t as b,a,g as S,F as W,n as $,I as z,am as D,an as G,__tla as H}from"./index-BUSn51wb.js";import{u as J,g as K,__tla as L}from"./profile-BQCm_-PE.js";let y,O=Promise.all([(()=>{try{return U}catch{}})(),(()=>{try{return F}catch{}})(),(()=>{try{return H}catch{}})(),(()=>{try{return L}catch{}})()]).then(async()=>{let u;u={style:{"text-align":"center"}},y=N({name:"BasicInfo",__name:"BasicInfo",setup(Q){const{t:e}=$(),h=z(),k=R(),w=d({nickname:[{required:!0,message:e("profile.rules.nickname"),trigger:"blur"}],email:[{required:!0,message:e("profile.rules.mail"),trigger:"blur"},{type:"email",message:e("profile.rules.truemail"),trigger:["blur","change"]}],mobile:[{required:!0,message:e("profile.rules.phone"),trigger:"blur"},{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:e("profile.rules.truephone"),trigger:"blur"}]}),x=d([{field:"nickname",label:e("profile.user.nickname"),component:"Input"},{field:"mobile",label:e("profile.user.mobile"),component:"Input"},{field:"email",label:e("profile.user.email"),component:"Input"},{field:"sex",label:e("profile.user.sex"),component:"InputNumber",value:0}]),r=A(),i=async()=>{var t;const m=await K();return(t=a(r))==null||t.setValues(m),m};return E(async()=>{await i()}),(m,t)=>{const c=D,I=G,V=j,p=B;return M(),P(W,null,[l(V,{ref_key:"formRef",ref:r,labelWidth:200,rules:a(w),schema:a(x)},{sex:o(s=>[l(I,{modelValue:s.sex,"onUpdate:modelValue":n=>s.sex=n},{default:o(()=>[l(c,{label:1},{default:o(()=>[g(b(a(e)("profile.user.man")),1)]),_:1}),l(c,{label:2},{default:o(()=>[g(b(a(e)("profile.user.woman")),1)]),_:1})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1},8,["rules","schema"]),S("div",u,[l(p,{title:a(e)("common.save"),type:"primary",onClick:t[0]||(t[0]=s=>(()=>{var f;const n=(f=a(r))==null?void 0:f.getElFormRef();n&&n.validate(async v=>{var _;if(v){const q=(_=a(r))==null?void 0:_.formModel;await J(q),h.success(e("common.updateSuccess"));const C=await i();k.setUserNicknameAction(C.nickname)}})})())},null,8,["title"]),l(p,{title:a(e)("common.reset"),type:"danger",onClick:t[1]||(t[1]=s=>i())},null,8,["title"])])],64)}}})});export{y as _,O as __tla};
