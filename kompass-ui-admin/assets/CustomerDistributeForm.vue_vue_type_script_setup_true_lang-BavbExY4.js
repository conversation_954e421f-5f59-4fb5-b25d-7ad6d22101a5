import{d as j,r as o,f as q,o as n,l as c,w as s,i as _,j as f,a,H as D,c as H,k as J,F as K,y as L,n as N,I as O,J as P,K as z,L as A,O as B,N as E,R as G,__tla as M}from"./index-BUSn51wb.js";import{_ as Q,__tla as S}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{d as T,__tla as W}from"./index-CD52sTBY.js";import{g as X,__tla as Y}from"./index-BYXzDB8j.js";let p,Z=Promise.all([(()=>{try{return M}catch{}})(),(()=>{try{return S}catch{}})(),(()=>{try{return W}catch{}})(),(()=>{try{return Y}catch{}})()]).then(async()=>{p=j({__name:"CustomerDistributeForm",emits:["success"],setup($,{expose:y,emit:w}){N();const U=O(),r=o(!1),d=o(!1),m=o([]),l=o({id:void 0,ownerUserId:void 0}),b=q({ownerUserId:[{required:!0,message:"\u8D1F\u8D23\u4EBA\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),i=o();y({open:async u=>{r.value=!0,V(),l.value.id=u,m.value=await X()}});const I=w,k=async()=>{if(i&&await i.value.validate()){d.value=!0;try{await T([l.value.id],l.value.ownerUserId),U.success("\u5206\u914D\u5BA2\u6237\u6210\u529F"),r.value=!1,I("success")}finally{d.value=!1}}},V=()=>{var u;l.value={id:void 0,ownerUserId:void 0},(u=i.value)==null||u.resetFields()};return(u,t)=>{const h=P,g=z,x=A,C=B,v=E,F=Q,R=G;return n(),c(F,{modelValue:a(r),"onUpdate:modelValue":t[2]||(t[2]=e=>L(r)?r.value=e:null),title:"\u5206\u914D\u5BA2\u6237"},{footer:s(()=>[_(v,{disabled:a(d),type:"primary",onClick:k},{default:s(()=>[f("\u786E \u5B9A")]),_:1},8,["disabled"]),_(v,{onClick:t[1]||(t[1]=e=>r.value=!1)},{default:s(()=>[f("\u53D6 \u6D88")]),_:1})]),default:s(()=>[D((n(),c(C,{ref_key:"formRef",ref:i,model:a(l),rules:a(b),"label-width":"100px"},{default:s(()=>[_(x,{label:"\u8D1F\u8D23\u4EBA",prop:"ownerUserId"},{default:s(()=>[_(g,{modelValue:a(l).ownerUserId,"onUpdate:modelValue":t[0]||(t[0]=e=>a(l).ownerUserId=e),class:"w-1/1"},{default:s(()=>[(n(!0),H(K,null,J(a(m),e=>(n(),c(h,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[R,a(d)]])]),_:1},8,["modelValue"])}}})});export{p as _,Z as __tla};
