import{d as p,r as i,cJ as s,__tla as v}from"./index-BUSn51wb.js";let n,_=Promise.all([(()=>{try{return v}catch{}})()]).then(async()=>{n=p({name:"DictTagText",props:{type:{type:String,required:!0},value:{type:[String,Number,Boolean],required:!0}},setup(t){const e=i(),o=()=>{var r;return t.type?t.value===void 0||t.value===null?null:(a=t.type,l=t.value.toString(),s(a).forEach(u=>{u.value===l&&(e.value=u)}),(r=e.value)==null?void 0:r.label):null;var a,l};return()=>o()}})});export{n as _,_ as __tla};
