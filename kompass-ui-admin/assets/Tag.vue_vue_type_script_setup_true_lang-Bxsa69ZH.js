import{d as m,o as e,c as l,F as x,k as c,a0 as i,t as _,__tla as b}from"./index-BUSn51wb.js";let o,g=Promise.all([(()=>{try{return b}catch{}})()]).then(async()=>{let t,r;t={class:"flex flex-wrap gap-[8px]"},r=["onClick"],o=m({__name:"Tag",props:{tags:{default:()=>[]},modelValue:{}},emits:["update:modelValue"],setup(s,{emit:d}){const u=s,p=d;return(n,f)=>(e(),l("div",t,[(e(!0),l(x,null,c(u.tags,a=>(e(),l("span",{key:a.value,class:i(["tag mb-2 border-[2px] border-solid border-[#DDDFE3] px-2 leading-6 text-[12px] bg-[#DDDFE3] rounded-[4px] cursor-pointer",n.modelValue===a.value&&"!border-[#846af7] text-[#846af7]"]),onClick:D=>p("update:modelValue",a.value)},_(a.label),11,r))),128))]))}})});export{o as _,g as __tla};
