import{d as S,S as b,r as s,u as T,C as j,o as n,c as D,H as F,a as t,l as R,w as _,j as H,a9 as N,i as r,F as W,I as K,N as Q,z as q,A as G,E as J,R as O,__tla as U}from"./index-BUSn51wb.js";import{g as X,_ as Y,__tla as Z}from"./index-CpmUC5sy.js";import{u as $,__tla as tt}from"./tagsView-BOOrxb3Q.js";import{a as at,__tla as rt}from"./index-D3Ji6shA.js";import{_ as _t,__tla as lt}from"./ReceivableDetailsHeader.vue_vue_type_script_setup_true_lang-DeyEUZA4.js";import{_ as et,__tla as st}from"./ReceivableDetailsInfo.vue_vue_type_script_setup_true_lang-CuP3AIhK.js";import{_ as it,__tla as ot}from"./PermissionList.vue_vue_type_script_setup_true_lang--VdCh_pH.js";import{B as w,__tla as ct}from"./index-pKzyIv29.js";import{_ as nt,__tla as ut}from"./ReceivableForm.vue_vue_type_script_setup_true_lang-37IhTDRa.js";import{__tla as mt}from"./el-timeline-item-D8aDRTsd.js";import{__tla as ft}from"./formatTime-DWdBpgsM.js";import{__tla as pt}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{__tla as yt}from"./el-card-CJbXGyyg.js";import{__tla as dt}from"./el-descriptions-item-dD3qa0ub.js";import{__tla as ht}from"./el-collapse-item-B_QvnH_b.js";import{__tla as vt}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import"./color-BN7ZL7BD.js";import{__tla as bt}from"./PermissionForm.vue_vue_type_script_setup_true_lang-oI9oCvWg.js";import{__tla as Rt}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as wt}from"./index-BYXzDB8j.js";import{__tla as zt}from"./index-Uo5NQqNb.js";import{__tla as Ct}from"./index-CD52sTBY.js";import{__tla as Et}from"./index-DrB1WZUR.js";let z,kt=Promise.all([(()=>{try{return U}catch{}})(),(()=>{try{return Z}catch{}})(),(()=>{try{return tt}catch{}})(),(()=>{try{return rt}catch{}})(),(()=>{try{return lt}catch{}})(),(()=>{try{return st}catch{}})(),(()=>{try{return ot}catch{}})(),(()=>{try{return ct}catch{}})(),(()=>{try{return ut}catch{}})(),(()=>{try{return mt}catch{}})(),(()=>{try{return ft}catch{}})(),(()=>{try{return pt}catch{}})(),(()=>{try{return yt}catch{}})(),(()=>{try{return dt}catch{}})(),(()=>{try{return ht}catch{}})(),(()=>{try{return vt}catch{}})(),(()=>{try{return bt}catch{}})(),(()=>{try{return Rt}catch{}})(),(()=>{try{return wt}catch{}})(),(()=>{try{return zt}catch{}})(),(()=>{try{return Ct}catch{}})(),(()=>{try{return Et}catch{}})()]).then(async()=>{z=S({name:"CrmReceivablePlanDetail",__name:"index",props:{id:{}},setup(C){const E=C,k=b(),g=K(),u=s(0),i=s(!0),l=s({}),m=s(),f=async a=>{i.value=!0;try{l.value=await at(a),await I(a)}finally{i.value=!1}},p=s(),y=s([]),I=async a=>{if(!a)return;const e=await X({bizType:w.CRM_RECEIVABLE,bizId:a});y.value=e.list},{delView:A}=$(),{currentRoute:B}=T(),d=()=>{A(t(B))};return b(),j(async()=>{const a=E.id||k.params.id;if(!a)return g.warning("\u53C2\u6570\u9519\u8BEF\uFF0C\u56DE\u6B3E\u4E0D\u80FD\u4E3A\u7A7A\uFF01"),void d();u.value=a,await f(u.value)}),(a,e)=>{const L=Q,o=q,V=Y,x=G,M=J,P=O;return n(),D(W,null,[F((n(),R(_t,{receivable:t(l)},{default:_(()=>{var c;return[(c=t(m))!=null&&c.validateWrite?(n(),R(L,{key:0,onClick:e[0]||(e[0]=gt=>{return h="update",v=t(l).id,void p.value.open(h,v);var h,v})},{default:_(()=>[H(" \u7F16\u8F91 ")]),_:1})):N("",!0)]}),_:1},8,["receivable"])),[[P,t(i)]]),r(M,null,{default:_(()=>[r(x,null,{default:_(()=>[r(o,{label:"\u8BE6\u7EC6\u8D44\u6599"},{default:_(()=>[r(et,{receivable:t(l)},null,8,["receivable"])]),_:1}),r(o,{label:"\u64CD\u4F5C\u65E5\u5FD7"},{default:_(()=>[r(V,{"log-list":t(y)},null,8,["log-list"])]),_:1}),r(o,{label:"\u56E2\u961F\u6210\u5458"},{default:_(()=>[r(it,{ref_key:"permissionListRef",ref:m,"biz-id":t(l).id,"biz-type":t(w).CRM_RECEIVABLE,"show-action":!0,onQuitTeam:d},null,8,["biz-id","biz-type"])]),_:1})]),_:1})]),_:1}),r(nt,{ref_key:"formRef",ref:p,onSuccess:e[1]||(e[1]=c=>f(t(l).id))},null,512)],64)}}})});export{kt as __tla,z as default};
