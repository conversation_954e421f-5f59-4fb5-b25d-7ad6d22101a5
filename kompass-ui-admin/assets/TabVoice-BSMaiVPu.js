import{d as N,I as S,e9 as T,b as W,r as V,f as q,o as n,c as w,a as r,g,t as D,i as a,w as l,l as G,j as b,y as P,s as H,_ as J,N as K,cF as L,E as O,bw as Q,a5 as R,a6 as X,B as Y,__tla as Z}from"./index-BUSn51wb.js";import{W as $,__tla as aa}from"./main-DvybYriQ.js";import ta,{__tla as ea}from"./main-CG5euiEw.js";import{u as la,U as ra,__tla as sa}from"./useUpload-gjof4KYU.js";import{__tla as oa}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{__tla as _a}from"./index-Cch5e1V0.js";import{__tla as ca}from"./main-DwQbyLY9.js";import{__tla as ua}from"./el-image-BjHZRFih.js";import{__tla as ia}from"./main.vue_vue_type_script_setup_true_lang-Bi4NZ0QE.js";import{__tla as na}from"./index-C4ZN3JCQ.js";import{__tla as da}from"./index-Cqwyhbsb.js";import{__tla as ma}from"./formatTime-DWdBpgsM.js";let k,pa=Promise.all([(()=>{try{return Z}catch{}})(),(()=>{try{return aa}catch{}})(),(()=>{try{return ea}catch{}})(),(()=>{try{return sa}catch{}})(),(()=>{try{return oa}catch{}})(),(()=>{try{return _a}catch{}})(),(()=>{try{return ca}catch{}})(),(()=>{try{return ua}catch{}})(),(()=>{try{return ia}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return ma}catch{}})()]).then(async()=>{let d,m,p;d={key:0,class:"select-item2"},m={class:"item-name"},p=(s=>(R("data-v-2e7170f9"),s=s(),X(),s))(()=>g("div",{class:"el-upload__tip"}," \u683C\u5F0F\u652F\u6301 mp3/wma/wav/amr\uFF0C\u6587\u4EF6\u5927\u5C0F\u4E0D\u8D85\u8FC7 2M\uFF0C\u64AD\u653E\u957F\u5EA6\u4E0D\u8D85\u8FC7 60s ",-1)),k=Y(N({__name:"TabVoice",props:{modelValue:{}},emits:["update:modelValue"],setup(s,{emit:j}){const x=S(),z={Authorization:"Bearer "+T()},B=s,C=j,e=W({get:()=>B.modelValue,set:t=>C("update:modelValue",t)}),o=V(!1),f=V([]),c=q({accountId:e.value.accountId,type:"voice",title:"",introduction:""}),M=t=>la(ra.Voice,10)(t),U=t=>{if(t.code!==0)return x.error("\u4E0A\u4F20\u51FA\u9519\uFF1A"+t.msg),!1;f.value=[],c.title="",c.introduction="",y(t.data)},A=()=>{e.value.mediaId=null,e.value.url=null,e.value.name=null},y=t=>{o.value=!1,e.value.mediaId=t.mediaId,e.value.url=t.url,e.value.name=t.name};return(t,_)=>{const u=H,v=J,i=K,E=L,h=O,F=Q;return n(),w("div",null,[r(e).url?(n(),w("div",d,[g("p",m,D(r(e).name),1),a(u,{class:"ope-row",justify:"center"},{default:l(()=>[a(r(ta),{url:r(e).url},null,8,["url"])]),_:1}),a(u,{class:"ope-row",justify:"center"},{default:l(()=>[a(i,{type:"danger",circle:"",onClick:A},{default:l(()=>[a(v,{icon:"ep:delete"})]),_:1})]),_:1})])):(n(),G(u,{key:1,style:{"text-align":"center"}},{default:l(()=>[a(h,{span:12,class:"col-select"},{default:l(()=>[a(i,{type:"success",onClick:_[0]||(_[0]=I=>o.value=!0)},{default:l(()=>[b(" \u7D20\u6750\u5E93\u9009\u62E9"),a(v,{icon:"ep:circle-check"})]),_:1}),a(E,{title:"\u9009\u62E9\u8BED\u97F3",modelValue:r(o),"onUpdate:modelValue":_[1]||(_[1]=I=>P(o)?o.value=I:null),width:"90%","append-to-body":"","destroy-on-close":""},{default:l(()=>[a(r($),{type:"voice","account-id":r(e).accountId,onSelectMaterial:y},null,8,["account-id"])]),_:1},8,["modelValue"])]),_:1}),a(h,{span:12,class:"col-add"},{default:l(()=>[a(F,{action:"http://**************:48080/admin-api/mp/material/upload-temporary",headers:z,multiple:"",limit:1,"file-list":r(f),data:r(c),"before-upload":M,"on-success":U},{tip:l(()=>[p]),default:l(()=>[a(i,{type:"primary"},{default:l(()=>[b("\u70B9\u51FB\u4E0A\u4F20")]),_:1})]),_:1},8,["file-list","data"])]),_:1})]),_:1}))])}}}),[["__scopeId","data-v-2e7170f9"]])});export{pa as __tla,k as default};
