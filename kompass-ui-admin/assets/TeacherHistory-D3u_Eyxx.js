import{d as i,o as d,l as h,w as t,i as a,g as e,__tla as f}from"./index-BUSn51wb.js";import{E as y,a as T,__tla as b}from"./el-timeline-item-D8aDRTsd.js";import{E as G,__tla as U}from"./el-card-CJbXGyyg.js";let s,E=Promise.all([(()=>{try{return f}catch{}})(),(()=>{try{return b}catch{}})(),(()=>{try{return U}catch{}})()]).then(async()=>{let _,u,p,r,n,o;_=e("h4",null,"Update Github template",-1),u=e("p",null,"Tom committed 2018/4/12 20:46",-1),p=e("h4",null,"Update Github template",-1),r=e("p",null,"Tom committed 2018/4/3 20:46",-1),n=e("h4",null,"Update Github template",-1),o=e("p",null,"Tom committed 2018/4/2 20:46",-1),s=i({name:"<PERSON>Hist<PERSON>",__name:"TeacherHistory",setup:H=>(g,w)=>{const l=G,m=y,c=T;return d(),h(c,null,{default:t(()=>[a(m,{timestamp:"2018/4/12",placement:"top"},{default:t(()=>[a(l,null,{default:t(()=>[_,u]),_:1})]),_:1}),a(m,{timestamp:"2018/4/3",placement:"top"},{default:t(()=>[a(l,null,{default:t(()=>[p,r]),_:1})]),_:1}),a(m,{timestamp:"2018/4/2",placement:"top"},{default:t(()=>[a(l,null,{default:t(()=>[n,o]),_:1})]),_:1})]),_:1})}})});export{E as __tla,s as default};
