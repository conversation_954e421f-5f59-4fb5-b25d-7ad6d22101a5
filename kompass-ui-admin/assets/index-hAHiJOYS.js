import{_ as o,__tla as m}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{d as s,o as c,l as n,w as a,i as t,z as u,A as i,__tla as f}from"./index-BUSn51wb.js";import{_ as p,__tla as h}from"./ChatConversationList.vue_vue_type_script_setup_true_lang-BDTiDuLH.js";import{_ as y,__tla as d}from"./ChatMessageList.vue_vue_type_script_setup_true_lang-DSUxeg50.js";import{__tla as b}from"./el-card-CJbXGyyg.js";import{__tla as x}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{__tla as A}from"./index-Cch5e1V0.js";import{__tla as g}from"./formatTime-DWdBpgsM.js";import{__tla as w}from"./index-UejJy_db.js";import{__tla as z}from"./index-BYXzDB8j.js";import{__tla as C}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import"./color-BN7ZL7BD.js";import{__tla as M}from"./index-CBZlCJOz.js";import"./fetch-D5K_4anA.js";let r,P=Promise.all([(()=>{try{return m}catch{}})(),(()=>{try{return f}catch{}})(),(()=>{try{return h}catch{}})(),(()=>{try{return d}catch{}})(),(()=>{try{return b}catch{}})(),(()=>{try{return x}catch{}})(),(()=>{try{return A}catch{}})(),(()=>{try{return g}catch{}})(),(()=>{try{return w}catch{}})(),(()=>{try{return z}catch{}})(),(()=>{try{return C}catch{}})(),(()=>{try{return M}catch{}})()]).then(async()=>{r=s({name:"AiChatManager",__name:"index",setup:j=>(k,q)=>{const _=u,l=i,e=o;return c(),n(e,null,{default:a(()=>[t(l,null,{default:a(()=>[t(_,{label:"\u5BF9\u8BDD\u5217\u8868"},{default:a(()=>[t(p)]),_:1}),t(_,{label:"\u6D88\u606F\u5217\u8868"},{default:a(()=>[t(y)]),_:1})]),_:1})]),_:1})}})});export{P as __tla,r as default};
