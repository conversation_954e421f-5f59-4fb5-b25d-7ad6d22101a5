import{by as t,__tla as o}from"./index-BUSn51wb.js";let r,c,e,s,l,u=Promise.all([(()=>{try{return o}catch{}})()]).then(async()=>{r=async a=>await t.get({url:"/crm/product-category/get?id="+a}),c=async a=>await t.post({url:"/crm/product-category/create",data:a}),l=async a=>await t.put({url:"/crm/product-category/update",data:a}),e=async a=>await t.delete({url:"/crm/product-category/delete?id="+a}),s=async a=>await t.get({url:"/crm/product-category/list",params:a})});export{u as __tla,r as a,c,e as d,s as g,l as u};
