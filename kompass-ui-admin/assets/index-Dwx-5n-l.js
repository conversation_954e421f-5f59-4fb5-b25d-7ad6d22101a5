import{d as F,I as H,r as i,c8 as O,f as q,T as B,o as f,c as E,i as t,w as e,a,y as P,j as p,H as z,l as I,F as G,L as J,O as K,_ as Q,s as R,z as W,N as X,A as Y,__tla as Z}from"./index-BUSn51wb.js";import{_ as $,__tla as aa}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as ta,__tla as la}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as ea,__tla as ra}from"./index-COobLwz-.js";import{_ as _a,__tla as oa}from"./main.vue_vue_type_script_setup_true_lang-CUZbNZvZ.js";import na,{__tla as ia}from"./ImageTable-CN5RfGnC.js";import{_ as pa,__tla as ma}from"./VoiceTable.vue_vue_type_script_setup_true_lang-B5AlT3AY.js";import{_ as sa,__tla as ua}from"./VideoTable.vue_vue_type_script_setup_true_lang-D2-Jhd7D.js";import x,{__tla as ca}from"./UploadFile-BxYgmJ3Y.js";import{_ as da,__tla as ga}from"./UploadVideo.vue_vue_type_script_setup_true_lang-CsT_aq48.js";import{__tla as fa}from"./upload-DyVf7G_u.js";import{g as ya,d as ha,__tla as va}from"./index-C4ZN3JCQ.js";import{U as m,__tla as Ua}from"./useUpload-gjof4KYU.js";import{__tla as Na}from"./index-Cch5e1V0.js";import{__tla as Va}from"./el-card-CJbXGyyg.js";import{__tla as ba}from"./index-C-Ee_eqi.js";import{__tla as za}from"./main-CG5euiEw.js";import{__tla as Ia}from"./formatTime-DWdBpgsM.js";import{__tla as Sa}from"./main.vue_vue_type_script_setup_true_lang-Bi4NZ0QE.js";let C,wa=Promise.all([(()=>{try{return Z}catch{}})(),(()=>{try{return aa}catch{}})(),(()=>{try{return la}catch{}})(),(()=>{try{return ra}catch{}})(),(()=>{try{return oa}catch{}})(),(()=>{try{return ia}catch{}})(),(()=>{try{return ma}catch{}})(),(()=>{try{return ua}catch{}})(),(()=>{try{return ca}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return fa}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return Ua}catch{}})(),(()=>{try{return Na}catch{}})(),(()=>{try{return Va}catch{}})(),(()=>{try{return ba}catch{}})(),(()=>{try{return za}catch{}})(),(()=>{try{return Ia}catch{}})(),(()=>{try{return Sa}catch{}})()]).then(async()=>{C=F({__name:"index",setup(Pa){const S=H(),d=i(m.Image),s=i(!1),u=i([]),c=i(0),y=i(-1);O("accountId",y);const r=q({pageNo:1,pageSize:10,accountId:y,permanent:!0}),g=i(!1),j=o=>{y.value=o,r.accountId=o,r.pageNo=1,n()},n=async()=>{s.value=!0;try{const o=await ya({...r,type:d.value});u.value=o.list,c.value=o.total}finally{s.value=!1}},A=()=>{u.value=[],c.value=0,r.pageNo=1,n()},h=async o=>{await S.confirm("\u6B64\u64CD\u4F5C\u5C06\u6C38\u4E45\u5220\u9664\u8BE5\u6587\u4EF6, \u662F\u5426\u7EE7\u7EED?"),await ha(o),S.alertSuccess("\u5220\u9664\u6210\u529F")};return(o,l)=>{const D=ea,L=J,M=K,w=ta,v=Q,U=R,N=$,V=W,T=X,k=Y,b=B("hasPermi");return f(),E(G,null,[t(D,{title:"\u516C\u4F17\u53F7\u7D20\u6750",url:"https://doc.iocoder.cn/mp/material/"}),t(w,null,{default:e(()=>[t(M,{class:"-mb-15px",inline:!0,"label-width":"68px"},{default:e(()=>[t(L,{label:"\u516C\u4F17\u53F7",prop:"accountId"},{default:e(()=>[t(a(_a),{onChange:j})]),_:1})]),_:1})]),_:1}),t(w,null,{default:e(()=>[t(k,{modelValue:a(d),"onUpdate:modelValue":l[8]||(l[8]=_=>P(d)?d.value=_:null),onTabChange:A},{default:e(()=>[t(V,{name:a(m).Image},{label:e(()=>[t(U,{align:"middle"},{default:e(()=>[t(v,{icon:"ep:picture"}),p("\u56FE\u7247 ")]),_:1})]),default:e(()=>[z((f(),I(x,{type:a(m).Image,onUploaded:n},{default:e(()=>[p(" \u652F\u6301 bmp/png/jpeg/jpg/gif \u683C\u5F0F\uFF0C\u5927\u5C0F\u4E0D\u8D85\u8FC7 2M ")]),_:1},8,["type"])),[[b,["mp:material:upload-permanent"]]]),t(na,{loading:a(s),list:a(u),onDelete:h},null,8,["loading","list"]),t(N,{total:a(c),page:a(r).pageNo,"onUpdate:page":l[0]||(l[0]=_=>a(r).pageNo=_),limit:a(r).pageSize,"onUpdate:limit":l[1]||(l[1]=_=>a(r).pageSize=_),onPagination:n},null,8,["total","page","limit"])]),_:1},8,["name"]),t(V,{name:a(m).Voice},{label:e(()=>[t(U,{align:"middle"},{default:e(()=>[t(v,{icon:"ep:microphone"}),p("\u8BED\u97F3 ")]),_:1})]),default:e(()=>[z((f(),I(x,{type:a(m).Voice,onUploaded:n},{default:e(()=>[p(" \u683C\u5F0F\u652F\u6301 mp3/wma/wav/amr\uFF0C\u6587\u4EF6\u5927\u5C0F\u4E0D\u8D85\u8FC7 2M\uFF0C\u64AD\u653E\u957F\u5EA6\u4E0D\u8D85\u8FC7 60s ")]),_:1},8,["type"])),[[b,["mp:material:upload-permanent"]]]),t(pa,{list:a(u),loading:a(s),onDelete:h},null,8,["list","loading"]),t(N,{total:a(c),page:a(r).pageNo,"onUpdate:page":l[2]||(l[2]=_=>a(r).pageNo=_),limit:a(r).pageSize,"onUpdate:limit":l[3]||(l[3]=_=>a(r).pageSize=_),onPagination:n},null,8,["total","page","limit"])]),_:1},8,["name"]),t(V,{name:a(m).Video},{label:e(()=>[t(U,{align:"middle"},{default:e(()=>[t(v,{icon:"ep:video-play"}),p(" \u89C6\u9891 ")]),_:1})]),default:e(()=>[z((f(),I(T,{type:"primary",plain:"",onClick:l[4]||(l[4]=_=>g.value=!0)},{default:e(()=>[p("\u65B0\u5EFA\u89C6\u9891")]),_:1})),[[b,["mp:material:upload-permanent"]]]),t(da,{modelValue:a(g),"onUpdate:modelValue":l[5]||(l[5]=_=>P(g)?g.value=_:null)},null,8,["modelValue"]),t(sa,{list:a(u),loading:a(s),onDelete:h},null,8,["list","loading"]),t(N,{total:a(c),page:a(r).pageNo,"onUpdate:page":l[6]||(l[6]=_=>a(r).pageNo=_),limit:a(r).pageSize,"onUpdate:limit":l[7]||(l[7]=_=>a(r).pageSize=_),onPagination:n},null,8,["total","page","limit"])]),_:1},8,["name"])]),_:1},8,["modelValue"])]),_:1})],64)}}})});export{wa as __tla,C as default};
