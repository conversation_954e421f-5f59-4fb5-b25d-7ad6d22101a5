import{d as z,u as F,r as i,f as H,C as M,o as v,c as S,i as e,w as l,a,U as j,j as f,H as q,l as B,F as R,Z as K,L,M as O,_ as Q,N as Z,O as A,P as E,Q as G,R as J,__tla as W}from"./index-BUSn51wb.js";import{_ as X,__tla as $}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as ee,__tla as ae}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as te,__tla as le}from"./index-COobLwz-.js";import{d as x,__tla as re}from"./formatTime-DWdBpgsM.js";import{j as oe,__tla as ne}from"./index-OMcsJcjy.js";import{__tla as se}from"./index-Cch5e1V0.js";import{__tla as ie}from"./el-card-CJbXGyyg.js";let T,ce=Promise.all([(()=>{try{return W}catch{}})(),(()=>{try{return $}catch{}})(),(()=>{try{return ae}catch{}})(),(()=>{try{return le}catch{}})(),(()=>{try{return re}catch{}})(),(()=>{try{return ne}catch{}})(),(()=>{try{return se}catch{}})(),(()=>{try{return ie}catch{}})()]).then(async()=>{T=z({name:"BpmTodoTask",__name:"index",setup(pe){const{push:V}=F(),c=i(!0),h=i(0),g=i([]),t=H({pageNo:1,pageSize:10,name:"",createTime:[]}),y=i(),p=async()=>{c.value=!0;try{const d=await oe(t);g.value=d.list,h.value=d.total}finally{c.value=!1}},m=()=>{t.pageNo=1,p()},D=()=>{y.value.resetFields(),m()};return M(()=>{p()}),(d,o)=>{const s=te,U=K,_=L,C=O,w=Q,u=Z,N=A,b=ee,n=E,I=G,P=X,Y=J;return v(),S(R,null,[e(s,{title:"\u5BA1\u6279\u901A\u8FC7\u3001\u4E0D\u901A\u8FC7\u3001\u9A73\u56DE",url:"https://doc.iocoder.cn/bpm/task-todo-done/"}),e(s,{title:"\u5BA1\u6279\u52A0\u7B7E\u3001\u51CF\u7B7E",url:"https://doc.iocoder.cn/bpm/sign/"}),e(s,{title:"\u5BA1\u6279\u8F6C\u529E\u3001\u59D4\u6D3E\u3001\u6284\u9001",url:"https://doc.iocoder.cn/bpm/task-delegation-and-cc/"}),e(s,{title:"\u5BA1\u6279\u52A0\u7B7E\u3001\u51CF\u7B7E",url:"https://doc.iocoder.cn/bpm/sign/"}),e(b,null,{default:l(()=>[e(N,{ref_key:"queryFormRef",ref:y,inline:!0,model:a(t),class:"-mb-15px","label-width":"68px"},{default:l(()=>[e(_,{label:"\u4EFB\u52A1\u540D\u79F0",prop:"name"},{default:l(()=>[e(U,{modelValue:a(t).name,"onUpdate:modelValue":o[0]||(o[0]=r=>a(t).name=r),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u4EFB\u52A1\u540D\u79F0",onKeyup:j(m,["enter"])},null,8,["modelValue"])]),_:1}),e(_,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:l(()=>[e(C,{modelValue:a(t).createTime,"onUpdate:modelValue":o[1]||(o[1]=r=>a(t).createTime=r),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),e(_,null,{default:l(()=>[e(u,{onClick:m},{default:l(()=>[e(w,{class:"mr-5px",icon:"ep:search"}),f(" \u641C\u7D22 ")]),_:1}),e(u,{onClick:D},{default:l(()=>[e(w,{class:"mr-5px",icon:"ep:refresh"}),f(" \u91CD\u7F6E ")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(b,null,{default:l(()=>[q((v(),B(I,{data:a(g)},{default:l(()=>[e(n,{align:"center",label:"\u6D41\u7A0B",prop:"processInstance.name",width:"180"}),e(n,{align:"center",label:"\u53D1\u8D77\u4EBA",prop:"processInstance.startUser.nickname",width:"100"}),e(n,{formatter:a(x),align:"center",label:"\u53D1\u8D77\u65F6\u95F4",prop:"createTime",width:"180"},null,8,["formatter"]),e(n,{align:"center",label:"\u5F53\u524D\u4EFB\u52A1",prop:"name",width:"180"}),e(n,{formatter:a(x),align:"center",label:"\u4EFB\u52A1\u65F6\u95F4",prop:"createTime",width:"180"},null,8,["formatter"]),e(n,{align:"center",label:"\u6D41\u7A0B\u7F16\u53F7",prop:"id","show-overflow-tooltip":!0}),e(n,{align:"center",label:"\u4EFB\u52A1\u7F16\u53F7",prop:"id","show-overflow-tooltip":!0}),e(n,{align:"center",label:"\u64CD\u4F5C",fixed:"right",width:"80"},{default:l(r=>[e(u,{link:"",type:"primary",onClick:me=>{return k=r.row,void V({name:"BpmProcessInstanceDetail",query:{id:k.processInstance.id}});var k}},{default:l(()=>[f("\u529E\u7406")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Y,a(c)]]),e(P,{limit:a(t).pageSize,"onUpdate:limit":o[2]||(o[2]=r=>a(t).pageSize=r),page:a(t).pageNo,"onUpdate:page":o[3]||(o[3]=r=>a(t).pageNo=r),total:a(h),onPagination:p},null,8,["limit","page","total"])]),_:1})],64)}}})});export{ce as __tla,T as default};
