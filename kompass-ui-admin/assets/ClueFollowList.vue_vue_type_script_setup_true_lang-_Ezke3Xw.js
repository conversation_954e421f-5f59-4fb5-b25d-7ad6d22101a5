import{d as z,r as i,f as D,u as I,bc as P,C as j,o as s,c as v,i as a,w as r,a as e,F as C,k as q,l as x,H as A,j as G,t as H,G as m,g as J,J as K,K as Q,L as Y,O as B,v as W,P as X,Q as Z,R as $,__tla as aa}from"./index-BUSn51wb.js";import{_ as ea,__tla as la}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as ta,__tla as ra}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as oa,__tla as na}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{b as pa,__tla as ia}from"./index-CRiW4Z5g.js";import{d as u,__tla as sa}from"./formatTime-DWdBpgsM.js";import{F as ua}from"./common-BQQO87UM.js";let U,da=Promise.all([(()=>{try{return aa}catch{}})(),(()=>{try{return la}catch{}})(),(()=>{try{return ra}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return ia}catch{}})(),(()=>{try{return sa}catch{}})()]).then(async()=>{let f;f=J("div",{class:"pb-5 text-xl"},"\u5206\u914D\u7ED9\u6211\u7684\u7EBF\u7D22",-1),U=z({name:"CrmClueFollowList",__name:"ClueFollowList",setup(_a){const d=i(!0),w=i(0),g=i([]),o=D({pageNo:1,pageSize:10,followUpStatus:!1,transformStatus:!1}),S=i(),p=async()=>{d.value=!0;try{const _=await pa(o);g.value=_.list,w.value=_.total}finally{d.value=!1}},N=()=>{o.pageNo=1,p()},{push:R}=I();return P(async()=>{await p()}),j(()=>{p()}),(_,n)=>{const L=K,T=Q,k=Y,E=B,h=oa,F=W,l=X,c=ta,M=Z,O=ea,V=$;return s(),v(C,null,[a(h,null,{default:r(()=>[f,a(E,{ref_key:"queryFormRef",ref:S,inline:!0,model:e(o),class:"-mb-15px","label-width":"68px"},{default:r(()=>[a(k,{label:"\u72B6\u6001",prop:"followUpStatus"},{default:r(()=>[a(T,{modelValue:e(o).followUpStatus,"onUpdate:modelValue":n[0]||(n[0]=t=>e(o).followUpStatus=t),class:"!w-240px",placeholder:"\u72B6\u6001",onChange:N},{default:r(()=>[(s(!0),v(C,null,q(e(ua),(t,b)=>(s(),x(L,{label:t.label,value:t.value,key:b},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),a(h,null,{default:r(()=>[A((s(),x(M,{data:e(g),stripe:!0,"show-overflow-tooltip":!0},{default:r(()=>[a(l,{label:"\u7EBF\u7D22\u540D\u79F0",align:"center",prop:"name",fixed:"left",width:"160"},{default:r(t=>[a(F,{underline:!1,type:"primary",onClick:b=>{return y=t.row.id,void R({name:"CrmClueDetail",params:{id:y}});var y}},{default:r(()=>[G(H(t.row.name),1)]),_:2},1032,["onClick"])]),_:1}),a(l,{label:"\u7EBF\u7D22\u6765\u6E90",align:"center",prop:"source",width:"100"},{default:r(t=>[a(c,{type:e(m).CRM_CUSTOMER_SOURCE,value:t.row.source},null,8,["type","value"])]),_:1}),a(l,{label:"\u624B\u673A",align:"center",prop:"mobile",width:"120"}),a(l,{label:"\u7535\u8BDD",align:"center",prop:"telephone",width:"130"}),a(l,{label:"\u90AE\u7BB1",align:"center",prop:"email",width:"180"}),a(l,{label:"\u5730\u5740",align:"center",prop:"detailAddress",width:"180"}),a(l,{align:"center",label:"\u5BA2\u6237\u884C\u4E1A",prop:"industryId",width:"100"},{default:r(t=>[a(c,{type:e(m).CRM_CUSTOMER_INDUSTRY,value:t.row.industryId},null,8,["type","value"])]),_:1}),a(l,{align:"center",label:"\u5BA2\u6237\u7EA7\u522B",prop:"level",width:"135"},{default:r(t=>[a(c,{type:e(m).CRM_CUSTOMER_LEVEL,value:t.row.level},null,8,["type","value"])]),_:1}),a(l,{formatter:e(u),align:"center",label:"\u4E0B\u6B21\u8054\u7CFB\u65F6\u95F4",prop:"contactNextTime",width:"180px"},null,8,["formatter"]),a(l,{align:"center",label:"\u5907\u6CE8",prop:"remark",width:"200"}),a(l,{label:"\u6700\u540E\u8DDF\u8FDB\u65F6\u95F4",align:"center",prop:"contactLastTime",formatter:e(u),width:"180px"},null,8,["formatter"]),a(l,{align:"center",label:"\u6700\u540E\u8DDF\u8FDB\u8BB0\u5F55",prop:"contactLastContent",width:"200"}),a(l,{align:"center",label:"\u8D1F\u8D23\u4EBA",prop:"ownerUserName",width:"100px"}),a(l,{align:"center",label:"\u6240\u5C5E\u90E8\u95E8",prop:"ownerUserDeptName",width:"100"}),a(l,{label:"\u66F4\u65B0\u65F6\u95F4",align:"center",prop:"updateTime",formatter:e(u),width:"180px"},null,8,["formatter"]),a(l,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:e(u),width:"180px"},null,8,["formatter"]),a(l,{align:"center",label:"\u521B\u5EFA\u4EBA",prop:"creatorName",width:"100px"})]),_:1},8,["data"])),[[V,e(d)]]),a(O,{total:e(w),page:e(o).pageNo,"onUpdate:page":n[1]||(n[1]=t=>e(o).pageNo=t),limit:e(o).pageSize,"onUpdate:limit":n[2]||(n[2]=t=>e(o).pageSize=t),onPagination:p},null,8,["total","page","limit"])]),_:1})],64)}}})});export{U as _,da as __tla};
