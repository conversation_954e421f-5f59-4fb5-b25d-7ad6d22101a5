import{d as b,r as i,C as D,o as M,c as Y,i as t,w as s,j as c,a as f,y as v,aV as C,ct as u,aM as j,an as k,M as H,__tla as P}from"./index-BUSn51wb.js";import{i as U,j as O,k as R,l as S,m as $,__tla as q}from"./formatTime-DWdBpgsM.js";let p,z=Promise.all([(()=>{try{return P}catch{}})(),(()=>{try{return q}catch{}})()]).then(async()=>{let d;d={class:"flex flex-row items-center gap-2"},p=b({name:"ShortcutDateRangePicker",__name:"index",emits:["change"],setup(A,{expose:h,emit:x}){const l=i(7),a=i(["",""]);h({times:a});const g=[{text:"\u6628\u5929",value:()=>U(new Date,-1)},{text:"\u6700\u8FD17\u5929",value:()=>O()},{text:"\u672C\u6708",value:()=>[u().startOf("M"),u().subtract(1,"d")]},{text:"\u6700\u8FD130\u5929",value:()=>R()},{text:"\u6700\u8FD11\u5E74",value:()=>S()}],m=async()=>{(function(){const n=u().subtract(l.value,"d"),e=u().subtract(1,"d");a.value=$(n,e)})(),await _()},w=x,_=async()=>{w("change",a.value)};return D(()=>{m()}),(n,e)=>{const o=j,y=k,V=H;return M(),Y("div",d,[t(y,{modelValue:f(l),"onUpdate:modelValue":e[0]||(e[0]=r=>v(l)?l.value=r:null),onChange:m},{default:s(()=>[t(o,{label:1},{default:s(()=>[c("\u6628\u5929")]),_:1}),t(o,{label:7},{default:s(()=>[c("\u6700\u8FD17\u5929")]),_:1}),t(o,{label:30},{default:s(()=>[c("\u6700\u8FD130\u5929")]),_:1})]),_:1},8,["modelValue"]),t(V,{modelValue:f(a),"onUpdate:modelValue":e[1]||(e[1]=r=>v(a)?a.value=r:null),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],shortcuts:g,class:"!w-240px",onChange:_},null,8,["modelValue","default-time"]),C(n.$slots,"default")])}}})});export{p as _,z as __tla};
