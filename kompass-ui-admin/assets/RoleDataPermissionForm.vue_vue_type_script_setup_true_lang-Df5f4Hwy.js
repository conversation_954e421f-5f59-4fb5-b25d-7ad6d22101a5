import{d as H,n as J,I as L,r as u,f as Y,o as v,l as h,w as o,i as s,a,j as n,H as q,t as C,c as z,F as B,k as Q,V as W,G as X,y as S,a9 as Z,ay as $,ax as ee,L as ae,J as le,K as te,O as de,ce as oe,eh as se,N as ue,R as ce,__tla as ne}from"./index-BUSn51wb.js";import{_ as re,__tla as ie}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{E as me,__tla as pe}from"./el-card-CJbXGyyg.js";import{d as _e,h as ve}from"./tree-BMa075Oj.js";import{m as D}from"./constants-A8BI3pz7.js";import{g as fe,__tla as ye}from"./index-Bqt292RI.js";import{b as he,__tla as Se}from"./index-CODXyRlK.js";let w,xe=Promise.all([(()=>{try{return ne}catch{}})(),(()=>{try{return ie}catch{}})(),(()=>{try{return pe}catch{}})(),(()=>{try{return ye}catch{}})(),(()=>{try{return Se}catch{}})()]).then(async()=>{w=H({name:"SystemRoleDataPermissionForm",__name:"RoleDataPermissionForm",emits:["success"],setup(Ve,{expose:P,emit:U}){const{t:E}=J(),I=L(),r=u(!1),f=u(!1),d=Y({id:void 0,name:"",code:"",dataScope:void 0,dataScopeDeptIds:[]}),k=u(),x=u([]),i=u(!0),m=u(),p=u(!1),_=u(!0);P({open:async l=>{var e;r.value=!0,M(),x.value=ve(await fe()),d.id=l.id,d.name=l.name,d.code=l.code,d.dataScope=l.dataScope,await $(),(e=l.dataScopeDeptIds)==null||e.forEach(c=>{m.value.setChecked(c,!0,!1)})}});const T=U,F=async()=>{f.value=!0;try{const l={roleId:d.id,dataScope:d.dataScope,dataScopeDeptIds:d.dataScope!==D.DEPT_CUSTOM?[]:m.value.getCheckedKeys(!1)};await he(l),I.success(E("common.updateSuccess")),r.value=!1,T("success")}finally{f.value=!1}},M=()=>{var l,e;p.value=!1,i.value=!0,_.value=!0,d.value={id:void 0,name:"",code:"",dataScope:void 0,dataScopeDeptIds:[]},(l=m.value)==null||l.setCheckedNodes([]),(e=k.value)==null||e.resetFields()},O=()=>{var e;const l=(e=m.value)==null?void 0:e.store.nodesMap;for(let c in l)l[c].expanded!==i.value&&(l[c].expanded=i.value)};return(l,e)=>{const c=ee,y=ae,R=le,g=te,A=de,V=oe,K=se,N=me,b=ue,j=re,G=ce;return v(),h(j,{modelValue:a(r),"onUpdate:modelValue":e[6]||(e[6]=t=>S(r)?r.value=t:null),title:"\u83DC\u5355\u6743\u9650",width:"800"},{footer:o(()=>[s(b,{disabled:a(f),type:"primary",onClick:F},{default:o(()=>[n("\u786E \u5B9A")]),_:1},8,["disabled"]),s(b,{onClick:e[5]||(e[5]=t=>r.value=!1)},{default:o(()=>[n("\u53D6 \u6D88")]),_:1})]),default:o(()=>[q((v(),h(A,{ref_key:"formRef",ref:k,model:a(d),"label-width":"80px"},{default:o(()=>[s(y,{label:"\u89D2\u8272\u540D\u79F0"},{default:o(()=>[s(c,null,{default:o(()=>[n(C(a(d).name),1)]),_:1})]),_:1}),s(y,{label:"\u89D2\u8272\u6807\u8BC6"},{default:o(()=>[s(c,null,{default:o(()=>[n(C(a(d).code),1)]),_:1})]),_:1}),s(y,{label:"\u6743\u9650\u8303\u56F4"},{default:o(()=>[s(g,{modelValue:a(d).dataScope,"onUpdate:modelValue":e[0]||(e[0]=t=>a(d).dataScope=t)},{default:o(()=>[(v(!0),z(B,null,Q(a(W)(a(X).SYSTEM_DATA_SCOPE),t=>(v(),h(R,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])),[[G,a(f)]]),a(d).dataScope===a(D).DEPT_CUSTOM?(v(),h(y,{key:0,label:"\u6743\u9650\u8303\u56F4",style:{display:"flex"}},{default:o(()=>[s(N,{class:"card",shadow:"never"},{header:o(()=>[n(" \u5168\u9009/\u5168\u4E0D\u9009: "),s(V,{modelValue:a(p),"onUpdate:modelValue":e[1]||(e[1]=t=>S(p)?p.value=t:null),"active-text":"\u662F","inactive-text":"\u5426","inline-prompt":"",onChange:e[2]||(e[2]=t=>{m.value.setCheckedNodes(p.value?x.value:[])})},null,8,["modelValue"]),n(" \u5168\u90E8\u5C55\u5F00/\u6298\u53E0: "),s(V,{modelValue:a(i),"onUpdate:modelValue":e[3]||(e[3]=t=>S(i)?i.value=t:null),"active-text":"\u5C55\u5F00","inactive-text":"\u6298\u53E0","inline-prompt":"",onChange:O},null,8,["modelValue"]),n(" \u7236\u5B50\u8054\u52A8(\u9009\u4E2D\u7236\u8282\u70B9\uFF0C\u81EA\u52A8\u9009\u62E9\u5B50\u8282\u70B9): "),s(V,{modelValue:a(_),"onUpdate:modelValue":e[4]||(e[4]=t=>S(_)?_.value=t:null),"active-text":"\u662F","inactive-text":"\u5426","inline-prompt":""},null,8,["modelValue"])]),default:o(()=>[s(K,{ref_key:"treeRef",ref:m,"check-strictly":!a(_),data:a(x),props:a(_e),"default-expand-all":"","empty-text":"\u52A0\u8F7D\u4E2D\uFF0C\u8BF7\u7A0D\u540E","node-key":"id","show-checkbox":""},null,8,["check-strictly","data","props"])]),_:1})]),_:1})):Z("",!0)]),_:1},8,["modelValue"])}}})});export{w as _,xe as __tla};
