import{d as ea,I as ta,r as c,o as r,c as v,g as e,i as l,w as t,j as n,a as u,y as z,F as h,k as V,l as B,t as x,a0 as F,av as sa,Z as ia,N as ua,J as oa,K as ra,cl as na,B as da,__tla as ca}from"./index-BUSn51wb.js";import{E as ma,__tla as va}from"./el-image-BjHZRFih.js";import{E as pa,__tla as _a}from"./el-space-Dxj8A-LJ.js";import{E as ya,__tla as fa}from"./el-text-CIwNlU-U.js";import{I as wa,__tla as ka}from"./index-Cjd1fP7g.js";import{M as K,I as ha,f as U,g as P,N as ga,a as A}from"./constants-C0I8ujwj.js";let G,ba=Promise.all([(()=>{try{return ca}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return _a}catch{}})(),(()=>{try{return fa}catch{}})(),(()=>{try{return ka}catch{}})()]).then(async()=>{let D,E,M,j,N,J,S,O,R,Y;D={class:"prompt"},E={class:"hot-words"},M={class:"image-size"},j=["onClick"],N={class:"size-font"},J={class:"model"},S={class:"model-font"},O={class:"version"},R={class:"model"},Y={class:"btns"},G=da(ea({__name:"index",emits:["onDrawStart","onDrawComplete"],setup(Va,{expose:H,emit:L}){const Q=ta(),C=c(!1),g=c(""),p=c(""),y=c(),I=c("midjourney"),b=c("1:1"),_=c("6.0"),f=c(K),Z=L,$=async s=>{I.value=s.key,s.key==="niji"?f.value=ga:f.value=K,_.value=f.value[0].value},T=async()=>{await Q.confirm("\u786E\u8BA4\u751F\u6210\u5185\u5BB9?");try{C.value=!0,Z("onDrawStart",A.MIDJOURNEY);const s=U.find(i=>b.value===i.key),o={prompt:p.value,model:I.value,width:s.width,height:s.height,version:_.value,referImageUrl:y.value};await wa.midjourneyImagine(o)}finally{Z("onDrawComplete",A.MIDJOURNEY),C.value=!1}};return H({settingValues:async s=>{p.value=s.prompt;const o=U.find(m=>m.key===`${s.width}:${s.height}`);b.value=o.key;const i=P.find(m=>{var d;return m.key===((d=s.options)==null?void 0:d.model)});await $(i),_.value=f.value.find(m=>{var d;return m.value===((d=s.options)==null?void 0:d.version)}).value,y.value=s.options.referImageUrl}}),(s,o)=>{const i=ya,m=ia,d=ua,w=pa,W=ma,X=oa,aa=ra,la=na;return r(),v(h,null,[e("div",D,[l(i,{tag:"b"},{default:t(()=>[n("\u753B\u9762\u63CF\u8FF0")]),_:1}),l(i,{tag:"p"},{default:t(()=>[n("\u5EFA\u8BAE\u4F7F\u7528\u201C\u5F62\u5BB9\u8BCD+\u52A8\u8BCD+\u98CE\u683C\u201D\u7684\u683C\u5F0F\uFF0C\u4F7F\u7528\u201C\uFF0C\u201D\u9694\u5F00.")]),_:1}),l(m,{modelValue:u(p),"onUpdate:modelValue":o[0]||(o[0]=a=>z(p)?p.value=a:null),maxlength:"1024",rows:"5",class:"w-100% mt-15px","input-style":"border-radius: 7px;",placeholder:"\u4F8B\u5982\uFF1A\u7AE5\u8BDD\u91CC\u7684\u5C0F\u5C4B\u5E94\u8BE5\u662F\u4EC0\u4E48\u6837\u5B50\uFF1F","show-word-limit":"",type:"textarea"},null,8,["modelValue"])]),e("div",E,[e("div",null,[l(i,{tag:"b"},{default:t(()=>[n("\u968F\u673A\u70ED\u8BCD")]),_:1})]),l(w,{wrap:"",class:"word-list"},{default:t(()=>[(r(!0),v(h,null,V(u(ha),a=>(r(),B(d,{round:"",class:"btn",type:u(g)===a?"primary":"default",key:a,onClick:q=>(async k=>{g.value!=k?(g.value=k,p.value=k):g.value=""})(a)},{default:t(()=>[n(x(a),1)]),_:2},1032,["type","onClick"]))),128))]),_:1})]),e("div",M,[e("div",null,[l(i,{tag:"b"},{default:t(()=>[n("\u5C3A\u5BF8")]),_:1})]),l(w,{wrap:"",class:"size-list"},{default:t(()=>[(r(!0),v(h,null,V(u(U),a=>(r(),v("div",{class:"size-item",key:a.key,onClick:q=>(async k=>{b.value=k.key})(a)},[e("div",{class:F(u(b)===a.key?"size-wrapper selectImageSize":"size-wrapper")},[e("div",{style:sa(a.style)},null,4)],2),e("div",N,x(a.key),1)],8,j))),128))]),_:1})]),e("div",J,[e("div",null,[l(i,{tag:"b"},{default:t(()=>[n("\u6A21\u578B")]),_:1})]),l(w,{wrap:"",class:"model-list"},{default:t(()=>[(r(!0),v(h,null,V(u(P),a=>(r(),v("div",{class:F(u(I)===a.key?"modal-item selectModel":"modal-item"),key:a.key},[l(W,{src:a.image,fit:"contain",onClick:q=>$(a)},null,8,["src","onClick"]),e("div",S,x(a.name),1)],2))),128))]),_:1})]),e("div",O,[e("div",null,[l(i,{tag:"b"},{default:t(()=>[n("\u7248\u672C")]),_:1})]),l(w,{wrap:"",class:"version-list"},{default:t(()=>[l(aa,{modelValue:u(_),"onUpdate:modelValue":o[1]||(o[1]=a=>z(_)?_.value=a:null),class:"version-select !w-350px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u7248\u672C"},{default:t(()=>[(r(!0),v(h,null,V(u(f),a=>(r(),B(X,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),e("div",R,[e("div",null,[l(i,{tag:"b"},{default:t(()=>[n("\u53C2\u8003\u56FE")]),_:1})]),l(w,{wrap:"",class:"model-list"},{default:t(()=>[l(la,{modelValue:u(y),"onUpdate:modelValue":o[2]||(o[2]=a=>z(y)?y.value=a:null),height:"120px",width:"120px"},null,8,["modelValue"])]),_:1})]),e("div",Y,[l(d,{type:"primary",size:"large",round:"",onClick:T},{default:t(()=>[n(x(u(C)?"\u751F\u6210\u4E2D":"\u751F\u6210\u5185\u5BB9"),1)]),_:1})])],64)}}}),[["__scopeId","data-v-e836f1d9"]])});export{ba as __tla,G as default};
