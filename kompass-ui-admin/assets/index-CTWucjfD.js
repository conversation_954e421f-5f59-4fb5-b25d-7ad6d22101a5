import{_ as Pe,__tla as Re}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{d as Se,p as $,r as v,S as we,u as Ae,C as Ce,T as ee,o,c as p,i as l,w as a,F as b,a as e,I as ge,j as u,t as _,G as k,l as n,a9 as c,H as ae,k as S,aF as y,g as m,aG as Ie,av as De,aH as Ne,N as Ue,ax as Fe,P as Oe,Q as Ye,E as Le,s as je,v as He,a5 as Ke,a6 as Me,B as Ve,__tla as Xe}from"./index-BUSn51wb.js";import{E as xe,a as Ge,__tla as qe}from"./el-timeline-item-D8aDRTsd.js";import{E as Be,a as Je,__tla as Qe}from"./el-descriptions-item-dD3qa0ub.js";import{_ as ze,__tla as We}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{g as Ze,a as $e,p as ea,__tla as aa}from"./index-BQq32Shw.js";import{f as I,__tla as la}from"./formatTime-DWdBpgsM.js";import{_ as ta,__tla as ra}from"./OrderUpdateRemarkForm.vue_vue_type_script_setup_true_lang-BDmCxfGu.js";import{_ as sa,__tla as ua}from"./OrderDeliveryForm.vue_vue_type_script_setup_true_lang-MqxSrqAW.js";import{_ as _a,__tla as oa}from"./OrderUpdateAddressForm.vue_vue_type_script_setup_true_lang-C4Mvw3HQ.js";import{_ as ia,__tla as na}from"./OrderUpdatePriceForm.vue_vue_type_script_setup_true_lang-Cs5XmatY.js";import{g as ca,__tla as da}from"./index-H6D82e8c.js";import{u as pa,__tla as ya}from"./tagsView-BOOrxb3Q.js";import{D as E,T as le}from"./constants-A8BI3pz7.js";import{g as ma,__tla as fa}from"./index-BmYfnmm4.js";import{__tla as va}from"./el-card-CJbXGyyg.js";import"./color-BN7ZL7BD.js";import{__tla as ba}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as ka}from"./el-tree-select-CBuha0HW.js";import{__tla as Ea}from"./index-CyP7ZSdX.js";import"./tree-BMa075Oj.js";let te,Ta=Promise.all([(()=>{try{return Re}catch{}})(),(()=>{try{return Xe}catch{}})(),(()=>{try{return qe}catch{}})(),(()=>{try{return Qe}catch{}})(),(()=>{try{return We}catch{}})(),(()=>{try{return aa}catch{}})(),(()=>{try{return la}catch{}})(),(()=>{try{return ra}catch{}})(),(()=>{try{return ua}catch{}})(),(()=>{try{return oa}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return ya}catch{}})(),(()=>{try{return fa}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return ba}catch{}})(),(()=>{try{return ka}catch{}})(),(()=>{try{return Ea}catch{}})()]).then(async()=>{let f,N,U,F,O,Y,L,j,H,K,M;f=T=>(Ke("data-v-483b862d"),T=T(),Me(),T),N=f(()=>m("span",{style:{color:"red"}},"\u63D0\u9192: ",-1)),U=f(()=>m("br",null,null,-1)),F=f(()=>m("br",null,null,-1)),O=f(()=>m("span",{style:{color:"red"}},"\u4F18\u60E0\u52B5\u4F18\u60E0: ",-1)),Y=f(()=>m("span",{style:{color:"red"}},"VIP \u4F18\u60E0: ",-1)),L=f(()=>m("span",{style:{color:"red"}},"\u6D3B\u52A8\u4F18\u60E0: ",-1)),j=f(()=>m("span",{style:{color:"red"}},"\u79EF\u5206\u62B5\u6263: ",-1)),H={key:0},K={key:1},M={class:"el-timeline-right-content"},te=Ve(Se({name:"TradeOrderDetail",__name:"index",props:{id:$.number.def(void 0),showPickUp:$.bool.def(!0)},setup(T){const C=ge(),re=i=>{const g=Ne(k.USER_TYPE,i);switch(g==null?void 0:g.colorType){case"success":return"#67C23A";case"info":return"#909399";case"warning":return"#E6A23C";case"danger":return"#F56C6C"}return"#409EFF"},t=v({logs:[]}),V=v(),se=()=>{var i;(i=V.value)==null||i.open(t.value)},X=v(),ue=()=>{var i;(i=X.value)==null||i.open(t.value)},x=v(),_e=()=>{var i;(i=x.value)==null||i.open(t.value)},G=v(),oe=()=>{var i;(i=G.value)==null||i.open(t.value)},ie=async()=>{try{await C.confirm("\u786E\u8BA4\u6838\u9500\u8BA2\u5355\u5417\uFF1F"),await ea(t.value.id),C.success("\u6838\u9500\u6210\u529F"),await h()}catch{}},{params:ne}=we(),ce=T,q=ne.id||ce.id,h=async()=>{if(q){const i=await $e(q);i||(C.error("\u4EA4\u6613\u8BA2\u5355\u4E0D\u5B58\u5728"),me()),t.value=i}},{delView:de}=pa(),{push:pe,currentRoute:ye}=Ae(),me=()=>{de(e(ye)),pe({name:"TradeOrder"})},fe=()=>{C.success("\u590D\u5236\u6210\u529F")},B=v([]),D=v([]),J=v({});return Ce(async()=>{await h(),t.value.deliveryType===E.EXPRESS.type?(B.value=await ca(),form.value.logisticsId&&(D.value=await Ze(t.value.id))):t.value.deliveryType===E.PICK_UP.type&&(J.value=await ma(t.value.pickUpStoreId))}),(i,g)=>{const r=Be,P=ze,R=Je,w=Ue,ve=Fe,A=Oe,be=Ye,Q=Le,ke=je,Ee=He,z=xe,W=Ge,Te=Pe,he=ee("hasPermi"),Z=ee("clipboard");return o(),p(b,null,[l(Te,null,{default:a(()=>[l(R,{title:"\u8BA2\u5355\u4FE1\u606F"},{default:a(()=>[l(r,{label:"\u8BA2\u5355\u53F7: "},{default:a(()=>[u(_(e(t).no),1)]),_:1}),l(r,{label:"\u4E70\u5BB6: "},{default:a(()=>{var s,d;return[u(_((d=(s=e(t))==null?void 0:s.user)==null?void 0:d.nickname),1)]}),_:1}),l(r,{label:"\u8BA2\u5355\u7C7B\u578B: "},{default:a(()=>[l(P,{type:e(k).TRADE_ORDER_TYPE,value:e(t).type},null,8,["type","value"])]),_:1}),l(r,{label:"\u8BA2\u5355\u6765\u6E90: "},{default:a(()=>[l(P,{type:e(k).TERMINAL,value:e(t).terminal},null,8,["type","value"])]),_:1}),l(r,{label:"\u4E70\u5BB6\u7559\u8A00: "},{default:a(()=>[u(_(e(t).userRemark),1)]),_:1}),l(r,{label:"\u5546\u5BB6\u5907\u6CE8: "},{default:a(()=>[u(_(e(t).remark),1)]),_:1}),l(r,{label:"\u652F\u4ED8\u5355\u53F7: "},{default:a(()=>[u(_(e(t).payOrderId),1)]),_:1}),l(r,{label:"\u4ED8\u6B3E\u65B9\u5F0F: "},{default:a(()=>[l(P,{type:e(k).PAY_CHANNEL_CODE,value:e(t).payChannelCode},null,8,["type","value"])]),_:1}),e(t).brokerageUser?(o(),n(r,{key:0,label:"\u63A8\u5E7F\u7528\u6237: "},{default:a(()=>{var s;return[u(_((s=e(t).brokerageUser)==null?void 0:s.nickname),1)]}),_:1})):c("",!0)]),_:1}),l(R,{column:1,title:"\u8BA2\u5355\u72B6\u6001"},{default:a(()=>[l(r,{label:"\u8BA2\u5355\u72B6\u6001: "},{default:a(()=>[l(P,{type:e(k).TRADE_ORDER_STATUS,value:e(t).status},null,8,["type","value"])]),_:1}),ae((o(),n(r,{"label-class-name":"no-colon"},{default:a(()=>[e(t).status===e(le).UNPAID.status?(o(),n(w,{key:0,type:"primary",onClick:oe},{default:a(()=>[u(" \u8C03\u6574\u4EF7\u683C ")]),_:1})):c("",!0),l(w,{type:"primary",onClick:se},{default:a(()=>[u("\u5907\u6CE8")]),_:1}),e(t).status===e(le).UNDELIVERED.status?(o(),p(b,{key:1},[e(t).deliveryType===e(E).EXPRESS.type?(o(),n(w,{key:0,type:"primary",onClick:ue},{default:a(()=>[u(" \u53D1\u8D27 ")]),_:1})):c("",!0),e(t).deliveryType===e(E).EXPRESS.type?(o(),n(w,{key:1,type:"primary",onClick:_e},{default:a(()=>[u(" \u4FEE\u6539\u5730\u5740 ")]),_:1})):c("",!0),e(t).deliveryType===e(E).PICK_UP.type&&T.showPickUp?(o(),n(w,{key:2,type:"primary",onClick:ie},{default:a(()=>[u(" \u6838\u9500 ")]),_:1})):c("",!0)],64)):c("",!0)]),_:1})),[[he,["trade:order:update"]]]),l(r,null,{label:a(()=>[N]),default:a(()=>[u(" \u4E70\u5BB6\u4ED8\u6B3E\u6210\u529F\u540E\uFF0C\u8D27\u6B3E\u5C06\u76F4\u63A5\u8FDB\u5165\u60A8\u7684\u5546\u6237\u53F7\uFF08\u5FAE\u4FE1\u3001\u652F\u4ED8\u5B9D\uFF09"),U,u(" \u8BF7\u53CA\u65F6\u5173\u6CE8\u4F60\u53D1\u51FA\u7684\u5305\u88F9\u72B6\u6001\uFF0C\u786E\u4FDD\u53EF\u4EE5\u914D\u9001\u81F3\u4E70\u5BB6\u624B\u4E2D "),F,u(" \u5982\u679C\u4E70\u5BB6\u8868\u793A\u6CA1\u6536\u5230\u8D27\u6216\u8D27\u7269\u6709\u95EE\u9898\uFF0C\u8BF7\u53CA\u65F6\u8054\u7CFB\u4E70\u5BB6\u5904\u7406\uFF0C\u53CB\u597D\u534F\u5546 ")]),_:1})]),_:1}),l(R,{title:"\u5546\u54C1\u4FE1\u606F"},{default:a(()=>[l(r,{labelClassName:"no-colon"},{default:a(()=>[l(ke,{gutter:20},{default:a(()=>[l(Q,{span:15},{default:a(()=>[l(be,{data:e(t).items,border:""},{default:a(()=>[l(A,{label:"\u5546\u54C1",prop:"spuName",width:"auto"},{default:a(({row:s})=>[u(_(s.spuName)+" ",1),(o(!0),p(b,null,S(s.properties,d=>(o(),n(ve,{key:d.propertyId},{default:a(()=>[u(_(d.propertyName)+": "+_(d.valueName),1)]),_:2},1024))),128))]),_:1}),l(A,{label:"\u5546\u54C1\u539F\u4EF7",prop:"price",width:"150"},{default:a(({row:s})=>[u(_(e(y)(s.price))+"\u5143",1)]),_:1}),l(A,{label:"\u6570\u91CF",prop:"count",width:"100"}),l(A,{label:"\u5408\u8BA1",prop:"payPrice",width:"150"},{default:a(({row:s})=>[u(_(e(y)(s.payPrice))+"\u5143",1)]),_:1}),l(A,{label:"\u552E\u540E\u72B6\u6001",prop:"afterSaleStatus",width:"120"},{default:a(({row:s})=>[l(P,{type:e(k).TRADE_ORDER_ITEM_AFTER_SALE_STATUS,value:s.afterSaleStatus},null,8,["type","value"])]),_:1})]),_:1},8,["data"])]),_:1}),l(Q,{span:10})]),_:1})]),_:1})]),_:1}),l(R,{column:4},{default:a(()=>[l(r,{label:"\u5546\u54C1\u603B\u989D: "},{default:a(()=>[u(_(e(y)(e(t).totalPrice))+" \u5143 ",1)]),_:1}),l(r,{label:"\u8FD0\u8D39\u91D1\u989D: "},{default:a(()=>[u(_(e(y)(e(t).deliveryPrice))+" \u5143 ",1)]),_:1}),l(r,{label:"\u8BA2\u5355\u8C03\u4EF7: "},{default:a(()=>[u(_(e(y)(e(t).adjustPrice))+" \u5143 ",1)]),_:1}),(o(),p(b,null,S(1,s=>l(r,{key:s,"label-class-name":"no-colon"})),64)),l(r,null,{label:a(()=>[O]),default:a(()=>[u(" "+_(e(y)(e(t).couponPrice))+" \u5143 ",1)]),_:1}),l(r,null,{label:a(()=>[Y]),default:a(()=>[u(" "+_(e(y)(e(t).vipPrice))+" \u5143 ",1)]),_:1}),l(r,null,{label:a(()=>[L]),default:a(()=>[u(" "+_(e(y)(e(t).discountPrice))+" \u5143 ",1)]),_:1}),l(r,null,{label:a(()=>[j]),default:a(()=>[u(" "+_(e(y)(e(t).pointPrice))+" \u5143 ",1)]),_:1}),(o(),p(b,null,S(3,s=>l(r,{key:s,"label-class-name":"no-colon"})),64)),l(r,{label:"\u5E94\u4ED8\u91D1\u989D: "},{default:a(()=>[u(_(e(y)(e(t).payPrice))+" \u5143 ",1)]),_:1})]),_:1}),l(R,{column:4,title:"\u6536\u8D27\u4FE1\u606F"},{default:a(()=>[l(r,{label:"\u914D\u9001\u65B9\u5F0F: "},{default:a(()=>[l(P,{type:e(k).TRADE_DELIVERY_TYPE,value:e(t).deliveryType},null,8,["type","value"])]),_:1}),l(r,{label:"\u6536\u8D27\u4EBA: "},{default:a(()=>[u(_(e(t).receiverName),1)]),_:1}),l(r,{label:"\u8054\u7CFB\u7535\u8BDD: "},{default:a(()=>[u(_(e(t).receiverMobile),1)]),_:1}),e(t).deliveryType===e(E).EXPRESS.type?(o(),p("div",H,[e(t).receiverDetailAddress?(o(),n(r,{key:0,label:"\u6536\u8D27\u5730\u5740: "},{default:a(()=>[u(_(e(t).receiverAreaName)+" "+_(e(t).receiverDetailAddress)+" ",1),ae(l(Ee,{icon:"ep:document-copy",type:"primary"},null,512),[[Z,e(t).receiverAreaName+" "+e(t).receiverDetailAddress,"copy"],[Z,fe,"success"]])]),_:1})):c("",!0),e(t).logisticsId?(o(),n(r,{key:1,label:"\u7269\u6D41\u516C\u53F8: "},{default:a(()=>{var s;return[u(_((s=e(B).find(d=>d.id===e(t).logisticsId))==null?void 0:s.name),1)]}),_:1})):c("",!0),e(t).logisticsId?(o(),n(r,{key:2,label:"\u8FD0\u5355\u53F7: "},{default:a(()=>[u(_(e(t).logisticsNo),1)]),_:1})):c("",!0),e(I).deliveryTime?(o(),n(r,{key:3,label:"\u53D1\u8D27\u65F6\u95F4: "},{default:a(()=>[u(_(e(I)(e(t).deliveryTime)),1)]),_:1})):c("",!0),(o(),p(b,null,S(2,s=>l(r,{key:s,"label-class-name":"no-colon"})),64)),e(D).length>0?(o(),n(r,{key:4,label:"\u7269\u6D41\u8BE6\u60C5: "},{default:a(()=>[l(W,null,{default:a(()=>[(o(!0),p(b,null,S(e(D),(s,d)=>(o(),n(z,{key:d,timestamp:e(I)(s.time)},{default:a(()=>[u(_(s.content),1)]),_:2},1032,["timestamp"]))),128))]),_:1})]),_:1})):c("",!0)])):c("",!0),e(t).deliveryType===e(E).PICK_UP.type?(o(),p("div",K,[e(t).pickUpStoreId?(o(),n(r,{key:0,label:"\u81EA\u63D0\u95E8\u5E97: "},{default:a(()=>{var s;return[u(_((s=e(J))==null?void 0:s.name),1)]}),_:1})):c("",!0)])):c("",!0)]),_:1}),l(R,{title:"\u8BA2\u5355\u64CD\u4F5C\u65E5\u5FD7"},{default:a(()=>[l(r,{labelClassName:"no-colon"},{default:a(()=>[l(W,null,{default:a(()=>[(o(!0),p(b,null,S(e(t).logs,(s,d)=>(o(),n(z,{key:d,timestamp:e(I)(s.createTime),placement:"top"},{dot:a(()=>[m("span",{style:De({backgroundColor:re(s.userType)}),class:"dot-node-style"},_(e(Ie)(e(k).USER_TYPE,s.userType)[0]),5)]),default:a(()=>[m("div",M,_(s.content),1)]),_:2},1032,["timestamp"]))),128))]),_:1})]),_:1})]),_:1})]),_:1}),l(sa,{ref_key:"deliveryFormRef",ref:X,onSuccess:h},null,512),l(ta,{ref_key:"updateRemarkForm",ref:V,onSuccess:h},null,512),l(_a,{ref_key:"updateAddressFormRef",ref:x,onSuccess:h},null,512),l(ia,{ref_key:"updatePriceFormRef",ref:G,onSuccess:h},null,512)],64)}}}),[["__scopeId","data-v-483b862d"]])});export{Ta as __tla,te as default};
