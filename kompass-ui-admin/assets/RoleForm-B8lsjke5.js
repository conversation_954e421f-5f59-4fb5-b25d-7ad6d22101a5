import{_ as t,__tla as r}from"./RoleForm.vue_vue_type_script_setup_true_lang-tI0G35tN.js";import{__tla as _}from"./index-BUSn51wb.js";import{__tla as a}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import"./constants-A8BI3pz7.js";import{__tla as l}from"./index-CCFX7HyJ.js";let o=Promise.all([(()=>{try{return r}catch{}})(),(()=>{try{return _}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})()]).then(async()=>{});export{o as __tla,t as default};
