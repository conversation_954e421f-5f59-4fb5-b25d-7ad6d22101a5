import{d as K,I as L,n as N,r as d,f as O,C as Q,T as Z,o as s,c as z,i as a,w as e,a as l,U as A,j as o,H as i,l as p,F as B,Z as D,L as W,_ as X,N as E,O as G,P as J,Q as M,R as Y,__tla as $}from"./index-BUSn51wb.js";import{_ as aa,__tla as ea}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as ta,__tla as la}from"./index-COobLwz-.js";import{d as ra,__tla as ca}from"./formatTime-DWdBpgsM.js";import{g as sa,d as oa,__tla as na}from"./index-V4315SLT.js";import{_ as _a,__tla as ua}from"./ProductCategoryForm.vue_vue_type_script_setup_true_lang-DeoOZ7Su.js";import{h as ma}from"./tree-BMa075Oj.js";import{__tla as da}from"./el-card-CJbXGyyg.js";import{__tla as ia}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";let R,pa=Promise.all([(()=>{try{return $}catch{}})(),(()=>{try{return ea}catch{}})(),(()=>{try{return la}catch{}})(),(()=>{try{return ca}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return ua}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return ia}catch{}})()]).then(async()=>{R=K({name:"CrmProductCategory",__name:"index",setup(fa){const w=L(),{t:F}=N(),f=d(!0),C=d([]),n=O({name:null}),g=d(),_=async()=>{f.value=!0;try{const u=await sa(n);C.value=ma(u,"id","parentId")}finally{f.value=!1}},y=()=>{_()},I=()=>{g.value.resetFields(),y()},x=d(),b=(u,t)=>{x.value.open(u,t)};return Q(()=>{_()}),(u,t)=>{const S=ta,V=D,v=W,h=X,r=E,T=G,P=aa,m=J,U=M,k=Z("hasPermi"),j=Y;return s(),z(B,null,[a(S,{title:"\u3010\u4EA7\u54C1\u3011\u4EA7\u54C1\u7BA1\u7406\u3001\u4EA7\u54C1\u5206\u7C7B",url:"https://doc.iocoder.cn/crm/product/"}),a(P,null,{default:e(()=>[a(T,{class:"-mb-15px",model:l(n),ref_key:"queryFormRef",ref:g,inline:!0,"label-width":"68px"},{default:e(()=>[a(v,{label:"\u540D\u79F0",prop:"name"},{default:e(()=>[a(V,{modelValue:l(n).name,"onUpdate:modelValue":t[0]||(t[0]=c=>l(n).name=c),placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0",clearable:"",onKeyup:A(y,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(v,null,{default:e(()=>[a(r,{onClick:y},{default:e(()=>[a(h,{icon:"ep:search",class:"mr-5px"}),o(" \u641C\u7D22")]),_:1}),a(r,{onClick:I},{default:e(()=>[a(h,{icon:"ep:refresh",class:"mr-5px"}),o(" \u91CD\u7F6E")]),_:1}),i((s(),p(r,{type:"primary",plain:"",onClick:t[1]||(t[1]=c=>b("create"))},{default:e(()=>[a(h,{icon:"ep:plus",class:"mr-5px"}),o(" \u65B0\u589E ")]),_:1})),[[k,["crm:product-category:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(P,null,{default:e(()=>[i((s(),p(U,{data:l(C),"row-key":"id","default-expand-all":""},{default:e(()=>[a(m,{label:"\u5206\u7C7B\u7F16\u53F7",align:"center",prop:"id"}),a(m,{label:"\u5206\u7C7B\u540D\u79F0",align:"center",prop:"name"}),a(m,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:l(ra),width:"180px"},null,8,["formatter"]),a(m,{label:"\u64CD\u4F5C",align:"center"},{default:e(c=>[i((s(),p(r,{link:"",type:"primary",onClick:q=>b("update",c.row.id)},{default:e(()=>[o(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[k,["crm:product-category:update"]]]),i((s(),p(r,{link:"",type:"danger",onClick:q=>(async H=>{try{await w.delConfirm(),await oa(H),w.success(F("common.delSuccess")),await _()}catch{}})(c.row.id)},{default:e(()=>[o(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[k,["crm:product-category:delete"]]])]),_:1})]),_:1},8,["data"])),[[j,l(f)]])]),_:1}),a(_a,{ref_key:"formRef",ref:x,onSuccess:_},null,512)],64)}}})});export{pa as __tla,R as default};
