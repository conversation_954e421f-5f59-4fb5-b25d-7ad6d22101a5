import{eD as Vl,r as y,c_ as Jo,C as ke,eK as Ue,dQ as Xo,d as H,bf as pe,b as h,o as V,l as X,w as I,a as e,c as G,av as Tl,a0 as B,b1 as $l,aV as ve,i as o,br as Xe,eL as en,a9 as xe,a3 as et,bg as Pe,bh as Mt,bd as He,bo as Le,bQ as tn,c8 as Fe,be as tt,as as lt,b6 as qe,g as v,b0 as Sl,t as _,bk as at,bP as be,eM as ot,ao as ln,eN as nt,eO as It,eP as an,eQ as on,bW as nn,cQ as sn,f as Be,at as ue,bb as Pl,h as oe,bp as ze,aN as Ll,dB as rn,H as we,a8 as De,F as ne,eR as Bl,cw as un,e4 as cn,bx as dn,c0 as pn,cT as mn,c6 as vn,bN as hn,cS as zl,ay as st,bj as fn,au as rt,j as se,a4 as ee,p as it,k as We,_ as ce,B as de,$ as re,y as Z,n as he,eS as Ge,ce as gn,Z as Vt,a5 as Ol,a6 as Al,eT as jl,eU as xn,e5 as bn,aK as El,dZ as wn,d_ as Tt,q as yn,N as $t,eV as ut,eW as Ie,u as Te,a7 as ct,b2 as Ve,cs as Rl,aD as St,eX as Nl,eY as _n,eg as Cn,z as kn,A as Mn,ap as In,aq as Vn,e as Pt,L as Tn,O as $n,eZ as Sn,e_ as Pn,c7 as Ln,e$ as Bn,f0 as zn,dJ as On,dz as An,f1 as jn,a2 as En,f2 as Rn,T as dt,__tla as Nn}from"./index-BUSn51wb.js";import{E as Un,__tla as Hn}from"./el-drawer-DMK0hKF6.js";import{c as Ul,l as Hl,h as Fl}from"./color-BN7ZL7BD.js";import{T as Fn,_ as qn,__tla as Dn}from"./LocaleDropdown.vue_vue_type_script_setup_true_lang-B8uf-ZMj.js";import{u as Wn}from"./useWatermark-BmKapRf9.js";import{c as Gn,f as Kn,g as Qn}from"./tree-BMa075Oj.js";import{u as pt,__tla as Zn}from"./tagsView-BOOrxb3Q.js";import{E as Lt,a as Bt,b as zt,__tla as Yn}from"./el-dropdown-item-CIJXMVYa.js";import{_ as Jn}from"./logo-DQEDlIK-.js";import{_ as Xn,__tla as es}from"./XButton-BjahQbul.js";import{a as mt}from"./avatar-BG6NdH5s.js";import{f as ts,__tla as ls}from"./formatTime-DWdBpgsM.js";import{c as as,d as os,__tla as ns}from"./index-DjJb6f82.js";import{E as ss,__tla as rs}from"./el-avatar-Da2TGjmj.js";import{_ as is,__tla as us}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{u as cs,__tla as ds}from"./useValidator-C1ftTumK.js";import{d as ps,__tla as ms}from"./dateUtil-DsBocVGV.js";import{__tla as vs}from"./useIcon-th7lSKBX.js";let ql,hs=Promise.all([(()=>{try{return Nn}catch{}})(),(()=>{try{return Hn}catch{}})(),(()=>{try{return Dn}catch{}})(),(()=>{try{return Zn}catch{}})(),(()=>{try{return Yn}catch{}})(),(()=>{try{return es}catch{}})(),(()=>{try{return ls}catch{}})(),(()=>{try{return ns}catch{}})(),(()=>{try{return rs}catch{}})(),(()=>{try{return us}catch{}})(),(()=>{try{return ds}catch{}})(),(()=>{try{return ms}catch{}})(),(()=>{try{return vs}catch{}})()]).then(async()=>{const Dl={visibilityHeight:{type:Number,default:200},target:{type:String,default:""},right:{type:Number,default:40},bottom:{type:Number,default:40}},Wl={click:t=>t instanceof MouseEvent},Ot="ElBacktop",Gl=H({name:Ot}),Kl=Mt(Pe(H({...Gl,props:Dl,emits:Wl,setup(t,{emit:l}){const a=t,n=pe("backtop"),{handleClick:s,visible:i}=((c,d,m)=>{const p=Vl(),f=Vl(),g=y(!1),L=()=>{p.value&&(g.value=p.value.scrollTop>=c.visibilityHeight)},P=Xo(L,300,!0);return Jo(f,"scroll",P),ke(()=>{var u;f.value=document,p.value=document.documentElement,c.target&&(p.value=(u=document.querySelector(c.target))!=null?u:void 0,p.value||Ue(m,`target does not exist: ${c.target}`),f.value=p.value),L()}),{visible:g,handleClick:u=>{var x;(x=p.value)==null||x.scrollTo({top:0,behavior:"smooth"}),d("click",u)}}})(a,l,Ot),r=h(()=>({right:`${a.right}px`,bottom:`${a.bottom}px`}));return(c,d)=>(V(),X(et,{name:`${e(n).namespace.value}-fade-in`},{default:I(()=>[e(i)?(V(),G("div",{key:0,style:Tl(e(r)),class:B(e(n).b()),onClick:d[0]||(d[0]=$l((...m)=>e(s)&&e(s)(...m),["stop"]))},[ve(c.$slots,"default",{},()=>[o(e(Xe),{class:B(e(n).e("icon"))},{default:I(()=>[o(e(en))]),_:1},8,["class"])])],6)):xe("v-if",!0)]),_:3},8,["name"]))}}),[["__file","backtop.vue"]])),At=Symbol("breadcrumbKey"),Ql=He({separator:{type:String,default:"/"},separatorIcon:{type:Le}}),Zl=["aria-label"],Yl=H({name:"ElBreadcrumb"}),Jl=H({...Yl,props:Ql,setup(t){const l=t,{t:a}=tn(),n=pe("breadcrumb"),s=y();return Fe(At,l),ke(()=>{const i=s.value.querySelectorAll(`.${n.e("item")}`);i.length&&i[i.length-1].setAttribute("aria-current","page")}),(i,r)=>(V(),G("div",{ref_key:"breadcrumb",ref:s,class:B(e(n).b()),"aria-label":e(a)("el.breadcrumb.label"),role:"navigation"},[ve(i.$slots,"default")],10,Zl))}});var Xl=Pe(Jl,[["__file","breadcrumb.vue"]]);const ea=He({to:{type:tt([String,Object]),default:""},replace:{type:Boolean,default:!1}}),ta=H({name:"ElBreadcrumbItem"});var jt=Pe(H({...ta,props:ea,setup(t){const l=t,a=lt(),n=qe(At,void 0),s=pe("breadcrumb"),i=a.appContext.config.globalProperties.$router,r=y(),c=()=>{l.to&&i&&(l.replace?i.replace(l.to):i.push(l.to))};return(d,m)=>{var p,f;return V(),G("span",{class:B(e(s).e("item"))},[v("span",{ref_key:"link",ref:r,class:B([e(s).e("inner"),e(s).is("link",!!d.to)]),role:"link",onClick:c},[ve(d.$slots,"default")],2),(p=e(n))!=null&&p.separatorIcon?(V(),X(e(Xe),{key:0,class:B(e(s).e("separator"))},{default:I(()=>[(V(),X(Sl(e(n).separatorIcon)))]),_:1},8,["class"])):(V(),G("span",{key:1,class:B(e(s).e("separator")),role:"presentation"},_((f=e(n))==null?void 0:f.separator),3))],2)}}}),[["__file","breadcrumb-item.vue"]]);const la=Mt(Xl,{BreadcrumbItem:jt}),aa=at(jt);let oa=class{constructor(t,l){this.parent=t,this.domNode=l,this.subIndex=0,this.subIndex=0,this.init()}init(){this.subMenuItems=this.domNode.querySelectorAll("li"),this.addListeners()}gotoSubIndex(t){t===this.subMenuItems.length?t=0:t<0&&(t=this.subMenuItems.length-1),this.subMenuItems[t].focus(),this.subIndex=t}addListeners(){const t=this.parent.domNode;Array.prototype.forEach.call(this.subMenuItems,l=>{l.addEventListener("keydown",a=>{let n=!1;switch(a.code){case be.down:this.gotoSubIndex(this.subIndex+1),n=!0;break;case be.up:this.gotoSubIndex(this.subIndex-1),n=!0;break;case be.tab:ot(t,"mouseleave");break;case be.enter:case be.space:n=!0,a.currentTarget.click()}return n&&(a.preventDefault(),a.stopPropagation()),!1})})}},na=class{constructor(t,l){this.domNode=t,this.submenu=null,this.submenu=null,this.init(l)}init(t){this.domNode.setAttribute("tabindex","0");const l=this.domNode.querySelector(`.${t}-menu`);l&&(this.submenu=new oa(this,l)),this.addListeners()}addListeners(){this.domNode.addEventListener("keydown",t=>{let l=!1;switch(t.code){case be.down:ot(t.currentTarget,"mouseenter"),this.submenu&&this.submenu.gotoSubIndex(0),l=!0;break;case be.up:ot(t.currentTarget,"mouseenter"),this.submenu&&this.submenu.gotoSubIndex(this.submenu.subMenuItems.length-1),l=!0;break;case be.tab:ot(t.currentTarget,"mouseleave");break;case be.enter:case be.space:l=!0,t.currentTarget.click()}l&&t.preventDefault()})}},sa=class{constructor(t,l){this.domNode=t,this.init(l)}init(t){const l=this.domNode.childNodes;Array.from(l).forEach(a=>{a.nodeType===1&&new na(a,t)})}};var ra=Pe(H({name:"ElMenuCollapseTransition",setup(){const t=pe("menu");return{listeners:{onBeforeEnter:l=>l.style.opacity="0.2",onEnter(l,a){nt(l,`${t.namespace.value}-opacity-transition`),l.style.opacity="1",a()},onAfterEnter(l){It(l,`${t.namespace.value}-opacity-transition`),l.style.opacity=""},onBeforeLeave(l){l.dataset||(l.dataset={}),an(l,t.m("collapse"))?(It(l,t.m("collapse")),l.dataset.oldOverflow=l.style.overflow,l.dataset.scrollWidth=l.clientWidth.toString(),nt(l,t.m("collapse"))):(nt(l,t.m("collapse")),l.dataset.oldOverflow=l.style.overflow,l.dataset.scrollWidth=l.clientWidth.toString(),It(l,t.m("collapse"))),l.style.width=`${l.scrollWidth}px`,l.style.overflow="hidden"},onLeave(l){nt(l,"horizontal-collapse-transition"),l.style.width=`${l.dataset.scrollWidth}px`}}}}}),[["render",function(t,l,a,n,s,i){return V(),X(et,ln({mode:"out-in"},t.listeners),{default:I(()=>[ve(t.$slots,"default")]),_:3},16)}],["__file","menu-collapse-transition.vue"]]);function Et(t,l){const a=h(()=>{let n=t.parent;const s=[l.value];for(;n.type.name!=="ElMenu";)n.props.index&&s.unshift(n.props.index),n=n.parent;return s});return{parentMenu:h(()=>{let n=t.parent;for(;n&&!["ElMenu","ElSubMenu"].includes(n.type.name);)n=n.parent;return n}),indexPath:a}}function ia(t){return h(()=>{const l=t.backgroundColor;return l?new on(l).shade(20).toString():""})}const Rt=(t,l)=>{const a=pe("menu");return h(()=>a.cssVarBlock({"text-color":t.textColor||"","hover-text-color":t.textColor||"","bg-color":t.backgroundColor||"","hover-bg-color":ia(t).value||"","active-color":t.activeTextColor||"",level:`${l}`}))},ua=He({index:{type:String,required:!0},showTimeout:Number,hideTimeout:Number,popperClass:String,disabled:Boolean,teleported:{type:Boolean,default:void 0},popperOffset:Number,expandCloseIcon:{type:Le},expandOpenIcon:{type:Le},collapseCloseIcon:{type:Le},collapseOpenIcon:{type:Le}}),vt="ElSubMenu";var ht=H({name:vt,props:ua,setup(t,{slots:l,expose:a}){const n=lt(),{indexPath:s,parentMenu:i}=Et(n,h(()=>t.index)),r=pe("menu"),c=pe("sub-menu"),d=qe("rootMenu");d||Ue(vt,"can not inject root menu");const m=qe(`subMenu:${i.value.uid}`);m||Ue(vt,"can not inject sub menu");const p=y({}),f=y({});let g;const L=y(!1),P=y(),u=y(null),x=h(()=>D.value==="horizontal"&&M.value?"bottom-start":"right-start"),w=h(()=>D.value==="horizontal"&&M.value||D.value==="vertical"&&!d.props.collapse?t.expandCloseIcon&&t.expandOpenIcon?K.value?t.expandOpenIcon:t.expandCloseIcon:nn:t.collapseCloseIcon&&t.collapseOpenIcon?K.value?t.collapseOpenIcon:t.collapseCloseIcon:sn),M=h(()=>m.level===0),F=h(()=>{const k=t.teleported;return k===void 0?M.value:k}),q=h(()=>d.props.collapse?`${r.namespace.value}-zoom-in-left`:`${r.namespace.value}-zoom-in-top`),R=h(()=>D.value==="horizontal"&&M.value?["bottom-start","bottom-end","top-start","top-end","right-start","left-start"]:["right-start","right","right-end","left-start","bottom-start","bottom-end","top-start","top-end"]),K=h(()=>d.openedMenus.includes(t.index)),N=h(()=>{let k=!1;return Object.values(p.value).forEach(U=>{U.active&&(k=!0)}),Object.values(f.value).forEach(U=>{U.active&&(k=!0)}),k}),D=h(()=>d.props.mode),b=Be({index:t.index,indexPath:s,active:N}),C=Rt(d.props,m.level+1),z=h(()=>{var k;return(k=t.popperOffset)!=null?k:d.props.popperOffset}),W=h(()=>{var k;return(k=t.popperClass)!=null?k:d.props.popperClass}),E=h(()=>{var k;return(k=t.showTimeout)!=null?k:d.props.showTimeout}),te=h(()=>{var k;return(k=t.hideTimeout)!=null?k:d.props.hideTimeout}),ie=k=>{var U,Q,$;k||($=(Q=(U=u.value)==null?void 0:U.popperRef)==null?void 0:Q.popperInstanceRef)==null||$.destroy()},le=()=>{d.props.menuTrigger==="hover"&&d.props.mode==="horizontal"||d.props.collapse&&d.props.mode==="vertical"||t.disabled||d.handleSubMenuClick({index:t.index,indexPath:s.value,active:N.value})},T=(k,U=E.value)=>{var Q;k.type!=="focus"&&(d.props.menuTrigger==="click"&&d.props.mode==="horizontal"||!d.props.collapse&&d.props.mode==="vertical"||t.disabled?m.mouseInChild.value=!0:(m.mouseInChild.value=!0,g==null||g(),{stop:g}=Bl(()=>{d.openMenu(t.index,s.value)},U),F.value&&((Q=i.value.vnode.el)==null||Q.dispatchEvent(new MouseEvent("mouseenter")))))},O=(k=!1)=>{var U;d.props.menuTrigger==="click"&&d.props.mode==="horizontal"||!d.props.collapse&&d.props.mode==="vertical"?m.mouseInChild.value=!1:(g==null||g(),m.mouseInChild.value=!1,{stop:g}=Bl(()=>!L.value&&d.closeMenu(t.index,s.value),te.value),F.value&&k&&((U=m.handleMouseleave)==null||U.call(m,!0)))};ue(()=>d.props.collapse,k=>ie(!!k));{const k=Q=>{f.value[Q.index]=Q},U=Q=>{delete f.value[Q.index]};Fe(`subMenu:${n.uid}`,{addSubMenu:k,removeSubMenu:U,handleMouseleave:O,mouseInChild:L,level:m.level+1})}return a({opened:K}),ke(()=>{d.addSubMenu(b),m.addSubMenu(b)}),Pl(()=>{m.removeSubMenu(b),d.removeSubMenu(b)}),()=>{var k;const U=[(k=l.title)==null?void 0:k.call(l),oe(Xe,{class:c.e("icon-arrow"),style:{transform:K.value?t.expandCloseIcon&&t.expandOpenIcon||t.collapseCloseIcon&&t.collapseOpenIcon&&d.props.collapse?"none":"rotateZ(180deg)":"none"}},{default:()=>ze(w.value)?oe(n.appContext.components[w.value]):oe(w.value)})],Q=d.isMenuPopup?oe(Ll,{ref:u,visible:K.value,effect:"light",pure:!0,offset:z.value,showArrow:!1,persistent:!0,popperClass:W.value,placement:x.value,teleported:F.value,fallbackPlacements:R.value,transition:q.value,gpuAcceleration:!1},{content:()=>{var $;return oe("div",{class:[r.m(D.value),r.m("popup-container"),W.value],onMouseenter:S=>T(S,100),onMouseleave:()=>O(!0),onFocus:S=>T(S,100)},[oe("ul",{class:[r.b(),r.m("popup"),r.m(`popup-${x.value}`)],style:C.value},[($=l.default)==null?void 0:$.call(l)])])},default:()=>oe("div",{class:c.e("title"),onClick:le},U)}):oe(ne,{},[oe("div",{class:c.e("title"),ref:P,onClick:le},U),oe(rn,{},{default:()=>{var $;return we(oe("ul",{role:"menu",class:[r.b(),r.m("inline")],style:C.value},[($=l.default)==null?void 0:$.call(l)]),[[De,K.value]])}})]);return oe("li",{class:[c.b(),c.is("active",N.value),c.is("opened",K.value),c.is("disabled",t.disabled)],role:"menuitem",ariaHaspopup:!0,ariaExpanded:K.value,onMouseenter:T,onMouseleave:()=>O(),onFocus:T},[Q])}}});const ca=He({mode:{type:String,values:["horizontal","vertical"],default:"vertical"},defaultActive:{type:String,default:""},defaultOpeneds:{type:tt(Array),default:()=>un([])},uniqueOpened:Boolean,router:Boolean,menuTrigger:{type:String,values:["hover","click"],default:"hover"},collapse:Boolean,backgroundColor:String,textColor:String,activeTextColor:String,closeOnClickOutside:Boolean,collapseTransition:{type:Boolean,default:!0},ellipsis:{type:Boolean,default:!0},popperOffset:{type:Number,default:6},ellipsisIcon:{type:Le,default:()=>cn},popperEffect:{type:String,values:["dark","light"],default:"dark"},popperClass:String,showTimeout:{type:Number,default:300},hideTimeout:{type:Number,default:300}}),ft=t=>Array.isArray(t)&&t.every(l=>ze(l));var da=H({name:"ElMenu",props:ca,emits:{close:(t,l)=>ze(t)&&ft(l),open:(t,l)=>ze(t)&&ft(l),select:(t,l,a,n)=>ze(t)&&ft(l)&&hn(a)&&(n===void 0||n instanceof Promise)},setup(t,{emit:l,slots:a,expose:n}){const s=lt(),i=s.appContext.config.globalProperties.$router,r=y(),c=pe("menu"),d=pe("sub-menu"),m=y(-1),p=y(t.defaultOpeneds&&!t.collapse?t.defaultOpeneds.slice(0):[]),f=y(t.defaultActive),g=y({}),L=y({}),P=h(()=>t.mode==="horizontal"||t.mode==="vertical"&&t.collapse),u=(b,C)=>{p.value.includes(b)||(t.uniqueOpened&&(p.value=p.value.filter(z=>C.includes(z))),p.value.push(b),l("open",b,C))},x=b=>{const C=p.value.indexOf(b);C!==-1&&p.value.splice(C,1)},w=(b,C)=>{x(b),l("close",b,C)},M=({index:b,indexPath:C})=>{p.value.includes(b)?w(b,C):u(b,C)},F=b=>{(t.mode==="horizontal"||t.collapse)&&(p.value=[]);const{index:C,indexPath:z}=b;if(!zl(C)&&!zl(z))if(t.router&&i){const W=b.route||C,E=i.push(W).then(te=>(te||(f.value=C),te));l("select",C,z,{index:C,indexPath:z,route:W},E)}else f.value=C,l("select",C,z,{index:C,indexPath:z})},q=()=>{var b,C;if(!r.value)return-1;const z=Array.from((C=(b=r.value)==null?void 0:b.childNodes)!=null?C:[]).filter(O=>O.nodeName!=="#comment"&&(O.nodeName!=="#text"||O.nodeValue)),W=getComputedStyle(r.value),E=Number.parseInt(W.paddingLeft,10),te=Number.parseInt(W.paddingRight,10),ie=r.value.clientWidth-E-te;let le=0,T=0;return z.forEach((O,k)=>{le+=(U=>{const Q=getComputedStyle(U),$=Number.parseInt(Q.marginLeft,10),S=Number.parseInt(Q.marginRight,10);return U.offsetWidth+$+S||0})(O),le<=ie-64&&(T=k+1)}),T===z.length?-1:T};let R=!0;const K=()=>{if(m.value===q())return;const b=()=>{m.value=-1,st(()=>{m.value=q()})};R?b():((C,z=33.34)=>{let W;return()=>{W&&clearTimeout(W),W=setTimeout(()=>{C()},z)}})(b)(),R=!1};let N;ue(()=>t.defaultActive,b=>{g.value[b]||(f.value=""),(C=>{const z=g.value,W=z[C]||f.value&&z[f.value]||z[t.defaultActive];f.value=W?W.index:C})(b)}),ue(()=>t.collapse,b=>{b&&(p.value=[])}),ue(g.value,()=>{const b=f.value&&g.value[f.value];!b||t.mode==="horizontal"||t.collapse||b.indexPath.forEach(C=>{const z=L.value[C];z&&u(C,z.indexPath)})}),dn(()=>{t.mode==="horizontal"&&t.ellipsis?N=pn(r,K).stop:N==null||N()});const D=y(!1);{const b=E=>{L.value[E.index]=E},C=E=>{delete L.value[E.index]};Fe("rootMenu",Be({props:t,openedMenus:p,items:g,subMenus:L,activeIndex:f,isMenuPopup:P,addMenuItem:E=>{g.value[E.index]=E},removeMenuItem:E=>{delete g.value[E.index]},addSubMenu:b,removeSubMenu:C,openMenu:u,closeMenu:w,handleMenuItemClick:F,handleSubMenuClick:M})),Fe(`subMenu:${s.uid}`,{addSubMenu:b,removeSubMenu:C,mouseInChild:D,level:0})}return ke(()=>{t.mode==="horizontal"&&new sa(s.vnode.el,c.namespace.value)}),n({open:b=>{const{indexPath:C}=L.value[b];C.forEach(z=>u(z,C))},close:x,handleResize:K}),()=>{var b,C;let z=(C=(b=a.default)==null?void 0:b.call(a))!=null?C:[];const W=[];if(t.mode==="horizontal"&&r.value){const le=mn(z),T=m.value===-1?le:le.slice(0,m.value),O=m.value===-1?[]:le.slice(m.value);O!=null&&O.length&&t.ellipsis&&(z=T,W.push(oe(ht,{index:"sub-menu-more",class:d.e("hide-arrow"),popperOffset:t.popperOffset},{title:()=>oe(Xe,{class:d.e("icon-more")},{default:()=>oe(t.ellipsisIcon)}),default:()=>O})))}const E=Rt(t,0),te=t.closeOnClickOutside?[[vn,()=>{p.value.length&&(D.value||(p.value.forEach(le=>{return l("close",le,(T=le,L.value[T].indexPath));var T}),p.value=[]))}]]:[],ie=we(oe("ul",{key:String(t.collapse),role:"menubar",ref:r,style:E.value,class:{[c.b()]:!0,[c.m(t.mode)]:!0,[c.m("collapse")]:t.collapse}},[...z,...W]),te);return t.collapseTransition&&t.mode==="vertical"?oe(ra,()=>ie):ie}}});const pa=He({index:{type:tt([String,null]),default:null},route:{type:tt([String,Object])},disabled:Boolean}),gt="ElMenuItem";var Nt=Pe(H({name:gt,components:{ElTooltip:Ll},props:pa,emits:{click:t=>ze(t.index)&&Array.isArray(t.indexPath)},setup(t,{emit:l}){const a=lt(),n=qe("rootMenu"),s=pe("menu"),i=pe("menu-item");n||Ue(gt,"can not inject root menu");const{parentMenu:r,indexPath:c}=Et(a,fn(t,"index")),d=qe(`subMenu:${r.value.uid}`);d||Ue(gt,"can not inject sub menu");const m=h(()=>t.index===n.activeIndex),p=Be({index:t.index,indexPath:c,active:m});return ke(()=>{d.addSubMenu(p),n.addMenuItem(p)}),Pl(()=>{d.removeSubMenu(p),n.removeMenuItem(p)}),{parentMenu:r,rootMenu:n,active:m,nsMenu:s,nsMenuItem:i,handleClick:()=>{t.disabled||(n.handleMenuItemClick({index:t.index,indexPath:c.value,route:t.route}),l("click",p))}}}}),[["render",function(t,l,a,n,s,i){const r=rt("el-tooltip");return V(),G("li",{class:B([t.nsMenuItem.b(),t.nsMenuItem.is("active",t.active),t.nsMenuItem.is("disabled",t.disabled)]),role:"menuitem",tabindex:"-1",onClick:l[0]||(l[0]=(...c)=>t.handleClick&&t.handleClick(...c))},[t.parentMenu.type.name==="ElMenu"&&t.rootMenu.props.collapse&&t.$slots.title?(V(),X(r,{key:0,effect:t.rootMenu.props.popperEffect,placement:"right","fallback-placements":["left"],persistent:""},{content:I(()=>[ve(t.$slots,"title")]),default:I(()=>[v("div",{class:B(t.nsMenu.be("tooltip","trigger"))},[ve(t.$slots,"default")],2)]),_:3},8,["effect"])):(V(),G(ne,{key:1},[ve(t.$slots,"default"),ve(t.$slots,"title")],64))],2)}],["__file","menu-item.vue"]]),Ut=Pe(H({name:"ElMenuItemGroup",props:{title:String},setup:()=>({ns:pe("menu-item-group")})}),[["render",function(t,l,a,n,s,i){return V(),G("li",{class:B(t.ns.b())},[v("div",{class:B(t.ns.e("title"))},[t.$slots.title?ve(t.$slots,"title",{key:1}):(V(),G(ne,{key:0},[se(_(t.title),1)],64))],2),v("ul",null,[ve(t.$slots,"default")])],2)}],["__file","menu-item-group.vue"]]);const ma=Mt(da,{MenuItem:Nt,MenuItemGroup:Ut,SubMenu:ht}),va=at(Nt);at(Ut);const ha=at(ht),fa=H({name:"BackTop",__name:"Backtop",setup(t){const{getPrefixCls:l,variables:a}=ee(),n=l("backtop");return(s,i)=>(V(),X(e(Kl),{class:B(`${e(n)}-backtop`),target:`.${e(a).namespace}-layout-content-scrollbar .${e(a).elNamespace}-scrollbar__wrap`},null,8,["class","target"]))}}),ga=["onClick"],xa=H({name:"ColorRadioPicker",__name:"ColorRadioPicker",props:{schema:{type:Array,default:()=>[]},modelValue:it.string.def("")},emits:["update:modelValue","change"],setup(t,{emit:l}){const{getPrefixCls:a}=ee(),n=a("color-radio-picker"),s=t,i=l,r=y(s.modelValue);return ue(()=>s.modelValue,c=>{c!==e(r)&&(r.value=c)}),ue(()=>r.value,c=>{i("update:modelValue",c),i("change",c)}),(c,d)=>{const m=ce;return V(),G("div",{class:B([e(n),"flex flex-wrap space-x-14px"])},[(V(!0),G(ne,null,We(t.schema,(p,f)=>(V(),G("span",{key:`radio-${f}`,class:B([{"is-active":e(r)===p},"mb-5px h-20px w-20px cursor-pointer border-2px border-gray-300 rounded-2px border-solid text-center leading-20px"]),style:Tl({background:p}),onClick:g=>r.value=p},[e(r)===p?(V(),X(m,{key:0,size:16,color:"#fff",icon:"ep:check"})):xe("",!0)],14,ga))),128))],2)}}}),xt=de(xa,[["__scopeId","data-v-7f41aaf3"]]),ba={class:"flex items-center justify-between"},wa={class:"text-14px"},ya={class:"flex items-center justify-between"},_a={class:"text-14px"},Ca={class:"flex items-center justify-between"},ka={class:"text-14px"},Ma={class:"flex items-center justify-between"},Ia={class:"text-14px"},Va={class:"flex items-center justify-between"},Ta={class:"text-14px"},$a={class:"flex items-center justify-between"},Sa={class:"text-14px"},Pa={class:"flex items-center justify-between"},La={class:"text-14px"},Ba={class:"flex items-center justify-between"},za={class:"text-14px"},Oa={class:"flex items-center justify-between"},Aa={class:"text-14px"},ja={class:"flex items-center justify-between"},Ea={class:"text-14px"},Ra={class:"flex items-center justify-between"},Na={class:"text-14px"},Ua={class:"flex items-center justify-between"},Ha={class:"text-14px"},Fa={class:"flex items-center justify-between"},qa={class:"text-14px"},Da={class:"flex items-center justify-between"},Wa={class:"text-14px"},Ga={class:"flex items-center justify-between"},Ka={class:"text-14px"},Qa={class:"flex items-center justify-between"},Za={class:"text-14px"},Ya=H({name:"InterfaceDisplay",__name:"InterfaceDisplay",setup(t){const{t:l}=he(),{getPrefixCls:a}=ee(),{setWatermark:n}=Wn(),s=a("interface-display"),i=re(),r=y(),c=y(i.getBreadcrumb),d=$=>{i.setBreadcrumb($)},m=y(i.getBreadcrumbIcon),p=$=>{i.setBreadcrumbIcon($)},f=y(i.getHamburger),g=$=>{i.setHamburger($)},L=y(i.getScreenfull),P=$=>{i.setScreenfull($)},u=y(i.getSize),x=$=>{i.setSize($)},w=y(i.getLocale),M=$=>{i.setLocale($)},F=y(i.getMessage),q=$=>{i.setMessage($)},R=y(i.getTagsView),K=$=>{Ge("--tags-view-height",$?"35px":"0px"),i.setTagsView($)},N=y(i.getTagsViewIcon),D=$=>{i.setTagsViewIcon($)},b=y(i.getLogo),C=$=>{i.setLogo($)},z=y(i.getUniqueOpened),W=$=>{i.setUniqueOpened($)},E=y(i.getFixedHeader),te=$=>{i.setFixedHeader($)},ie=y(i.getFooter),le=$=>{i.setFooter($)},T=y(i.getGreyMode),O=$=>{i.setGreyMode($)},k=y(i.getFixedMenu),U=$=>{i.setFixedMenu($)},Q=h(()=>i.getLayout);return ue(()=>Q.value,$=>{$==="top"&&i.setCollapse(!1)}),($,S)=>{const J=gn,Se=Vt;return V(),G("div",{class:B(e(s))},[v("div",ba,[v("span",wa,_(e(l)("setting.breadcrumb")),1),o(J,{modelValue:e(c),"onUpdate:modelValue":S[0]||(S[0]=A=>Z(c)?c.value=A:null),onChange:d},null,8,["modelValue"])]),v("div",ya,[v("span",_a,_(e(l)("setting.breadcrumbIcon")),1),o(J,{modelValue:e(m),"onUpdate:modelValue":S[1]||(S[1]=A=>Z(m)?m.value=A:null),onChange:p},null,8,["modelValue"])]),v("div",Ca,[v("span",ka,_(e(l)("setting.hamburgerIcon")),1),o(J,{modelValue:e(f),"onUpdate:modelValue":S[2]||(S[2]=A=>Z(f)?f.value=A:null),onChange:g},null,8,["modelValue"])]),v("div",Ma,[v("span",Ia,_(e(l)("setting.screenfullIcon")),1),o(J,{modelValue:e(L),"onUpdate:modelValue":S[3]||(S[3]=A=>Z(L)?L.value=A:null),onChange:P},null,8,["modelValue"])]),v("div",Va,[v("span",Ta,_(e(l)("setting.sizeIcon")),1),o(J,{modelValue:e(u),"onUpdate:modelValue":S[4]||(S[4]=A=>Z(u)?u.value=A:null),onChange:x},null,8,["modelValue"])]),v("div",$a,[v("span",Sa,_(e(l)("setting.localeIcon")),1),o(J,{modelValue:e(w),"onUpdate:modelValue":S[5]||(S[5]=A=>Z(w)?w.value=A:null),onChange:M},null,8,["modelValue"])]),v("div",Pa,[v("span",La,_(e(l)("setting.messageIcon")),1),o(J,{modelValue:e(F),"onUpdate:modelValue":S[6]||(S[6]=A=>Z(F)?F.value=A:null),onChange:q},null,8,["modelValue"])]),v("div",Ba,[v("span",za,_(e(l)("setting.tagsView")),1),o(J,{modelValue:e(R),"onUpdate:modelValue":S[7]||(S[7]=A=>Z(R)?R.value=A:null),onChange:K},null,8,["modelValue"])]),v("div",Oa,[v("span",Aa,_(e(l)("setting.tagsViewIcon")),1),o(J,{modelValue:e(N),"onUpdate:modelValue":S[8]||(S[8]=A=>Z(N)?N.value=A:null),onChange:D},null,8,["modelValue"])]),v("div",ja,[v("span",Ea,_(e(l)("setting.logo")),1),o(J,{modelValue:e(b),"onUpdate:modelValue":S[9]||(S[9]=A=>Z(b)?b.value=A:null),onChange:C},null,8,["modelValue"])]),v("div",Ra,[v("span",Na,_(e(l)("setting.uniqueOpened")),1),o(J,{modelValue:e(z),"onUpdate:modelValue":S[10]||(S[10]=A=>Z(z)?z.value=A:null),onChange:W},null,8,["modelValue"])]),v("div",Ua,[v("span",Ha,_(e(l)("setting.fixedHeader")),1),o(J,{modelValue:e(E),"onUpdate:modelValue":S[11]||(S[11]=A=>Z(E)?E.value=A:null),onChange:te},null,8,["modelValue"])]),v("div",Fa,[v("span",qa,_(e(l)("setting.footer")),1),o(J,{modelValue:e(ie),"onUpdate:modelValue":S[12]||(S[12]=A=>Z(ie)?ie.value=A:null),onChange:le},null,8,["modelValue"])]),v("div",Da,[v("span",Wa,_(e(l)("setting.greyMode")),1),o(J,{modelValue:e(T),"onUpdate:modelValue":S[13]||(S[13]=A=>Z(T)?T.value=A:null),onChange:O},null,8,["modelValue"])]),v("div",Ga,[v("span",Ka,_(e(l)("setting.fixedMenu")),1),o(J,{modelValue:e(k),"onUpdate:modelValue":S[14]||(S[14]=A=>Z(k)?k.value=A:null),onChange:U},null,8,["modelValue"])]),v("div",Qa,[v("span",Za,_(e(l)("watermark.watermark")),1),o(Se,{modelValue:e(r),"onUpdate:modelValue":S[15]||(S[15]=A=>Z(r)?r.value=A:null),class:"right-1 w-20",onChange:S[16]||(S[16]=A=>{n(r.value)})},null,8,["modelValue"])])],2)}}}),Ja=[(t=>(Ol("data-v-10d54d1d"),t=t(),Al(),t))(()=>v("div",{class:"absolute left-[10%] top-0 h-full w-[33%] bg-gray-200"},null,-1))],Xa=H({name:"LayoutRadioPicker",__name:"LayoutRadioPicker",setup(t){const{getPrefixCls:l}=ee(),a=l("layout-radio-picker"),n=re(),s=h(()=>n.getLayout);return(i,r)=>(V(),G("div",{class:B([e(a),"flex flex-wrap space-x-14px"])},[v("div",{class:B([`${e(a)}__classic`,"relative w-56px h-48px cursor-pointer bg-gray-300",{"is-acitve":e(s)==="classic"}]),onClick:r[0]||(r[0]=c=>e(n).setLayout("classic"))},null,2),v("div",{class:B([`${e(a)}__top-left`,"relative w-56px h-48px cursor-pointer bg-gray-300",{"is-acitve":e(s)==="topLeft"}]),onClick:r[1]||(r[1]=c=>e(n).setLayout("topLeft"))},null,2),v("div",{class:B([`${e(a)}__top`,"relative w-56px h-48px cursor-pointer bg-gray-300",{"is-acitve":e(s)==="top"}]),onClick:r[2]||(r[2]=c=>e(n).setLayout("top"))},null,2),v("div",{class:B([`${e(a)}__cut-menu`,"relative w-56px h-48px cursor-pointer bg-gray-300",{"is-acitve":e(s)==="cutMenu"}]),onClick:r[3]||(r[3]=c=>e(n).setLayout("cutMenu"))},Ja,2)],2))}}),eo=de(Xa,[["__scopeId","data-v-10d54d1d"]]),to={class:"text-16px font-700"},lo={class:"text-center"},ao={class:"mt-5px"},oo=H({name:"Setting",__name:"Setting",setup(t){const{t:l}=he(),a=re(),{getPrefixCls:n}=ee(),s=n("setting"),i=h(()=>a.getLayout),r=y(!1),c=y(a.getTheme.elColorPrimary),d=u=>{Ge("--el-color-primary",u),a.setTheme({elColorPrimary:u});const x=jl("--left-menu-bg-color",document.documentElement);g(xn(e(x)))},m=y(a.getTheme.topHeaderBgColor||""),p=u=>{const x=Ul(u),w=x?"#fff":"inherit",M=x?Hl(u,6):"#f6f6f6",F=x?u:"#eee";Ge("--top-header-bg-color",u),Ge("--top-header-text-color",w),Ge("--top-header-hover-color",M),a.setTheme({topHeaderBgColor:u,topHeaderTextColor:w,topHeaderHoverColor:M,topToolBorderColor:F}),e(i)==="top"&&g(u)},f=y(a.getTheme.leftMenuBgColor||""),g=u=>{const x=jl("--el-color-primary",document.documentElement),w=Ul(u),M={leftMenuBorderColor:w?"inherit":"#eee",leftMenuBgColor:u,leftMenuBgLightColor:w?Hl(u,6):u,leftMenuBgActiveColor:w?"var(--el-color-primary)":Fl(e(x),.1),leftMenuCollapseBgActiveColor:w?"var(--el-color-primary)":Fl(e(x),.1),leftMenuTextColor:w?"#bfcbd9":"#333",leftMenuTextActiveColor:w?"#fff":"var(--el-color-primary)",logoTitleTextColor:w?"#fff":"inherit",logoBorderColor:w?u:"#eee"};a.setTheme(M),a.setCssVarTheme()};i.value!=="top"||a.getIsDark||(m.value="#fff",p("#fff")),ue(()=>i.value,u=>{u!=="top"||a.getIsDark?g(e(f)):(m.value="#fff",p("#fff"))});const L=async()=>{const{copy:u,copied:x,isSupported:w}=bn({source:`
      // \u9762\u5305\u5C51
      breadcrumb: ${a.getBreadcrumb},
      // \u9762\u5305\u5C51\u56FE\u6807
      breadcrumbIcon: ${a.getBreadcrumbIcon},
      // \u6298\u53E0\u56FE\u6807
      hamburger: ${a.getHamburger},
      // \u5168\u5C4F\u56FE\u6807
      screenfull: ${a.getScreenfull},
      // \u5C3A\u5BF8\u56FE\u6807
      size: ${a.getSize},
      // \u591A\u8BED\u8A00\u56FE\u6807
      locale: ${a.getLocale},
      // \u6D88\u606F\u56FE\u6807
      message: ${a.getMessage},
      // \u6807\u7B7E\u9875
      tagsView: ${a.getTagsView},
      // \u6807\u7B7E\u9875\u56FE\u6807
      getTagsViewIcon: ${a.getTagsViewIcon},
      // logo
      logo: ${a.getLogo},
      // \u83DC\u5355\u624B\u98CE\u7434
      uniqueOpened: ${a.getUniqueOpened},
      // \u56FA\u5B9Aheader
      fixedHeader: ${a.getFixedHeader},
      // \u9875\u811A
      footer: ${a.getFooter},
      // \u7070\u8272\u6A21\u5F0F
      greyMode: ${a.getGreyMode},
      // layout\u5E03\u5C40
      layout: '${a.getLayout}',
      // \u6697\u9ED1\u6A21\u5F0F
      isDark: ${a.getIsDark},
      // \u7EC4\u4EF6\u5C3A\u5BF8
      currentSize: '${a.getCurrentSize}',
      // \u4E3B\u9898\u76F8\u5173
      theme: {
        // \u4E3B\u9898\u8272
        elColorPrimary: '${a.getTheme.elColorPrimary}',
        // \u5DE6\u4FA7\u83DC\u5355\u8FB9\u6846\u989C\u8272
        leftMenuBorderColor: '${a.getTheme.leftMenuBorderColor}',
        // \u5DE6\u4FA7\u83DC\u5355\u80CC\u666F\u989C\u8272
        leftMenuBgColor: '${a.getTheme.leftMenuBgColor}',
        // \u5DE6\u4FA7\u83DC\u5355\u6D45\u8272\u80CC\u666F\u989C\u8272
        leftMenuBgLightColor: '${a.getTheme.leftMenuBgLightColor}',
        // \u5DE6\u4FA7\u83DC\u5355\u9009\u4E2D\u80CC\u666F\u989C\u8272
        leftMenuBgActiveColor: '${a.getTheme.leftMenuBgActiveColor}',
        // \u5DE6\u4FA7\u83DC\u5355\u6536\u8D77\u9009\u4E2D\u80CC\u666F\u989C\u8272
        leftMenuCollapseBgActiveColor: '${a.getTheme.leftMenuCollapseBgActiveColor}',
        // \u5DE6\u4FA7\u83DC\u5355\u5B57\u4F53\u989C\u8272
        leftMenuTextColor: '${a.getTheme.leftMenuTextColor}',
        // \u5DE6\u4FA7\u83DC\u5355\u9009\u4E2D\u5B57\u4F53\u989C\u8272
        leftMenuTextActiveColor: '${a.getTheme.leftMenuTextActiveColor}',
        // logo\u5B57\u4F53\u989C\u8272
        logoTitleTextColor: '${a.getTheme.logoTitleTextColor}',
        // logo\u8FB9\u6846\u989C\u8272
        logoBorderColor: '${a.getTheme.logoBorderColor}',
        // \u5934\u90E8\u80CC\u666F\u989C\u8272
        topHeaderBgColor: '${a.getTheme.topHeaderBgColor}',
        // \u5934\u90E8\u5B57\u4F53\u989C\u8272
        topHeaderTextColor: '${a.getTheme.topHeaderTextColor}',
        // \u5934\u90E8\u60AC\u505C\u989C\u8272
        topHeaderHoverColor: '${a.getTheme.topHeaderHoverColor}',
        // \u5934\u90E8\u8FB9\u6846\u989C\u8272
        topToolBorderColor: '${a.getTheme.topToolBorderColor}'
      }
    `});w?(await u(),e(x)&&El.success(l("setting.copySuccess"))):El.error(l("setting.copyFailed"))},P=()=>{const{wsCache:u}=wn();u.delete(Tt.LAYOUT),u.delete(Tt.THEME),u.delete(Tt.IS_DARK),window.location.reload()};return(u,x)=>{const w=ce,M=yn,F=$t,q=Un;return V(),G(ne,null,[v("div",{class:B([e(s),"fixed right-0 top-[45%] h-40px w-40px cursor-pointer bg-[var(--el-color-primary)] text-center leading-40px"]),onClick:x[0]||(x[0]=R=>r.value=!0)},[o(w,{color:"#fff",icon:"ep:setting"})],2),o(q,{modelValue:e(r),"onUpdate:modelValue":x[4]||(x[4]=R=>Z(r)?r.value=R:null),"z-index":4e3,direction:"rtl",size:"350px"},{header:I(()=>[v("span",to,_(e(l)("setting.projectSetting")),1)]),default:I(()=>[v("div",lo,[o(M,null,{default:I(()=>[se(_(e(l)("setting.theme")),1)]),_:1}),o(e(Fn)),o(M,null,{default:I(()=>[se(_(e(l)("setting.layout")),1)]),_:1}),o(eo),o(M,null,{default:I(()=>[se(_(e(l)("setting.systemTheme")),1)]),_:1}),o(xt,{modelValue:e(c),"onUpdate:modelValue":x[1]||(x[1]=R=>Z(c)?c.value=R:null),schema:["#409eff","#009688","#536dfe","#ff5c93","#ee4f12","#0096c7","#9c27b0","#ff9800"],onChange:d},null,8,["modelValue"]),o(M,null,{default:I(()=>[se(_(e(l)("setting.headerTheme")),1)]),_:1}),o(xt,{modelValue:e(m),"onUpdate:modelValue":x[2]||(x[2]=R=>Z(m)?m.value=R:null),schema:["#fff","#151515","#5172dc","#e74c3c","#24292e","#394664","#009688","#383f45"],onChange:p},null,8,["modelValue"]),e(i)!=="top"?(V(),G(ne,{key:0},[o(M,null,{default:I(()=>[se(_(e(l)("setting.menuTheme")),1)]),_:1}),o(xt,{modelValue:e(f),"onUpdate:modelValue":x[3]||(x[3]=R=>Z(f)?f.value=R:null),schema:["#fff","#001529","#212121","#273352","#191b24","#383f45","#001628","#344058"],onChange:g},null,8,["modelValue"])],64)):xe("",!0)]),o(M,null,{default:I(()=>[se(_(e(l)("setting.interfaceDisplay")),1)]),_:1}),o(Ya),o(M),v("div",null,[o(F,{class:"w-full",type:"primary",onClick:L},{default:I(()=>[se(_(e(l)("setting.copy")),1)]),_:1})]),v("div",ao,[o(F,{class:"w-full",type:"danger",onClick:P},{default:I(()=>[se(_(e(l)("setting.clearAndReset")),1)]),_:1})])]),_:1},8,["modelValue"])],64)}}}),no=de(oo,[["__scopeId","data-v-97c3b87d"]]),so=(t,l)=>(Gn(t,a=>a.path===l)||[]).map(a=>a.path),{renderMenuTitle:Ht}={renderMenuTitle:t=>{const{t:l}=he(),{title:a="Please set title",icon:n}=t;return n?o(ne,null,[o(ce,{icon:t.icon},null),o("span",{class:"v-menu__title overflow-hidden overflow-ellipsis whitespace-nowrap"},[l(a)])]):o("span",{class:"v-menu__title overflow-hidden overflow-ellipsis whitespace-nowrap"},[l(a)])}},ro=()=>{const t=(l,a="/")=>l.filter(n=>{var s;return!((s=n.meta)!=null&&s.hidden)}).map(n=>{const s=n.meta??{},{oneShowingChild:i,onlyOneChild:r}=((d=[],m)=>{const p=y(),f=d.filter(g=>!(g.meta??{}).hidden&&(p.value=g,!0));return f.length===1?{oneShowingChild:!0,onlyOneChild:e(p)}:f.length?{oneShowingChild:!1,onlyOneChild:e(p)}:(p.value={...m,path:"",noShowingChildren:!0},{oneShowingChild:!0,onlyOneChild:e(p)})})(n.children,n),c=ut(n.path)?n.path:Ie(a,n.path);return!i||r!=null&&r.children&&!(r!=null&&r.noShowingChildren)||s!=null&&s.alwaysShow?o(ha,{index:c},{title:()=>Ht(s),default:()=>t(n.children,c)}):o(va,{index:r?Ie(c,r.path):c},{default:()=>Ht(r?r==null?void 0:r.meta:s)})});return{renderMenuItem:t}},{getPrefixCls:io}=ee(),bt=io("menu"),uo=H({name:"Menu",props:{menuSelect:{type:Function,default:void 0}},setup(t){const l=re(),a=h(()=>l.getLayout),{push:n,currentRoute:s}=Te(),i=ct(),r=h(()=>["classic","topLeft","cutMenu"].includes(e(a))?"vertical":"horizontal"),c=h(()=>e(a)==="cutMenu"?i.getMenuTabRouters:i.getRouters),d=h(()=>l.getCollapse),m=h(()=>l.getUniqueOpened),p=h(()=>{const{meta:P,path:u}=e(s);return P.activeMenu?P.activeMenu:u}),f=P=>{t.menuSelect&&t.menuSelect(P),ut(P)?window.open(P):n(P)},g=()=>{if(e(a)==="top")return L();{let u;return o(Ve,null,typeof(P=u=L())=="function"||Object.prototype.toString.call(P)==="[object Object]"&&!Rl(P)?u:{default:()=>[u]})}var P},L=()=>o(ma,{defaultActive:e(p),mode:e(r),collapse:e(a)!=="top"&&e(a)!=="cutMenu"&&e(d),uniqueOpened:e(a)!=="top"&&e(m),backgroundColor:"var(--left-menu-bg-color)",textColor:"var(--left-menu-text-color)",activeTextColor:"var(--left-menu-text-active-color)",onSelect:f},{default:()=>{const{renderMenuItem:P}=ro(e(r));return P(e(c))}});return()=>o("div",{id:bt,class:[`${bt} ${bt}__${e(r)}`,"h-[100%] overflow-hidden flex-col bg-[var(--left-menu-bg-color)]",{"w-[var(--left-menu-min-width)]":e(d)&&e(a)!=="cutMenu","w-[var(--left-menu-max-width)]":!e(d)&&e(a)!=="cutMenu"}]},[g()])}}),Ke=de(uo,[["__scopeId","data-v-47888cfd"]]),Qe=Be({}),Ft=(t,l)=>{const a=[];for(const n of t){let s=null;const i=n.meta??{};if(!i.hidden||i.canTo){const r=so(l,n.path),c=ut(n.path)?n.path:r.join("/");s=St(n),s.path=c,n.children&&s&&(s.children=Ft(n.children,l)),s&&a.push(s),r.length&&Reflect.has(Qe,r[0])&&Qe[r[0]].push(c)}}return a},{getPrefixCls:co,variables:po}=ee(),wt=co("tab-menu"),mo=H({name:"TabMenu",setup(){const{push:t,currentRoute:l}=Te(),{t:a}=he(),n=re(),s=h(()=>n.getCollapse),i=h(()=>n.getFixedMenu),r=ct(),c=h(()=>r.getRouters),d=h(()=>e(c).filter(u=>{var x;return!((x=u==null?void 0:u.meta)!=null&&x.hidden)})),m=()=>{n.setCollapse(!e(s))};ke(()=>{var u;if(e(i)){const x=`/${e(l).path.split("/")[1]}`,w=(u=e(d).find(M=>{var F,q,R;return(((F=M.meta)==null?void 0:F.alwaysShow)||((q=M==null?void 0:M.children)==null?void 0:q.length)&&((R=M==null?void 0:M.children)==null?void 0:R.length)>1)&&M.path===x}))==null?void 0:u.children;g.value=x,w&&r.setMenuTabRouters(St(w).map(M=>(M.path=Ie(e(g),M.path),M)))}}),ue(()=>c.value,u=>{(x=>{for(const w of x){const M=w.meta??{};M!=null&&M.hidden||(Qe[w.path]=[])}})(u),Ft(u,u)},{immediate:!0,deep:!0});const p=y(!0);ue(()=>s.value,u=>{u?p.value=!u:setTimeout(()=>{p.value=!u},200)});const f=y(!!e(i)),g=y(""),L=u=>{const{path:x}=e(l);return!!Qe[u].includes(x)},P=()=>{e(f)&&!e(i)&&(f.value=!1)};return()=>o("div",{id:`${po.namespace}-menu`,class:[wt,"relative bg-[var(--left-menu-bg-color)] top-1px layout-border__right",{"w-[var(--tab-menu-max-width)]":!e(s),"w-[var(--tab-menu-min-width)]":e(s)}],onMouseleave:P},[o(Ve,{class:"!h-[calc(100%-var(--tab-menu-collapse-height)-1px)]"},{default:()=>[o("div",null,{default:()=>e(d).map(u=>{var w,M,F,q,R,K;const x=(w=u.meta)!=null&&w.alwaysShow||(M=u==null?void 0:u.children)!=null&&M.length&&((F=u==null?void 0:u.children)==null?void 0:F.length)>1?u:{...(u==null?void 0:u.children)&&(u==null?void 0:u.children[0]),path:Ie(u.path,(q=(u==null?void 0:u.children)&&(u==null?void 0:u.children[0]))==null?void 0:q.path)};return o("div",{class:[`${wt}__item`,"text-center text-12px relative py-12px cursor-pointer",{"is-active":L(u.path)}],onClick:()=>{(N=>{if(ut(N.path))return void window.open(N.path);const D=N.children?N.path:N.path.split("/")[0],b=e(g);g.value=N.children?N.path:N.path.split("/")[0],N.children?(D!==b&&e(f)||(f.value=!!e(i)||!e(f)),e(f)&&r.setMenuTabRouters(St(N.children).map(C=>(C.path=Ie(e(g),C.path),C)))):(t(N.path),r.setMenuTabRouters([]),f.value=!1)})(x)}},[o("div",null,[o(ce,{icon:(R=x==null?void 0:x.meta)==null?void 0:R.icon},null)]),e(p)?o("p",{class:"mt-5px break-words px-2px"},[a((K=x.meta)==null?void 0:K.title)]):void 0])})})]}),o("div",{class:[`${wt}--collapse`,"text-center h-[var(--tab-menu-collapse-height)] leading-[var(--tab-menu-collapse-height)] cursor-pointer"],onClick:m},[o(ce,{icon:e(s)?"ep:d-arrow-right":"ep:d-arrow-left"},null)]),o(Ke,{class:["!absolute top-0 z-11",{"!left-[var(--tab-menu-min-width)]":e(s),"!left-[var(--tab-menu-max-width)]":!e(s),"!w-[calc(var(--left-menu-max-width)+1px)]":e(f)||e(i),"!w-0":!e(f)&&!e(i)}],style:"transition: width var(--transition-time-02), left var(--transition-time-02);"},null)])}}),vo=de(mo,[["__scopeId","data-v-3f525f43"]]),qt=(t,l="")=>{let a=[];return t.forEach(n=>{const s=n.meta,i=Ie(l,n.path);if(s!=null&&s.affix&&a.push({...n,path:i,fullPath:i}),n.children){const r=qt(n.children,i);r.length>=1&&(a=[...a,...r])}}),a},Dt=H({name:"ContextMenu",__name:"ContextMenu",props:{schema:{type:Array,default:()=>[]},trigger:{type:String,default:"contextmenu"},tagItem:{type:Object,default:()=>({})}},emits:["visibleChange"],setup(t,{expose:l,emit:a}){const{getPrefixCls:n}=ee(),s=n("context-menu"),{t:i}=he(),r=a,c=t,d=f=>{f.command&&f.command(f)},m=f=>{r("visibleChange",f,c.tagItem)},p=y();return l({elDropdownMenuRef:p,tagItem:c.tagItem}),(f,g)=>{const L=ce,P=Lt,u=Bt,x=zt;return V(),X(x,{ref_key:"elDropdownMenuRef",ref:p,class:B(e(s)),trigger:t.trigger,placement:"bottom-start","popper-class":"v-context-menu-popper",onCommand:d,onVisibleChange:m},{dropdown:I(()=>[o(u,null,{default:I(()=>[(V(!0),G(ne,null,We(t.schema,(w,M)=>(V(),X(P,{key:`dropdown${M}`,command:w,disabled:w.disabled,divided:w.divided},{default:I(()=>[o(L,{icon:w.icon},null,8,["icon"]),se(" "+_(e(i)(w.label)),1)]),_:2},1032,["command","disabled","divided"]))),128))]),_:1})]),default:I(()=>[ve(f.$slots,"default")]),_:3},8,["class","trigger"])}}});function Oe({el:t,position:l="scrollLeft",to:a,duration:n=500,callback:s}){const i=y(!1),r=t[l],c=a-r,d=20;let m=0;function p(){if(!e(i))return;m+=d;const f=(g=m,L=r,P=c,(g/=n/2)<1?P/2*g*g+L:-P/2*(--g*(g-2)-1)+L);var g,L,P;((u,x,w)=>{u[x]=w})(t,l,f),m<n&&e(i)?requestAnimationFrame(p):s&&s()}return{start:function(){i.value=!0,p()},stop:function(){i.value=!1}}}const ho=["id"],fo={class:"flex-1 overflow-hidden"},go={class:"h-full flex"},xo=["onClick"],bo=H({__name:"TagsView",setup(t){const{getPrefixCls:l}=ee(),a=l("tags-view"),{t:n}=he(),{currentRoute:s,push:i,replace:r}=Te(),c=ct(),d=h(()=>c.getRouters),m=pt(),p=h(()=>m.getVisitedViews),f=y([]),g=re(),L=h(()=>g.getTagsViewIcon),P=h(()=>g.getIsDark),u=y(),x=()=>{const{name:T}=e(s);return T&&(u.value=e(s),m.addView(e(s))),!1},w=T=>{var O;(O=T==null?void 0:T.meta)!=null&&O.affix||(m.delView(T),C(T)&&N())},M=()=>{m.delAllViews(),N()},F=()=>{m.delOthersViews(e(u))},q=async T=>{if(!T)return;m.delCachedView();const{path:O,query:k}=T;await st(),r({path:"/redirect"+O,query:k})},R=()=>{m.delLeftViews(e(u))},K=()=>{m.delRightViews(e(u))},N=()=>{const T=m.getVisitedViews.slice(-1)[0];if(T)i(T);else{if(e(s).path===c.getAddRouters[0].path||e(s).path===c.getAddRouters[0].redirect)return void x();i("/")}},D=Nl(),b=T=>{var $;const O=($=e(E))==null?void 0:$.wrapRef;let k=null,U=null;const Q=e(D);if(Q.length>0&&(k=Q[0],U=Q[Q.length-1]),(k==null?void 0:k.to).fullPath===T.fullPath){const{start:S}=Oe({el:O,position:"scrollLeft",to:0,duration:500});S()}else if((U==null?void 0:U.to).fullPath===T.fullPath){const{start:S}=Oe({el:O,position:"scrollLeft",to:O.scrollWidth-O.offsetWidth,duration:500});S()}else{const S=Q.findIndex(ge=>(ge==null?void 0:ge.to).fullPath===T.fullPath),J=document.getElementsByClassName(`${a}__item`),Se=J[S-1],A=J[S+1],j=A.offsetLeft+A.offsetWidth+4,Ne=Se.offsetLeft-4;if(j>e(te)+O.offsetWidth){const{start:ge}=Oe({el:O,position:"scrollLeft",to:j-O.offsetWidth,duration:500});ge()}else if(Ne<e(te)){const{start:ge}=Oe({el:O,position:"scrollLeft",to:Ne,duration:500});ge()}}},C=T=>T.path===e(s).path,z=Nl(),W=(T,O)=>{if(T)for(const k of e(z)){const U=k.elDropdownMenuRef;O.fullPath!==k.tagItem.fullPath&&(U==null||U.handleClose())}},E=y(),te=y(0),ie=({scrollLeft:T})=>{te.value=T},le=T=>{var U;const O=(U=e(E))==null?void 0:U.wrapRef,{start:k}=Oe({el:O,position:"scrollLeft",to:e(te)+T,duration:500});k()};return ke(()=>{(()=>{f.value=qt(e(d));for(const T of e(f))T.name&&m.addVisitedView(T)})(),x()}),ue(()=>s.value,()=>{x(),(async()=>{await st();for(const T of e(p))if(T.fullPath===e(s).path){b(T),T.fullPath!==e(s).fullPath&&m.updateVisitedView(e(s));break}})()}),(T,O)=>{var Q,$,S,J,Se,A;const k=ce,U=rt("router-link");return V(),G("div",{id:e(a),class:B([e(a),"relative w-full flex bg-[#fff] dark:bg-[var(--el-bg-color)]"])},[v("span",{class:B([`${e(a)}__tool ${e(a)}__tool--first`,"h-[var(--tags-view-height)] w-[var(--tags-view-height)] flex cursor-pointer items-center justify-center"]),onClick:O[0]||(O[0]=j=>le(-200))},[o(k,{icon:"ep:d-arrow-left",color:"var(--el-text-color-placeholder)","hover-color":P.value?"#fff":"var(--el-color-black)"},null,8,["hover-color"])],2),v("div",fo,[o(e(Ve),{ref_key:"scrollbarRef",ref:E,class:"h-full",onScroll:ie},{default:I(()=>[v("div",go,[(V(!0),G(ne,null,We(p.value,j=>{var Ne,ge,hl,fl,gl,xl,bl,wl,yl;return V(),X(e(Dt),{ref_for:!0,ref:e(z).set,schema:[{icon:"ep:refresh",label:e(n)("common.reload"),disabled:((Ne=u.value)==null?void 0:Ne.fullPath)!==j.fullPath,command:()=>{q(j)}},{icon:"ep:close",label:e(n)("common.closeTab"),disabled:!!((ge=p.value)!=null&&ge.length)&&((hl=u.value)==null?void 0:hl.meta.affix),command:()=>{w(j)}},{divided:!0,icon:"ep:d-arrow-left",label:e(n)("common.closeTheLeftTab"),disabled:!!((fl=p.value)!=null&&fl.length)&&(j.fullPath===p.value[0].fullPath||((gl=u.value)==null?void 0:gl.fullPath)!==j.fullPath),command:()=>{R()}},{icon:"ep:d-arrow-right",label:e(n)("common.closeTheRightTab"),disabled:!!((xl=p.value)!=null&&xl.length)&&(j.fullPath===p.value[p.value.length-1].fullPath||((bl=u.value)==null?void 0:bl.fullPath)!==j.fullPath),command:()=>{K()}},{divided:!0,icon:"ep:discount",label:e(n)("common.closeOther"),disabled:((wl=u.value)==null?void 0:wl.fullPath)!==j.fullPath,command:()=>{F()}},{icon:"ep:minus",label:e(n)("common.closeAll"),command:()=>{M()}}],key:j.fullPath,"tag-item":j,class:B([`${e(a)}__item`,(yl=j==null?void 0:j.meta)!=null&&yl.affix?`${e(a)}__item--affix`:"",{"is-active":C(j)}]),onVisibleChange:W},{default:I(()=>[v("div",null,[o(U,{ref_for:!0,ref:e(D).set,to:{...j},custom:""},{default:I(({navigate:Yo})=>{var _l,Cl,kl,Ml,Il;return[v("div",{onClick:Yo,class:"h-full flex items-center justify-center whitespace-nowrap pl-15px"},[j!=null&&j.matched&&(j!=null&&j.matched[1])&&((Cl=(_l=j==null?void 0:j.matched[1])==null?void 0:_l.meta)!=null&&Cl.icon)&&L.value?(V(),X(k,{key:0,icon:(Ml=(kl=j==null?void 0:j.matched[1])==null?void 0:kl.meta)==null?void 0:Ml.icon,size:12,class:"mr-5px"},null,8,["icon"])):xe("",!0),se(" "+_(e(n)((Il=j==null?void 0:j.meta)==null?void 0:Il.title))+" ",1),o(k,{class:B(`${e(a)}__item--close`),color:"#333",icon:"ep:close",size:12,onClick:$l(fs=>w(j),["prevent","stop"])},null,8,["class","onClick"])],8,xo)]}),_:2},1032,["to"])])]),_:2},1032,["schema","tag-item","class"])}),128))])]),_:1},512)]),v("span",{class:B([`${e(a)}__tool`,"h-[var(--tags-view-height)] w-[var(--tags-view-height)] flex cursor-pointer items-center justify-center"]),onClick:O[1]||(O[1]=j=>le(200))},[o(k,{icon:"ep:d-arrow-right",color:"var(--el-text-color-placeholder)","hover-color":P.value?"#fff":"var(--el-color-black)"},null,8,["hover-color"])],2),v("span",{class:B([`${e(a)}__tool`,"h-[var(--tags-view-height)] w-[var(--tags-view-height)] flex cursor-pointer items-center justify-center"]),onClick:O[2]||(O[2]=j=>q(u.value))},[o(k,{icon:"ep:refresh-right",color:"var(--el-text-color-placeholder)","hover-color":P.value?"#fff":"var(--el-color-black)"},null,8,["hover-color"])],2),o(e(Dt),{trigger:"click",schema:[{icon:"ep:refresh",label:e(n)("common.reload"),command:()=>{q(u.value)}},{icon:"ep:close",label:e(n)("common.closeTab"),disabled:!!((Q=p.value)!=null&&Q.length)&&(($=u.value)==null?void 0:$.meta.affix),command:()=>{w(u.value)}},{divided:!0,icon:"ep:d-arrow-left",label:e(n)("common.closeTheLeftTab"),disabled:!!((S=p.value)!=null&&S.length)&&((J=u.value)==null?void 0:J.fullPath)===p.value[0].fullPath,command:()=>{R()}},{icon:"ep:d-arrow-right",label:e(n)("common.closeTheRightTab"),disabled:!!((Se=p.value)!=null&&Se.length)&&((A=u.value)==null?void 0:A.fullPath)===p.value[p.value.length-1].fullPath,command:()=>{K()}},{divided:!0,icon:"ep:discount",label:e(n)("common.closeOther"),command:()=>{F()}},{icon:"ep:minus",label:e(n)("common.closeAll"),command:()=>{M()}}]},{default:I(()=>[v("span",{class:B([`${e(a)}__tool`,"block h-[var(--tags-view-height)] w-[var(--tags-view-height)] flex cursor-pointer items-center justify-center"])},[o(k,{icon:"ep:menu",color:"var(--el-text-color-placeholder)","hover-color":P.value?"#fff":"var(--el-color-black)"},null,8,["hover-color"])],2)]),_:1},8,["schema"])],10,ho)}}}),Ze=de(bo,[["__scopeId","data-v-caf12b78"]]),wo=v("img",{class:"h-[calc(var(--logo-height)-10px)] w-[calc(var(--logo-height)-10px)]",src:Jn},null,-1),Ye=H({name:"Logo",__name:"Logo",setup(t){const{getPrefixCls:l}=ee(),a=l("logo"),n=re(),s=y(!0),i=h(()=>n.getTitle),r=h(()=>n.getLayout),c=h(()=>n.getCollapse);return ke(()=>{e(c)&&(s.value=!1)}),ue(()=>c.value,d=>{e(r)!=="topLeft"&&e(r)!=="cutMenu"?d?s.value=!d:setTimeout(()=>{s.value=!d},400):s.value=!0}),ue(()=>r.value,d=>{d==="top"||d==="cutMenu"?s.value=!0:e(c)?s.value=!1:s.value=!0}),(d,m)=>{const p=rt("router-link");return V(),G("div",null,[o(p,{class:B([e(a),r.value!=="classic"?`${e(a)}__Top`:"","flex !h-[var(--logo-height)] items-center cursor-pointer pl-8px relative decoration-none overflow-hidden"]),to:"/"},{default:I(()=>[wo,s.value?(V(),G("div",{key:0,class:B(["ml-10px text-16px font-700",{"text-[var(--logo-title-text-color)]":r.value==="classic","text-[var(--top-header-text-color)]":r.value==="topLeft"||r.value==="top"||r.value==="cutMenu"}])},_(i.value),3)):xe("",!0)]),_:1},8,["class"])])}}}),yo={class:"text-14px"},_o=H({name:"Footer",__name:"Footer",setup(t){const{getPrefixCls:l}=ee(),a=l("footer"),n=re(),s=h(()=>n.getTitle);return(i,r)=>(V(),G("div",{class:B([e(a),"h-[var(--app-footer-height)] bg-[var(--app-content-bg-color)] text-center leading-[var(--app-footer-height)] text-[var(--el-text-color-placeholder)] dark:bg-[var(--el-bg-color)]"])},[v("span",yo,"Copyright \xA92024-"+_(e(s)),1)],2))}}),Je=H({name:"AppView",__name:"AppView",setup(t){const l=re(),a=h(()=>l.getLayout),n=h(()=>l.getFixedHeader),s=h(()=>l.getFooter),i=pt(),r=h(()=>i.getCachedViews),c=h(()=>l.getTagsView),d=y(!0);return Fe("reload",()=>{d.value=!1,st(()=>d.value=!0)}),(m,p)=>{const f=rt("router-view");return V(),G(ne,null,[v("section",{class:B(["p-[var(--app-content-padding)] w-[calc(100%-var(--app-content-padding)-var(--app-content-padding))] bg-[var(--app-content-bg-color)] dark:bg-[var(--el-bg-color)]",{"!min-h-[calc(100%-var(--app-content-padding)-var(--app-content-padding)-var(--app-footer-height))]":e(n)&&(e(a)==="classic"||e(a)==="topLeft"||e(a)==="top")&&e(s)||!e(c)&&e(a)==="top"&&e(s),"!min-h-[calc(100%-var(--app-content-padding)-var(--app-content-padding)-var(--app-footer-height)-var(--tags-view-height))]":e(c)&&e(a)==="top"&&e(s),"!min-h-[calc(100%-var(--tags-view-height)-var(--app-content-padding)-var(--app-content-padding)-var(--top-tool-height)-var(--app-footer-height))]":!e(n)&&e(a)==="classic"&&e(s),"!min-h-[calc(100%-var(--tags-view-height)-var(--app-content-padding)-var(--app-content-padding)-var(--app-footer-height))]":!e(n)&&e(a)==="topLeft"&&e(s),"!min-h-[calc(100%-var(--top-tool-height)-var(--app-content-padding)-var(--app-content-padding))]":e(n)&&e(a)==="cutMenu"&&e(s),"!min-h-[calc(100%-var(--top-tool-height)-var(--app-content-padding)-var(--app-content-padding)-var(--tags-view-height))]":!e(n)&&e(a)==="cutMenu"&&e(s)}])},[e(d)?(V(),X(f,{key:0},{default:I(({Component:g,route:L})=>[(V(),X(_n,{include:e(r)},[(V(),X(Sl(g),{key:L.fullPath}))],1032,["include"]))]),_:1})):xe("",!0)],2),e(s)?(V(),X(e(_o),{key:0})):xe("",!0)],64)}}}),Co={class:"message"},ko=(t=>(Ol("data-v-505654a6"),t=t(),Al(),t))(()=>v("img",{alt:"",class:"message-icon",src:mt},null,-1)),Mo={class:"message-content"},Io={class:"message-title"},Vo={class:"message-date"},To={style:{"margin-top":"10px","text-align":"right"}},$o=de(H({name:"Message",__name:"Message",setup(t){const{push:l}=Te(),a=y("notice"),n=y(0),s=y([]),i=async()=>{s.value=await as(),n.value=0},r=async()=>{os().then(d=>{n.value=d})},c=()=>{l({name:"MyNotifyMessage"})};return ke(()=>{r(),setInterval(()=>{r()},12e4)}),(d,m)=>{const p=ce,f=Cn,g=Ve,L=kn,P=Mn,u=Xn,x=In;return V(),G("div",Co,[o(x,{width:400,placement:"bottom",trigger:"click"},{reference:I(()=>[o(f,{"is-dot":e(n)>0,class:"item"},{default:I(()=>[o(p,{size:18,class:"cursor-pointer",icon:"ep:bell",onClick:i})]),_:1},8,["is-dot"])]),default:I(()=>[o(P,{modelValue:e(a),"onUpdate:modelValue":m[0]||(m[0]=w=>Z(a)?a.value=w:null)},{default:I(()=>[o(L,{label:"\u6211\u7684\u7AD9\u5185\u4FE1",name:"notice"},{default:I(()=>[o(g,{class:"message-list"},{default:I(()=>[(V(!0),G(ne,null,We(e(s),w=>(V(),G("div",{key:w.id,class:"message-item"},[ko,v("div",Mo,[v("span",Io,_(w.templateNickname)+"\uFF1A"+_(w.templateContent),1),v("span",Vo,_(e(ts)(w.createTime)),1)])]))),128))]),_:1})]),_:1})]),_:1},8,["modelValue"]),v("div",To,[o(u,{preIcon:"ep:view",title:"\u67E5\u770B\u5168\u90E8",type:"primary",onClick:c})])]),_:1})])}}}),[["__scopeId","data-v-505654a6"]]),So=H({name:"Collapse",__name:"Collapse",props:{color:it.string.def("")},setup(t){const{getPrefixCls:l}=ee(),a=l("collapse"),n=re(),s=h(()=>n.getCollapse),i=()=>{const r=e(s);n.setCollapse(!r)};return(r,c)=>{const d=ce;return V(),G("div",{class:B(e(a)),onClick:i},[o(d,{color:t.color,icon:e(s)?"ep:expand":"ep:fold",size:18,class:"cursor-pointer"},null,8,["color","icon"])],2)}}}),yt=Vn("lock",{state:()=>({lockInfo:{}}),getters:{getLockInfo(){return this.lockInfo}},actions:{setLockInfo(t){this.lockInfo=t},resetLockInfo(){this.lockInfo={}},unLock(t){var l;return((l=this.lockInfo)==null?void 0:l.password)===t&&(this.resetLockInfo(),!0)}},persist:!0}),Po={class:"flex flex-col items-center"},Lo=["src"],Bo={class:"text-14px my-10px text-[var(--top-header-text-color)]"},zo=H({__name:"LockDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue"],setup(t,{emit:l}){const{getPrefixCls:a}=ee(),n=a("lock-dialog"),{required:s}=cs(),{t:i}=he(),r=yt(),c=t,d=Pt(),m=h(()=>d.user.avatar??mt),p=h(()=>d.user.nickname??"Admin"),f=l,g=h({get:()=>c.modelValue,set:M=>{console.log("set: ",M),f("update:modelValue",M)}}),L=y(i("lock.lockScreen")),P=y({password:void 0}),u=Be({password:[s()]}),x=y(),w=async()=>{x&&await x.value.validate()&&(g.value=!1,r.setLockInfo({...P.value,isLock:!0}))};return(M,F)=>{const q=Vt,R=Tn,K=$n,N=$t,D=is;return V(),X(D,{modelValue:e(g),"onUpdate:modelValue":F[1]||(F[1]=b=>Z(g)?g.value=b:null),width:"500px","max-height":"170px",class:B(e(n)),title:e(L)},{footer:I(()=>[o(N,{type:"primary",onClick:w},{default:I(()=>[se(_(e(i)("lock.lock")),1)]),_:1})]),default:I(()=>[v("div",Po,[v("img",{src:e(m),alt:"",class:"w-70px h-70px rounded-[50%]"},null,8,Lo),v("span",Bo,_(e(p)),1)]),o(K,{ref_key:"formRef",ref:x,model:e(P),rules:e(u),"label-width":"80px"},{default:I(()=>[o(R,{label:e(i)("lock.lockPassword"),prop:"password"},{default:I(()=>[o(q,{type:"password",modelValue:e(P).password,"onUpdate:modelValue":F[0]||(F[0]=b=>e(P).password=b),placeholder:"\u8BF7\u8F93\u5165"+e(i)("lock.lockPassword"),clearable:"","show-password":""},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),_:1},8,["modelValue","class","title"])}}}),Oo=de(zo,[["__scopeId","data-v-07f2cba7"]]),Ao={class:"flex w-screen h-screen justify-center items-center"},jo={class:"flex flex-col items-center"},Eo=["src"],Ro={class:"text-14px my-10px text-[var(--logo-title-text-color)]"},No={class:"absolute bottom-5 w-full text-gray-300 xl:text-xl 2xl:text-3xl text-center enter-y"},Uo={class:"text-5xl mb-4 enter-x"},Ho={class:"text-3xl"},Fo={class:"text-2xl"},qo=H({__name:"LockPage",setup(t){const l=pt(),{replace:a}=Te(),n=Pt(),s=y(""),i=y(!1),r=y(!1),c=y(!0),{getPrefixCls:d}=ee(),m=d("lock-page"),p=h(()=>n.user.avatar??mt),f=h(()=>n.user.nickname??"Admin"),g=yt(),{hour:L,month:P,minute:u,meridiem:x,year:w,day:M,week:F}=((N=!0)=>{let D;const b=Be({year:0,month:0,week:"",day:0,hour:"",minute:"",second:0,meridiem:""}),C=()=>{const E=ps(),te=E.format("HH"),ie=E.format("mm"),le=E.get("s");b.year=E.get("y"),b.month=E.get("M")+1,b.week="\u661F\u671F"+["\u65E5","\u4E00","\u4E8C","\u4E09","\u56DB","\u4E94","\u516D"][E.day()],b.day=E.get("date"),b.hour=te,b.minute=ie,b.second=le,b.meridiem=E.format("A")};function z(){C(),clearInterval(D),D=setInterval(()=>C(),1e3)}function W(){clearInterval(D)}return Sn(()=>{N&&z()}),Pn(()=>{W()}),{...Ln(b),start:z,stop:W}})(!0),{t:q}=he();async function R(){await n.loginOut().catch(()=>{}),Bn(),l.delAllViews(),zn(),g.resetLockInfo(),a("/login")}function K(N=!1){c.value=N}return(N,D)=>{const b=ce,C=Vt,z=$t;return V(),G("div",{class:B([e(m),"fixed inset-0 flex h-screen w-screen bg-black items-center justify-center"])},[we(v("div",{class:B([`${e(m)}__unlock`,"absolute top-0 left-1/2 flex pt-5 h-16 items-center justify-center sm:text-md xl:text-xl text-white flex-col cursor-pointer transform translate-x-1/2"]),onClick:D[0]||(D[0]=W=>K(!1))},[o(b,{icon:"ep:lock"}),v("span",null,_(e(q)("lock.unlock")),1)],2),[[De,e(c)]]),v("div",Ao,[v("div",{class:B([`${e(m)}__hour`,"relative mr-5 md:mr-20 w-2/5 h-2/5 md:h-4/5"])},[v("span",null,_(e(L)),1),we(v("span",{class:"meridiem absolute left-5 top-5 text-md xl:text-xl"},_(e(x)),513),[[De,e(c)]])],2),v("div",{class:B(`${e(m)}__minute w-2/5 h-2/5 md:h-4/5 `)},[v("span",null,_(e(u)),1)],2)]),o(et,{name:"fade-slide"},{default:I(()=>[we(v("div",{class:B(`${e(m)}-entry`)},[v("div",{class:B(`${e(m)}-entry-content`)},[v("div",jo,[v("img",{src:e(p),alt:"",class:"w-70px h-70px rounded-[50%]"},null,8,Eo),v("span",Ro,_(e(f)),1)]),o(C,{type:"password",placeholder:e(q)("lock.placeholder"),class:"enter-x",modelValue:e(s),"onUpdate:modelValue":D[1]||(D[1]=W=>Z(s)?s.value=W:null)},null,8,["placeholder","modelValue"]),e(r)?(V(),G("span",{key:0,class:B(`text-14px ${e(m)}-entry__err-msg enter-x`)},_(e(q)("lock.message")),3)):xe("",!0),v("div",{class:B(`${e(m)}-entry__footer enter-x`)},[o(z,{type:"primary",size:"small",class:"mt-2 mr-2 enter-x",link:"",disabled:e(i),onClick:D[2]||(D[2]=W=>K(!0))},{default:I(()=>[se(_(e(q)("common.back")),1)]),_:1},8,["disabled"]),o(z,{type:"primary",size:"small",class:"mt-2 mr-2 enter-x",link:"",disabled:e(i),onClick:R},{default:I(()=>[se(_(e(q)("lock.backToLogin")),1)]),_:1},8,["disabled"]),o(z,{type:"primary",class:"mt-2",size:"small",link:"",onClick:D[3]||(D[3]=W=>async function(){if(!s.value)return;let E=s.value;try{i.value=!0;const te=await g.unLock(E);r.value=!te}finally{i.value=!1}}()),disabled:e(i)},{default:I(()=>[se(_(e(q)("lock.entrySystem")),1)]),_:1},8,["disabled"])],2)],2)],2),[[De,!e(c)]])]),_:1}),v("div",No,[we(v("div",Uo,[se(_(e(L))+":"+_(e(u))+" ",1),v("span",Ho,_(e(x)),1)],512),[[De,!e(c)]]),v("div",Fo,_(e(w))+"/"+_(e(P))+"/"+_(e(M))+" "+_(e(F)),1)])],2)}}}),Do=de(qo,[["__scopeId","data-v-32f0095c"]]),Wo={class:"flex items-center"},Go={class:"pl-[5px] text-14px text-[var(--top-header-text-color)] <lg:hidden"},Ko=H({name:"UserInfo",__name:"UserInfo",setup(t){const{t:l}=he(),{push:a,replace:n}=Te(),s=Pt(),i=pt(),{getPrefixCls:r}=ee(),c=r("user-info"),d=h(()=>s.user.avatar??mt),m=h(()=>s.user.nickname??"Admin"),p=yt(),f=h(()=>{var w;return((w=p.getLockInfo)==null?void 0:w.isLock)??!1}),g=y(!1),L=()=>{g.value=!0},P=async()=>{try{await An.confirm(l("common.loginOutMessage"),l("common.reminder"),{confirmButtonText:l("common.ok"),cancelButtonText:l("common.cancel"),type:"warning"}),await s.loginOut(),i.delAllViews(),n("/login?redirect=/index")}catch{}},u=async()=>{a("/user/profile")},x=()=>{window.open("https://doc.iocoder.cn/")};return(w,M)=>{const F=ss,q=ce,R=Lt,K=Bt,N=zt;return V(),G(ne,null,[o(N,{class:B(["custom-hover",e(c)]),trigger:"click"},{dropdown:I(()=>[o(K,null,{default:I(()=>[o(R,null,{default:I(()=>[o(q,{icon:"ep:tools"}),v("div",{onClick:u},_(e(l)("common.profile")),1)]),_:1}),o(R,null,{default:I(()=>[o(q,{icon:"ep:menu"}),v("div",{onClick:x},_(e(l)("common.document")),1)]),_:1}),o(R,{divided:""},{default:I(()=>[o(q,{icon:"ep:lock"}),v("div",{onClick:L},_(e(l)("lock.lockScreen")),1)]),_:1}),o(R,{divided:"",onClick:P},{default:I(()=>[o(q,{icon:"ep:switch-button"}),v("div",null,_(e(l)("common.loginOut")),1)]),_:1})]),_:1})]),default:I(()=>[v("div",Wo,[o(F,{src:e(d),alt:"",class:"w-[calc(var(--logo-height)-25px)] rounded-[50%]"},null,8,["src"]),v("span",Go,_(e(m)),1)])]),_:1},8,["class"]),e(g)?(V(),X(Oo,{key:0,modelValue:e(g),"onUpdate:modelValue":M[0]||(M[0]=D=>Z(g)?g.value=D:null)},null,8,["modelValue"])):xe("",!0),(V(),X(On,{to:"body"},[o(et,{name:"fade-bottom",mode:"out-in"},{default:I(()=>[e(f)?(V(),X(Do,{key:0})):xe("",!0)]),_:1})]))],64)}}}),Qo=de(Ko,[["__scopeId","data-v-a9bcc718"]]),Zo=H({name:"ScreenFull",__name:"Screenfull",props:{color:it.string.def("")},setup(t){const{getPrefixCls:l}=ee(),a=l("screenfull"),{toggle:n,isFullscreen:s}=jn(),i=()=>{n()};return(r,c)=>(V(),G("div",{class:B(e(a)),onClick:i},[o(e(ce),{color:t.color,icon:e(s)?"zmdi:fullscreen-exit":"zmdi:fullscreen",size:18},null,8,["color","icon"])],2))}}),Wt=(t,l="")=>{var n;const a=[];for(const s of t){const i=s==null?void 0:s.meta;if(i.hidden&&!i.canTo)continue;const r=i.alwaysShow||((n=s.children)==null?void 0:n.length)!==1?{...s}:{...s.children[0],path:Ie(s.path,s.children[0].path)};r.path=Ie(l,r.path),r.children&&(r.children=Wt(r.children,r.path)),r&&a.push(r)}return a};let Gt,Kt,Qt,Zt,Yt,Jt,Xt,el,tl,ye,ll,al,ol,nl,sl,_t,rl,il,Ae,ul,_e,fe,je,Ce,ae,$e,Y,me,Me,Ee,cl,Ct,Re,dl,pl,kt,ml,vl;({getPrefixCls:Gt}=ee()),Kt=Gt("breadcrumb"),Qt=re(),Zt=h(()=>Qt.getBreadcrumbIcon),Yt=de(H({name:"Breadcrumb",setup(){const{currentRoute:t}=Te(),{t:l}=he(),a=y([]),n=ct(),s=h(()=>{const i=n.getRouters;return Wt(i)});return ue(()=>t.value,i=>{i.path.startsWith("/redirect/")||(()=>{const r=t.value.matched.slice(-1)[0].path;a.value=Kn(e(s),c=>c.path===r)})()},{immediate:!0}),()=>{let i;return o(la,{separator:"/",class:`${Kt} flex items-center h-full ml-[10px]`},{default:()=>{return[o(En,{appear:!0,"enter-active-class":"animate__animated animate__fadeInRight"},(r=i=Qn(e(a)).map(c=>{const d=!c.redirect||c.redirect==="noredirect",m=c.meta;return o(aa,{to:{path:d?"":c.path},key:c.name},{default:()=>{var p,f;return[m!=null&&m.icon&&Zt.value?o("div",{class:"flex items-center"},[o(ce,{icon:m.icon,class:"mr-[2px]",svgClass:"inline-block"},null),l((p=c==null?void 0:c.meta)==null?void 0:p.title)]):l((f=c==null?void 0:c.meta)==null?void 0:f.title)]}})}),typeof r=="function"||Object.prototype.toString.call(r)==="[object Object]"&&!Rl(r)?i:{default:()=>[i]}))];var r}})}}}),[["__scopeId","data-v-cd6e165a"]]),Jt=H({name:"SizeDropdown",__name:"SizeDropdown",props:{color:it.string.def("")},setup(t){const{getPrefixCls:l}=ee(),a=l("size-dropdown"),{t:n}=he(),s=re(),i=h(()=>s.sizeMap),r=c=>{s.setCurrentSize(c)};return(c,d)=>{const m=ce,p=Lt,f=Bt,g=zt;return V(),X(g,{class:B(e(a)),trigger:"click",onCommand:r},{dropdown:I(()=>[o(f,null,{default:I(()=>[(V(!0),G(ne,null,We(e(i),L=>(V(),X(p,{key:L,command:L},{default:I(()=>[se(_(e(n)(`size.${L}`)),1)]),_:2},1032,["command"]))),128))]),_:1})]),default:I(()=>[o(m,{color:t.color,size:18,class:"cursor-pointer",icon:"mdi:format-size"},null,8,["color"])]),_:1},8,["class"])}}}),{getPrefixCls:Xt,variables:el}=ee(),tl=Xt("tool-header"),ye=re(),ll=h(()=>ye.getBreadcrumb),al=h(()=>ye.getHamburger),ol=h(()=>ye.getScreenfull),nl=h(()=>ye.search),sl=h(()=>ye.getSize),_t=h(()=>ye.getLayout),rl=h(()=>ye.getLocale),il=h(()=>ye.getMessage),Ae=de(H({name:"ToolHeader",setup:()=>()=>o("div",{id:`${el.namespace}-tool-header`,class:[tl,"h-[var(--top-tool-height)] relative px-[var(--top-tool-p-x)] flex items-center justify-between","dark:bg-[var(--el-bg-color)]"]},[_t.value!=="top"?o("div",{class:"h-full flex items-center"},[al.value&&_t.value!=="cutMenu"?o(So,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,ll.value?o(Yt,{class:"lt-md:hidden"},null):void 0]):void 0,o("div",{class:"h-full flex items-center"},[ol.value?o(Zo,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,nl.value?o(Rn,{isModal:!1},null):void 0,sl.value?o(Jt,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,rl.value?o(qn,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,il.value?o($o,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,o(Qo,null,null)])])}),[["__scopeId","data-v-a4c24f40"]]),{getPrefixCls:ul}=ee(),_e=ul("layout"),fe=re(),je=h(()=>fe.getPageLoading),Ce=h(()=>fe.getTagsView),ae=h(()=>fe.getCollapse),$e=h(()=>fe.logo),Y=h(()=>fe.getFixedHeader),me=h(()=>fe.getMobile),Me=h(()=>fe.getFixedMenu),Ee=()=>({renderClassic:()=>o(ne,null,[o("div",{class:["absolute top-0 left-0 h-full layout-border__right",{"!fixed z-3000":me.value}]},[$e.value?o(Ye,{class:["bg-[var(--left-menu-bg-color)] relative",{"!pl-0":me.value&&ae.value,"w-[var(--left-menu-min-width)]":fe.getCollapse,"w-[var(--left-menu-max-width)]":!fe.getCollapse}],style:"transition: all var(--transition-time-02);"},null):void 0,o(Ke,{class:[{"!h-[calc(100%-var(--logo-height))]":$e.value}]},null)]),o("div",{class:[`${_e}-content`,"absolute top-0 h-[100%]",{"w-[calc(100%-var(--left-menu-min-width))] left-[var(--left-menu-min-width)]":ae.value&&!me.value&&!me.value,"w-[calc(100%-var(--left-menu-max-width))] left-[var(--left-menu-max-width)]":!ae.value&&!me.value&&!me.value,"fixed !w-full !left-0":me.value}],style:"transition: all var(--transition-time-02);"},[we(o(Ve,{class:[`${_e}-content-scrollbar`,{"!h-[calc(100%-var(--top-tool-height)-var(--tags-view-height))] mt-[calc(var(--top-tool-height)+var(--tags-view-height))]":Y.value}]},{default:()=>[o("div",{class:[{"fixed top-0 left-0 z-10":Y.value,"w-[calc(100%-var(--left-menu-min-width))] !left-[var(--left-menu-min-width)]":ae.value&&Y.value&&!me.value,"w-[calc(100%-var(--left-menu-max-width))] !left-[var(--left-menu-max-width)]":!ae.value&&Y.value&&!me.value,"!w-full !left-0":me.value}],style:"transition: all var(--transition-time-02);"},[o(Ae,{class:["bg-[var(--top-header-bg-color)]",{"layout-border__bottom":!Ce.value}]},null),Ce.value?o(Ze,{class:"layout-border__top layout-border__bottom"},null):void 0]),o(Je,null,null)]}),[[dt("loading"),je.value]])])]),renderTopLeft:()=>o(ne,null,[o("div",{class:"relative flex items-center bg-[var(--top-header-bg-color)] layout-border__bottom dark:bg-[var(--el-bg-color)]"},[$e.value?o(Ye,{class:"custom-hover"},null):void 0,o(Ae,{class:"flex-1"},null)]),o("div",{class:"absolute left-0 top-[var(--logo-height)+1px] h-[calc(100%-1px-var(--logo-height))] w-full flex"},[o(Ke,{class:"relative layout-border__right !h-full"},null),o("div",{class:[`${_e}-content`,"h-[100%]",{"w-[calc(100%-var(--left-menu-min-width))] left-[var(--left-menu-min-width)]":ae.value,"w-[calc(100%-var(--left-menu-max-width))] left-[var(--left-menu-max-width)]":!ae.value}],style:"transition: all var(--transition-time-02);"},[we(o(Ve,{class:[`${_e}-content-scrollbar`,{"!h-[calc(100%-var(--tags-view-height))] mt-[calc(var(--tags-view-height))]":Y.value&&Ce.value}]},{default:()=>[Ce.value?o(Ze,{class:["layout-border__bottom absolute",{"!fixed top-0 left-0 z-10":Y.value,"w-[calc(100%-var(--left-menu-min-width))] !left-[var(--left-menu-min-width)] mt-[calc(var(--logo-height)+1px)]":ae.value&&Y.value,"w-[calc(100%-var(--left-menu-max-width))] !left-[var(--left-menu-max-width)] mt-[calc(var(--logo-height)+1px)]":!ae.value&&Y.value}],style:"transition: width var(--transition-time-02), left var(--transition-time-02);"},null):void 0,o(Je,null,null)]}),[[dt("loading"),je.value]])])])]),renderTop:()=>o(ne,null,[o("div",{class:["flex items-center justify-between bg-[var(--top-header-bg-color)] relative",{"layout-border__bottom":!Ce.value}]},[$e.value?o(Ye,{class:"custom-hover"},null):void 0,o(Ke,{class:"h-[var(--top-tool-height)] flex-1 px-10px"},null),o(Ae,null,null)]),o("div",{class:[`${_e}-content`,"w-full",{"h-[calc(100%-var(--app-footer-height))]":!Y.value,"h-[calc(100%-var(--tags-view-height)-var(--app-footer-height))]":Y.value}]},[we(o(Ve,{class:[`${_e}-content-scrollbar`,{"mt-[var(--tags-view-height)] !pb-[calc(var(--tags-view-height)+var(--app-footer-height))]":Y.value,"pb-[var(--app-footer-height)]":!Y.value}]},{default:()=>[Ce.value?o(Ze,{class:["layout-border__bottom layout-border__top relative",{"!fixed w-full top-[calc(var(--top-tool-height)+1px)] left-0":Y.value}],style:"transition: width var(--transition-time-02), left var(--transition-time-02);"},null):void 0,o(Je,null,null)]}),[[dt("loading"),je.value]])])]),renderCutMenu:()=>o(ne,null,[o("div",{class:"relative flex items-center bg-[var(--top-header-bg-color)] layout-border__bottom"},[$e.value?o(Ye,{class:"custom-hover !pr-15px"},null):void 0,o(Ae,{class:"flex-1"},null)]),o("div",{class:"absolute left-0 top-[var(--logo-height)] h-[calc(100%-var(--logo-height))] w-[calc(100%-2px)] flex"},[o(vo,null,null),o("div",{class:[`${_e}-content`,"h-[100%]",{"w-[calc(100%-var(--tab-menu-min-width))] left-[var(--tab-menu-min-width)]":ae.value&&!Me.value,"w-[calc(100%-var(--tab-menu-max-width))] left-[var(--tab-menu-max-width)]":!ae.value&&!Me.value,"w-[calc(100%-var(--tab-menu-min-width)-var(--left-menu-max-width))] ml-[var(--left-menu-max-width)]":ae.value&&Me.value,"w-[calc(100%-var(--tab-menu-max-width)-var(--left-menu-max-width))] ml-[var(--left-menu-max-width)]":!ae.value&&Me.value}],style:"transition: all var(--transition-time-02);"},[we(o(Ve,{class:[`${_e}-content-scrollbar`,{"!h-[calc(100%-var(--tags-view-height))] mt-[calc(var(--tags-view-height))]":Y.value&&Ce.value}]},{default:()=>[Ce.value?o(Ze,{class:["relative layout-border__bottom layout-border__top",{"!fixed top-0 left-0 z-10":Y.value,"w-[calc(100%-var(--tab-menu-min-width))] !left-[var(--tab-menu-min-width)] mt-[var(--logo-height)]":ae.value&&Y.value,"w-[calc(100%-var(--tab-menu-max-width))] !left-[var(--tab-menu-max-width)] mt-[var(--logo-height)]":!ae.value&&Y.value,"!fixed top-0 !left-[var(--tab-menu-min-width)+var(--left-menu-max-width)] z-10":Y.value&&Me.value,"w-[calc(100%-var(--tab-menu-min-width)-var(--left-menu-max-width))] !left-[var(--tab-menu-min-width)+var(--left-menu-max-width)] mt-[var(--logo-height)]":ae.value&&Y.value&&Me.value,"w-[calc(100%-var(--tab-menu-max-width)-var(--left-menu-max-width))] !left-[var(--tab-menu-max-width)+var(--left-menu-max-width)] mt-[var(--logo-height)]":!ae.value&&Y.value&&Me.value}],style:"transition: width var(--transition-time-02), left var(--transition-time-02);"},null):void 0,o(Je,null,null)]}),[[dt("loading"),je.value]])])])])}),{getPrefixCls:cl}=ee(),Ct=cl("layout"),Re=re(),dl=h(()=>Re.getMobile),pl=h(()=>Re.getCollapse),kt=h(()=>Re.getLayout),ml=()=>{Re.setCollapse(!0)},vl=()=>{switch(e(kt)){case"classic":const{renderClassic:t}=Ee();return t();case"topLeft":const{renderTopLeft:l}=Ee();return l();case"top":const{renderTop:a}=Ee();return a();case"cutMenu":const{renderCutMenu:n}=Ee();return n()}},ql=de(H({name:"Layout",setup:()=>()=>o("section",{class:[Ct,`${Ct}__${kt.value}`,"w-[100%] h-[100%] relative"]},[dl.value&&!pl.value?o("div",{class:"absolute left-0 top-0 z-99 h-full w-full bg-[var(--el-color-black)] opacity-30",onClick:ml},null):void 0,vl(),o(fa,null,null),o(no,null,null)])}),[["__scopeId","data-v-de864217"]])});export{hs as __tla,ql as default};
