import{d as b,r as y,f as u,o as V,l as h,w,i as l,a as e,n as v,I as R,L as q,O as k,__tla as x}from"./index-BUSn51wb.js";import{_ as M,__tla as U}from"./XButton-BjahQbul.js";import{I as i,__tla as C}from"./InputPassword-RefetKoR.js";import{a as I,__tla as E}from"./profile-BQCm_-PE.js";let p,F=Promise.all([(()=>{try{return x}catch{}})(),(()=>{try{return U}catch{}})(),(()=>{try{return C}catch{}})(),(()=>{try{return E}catch{}})()]).then(async()=>{p=b({name:"ResetPwd",__name:"ResetPwd",setup(L){const{t:r}=v(),f=R(),n=y(),s=u({oldPassword:"",newPassword:"",confirmPassword:""}),_=u({oldPassword:[{required:!0,message:r("profile.password.oldPwdMsg"),trigger:"blur"},{min:6,max:20,message:r("profile.password.pwdRules"),trigger:"blur"}],newPassword:[{required:!0,message:r("profile.password.newPwdMsg"),trigger:"blur"},{min:6,max:20,message:r("profile.password.pwdRules"),trigger:"blur"}],confirmPassword:[{required:!0,message:r("profile.password.cfPwdMsg"),trigger:"blur"},{required:!0,validator:(P,a,o)=>{s.newPassword!==a?o(new Error(r("profile.password.diffPwd"))):o()},trigger:"blur"}]});return(P,a)=>{const o=q,m=M,c=k;return V(),h(c,{ref_key:"formRef",ref:n,model:e(s),rules:e(_),"label-width":200},{default:w(()=>[l(o,{label:e(r)("profile.password.oldPassword"),prop:"oldPassword"},{default:w(()=>[l(e(i),{modelValue:e(s).oldPassword,"onUpdate:modelValue":a[0]||(a[0]=d=>e(s).oldPassword=d)},null,8,["modelValue"])]),_:1},8,["label"]),l(o,{label:e(r)("profile.password.newPassword"),prop:"newPassword"},{default:w(()=>[l(e(i),{modelValue:e(s).newPassword,"onUpdate:modelValue":a[1]||(a[1]=d=>e(s).newPassword=d),strength:""},null,8,["modelValue"])]),_:1},8,["label"]),l(o,{label:e(r)("profile.password.confirmPassword"),prop:"confirmPassword"},{default:w(()=>[l(e(i),{modelValue:e(s).confirmPassword,"onUpdate:modelValue":a[2]||(a[2]=d=>e(s).confirmPassword=d),strength:""},null,8,["modelValue"])]),_:1},8,["label"]),l(o,null,{default:w(()=>[l(m,{title:e(r)("common.save"),type:"primary",onClick:a[3]||(a[3]=d=>{var t;(t=e(n))&&t.validate(async g=>{g&&(await I(s.oldPassword,s.newPassword),f.success(r("common.updateSuccess")))})})},null,8,["title"]),l(m,{title:e(r)("common.reset"),type:"danger",onClick:a[4]||(a[4]=d=>{var t;(t=e(n))&&t.resetFields()})},null,8,["title"])]),_:1})]),_:1},8,["model","rules"])}}})});export{p as _,F as __tla};
