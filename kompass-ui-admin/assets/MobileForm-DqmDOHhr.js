import{d as G,a7 as J,u as K,r as g,b as Q,a as e,f as w,at as W,H as X,a8 as Y,o as _,l as C,w as l,i as a,a9 as b,c as S,t as V,n as $,I as ee,dp as ae,aj as le,ak as te,af as I,dq as oe,ac as ne,L as ie,E as se,Z as re,s as de,O as me,B as pe,__tla as ce}from"./index-BUSn51wb.js";import{_ as ue,__tla as ge}from"./XButton-BjahQbul.js";import{u as h,__tla as _e}from"./useIcon-th7lSKBX.js";import{u as fe,L as be,_ as he,a as ye,__tla as xe}from"./LoginFormTitle.vue_vue_type_script_setup_true_lang-DUoWRVM3.js";import{r as y,__tla as ve}from"./formRules-CA9eXdcX.js";let L,ke=Promise.all([(()=>{try{return ce}catch{}})(),(()=>{try{return ge}catch{}})(),(()=>{try{return _e}catch{}})(),(()=>{try{return xe}catch{}})(),(()=>{try{return ve}catch{}})()]).then(async()=>{let x;x={key:1,class:"getMobileCode",style:{cursor:"pointer"}},L=pe(G({name:"MobileForm",__name:"MobileForm",setup(Fe){const{t:i}=$(),M=ee(),E=J(),{currentRoute:P,push:B}=K(),v=g(),c=g(!1),U=h({icon:"ep:house"}),j=h({icon:"ep:cellphone"}),q=h({icon:"ep:circle-check"}),{validForm:H}=ye(v),{handleBackLogin:O,getLoginState:R}=fe(),z=Q(()=>e(R)===be.MOBILE),T={tenantName:[y],mobileNumber:[y],code:[y]},t=w({codeImg:"",tenantEnable:"true",token:"",loading:{signIn:!1},loginForm:{uuid:"",tenantName:"\u828B\u9053\u6E90\u7801",mobileNumber:"",code:""}}),p=w({smsCode:{mobile:"",scene:21},loginSms:{mobile:"",code:""}}),s=g(0),u=g(""),Z=async()=>{await k(),p.smsCode.mobile=t.loginForm.mobileNumber,await ae(p.smsCode).then(async()=>{M.success(i("login.SmsSendMsg")),s.value=60;let n=setInterval(()=>{s.value=s.value-1,s.value<=0&&clearInterval(n)},1e3)})};W(()=>P.value,n=>{var o;u.value=(o=n==null?void 0:n.query)==null?void 0:o.redirect},{immediate:!0});const k=async()=>{if(t.tenantEnable==="true"){const n=await le(t.loginForm.tenantName);te(n)}};return(n,o)=>{const m=ie,r=se,f=re,F=de,N=ue,A=me;return X((_(),C(A,{ref_key:"formSmsLogin",ref:v,model:e(t).loginForm,rules:T,class:"login-form","label-position":"top","label-width":"120px",size:"large"},{default:l(()=>[a(F,{style:{"margin-right":"-10px","margin-left":"-10px"}},{default:l(()=>[a(r,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[a(m,null,{default:l(()=>[a(he,{style:{width:"100%"}})]),_:1})]),_:1}),a(r,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[e(t).tenantEnable==="true"?(_(),C(m,{key:0,prop:"tenantName"},{default:l(()=>[a(f,{modelValue:e(t).loginForm.tenantName,"onUpdate:modelValue":o[0]||(o[0]=d=>e(t).loginForm.tenantName=d),placeholder:e(i)("login.tenantNamePlaceholder"),"prefix-icon":e(U),type:"primary",link:""},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})):b("",!0)]),_:1}),a(r,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[a(m,{prop:"mobileNumber"},{default:l(()=>[a(f,{modelValue:e(t).loginForm.mobileNumber,"onUpdate:modelValue":o[1]||(o[1]=d=>e(t).loginForm.mobileNumber=d),placeholder:e(i)("login.mobileNumberPlaceholder"),"prefix-icon":e(j)},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),a(r,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[a(m,{prop:"code"},{default:l(()=>[a(F,{gutter:5,justify:"space-between",style:{width:"100%"}},{default:l(()=>[a(r,{span:24},{default:l(()=>[a(f,{modelValue:e(t).loginForm.code,"onUpdate:modelValue":o[2]||(o[2]=d=>e(t).loginForm.code=d),placeholder:e(i)("login.codePlaceholder"),"prefix-icon":e(q)},{append:l(()=>[e(s)<=0?(_(),S("span",{key:0,class:"getMobileCode",style:{cursor:"pointer"},onClick:Z},V(e(i)("login.getSmsCode")),1)):b("",!0),e(s)>0?(_(),S("span",x,V(e(s))+"\u79D2\u540E\u53EF\u91CD\u65B0\u83B7\u53D6 ",1)):b("",!0)]),_:1},8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1})]),_:1})]),_:1}),a(r,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[a(m,null,{default:l(()=>[a(N,{loading:e(c),title:e(i)("login.login"),class:"w-[100%]",type:"primary",onClick:o[3]||(o[3]=d=>(async()=>{await k(),await H()&&(I.service({lock:!0,text:"\u6B63\u5728\u52A0\u8F7D\u7CFB\u7EDF\u4E2D...",background:"rgba(0, 0, 0, 0.7)"}),c.value=!0,p.loginSms.mobile=t.loginForm.mobileNumber,p.loginSms.code=t.loginForm.code,await oe(p.loginSms).then(async D=>{ne(D),u.value||(u.value="/"),B({path:u.value||E.addRouters[0].path})}).catch(()=>{}).finally(()=>{c.value=!1,setTimeout(()=>{I.service().close()},400)}))})())},null,8,["loading","title"])]),_:1})]),_:1}),a(r,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[a(m,null,{default:l(()=>[a(N,{loading:e(c),title:e(i)("login.backLogin"),class:"w-[100%]",onClick:o[4]||(o[4]=d=>e(O)())},null,8,["loading","title"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])),[[Y,e(z)]])}}}),[["__scopeId","data-v-ca3a1bb5"]])});export{ke as __tla,L as default};
