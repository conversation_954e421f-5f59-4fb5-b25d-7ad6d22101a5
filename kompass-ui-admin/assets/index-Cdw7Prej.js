import{d as X,I as Y,r as i,e9 as ee,b as w,eu as ae,bx as le,C as te,o as m,c as f,i as s,g as e,w as r,a as t,j as y,t as h,y as b,F as g,k as z,l as se,ax as re,Z as ue,N as oe,J as ne,K as de,__tla as ce}from"./index-BUSn51wb.js";import{E as ie,__tla as me}from"./el-card-CJbXGyyg.js";import{_ as pe,__tla as ve}from"./index-COobLwz-.js";import{f as _e,__tla as fe}from"./formatTime-DWdBpgsM.js";import{g as ye,__tla as he}from"./index-BYXzDB8j.js";let F,xe=Promise.all([(()=>{try{return ce}catch{}})(),(()=>{try{return me}catch{}})(),(()=>{try{return ve}catch{}})(),(()=>{try{return fe}catch{}})(),(()=>{try{return he}catch{}})()]).then(async()=>{let k,V,U,N,S,I,J,O,$,C,D,R;k={class:"flex"},V=e("div",{class:"card-header"},[e("span",null,"\u8FDE\u63A5")],-1),U={class:"flex items-center"},N=e("span",{class:"mr-4 text-lg font-medium"}," \u8FDE\u63A5\u72B6\u6001: ",-1),S=e("hr",{class:"my-4"},null,-1),I={class:"flex"},J=e("p",{class:"mt-4 text-lg font-medium"},"\u6D88\u606F\u8F93\u5165\u6846",-1),O=e("hr",{class:"my-4"},null,-1),$=e("div",{class:"card-header"},[e("span",null,"\u6D88\u606F\u8BB0\u5F55")],-1),C={class:"max-h-80 overflow-auto"},D={class:"flex items-center"},R=e("span",{class:"text-primary mr-2 font-medium"},"\u6536\u5230\u6D88\u606F:",-1),F=X({name:"InfraWebSocket",__name:"index",setup(we){const x=Y(),p=i("http://118.195.130.96:48080/infra/ws".replace("http","ws")+"?token="+ee()),n=w(()=>T.value==="OPEN"),Z=w(()=>n.value?"success":"red"),{status:T,data:d,send:q,close:A,open:B}=ae(p.value,{autoReconnect:!1,heartbeat:!0}),v=i([]),G=w(()=>v.value.slice().reverse());le(()=>{if(d.value)try{if(d.value==="pong")return;const o=JSON.parse(d.value),l=o.type,u=JSON.parse(o.content);if(!l)return void x.error("\u672A\u77E5\u7684\u6D88\u606F\u7C7B\u578B\uFF1A"+d.value);if(l==="demo-message-receive")return void(u.single?v.value.push({text:`\u3010\u5355\u53D1\u3011\u7528\u6237\u7F16\u53F7(${u.fromUserId})\uFF1A${u.text}`,time:new Date().getTime()}):v.value.push({text:`\u3010\u7FA4\u53D1\u3011\u7528\u6237\u7F16\u53F7(${u.fromUserId})\uFF1A${u.text}`,time:new Date().getTime()}));if(l==="notice-push")return void v.value.push({text:`\u3010\u7CFB\u7EDF\u901A\u77E5\u3011\uFF1A${u.title}`,time:new Date().getTime()});x.error("\u672A\u5904\u7406\u6D88\u606F\uFF1A"+d.value)}catch(o){x.error("\u5904\u7406\u6D88\u606F\u53D1\u751F\u5F02\u5E38\uFF1A"+d.value),console.error(o)}});const c=i(""),_=i(""),H=()=>{const o=JSON.stringify({text:c.value,toUserId:_.value}),l=JSON.stringify({type:"demo-message-send",content:o});q(l),c.value=""},L=()=>{n.value?A():B()},E=i([]);return te(async()=>{E.value=await ye()}),(o,l)=>{const u=pe,M=re,K=ue,P=oe,W=ne,Q=de,j=ie;return m(),f(g,null,[s(u,{title:"WebSocket \u5B9E\u65F6\u901A\u4FE1",url:"https://doc.iocoder.cn/websocket/"}),e("div",k,[s(j,{gutter:12,class:"w-1/2",shadow:"always"},{header:r(()=>[V]),default:r(()=>[e("div",U,[N,s(M,{color:t(Z)},{default:r(()=>[y(h(t(T)),1)]),_:1},8,["color"])]),S,e("div",I,[s(K,{modelValue:t(p),"onUpdate:modelValue":l[0]||(l[0]=a=>b(p)?p.value=a:null),disabled:""},{prepend:r(()=>[y("\u670D\u52A1\u5730\u5740")]),_:1},8,["modelValue"]),s(P,{type:t(n)?"danger":"primary",onClick:L},{default:r(()=>[y(h(t(n)?"\u5173\u95ED\u8FDE\u63A5":"\u5F00\u542F\u8FDE\u63A5"),1)]),_:1},8,["type"])]),J,O,s(K,{modelValue:t(c),"onUpdate:modelValue":l[1]||(l[1]=a=>b(c)?c.value=a:null),autosize:{minRows:2,maxRows:4},disabled:!t(n),clearable:"",placeholder:"\u8BF7\u8F93\u5165\u4F60\u8981\u53D1\u9001\u7684\u6D88\u606F",type:"textarea"},null,8,["modelValue","disabled"]),s(Q,{modelValue:t(_),"onUpdate:modelValue":l[2]||(l[2]=a=>b(_)?_.value=a:null),class:"mt-4",placeholder:"\u8BF7\u9009\u62E9\u53D1\u9001\u4EBA"},{default:r(()=>[s(W,{key:"",label:"\u6240\u6709\u4EBA",value:""}),(m(!0),f(g,null,z(t(E),a=>(m(),se(W,{key:a.id,label:a.nickname,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),s(P,{disabled:!t(n),block:"",class:"ml-2 mt-4",type:"primary",onClick:H},{default:r(()=>[y(" \u53D1\u9001 ")]),_:1},8,["disabled"])]),_:1}),s(j,{gutter:12,class:"w-1/2",shadow:"always"},{header:r(()=>[$]),default:r(()=>[e("div",C,[e("ul",null,[(m(!0),f(g,null,z(t(G),a=>(m(),f("li",{key:a.time,class:"mt-2"},[e("div",D,[R,e("span",null,h(t(_e)(a.time)),1)]),e("div",null,h(a.text),1)]))),128))])])]),_:1})])],64)}}})});export{xe as __tla,F as default};
