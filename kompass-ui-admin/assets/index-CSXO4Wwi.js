import{d as o,o as r,c as e,l as p,av as y,B as c,__tla as _}from"./index-BUSn51wb.js";import{E as u,__tla as n}from"./el-image-BjHZRFih.js";let a,d=Promise.all([(()=>{try{return _}catch{}})(),(()=>{try{return n}catch{}})()]).then(async()=>{let l;l=["src","poster","autoplay"],a=c(o({name:"VideoPlayer",__name:"index",props:{property:{}},setup:i=>(t,f)=>{const s=u;return r(),e("div",{class:"w-full",style:y({height:`${t.property.style.height}px`})},[t.property.posterUrl?(r(),p(s,{key:0,class:"w-full w-full",src:t.property.posterUrl},null,8,["src"])):(r(),e("video",{key:1,class:"w-full w-full",src:t.property.videoUrl,poster:t.property.posterUrl,autoplay:t.property.autoplay,controls:""},null,8,l))],4)}}),[["__scopeId","data-v-afd0794c"]])});export{d as __tla,a as default};
