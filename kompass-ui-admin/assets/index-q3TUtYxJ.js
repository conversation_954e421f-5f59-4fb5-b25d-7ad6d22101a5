import{d as sa,S as oa,u as ua,I as na,r as s,b as ca,C as ia,au as ma,o,c as v,i as _,a,H as pa,l as y,w as t,y as J,F as b,k as x,g as A,j as F,t as T,E as fa,s as da,z as ya,A as ha,_ as va,N as ba,J as ga,K as ka,L as wa,O as xa,R as Ca,__tla as Va}from"./index-BUSn51wb.js";import{_ as Ua,__tla as Sa}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{E as Ia,__tla as Pa}from"./el-card-CJbXGyyg.js";import{E as Aa,__tla as Fa}from"./el-text-CIwNlU-U.js";import{E as qa,__tla as za}from"./el-image-BjHZRFih.js";import{_ as Ea,__tla as La}from"./index-COobLwz-.js";import{b as Oa,a as Ra,__tla as Ba}from"./index-D5ZFci2X.js";import{g as Da,a as Ha,__tla as Ja}from"./index-BtD-8VxR.js";import{b as Ta,__tla as $a}from"./formCreate-DDLxm5B5.js";import{_ as ja,__tla as Ga}from"./ProcessInstanceBpmnViewer.vue_vue_type_style_index_0_lang-5V_uep-K.js";import{C as Ka,__tla as Ma}from"./index-B5YaQXtD.js";import{u as Na,__tla as Xa}from"./tagsView-BOOrxb3Q.js";import{g as Ya,__tla as Za}from"./index-BYXzDB8j.js";import{__tla as Qa}from"./bpmn-embedded-D6vUWKn8.js";import{__tla as Wa}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as at}from"./XTextButton-DMuYh5Ak.js";import{__tla as tt}from"./XButton-BjahQbul.js";import{__tla as et}from"./el-collapse-item-B_QvnH_b.js";import{__tla as rt}from"./index-COJ8hy-t.js";import{__tla as lt}from"./el-tree-select-CBuha0HW.js";import"./tree-BMa075Oj.js";import{__tla as _t}from"./index-CCFX7HyJ.js";import{__tla as st}from"./index-Bqt292RI.js";import{__tla as ot}from"./index-D6tFY92u.js";import{__tla as ut}from"./index-xiOMzVtR.js";import{__tla as nt}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{__tla as ct}from"./index-Cch5e1V0.js";import"./constants-A8BI3pz7.js";import{__tla as it}from"./index-BEeS1wHc.js";import{__tla as mt}from"./el-drawer-DMK0hKF6.js";import{__tla as pt}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import"./color-BN7ZL7BD.js";import{__tla as ft}from"./index-CRkUQbt2.js";import{__tla as dt}from"./formatTime-DWdBpgsM.js";let $,yt=Promise.all([(()=>{try{return Va}catch{}})(),(()=>{try{return Sa}catch{}})(),(()=>{try{return Pa}catch{}})(),(()=>{try{return Fa}catch{}})(),(()=>{try{return za}catch{}})(),(()=>{try{return La}catch{}})(),(()=>{try{return Ba}catch{}})(),(()=>{try{return Ja}catch{}})(),(()=>{try{return $a}catch{}})(),(()=>{try{return Ga}catch{}})(),(()=>{try{return Ma}catch{}})(),(()=>{try{return Xa}catch{}})(),(()=>{try{return Za}catch{}})(),(()=>{try{return Qa}catch{}})(),(()=>{try{return Wa}catch{}})(),(()=>{try{return at}catch{}})(),(()=>{try{return tt}catch{}})(),(()=>{try{return et}catch{}})(),(()=>{try{return rt}catch{}})(),(()=>{try{return lt}catch{}})(),(()=>{try{return _t}catch{}})(),(()=>{try{return st}catch{}})(),(()=>{try{return ot}catch{}})(),(()=>{try{return ut}catch{}})(),(()=>{try{return nt}catch{}})(),(()=>{try{return ct}catch{}})(),(()=>{try{return it}catch{}})(),(()=>{try{return mt}catch{}})(),(()=>{try{return pt}catch{}})(),(()=>{try{return ft}catch{}})(),(()=>{try{return dt}catch{}})()]).then(async()=>{let q,z,E;q={class:"flex"},z={class:"clearfix"},E={class:"el-icon-document"},$=sa({name:"BpmProcessInstanceCreate",__name:"index",setup(ht){const j=oa(),{push:L,currentRoute:G}=ua(),C=na(),{delView:K}=Na(),g=j.query.processInstanceId,V=s(!0),k=s([]),h=s(""),U=s([]),M=ca(()=>U.value.filter(e=>e.category==h.value)),c=s(),i=s({rule:[],option:{},value:{}}),m=s(),O=s(null),p=s([]),f=s({}),R=s(),S=s({}),B=s([]),D=async(e,r)=>{var d;if(m.value=e,p.value=[],f.value={},S.value={},e.formType==10){Ta(i,e.formConf,e.formFields,r);const n=await Ra(e.id);if(n&&(O.value=n.bpmnXml,p.value=n.startUserSelectTasks,((d=p.value)==null?void 0:d.length)>0)){i.value.rule.push({type:"startUserSelect",props:{title:"\u6307\u5B9A\u5BA1\u6279\u4EBA"}});for(const w of p.value)f.value[w.id]=[],S.value[w.id]=[{required:!0,message:"\u8BF7\u9009\u62E9\u5BA1\u6279\u4EBA",trigger:"blur"}];B.value=await Ya()}}else e.formCustomCreatePath&&await L({path:e.formCustomCreatePath})},N=async e=>{var r;if(c.value&&m.value){((r=p.value)==null?void 0:r.length)>0&&await R.value.validate(),c.value.btn.loading(!0);try{await Ha({processDefinitionId:m.value.id,variables:e,startUserSelectAssignees:f.value}),C.success("\u53D1\u8D77\u6D41\u7A0B\u6210\u529F"),K(a(G)),await L({name:"BpmProcessInstanceMy"})}finally{c.value.btn.loading(!1)}}};return ia(()=>{(async()=>{V.value=!0;try{if(k.value=await Ka.getCategorySimpleList(),k.value.length>0&&(h.value=k.value[0].code),U.value=await Oa({suspensionState:1}),(g==null?void 0:g.length)>0){const e=await Da(g);if(!e)return void C.error("\u91CD\u65B0\u53D1\u8D77\u6D41\u7A0B\u5931\u8D25\uFF0C\u539F\u56E0\uFF1A\u6D41\u7A0B\u5B9E\u4F8B\u4E0D\u5B58\u5728");const r=U.value.find(d=>{var n;return d.key==((n=e.processDefinition)==null?void 0:n.key)});if(!r)return void C.error("\u91CD\u65B0\u53D1\u8D77\u6D41\u7A0B\u5931\u8D25\uFF0C\u539F\u56E0\uFF1A\u6D41\u7A0B\u5B9A\u4E49\u4E0D\u5B58\u5728");await D(r,e.formVariables)}}finally{V.value=!1}})()}),(e,r)=>{const d=Ea,n=qa,w=Aa,I=Ia,P=fa,X=da,Y=ya,Z=ha,H=Ua,Q=va,W=ba,aa=ga,ta=ka,ea=wa,ra=xa,la=ma("form-create"),_a=Ca;return o(),v(b,null,[_(d,{title:"\u6D41\u7A0B\u53D1\u8D77\u3001\u53D6\u6D88\u3001\u91CD\u65B0\u53D1\u8D77",url:"https://doc.iocoder.cn/bpm/process-instance/"}),a(m)?(o(),y(H,{key:1},{default:t(()=>[_(I,{class:"box-card"},{default:t(()=>[A("div",z,[A("span",E,"\u7533\u8BF7\u4FE1\u606F\u3010"+T(a(m).name)+"\u3011",1),_(W,{style:{float:"right"},type:"primary",onClick:r[1]||(r[1]=l=>m.value=void 0)},{default:t(()=>[_(Q,{icon:"ep:delete"}),F(" \u9009\u62E9\u5176\u5B83\u6D41\u7A0B ")]),_:1})]),_(P,{span:16,offset:6,style:{"margin-top":"20px"}},{default:t(()=>[_(la,{rule:a(i).rule,api:a(c),"onUpdate:api":r[2]||(r[2]=l=>J(c)?c.value=l:null),modelValue:a(i).value,"onUpdate:modelValue":r[3]||(r[3]=l=>a(i).value=l),option:a(i).option,onSubmit:N},{"type-startUserSelect":t(()=>[_(P,{span:24},{default:t(()=>[_(I,{class:"mb-10px"},{header:t(()=>[F("\u6307\u5B9A\u5BA1\u6279\u4EBA")]),default:t(()=>[_(ra,{model:a(f),rules:a(S),ref_key:"startUserSelectAssigneesFormRef",ref:R},{default:t(()=>[(o(!0),v(b,null,x(a(p),l=>(o(),y(ea,{key:l.id,label:`\u4EFB\u52A1\u3010${l.name}\u3011`,prop:l.id},{default:t(()=>[_(ta,{modelValue:a(f)[l.id],"onUpdate:modelValue":u=>a(f)[l.id]=u,multiple:"",placeholder:"\u8BF7\u9009\u62E9\u5BA1\u6279\u4EBA"},{default:t(()=>[(o(!0),v(b,null,x(a(B),u=>(o(),y(aa,{key:u.id,label:u.nickname,value:u.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["label","prop"]))),128))]),_:1},8,["model","rules"])]),_:1})]),_:1})]),_:1},8,["rule","api","modelValue","option"])]),_:1})]),_:1}),_(ja,{"bpmn-xml":a(O)},null,8,["bpmn-xml"])]),_:1})):pa((o(),y(H,{key:0},{default:t(()=>[_(Z,{"tab-position":"left",modelValue:a(h),"onUpdate:modelValue":r[0]||(r[0]=l=>J(h)?h.value=l:null)},{default:t(()=>[(o(!0),v(b,null,x(a(k),l=>(o(),y(Y,{label:l.name,name:l.code,key:l.code},{default:t(()=>[_(X,{gutter:20},{default:t(()=>[(o(!0),v(b,null,x(a(M),u=>(o(),y(P,{lg:6,sm:12,xs:24,key:u.id},{default:t(()=>[_(I,{shadow:"hover",class:"mb-20px cursor-pointer",onClick:vt=>D(u)},{default:t(()=>[A("div",q,[_(n,{src:u.icon,class:"w-32px h-32px"},null,8,["src"]),_(w,{class:"!ml-10px",size:"large"},{default:t(()=>[F(T(u.name),1)]),_:2},1024)])]),_:2},1032,["onClick"])]),_:2},1024))),128))]),_:1})]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])]),_:1})),[[_a,a(V)]])],64)}}})});export{yt as __tla,$ as default};
