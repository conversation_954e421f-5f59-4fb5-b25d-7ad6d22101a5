import{d as J,r as i,f as K,o as s,l as v,w as a,i as e,j as E,a as d,H as P,c as U,k as y,V as k,G as q,F as w,y as Y,n as Z,I as z,e as B,Z as W,L as X,E as $,J as ee,K as le,s as ae,cd as de,M as oe,O as ue,N as te,R as re,__tla as se}from"./index-BUSn51wb.js";import{_ as ne,__tla as me}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{g as ie,c as pe,b as _e,__tla as ce}from"./index-CD52sTBY.js";import{g as ve,__tla as fe}from"./index-CyP7ZSdX.js";import{d as Ve}from"./tree-BMa075Oj.js";import{g as be,__tla as he}from"./index-BYXzDB8j.js";let M,Ue=Promise.all([(()=>{try{return se}catch{}})(),(()=>{try{return me}catch{}})(),(()=>{try{return ce}catch{}})(),(()=>{try{return fe}catch{}})(),(()=>{try{return he}catch{}})()]).then(async()=>{M=J({__name:"CustomerForm",emits:["success"],setup(ye,{expose:N,emit:S}){const{t:I}=Z(),g=z(),_=i(!1),x=i(""),c=i(!1),f=i(""),C=i([]),R=i([]),u=i({id:void 0,name:void 0,contactNextTime:void 0,ownerUserId:0,mobile:void 0,telephone:void 0,qq:void 0,wechat:void 0,email:void 0,areaId:void 0,detailAddress:void 0,industryId:void 0,level:void 0,source:void 0,remark:void 0}),A=K({name:[{required:!0,message:"\u5BA2\u6237\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],ownerUserId:[{required:!0,message:"\u8D1F\u8D23\u4EBA\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),V=i();N({open:async(n,o)=>{if(_.value=!0,x.value=I("action."+n),f.value=n,F(),o){c.value=!0;try{u.value=await ie(o)}finally{c.value=!1}}C.value=await ve(),R.value=await be(),f.value==="create"&&(u.value.ownerUserId=B().getUser.id)}});const O=S,Q=async()=>{if(V&&await V.value.validate()){c.value=!0;try{const n=u.value;f.value==="create"?(await pe(n),g.success(I("common.createSuccess"))):(await _e(n),g.success(I("common.updateSuccess"))),_.value=!1,O("success")}finally{c.value=!1}}},F=()=>{var n;u.value={id:void 0,name:void 0,contactNextTime:void 0,ownerUserId:0,mobile:void 0,telephone:void 0,qq:void 0,wechat:void 0,email:void 0,areaId:void 0,detailAddress:void 0,industryId:void 0,level:void 0,source:void 0,remark:void 0},(n=V.value)==null||n.resetFields()};return(n,o)=>{const m=W,t=X,r=$,b=ee,h=le,p=ae,L=de,j=oe,D=ue,T=te,G=ne,H=re;return s(),v(G,{modelValue:d(_),"onUpdate:modelValue":o[15]||(o[15]=l=>Y(_)?_.value=l:null),title:d(x)},{footer:a(()=>[e(T,{disabled:d(c),type:"primary",onClick:Q},{default:a(()=>[E("\u786E \u5B9A")]),_:1},8,["disabled"]),e(T,{onClick:o[14]||(o[14]=l=>_.value=!1)},{default:a(()=>[E("\u53D6 \u6D88")]),_:1})]),default:a(()=>[P((s(),v(D,{ref_key:"formRef",ref:V,model:d(u),rules:d(A),"label-width":"100px"},{default:a(()=>[e(p,null,{default:a(()=>[e(r,{span:12},{default:a(()=>[e(t,{label:"\u5BA2\u6237\u540D\u79F0",prop:"name"},{default:a(()=>[e(m,{modelValue:d(u).name,"onUpdate:modelValue":o[0]||(o[0]=l=>d(u).name=l),placeholder:"\u8BF7\u8F93\u5165\u5BA2\u6237\u540D\u79F0"},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:a(()=>[e(t,{label:"\u5BA2\u6237\u6765\u6E90",prop:"source"},{default:a(()=>[e(h,{modelValue:d(u).source,"onUpdate:modelValue":o[1]||(o[1]=l=>d(u).source=l),placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237\u6765\u6E90",class:"w-1/1"},{default:a(()=>[(s(!0),U(w,null,y(d(k)(d(q).CRM_CUSTOMER_SOURCE),l=>(s(),v(b,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(p,null,{default:a(()=>[e(r,{span:12},{default:a(()=>[e(t,{label:"\u624B\u673A",prop:"mobile"},{default:a(()=>[e(m,{modelValue:d(u).mobile,"onUpdate:modelValue":o[2]||(o[2]=l=>d(u).mobile=l),placeholder:"\u8BF7\u8F93\u5165\u624B\u673A"},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:a(()=>[e(t,{label:"\u8D1F\u8D23\u4EBA",prop:"ownerUserId"},{default:a(()=>[e(h,{modelValue:d(u).ownerUserId,"onUpdate:modelValue":o[3]||(o[3]=l=>d(u).ownerUserId=l),disabled:d(f)!=="create",class:"w-1/1"},{default:a(()=>[(s(!0),U(w,null,y(d(R),l=>(s(),v(b,{key:l.id,label:l.nickname,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(p,null,{default:a(()=>[e(r,{span:12},{default:a(()=>[e(t,{label:"\u7535\u8BDD",prop:"telephone"},{default:a(()=>[e(m,{modelValue:d(u).telephone,"onUpdate:modelValue":o[4]||(o[4]=l=>d(u).telephone=l),placeholder:"\u8BF7\u8F93\u5165\u7535\u8BDD"},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:a(()=>[e(t,{label:"\u90AE\u7BB1",prop:"email"},{default:a(()=>[e(m,{modelValue:d(u).email,"onUpdate:modelValue":o[5]||(o[5]=l=>d(u).email=l),placeholder:"\u8BF7\u8F93\u5165\u90AE\u7BB1"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(p,null,{default:a(()=>[e(r,{span:12},{default:a(()=>[e(t,{label:"\u5FAE\u4FE1",prop:"wechat"},{default:a(()=>[e(m,{modelValue:d(u).wechat,"onUpdate:modelValue":o[6]||(o[6]=l=>d(u).wechat=l),placeholder:"\u8BF7\u8F93\u5165\u5FAE\u4FE1"},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:a(()=>[e(t,{label:"QQ",prop:"qq"},{default:a(()=>[e(m,{modelValue:d(u).qq,"onUpdate:modelValue":o[7]||(o[7]=l=>d(u).qq=l),placeholder:"\u8BF7\u8F93\u5165 QQ"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(p,null,{default:a(()=>[e(r,{span:12},{default:a(()=>[e(t,{label:"\u5BA2\u6237\u884C\u4E1A",prop:"industryId"},{default:a(()=>[e(h,{modelValue:d(u).industryId,"onUpdate:modelValue":o[8]||(o[8]=l=>d(u).industryId=l),placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237\u884C\u4E1A",class:"w-1/1"},{default:a(()=>[(s(!0),U(w,null,y(d(k)(d(q).CRM_CUSTOMER_INDUSTRY),l=>(s(),v(b,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:a(()=>[e(t,{label:"\u5BA2\u6237\u7EA7\u522B",prop:"level"},{default:a(()=>[e(h,{modelValue:d(u).level,"onUpdate:modelValue":o[9]||(o[9]=l=>d(u).level=l),placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237\u7EA7\u522B",class:"w-1/1"},{default:a(()=>[(s(!0),U(w,null,y(d(k)(d(q).CRM_CUSTOMER_LEVEL),l=>(s(),v(b,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(p,null,{default:a(()=>[e(r,{span:12},{default:a(()=>[e(t,{label:"\u5730\u5740",prop:"areaId"},{default:a(()=>[e(L,{modelValue:d(u).areaId,"onUpdate:modelValue":o[10]||(o[10]=l=>d(u).areaId=l),options:d(C),props:d(Ve),class:"w-1/1",clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u57CE\u5E02"},null,8,["modelValue","options","props"])]),_:1})]),_:1}),e(r,{span:12},{default:a(()=>[e(t,{label:"\u8BE6\u7EC6\u5730\u5740",prop:"detailAddress"},{default:a(()=>[e(m,{modelValue:d(u).detailAddress,"onUpdate:modelValue":o[11]||(o[11]=l=>d(u).detailAddress=l),placeholder:"\u8BF7\u8F93\u5165\u8BE6\u7EC6\u5730\u5740"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(p,null,{default:a(()=>[e(r,{span:12},{default:a(()=>[e(t,{label:"\u4E0B\u6B21\u8054\u7CFB\u65F6\u95F4",prop:"contactNextTime"},{default:a(()=>[e(j,{modelValue:d(u).contactNextTime,"onUpdate:modelValue":o[12]||(o[12]=l=>d(u).contactNextTime=l),placeholder:"\u9009\u62E9\u4E0B\u6B21\u8054\u7CFB\u65F6\u95F4",type:"datetime","value-format":"x"},null,8,["modelValue"])]),_:1})]),_:1}),e(r,{span:12},{default:a(()=>[e(t,{label:"\u5907\u6CE8",prop:"remark"},{default:a(()=>[e(m,{type:"textarea",modelValue:d(u).remark,"onUpdate:modelValue":o[13]||(o[13]=l=>d(u).remark=l),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])),[[H,d(c)]])]),_:1},8,["modelValue","title"])}}})});export{M as _,Ue as __tla};
