import{f as M,G as K,by as v,d as Q,n as W,I as X,r as c,o as U,c as Z,i,w as u,a as r,j as k,H as $,l as tt,y as at,F as et,aD as st,ay as ot,N as rt,cc as lt,P as it,R as ct,__tla as ut}from"./index-BUSn51wb.js";import{_ as nt,__tla as dt}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{_ as pt,__tla as mt}from"./Form-DJa9ov9B.js";import{_ as _t,__tla as ft}from"./SpuSelect.vue_vue_type_script_setup_true_lang-Q9Ka9uY_.js";import{_ as yt,__tla as vt}from"./SpuAndSkuList.vue_vue_type_script_setup_true_lang-BlbORlY6.js";import{b as E,__tla as ht}from"./formatTime-DWdBpgsM.js";import{r as h,__tla as wt}from"./formRules-CA9eXdcX.js";import{u as Pt,__tla as gt}from"./useCrudSchemas-hBakuBRx.js";import{b as bt,__tla as kt}from"./spu-CW3JGweV.js";import{g as St,__tla as Tt}from"./index-CjyLHUq3.js";let x,A,O,N,Ct=Promise.all([(()=>{try{return ut}catch{}})(),(()=>{try{return dt}catch{}})(),(()=>{try{return mt}catch{}})(),(()=>{try{return ft}catch{}})(),(()=>{try{return vt}catch{}})(),(()=>{try{return ht}catch{}})(),(()=>{try{return wt}catch{}})(),(()=>{try{return gt}catch{}})(),(()=>{try{return kt}catch{}})(),(()=>{try{return Tt}catch{}})()]).then(async()=>{let S,T,C;S=M({spuId:[h],name:[h],startTime:[h],endTime:[h],discountType:[h]}),T=M([{label:"\u6D3B\u52A8\u540D\u79F0",field:"name",isSearch:!0,form:{colProps:{span:24}},table:{width:120}},{label:"\u6D3B\u52A8\u5F00\u59CB\u65F6\u95F4",field:"startTime",formatter:E,isSearch:!0,search:{component:"DatePicker",componentProps:{valueFormat:"YYYY-MM-DD",type:"daterange"}},form:{component:"DatePicker",componentProps:{type:"date",valueFormat:"x"}},table:{width:120}},{label:"\u6D3B\u52A8\u7ED3\u675F\u65F6\u95F4",field:"endTime",formatter:E,isSearch:!0,search:{component:"DatePicker",componentProps:{valueFormat:"YYYY-MM-DD",type:"daterange"}},form:{component:"DatePicker",componentProps:{type:"date",valueFormat:"x"}},table:{width:120}},{label:"\u4F18\u60E0\u7C7B\u578B",field:"discountType",dictType:K.PROMOTION_DISCOUNT_TYPE,dictClass:"number",isSearch:!0,form:{component:"Radio",value:1}},{label:"\u6D3B\u52A8\u5546\u54C1",field:"spuId",isTable:!0,isSearch:!1,form:{colProps:{span:24}},table:{width:300}},{label:"\u5907\u6CE8",field:"remark",isSearch:!1,form:{component:"Input",componentProps:{type:"textarea",rows:4},colProps:{span:24}},table:{width:300}}]),{allSchemas:C}=Pt(T),N=async m=>await v.get({url:"/promotion/discount-activity/page",params:m}),A=async m=>await v.put({url:"/promotion/discount-activity/close?id="+m}),O=async m=>await v.delete({url:"/promotion/discount-activity/delete?id="+m}),x=Q({name:"PromotionDiscountActivityForm",__name:"DiscountActivityForm",emits:["success"],setup(m,{expose:L,emit:j}){const{t:g}=W(),D=X(),_=c(!1),I=c(""),f=c(!1),V=c(""),n=c(),F=c(),Y=c(),G=[],w=c([]),b=c([]),H=(o,a)=>{n.value.setValues({spuId:o}),R(o,a)},R=async(o,a,e)=>{var P;const l=[],d=await bt([o]);if(d.length==0)return;w.value=[];const t=d[0],y=a===void 0?t==null?void 0:t.skus:(P=t==null?void 0:t.skus)==null?void 0:P.filter(s=>a.includes(s.id));y==null||y.forEach(s=>{let p={skuId:s.id,spuId:t.id,discountType:1,discountPercent:0,discountPrice:0};e!==void 0&&(p=e.find(J=>J.skuId===s.id)||p),s.productConfig=p}),t.skus=y,l.push({spuId:t.id,spuDetail:t,propertyList:St(t)}),w.value.push(t),b.value=l};L({open:async(o,a)=>{var e;if(_.value=!0,I.value=g("action."+o),V.value=o,await B(),a){f.value=!0;try{const l=await(async t=>await v.get({url:"/promotion/discount-activity/get?id="+t}))(a),d=l.products[0].spuId;await R(d,(e=l.products)==null?void 0:e.map(t=>t.skuId),l.products),n.value.setValues(l)}finally{f.value=!1}}}});const q=j,z=async()=>{if(n&&await n.value.getElFormRef().validate()){f.value=!0;try{const o=n.value.formModel,a=st(Y.value.getSkuConfigs("productConfig"));a.forEach(e=>{e.discountType=o.discountType}),o.products=a,V.value==="create"?(await(async e=>await v.post({url:"/promotion/discount-activity/create",data:e}))(o),D.success(g("common.createSuccess"))):(await(async e=>await v.put({url:"/promotion/discount-activity/update",data:e}))(o),D.success(g("common.updateSuccess"))),_.value=!1,q("success")}finally{f.value=!1}}},B=async()=>{w.value=[],b.value=[],await ot(),n.value.getElFormRef().resetFields()};return(o,a)=>{const e=rt,l=lt,d=it,t=pt,y=nt,P=ct;return U(),Z(et,null,[i(y,{modelValue:r(_),"onUpdate:modelValue":a[2]||(a[2]=s=>at(_)?_.value=s:null),title:r(I),width:"65%"},{footer:u(()=>[i(e,{disabled:r(f),type:"primary",onClick:z},{default:u(()=>[k("\u786E \u5B9A")]),_:1},8,["disabled"]),i(e,{onClick:a[1]||(a[1]=s=>_.value=!1)},{default:u(()=>[k("\u53D6 \u6D88")]),_:1})]),default:u(()=>[$((U(),tt(t,{ref_key:"formRef",ref:n,isCol:!0,rules:r(S),schema:r(C).formSchema},{spuId:u(()=>[i(e,{onClick:a[0]||(a[0]=s=>r(F).open())},{default:u(()=>[k("\u9009\u62E9\u5546\u54C1")]),_:1}),i(r(yt),{ref_key:"spuAndSkuListRef",ref:Y,"rule-config":G,"spu-list":r(w),"spu-property-list-p":r(b)},{default:u(()=>[i(d,{align:"center",label:"\u4F18\u60E0\u91D1\u989D","min-width":"168"},{default:u(({row:s})=>[i(l,{modelValue:s.productConfig.discountPrice,"onUpdate:modelValue":p=>s.productConfig.discountPrice=p,min:0,class:"w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(d,{align:"center",label:"\u6298\u6263\u767E\u5206\u6BD4(%)","min-width":"168"},{default:u(({row:s})=>[i(l,{modelValue:s.productConfig.discountPercent,"onUpdate:modelValue":p=>s.productConfig.discountPercent=p,class:"w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1},8,["spu-list","spu-property-list-p"])]),_:1},8,["rules","schema"])),[[P,r(f)]])]),_:1},8,["modelValue","title"]),i(r(_t),{ref_key:"spuSelectRef",ref:F,isSelectSku:!0,onConfirm:H},null,512)],64)}}})});export{x as _,Ct as __tla,A as c,O as d,N as g};
