import{_ as u,__tla as m}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as d,__tla as y}from"./IFrame.vue_vue_type_script_setup_true_lang-silYuU_p.js";import{_ as f,__tla as h}from"./index-COobLwz-.js";import{b as p,__tla as x}from"./index-BXfU_lLO.js";import{d as b,r as s,C as v,o as e,c as w,i as a,w as g,a as c,l as j,a9 as k,F as B,__tla as C}from"./index-BUSn51wb.js";import{__tla as D}from"./el-card-CJbXGyyg.js";let n,F=Promise.all([(()=>{try{return m}catch{}})(),(()=>{try{return y}catch{}})(),(()=>{try{return h}catch{}})(),(()=>{try{return x}catch{}})(),(()=>{try{return C}catch{}})(),(()=>{try{return D}catch{}})()]).then(async()=>{n=b({name:"InfraDruid",__name:"index",setup(I){const r=s(!0),_=s("http://**************:48080/druid/index.html");return v(async()=>{try{const t=await p("url.druid");t&&t.length>0&&(_.value=t)}finally{r.value=!1}}),(t,M)=>{const l=f,o=d,i=u;return e(),w(B,null,[a(l,{title:"\u6570\u636E\u5E93 MyBatis",url:"https://doc.iocoder.cn/mybatis/"}),a(l,{title:"\u591A\u6570\u636E\u6E90\uFF08\u8BFB\u5199\u5206\u79BB\uFF09",url:"https://doc.iocoder.cn/dynamic-datasource/"}),a(i,null,{default:g(()=>[c(r)?k("",!0):(e(),j(o,{key:0,src:c(_)},null,8,["src"]))]),_:1})],64)}}})});export{F as __tla,n as default};
