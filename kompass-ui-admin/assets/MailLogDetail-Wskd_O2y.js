import{_ as t,__tla as r}from"./MailLogDetail.vue_vue_type_script_setup_true_lang-D1hD4CUj.js";import{__tla as _}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as a}from"./index-BUSn51wb.js";import{__tla as l}from"./Descriptions-DnGjMn9o.js";import{__tla as o}from"./Descriptions.vue_vue_type_style_index_0_scoped_30b8da63_lang-DDC-j81O.js";import{__tla as m}from"./el-descriptions-item-dD3qa0ub.js";import{__tla as c}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import"./color-BN7ZL7BD.js";import{__tla as e}from"./formatTime-DWdBpgsM.js";import{__tla as s}from"./index-Dtskw_Cu.js";import{__tla as i}from"./useCrudSchemas-hBakuBRx.js";import"./tree-BMa075Oj.js";let p=Promise.all([(()=>{try{return r}catch{}})(),(()=>{try{return _}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return m}catch{}})(),(()=>{try{return c}catch{}})(),(()=>{try{return e}catch{}})(),(()=>{try{return s}catch{}})(),(()=>{try{return i}catch{}})()]).then(async()=>{});export{p as __tla,t as default};
