import{d as g,r as f,o as a,c as r,i as e,w as i,F as v,k as b,l as k,t as n,a as w,a9 as j,_ as C,__tla as E}from"./index-BUSn51wb.js";import{E as F,a as P,__tla as U}from"./el-carousel-item-D3JjuyEq.js";import{E as q,__tla as z}from"./el-image-BjHZRFih.js";let c,A=Promise.all([(()=>{try{return E}catch{}})(),(()=>{try{return U}catch{}})(),(()=>{try{return z}catch{}})()]).then(async()=>{let s,l,p;s={key:0,class:"h-250px flex items-center justify-center bg-gray-3"},l={key:1,class:"relative"},p={key:0,class:"absolute bottom-10px right-10px rounded-xl bg-black p-x-8px p-y-2px text-10px text-white opacity-40"},c=g({name:"Carousel",__name:"index",props:{property:{}},setup(B){const o=f(0),y=t=>{o.value=t+1};return(t,D)=>{const u=C,_=q,d=F,m=P;return t.property.items.length===0?(a(),r("div",s,[e(u,{icon:"tdesign:image",class:"text-gray-8 text-120px!"})])):(a(),r("div",l,[e(m,{height:"174px",type:t.property.type==="card"?"card":"",autoplay:t.property.autoplay,interval:1e3*t.property.interval,"indicator-position":t.property.indicator==="number"?"none":void 0,onChange:y},{default:i(()=>[(a(!0),r(v,null,b(t.property.items,(x,h)=>(a(),k(d,{key:h},{default:i(()=>[e(_,{class:"h-full w-full",src:x.imgUrl},null,8,["src"])]),_:2},1024))),128))]),_:1},8,["type","autoplay","interval","indicator-position"]),t.property.indicator==="number"?(a(),r("div",p,n(w(o))+" / "+n(t.property.items.length),1)):j("",!0)]))}}})});export{A as __tla,c as default};
