import{bd as $,d as z,bf as L,b as y,h as R,aV as T,r as A,bx as Y,bV as f,bn as h,be as v,cs as O,bp as I,bm as N,i as m,cG as c,j as B,cH as P,cI as V,bh as W,__tla as H}from"./index-BUSn51wb.js";let j,K=Promise.all([(()=>{try{return H}catch{}})()]).then(async()=>{const E=z({name:"ElSpaceItem",props:$({prefixCls:{type:String}}),setup(e,{slots:S}){const b=L("space"),g=y(()=>`${e.prefixCls||b.b()}__item`);return()=>R("div",{class:g.value},T(S,"default"))}}),C={small:8,default:12,large:16};let _;_=z({name:"ElSpace",props:$({direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},class:{type:v([String,Object,Array]),default:""},style:{type:v([String,Array,Object]),default:""},alignment:{type:v(String),default:"center"},prefixCls:{type:String},spacer:{type:v([Object,String,Number,Array]),default:null,validator:e=>O(e)||h(e)||I(e)},wrap:Boolean,fill:Boolean,fillRatio:{type:Number,default:100},size:{type:[String,Array,Number],values:N,validator:e=>h(e)||f(e)&&e.length===2&&e.every(h)}}),setup(e,{slots:S}){const{classes:b,containerStyle:g,itemStyle:x}=function(a){const s=L("space"),n=y(()=>[s.b(),s.m(a.direction),a.class]),t=A(0),l=A(0),o=y(()=>[a.wrap||a.fill?{flexWrap:"wrap"}:{},{alignItems:a.alignment},{rowGap:`${l.value}px`,columnGap:`${t.value}px`},a.style]),i=y(()=>a.fill?{flexGrow:1,minWidth:`${a.fillRatio}%`}:{});return Y(()=>{const{size:r="small",wrap:d,direction:p,fill:k}=a;if(f(r)){const[u=0,G=0]=r;t.value=u,l.value=G}else{let u;u=h(r)?r:C[r||"small"]||C.small,(d||k)&&p==="horizontal"?t.value=l.value=u:p==="horizontal"?(t.value=u,l.value=0):(l.value=u,t.value=0)}}),{classes:n,containerStyle:o,itemStyle:i}}(e);function w(a,s="",n=[]){const{prefixCls:t}=e;return a.forEach((l,o)=>{P(l)?f(l.children)&&l.children.forEach((i,r)=>{P(i)&&f(i.children)?w(i.children,`${s+r}-`,n):n.push(m(E,{style:x.value,prefixCls:t,key:`nested-${s+r}`},{default:()=>[i]},c.PROPS|c.STYLE,["style","prefixCls"]))}):V(l)&&n.push(m(E,{style:x.value,prefixCls:t,key:`LoopKey${s+o}`},{default:()=>[l]},c.PROPS|c.STYLE,["style","prefixCls"]))}),n}return()=>{var a;const{spacer:s,direction:n}=e,t=T(S,"default",{key:0},()=>[]);if(((a=t.children)!=null?a:[]).length===0)return null;if(f(t.children)){let l=w(t.children);if(s){const o=l.length-1;l=l.reduce((i,r,d)=>{const p=[...i,r];return d!==o&&p.push(m("span",{style:[x.value,n==="vertical"?"width: 100%":null],key:d},[O(s)?s:B(s,c.TEXT)],c.STYLE)),p},[])}return m("div",{class:b.value,style:g.value},l,c.STYLE|c.CLASS)}return t.children}}}),j=W(_)});export{j as E,K as __tla};
