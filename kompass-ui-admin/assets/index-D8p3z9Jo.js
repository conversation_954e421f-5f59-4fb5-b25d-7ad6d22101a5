import{d as B,r as a,at as w,C as z,o as p,l as C,w as L,g as v,av as s,a as l,c as i,F as j,k as I,i as b,a9 as x,a0 as P,t as g,b2 as U,__tla as E}from"./index-BUSn51wb.js";import{E as F,__tla as G}from"./el-image-BjHZRFih.js";import{b as S,__tla as W}from"./spu-CW3JGweV.js";let R,q=Promise.all([(()=>{try{return E}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return W}catch{}})()]).then(async()=>{let m;m={key:0,class:"absolute left-0 top-0 z-1 items-center justify-center"},R=B({name:"ProductList",__name:"index",props:{property:{}},setup($){const t=$,o=a([]);w(()=>t.property.spuIds,async()=>{o.value=await S(t.property.spuIds)},{immediate:!0,deep:!0});const u=a(375),h=a(),r=a(2),c=a("100%"),d=a("0"),y=a("");return w(()=>[t.property,u,o.value.length],()=>{r.value=t.property.layoutType==="twoCol"?2:3;const e=(u.value-t.property.space*(r.value-1))/r.value;d.value=r.value===2?"64px":`${e}px`,t.property.layoutType==="horizSwiper"?(y.value=`repeat(auto-fill, ${e}px)`,c.value=e*o.value.length+t.property.space*(o.value.length-1)+"px"):(y.value=`repeat(${r.value}, auto)`,c.value="100%")},{immediate:!0,deep:!0}),z(()=>{var e,n;u.value=((n=(e=h.value)==null?void 0:e.wrapRef)==null?void 0:n.offsetWidth)||375}),(e,n)=>{const _=F,T=U;return p(),C(T,{class:"z-1 min-h-30px","wrap-class":"w-full",ref_key:"containerRef",ref:h},{default:L(()=>[v("div",{class:"grid overflow-x-auto",style:s({gridGap:`${e.property.space}px`,gridTemplateColumns:l(y),width:l(c)})},[(p(!0),i(j,null,I(l(o),(f,k)=>(p(),i("div",{class:"relative box-content flex flex-row flex-wrap overflow-hidden bg-white",style:s({borderTopLeftRadius:`${e.property.borderRadiusTop}px`,borderTopRightRadius:`${e.property.borderRadiusTop}px`,borderBottomLeftRadius:`${e.property.borderRadiusBottom}px`,borderBottomRightRadius:`${e.property.borderRadiusBottom}px`}),key:k},[e.property.badge.show?(p(),i("div",m,[b(_,{fit:"cover",src:e.property.badge.imgUrl,class:"h-26px w-38px"},null,8,["src"])])):x("",!0),b(_,{fit:"cover",src:f.picUrl,style:s({width:l(d),height:l(d)})},null,8,["src","style"]),v("div",{class:P(["flex flex-col gap-8px p-8px box-border",{"w-[calc(100%-64px)]":l(r)===2,"w-full":l(r)===3}])},[e.property.fields.name.show?(p(),i("div",{key:0,class:"truncate text-12px",style:s({color:e.property.fields.name.color})},g(f.name),5)):x("",!0),v("div",null,[e.property.fields.price.show?(p(),i("span",{key:0,class:"text-12px",style:s({color:e.property.fields.price.color})}," \uFFE5"+g(f.price),5)):x("",!0)])],2)],4))),128))],4)]),_:1},512)}}})});export{q as __tla,R as default};
