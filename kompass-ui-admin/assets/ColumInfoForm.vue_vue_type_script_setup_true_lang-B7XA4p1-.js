import{d as h,r as s,at as y,C as g,o as i,l as V,w as t,i as l,c as T,k as O,a as w,F as E,P as C,Z as I,J as x,K as B,ai as D,Q as L,__tla as j}from"./index-BUSn51wb.js";import{g as F,__tla as k}from"./dict.type-7eDXjvul.js";let f,K=Promise.all([(()=>{try{return j}catch{}})(),(()=>{try{return k}catch{}})()]).then(async()=>{f=h({name:"InfraCodegenColumInfoForm",__name:"ColumInfoForm",props:{columns:{type:Array,default:()=>null}},setup(v){const c=v,p=s([]),_=document.documentElement.scrollHeight-350+"px",b=s();return y(()=>c.columns,m=>{m&&(p.value=m)},{deep:!0,immediate:!0}),g(async()=>{await(async()=>{b.value=await F()})()}),(m,N)=>{const u=C,r=I,o=x,n=B,d=D,U=L;return i(),V(U,{ref:"dragTable",data:w(p),"max-height":_,"row-key":"columnId"},{default:t(()=>[l(u,{"show-overflow-tooltip":!0,label:"\u5B57\u6BB5\u5217\u540D","min-width":"10%",prop:"columnName"}),l(u,{label:"\u5B57\u6BB5\u63CF\u8FF0","min-width":"10%"},{default:t(e=>[l(r,{modelValue:e.row.columnComment,"onUpdate:modelValue":a=>e.row.columnComment=a},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(u,{"show-overflow-tooltip":!0,label:"\u7269\u7406\u7C7B\u578B","min-width":"10%",prop:"dataType"}),l(u,{label:"Java\u7C7B\u578B","min-width":"11%"},{default:t(e=>[l(n,{modelValue:e.row.javaType,"onUpdate:modelValue":a=>e.row.javaType=a},{default:t(()=>[l(o,{label:"Long",value:"Long"}),l(o,{label:"String",value:"String"}),l(o,{label:"Integer",value:"Integer"}),l(o,{label:"Double",value:"Double"}),l(o,{label:"BigDecimal",value:"BigDecimal"}),l(o,{label:"LocalDateTime",value:"LocalDateTime"}),l(o,{label:"Boolean",value:"Boolean"})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),l(u,{label:"java\u5C5E\u6027","min-width":"10%"},{default:t(e=>[l(r,{modelValue:e.row.javaField,"onUpdate:modelValue":a=>e.row.javaField=a},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(u,{label:"\u63D2\u5165","min-width":"4%"},{default:t(e=>[l(d,{modelValue:e.row.createOperation,"onUpdate:modelValue":a=>e.row.createOperation=a,"false-label":"false","true-label":"true"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(u,{label:"\u7F16\u8F91","min-width":"4%"},{default:t(e=>[l(d,{modelValue:e.row.updateOperation,"onUpdate:modelValue":a=>e.row.updateOperation=a,"false-label":"false","true-label":"true"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(u,{label:"\u5217\u8868","min-width":"4%"},{default:t(e=>[l(d,{modelValue:e.row.listOperationResult,"onUpdate:modelValue":a=>e.row.listOperationResult=a,"false-label":"false","true-label":"true"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(u,{label:"\u67E5\u8BE2","min-width":"4%"},{default:t(e=>[l(d,{modelValue:e.row.listOperation,"onUpdate:modelValue":a=>e.row.listOperation=a,"false-label":"false","true-label":"true"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(u,{label:"\u67E5\u8BE2\u65B9\u5F0F","min-width":"10%"},{default:t(e=>[l(n,{modelValue:e.row.listOperationCondition,"onUpdate:modelValue":a=>e.row.listOperationCondition=a},{default:t(()=>[l(o,{label:"=",value:"="}),l(o,{label:"!=",value:"!="}),l(o,{label:">",value:">"}),l(o,{label:">=",value:">="}),l(o,{label:"<",value:"<>"}),l(o,{label:"<=",value:"<="}),l(o,{label:"LIKE",value:"LIKE"}),l(o,{label:"BETWEEN",value:"BETWEEN"})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),l(u,{label:"\u5141\u8BB8\u7A7A","min-width":"5%"},{default:t(e=>[l(d,{modelValue:e.row.nullable,"onUpdate:modelValue":a=>e.row.nullable=a,"false-label":"false","true-label":"true"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),l(u,{label:"\u663E\u793A\u7C7B\u578B","min-width":"12%"},{default:t(e=>[l(n,{modelValue:e.row.htmlType,"onUpdate:modelValue":a=>e.row.htmlType=a},{default:t(()=>[l(o,{label:"\u6587\u672C\u6846",value:"input"}),l(o,{label:"\u6587\u672C\u57DF",value:"textarea"}),l(o,{label:"\u4E0B\u62C9\u6846",value:"select"}),l(o,{label:"\u5355\u9009\u6846",value:"radio"}),l(o,{label:"\u590D\u9009\u6846",value:"checkbox"}),l(o,{label:"\u65E5\u671F\u63A7\u4EF6",value:"datetime"}),l(o,{label:"\u56FE\u7247\u4E0A\u4F20",value:"imageUpload"}),l(o,{label:"\u6587\u4EF6\u4E0A\u4F20",value:"fileUpload"}),l(o,{label:"\u5BCC\u6587\u672C\u63A7\u4EF6",value:"editor"})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),l(u,{label:"\u5B57\u5178\u7C7B\u578B","min-width":"12%"},{default:t(e=>[l(n,{modelValue:e.row.dictType,"onUpdate:modelValue":a=>e.row.dictType=a,clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9"},{default:t(()=>[(i(!0),T(E,null,O(w(b),a=>(i(),V(o,{key:a.id,label:a.name,value:a.type},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),l(u,{label:"\u793A\u4F8B","min-width":"10%"},{default:t(e=>[l(r,{modelValue:e.row.example,"onUpdate:modelValue":a=>e.row.example=a},null,8,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1},8,["data"])}}})});export{f as _,K as __tla};
