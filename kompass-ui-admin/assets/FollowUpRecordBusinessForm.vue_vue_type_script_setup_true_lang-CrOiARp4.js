import{d,r as _,at as f,o as g,l as b,w as l,i as e,a as n,dV as h,j as v,P as w,N as y,Q as x,__tla as N}from"./index-BUSn51wb.js";let o,k=Promise.all([(()=>{try{return N}catch{}})()]).then(async()=>{o=d({__name:"FollowUpRecordBusinessForm",props:{businesses:{}},setup(i){const p=i,t=_([]);return f(()=>p.businesses,async s=>{t.value=s},{immediate:!0}),(s,P)=>{const a=w,c=y,u=x;return g(),b(u,{data:n(t),"show-overflow-tooltip":!0,stripe:!0,height:"120"},{default:l(()=>[e(a,{label:"\u5546\u673A\u540D\u79F0",fixed:"left",align:"center",prop:"name"}),e(a,{label:"\u5546\u673A\u91D1\u989D",align:"center",prop:"totalPrice",formatter:n(h)},null,8,["formatter"]),e(a,{label:"\u5BA2\u6237\u540D\u79F0",align:"center",prop:"customerName"}),e(a,{label:"\u5546\u673A\u7EC4",align:"center",prop:"statusTypeName"}),e(a,{label:"\u5546\u673A\u9636\u6BB5",align:"center",prop:"statusName"}),e(a,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"80"},{default:l(({$index:m})=>[e(c,{link:"",type:"danger",onClick:C=>{return r=m,void t.value.splice(r,1);var r}},{default:l(()=>[v(" \u79FB\u9664")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])}}})});export{o as _,k as __tla};
