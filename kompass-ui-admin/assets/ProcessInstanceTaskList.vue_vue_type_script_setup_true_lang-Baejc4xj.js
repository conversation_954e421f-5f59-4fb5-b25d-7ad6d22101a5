import{d as ra,p as b,r as w,au as oa,o as t,c as i,H as ia,l as y,w as e,i as l,a as s,y as ca,F as $,R as ua,g as _,j as m,t as n,G as q,a9 as r,k as _a,aA as ma,ay as pa,_ as fa,N as ya,ax as da,E as ha,__tla as ga}from"./index-BUSn51wb.js";import{_ as ka,__tla as ba}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{E as wa,a as Ta,__tla as va}from"./el-timeline-item-D8aDRTsd.js";import{E as Ia,__tla as Sa}from"./el-card-CJbXGyyg.js";import{_ as xa,__tla as Ua}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{f as T,a as Ca,__tla as Va}from"./formatTime-DWdBpgsM.js";import{_ as Aa,__tla as Pa}from"./TaskSignList.vue_vue_type_script_setup_true_lang-BoJw9trq.js";import{b as Ea,__tla as Ma}from"./formCreate-DDLxm5B5.js";let D,Ba=Promise.all([(()=>{try{return ga}catch{}})(),(()=>{try{return ba}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return Sa}catch{}})(),(()=>{try{return Ua}catch{}})(),(()=>{try{return Va}catch{}})(),(()=>{try{return Pa}catch{}})(),(()=>{try{return Ma}catch{}})()]).then(async()=>{let I,S,x,U,C,V,A,P,E,M,B,N,j;I=_("span",{class:"el-icon-picture-outline"},"\u5BA1\u6279\u8BB0\u5F55",-1),S={class:"block"},x={style:{"font-weight":"700"}},U={style:{"font-weight":"700"}},C={key:0,style:{"margin-right":"30px","font-weight":"normal"}},V={key:1,style:{"font-weight":"normal"}},A={style:{"font-weight":"normal",color:"#8a909c"}},P={key:2,style:{"margin-left":"30px","font-weight":"normal"}},E={key:3,style:{"font-weight":"normal",color:"#8a909c"}},M={key:4,style:{"margin-left":"30px","font-weight":"normal"}},B={key:5,style:{"font-weight":"normal",color:"#8a909c"}},N={key:6},j={style:{"font-weight":"700"}},D=ra({name:"BpmProcessInstanceTaskList",__name:"ProcessInstanceTaskList",props:{loading:b.bool,processInstance:b.object,tasks:b.arrayOf(b.object)},emits:["refresh"],setup(o,{emit:Q}){const W=u=>[0,1,6,7].includes(u.status)?"primary":u.status===2?"success":u.status===3?"danger":u.status===4?"info":u.status===5?"warning":"",z=w(),d=w(),p=w({rule:[],option:{},value:{}}),h=w(!1),X=Q,Y=()=>{X("refresh")};return(u,g)=>{const L=xa,v=wa,R=fa,F=ya,Z=da,O=Ia,aa=Ta,ta=ha,sa=oa("form-create"),ea=ka,la=ua;return t(),i($,null,[ia((t(),y(O,{class:"box-card"},{header:e(()=>[I]),default:e(()=>[l(ta,{offset:3,span:17},{default:e(()=>[_("div",S,[l(aa,null,{default:e(()=>{return[o.processInstance.endTime?(t(),y(v,{key:0,type:(c=o.processInstance,c.status===2?"success":c.status===3?"danger":c.status===4?"warning":"")},{default:e(()=>{var a;return[_("p",x,[m(" \u7ED3\u675F\u6D41\u7A0B\uFF1A\u5728 "+n(s(T)((a=o.processInstance)==null?void 0:a.endTime))+" \u7ED3\u675F ",1),l(L,{type:s(q).BPM_PROCESS_INSTANCE_STATUS,value:o.processInstance.status},null,8,["type","value"])])]}),_:1},8,["type"])):r("",!0),(t(!0),i($,null,_a(o.tasks,(a,k)=>(t(),y(v,{key:k,type:W(a)},{default:e(()=>[_("p",U,[m(" \u5BA1\u6279\u4EFB\u52A1\uFF1A"+n(a.name)+" ",1),l(L,{type:s(q).BPM_TASK_STATUS,value:a.status},null,8,["type","value"]),s(ma)(a.children)?r("",!0):(t(),y(F,{key:0,class:"ml-10px",onClick:na=>(f=>{z.value.open(f)})(a),size:"small"},{default:e(()=>[l(R,{icon:"ep:memo"}),m(" \u5B50\u4EFB\u52A1 ")]),_:2},1032,["onClick"])),a.formId>0?(t(),y(F,{key:1,class:"ml-10px",size:"small",onClick:na=>(async f=>{var G,H,J,K;Ea(p,f.formConf,f.formFields,f.formVariables),h.value=!0,await pa(),d.value.fapi.btn.show(!1),(H=(G=d.value)==null?void 0:G.fapi)==null||H.resetBtn.show(!1),(K=(J=d.value)==null?void 0:J.fapi)==null||K.disabled(!0)})(a)},{default:e(()=>[l(R,{icon:"ep:document"}),m(" \u67E5\u770B\u8868\u5355 ")]),_:2},1032,["onClick"])):r("",!0)]),l(O,{"body-style":{padding:"10px"}},{default:e(()=>[a.assigneeUser?(t(),i("label",C,[m(" \u5BA1\u6279\u4EBA\uFF1A"+n(a.assigneeUser.nickname)+" ",1),l(Z,{size:"small",type:"info"},{default:e(()=>[m(n(a.assigneeUser.deptName),1)]),_:2},1024)])):r("",!0),a.createTime?(t(),i("label",V,"\u521B\u5EFA\u65F6\u95F4\uFF1A")):r("",!0),_("label",A,n(s(T)(a==null?void 0:a.createTime)),1),a.endTime?(t(),i("label",P," \u5BA1\u6279\u65F6\u95F4\uFF1A ")):r("",!0),a.endTime?(t(),i("label",E,n(s(T)(a==null?void 0:a.endTime)),1)):r("",!0),a.durationInMillis?(t(),i("label",M," \u8017\u65F6\uFF1A ")):r("",!0),a.durationInMillis?(t(),i("label",B,n(s(Ca)(a==null?void 0:a.durationInMillis)),1)):r("",!0),a.reason?(t(),i("p",N," \u5BA1\u6279\u5EFA\u8BAE\uFF1A"+n(a.reason),1)):r("",!0)]),_:2},1024)]),_:2},1032,["type"]))),128)),l(v,{type:"success"},{default:e(()=>{var a,k;return[_("p",j," \u53D1\u8D77\u6D41\u7A0B\uFF1A\u3010"+n((a=o.processInstance.startUser)==null?void 0:a.nickname)+"\u3011\u5728 "+n(s(T)((k=o.processInstance)==null?void 0:k.startTime))+" \u53D1\u8D77\u3010 "+n(o.processInstance.name)+" \u3011\u6D41\u7A0B ",1)]}),_:1})];var c}),_:1})])]),_:1})]),_:1})),[[la,o.loading]]),l(Aa,{ref_key:"taskSignListRef",ref:z,onSuccess:Y},null,512),l(ea,{title:"\u8868\u5355\u8BE6\u60C5",modelValue:s(h),"onUpdate:modelValue":g[1]||(g[1]=c=>ca(h)?h.value=c:null),width:"600"},{default:e(()=>[l(sa,{ref_key:"fApi",ref:d,modelValue:s(p).value,"onUpdate:modelValue":g[0]||(g[0]=c=>s(p).value=c),option:s(p).option,rule:s(p).rule},null,8,["modelValue","option","rule"])]),_:1},8,["modelValue"])],64)}}})});export{D as _,Ba as __tla};
