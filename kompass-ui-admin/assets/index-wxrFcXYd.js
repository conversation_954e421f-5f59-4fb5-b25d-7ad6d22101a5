import{d as H,u as L,S as O,r as _,C as q,H as G,a,o as S,l as E,w as s,i as l,y as f,j as I,a9 as J,n as K,I as M,aB as k,aC as P,aD as Q,aE as h,z as W,A as X,N as Y,L as Z,O as $,R as aa,__tla as ra}from"./index-BUSn51wb.js";import{_ as ta,__tla as ea}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{u as ia,__tla as la}from"./tagsView-BOOrxb3Q.js";import{g as oa,c as ca,u as sa,__tla as _a}from"./spu-CW3JGweV.js";import{_ as ma,__tla as na}from"./InfoForm.vue_vue_type_script_setup_true_lang-DK__qetF.js";import{_ as ua,__tla as pa}from"./DescriptionForm.vue_vue_type_script_setup_true_lang-K_Jc4edl.js";import{_ as da,__tla as fa}from"./OtherForm.vue_vue_type_script_setup_true_lang-DPZDZvGo.js";import{_ as ya,__tla as va}from"./SkuForm.vue_vue_type_script_setup_true_lang-I0g2U3dN.js";import{_ as ka,__tla as Pa}from"./DeliveryForm.vue_vue_type_script_setup_true_lang-B4qY9ker.js";import{__tla as ha}from"./el-card-CJbXGyyg.js";import"./tree-BMa075Oj.js";import{__tla as ga}from"./category-WzWM3ODe.js";import{__tla as Na}from"./brand-DwnGZI23.js";import{__tla as Ba}from"./formRules-CA9eXdcX.js";import{__tla as wa}from"./index-CjyLHUq3.js";import{__tla as Da}from"./SkuList-DG93D6KA.js";import{__tla as Fa}from"./el-image-BjHZRFih.js";import{__tla as Ua}from"./ProductAttributes.vue_vue_type_script_setup_true_lang-HWrQWq3l.js";import{__tla as ba}from"./el-text-CIwNlU-U.js";import{__tla as Ca}from"./property-BdOytbZT.js";import{__tla as Ra}from"./ProductPropertyAddForm.vue_vue_type_script_setup_true_lang-BR4HNBkA.js";import{__tla as Sa}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import"./constants-A8BI3pz7.js";let T,Ea=Promise.all([(()=>{try{return ra}catch{}})(),(()=>{try{return ea}catch{}})(),(()=>{try{return la}catch{}})(),(()=>{try{return _a}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return pa}catch{}})(),(()=>{try{return fa}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return Pa}catch{}})(),(()=>{try{return ha}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return Na}catch{}})(),(()=>{try{return Ba}catch{}})(),(()=>{try{return wa}catch{}})(),(()=>{try{return Da}catch{}})(),(()=>{try{return Fa}catch{}})(),(()=>{try{return Ua}catch{}})(),(()=>{try{return ba}catch{}})(),(()=>{try{return Ca}catch{}})(),(()=>{try{return Ra}catch{}})(),(()=>{try{return Sa}catch{}})()]).then(async()=>{T=H({name:"ProductSpuForm",__name:"index",setup(Ia){const{t:N}=K(),B=M(),{push:V,currentRoute:j}=L(),{params:w,name:x}=O(),{delView:z}=ia(),p=_(!1),t=_("info"),m=_(!1),D=_(),F=_(),U=_(),b=_(),C=_(),n=_({name:"",categoryId:void 0,keyword:"",picUrl:"",sliderPicUrls:[],introduction:"",deliveryTypes:[],deliveryTemplateId:void 0,brandId:void 0,specType:!1,subCommissionType:!1,skus:[{price:0,marketPrice:0,costPrice:0,barCode:"",picUrl:"",stock:0,weight:0,volume:0,firstBrokeragePrice:0,secondBrokeragePrice:0}],description:"",sort:0,giveIntegral:0,virtualSalesCount:0}),A=async()=>{var d,e,o,r,y;p.value=!0;try{await((d=a(D))==null?void 0:d.validate()),await((e=a(F))==null?void 0:e.validate()),await((o=a(U))==null?void 0:o.validate()),await((r=a(b))==null?void 0:r.validate()),await((y=a(C))==null?void 0:y.validate());const u=Q(a(n.value));u.skus.forEach(i=>{i.name=u.name,i.price=h(i.price),i.marketPrice=h(i.marketPrice),i.costPrice=h(i.costPrice),i.firstBrokeragePrice=h(i.firstBrokeragePrice),i.secondBrokeragePrice=h(i.secondBrokeragePrice)});const v=[];u.sliderPicUrls.forEach(i=>{typeof i=="object"?v.push(i.url):v.push(i)}),u.sliderPicUrls=v;const g=u;w.id?(await sa(g),B.success(N("common.updateSuccess"))):(await ca(g),B.success(N("common.createSuccess"))),R()}finally{p.value=!1}},R=()=>{z(a(j)),V({name:"ProductSpu"})};return q(async()=>{await(async()=>{var e;x==="ProductSpuDetail"&&(m.value=!0);const d=w.id;if(d){p.value=!0;try{const o=await oa(d);(e=o.skus)==null||e.forEach(r=>{m.value?(r.price=k(r.price),r.marketPrice=k(r.marketPrice),r.costPrice=k(r.costPrice),r.firstBrokeragePrice=k(r.firstBrokeragePrice),r.secondBrokeragePrice=k(r.secondBrokeragePrice)):(r.price=P(r.price),r.marketPrice=P(r.marketPrice),r.costPrice=P(r.costPrice),r.firstBrokeragePrice=P(r.firstBrokeragePrice),r.secondBrokeragePrice=P(r.secondBrokeragePrice))}),n.value=o}finally{p.value=!1}}})()}),(d,e)=>{const o=W,r=X,y=Y,u=Z,v=$,g=ta,i=aa;return G((S(),E(g,null,{default:s(()=>[l(r,{modelValue:a(t),"onUpdate:modelValue":e[5]||(e[5]=c=>f(t)?t.value=c:null)},{default:s(()=>[l(o,{label:"\u57FA\u7840\u8BBE\u7F6E",name:"info"},{default:s(()=>[l(ma,{ref_key:"infoRef",ref:D,activeName:a(t),"onUpdate:activeName":e[0]||(e[0]=c=>f(t)?t.value=c:null),"is-detail":a(m),propFormData:a(n)},null,8,["activeName","is-detail","propFormData"])]),_:1}),l(o,{label:"\u4EF7\u683C\u5E93\u5B58",name:"sku"},{default:s(()=>[l(ya,{ref_key:"skuRef",ref:F,activeName:a(t),"onUpdate:activeName":e[1]||(e[1]=c=>f(t)?t.value=c:null),"is-detail":a(m),propFormData:a(n)},null,8,["activeName","is-detail","propFormData"])]),_:1}),l(o,{label:"\u7269\u6D41\u8BBE\u7F6E",name:"delivery"},{default:s(()=>[l(ka,{ref_key:"deliveryRef",ref:U,activeName:a(t),"onUpdate:activeName":e[2]||(e[2]=c=>f(t)?t.value=c:null),"is-detail":a(m),propFormData:a(n)},null,8,["activeName","is-detail","propFormData"])]),_:1}),l(o,{label:"\u5546\u54C1\u8BE6\u60C5",name:"description"},{default:s(()=>[l(ua,{ref_key:"descriptionRef",ref:b,activeName:a(t),"onUpdate:activeName":e[3]||(e[3]=c=>f(t)?t.value=c:null),"is-detail":a(m),propFormData:a(n)},null,8,["activeName","is-detail","propFormData"])]),_:1}),l(o,{label:"\u5176\u5B83\u8BBE\u7F6E",name:"other"},{default:s(()=>[l(da,{ref_key:"otherRef",ref:C,activeName:a(t),"onUpdate:activeName":e[4]||(e[4]=c=>f(t)?t.value=c:null),"is-detail":a(m),propFormData:a(n)},null,8,["activeName","is-detail","propFormData"])]),_:1})]),_:1},8,["modelValue"]),l(v,null,{default:s(()=>[l(u,{style:{float:"right"}},{default:s(()=>[a(m)?J("",!0):(S(),E(y,{key:0,loading:a(p),type:"primary",onClick:A},{default:s(()=>[I(" \u4FDD\u5B58 ")]),_:1},8,["loading"])),l(y,{onClick:R},{default:s(()=>[I("\u8FD4\u56DE")]),_:1})]),_:1})]),_:1})]),_:1})),[[i,a(p)]])}}})});export{Ea as __tla,T as default};
