import{by as d,d as W,n as Z,I as G,r as u,f as J,b as K,o as V,l as b,w as t,a as e,j as T,a9 as Q,i as a,H as X,y as F,Z as Y,L as $,E as ee,M as ae,cn as le,s as te,O as se,z as ce,A as oe,N as re,R as ue,__tla as de}from"./index-BUSn51wb.js";import{_ as ie,__tla as me}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{_ as pe,__tla as ne}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as ke,__tla as _e}from"./StockCheckItemForm.vue_vue_type_script_setup_true_lang-k1XaBZSi.js";let p,I,fe=Promise.all([(()=>{try{return de}catch{}})(),(()=>{try{return me}catch{}})(),(()=>{try{return ne}catch{}})(),(()=>{try{return _e}catch{}})()]).then(async()=>{p={getStockCheckPage:async s=>await d.get({url:"/erp/stock-check/page",params:s}),getStockCheck:async s=>await d.get({url:"/erp/stock-check/get?id="+s}),createStockCheck:async s=>await d.post({url:"/erp/stock-check/create",data:s}),updateStockCheck:async s=>await d.put({url:"/erp/stock-check/update",data:s}),updateStockCheckStatus:async(s,_)=>await d.put({url:"/erp/stock-check/update-status",params:{id:s,status:_}}),deleteStockCheck:async s=>await d.delete({url:"/erp/stock-check/delete",params:{ids:s.join(",")}}),exportStockCheck:async s=>await d.download({url:"/erp/stock-check/export-excel",params:s})},I=W({name:"StockCheckForm",__name:"StockCheckForm",emits:["success"],setup(s,{expose:_,emit:R}){const{t:f}=Z(),S=G(),i=u(!1),C=u(""),m=u(!1),h=u(""),c=u({id:void 0,customerId:void 0,checkTime:void 0,remark:void 0,fileUrl:"",items:[]}),j=J({checkTime:[{required:!0,message:"\u76D8\u70B9\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),v=K(()=>h.value==="detail"),y=u(),w=u("item"),g=u();_({open:async(r,l)=>{if(i.value=!0,C.value=f("action."+r),h.value=r,q(),l){m.value=!0;try{c.value=await p.getStockCheck(l)}finally{m.value=!1}}}});const L=R,P=async()=>{await y.value.validate(),await g.value.validate(),m.value=!0;try{const r=c.value;h.value==="create"?(await p.createStockCheck(r),S.success(f("common.createSuccess"))):(await p.updateStockCheck(r),S.success(f("common.updateSuccess"))),i.value=!1,L("success")}finally{m.value=!1}},q=()=>{var r;c.value={id:void 0,customerId:void 0,checkTime:void 0,remark:void 0,fileUrl:void 0,items:[]},(r=y.value)==null||r.resetFields()};return(r,l)=>{const U=Y,n=$,k=ee,z=ae,A=le,B=te,D=se,E=ce,H=oe,M=pe,x=re,N=ie,O=ue;return V(),b(N,{title:e(C),modelValue:e(i),"onUpdate:modelValue":l[6]||(l[6]=o=>F(i)?i.value=o:null),width:"1080"},{footer:t(()=>[e(v)?Q("",!0):(V(),b(x,{key:0,onClick:P,type:"primary",disabled:e(m)},{default:t(()=>[T(" \u786E \u5B9A ")]),_:1},8,["disabled"])),a(x,{onClick:l[5]||(l[5]=o=>i.value=!1)},{default:t(()=>[T("\u53D6 \u6D88")]),_:1})]),default:t(()=>[X((V(),b(D,{ref_key:"formRef",ref:y,model:e(c),rules:e(j),"label-width":"100px",disabled:e(v)},{default:t(()=>[a(B,{gutter:20},{default:t(()=>[a(k,{span:8},{default:t(()=>[a(n,{label:"\u76D8\u70B9\u5355\u53F7",prop:"no"},{default:t(()=>[a(U,{disabled:"",modelValue:e(c).no,"onUpdate:modelValue":l[0]||(l[0]=o=>e(c).no=o),placeholder:"\u4FDD\u5B58\u65F6\u81EA\u52A8\u751F\u6210"},null,8,["modelValue"])]),_:1})]),_:1}),a(k,{span:8},{default:t(()=>[a(n,{label:"\u76D8\u70B9\u65F6\u95F4",prop:"checkTime"},{default:t(()=>[a(z,{modelValue:e(c).checkTime,"onUpdate:modelValue":l[1]||(l[1]=o=>e(c).checkTime=o),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u76D8\u70B9\u65F6\u95F4",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),a(k,{span:16},{default:t(()=>[a(n,{label:"\u5907\u6CE8",prop:"remark"},{default:t(()=>[a(U,{type:"textarea",modelValue:e(c).remark,"onUpdate:modelValue":l[2]||(l[2]=o=>e(c).remark=o),rows:1,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1}),a(k,{span:8},{default:t(()=>[a(n,{label:"\u9644\u4EF6",prop:"fileUrl"},{default:t(()=>[a(A,{"is-show-tip":!1,modelValue:e(c).fileUrl,"onUpdate:modelValue":l[3]||(l[3]=o=>e(c).fileUrl=o),limit:1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules","disabled"])),[[O,e(m)]]),a(M,null,{default:t(()=>[a(H,{modelValue:e(w),"onUpdate:modelValue":l[4]||(l[4]=o=>F(w)?w.value=o:null),class:"-mt-15px -mb-10px"},{default:t(()=>[a(E,{label:"\u76D8\u70B9\u4EA7\u54C1\u6E05\u5355",name:"item"},{default:t(()=>[a(ke,{ref_key:"itemFormRef",ref:g,items:e(c).items,disabled:e(v)},null,8,["items","disabled"])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["title","modelValue"])}}})});export{p as S,I as _,fe as __tla};
