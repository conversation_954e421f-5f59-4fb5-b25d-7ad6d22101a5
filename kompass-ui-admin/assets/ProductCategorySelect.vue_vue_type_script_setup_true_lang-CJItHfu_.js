import{d as _,b4 as i,p as u,b as y,r as h,C as b,o as V,l as f,a as l,y as g,__tla as w}from"./index-BUSn51wb.js";import{E as I,__tla as k}from"./el-tree-select-CBuha0HW.js";import{h as v,d as x}from"./tree-BMa075Oj.js";import{g as C,__tla as P}from"./category-WzWM3ODe.js";let d,S=Promise.all([(()=>{try{return w}catch{}})(),(()=>{try{return k}catch{}})(),(()=>{try{return P}catch{}})()]).then(async()=>{d=_({name:"ProductCategorySelect",__name:"ProductCategorySelect",props:{modelValue:i([Number,Array]),multiple:u.bool.def(!1),parentId:u.number.def(void 0)},emits:["update:modelValue"],setup(e,{emit:m}){const r=e,t=y({get:()=>r.modelValue,set:a=>{p("update:modelValue",a)}}),p=m,o=h([]);return b(async()=>{const a=await C({parentId:r.parentId});o.value=v(a,"id","parentId")}),(a,s)=>{const n=I;return V(),f(n,{modelValue:l(t),"onUpdate:modelValue":s[0]||(s[0]=c=>g(t)?t.value=c:null),data:l(o),props:l(x),multiple:e.multiple,"show-checkbox":e.multiple,class:"w-1/1","node-key":"id",placeholder:"\u8BF7\u9009\u62E9\u5546\u54C1\u5206\u7C7B"},null,8,["modelValue","data","props","multiple","show-checkbox"])}}})});export{d as _,S as __tla};
