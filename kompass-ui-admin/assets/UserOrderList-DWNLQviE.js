import{_ as t,__tla as r}from"./UserOrderList.vue_vue_type_script_setup_true_lang-DM-W4bRi.js";import{__tla as _}from"./index-BUSn51wb.js";import{__tla as a}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{__tla as l}from"./index-Cch5e1V0.js";import{__tla as o}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{__tla as m}from"./el-card-CJbXGyyg.js";import{__tla as c}from"./index-BQq32Shw.js";import{__tla as e}from"./index-BmYfnmm4.js";import{__tla as s}from"./index-H6D82e8c.js";import{__tla as i}from"./OrderTableColumn-AjIStixC.js";import{__tla as p}from"./el-image-BjHZRFih.js";import{__tla as n}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import"./color-BN7ZL7BD.js";import"./constants-A8BI3pz7.js";import{__tla as f}from"./formatTime-DWdBpgsM.js";let h=Promise.all([(()=>{try{return r}catch{}})(),(()=>{try{return _}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return m}catch{}})(),(()=>{try{return c}catch{}})(),(()=>{try{return e}catch{}})(),(()=>{try{return s}catch{}})(),(()=>{try{return i}catch{}})(),(()=>{try{return p}catch{}})(),(()=>{try{return n}catch{}})(),(()=>{try{return f}catch{}})()]).then(async()=>{});export{h as __tla,t as default};
