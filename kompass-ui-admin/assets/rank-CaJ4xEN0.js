import{by as a,__tla as e}from"./index-BUSn51wb.js";let r,s=Promise.all([(()=>{try{return e}catch{}})()]).then(async()=>{r={getContractPriceRank:t=>a.get({url:"/crm/statistics-rank/get-contract-price-rank",params:t}),getReceivablePriceRank:t=>a.get({url:"/crm/statistics-rank/get-receivable-price-rank",params:t}),getContractCountRank:t=>a.get({url:"/crm/statistics-rank/get-contract-count-rank",params:t}),getProductSalesRank:t=>a.get({url:"/crm/statistics-rank/get-product-sales-rank",params:t}),getCustomerCountRank:t=>a.get({url:"/crm/statistics-rank/get-customer-count-rank",params:t}),getContactsCountRank:t=>a.get({url:"/crm/statistics-rank/get-contacts-count-rank",params:t}),getFollowCountRank:t=>a.get({url:"/crm/statistics-rank/get-follow-count-rank",params:t}),getFollowCustomerCountRank:t=>a.get({url:"/crm/statistics-rank/get-follow-customer-count-rank",params:t})}});export{r as S,s as __tla};
