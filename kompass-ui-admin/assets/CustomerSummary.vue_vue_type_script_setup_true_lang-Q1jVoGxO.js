import{d as I,r as g,f as D,C as j,o as x,c as q,i as a,w as l,a as r,H as B,l as E,j as m,t as u,ej as d,dV as f,F as L,P as U,Q as F,R as H,__tla as N}from"./index-BUSn51wb.js";import{E as Q,__tla as R}from"./el-card-CJbXGyyg.js";import{E as V,__tla as X}from"./el-skeleton-item-tDN8U6BH.js";import{_ as Z,__tla as k}from"./Echart.vue_vue_type_script_setup_true_lang-DnLZLH3w.js";import{S as w,__tla as z}from"./customer-DXRFD9ec.js";let b,G=Promise.all([(()=>{try{return N}catch{}})(),(()=>{try{return R}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return k}catch{}})(),(()=>{try{return z}catch{}})()]).then(async()=>{b=I({name:"CustomerSummary",__name:"CustomerSummary",props:{queryParams:{}},setup(C,{expose:v}){const c=C,n=g(!1),p=g([]),t=D({grid:{left:20,right:30,bottom:20,containLabel:!0},legend:{},series:[{name:"\u65B0\u589E\u5BA2\u6237\u6570",type:"bar",yAxisIndex:0,data:[]},{name:"\u6210\u4EA4\u5BA2\u6237\u6570",type:"bar",yAxisIndex:1,data:[]}],toolbox:{feature:{dataZoom:{xAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u5BA2\u6237\u603B\u91CF\u5206\u6790"}}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},yAxis:[{type:"value",name:"\u65B0\u589E\u5BA2\u6237\u6570",min:0,minInterval:1},{type:"value",name:"\u6210\u4EA4\u5BA2\u6237\u6570",min:0,minInterval:1,splitLine:{lineStyle:{type:"dotted",opacity:.7}}}],xAxis:{type:"category",name:"\u65E5\u671F",data:[]}}),_=async()=>{n.value=!0;try{await(async()=>{const o=await w.getCustomerSummaryByDate(c.queryParams),y=await w.getCustomerSummaryByUser(c.queryParams);t.xAxis&&t.xAxis.data&&(t.xAxis.data=o.map(s=>s.time)),t.series&&t.series[0]&&t.series[0].data&&(t.series[0].data=o.map(s=>s.customerCreateCount)),t.series&&t.series[1]&&t.series[1].data&&(t.series[1].data=o.map(s=>s.customerDealCount)),p.value=y})()}finally{n.value=!1}};return v({loadData:_}),j(()=>{_()}),(o,y)=>{const s=Z,P=V,h=Q,e=U,A=F,S=H;return x(),q(L,null,[a(h,{shadow:"never"},{default:l(()=>[a(P,{loading:r(n),animated:""},{default:l(()=>[a(s,{height:500,options:r(t)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),a(h,{shadow:"never",class:"mt-16px"},{default:l(()=>[B((x(),E(A,{data:r(p)},{default:l(()=>[a(e,{label:"\u5E8F\u53F7",align:"center",type:"index",width:"80",fixed:"left"}),a(e,{label:"\u5458\u5DE5\u59D3\u540D",prop:"ownerUserName","min-width":"100",fixed:"left"}),a(e,{label:"\u65B0\u589E\u5BA2\u6237\u6570",align:"right",prop:"customerCreateCount","min-width":"200"}),a(e,{label:"\u6210\u4EA4\u5BA2\u6237\u6570",align:"right",prop:"customerDealCount","min-width":"200"}),a(e,{label:"\u5BA2\u6237\u6210\u4EA4\u7387(%)",align:"right","min-width":"200"},{default:l(i=>[m(u(r(d)(i.row.customerDealCount,i.row.customerCreateCount)),1)]),_:1}),a(e,{label:"\u5408\u540C\u603B\u91D1\u989D",align:"right",prop:"contractPrice","min-width":"200",formatter:r(f)},null,8,["formatter"]),a(e,{label:"\u56DE\u6B3E\u91D1\u989D",align:"right",prop:"receivablePrice","min-width":"200",formatter:r(f)},null,8,["formatter"]),a(e,{label:"\u672A\u56DE\u6B3E\u91D1\u989D",align:"right","min-width":"200"},{default:l(i=>[m(u(r(d)(i.row.receivablePrice,i.row.contractPrice)),1)]),_:1}),a(e,{label:"\u56DE\u6B3E\u5B8C\u6210\u7387(%)",align:"right","min-width":"200",fixed:"right"},{default:l(i=>[m(u(r(d)(i.row.receivablePrice,i.row.contractPrice)),1)]),_:1})]),_:1},8,["data"])),[[S,r(n)]])]),_:1})],64)}}})});export{b as _,G as __tla};
