import{d as i,p as u,r as e,C as _,H as d,a as s,o as f,c as h,g as p,av as g,R as y,__tla as v}from"./index-BUSn51wb.js";let l,x=Promise.all([(()=>{try{return v}catch{}})()]).then(async()=>{let a;a=["src"],l=i({name:"IFrame",__name:"IFrame",props:{src:u.string.def("")},setup(n){const c=n,t=e(!0),r=e(""),m=e(null);return _(()=>{setTimeout(()=>{r.value=document.documentElement.clientHeight-94.5+"px",t.value=!1},300)}),(F,H)=>{const o=y;return d((f(),h("div",{style:g("height:"+s(r))},[p("iframe",{ref_key:"frameRef",ref:m,src:c.src,frameborder:"no",scrolling:"auto",style:{width:"100%",height:"100%"}},null,8,a)],4)),[[o,s(t)]])}}})});export{l as _,x as __tla};
