import{d as J,n as K,I as L,r as m,f as M,o as i,l as _,w as s,i as r,a as e,j as y,H as N,c as O,F as S,k as D,y as P,Z,L as q,J as z,K as A,O as B,N as E,R as G,__tla as Q}from"./index-BUSn51wb.js";import{_ as T,__tla as W}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{b as X,__tla as Y}from"./index-CTCcbwMi.js";import{g as $,u as aa,__tla as ea}from"./index-eNSu58Pd.js";let k,la=Promise.all([(()=>{try{return Q}catch{}})(),(()=>{try{return W}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return ea}catch{}})()]).then(async()=>{k=J({name:"MpUserForm",__name:"UserForm",emits:["success"],setup(ta,{expose:V,emit:b}){const{t:h}=K(),g=L(),u=m(!1),d=m(!1),l=m({id:void 0,nickname:void 0,remark:void 0,tagIds:[]}),I=M({}),n=m(),p=m([]);V({open:async o=>{if(u.value=!0,F(),o){d.value=!0;try{l.value=await $(o)}finally{d.value=!1}}p.value=await X()}});const w=b,U=async()=>{if(n&&await n.value.validate()){d.value=!0;try{await aa(l.value),g.success(h("common.updateSuccess")),u.value=!1,w("success")}finally{d.value=!1}}},F=()=>{var o;l.value={id:void 0,nickname:void 0,remark:void 0,tagIds:[]},(o=n.value)==null||o.resetFields()};return(o,t)=>{const f=Z,c=q,x=z,j=A,C=B,v=E,R=T,H=G;return i(),_(R,{modelValue:e(u),"onUpdate:modelValue":t[4]||(t[4]=a=>P(u)?u.value=a:null),title:"\u4FEE\u6539"},{footer:s(()=>[r(v,{disabled:e(d),type:"primary",onClick:U},{default:s(()=>[y("\u786E \u5B9A")]),_:1},8,["disabled"]),r(v,{onClick:t[3]||(t[3]=a=>u.value=!1)},{default:s(()=>[y("\u53D6 \u6D88")]),_:1})]),default:s(()=>[N((i(),_(C,{ref_key:"formRef",ref:n,model:e(l),rules:e(I),"label-width":"80px"},{default:s(()=>[r(c,{label:"\u6635\u79F0",prop:"nickname"},{default:s(()=>[r(f,{modelValue:e(l).nickname,"onUpdate:modelValue":t[0]||(t[0]=a=>e(l).nickname=a),placeholder:"\u8BF7\u8F93\u5165\u6635\u79F0"},null,8,["modelValue"])]),_:1}),r(c,{label:"\u5907\u6CE8",prop:"remark"},{default:s(()=>[r(f,{modelValue:e(l).remark,"onUpdate:modelValue":t[1]||(t[1]=a=>e(l).remark=a),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1}),r(c,{label:"\u6807\u7B7E",prop:"tagIds"},{default:s(()=>[r(j,{modelValue:e(l).tagIds,"onUpdate:modelValue":t[2]||(t[2]=a=>e(l).tagIds=a),clearable:"",multiple:"",placeholder:"\u8BF7\u9009\u62E9\u6807\u7B7E"},{default:s(()=>[(i(!0),O(S,null,D(e(p),a=>(i(),_(x,{key:a.tagId,label:a.name,value:a.tagId},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[H,e(d)]])]),_:1},8,["modelValue"])}}})});export{k as _,la as __tla};
