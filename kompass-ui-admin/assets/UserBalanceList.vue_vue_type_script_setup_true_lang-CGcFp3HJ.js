import{d as j,r as p,f as x,C as z,o as m,l as d,w as s,H as P,a,i as l,j as g,t as f,aF as w,P as S,Q as B,R as L,__tla as q}from"./index-BUSn51wb.js";import{_ as C,__tla as F}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as H,__tla as Q}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{d as R,__tla as T}from"./formatTime-DWdBpgsM.js";import{g as k,__tla as A}from"./index-CPcKnf_r.js";let y,D=Promise.all([(()=>{try{return q}catch{}})(),(()=>{try{return F}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return T}catch{}})(),(()=>{try{return A}catch{}})()]).then(async()=>{y=j({name:"UserBalanceList",__name:"UserBalanceList",props:{walletId:{type:Number,required:!1}},setup(b){const h=b,n=p(!0),_=p(0),e=x({pageNo:1,pageSize:10,walletId:null}),c=p([]),u=async()=>{n.value=!0;try{e.walletId=h.walletId;const i=await k(e);c.value=i.list,_.value=i.total}finally{n.value=!1}};return z(()=>{u()}),(i,o)=>{const r=S,v=B,I=H,N=C,U=L;return m(),d(N,null,{default:s(()=>[P((m(),d(v,{data:a(c),"show-overflow-tooltip":!0,stripe:!0},{default:s(()=>[l(r,{align:"center",label:"\u7F16\u53F7",prop:"id"}),l(r,{align:"center",label:"\u5173\u8054\u4E1A\u52A1\u6807\u9898",prop:"title"}),l(r,{align:"center",label:"\u4EA4\u6613\u91D1\u989D",prop:"price"},{default:s(({row:t})=>[g(f(a(w)(t.price))+" \u5143",1)]),_:1}),l(r,{align:"center",label:"\u94B1\u5305\u4F59\u989D",prop:"balance"},{default:s(({row:t})=>[g(f(a(w)(t.balance))+" \u5143",1)]),_:1}),l(r,{formatter:a(R),align:"center",label:"\u4EA4\u6613\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"])]),_:1},8,["data"])),[[U,a(n)]]),l(I,{limit:a(e).pageSize,"onUpdate:limit":o[0]||(o[0]=t=>a(e).pageSize=t),page:a(e).pageNo,"onUpdate:page":o[1]||(o[1]=t=>a(e).pageNo=t),total:a(_),onPagination:u},null,8,["limit","page","total"])]),_:1})}}})});export{y as _,D as __tla};
