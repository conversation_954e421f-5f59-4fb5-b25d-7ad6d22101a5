import{d as D,r as d,f,C as P,o as b,l as N,w as l,i as r,a as n,ek as A,E,s as q,__tla as R}from"./index-BUSn51wb.js";import{E as j,__tla as k}from"./el-card-CJbXGyyg.js";import{E as S,__tla as z}from"./el-skeleton-item-tDN8U6BH.js";import{e as B,_ as F,__tla as G}from"./Echart.vue_vue_type_script_setup_true_lang-DnLZLH3w.js";import{c as H}from"./china-aeAnb323.js";import{S as I,__tla as J}from"./portrait-BcNwms8P.js";let h,K=Promise.all([(()=>{try{return R}catch{}})(),(()=>{try{return k}catch{}})(),(()=>{try{return z}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return J}catch{}})()]).then(async()=>{h=D({name:"PortraitCustomerArea",__name:"PortraitCustomerArea",props:{queryParams:{}},setup(g,{expose:v}){var p;const y=g;(p=B)==null||p.registerMap("china",H);const s=d(!1),m=d([]),o=f({title:{text:"\u5168\u90E8\u5BA2\u6237",left:"center"},tooltip:{trigger:"item",showDelay:0,transitionDuration:.2},visualMap:{text:["\u9AD8","\u4F4E"],realtime:!1,calculable:!0,top:"middle",inRange:{color:["#fff","#3b82f6"]}},series:[{name:"\u5BA2\u6237\u5730\u57DF\u5206\u5E03",type:"map",map:"china",roam:!1,selectedMode:!1,data:[]}]}),i=f({title:{text:"\u6210\u4EA4\u5BA2\u6237",left:"center"},tooltip:{trigger:"item",showDelay:0,transitionDuration:.2},visualMap:{text:["\u9AD8","\u4F4E"],realtime:!1,calculable:!0,top:"middle",inRange:{color:["#fff","#3b82f6"]}},series:[{name:"\u5BA2\u6237\u5730\u57DF\u5206\u5E03",type:"map",map:"china",roam:!1,selectedMode:!1,data:[]}]}),u=async()=>{s.value=!0;const e=await I.getCustomerArea(y.queryParams);m.value=e.map(t=>({...t,areaName:A(t.areaName)})),M(),x(),s.value=!1};v({loadData:u});const M=()=>{let e=0,t=0;o.series[0].data=m.value.map(a=>(e=Math.min(e,a.customerCount||0),t=Math.max(t,a.customerCount||0),{...a,name:a.areaName,value:a.customerCount||0})),o.visualMap.min=e,o.visualMap.max=t},x=()=>{let e=0,t=0;i.series[0].data=m.value.map(a=>(e=Math.min(e,a.dealCount||0),t=Math.max(t,a.dealCount||0),{...a,name:a.areaName,value:a.dealCount||0})),i.visualMap.min=e,i.visualMap.max=t};return P(()=>{u()}),(e,t)=>{const a=F,_=S,c=E,C=q,w=j;return b(),N(w,{shadow:"never"},{default:l(()=>[r(C,{gutter:20},{default:l(()=>[r(c,{span:12},{default:l(()=>[r(_,{loading:n(s),animated:""},{default:l(()=>[r(a,{height:500,options:n(o)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),r(c,{span:12},{default:l(()=>[r(_,{loading:n(s),animated:""},{default:l(()=>[r(a,{height:500,options:n(i)},null,8,["options"])]),_:1},8,["loading"])]),_:1})]),_:1})]),_:1})}}})});export{h as _,K as __tla};
