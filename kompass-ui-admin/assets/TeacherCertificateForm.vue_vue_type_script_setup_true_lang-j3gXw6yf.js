import{_ as H,__tla as C}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{d as j,I as O,r as s,f as F,o,l as m,w as l,g as P,i as t,j as b,a9 as n,a as A,cl as B,Z as J,L as K,M as L,J as R,K as Z,O as z,N as E,__tla as G}from"./index-BUSn51wb.js";import{T as S,__tla as Q}from"./index-Cy31ghQS.js";let y,W=Promise.all([(()=>{try{return C}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return Q}catch{}})()]).then(async()=>{let _;_={class:"dialog-footer"},y=j({__name:"TeacherCertificateForm",emits:["success"],setup(X,{expose:N,emit:I}){const U=I,x=O(),u=s(!1),p=s(!1),f=s(),h=s(!1),V={certificateId:0,teacherId:0,teacherName:"",teacherSex:"",picUrl:"",teacherIdNumber:"",certificateNo:"",certificateStatus:0,validTime:"",remark:""},a=F({...V}),k={certificateNo:[{required:!0,message:"\u8BF7\u8F93\u5165\u8BC1\u4E66\u7F16\u53F7",trigger:"blur"}],teacherSex:[{required:!0,message:"\u8BF7\u8F93\u5165\u8001\u5E08\u6027\u522B",trigger:"blur"}],teacherId:[{required:!0,message:"\u8001\u5E08ID",trigger:"blur"}],teacherIdNumber:[{required:!0,message:"\u8EAB\u4EFD\u8BC1\u53F7",trigger:"blur"}],teacherName:[{required:!0,message:"\u8001\u5E08\u59D3\u540D",trigger:"blur"}],certificateStatus:[{required:!0,message:"\u8BF7\u9009\u62E9\u8BC1\u4E66\u72B6\u6001",trigger:"change"}]},T=c=>{if(!c)return"";const e=new Date(c);return`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")} ${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}:${String(e.getSeconds()).padStart(2,"0")}`},w=()=>{u.value=!1},D=async()=>{if(f.value&&await f.value.validate().catch(()=>!1))try{p.value=!0;const c={...a,validTime:a.validTime?new Date(a.validTime):new Date};await S.createOrUpdateTeacherCertificate(c),x.success("\u53D1\u653E\u4E0A\u5C97\u8BC1\u6210\u529F"),u.value=!1,U("success")}catch(c){console.error("\u53D1\u653E\u4E0A\u5C97\u8BC1\u5931\u8D25:",c)}finally{p.value=!1}};return N({open:async c=>{if(!h.value&&(Object.assign(a,V),a.teacherId=c,u.value=!0,c))try{h.value=!0;const e=await S.getByTeacherId(c);console.log("API\u5B8C\u6574\u54CD\u5E94:",e),e&&(a.teacherName=e.teacherName,a.teacherSex=e.teacherSex,a.teacherIdNumber=e.teacherIdNumber,a.picUrl=e.picUrl,a.validTime=T(e.validTime),a.certificateNo=e.certificateNo,a.certificateStatus=Number(e.certificateStatus),a.remark=e.remark)}catch(e){console.error("\u83B7\u53D6\u8001\u5E08\u4FE1\u606F\u5931\u8D25:",e)}finally{h.value=!1}}}),(c,e)=>{const d=J,i=K,Y=L,g=R,M=Z,q=z,v=E,$=H;return o(),m($,{title:"\u7F16\u8F91\u8001\u5E08\u4FE1\u606F",modelValue:u.value,"onUpdate:modelValue":e[9]||(e[9]=r=>u.value=r),width:"600px"},{footer:l(()=>[P("div",_,[t(v,{onClick:w},{default:l(()=>[b("\u53D6 \u6D88")]),_:1}),t(v,{type:"primary",onClick:D,loading:p.value},{default:l(()=>[b("\u786E \u5B9A")]),_:1},8,["loading"])])]),default:l(()=>[t(q,{ref_key:"formRef",ref:f,model:a,rules:k,"label-width":"100px"},{default:l(()=>[t(i,{label:"\u8001\u5E08ID",prop:"teacherId"},{default:l(()=>[t(d,{modelValue:a.teacherId,"onUpdate:modelValue":e[0]||(e[0]=r=>a.teacherId=r),readonly:""},null,8,["modelValue"])]),_:1}),t(i,{label:"\u8001\u5E08\u59D3\u540D",prop:"teacherName"},{default:l(()=>[t(d,{modelValue:a.teacherName,"onUpdate:modelValue":e[1]||(e[1]=r=>a.teacherName=r),readonly:""},null,8,["modelValue"])]),_:1}),t(i,{label:"\u8001\u5E08\u6027\u522B",prop:"teacherSex"},{default:l(()=>[t(d,{modelValue:a.teacherSex,"onUpdate:modelValue":e[2]||(e[2]=r=>a.teacherSex=r),readonly:""},null,8,["modelValue"])]),_:1}),a.certificateNo?(o(),m(i,{key:0,label:"\u8BC1\u4E66\u7F16\u53F7"},{default:l(()=>[t(d,{modelValue:a.certificateNo,"onUpdate:modelValue":e[3]||(e[3]=r=>a.certificateNo=r),placeholder:"\u81EA\u52A8\u751F\u6210\uFF0C\u65E0\u9700\u586B\u5199"},null,8,["modelValue"])]),_:1})):n("",!0),t(i,{label:"\u7701\u4EFD\u8BC1\u53F7",prop:"teacherIdNumber"},{default:l(()=>[t(d,{modelValue:a.teacherIdNumber,"onUpdate:modelValue":e[4]||(e[4]=r=>a.teacherIdNumber=r),placeholder:"\u8BF7\u8F93\u5165",readonly:""},null,8,["modelValue"])]),_:1}),a.certificateNo?(o(),m(i,{key:1,label:"\u8BC1\u4E66\u7167",prop:"picUrl"},{default:l(()=>[t(A(B),{modelValue:a.picUrl,"onUpdate:modelValue":e[5]||(e[5]=r=>a.picUrl=r),height:"120px",width:"120px"},null,8,["modelValue"])]),_:1})):n("",!0),a.certificateNo?(o(),m(i,{key:2,label:"\u6709\u6548\u671F",prop:"validTime"},{default:l(()=>[t(Y,{modelValue:a.validTime,"onUpdate:modelValue":e[6]||(e[6]=r=>a.validTime=r),type:"datetime",placeholder:"\u9009\u62E9\u6709\u6548\u671F",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])]),_:1})):n("",!0),a.certificateNo?(o(),m(i,{key:3,label:"\u8BC1\u4E66\u72B6\u6001",prop:"certificateStatus"},{default:l(()=>[t(M,{modelValue:a.certificateStatus,"onUpdate:modelValue":e[7]||(e[7]=r=>a.certificateStatus=r),placeholder:"\u8BF7\u9009\u62E9\u8BC1\u4E66\u72B6\u6001"},{default:l(()=>[t(g,{label:"\u6709\u6548",value:0}),t(g,{label:"\u65E0\u6548-\u5DF2\u8FC7\u671F",value:1}),t(g,{label:"\u65E0\u6548-\u5DF2\u6CE8\u9500",value:2})]),_:1},8,["modelValue"])]),_:1})):n("",!0),t(i,{label:"\u5907\u6CE8",prop:"remark"},{default:l(()=>[t(d,{modelValue:a.remark,"onUpdate:modelValue":e[8]||(e[8]=r=>a.remark=r),type:"textarea",placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}})});export{y as _,W as __tla};
