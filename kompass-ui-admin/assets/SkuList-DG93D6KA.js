import{d as X,p as C,r as B,at as R,o as u,c as V,l as h,w as o,a as p,a9 as w,F as v,i,cl as Y,k as N,g as S,t as m,j as f,Q as A,aV as ee,I as le,aJ as ae,dK as te,aA as $,P as ie,Z as oe,cc as re,N as ne,B as se,__tla as ce}from"./index-BUSn51wb.js";import{E as de,__tla as ue}from"./el-image-BjHZRFih.js";let K,pe=Promise.all([(()=>{try{return ce}catch{}})(),(()=>{try{return ue}catch{}})()]).then(async()=>{let T,E,L;T={style:{"font-weight":"bold",color:"#40aaff"}},E={style:{"font-weight":"bold",color:"#40aaff"}},L={style:{"font-weight":"bold",color:"#40aaff"}},K=se(X({name:"SkuList",__name:"SkuList",props:{propFormData:{type:Object,default:()=>{}},propertyList:{type:Array,default:()=>[]},ruleConfig:{type:Array,default:()=>[]},isBatch:C.bool.def(!1),isDetail:C.bool.def(!1),isComponent:C.bool.def(!1),isActivityComponent:C.bool.def(!1)},emits:["selectionChange"],setup(g,{expose:Q,emit:W}){const O=le(),y=g,d=B(),x=B([{price:0,marketPrice:0,costPrice:0,barCode:"",picUrl:"",stock:0,weight:0,volume:0,firstBrokeragePrice:0,secondBrokeragePrice:0}]),z=t=>{ae({zIndex:9999999,urlList:[t]})},Z=()=>{D(),d.value.skus.forEach(t=>{te(t,x.value[0])})},D=()=>{const t="\u5B58\u5728\u5C5E\u6027\u5C5E\u6027\u503C\u4E3A\u7A7A\uFF0C\u8BF7\u5148\u68C0\u67E5\u5B8C\u5584\u5C5E\u6027\u503C\u540E\u91CD\u8BD5\uFF01\uFF01\uFF01";for(const c of y.propertyList)if(!c.values||$(c.values))throw O.warning(t),new Error(t)},U=B([]),q=(t,c)=>{const e=c.split(".");let r=t;for(const n of e){if(!r||typeof r!="object"||!(n in r)){r=void 0;break}r=r[n]}return r},G=W,H=t=>{G("selectionChange",t)};R(()=>y.propFormData,t=>{t&&(d.value=t)},{deep:!0,immediate:!0});const I=t=>{const c=t.map(r=>r.values.map(n=>({propertyId:r.id,propertyName:r.name,valueId:n.id,valueName:n.name}))),e=j(c);J(t)||(d.value.skus=[]);for(const r of e){const n={properties:Array.isArray(r)?r:[r],price:0,marketPrice:0,costPrice:0,barCode:"",picUrl:"",stock:0,weight:0,volume:0,firstBrokeragePrice:0,secondBrokeragePrice:0};d.value.skus.findIndex(b=>JSON.stringify(b.properties)===JSON.stringify(n.properties))===-1&&d.value.skus.push(n)}},J=t=>{const c=[];d.value.skus.forEach(r=>{var n,b;return(b=(n=r.properties)==null?void 0:n.map(_=>_.propertyId))==null?void 0:b.forEach(_=>{c.indexOf(_)===-1&&c.push(_)})});const e=t.map(r=>r.id);return c.length===e.length},j=t=>{if(t.length===0)return[];if(t.length===1)return t[0];{const c=[],e=j(t.slice(1));for(let r=0;r<t[0].length;r++)for(let n=0;n<e.length;n++)Array.isArray(e[n])?c.push([t[0][r],...e[n]]):c.push([t[0][r],e[n]]);return c}};R(()=>y.propertyList,t=>{d.value.specType&&(y.isBatch&&(x.value=[{price:0,marketPrice:0,costPrice:0,barCode:"",picUrl:"",stock:0,weight:0,volume:0,firstBrokeragePrice:0,secondBrokeragePrice:0}]),JSON.stringify(t)!=="[]"&&(U.value=[],t.forEach((c,e)=>{U.value.push({prop:`name${e}`,label:c.name})}),J(t)||t.some(c=>!c.values||$(c.values))||I(t)))},{deep:!0,immediate:!0});const F=B();return Q({generateTableData:I,validateSku:()=>{D();let t="\u8BF7\u68C0\u67E5\u5546\u54C1\u5404\u884C\u76F8\u5173\u5C5E\u6027\u914D\u7F6E\uFF0C",c=!0;for(const e of d.value.skus){for(const r of y==null?void 0:y.ruleConfig){const n=q(e,r.name);if(!r.rule(n)){c=!1,t+=r.message;break}}if(!c)throw O.warning(t),new Error(t)}},getSkuTableRef:()=>F.value}),(t,c)=>{const e=ie,r=oe,n=re,b=ne,_=de;return u(),V(v,null,[g.isDetail||g.isActivityComponent?w("",!0):(u(),h(p(A),{key:0,data:g.isBatch?p(x):p(d).skus,border:"",class:"tabNumWidth","max-height":"500",size:"small"},{default:o(()=>{var l;return[i(e,{align:"center",label:"\u56FE\u7247","min-width":"65"},{default:o(({row:a})=>[i(p(Y),{modelValue:a.picUrl,"onUpdate:modelValue":s=>a.picUrl=s,height:"50px",width:"50px"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),p(d).specType&&!g.isBatch?(u(!0),V(v,{key:0},N(p(U),(a,s)=>(u(),h(e,{key:s,label:a.label,align:"center","min-width":"120"},{default:o(({row:k})=>{var P;return[S("span",T,m((P=k.properties[s])==null?void 0:P.valueName),1)]}),_:2},1032,["label"]))),128)):w("",!0),i(e,{align:"center",label:"\u5546\u54C1\u6761\u7801","min-width":"168"},{default:o(({row:a})=>[i(r,{modelValue:a.barCode,"onUpdate:modelValue":s=>a.barCode=s,class:"w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(e,{align:"center",label:"\u9500\u552E\u4EF7","min-width":"168"},{default:o(({row:a})=>[i(n,{modelValue:a.price,"onUpdate:modelValue":s=>a.price=s,min:0,precision:2,step:.1,class:"w-100%","controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(e,{align:"center",label:"\u5E02\u573A\u4EF7","min-width":"168"},{default:o(({row:a})=>[i(n,{modelValue:a.marketPrice,"onUpdate:modelValue":s=>a.marketPrice=s,min:0,precision:2,step:.1,class:"w-100%","controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(e,{align:"center",label:"\u6210\u672C\u4EF7","min-width":"168"},{default:o(({row:a})=>[i(n,{modelValue:a.costPrice,"onUpdate:modelValue":s=>a.costPrice=s,min:0,precision:2,step:.1,class:"w-100%","controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(e,{align:"center",label:"\u5E93\u5B58","min-width":"168"},{default:o(({row:a})=>[i(n,{modelValue:a.stock,"onUpdate:modelValue":s=>a.stock=s,min:0,class:"w-100%","controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(e,{align:"center",label:"\u91CD\u91CF(kg)","min-width":"168"},{default:o(({row:a})=>[i(n,{modelValue:a.weight,"onUpdate:modelValue":s=>a.weight=s,min:0,precision:2,step:.1,class:"w-100%","controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(e,{align:"center",label:"\u4F53\u79EF(m^3)","min-width":"168"},{default:o(({row:a})=>[i(n,{modelValue:a.volume,"onUpdate:modelValue":s=>a.volume=s,min:0,precision:2,step:.1,class:"w-100%","controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),p(d).subCommissionType?(u(),V(v,{key:1},[i(e,{align:"center",label:"\u4E00\u7EA7\u8FD4\u4F63(\u5143)","min-width":"168"},{default:o(({row:a})=>[i(n,{modelValue:a.firstBrokeragePrice,"onUpdate:modelValue":s=>a.firstBrokeragePrice=s,min:0,precision:2,step:.1,class:"w-100%","controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(e,{align:"center",label:"\u4E8C\u7EA7\u8FD4\u4F63(\u5143)","min-width":"168"},{default:o(({row:a})=>[i(n,{modelValue:a.secondBrokeragePrice,"onUpdate:modelValue":s=>a.secondBrokeragePrice=s,min:0,precision:2,step:.1,class:"w-100%","controls-position":"right"},null,8,["modelValue","onUpdate:modelValue"])]),_:1})],64)):w("",!0),(l=p(d))!=null&&l.specType?(u(),h(e,{key:2,align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"80"},{default:o(({row:a})=>[g.isBatch?(u(),h(b,{key:0,link:"",size:"small",type:"primary",onClick:Z},{default:o(()=>[f(" \u6279\u91CF\u6DFB\u52A0 ")]),_:1})):(u(),h(b,{key:1,link:"",size:"small",type:"primary",onClick:s=>(k=>{const P=d.value.skus.findIndex(M=>JSON.stringify(M.properties)===JSON.stringify(k.properties));d.value.skus.splice(P,1)})(a)},{default:o(()=>[f("\u5220\u9664")]),_:2},1032,["onClick"]))]),_:1})):w("",!0)]}),_:1},8,["data"])),g.isDetail?(u(),h(p(A),{key:1,ref_key:"activitySkuListRef",ref:F,data:p(d).skus,border:"","max-height":"500",size:"small",style:{width:"99%"},onSelectionChange:H},{default:o(()=>[g.isComponent?(u(),h(e,{key:0,type:"selection",width:"45"})):w("",!0),i(e,{align:"center",label:"\u56FE\u7247","min-width":"80"},{default:o(({row:l})=>[l.picUrl?(u(),h(_,{key:0,src:l.picUrl,class:"h-50px w-50px",onClick:a=>z(l.picUrl)},null,8,["src","onClick"])):w("",!0)]),_:1}),p(d).specType&&!g.isBatch?(u(!0),V(v,{key:1},N(p(U),(l,a)=>(u(),h(e,{key:a,label:l.label,align:"center","min-width":"80"},{default:o(({row:s})=>{var k;return[S("span",E,m((k=s.properties[a])==null?void 0:k.valueName),1)]}),_:2},1032,["label"]))),128)):w("",!0),i(e,{align:"center",label:"\u5546\u54C1\u6761\u7801","min-width":"100"},{default:o(({row:l})=>[f(m(l.barCode),1)]),_:1}),i(e,{align:"center",label:"\u9500\u552E\u4EF7(\u5143)","min-width":"80"},{default:o(({row:l})=>[f(m(l.price),1)]),_:1}),i(e,{align:"center",label:"\u5E02\u573A\u4EF7(\u5143)","min-width":"80"},{default:o(({row:l})=>[f(m(l.marketPrice),1)]),_:1}),i(e,{align:"center",label:"\u6210\u672C\u4EF7(\u5143)","min-width":"80"},{default:o(({row:l})=>[f(m(l.costPrice),1)]),_:1}),i(e,{align:"center",label:"\u5E93\u5B58","min-width":"80"},{default:o(({row:l})=>[f(m(l.stock),1)]),_:1}),i(e,{align:"center",label:"\u91CD\u91CF(kg)","min-width":"80"},{default:o(({row:l})=>[f(m(l.weight),1)]),_:1}),i(e,{align:"center",label:"\u4F53\u79EF(m^3)","min-width":"80"},{default:o(({row:l})=>[f(m(l.volume),1)]),_:1}),p(d).subCommissionType?(u(),V(v,{key:2},[i(e,{align:"center",label:"\u4E00\u7EA7\u8FD4\u4F63(\u5143)","min-width":"80"},{default:o(({row:l})=>[f(m(l.firstBrokeragePrice),1)]),_:1}),i(e,{align:"center",label:"\u4E8C\u7EA7\u8FD4\u4F63(\u5143)","min-width":"80"},{default:o(({row:l})=>[f(m(l.secondBrokeragePrice),1)]),_:1})],64)):w("",!0)]),_:1},8,["data"])):w("",!0),g.isActivityComponent?(u(),h(p(A),{key:2,data:p(d).skus,border:"","max-height":"500",size:"small",style:{width:"99%"}},{default:o(()=>[g.isComponent?(u(),h(e,{key:0,type:"selection",width:"45"})):w("",!0),i(e,{align:"center",label:"\u56FE\u7247","min-width":"80"},{default:o(({row:l})=>[i(_,{src:l.picUrl,class:"h-60px w-60px",onClick:a=>z(l.picUrl)},null,8,["src","onClick"])]),_:1}),p(d).specType?(u(!0),V(v,{key:1},N(p(U),(l,a)=>(u(),h(e,{key:a,label:l.label,align:"center","min-width":"80"},{default:o(({row:s})=>{var k;return[S("span",L,m((k=s.properties[a])==null?void 0:k.valueName),1)]}),_:2},1032,["label"]))),128)):w("",!0),i(e,{align:"center",label:"\u5546\u54C1\u6761\u7801","min-width":"100"},{default:o(({row:l})=>[f(m(l.barCode),1)]),_:1}),i(e,{align:"center",label:"\u9500\u552E\u4EF7(\u5143)","min-width":"80"},{default:o(({row:l})=>[f(m(l.price),1)]),_:1}),i(e,{align:"center",label:"\u5E02\u573A\u4EF7(\u5143)","min-width":"80"},{default:o(({row:l})=>[f(m(l.marketPrice),1)]),_:1}),i(e,{align:"center",label:"\u6210\u672C\u4EF7(\u5143)","min-width":"80"},{default:o(({row:l})=>[f(m(l.costPrice),1)]),_:1}),i(e,{align:"center",label:"\u5E93\u5B58","min-width":"80"},{default:o(({row:l})=>[f(m(l.stock),1)]),_:1}),ee(t.$slots,"extension",{},void 0,!0)]),_:3},8,["data"])):w("",!0)],64)}}}),[["__scopeId","data-v-8117af22"]])});export{pe as __tla,K as default};
