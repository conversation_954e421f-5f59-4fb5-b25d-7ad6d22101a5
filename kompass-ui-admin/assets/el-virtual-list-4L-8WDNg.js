import{as as Ne,b as c,cu as ze,cv as m,be as w,bd as I,cw as Ee,d as Me,bf as Z,r as ee,f as Le,cx as Be,a as R,at as Ce,bb as Ie,h as te,b1 as Re,bz as Te,bB as Oe,__tla as _e}from"./index-BUSn51wb.js";let ae,A,re,le,ne,j,h,se,T,oe,O,P,k,ie,ue,H,ce,de,ve,me,pe,fe,V,ge,he,$e=Promise.all([(()=>{try{return _e}catch{}})()]).then(async()=>{var K=Number.isNaN||function(e){return typeof e=="number"&&e!=e};function be(e,a){if(e.length!==a.length)return!1;for(var l=0;l<e.length;l++)if(n=e[l],r=a[l],!(n===r||K(n)&&K(r)))return!1;var n,r;return!0}let W,x,N,z,Y,E,b,_,$,M,q,D;ge=()=>{const e=Ne().proxy.$props;return c(()=>{const a=(l,n,r)=>({});return e.perfMode?ze(a):function(l,n){n===void 0&&(n=be);var r=null;function p(){for(var u=[],d=0;d<arguments.length;d++)u[d]=arguments[d];if(r&&r.lastThis===this&&n(u,r.lastArgs))return r.lastResult;var f=l.apply(this,u);return r={lastResult:f,lastArgs:u,lastThis:this},f}return p.clear=function(){r=null},p}(a)})},le=50,se="itemRendered",oe="scroll",j="forward",A="backward",ae="auto",ve="smart",ce="start",re="center",ne="end",h="horizontal",O="vertical",T="rtl",k="negative",P="positive-ascending",H="positive-descending",W={[h]:"left",[O]:"top"},x=m({type:w([Number,Function]),required:!0}),N=m({type:Number}),z=m({type:Number,default:2}),Y=m({type:String,values:["ltr","rtl"],default:"ltr"}),E=m({type:Number,default:0}),b=m({type:Number,required:!0}),_=m({type:String,values:["horizontal","vertical"],default:O}),$=I({className:{type:String,default:""},containerElement:{type:w([String,Object]),default:"div"},data:{type:w(Array),default:()=>Ee([])},direction:Y,height:{type:[String,Number],required:!0},innerElement:{type:[String,Object],default:"div"},style:{type:w([Object,String,Array])},useIsScrolling:{type:Boolean,default:!1},width:{type:[Number,String],required:!1},perfMode:{type:Boolean,default:!0},scrollbarAlwaysOn:{type:Boolean,default:!1}}),he=I({cache:z,estimatedItemSize:N,layout:_,initScrollOffset:E,total:b,itemSize:x,...$}),M={type:Number,default:6},q={type:Number,default:0},D={type:Number,default:2},pe=I({columnCache:z,columnWidth:x,estimatedColumnWidth:N,estimatedRowHeight:N,initScrollLeft:E,initScrollTop:E,itemKey:{type:w(Function),default:({columnIndex:e,rowIndex:a})=>`${a}:${e}`},rowCache:z,rowHeight:x,totalColumn:b,totalRow:b,hScrollbarSize:M,vScrollbarSize:M,scrollbarStartGap:q,scrollbarEndGap:D,role:String,...$}),V=I({alwaysOn:Boolean,class:String,layout:_,total:b,ratio:{type:Number,required:!0},clientSize:{type:Number,required:!0},scrollFrom:{type:Number,required:!0},scrollbarSize:M,startGap:q,endGap:D,visible:Boolean}),ue=(e,a)=>e<a?j:A,me=e=>e==="ltr"||e===T||e===h,fe=e=>e===T;let y=null;de=function(e=!1){if(y===null||e){const a=document.createElement("div"),l=a.style;l.width="50px",l.height="50px",l.overflow="scroll",l.direction="rtl";const n=document.createElement("div"),r=n.style;return r.width="100px",r.height="100px",a.appendChild(n),document.body.appendChild(a),a.scrollLeft>0?y=H:(a.scrollLeft=1,y=a.scrollLeft===0?k:P),document.body.removeChild(a),y}return y},ie=Me({name:"ElVirtualScrollBar",props:V,emits:["scroll","start-move","stop-move"],setup(e,{emit:a}){const l=c(()=>e.startGap+e.endGap),n=Z("virtual-scrollbar"),r=Z("scrollbar"),p=ee(),u=ee();let d=null,f=null;const i=Le({isDragging:!1,traveled:0}),s=c(()=>Be[e.layout]),J=c(()=>e.clientSize-R(l)),ye=c(()=>({position:"absolute",width:`${h===e.layout?J.value:e.scrollbarSize}px`,height:`${h===e.layout?e.scrollbarSize:J.value}px`,[W[e.layout]]:"2px",right:"2px",bottom:"2px",borderRadius:"4px"})),F=c(()=>{const t=e.ratio,o=e.clientSize;if(t>=100)return Number.POSITIVE_INFINITY;if(t>=50)return t*o/100;const g=o/3;return Math.floor(Math.min(Math.max(t*o,20),g))}),Se=c(()=>{if(!Number.isFinite(F.value))return{display:"none"};const t=`${F.value}px`;return function({move:g,size:C,bar:X},xe){const v={},G=`translate${X.axis}(${g}px)`;return v[X.size]=C,v.transform=G,v.msTransform=G,v.webkitTransform=G,xe==="horizontal"?v.height="100%":v.width="100%",v}({bar:s.value,size:t,move:i.traveled},e.layout)}),S=c(()=>Math.floor(e.clientSize-F.value-R(l))),Q=()=>{window.removeEventListener("mousemove",B),window.removeEventListener("mouseup",L),document.onselectstart=f,f=null;const t=R(u);t&&(t.removeEventListener("touchmove",B),t.removeEventListener("touchend",L))},U=t=>{t.stopImmediatePropagation(),t.ctrlKey||[1,2].includes(t.button)||(i.isDragging=!0,i[s.value.axis]=t.currentTarget[s.value.offset]-(t[s.value.client]-t.currentTarget.getBoundingClientRect()[s.value.direction]),a("start-move"),(()=>{window.addEventListener("mousemove",B),window.addEventListener("mouseup",L);const o=R(u);o&&(f=document.onselectstart,document.onselectstart=()=>!1,o.addEventListener("touchmove",B),o.addEventListener("touchend",L))})())},L=()=>{i.isDragging=!1,i[s.value.axis]=0,a("stop-move"),Q()},B=t=>{const{isDragging:o}=i;if(!o||!u.value||!p.value)return;const g=i[s.value.axis];if(!g)return;Te(d);const C=-1*(p.value.getBoundingClientRect()[s.value.direction]-t[s.value.client])-(u.value[s.value.offset]-g);d=Oe(()=>{i.traveled=Math.max(e.startGap,Math.min(C,S.value)),a("scroll",C,S.value)})},we=t=>{const o=Math.abs(t.target.getBoundingClientRect()[s.value.direction]-t[s.value.client])-u.value[s.value.offset]/2;i.traveled=Math.max(0,Math.min(o,S.value)),a("scroll",o,S.value)};return Ce(()=>e.scrollFrom,t=>{i.isDragging||(i.traveled=Math.ceil(t*S.value))}),Ie(()=>{Q()}),()=>te("div",{role:"presentation",ref:p,class:[n.b(),e.class,(e.alwaysOn||i.isDragging)&&"always-on"],style:ye.value,onMousedown:Re(we,["stop","prevent"]),onTouchstartPrevent:U},te("div",{ref:u,class:r.e("thumb"),style:Se.value,onMousedown:U},[]))}})});export{ae as A,A as B,re as C,le as D,ne as E,j as F,h as H,se as I,T as R,oe as S,O as V,$e as __tla,P as a,k as b,ie as c,ue as d,H as e,ce as f,de as g,ve as h,me as i,pe as j,fe as k,V as l,ge as u,he as v};
