import{d as re,r as m,u as se,a7 as de,b as pe,a as t,f as ce,at as ue,C as me,H as fe,a8 as ge,o as F,l as S,w as a,i as e,a9 as _e,U as he,j as p,t as v,g as z,c as ye,k as we,F as xe,n as be,I as Fe,aa as ve,dl as ke,ak as k,ae as Ve,af as De,ag as Ne,ah as Ce,ac as Ee,dm as Le,aj as O,dn as Re,L as je,E as Ie,Z as Me,ai as Pe,v as Ue,s as Se,q as ze,_ as Oe,O as Te,B as $e,__tla as qe}from"./index-BUSn51wb.js";import{_ as Ae,__tla as Be}from"./Verify-D3ATtF6p.js";import{_ as Ge,__tla as Qe}from"./XButton-BjahQbul.js";import{u as He,L as h,_ as Ke,a as Ze,__tla as Je}from"./LoginFormTitle.vue_vue_type_script_setup_true_lang-DUoWRVM3.js";import{u as V,__tla as We}from"./useIcon-th7lSKBX.js";import{r as D,__tla as Xe}from"./formRules-CA9eXdcX.js";let T,Ye=Promise.all([(()=>{try{return qe}catch{}})(),(()=>{try{return Be}catch{}})(),(()=>{try{return Qe}catch{}})(),(()=>{try{return Je}catch{}})(),(()=>{try{return We}catch{}})(),(()=>{try{return Xe}catch{}})()]).then(async()=>{let N,C;N={class:"w-[100%] flex justify-between"},C={class:"w-[100%] flex justify-between"},T=$e(re({name:"LoginForm",__name:"LoginForm",setup(ea){const{t:s}=be(),E=Fe(),$=V({icon:"ep:house"}),q=V({icon:"ep:avatar"}),A=V({icon:"ep:lock"}),L=m(),{validForm:B}=Ze(L),{setLoginState:y,getLoginState:G}=He(),{currentRoute:Q,push:H}=se(),K=de(),c=m(""),f=m(!1),R=m(),Z=m("blockPuzzle"),J=pe(()=>t(G)===h.LOGIN),W={tenantName:[D],username:[D],password:[D]},n=ce({isShowPassword:!1,captchaEnable:"false",tenantEnable:"true",loginForm:{tenantName:"\u603B\u90E8",username:"admin",password:"admin123",captchaVerification:"",rememberMe:!0}}),X=[{icon:"ant-design:wechat-filled",type:30},{icon:"ant-design:dingtalk-circle-filled",type:20},{icon:"ant-design:github-filled",type:0},{icon:"ant-design:alipay-circle-filled",type:0}],j=async()=>{n.captchaEnable==="false"?await P({}):R.value.show()},I=async()=>{if(n.tenantEnable==="true"){const o=await O(n.loginForm.tenantName);k(o)}},M=m(),P=async o=>{f.value=!0;try{if(await I(),!await B())return;n.loginForm.captchaVerification=o.captchaVerification;const l=await Ve(n.loginForm);if(!l)return;M.value=De.service({lock:!0,text:"\u6B63\u5728\u52A0\u8F7D\u7CFB\u7EDF\u4E2D...",background:"rgba(0, 0, 0, 0.7)"}),n.loginForm.rememberMe?Ne(n.loginForm):Ce(),Ee(l),c.value||(c.value="/"),c.value.indexOf("sso")!==-1?window.location.href=window.location.href.replace("/login?redirect=",""):H({path:c.value||K.addRouters[0].path})}finally{f.value=!1,M.value.close()}};return ue(()=>Q.value,o=>{var l;c.value=(l=o==null?void 0:o.query)==null?void 0:l.redirect},{immediate:!0}),me(()=>{(()=>{const o=ve();o&&(n.loginForm={...n.loginForm,username:o.username?o.username:n.loginForm.username,password:o.password?o.password:n.loginForm.password,rememberMe:o.rememberMe,tenantName:o.tenantName?o.tenantName:n.loginForm.tenantName})})(),(async()=>{const o=location.host,l=await ke(o);l&&(n.loginForm.tenantName=l.name,k(l.id))})()}),(o,l)=>{const d=je,r=Ie,w=Me,Y=Pe,g=Ue,x=Se,_=Ge,ee=Ae,U=ze,ae=Oe,te=Te;return fe((F(),S(te,{ref_key:"formLogin",ref:L,model:t(n).loginForm,rules:W,class:"login-form","label-position":"top","label-width":"120px",size:"large"},{default:a(()=>[e(x,{style:{"margin-right":"-10px","margin-left":"-10px"}},{default:a(()=>[e(r,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:a(()=>[e(d,null,{default:a(()=>[e(Ke,{style:{width:"100%"}})]),_:1})]),_:1}),e(r,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:a(()=>[t(n).tenantEnable==="true"?(F(),S(d,{key:0,prop:"tenantName"},{default:a(()=>[e(w,{modelValue:t(n).loginForm.tenantName,"onUpdate:modelValue":l[0]||(l[0]=i=>t(n).loginForm.tenantName=i),placeholder:t(s)("login.tenantNamePlaceholder"),"prefix-icon":t($),link:"",type:"primary"},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})):_e("",!0)]),_:1}),e(r,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:a(()=>[e(d,{prop:"username"},{default:a(()=>[e(w,{modelValue:t(n).loginForm.username,"onUpdate:modelValue":l[1]||(l[1]=i=>t(n).loginForm.username=i),placeholder:t(s)("login.usernamePlaceholder"),"prefix-icon":t(q)},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),e(r,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:a(()=>[e(d,{prop:"password"},{default:a(()=>[e(w,{modelValue:t(n).loginForm.password,"onUpdate:modelValue":l[2]||(l[2]=i=>t(n).loginForm.password=i),placeholder:t(s)("login.passwordPlaceholder"),"prefix-icon":t(A),"show-password":"",type:"password",onKeyup:l[3]||(l[3]=he(i=>j(),["enter"]))},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),e(r,{span:24,style:{"padding-right":"10px","padding-left":"10px","margin-top":"-20px","margin-bottom":"-20px"}},{default:a(()=>[e(d,null,{default:a(()=>[e(x,{justify:"space-between",style:{width:"100%"}},{default:a(()=>[e(r,{span:6},{default:a(()=>[e(Y,{modelValue:t(n).loginForm.rememberMe,"onUpdate:modelValue":l[4]||(l[4]=i=>t(n).loginForm.rememberMe=i)},{default:a(()=>[p(v(t(s)("login.remember")),1)]),_:1},8,["modelValue"])]),_:1}),e(r,{offset:6,span:12},{default:a(()=>[e(g,{style:{float:"right"},type:"primary"},{default:a(()=>[p(v(t(s)("login.forgetPassword")),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),e(r,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:a(()=>[e(d,null,{default:a(()=>[e(_,{loading:t(f),title:t(s)("login.login"),class:"w-[100%]",type:"primary",onClick:l[5]||(l[5]=i=>j())},null,8,["loading","title"])]),_:1})]),_:1}),e(ee,{ref_key:"verify",ref:R,captchaType:t(Z),imgSize:{width:"400px",height:"200px"},mode:"pop",onSuccess:P},null,8,["captchaType"]),e(r,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:a(()=>[e(d,null,{default:a(()=>[e(x,{gutter:5,justify:"space-between",style:{width:"100%"}},{default:a(()=>[e(r,{span:8},{default:a(()=>[e(_,{title:t(s)("login.btnMobile"),class:"w-[100%]",onClick:l[6]||(l[6]=i=>t(y)(t(h).MOBILE))},null,8,["title"])]),_:1}),e(r,{span:8},{default:a(()=>[e(_,{title:t(s)("login.btnQRCode"),class:"w-[100%]",onClick:l[7]||(l[7]=i=>t(y)(t(h).QR_CODE))},null,8,["title"])]),_:1}),e(r,{span:8},{default:a(()=>[e(_,{title:t(s)("login.btnRegister"),class:"w-[100%]",onClick:l[8]||(l[8]=i=>t(y)(t(h).REGISTER))},null,8,["title"])]),_:1})]),_:1})]),_:1})]),_:1}),e(U,{"content-position":"center"},{default:a(()=>[p(v(t(s)("login.otherLogin")),1)]),_:1}),e(r,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:a(()=>[e(d,null,{default:a(()=>[z("div",N,[(F(),ye(xe,null,we(X,(i,le)=>e(ae,{key:le,icon:i.icon,size:30,class:"anticon cursor-pointer",color:"#999",onClick:aa=>(async b=>{if(b===0)E.error("\u6B64\u65B9\u5F0F\u672A\u914D\u7F6E");else{if(f.value=!0,n.tenantEnable==="true"&&(await I(),!Le()))try{const u=await E.prompt("\u8BF7\u8F93\u5165\u79DF\u6237\u540D\u79F0",s("common.reminder"));if((u==null?void 0:u.action)!=="confirm")throw"cancel";const ie=await O(u.value);k(ie)}catch(u){if(u==="cancel")return}finally{f.value=!1}const ne=location.origin+"/social-login?"+encodeURIComponent(`type=${b}&redirect=${c.value||"/"}`),oe=await Re(b,encodeURIComponent(ne));window.location.href=oe}})(i.type)},null,8,["icon","onClick"])),64))])]),_:1})]),_:1}),e(U,{"content-position":"center"},{default:a(()=>[p("\u840C\u65B0\u5FC5\u8BFB")]),_:1}),e(r,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:a(()=>[e(d,null,{default:a(()=>[z("div",C,[e(g,{href:"https://doc.iocoder.cn/",target:"_blank"},{default:a(()=>[p("\u{1F4DA}\u5F00\u53D1\u6307\u5357")]),_:1}),e(g,{href:"https://doc.iocoder.cn/video/",target:"_blank"},{default:a(()=>[p("\u{1F525}\u89C6\u9891\u6559\u7A0B")]),_:1}),e(g,{href:"https://www.iocoder.cn/Interview/good-collection/",target:"_blank"},{default:a(()=>[p(" \u26A1\u9762\u8BD5\u624B\u518C ")]),_:1}),e(g,{href:"http://static.yudao.iocoder.cn/mp/Aix9975.jpeg",target:"_blank"},{default:a(()=>[p(" \u{1F91D}\u5916\u5305\u54A8\u8BE2 ")]),_:1})])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])),[[ge,t(J)]])}}}),[["__scopeId","data-v-c3800b87"]])});export{Ye as __tla,T as default};
