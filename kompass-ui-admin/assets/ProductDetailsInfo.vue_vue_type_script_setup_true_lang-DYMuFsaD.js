import{_ as T,__tla as h}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{d as v,r as U,o as C,l as I,w as a,i as t,j as _,t as r,a as u,dX as D,G as n,y as P,g as R,__tla as g}from"./index-BUSn51wb.js";import{E as V,a as j,__tla as x}from"./el-collapse-item-B_QvnH_b.js";import{E,a as M,__tla as N}from"./el-descriptions-item-dD3qa0ub.js";import{_ as O,__tla as S}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";let p,w=Promise.all([(()=>{try{return h}catch{}})(),(()=>{try{return g}catch{}})(),(()=>{try{return x}catch{}})(),(()=>{try{return N}catch{}})(),(()=>{try{return S}catch{}})()]).then(async()=>{let o;o=R("span",{class:"text-base font-bold"},"\u57FA\u672C\u4FE1\u606F",-1),p=v({__name:"ProductDetailsInfo",props:{product:{}},setup(A){const s=U(["basicInfo"]);return(l,c)=>{const e=E,d=O,f=M,m=V,i=j,y=T;return C(),I(y,null,{default:a(()=>[t(i,{modelValue:u(s),"onUpdate:modelValue":c[0]||(c[0]=b=>P(s)?s.value=b:null)},{default:a(()=>[t(m,{name:"basicInfo"},{title:a(()=>[o]),default:a(()=>[t(f,{column:4},{default:a(()=>[t(e,{label:"\u4EA7\u54C1\u540D\u79F0"},{default:a(()=>[_(r(l.product.name),1)]),_:1}),t(e,{label:"\u4EA7\u54C1\u7F16\u7801"},{default:a(()=>[_(r(l.product.no),1)]),_:1}),t(e,{label:"\u4EF7\u683C"},{default:a(()=>[_(r(u(D)(l.product.price))+" \u5143 ",1)]),_:1}),t(e,{label:"\u4EA7\u54C1\u63CF\u8FF0"},{default:a(()=>[_(r(l.product.description),1)]),_:1}),t(e,{label:"\u4EA7\u54C1\u7C7B\u578B"},{default:a(()=>[_(r(l.product.categoryName),1)]),_:1}),t(e,{label:"\u662F\u5426\u4E0A\u4E0B\u67B6"},{default:a(()=>[t(d,{type:u(n).CRM_PRODUCT_STATUS,value:l.product.status},null,8,["type","value"])]),_:1}),t(e,{label:"\u5355\u4F4D"},{default:a(()=>[t(d,{type:u(n).CRM_PRODUCT_UNIT,value:l.product.unit},null,8,["type","value"])]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})}}})});export{p as _,w as __tla};
