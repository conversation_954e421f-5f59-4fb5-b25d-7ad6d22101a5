import{by as a,__tla as l}from"./index-BUSn51wb.js";let r,s=Promise.all([(()=>{try{return l}catch{}})()]).then(async()=>{r={getSaleOrderPage:async e=>await a.get({url:"/erp/sale-order/page",params:e}),getSaleOrder:async e=>await a.get({url:"/erp/sale-order/get?id="+e}),createSaleOrder:async e=>await a.post({url:"/erp/sale-order/create",data:e}),updateSaleOrder:async e=>await a.put({url:"/erp/sale-order/update",data:e}),updateSaleOrderStatus:async(e,t)=>await a.put({url:"/erp/sale-order/update-status",params:{id:e,status:t}}),deleteSaleOrder:async e=>await a.delete({url:"/erp/sale-order/delete",params:{ids:e.join(",")}}),exportSaleOrder:async e=>await a.download({url:"/erp/sale-order/export-excel",params:e})}});export{r as S,s as __tla};
