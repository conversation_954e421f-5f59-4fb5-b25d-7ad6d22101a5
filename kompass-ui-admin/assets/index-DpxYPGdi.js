import{bz as _o,bB as Lo,d as we,bf as Ne,as as yt,r as U,bn as re,b as M,a as r,C as Qe,bC as zo,ay as Pe,eH as Fo,b0 as Ut,h as Ae,bp as Xt,bE as jo,bM as ko,bN as xt,at as Ze,eD as De,ba as Vo,bV as $e,c1 as Rt,bq as et,cs as Ee,c0 as Go,bb as No,bj as Yt,be as k,cw as Po,bd as Ke,aV as Be,i as R,d4 as $o,ao as te,b6 as Jt,eI as Bo,eJ as qo,br as Qt,cQ as Uo,bF as Xo,c8 as Yo,bh as Zt,o as Jo,c as Qo,w as tt,j as Zo,g as el,F as tl,_ as ol,N as ll,__tla as rl}from"./index-BUSn51wb.js";import{E as al,__tla as nl}from"./el-empty-DomufbmG.js";import{j as Me,I as eo,S as to,F as ot,u as sl,B as oo,d as lt,A as rt,R as il,g as lo,a as cl,b as ro,k as ao,c as no,e as dl,C as _e,E as bt,f as St,h as so,D as io,v as ul,l as hl,__tla as ml}from"./el-virtual-list-4L-8WDNg.js";import{_ as fl,__tla as wl}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as pl,__tla as gl}from"./index-COobLwz-.js";import{_ as yl,__tla as xl}from"./AreaForm.vue_vue_type_script_setup_true_lang-Coh_djzw.js";import{g as Rl,__tla as bl}from"./index-CyP7ZSdX.js";import{__tla as Sl}from"./el-card-CJbXGyyg.js";import{__tla as vl}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";let co,Tl=Promise.all([(()=>{try{return rl}catch{}})(),(()=>{try{return nl}catch{}})(),(()=>{try{return ml}catch{}})(),(()=>{try{return wl}catch{}})(),(()=>{try{return gl}catch{}})(),(()=>{try{return xl}catch{}})(),(()=>{try{return bl}catch{}})(),(()=>{try{return Sl}catch{}})(),(()=>{try{return vl}catch{}})()]).then(async()=>{const vt=({name:e,clearCache:t,getColumnPosition:o,getColumnStartIndexForOffset:l,getColumnStopIndexForStartIndex:a,getEstimatedTotalHeight:n,getEstimatedTotalWidth:i,getColumnOffset:s,getRowOffset:m,getRowPosition:u,getRowStartIndexForOffset:c,getRowStopIndexForStartIndex:f,initCache:b,injectToInstance:D,validateProps:E})=>we({name:e??"ElVirtualList",props:Me,emits:[eo,to],setup(d,{emit:P,expose:N,slots:q}){const V=Ne("vl");E(d);const $=yt(),x=U(b(d,$));D==null||D($,x);const K=U(),G=U(),B=U(),X=U(null),_=U({isScrolling:!1,scrollLeft:re(d.initScrollLeft)?d.initScrollLeft:0,scrollTop:re(d.initScrollTop)?d.initScrollTop:0,updateRequested:!1,xAxisScrollDir:ot,yAxisScrollDir:ot}),T=sl(),A=M(()=>Number.parseInt(`${d.height}`,10)),F=M(()=>Number.parseInt(`${d.width}`,10)),Y=M(()=>{const{totalColumn:h,totalRow:g,columnCache:w}=d,{isScrolling:O,xAxisScrollDir:y,scrollLeft:H}=r(_);if(h===0||g===0)return[0,0,0,0];const S=l(d,H,r(x)),C=a(d,S,H,r(x)),v=O&&y!==oo?1:Math.max(1,w),I=O&&y!==ot?1:Math.max(1,w);return[Math.max(0,S-v),Math.max(0,Math.min(h-1,C+I)),S,C]}),ne=M(()=>{const{totalColumn:h,totalRow:g,rowCache:w}=d,{isScrolling:O,yAxisScrollDir:y,scrollTop:H}=r(_);if(h===0||g===0)return[0,0,0,0];const S=c(d,H,r(x)),C=f(d,S,H,r(x)),v=O&&y!==oo?1:Math.max(1,w),I=O&&y!==ot?1:Math.max(1,w);return[Math.max(0,S-v),Math.max(0,Math.min(g-1,C+I)),S,C]}),ee=M(()=>n(d,r(x))),le=M(()=>i(d,r(x))),xe=M(()=>{var h;return[{position:"relative",overflow:"hidden",WebkitOverflowScrolling:"touch",willChange:"transform"},{direction:d.direction,height:re(d.height)?`${d.height}px`:d.height,width:re(d.width)?`${d.width}px`:d.width},(h=d.style)!=null?h:{}]}),Re=M(()=>{const h=`${r(le)}px`;return{height:`${r(ee)}px`,pointerEvents:r(_).isScrolling?"none":void 0,width:h}}),ae=()=>{const{totalColumn:h,totalRow:g}=d;if(h>0&&g>0){const[C,v,I,L]=r(Y),[p,W,j,Q]=r(ne);P(eo,{columnCacheStart:C,columnCacheEnd:v,rowCacheStart:p,rowCacheEnd:W,columnVisibleStart:I,columnVisibleEnd:L,rowVisibleStart:j,rowVisibleEnd:Q})}const{scrollLeft:w,scrollTop:O,updateRequested:y,xAxisScrollDir:H,yAxisScrollDir:S}=r(_);P(to,{xAxisScrollDir:H,scrollLeft:w,yAxisScrollDir:S,scrollTop:O,updateRequested:y})},be=h=>{const{clientHeight:g,clientWidth:w,scrollHeight:O,scrollLeft:y,scrollTop:H,scrollWidth:S}=h.currentTarget,C=r(_);if(C.scrollTop===H&&C.scrollLeft===y)return;let v=y;if(ao(d.direction))switch(lo()){case ro:v=-y;break;case dl:v=S-w-y}_.value={...C,isScrolling:!0,scrollLeft:v,scrollTop:Math.max(0,Math.min(H,O-g)),updateRequested:!0,xAxisScrollDir:lt(C.scrollLeft,v),yAxisScrollDir:lt(C.scrollTop,H)},Pe(()=>ve()),Oe(),ae()},Se=(h,g)=>{const w=r(A),O=(ee.value-w)/g*h;se({scrollTop:Math.min(ee.value-w,O)})},ue=(h,g)=>{const w=r(F),O=(le.value-w)/g*h;se({scrollLeft:Math.min(le.value-w,O)})},{onWheel:ce}=(({atXEndEdge:h,atXStartEdge:g,atYEndEdge:w,atYStartEdge:O},y)=>{let H=null,S=0,C=0;const v=(I,L)=>{const p=I<=0&&g.value||I>=0&&h.value,W=L<=0&&O.value||L>=0&&w.value;return p&&W};return{hasReachedEdge:v,onWheel:I=>{_o(H);let L=I.deltaX,p=I.deltaY;Math.abs(L)>Math.abs(p)?p=0:L=0,I.shiftKey&&p!==0&&(L=p,p=0),v(S,C)&&v(S+L,C+p)||(S+=L,C+=p,I.preventDefault(),H=Lo(()=>{y(S,C),S=0,C=0}))}}})({atXStartEdge:M(()=>_.value.scrollLeft<=0),atXEndEdge:M(()=>_.value.scrollLeft>=le.value-r(F)),atYStartEdge:M(()=>_.value.scrollTop<=0),atYEndEdge:M(()=>_.value.scrollTop>=ee.value-r(A))},(h,g)=>{var w,O,y,H;(O=(w=G.value)==null?void 0:w.onMouseUp)==null||O.call(w),(H=(y=B.value)==null?void 0:y.onMouseUp)==null||H.call(y);const S=r(F),C=r(A);se({scrollLeft:Math.min(_.value.scrollLeft+h,le.value-S),scrollTop:Math.min(_.value.scrollTop+g,ee.value-C)})}),se=({scrollLeft:h=_.value.scrollLeft,scrollTop:g=_.value.scrollTop})=>{h=Math.max(h,0),g=Math.max(g,0);const w=r(_);g===w.scrollTop&&h===w.scrollLeft||(_.value={...w,xAxisScrollDir:lt(w.scrollLeft,h),yAxisScrollDir:lt(w.scrollTop,g),scrollLeft:h,scrollTop:g,updateRequested:!0},Pe(()=>ve()),Oe(),ae())},fe=(h,g)=>{const{columnWidth:w,direction:O,rowHeight:y}=d,H=T.value(t&&w,t&&y,t&&O),S=`${h},${g}`;if(jo(H,S))return H[S];{const[,C]=o(d,g,r(x)),v=r(x),I=ao(O),[L,p]=u(d,h,v),[W]=o(d,g,v);return H[S]={position:"absolute",left:I?void 0:`${C}px`,right:I?`${C}px`:void 0,top:`${p}px`,height:`${L}px`,width:`${W}px`},H[S]}},ve=()=>{_.value.isScrolling=!1,Pe(()=>{T.value(-1,null,null)})};Qe(()=>{if(!zo)return;const{initScrollLeft:h,initScrollTop:g}=d,w=r(K);w&&(re(h)&&(w.scrollLeft=h),re(g)&&(w.scrollTop=g)),ae()});const Oe=()=>{const{direction:h}=d,{scrollLeft:g,scrollTop:w,updateRequested:O}=r(_),y=r(K);if(O&&y){if(h===il)switch(lo()){case ro:y.scrollLeft=-g;break;case cl:y.scrollLeft=g;break;default:{const{clientWidth:H,scrollWidth:S}=y;y.scrollLeft=S-H-g;break}}else y.scrollLeft=Math.max(0,g);y.scrollTop=Math.max(0,w)}},{resetAfterColumnIndex:Te,resetAfterRowIndex:Ve,resetAfter:Ge}=$.proxy;N({windowRef:K,innerRef:X,getItemStyleCache:T,scrollTo:se,scrollToItem:(h=0,g=0,w=rt)=>{const O=r(_);g=Math.max(0,Math.min(g,d.totalColumn-1)),h=Math.max(0,Math.min(h,d.totalRow-1));const y=Fo(V.namespace.value),H=r(x),S=n(d,H),C=i(d,H);se({scrollLeft:s(d,g,w,O.scrollLeft,H,C>d.width?y:0),scrollTop:m(d,h,w,O.scrollTop,H,S>d.height?y:0)})},states:_,resetAfterColumnIndex:Te,resetAfterRowIndex:Ve,resetAfter:Ge});const z=()=>{const h=Ut(d.innerElement),g=(()=>{var w;const[O,y]=r(Y),[H,S]=r(ne),{data:C,totalColumn:v,totalRow:I,useIsScrolling:L,itemKey:p}=d,W=[];if(I>0&&v>0)for(let j=H;j<=S;j++)for(let Q=O;Q<=y;Q++)W.push((w=q.default)==null?void 0:w.call(q,{columnIndex:Q,data:C,key:p({columnIndex:Q,data:C,rowIndex:j}),isScrolling:L?r(_).isScrolling:void 0,style:fe(j,Q),rowIndex:j}));return W})();return[Ae(h,{style:r(Re),ref:X},Xt(h)?g:{default:()=>g})]};return()=>{const h=Ut(d.containerElement),{horizontalScrollbar:g,verticalScrollbar:w}=(()=>{const{scrollbarAlwaysOn:y,scrollbarStartGap:H,scrollbarEndGap:S,totalColumn:C,totalRow:v}=d,I=r(F),L=r(A),p=r(le),W=r(ee),{scrollLeft:j,scrollTop:Q}=r(_);return{horizontalScrollbar:Ae(no,{ref:G,alwaysOn:y,startGap:H,endGap:S,class:V.e("horizontal"),clientSize:I,layout:"horizontal",onScroll:ue,ratio:100*I/p,scrollFrom:j/(p-I),total:v,visible:!0}),verticalScrollbar:Ae(no,{ref:B,alwaysOn:y,startGap:H,endGap:S,class:V.e("vertical"),clientSize:L,layout:"vertical",onScroll:Se,ratio:100*L/W,scrollFrom:Q/(W-L),total:C,visible:!0})}})(),O=z();return Ae("div",{key:0,class:V.e("wrapper"),role:d.role},[Ae(h,{class:d.className,style:r(xe),onScroll:be,onWheel:ce,ref:K},Xt(h)?O:{default:()=>O}),g,w])}}}),uo=vt({name:"ElFixedSizeGrid",getColumnPosition:({columnWidth:e},t)=>[e,t*e],getRowPosition:({rowHeight:e},t)=>[e,t*e],getEstimatedTotalHeight:({totalRow:e,rowHeight:t})=>t*e,getEstimatedTotalWidth:({totalColumn:e,columnWidth:t})=>t*e,getColumnOffset:({totalColumn:e,columnWidth:t,width:o},l,a,n,i,s)=>{o=Number(o);const m=Math.max(0,e*t-o),u=Math.min(m,l*t),c=Math.max(0,l*t-o+s+t);switch(a==="smart"&&(a=n>=c-o&&n<=u+o?rt:_e),a){case St:return u;case bt:return c;case _e:{const f=Math.round(c+(u-c)/2);return f<Math.ceil(o/2)?0:f>m+Math.floor(o/2)?m:f}default:return n>=c&&n<=u?n:c>u||n<c?c:u}},getRowOffset:({rowHeight:e,height:t,totalRow:o},l,a,n,i,s)=>{t=Number(t);const m=Math.max(0,o*e-t),u=Math.min(m,l*e),c=Math.max(0,l*e-t+s+e);switch(a===so&&(a=n>=c-t&&n<=u+t?rt:_e),a){case St:return u;case bt:return c;case _e:{const f=Math.round(c+(u-c)/2);return f<Math.ceil(t/2)?0:f>m+Math.floor(t/2)?m:f}default:return n>=c&&n<=u?n:c>u||n<c?c:u}},getColumnStartIndexForOffset:({columnWidth:e,totalColumn:t},o)=>Math.max(0,Math.min(t-1,Math.floor(o/e))),getColumnStopIndexForStartIndex:({columnWidth:e,totalColumn:t,width:o},l,a)=>{const n=l*e,i=Math.ceil((o+a-n)/e);return Math.max(0,Math.min(t-1,l+i-1))},getRowStartIndexForOffset:({rowHeight:e,totalRow:t},o)=>Math.max(0,Math.min(t-1,Math.floor(o/e))),getRowStopIndexForStartIndex:({rowHeight:e,totalRow:t,height:o},l,a)=>{const n=l*e,i=Math.ceil((o+a-n)/e);return Math.max(0,Math.min(t-1,l+i-1))},initCache:()=>{},clearCache:!0,validateProps:({columnWidth:e,rowHeight:t})=>{}}),{max:qe,min:Tt,floor:Ht}=Math,ho={column:"columnWidth",row:"rowHeight"},at={column:"lastVisitedColumnIndex",row:"lastVisitedRowIndex"},de=(e,t,o,l)=>{const[a,n,i]=[o[l],e[ho[l]],o[at[l]]];if(t>i){let s=0;if(i>=0){const m=a[i];s=m.offset+m.size}for(let m=i+1;m<=t;m++){const u=n(m);a[m]={offset:s,size:u},s+=u}o[at[l]]=t}return a[t]},Ct=(e,t,o,l,a,n)=>{for(;o<=l;){const i=o+Ht((l-o)/2),s=de(e,i,t,n).offset;if(s===a)return i;s<a?o=i+1:l=i-1}return qe(0,o-1)},It=(e,t,o,l)=>{const[a,n]=[t[l],t[at[l]]];return(n>0?a[n].offset:0)>=o?Ct(e,t,0,n,o,l):((i,s,m,u,c)=>{const f=c==="column"?i.totalColumn:i.totalRow;let b=1;for(;m<f&&de(i,m,s,c).offset<u;)m+=b,b*=2;return Ct(i,s,Ht(m/2),Tt(m,f-1),u,c)})(e,t,qe(0,n),o,l)},Et=({totalRow:e},{estimatedRowHeight:t,lastVisitedRowIndex:o,row:l})=>{let a=0;if(o>=e&&(o=e-1),o>=0){const n=l[o];a=n.offset+n.size}return a+(e-o-1)*t},Mt=({totalColumn:e},{column:t,estimatedColumnWidth:o,lastVisitedColumnIndex:l})=>{let a=0;if(l>e&&(l=e-1),l>=0){const n=t[l];a=n.offset+n.size}return a+(e-l-1)*o},mo={column:Mt,row:Et},Ot=(e,t,o,l,a,n,i)=>{const[s,m]=[n==="row"?e.height:e.width,mo[n]],u=de(e,t,a,n),c=m(e,a),f=qe(0,Tt(c-s,u.offset)),b=qe(0,u.offset-s+i+u.size);switch(o===so&&(o=l>=b-s&&l<=f+s?rt:_e),o){case St:return f;case bt:return b;case _e:return Math.round(b+(f-b)/2);default:return l>=b&&l<=f?l:b>f||l<b?b:f}},fo=vt({name:"ElDynamicSizeGrid",getColumnPosition:(e,t,o)=>{const l=de(e,t,o,"column");return[l.size,l.offset]},getRowPosition:(e,t,o)=>{const l=de(e,t,o,"row");return[l.size,l.offset]},getColumnOffset:(e,t,o,l,a,n)=>Ot(e,t,o,l,a,"column",n),getRowOffset:(e,t,o,l,a,n)=>Ot(e,t,o,l,a,"row",n),getColumnStartIndexForOffset:(e,t,o)=>It(e,o,t,"column"),getColumnStopIndexForStartIndex:(e,t,o,l)=>{const a=de(e,t,l,"column"),n=o+e.width;let i=a.offset+a.size,s=t;for(;s<e.totalColumn-1&&i<n;)s++,i+=de(e,t,l,"column").size;return s},getEstimatedTotalHeight:Et,getEstimatedTotalWidth:Mt,getRowStartIndexForOffset:(e,t,o)=>It(e,o,t,"row"),getRowStopIndexForStartIndex:(e,t,o,l)=>{const{totalRow:a,height:n}=e,i=de(e,t,l,"row"),s=o+n;let m=i.size+i.offset,u=t;for(;u<a-1&&m<s;)u++,m+=de(e,u,l,"row").size;return u},injectToInstance:(e,t)=>{const o=({columnIndex:l,rowIndex:a},n)=>{var i,s;n=!!ko(n)||n,re(l)&&(t.value.lastVisitedColumnIndex=Math.min(t.value.lastVisitedColumnIndex,l-1)),re(a)&&(t.value.lastVisitedRowIndex=Math.min(t.value.lastVisitedRowIndex,a-1)),(i=e.exposed)==null||i.getItemStyleCache.value(-1,null,null),n&&((s=e.proxy)==null||s.$forceUpdate())};Object.assign(e.proxy,{resetAfterColumnIndex:(l,a)=>{o({columnIndex:l},a)},resetAfterRowIndex:(l,a)=>{o({rowIndex:l},a)},resetAfter:o})},initCache:({estimatedColumnWidth:e=io,estimatedRowHeight:t=io})=>({column:{},estimatedColumnWidth:e,estimatedRowHeight:t,lastVisitedColumnIndex:-1,lastVisitedRowIndex:-1,row:{}}),clearCache:!1,validateProps:({columnWidth:e,rowHeight:t})=>{}});var Le=(e=>(e.ASC="asc",e.DESC="desc",e))(Le||{}),ze=(e=>(e.CENTER="center",e.RIGHT="right",e))(ze||{}),Wt=(e=>(e.LEFT="left",e.RIGHT="right",e))(Wt||{});const nt={asc:"desc",desc:"asc"},Fe=Symbol("placeholder"),wo=(e,t,o)=>{var l;const a={flexGrow:0,flexShrink:0,...o?{}:{flexGrow:e.flexGrow||0,flexShrink:e.flexShrink||1}};o||(a.flexShrink=1);const n={...(l=e.style)!=null?l:{},...a,flexBasis:"auto",width:e.width};return t||(e.maxWidth&&(n.maxWidth=e.maxWidth),e.minWidth&&(n.minWidth=e.minWidth)),n},po=(e,{mainTableRef:t,leftTableRef:o,rightTableRef:l,tableInstance:a,ns:n,isScrolling:i})=>{const s=yt(),{emit:m}=s,u=De(!1),c=U(e.defaultExpandedRowKeys||[]),f=U(-1),b=De(null),D=U({}),E=U({}),d=De({}),P=De({}),N=De({}),q=M(()=>re(e.estimatedRowHeight)),V=Vo(()=>{var x,K,G,B;u.value=!0,D.value={...r(D),...r(E)},$(r(b),!1),E.value={},b.value=null,(x=t.value)==null||x.forceUpdate(),(K=o.value)==null||K.forceUpdate(),(G=l.value)==null||G.forceUpdate(),(B=s.proxy)==null||B.$forceUpdate(),u.value=!1},0);function $(x,K=!1){r(q)&&[t,o,l].forEach(G=>{const B=r(G);B&&B.resetAfterRowIndex(x,K)})}return{expandedRowKeys:c,lastRenderedRowIndex:f,isDynamic:q,isResetting:u,rowHeights:D,resetAfterIndex:$,onRowExpanded:function({expanded:x,rowData:K,rowIndex:G,rowKey:B}){var X,_;const T=[...r(c)],A=T.indexOf(B);x?A===-1&&T.push(B):A>-1&&T.splice(A,1),c.value=T,m("update:expandedRowKeys",T),(X=e.onRowExpand)==null||X.call(e,{expanded:x,rowData:K,rowIndex:G,rowKey:B}),(_=e.onExpandedRowsChange)==null||_.call(e,T)},onRowHovered:function({hovered:x,rowKey:K}){i.value||a.vnode.el.querySelectorAll(`[rowkey=${K}]`).forEach(G=>{x?G.classList.add(n.is("hovered")):G.classList.remove(n.is("hovered"))})},onRowsRendered:function(x){var K;(K=e.onRowsRendered)==null||K.call(e,x),x.rowCacheEnd>r(f)&&(f.value=x.rowCacheEnd)},onRowHeightChange:function({rowKey:x,height:K,rowIndex:G},B){B?B===Wt.RIGHT?N.value[x]=K:d.value[x]=K:P.value[x]=K;const X=Math.max(...[d,N,P].map(_=>_.value[x]||0));r(D)[x]!==X&&(function(_,T,A){const F=r(b);(F===null||F>A)&&(b.value=A),E.value[_]=T}(x,X,G),V())}}},go=(e,t)=>e+t,Ue=e=>$e(e)?e.reduce(go,0):e,pe=(e,t,o={})=>Rt(e)?e(t):e??o,me=e=>(["width","maxWidth","minWidth","height"].forEach(t=>{e[t]=et(e[t])}),e),At=e=>Ee(e)?t=>Ae(e,t):e;function yo(e){const t=U(),o=U(),l=U(),{columns:a,columnsStyles:n,columnsTotalWidth:i,fixedColumnsOnLeft:s,fixedColumnsOnRight:m,hasFixedColumns:u,mainColumns:c,onColumnSorted:f}=function(z,h,g){const w=M(()=>r(h).filter(p=>!p.hidden)),O=M(()=>r(w).filter(p=>p.fixed==="left"||p.fixed===!0)),y=M(()=>r(w).filter(p=>p.fixed==="right")),H=M(()=>r(w).filter(p=>!p.fixed)),S=M(()=>{const p=[];return r(O).forEach(W=>{p.push({...W,placeholderSign:Fe})}),r(H).forEach(W=>{p.push(W)}),r(y).forEach(W=>{p.push({...W,placeholderSign:Fe})}),p}),C=M(()=>r(O).length||r(y).length),v=M(()=>r(h).reduce((p,W)=>(p[W.key]=wo(W,r(g),z.fixed),p),{})),I=M(()=>r(w).reduce((p,W)=>p+W.width,0)),L=p=>r(h).find(W=>W.key===p);return{columns:h,columnsStyles:v,columnsTotalWidth:I,fixedColumnsOnLeft:O,fixedColumnsOnRight:y,hasFixedColumns:C,mainColumns:S,normalColumns:H,visibleColumns:w,getColumn:L,getColumnStyle:p=>r(v)[p],updateColumnWidth:(p,W)=>{p.width=W},onColumnSorted:function(p){var W;const{key:j}=p.currentTarget.dataset;if(!j)return;const{sortState:Q,sortBy:He}=z;let Ce=Le.ASC;Ce=xt(Q)?nt[Q[j]]:nt[He.order],(W=z.onColumnSort)==null||W.call(z,{column:L(j),key:j,order:Ce})}}}(e,Yt(e,"columns"),Yt(e,"fixed")),{scrollTo:b,scrollToLeft:D,scrollToTop:E,scrollToRow:d,onScroll:P,onVerticalScroll:N,scrollPos:q}=((z,{mainTableRef:h,leftTableRef:g,rightTableRef:w,onMaybeEndReached:O})=>{const y=U({scrollLeft:0,scrollTop:0});function H(v){var I,L,p;const{scrollTop:W}=v;(I=h.value)==null||I.scrollTo(v),(L=g.value)==null||L.scrollToTop(W),(p=w.value)==null||p.scrollToTop(W)}function S(v){y.value=v,H(v)}function C(v){y.value.scrollTop=v,H(r(y))}return Ze(()=>r(y).scrollTop,(v,I)=>{v>I&&O()}),{scrollPos:y,scrollTo:S,scrollToLeft:function(v){var I,L;y.value.scrollLeft=v,(L=(I=h.value)==null?void 0:I.scrollTo)==null||L.call(I,r(y))},scrollToTop:C,scrollToRow:function(v,I="auto"){var L;(L=h.value)==null||L.scrollToRow(v,I)},onScroll:function(v){var I;S(v),(I=z.onScroll)==null||I.call(z,v)},onVerticalScroll:function({scrollTop:v}){const{scrollTop:I}=r(y);v!==I&&C(v)}}})(e,{mainTableRef:t,leftTableRef:o,rightTableRef:l,onMaybeEndReached:function(){const{onEndReached:z}=e;if(!z)return;const{scrollTop:h}=r(q),g=r(ce),w=r(se),O=g-(h+w)+e.hScrollbarSize;r(G)>=0&&g===h+r(ae)-r(Te)&&z(O)}}),V=Ne("table-v2"),$=yt(),x=De(!1),{expandedRowKeys:K,lastRenderedRowIndex:G,isDynamic:B,isResetting:X,rowHeights:_,resetAfterIndex:T,onRowExpanded:A,onRowHeightChange:F,onRowHovered:Y,onRowsRendered:ne}=po(e,{mainTableRef:t,leftTableRef:o,rightTableRef:l,tableInstance:$,ns:V,isScrolling:x}),{data:ee,depthMap:le}=((z,{expandedRowKeys:h,lastRenderedRowIndex:g,resetAfterIndex:w})=>{const O=U({}),y=M(()=>{const S={},{data:C,rowKey:v}=z,I=r(h);if(!I||!I.length)return C;const L=[],p=new Set;I.forEach(j=>p.add(j));let W=C.slice();for(W.forEach(j=>S[j[v]]=0);W.length>0;){const j=W.shift();L.push(j),p.has(j[v])&&Array.isArray(j.children)&&j.children.length>0&&(W=[...j.children,...W],j.children.forEach(Q=>S[Q[v]]=S[j[v]]+1))}return O.value=S,L}),H=M(()=>{const{data:S,expandColumnKey:C}=z;return C?r(y):S});return Ze(H,(S,C)=>{S!==C&&(g.value=-1,w(0,!0))}),{data:H,depthMap:O}})(e,{expandedRowKeys:K,lastRenderedRowIndex:G,resetAfterIndex:T}),{bodyWidth:xe,fixedTableHeight:Re,mainTableHeight:ae,leftTableWidth:be,rightTableWidth:Se,headerWidth:ue,rowsHeight:ce,windowHeight:se,footerHeight:fe,emptyStyle:ve,rootStyle:Oe,headerHeight:Te}=((z,{columnsTotalWidth:h,data:g,fixedColumnsOnLeft:w,fixedColumnsOnRight:O})=>{const y=M(()=>{const{fixed:J,width:oe,vScrollbarSize:Z}=z,We=oe-Z;return J?Math.max(Math.round(r(h)),We):We}),H=M(()=>r(y)+z.vScrollbarSize),S=M(()=>{const{height:J=0,maxHeight:oe=0,footerHeight:Z,hScrollbarSize:We}=z;if(oe>0){const Ye=r(j),pt=r(C),Je=r(W)+Ye+pt+We;return Math.min(Je,oe-Z)}return J-Z}),C=M(()=>{const{rowHeight:J,estimatedRowHeight:oe}=z,Z=r(g);return re(oe)?Z.length*oe:Z.length*J}),v=M(()=>{const{maxHeight:J}=z,oe=r(S);if(re(J)&&J>0)return oe;const Z=r(C)+r(W)+r(j);return Math.min(oe,Z)}),I=J=>J.width,L=M(()=>Ue(r(w).map(I))),p=M(()=>Ue(r(O).map(I))),W=M(()=>Ue(z.headerHeight)),j=M(()=>{var J;return(((J=z.fixedData)==null?void 0:J.length)||0)*z.rowHeight}),Q=M(()=>r(S)-r(W)-r(j)),He=M(()=>{const{style:J={},height:oe,width:Z}=z;return me({...J,height:oe,width:Z})}),Ce=M(()=>me({height:z.footerHeight})),wt=M(()=>({top:et(r(W)),bottom:et(z.footerHeight),width:et(z.width)}));return{bodyWidth:y,fixedTableHeight:v,mainTableHeight:S,leftTableWidth:L,rightTableWidth:p,headerWidth:H,rowsHeight:C,windowHeight:Q,footerHeight:Ce,emptyStyle:wt,rootStyle:He,headerHeight:W}})(e,{columnsTotalWidth:i,data:ee,fixedColumnsOnLeft:s,fixedColumnsOnRight:m}),Ve=U(),Ge=M(()=>{const z=r(ee).length===0;return $e(e.fixedData)?e.fixedData.length===0&&z:z});return Ze(()=>e.expandedRowKeys,z=>K.value=z,{deep:!0}),{columns:a,containerRef:Ve,mainTableRef:t,leftTableRef:o,rightTableRef:l,isDynamic:B,isResetting:X,isScrolling:x,hasFixedColumns:u,columnsStyles:n,columnsTotalWidth:i,data:ee,expandedRowKeys:K,depthMap:le,fixedColumnsOnLeft:s,fixedColumnsOnRight:m,mainColumns:c,bodyWidth:xe,emptyStyle:ve,rootStyle:Oe,headerWidth:ue,footerHeight:fe,mainTableHeight:ae,fixedTableHeight:Re,leftTableWidth:be,rightTableWidth:Se,showEmpty:Ge,getRowHeight:function(z){const{estimatedRowHeight:h,rowHeight:g,rowKey:w}=e;return h?r(_)[r(ee)[z][w]]||h:g},onColumnSorted:f,onRowHovered:Y,onRowExpanded:A,onRowsRendered:ne,onRowHeightChange:F,scrollTo:b,scrollToLeft:D,scrollToTop:E,scrollToRow:d,onScroll:P,onVerticalScroll:N}}const st=Symbol("tableV2"),Dt=String,je={type:k(Array),required:!0},it={type:k(Array)},Kt={...it,required:!0},xo=String,_t={type:k(Array),default:()=>Po([])},ge={type:Number,required:!0},Lt={type:k([String,Number,Symbol]),default:"id"},zt={type:k(Object)},ye=Ke({class:String,columns:je,columnsStyles:{type:k(Object),required:!0},depth:Number,expandColumnKey:xo,estimatedRowHeight:{...Me.estimatedRowHeight,default:void 0},isScrolling:Boolean,onRowExpand:{type:k(Function)},onRowHover:{type:k(Function)},onRowHeightChange:{type:k(Function)},rowData:{type:k(Object),required:!0},rowEventHandlers:{type:k(Object)},rowIndex:{type:Number,required:!0},rowKey:Lt,style:{type:k(Object)}}),ct={type:Number,required:!0},dt=Ke({class:String,columns:je,fixedHeaderData:{type:k(Array)},headerData:{type:k(Array),required:!0},headerHeight:{type:k([Number,Array]),default:50},rowWidth:ct,rowHeight:{type:Number,default:50},height:ct,width:ct}),Xe=Ke({columns:je,data:Kt,fixedData:it,estimatedRowHeight:ye.estimatedRowHeight,width:ge,height:ge,headerWidth:ge,headerHeight:dt.headerHeight,bodyWidth:ge,rowHeight:ge,cache:ul.cache,useIsScrolling:Boolean,scrollbarAlwaysOn:Me.scrollbarAlwaysOn,scrollbarStartGap:Me.scrollbarStartGap,scrollbarEndGap:Me.scrollbarEndGap,class:Dt,style:zt,containerStyle:zt,getRowHeight:{type:k(Function),required:!0},rowKey:ye.rowKey,onRowsRendered:{type:k(Function)},onScroll:{type:k(Function)}}),Ro=Ke({cache:Xe.cache,estimatedRowHeight:ye.estimatedRowHeight,rowKey:Lt,headerClass:{type:k([String,Function])},headerProps:{type:k([Object,Function])},headerCellProps:{type:k([Object,Function])},headerHeight:dt.headerHeight,footerHeight:{type:Number,default:0},rowClass:{type:k([String,Function])},rowProps:{type:k([Object,Function])},rowHeight:{type:Number,default:50},cellProps:{type:k([Object,Function])},columns:je,data:Kt,dataGetter:{type:k(Function)},fixedData:it,expandColumnKey:ye.expandColumnKey,expandedRowKeys:_t,defaultExpandedRowKeys:_t,class:Dt,fixed:Boolean,style:{type:k(Object)},width:ge,height:ge,maxHeight:Number,useIsScrolling:Boolean,indentSize:{type:Number,default:12},iconSize:{type:Number,default:12},hScrollbarSize:Me.hScrollbarSize,vScrollbarSize:Me.vScrollbarSize,scrollbarAlwaysOn:hl.alwaysOn,sortBy:{type:k(Object),default:()=>({})},sortState:{type:k(Object),default:void 0},onColumnSort:{type:k(Function)},onExpandedRowsChange:{type:k(Function)},onEndReached:{type:k(Function)},onRowExpand:ye.onRowExpand,onScroll:Xe.onScroll,onRowsRendered:Xe.onRowsRendered,rowEventHandlers:ye.rowEventHandlers}),ut=(e,{slots:t})=>{var o;const{cellData:l,style:a}=e,n=((o=l==null?void 0:l.toString)==null?void 0:o.call(l))||"",i=Be(t,"default",e,()=>[n]);return R("div",{class:e.class,title:n,style:a},[i])};ut.displayName="ElTableV2Cell",ut.inheritAttrs=!1;const ht=(e,{slots:t})=>Be(t,"default",e,()=>{var o,l;return[R("div",{class:e.class,title:(o=e.column)==null?void 0:o.title},[(l=e.column)==null?void 0:l.title])]});ht.displayName="ElTableV2HeaderCell",ht.inheritAttrs=!1;const bo=Ke({class:String,columns:je,columnsStyles:{type:k(Object),required:!0},headerIndex:Number,style:{type:k(Object)}}),So=we({name:"ElTableV2HeaderRow",props:bo,setup:(e,{slots:t})=>()=>{const{columns:o,columnsStyles:l,headerIndex:a,style:n}=e;let i=o.map((s,m)=>t.cell({columns:o,column:s,columnIndex:m,headerIndex:a,style:l[s.key]}));return t.header&&(i=t.header({cells:i.map(s=>$e(s)&&s.length===1?s[0]:s),columns:o,headerIndex:a})),R("div",{class:e.class,style:n,role:"row"},[i])}}),vo=we({name:"ElTableV2Header",props:dt,setup(e,{slots:t,expose:o}){const l=Ne("table-v2"),a=U(),n=M(()=>me({width:e.width,height:e.height})),i=M(()=>me({width:e.rowWidth,height:e.height})),s=M(()=>$o(r(e.headerHeight))),m=()=>{const c=l.e("fixed-header-row"),{columns:f,fixedHeaderData:b,rowHeight:D}=e;return b==null?void 0:b.map((E,d)=>{var P;const N=me({height:D,width:"100%"});return(P=t.fixed)==null?void 0:P.call(t,{class:c,columns:f,rowData:E,rowIndex:-(d+1),style:N})})},u=()=>{const c=l.e("dynamic-header-row"),{columns:f}=e;return r(s).map((b,D)=>{var E;const d=me({width:"100%",height:b});return(E=t.dynamic)==null?void 0:E.call(t,{class:c,columns:f,headerIndex:D,style:d})})};return o({scrollToLeft:c=>{const f=r(a);Pe(()=>{f!=null&&f.scroll&&f.scroll({left:c})})}}),()=>{if(!(e.height<=0))return R("div",{ref:a,class:e.class,style:r(n),role:"rowgroup"},[R("div",{style:r(i),class:l.e("header")},[u(),m()])])}}}),To=e=>{const{isScrolling:t}=Jt(st),o=U(!1),l=U(),a=M(()=>re(e.estimatedRowHeight)&&e.rowIndex>=0),n=M(()=>{const{rowData:i,rowIndex:s,rowKey:m,onRowHover:u}=e,c=e.rowEventHandlers||{},f={};return Object.entries(c).forEach(([b,D])=>{Rt(D)&&(f[b]=E=>{D({event:E,rowData:i,rowIndex:s,rowKey:m})})}),u&&[{name:"onMouseleave",hovered:!1},{name:"onMouseenter",hovered:!0}].forEach(({name:b,hovered:D})=>{const E=f[b];f[b]=d=>{u({event:d,hovered:D,rowData:i,rowIndex:s,rowKey:m}),E==null||E(d)}}),f});return Qe(()=>{r(a)&&((i=!1)=>{const s=r(l);if(!s)return;const{columns:m,onRowHeightChange:u,rowKey:c,rowIndex:f,style:b}=e,{height:D}=s.getBoundingClientRect();o.value=!0,Pe(()=>{if(i||D!==Number.parseInt(b.height)){const E=m[0],d=(E==null?void 0:E.placeholderSign)===Fe;u==null||u({rowKey:c,height:D,rowIndex:f},E&&!d&&E.fixed)}})})(!0)}),{isScrolling:t,measurable:a,measured:o,rowRef:l,eventHandlers:n,onExpand:i=>{const{onRowExpand:s,rowData:m,rowIndex:u,rowKey:c}=e;s==null||s({expanded:i,rowData:m,rowIndex:u,rowKey:c})}}},Ho=we({name:"ElTableV2TableRow",props:ye,setup(e,{expose:t,slots:o,attrs:l}){const{eventHandlers:a,isScrolling:n,measurable:i,measured:s,rowRef:m,onExpand:u}=To(e);return t({onExpand:u}),()=>{const{columns:c,columnsStyles:f,expandColumnKey:b,depth:D,rowData:E,rowIndex:d,style:P}=e;let N=c.map((q,V)=>{const $=$e(E.children)&&E.children.length>0&&q.key===b;return o.cell({column:q,columns:c,columnIndex:V,depth:D,style:f[q.key],rowData:E,rowIndex:d,isScrolling:r(n),expandIconProps:$?{rowData:E,rowIndex:d,onExpand:u}:void 0})});if(o.row&&(N=o.row({cells:N.map(q=>$e(q)&&q.length===1?q[0]:q),style:P,columns:c,depth:D,rowData:E,rowIndex:d,isScrolling:r(n)})),r(i)){const{height:q,...V}=P||{},$=r(s);return R("div",te({ref:m,class:e.class,style:$?P:V,role:"row"},l,r(a)),[N])}return R("div",te(l,{ref:m,class:e.class,style:P,role:"row"},r(a)),[N])}}}),Co=e=>{const{sortOrder:t}=e;return R(Qt,{size:14,class:e.class},{default:()=>[t===Le.ASC?R(Bo,null,null):R(qo,null,null)]})},Io=e=>{const{expanded:t,expandable:o,onExpand:l,style:a,size:n}=e,i={onClick:o?()=>l(!t):void 0,class:e.class};return R(Qt,te(i,{size:n,style:a}),{default:()=>[R(Uo,null,null)]})},mt=we({name:"ElTableV2Grid",props:Xe,setup(e,{slots:t,expose:o}){const{ns:l}=Jt(st),{bodyRef:a,fixedRowHeight:n,gridHeight:i,hasHeader:s,headerRef:m,headerHeight:u,totalHeight:c,forceUpdate:f,itemKey:b,onItemRendered:D,resetAfterRowIndex:E,scrollTo:d,scrollToTop:P,scrollToRow:N}=(V=>{const $=U(),x=U(),K=M(()=>{const{data:T,rowHeight:A,estimatedRowHeight:F}=V;if(!F)return T.length*A}),G=M(()=>{const{fixedData:T,rowHeight:A}=V;return((T==null?void 0:T.length)||0)*A}),B=M(()=>Ue(V.headerHeight)),X=M(()=>{const{height:T}=V;return Math.max(0,T-r(B)-r(G))}),_=M(()=>r(B)+r(G)>0);return{bodyRef:x,forceUpdate:function(){var T,A;(T=r(x))==null||T.$forceUpdate(),(A=r($))==null||A.$forceUpdate()},fixedRowHeight:G,gridHeight:X,hasHeader:_,headerHeight:B,headerRef:$,totalHeight:K,itemKey:({data:T,rowIndex:A})=>T[A][V.rowKey],onItemRendered:function({rowCacheStart:T,rowCacheEnd:A,rowVisibleStart:F,rowVisibleEnd:Y}){var ne;(ne=V.onRowsRendered)==null||ne.call(V,{rowCacheStart:T,rowCacheEnd:A,rowVisibleStart:F,rowVisibleEnd:Y})},resetAfterRowIndex:function(T,A){var F;(F=x.value)==null||F.resetAfterRowIndex(T,A)},scrollTo:function(T,A){const F=r($),Y=r(x);F&&Y&&(xt(T)?(F.scrollToLeft(T.scrollLeft),Y.scrollTo(T)):(F.scrollToLeft(T),Y.scrollTo({scrollLeft:T,scrollTop:A})))},scrollToTop:function(T){var A;(A=r(x))==null||A.scrollTo({scrollTop:T})},scrollToRow:function(T,A){var F;(F=r(x))==null||F.scrollToItem(T,1,A)}}})(e);o({forceUpdate:f,totalHeight:c,scrollTo:d,scrollToTop:P,scrollToRow:N,resetAfterRowIndex:E});const q=()=>e.bodyWidth;return()=>{const{cache:V,columns:$,data:x,fixedData:K,useIsScrolling:G,scrollbarAlwaysOn:B,scrollbarEndGap:X,scrollbarStartGap:_,style:T,rowHeight:A,bodyWidth:F,estimatedRowHeight:Y,headerWidth:ne,height:ee,width:le,getRowHeight:xe,onScroll:Re}=e,ae=re(Y),be=ae?fo:uo,Se=r(u);return R("div",{role:"table",class:[l.e("table"),e.class],style:T},[R(be,{ref:a,data:x,useIsScrolling:G,itemKey:b,columnCache:0,columnWidth:ae?q:F,totalColumn:1,totalRow:x.length,rowCache:V,rowHeight:ae?xe:A,width:le,height:r(i),class:l.e("body"),role:"rowgroup",scrollbarStartGap:_,scrollbarEndGap:X,scrollbarAlwaysOn:B,onScroll:Re,onItemRendered:D,perfMode:!1},{default:ue=>{var ce;const se=x[ue.rowIndex];return(ce=t.row)==null?void 0:ce.call(t,{...ue,columns:$,rowData:se})}}),r(s)&&R(vo,{ref:m,class:l.e("header-wrapper"),columns:$,headerData:x,headerHeight:e.headerHeight,fixedHeaderData:K,rowWidth:ne,rowHeight:A,width:le,height:Math.min(Se+r(n),ee)},{dynamic:t.header,fixed:t.row})])}}}),Eo=(e,{slots:t})=>{const{mainTableRef:o,...l}=e;return R(mt,te({ref:o},l),typeof(a=t)=="function"||Object.prototype.toString.call(a)==="[object Object]"&&!Ee(a)?t:{default:()=>[t]});var a},Mo=(e,{slots:t})=>{if(!e.columns.length)return;const{leftTableRef:o,...l}=e;return R(mt,te({ref:o},l),typeof(a=t)=="function"||Object.prototype.toString.call(a)==="[object Object]"&&!Ee(a)?t:{default:()=>[t]});var a},Oo=(e,{slots:t})=>{if(!e.columns.length)return;const{rightTableRef:o,...l}=e;return R(mt,te({ref:o},l),typeof(a=t)=="function"||Object.prototype.toString.call(a)==="[object Object]"&&!Ee(a)?t:{default:()=>[t]});var a},Wo=(e,{slots:t})=>{const{columns:o,columnsStyles:l,depthMap:a,expandColumnKey:n,expandedRowKeys:i,estimatedRowHeight:s,hasFixedColumns:m,rowData:u,rowIndex:c,style:f,isScrolling:b,rowProps:D,rowClass:E,rowKey:d,rowEventHandlers:P,ns:N,onRowHovered:q,onRowExpanded:V}=e,$=pe(E,{columns:o,rowData:u,rowIndex:c},""),x=pe(D,{columns:o,rowData:u,rowIndex:c}),K=u[d],G=a[K]||0,B=!!n,X=c<0,_=[N.e("row"),$,{[N.e(`row-depth-${G}`)]:B&&c>=0,[N.is("expanded")]:B&&i.includes(K),[N.is("fixed")]:!G&&X,[N.is("customized")]:!!t.row}],T=m?q:void 0,A={...x,columns:o,columnsStyles:l,class:_,depth:G,expandColumnKey:n,estimatedRowHeight:X?void 0:s,isScrolling:b,rowIndex:c,rowData:u,rowKey:K,rowEventHandlers:P,style:f};return R(Ho,te(A,{onRowExpand:V,onMouseenter:Y=>{T==null||T({hovered:!0,rowKey:K,event:Y,rowData:u,rowIndex:c})},onMouseleave:Y=>{T==null||T({hovered:!1,rowKey:K,event:Y,rowData:u,rowIndex:c})},rowkey:K}),typeof(F=t)=="function"||Object.prototype.toString.call(F)==="[object Object]"&&!Ee(F)?t:{default:()=>[t]});var F},ft=({columns:e,column:t,columnIndex:o,depth:l,expandIconProps:a,isScrolling:n,rowData:i,rowIndex:s,style:m,expandedRowKeys:u,ns:c,cellProps:f,expandColumnKey:b,indentSize:D,iconSize:E,rowKey:d},{slots:P})=>{const N=me(m);if(t.placeholderSign===Fe)return R("div",{class:c.em("row-cell","placeholder"),style:N},null);const{cellRenderer:q,dataKey:V,dataGetter:$}=t,x=Rt($)?$({columns:e,column:t,columnIndex:o,rowData:i,rowIndex:s}):Xo(i,V??""),K=pe(f,{cellData:x,columns:e,column:t,columnIndex:o,rowIndex:s,rowData:i}),G={class:c.e("cell-text"),columns:e,column:t,columnIndex:o,cellData:x,isScrolling:n,rowData:i,rowIndex:s},B=At(q),X=B?B(G):Be(P,"default",G,()=>[R(ut,G,null)]),_=[c.e("row-cell"),t.class,t.align===ze.CENTER&&c.is("align-center"),t.align===ze.RIGHT&&c.is("align-right")],T=s>=0&&b&&t.key===b,A=s>=0&&u.includes(i[d]);let F;const Y=`margin-inline-start: ${l*D}px;`;return T&&(F=xt(a)?R(Io,te(a,{class:[c.e("expand-icon"),c.is("expanded",A)],size:E,expanded:A,style:Y,expandable:!0}),null):R("div",{style:[Y,`width: ${E}px; height: ${E}px;`].join(" ")},null)),R("div",te({class:_,style:N},K,{role:"cell"}),[F,X])};ft.inheritAttrs=!1;const Ao=({columns:e,columnsStyles:t,headerIndex:o,style:l,headerClass:a,headerProps:n,ns:i},{slots:s})=>{const m={columns:e,headerIndex:o},u=[i.e("header-row"),pe(a,m,""),{[i.is("customized")]:!!s.header}],c={...pe(n,m),columnsStyles:t,class:u,columns:e,headerIndex:o,style:l};return R(So,c,typeof(f=s)=="function"||Object.prototype.toString.call(f)==="[object Object]"&&!Ee(f)?s:{default:()=>[s]});var f},Ft=(e,{slots:t})=>{const{column:o,ns:l,style:a,onColumnSorted:n}=e,i=me(a);if(o.placeholderSign===Fe)return R("div",{class:l.em("header-row-cell","placeholder"),style:i},null);const{headerCellRenderer:s,headerClass:m,sortable:u}=o,c={...e,class:l.e("header-cell-text")},f=At(s),b=f?f(c):Be(t,"default",c,()=>[R(ht,c,null)]),{sortBy:D,sortState:E,headerCellProps:d}=e;let P,N;if(E){const $=E[o.key];P=!!nt[$],N=P?$:Le.ASC}else P=o.key===D.key,N=P?D.order:Le.ASC;const q=[l.e("header-cell"),pe(m,e,""),o.align===ze.CENTER&&l.is("align-center"),o.align===ze.RIGHT&&l.is("align-right"),u&&l.is("sortable")],V={...pe(d,e),onClick:o.sortable?n:void 0,class:q,style:i,"data-key":o.key};return R("div",te(V,{role:"columnheader"}),[b,u&&R(Co,{class:[l.e("sort-icon"),P&&l.is("sorting")],sortOrder:N},null)])},jt=(e,{slots:t})=>{var o;return R("div",{class:e.class,style:e.style},[(o=t.default)==null?void 0:o.call(t)])};jt.displayName="ElTableV2Footer";const kt=(e,{slots:t})=>{const o=Be(t,"default",{},()=>[R(al,null,null)]);return R("div",{class:e.class,style:e.style},[o])};kt.displayName="ElTableV2Empty";const Vt=(e,{slots:t})=>{var o;return R("div",{class:e.class,style:e.style},[(o=t.default)==null?void 0:o.call(t)])};function ke(e){return typeof e=="function"||Object.prototype.toString.call(e)==="[object Object]"&&!Ee(e)}Vt.displayName="ElTableV2Overlay";let Gt,Nt,Pt,$t,Bt,qt;Gt=we({name:"ElTableV2",props:Ro,setup(e,{slots:t,expose:o}){const l=Ne("table-v2"),{columnsStyles:a,fixedColumnsOnLeft:n,fixedColumnsOnRight:i,mainColumns:s,mainTableHeight:m,fixedTableHeight:u,leftTableWidth:c,rightTableWidth:f,data:b,depthMap:D,expandedRowKeys:E,hasFixedColumns:d,mainTableRef:P,leftTableRef:N,rightTableRef:q,isDynamic:V,isResetting:$,isScrolling:x,bodyWidth:K,emptyStyle:G,rootStyle:B,headerWidth:X,footerHeight:_,showEmpty:T,scrollTo:A,scrollToLeft:F,scrollToTop:Y,scrollToRow:ne,getRowHeight:ee,onColumnSorted:le,onRowHeightChange:xe,onRowHovered:Re,onRowExpanded:ae,onRowsRendered:be,onScroll:Se,onVerticalScroll:ue}=yo(e);return o({scrollTo:A,scrollToLeft:F,scrollToTop:Y,scrollToRow:ne}),Yo(st,{ns:l,isResetting:$,isScrolling:x}),()=>{const{cache:ce,cellProps:se,estimatedRowHeight:fe,expandColumnKey:ve,fixedData:Oe,headerHeight:Te,headerClass:Ve,headerProps:Ge,headerCellProps:z,sortBy:h,sortState:g,rowHeight:w,rowClass:O,rowEventHandlers:y,rowKey:H,rowProps:S,scrollbarAlwaysOn:C,indentSize:v,iconSize:I,useIsScrolling:L,vScrollbarSize:p,width:W}=e,j=r(b),Q={cache:ce,class:l.e("main"),columns:r(s),data:j,fixedData:Oe,estimatedRowHeight:fe,bodyWidth:r(K)+p,headerHeight:Te,headerWidth:r(X),height:r(m),mainTableRef:P,rowKey:H,rowHeight:w,scrollbarAlwaysOn:C,scrollbarStartGap:2,scrollbarEndGap:p,useIsScrolling:L,width:W,getRowHeight:ee,onRowsRendered:be,onScroll:Se},He=r(c),Ce=r(u),wt={cache:ce,class:l.e("left"),columns:r(n),data:j,estimatedRowHeight:fe,leftTableRef:N,rowHeight:w,bodyWidth:He,headerWidth:He,headerHeight:Te,height:Ce,rowKey:H,scrollbarAlwaysOn:C,scrollbarStartGap:2,scrollbarEndGap:p,useIsScrolling:L,width:He,getRowHeight:ee,onScroll:ue},J=r(f)+p,oe={cache:ce,class:l.e("right"),columns:r(i),data:j,estimatedRowHeight:fe,rightTableRef:q,rowHeight:w,bodyWidth:J,headerWidth:J,headerHeight:Te,height:Ce,rowKey:H,scrollbarAlwaysOn:C,scrollbarStartGap:2,scrollbarEndGap:p,width:J,style:`--${r(l.namespace)}-table-scrollbar-size: ${p}px`,useIsScrolling:L,getRowHeight:ee,onScroll:ue},Z=r(a),We={ns:l,depthMap:r(D),columnsStyles:Z,expandColumnKey:ve,expandedRowKeys:r(E),estimatedRowHeight:fe,hasFixedColumns:r(d),rowProps:S,rowClass:O,rowKey:H,rowEventHandlers:y,onRowHovered:Re,onRowExpanded:ae,onRowHeightChange:xe},Ye={cellProps:se,expandColumnKey:ve,indentSize:v,iconSize:I,rowKey:H,expandedRowKeys:r(E),ns:l},pt={ns:l,headerClass:Ve,headerProps:Ge,columnsStyles:Z},Je={ns:l,sortBy:h,sortState:g,headerCellProps:z,onColumnSorted:le},he={row:gt=>R(Wo,te(gt,We),{row:t.row,cell:ie=>{let Ie;return t.cell?R(ft,te(ie,Ye,{style:Z[ie.column.key]}),ke(Ie=t.cell(ie))?Ie:{default:()=>[Ie]}):R(ft,te(ie,Ye,{style:Z[ie.column.key]}),null)}}),header:gt=>R(Ao,te(gt,pt),{header:t.header,cell:ie=>{let Ie;return t["header-cell"]?R(Ft,te(ie,Je,{style:Z[ie.column.key]}),ke(Ie=t["header-cell"](ie))?Ie:{default:()=>[Ie]}):R(Ft,te(ie,Je,{style:Z[ie.column.key]}),null)}})},Do=[e.class,l.b(),l.e("root"),{[l.is("dynamic")]:r(V)}],Ko={class:l.e("footer"),style:r(_)};return R("div",{class:Do,style:r(B)},[R(Eo,Q,ke(he)?he:{default:()=>[he]}),R(Mo,wt,ke(he)?he:{default:()=>[he]}),R(Oo,oe,ke(he)?he:{default:()=>[he]}),t.footer&&R(jt,Ko,{default:t.footer}),r(T)&&R(kt,{class:l.e("empty"),style:r(G)},{default:t.empty}),t.overlay&&R(Vt,{class:l.e("overlay")},{default:t.overlay})])}}}),Nt=Ke({disableWidth:Boolean,disableHeight:Boolean,onResize:{type:k(Function)}}),Pt=we({name:"ElAutoResizer",props:Nt,setup(e,{slots:t}){const o=Ne("auto-resizer"),{height:l,width:a,sizer:n}=(s=>{const m=U(),u=U(0),c=U(0);let f;return Qe(()=>{f=Go(m,([b])=>{const{width:D,height:E}=b.contentRect,{paddingLeft:d,paddingRight:P,paddingTop:N,paddingBottom:q}=getComputedStyle(b.target),V=Number.parseInt(d)||0,$=Number.parseInt(P)||0,x=Number.parseInt(N)||0,K=Number.parseInt(q)||0;u.value=D-V-$,c.value=E-x-K}).stop}),No(()=>{f==null||f()}),Ze([u,c],([b,D])=>{var E;(E=s.onResize)==null||E.call(s,{width:b,height:D})}),{sizer:m,width:u,height:c}})(e),i={width:"100%",height:"100%"};return()=>{var s;return R("div",{ref:n,class:o.b(),style:i},[(s=t.default)==null?void 0:s.call(t,{height:l.value,width:a.value})])}}}),$t=Zt(Gt),Bt=Zt(Pt),qt={style:{width:"100%",height:"700px"}},co=we({name:"SystemArea",__name:"index",setup(e){const t=[{dataKey:"id",title:"\u7F16\u53F7",width:400,fixed:!0,key:"id"},{dataKey:"name",title:"\u5730\u540D",width:200}],o=U([]),l=U();return Qe(()=>{(async()=>o.value=await Rl())()}),(a,n)=>{const i=pl,s=ol,m=ll,u=fl,c=$t,f=Bt;return Jo(),Qo(tl,null,[R(i,{title:"\u5730\u533A & IP",url:"https://doc.iocoder.cn/area-and-ip/"}),R(u,null,{default:tt(()=>[R(m,{type:"primary",plain:"",onClick:n[0]||(n[0]=b=>{l.value.open()})},{default:tt(()=>[R(s,{icon:"ep:plus",class:"mr-5px"}),Zo(" IP \u67E5\u8BE2 ")]),_:1})]),_:1}),R(u,null,{default:tt(()=>[el("div",qt,[R(f,null,{default:tt(({height:b,width:D})=>[R(c,{columns:t,data:r(o),width:D,height:b,"expand-column-key":"id"},null,8,["data","width","height"])]),_:1})])]),_:1}),R(yl,{ref_key:"formRef",ref:l},null,512)],64)}}})});export{Tl as __tla,co as default};
