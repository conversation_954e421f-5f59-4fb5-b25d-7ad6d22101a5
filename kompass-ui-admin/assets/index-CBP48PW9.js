import{_ as a,__tla as l}from"./index-COobLwz-.js";import{_ as o,__tla as c}from"./ProductSummary.vue_vue_type_script_setup_true_lang-IE27TV3E.js";import{_ as m,__tla as s}from"./ProductRank.vue_vue_type_script_setup_true_lang-DiXfHDS_.js";import{d as e,o as i,c as n,i as t,F as u,__tla as p}from"./index-BUSn51wb.js";import{__tla as h}from"./el-card-CJbXGyyg.js";import{__tla as f}from"./el-skeleton-item-tDN8U6BH.js";import{__tla as y}from"./Echart.vue_vue_type_script_setup_true_lang-DnLZLH3w.js";import{__tla as d}from"./index.vue_vue_type_script_setup_true_lang-BeC3r7Xt.js";import{__tla as x}from"./formatTime-DWdBpgsM.js";import{__tla as P}from"./product-lJh9Q1vt.js";import{__tla as F}from"./index.vue_vue_type_script_setup_true_lang-CakuHPje.js";import{__tla as S}from"./CountTo.vue_vue_type_script_setup_true_lang-BUNK7cmG.js";import"./download-e0EdwhTv.js";import{__tla as b}from"./CardTitle-Dm4BG9kg.js";import{__tla as g}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{__tla as j}from"./index-Cch5e1V0.js";import{__tla as k}from"./el-image-BjHZRFih.js";let r,q=Promise.all([(()=>{try{return l}catch{}})(),(()=>{try{return c}catch{}})(),(()=>{try{return s}catch{}})(),(()=>{try{return p}catch{}})(),(()=>{try{return h}catch{}})(),(()=>{try{return f}catch{}})(),(()=>{try{return y}catch{}})(),(()=>{try{return d}catch{}})(),(()=>{try{return x}catch{}})(),(()=>{try{return P}catch{}})(),(()=>{try{return F}catch{}})(),(()=>{try{return S}catch{}})(),(()=>{try{return b}catch{}})(),(()=>{try{return g}catch{}})(),(()=>{try{return j}catch{}})(),(()=>{try{return k}catch{}})()]).then(async()=>{r=e({name:"ProductStatistics",__name:"index",setup:v=>(w,z)=>{const _=a;return i(),n(u,null,[t(_,{title:"\u3010\u7EDF\u8BA1\u3011\u4F1A\u5458\u3001\u5546\u54C1\u3001\u4EA4\u6613\u7EDF\u8BA1",url:"https://doc.iocoder.cn/mall/statistics/"}),t(o),t(m,{class:"mt-16px"})],64)}})});export{q as __tla,r as default};
