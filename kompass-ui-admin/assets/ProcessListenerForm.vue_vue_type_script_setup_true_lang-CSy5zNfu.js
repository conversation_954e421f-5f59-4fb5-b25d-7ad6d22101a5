import{d as j,n as Y,I as G,r as i,f as H,o,l as d,w as t,i as s,a as l,j as h,H as D,c as p,F as c,k as _,V as J,G as P,t as K,dR as U,y as Z,Z as z,L as Q,am as W,an as X,J as $,K as ee,O as le,N as ae,R as ue,__tla as te}from"./index-BUSn51wb.js";import{_ as se,__tla as oe}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{P as T,__tla as re}from"./index-CRkUQbt2.js";import{C as de}from"./constants-A8BI3pz7.js";let C,ne=Promise.all([(()=>{try{return te}catch{}})(),(()=>{try{return oe}catch{}})(),(()=>{try{return re}catch{}})()]).then(async()=>{C=j({name:"ProcessListenerForm",__name:"ProcessListenerForm",emits:["success"],setup(ve,{expose:R,emit:q}){const{t:y}=Y(),E=G(),v=i(!1),k=i(""),m=i(!1),L=i(""),u=i({id:void 0,name:void 0,type:void 0,status:void 0,event:void 0,valueType:void 0,value:void 0}),w=H({name:[{required:!0,message:"\u540D\u5B57\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],type:[{required:!0,message:"\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],status:[{required:!0,message:"\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],event:[{required:!0,message:"\u76D1\u542C\u4E8B\u4EF6\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],valueType:[{required:!0,message:"\u503C\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],value:[{required:!0,message:"\u503C\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),f=i();R({open:async(r,a)=>{if(v.value=!0,k.value=y("action."+r),L.value=r,x(),a){m.value=!0;try{u.value=await T.getProcessListener(a)}finally{m.value=!1}}}});const N=q,O=async()=>{await f.value.validate(),m.value=!0;try{const r=u.value;L.value==="create"?(await T.createProcessListener(r),E.success(y("common.createSuccess"))):(await T.updateProcessListener(r),E.success(y("common.updateSuccess"))),v.value=!1,N("success")}finally{m.value=!1}},x=()=>{var r;u.value={id:void 0,name:void 0,type:void 0,status:de.ENABLE,event:void 0,valueType:void 0,value:void 0},(r=f.value)==null||r.resetFields()};return(r,a)=>{const V=z,n=Q,A=W,F=X,b=$,g=ee,M=le,S=ae,B=se,I=ue;return o(),d(B,{title:l(k),modelValue:l(v),"onUpdate:modelValue":a[9]||(a[9]=e=>Z(v)?v.value=e:null)},{footer:t(()=>[s(S,{onClick:O,type:"primary",disabled:l(m)},{default:t(()=>[h("\u786E \u5B9A")]),_:1},8,["disabled"]),s(S,{onClick:a[8]||(a[8]=e=>v.value=!1)},{default:t(()=>[h("\u53D6 \u6D88")]),_:1})]),default:t(()=>[D((o(),d(M,{ref_key:"formRef",ref:f,model:l(u),rules:l(w),"label-width":"110px"},{default:t(()=>[s(n,{label:"\u540D\u5B57",prop:"name"},{default:t(()=>[s(V,{modelValue:l(u).name,"onUpdate:modelValue":a[0]||(a[0]=e=>l(u).name=e),placeholder:"\u8BF7\u8F93\u5165\u540D\u5B57"},null,8,["modelValue"])]),_:1}),s(n,{label:"\u72B6\u6001",prop:"status"},{default:t(()=>[s(F,{modelValue:l(u).status,"onUpdate:modelValue":a[1]||(a[1]=e=>l(u).status=e)},{default:t(()=>[(o(!0),p(c,null,_(l(J)(l(P).COMMON_STATUS),e=>(o(),d(A,{key:e.value,label:e.value},{default:t(()=>[h(K(e.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(n,{label:"\u7C7B\u578B",prop:"type"},{default:t(()=>[s(g,{modelValue:l(u).type,"onUpdate:modelValue":a[2]||(a[2]=e=>l(u).type=e),placeholder:"\u8BF7\u9009\u62E9\u7C7B\u578B",onChange:a[3]||(a[3]=e=>l(u).event=void 0)},{default:t(()=>[(o(!0),p(c,null,_(l(U)(l(P).BPM_PROCESS_LISTENER_TYPE),e=>(o(),d(b,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(n,{label:"\u4E8B\u4EF6",prop:"event"},{default:t(()=>[s(g,{modelValue:l(u).event,"onUpdate:modelValue":a[4]||(a[4]=e=>l(u).event=e),placeholder:"\u8BF7\u9009\u62E9\u4E8B\u4EF6"},{default:t(()=>[(o(!0),p(c,null,_(l(u).type=="execution"?["start","end"]:["create","assignment","complete","delete","update","timeout"],e=>(o(),d(b,{label:e,value:e,key:e},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(n,{label:"\u503C\u7C7B\u578B",prop:"valueType"},{default:t(()=>[s(g,{modelValue:l(u).valueType,"onUpdate:modelValue":a[5]||(a[5]=e=>l(u).valueType=e),placeholder:"\u8BF7\u9009\u62E9\u503C\u7C7B\u578B"},{default:t(()=>[(o(!0),p(c,null,_(l(U)(l(P).BPM_PROCESS_LISTENER_VALUE_TYPE),e=>(o(),d(b,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(u).type=="class"?(o(),d(n,{key:0,label:"\u7C7B\u8DEF\u5F84",prop:"value"},{default:t(()=>[s(V,{modelValue:l(u).value,"onUpdate:modelValue":a[6]||(a[6]=e=>l(u).value=e),placeholder:"\u8BF7\u8F93\u5165\u7C7B\u8DEF\u5F84"},null,8,["modelValue"])]),_:1})):(o(),d(n,{key:1,label:"\u8868\u8FBE\u5F0F",prop:"value"},{default:t(()=>[s(V,{modelValue:l(u).value,"onUpdate:modelValue":a[7]||(a[7]=e=>l(u).value=e),placeholder:"\u8BF7\u8F93\u5165\u8868\u8FBE\u5F0F"},null,8,["modelValue"])]),_:1}))]),_:1},8,["model","rules"])),[[I,l(m)]])]),_:1},8,["title","modelValue"])}}})});export{C as _,ne as __tla};
