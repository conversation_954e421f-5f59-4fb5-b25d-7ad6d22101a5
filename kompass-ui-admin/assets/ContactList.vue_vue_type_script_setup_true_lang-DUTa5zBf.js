import{d as H,r as n,f as Q,u as q,at as J,T as K,o as u,c as V,i as a,w as o,j as p,a as s,H as h,l as d,a9 as m,t as W,G as X,F as Y,I as Z,_ as $,N as aa,s as ea,P as ta,v as sa,Q as ra,R as la,__tla as oa}from"./index-BUSn51wb.js";import{_ as ia,__tla as ca}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as na,__tla as ua}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as _a,__tla as da}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{d as pa,g as ma,e as fa,f as ya,__tla as Ia}from"./index-9ux5MgCS.js";import{_ as ba,__tla as ga}from"./ContactForm.vue_vue_type_script_setup_true_lang-B3yX3y4l.js";import{B as R,__tla as va}from"./index-pKzyIv29.js";import{_ as ha,__tla as wa}from"./ContactListModal.vue_vue_type_script_setup_true_lang-Ba5Cb5nV.js";let x,ka=Promise.all([(()=>{try{return oa}catch{}})(),(()=>{try{return ca}catch{}})(),(()=>{try{return ua}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return Ia}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return wa}catch{}})()]).then(async()=>{x=H({name:"CrmContactList",__name:"ContactList",props:{bizType:{},bizId:{},customerId:{},businessId:{}},setup(T){const r=T,f=n(!0),w=n(0),k=n([]),e=Q({pageNo:1,pageSize:10,customerId:void 0,businessId:void 0}),y=Z(),I=async()=>{f.value=!0;try{e.customerId=void 0;let t={list:[],total:0};switch(r.bizType){case R.CRM_CUSTOMER:e.customerId=r.bizId,t=await ma(e);break;case R.CRM_BUSINESS:e.businessId=r.bizId,t=await pa(e);break;default:return}k.value=t.list,w.value=t.total}finally{f.value=!1}},b=()=>{e.pageNo=1,I()},C=n(),B=()=>{C.value.open("create",void 0,r.customerId,r.businessId)},{push:M}=q(),z=n(),E=()=>{z.value.open()},L=async t=>{const l={businessId:r.bizId,contactIds:t};g.value.getSelectionRows().forEach(_=>{l.contactIds.push(_.id)}),await fa(l),y.success("\u5173\u8054\u8054\u7CFB\u4EBA\u6210\u529F"),b()},g=n(),P=async()=>{const t={businessId:r.bizId,contactIds:g.value.getSelectionRows().map(l=>l.id)};if(t.contactIds.length===0)return y.error("\u672A\u9009\u62E9\u8054\u7CFB\u4EBA");await ya(t),y.success("\u53D6\u5173\u8054\u7CFB\u4EBA\u6210\u529F"),b()};return J(()=>[r.bizId,r.bizType],()=>{b()},{immediate:!0,deep:!0}),(t,l)=>{const _=$,v=aa,U=ea,c=ta,O=sa,j=_a,A=ra,D=na,F=ia,S=K("hasPermi"),G=la;return u(),V(Y,null,[a(U,{justify:"end"},{default:o(()=>[a(v,{onClick:B},{default:o(()=>[a(_,{class:"mr-5px",icon:"system-uicons:contacts"}),p(" \u521B\u5EFA\u8054\u7CFB\u4EBA ")]),_:1}),s(e).businessId?h((u(),d(v,{key:0,onClick:E},{default:o(()=>[a(_,{class:"mr-5px",icon:"ep:circle-plus"}),p(" \u5173\u8054 ")]),_:1})),[[S,["crm:contact:create-business"]]]):m("",!0),s(e).businessId?h((u(),d(v,{key:1,onClick:P},{default:o(()=>[a(_,{class:"mr-5px",icon:"ep:remove"}),p(" \u89E3\u9664\u5173\u8054 ")]),_:1})),[[S,["crm:contact:delete-business"]]]):m("",!0)]),_:1}),a(F,{class:"mt-10px"},{default:o(()=>[h((u(),d(A,{ref_key:"contactRef",ref:g,data:s(k),"show-overflow-tooltip":!0,stripe:!0},{default:o(()=>[s(e).businessId?(u(),d(c,{key:0,type:"selection",width:"55"})):m("",!0),a(c,{align:"center",fixed:"left",label:"\u59D3\u540D",prop:"name"},{default:o(i=>[a(O,{underline:!1,type:"primary",onClick:Ca=>{return N=i.row.id,void M({name:"CrmContactDetail",params:{id:N}});var N}},{default:o(()=>[p(W(i.row.name),1)]),_:2},1032,["onClick"])]),_:1}),a(c,{align:"center",label:"\u624B\u673A\u53F7",prop:"mobile"}),a(c,{align:"center",label:"\u804C\u4F4D",prop:"post"}),a(c,{align:"center",label:"\u76F4\u5C5E\u4E0A\u7EA7",prop:"parentName"}),a(c,{align:"center",label:"\u662F\u5426\u5173\u952E\u51B3\u7B56\u4EBA","min-width":"100",prop:"master"},{default:o(i=>[a(j,{type:s(X).INFRA_BOOLEAN_STRING,value:i.row.master},null,8,["type","value"])]),_:1})]),_:1},8,["data"])),[[G,s(f)]]),a(D,{limit:s(e).pageSize,"onUpdate:limit":l[0]||(l[0]=i=>s(e).pageSize=i),page:s(e).pageNo,"onUpdate:page":l[1]||(l[1]=i=>s(e).pageNo=i),total:s(w),onPagination:I},null,8,["limit","page","total"])]),_:1}),a(ba,{ref_key:"formRef",ref:C,onSuccess:I},null,512),t.customerId?(u(),d(ha,{key:0,ref_key:"contactModalRef",ref:z,"customer-id":t.customerId,onSuccess:L},null,8,["customer-id"])):m("",!0)],64)}}})});export{x as _,ka as __tla};
