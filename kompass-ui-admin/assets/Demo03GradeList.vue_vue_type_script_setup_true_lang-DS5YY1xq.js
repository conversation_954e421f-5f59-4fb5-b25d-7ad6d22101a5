import{d as h,n as y,I as b,r as n,C as v,o as _,l as o,w as p,H as g,a as l,i as t,P as w,Q as I,R as x,__tla as P}from"./index-BUSn51wb.js";import{_ as j,__tla as C}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{d as D,__tla as G}from"./formatTime-DWdBpgsM.js";import{b as H,__tla as L}from"./index-DrnBZ6x8.js";let c,Q=Promise.all([(()=>{try{return P}catch{}})(),(()=>{try{return C}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return L}catch{}})()]).then(async()=>{c=h({__name:"Demo03GradeList",props:{studentId:{}},setup(i){y(),b();const u=i,r=n(!1),s=n([]);return v(()=>{(async()=>{r.value=!0;try{const e=await H(u.studentId);if(!e)return;s.value.push(e)}finally{r.value=!1}})()}),(e,R)=>{const a=w,m=I,d=j,f=x;return _(),o(d,null,{default:p(()=>[g((_(),o(m,{data:l(s),stripe:!0,"show-overflow-tooltip":!0},{default:p(()=>[t(a,{label:"\u7F16\u53F7",align:"center",prop:"id"}),t(a,{label:"\u540D\u5B57",align:"center",prop:"name"}),t(a,{label:"\u73ED\u4E3B\u4EFB",align:"center",prop:"teacher"}),t(a,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:l(D),width:"180px"},null,8,["formatter"])]),_:1},8,["data"])),[[f,l(r)]])]),_:1})}}})});export{c as _,Q as __tla};
