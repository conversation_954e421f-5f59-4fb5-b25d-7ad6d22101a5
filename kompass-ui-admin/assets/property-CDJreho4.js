import{_ as F,__tla as L}from"./ComponentContainerProperty-U-5gd_f0.js";import{d as M,o as p,l as n,w as l,i as a,a as o,j as u,a9 as N,c as O,F as A,_ as B,aM as I,aN as Q,an as S,L as W,am as X,ce as q,cf as G,cl as H,cn as J,O as K,__tla as R}from"./index-BUSn51wb.js";import{_ as T,__tla as Y}from"./index.vue_vue_type_script_setup_true_lang-C2f0jZah.js";import{_ as Z,__tla as $}from"./index.vue_vue_type_script_setup_true_lang-D01sdz8R.js";import{E as C,__tla as aa}from"./el-card-CJbXGyyg.js";import{E as la,__tla as ea}from"./el-text-CIwNlU-U.js";import{u as ta,__tla as oa}from"./util-Dyp86Gv2.js";import{__tla as ra}from"./index-11u3nuTi.js";import"./color-BN7ZL7BD.js";import{__tla as ma}from"./vuedraggable.umd-BTL7hPHv.js";import{__tla as _a}from"./AppLinkSelectDialog.vue_vue_type_script_setup_true_lang-CHi5jpXX.js";import{__tla as ua}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as da}from"./ProductCategorySelect.vue_vue_type_script_setup_true_lang-CJItHfu_.js";import{__tla as pa}from"./el-tree-select-CBuha0HW.js";import"./tree-BMa075Oj.js";import{__tla as sa}from"./category-WzWM3ODe.js";import{__tla as na}from"./Qrcode-CP7wmJi0.js";import{__tla as ia}from"./IFrame.vue_vue_type_script_setup_true_lang-silYuU_p.js";import{__tla as ca}from"./el-collapse-item-B_QvnH_b.js";let h,fa=Promise.all([(()=>{try{return L}catch{}})(),(()=>{try{return R}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return $}catch{}})(),(()=>{try{return aa}catch{}})(),(()=>{try{return ea}catch{}})(),(()=>{try{return oa}catch{}})(),(()=>{try{return ra}catch{}})(),(()=>{try{return ma}catch{}})(),(()=>{try{return _a}catch{}})(),(()=>{try{return ua}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return pa}catch{}})(),(()=>{try{return sa}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return ia}catch{}})(),(()=>{try{return ca}catch{}})()]).then(async()=>{h=M({name:"CarouselProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(b,{emit:U}){const x=b,w=U,{formData:t}=ta(x.modelValue,w);return(ya,r)=>{const i=B,c=I,f=Q,s=S,m=W,d=X,g=q,v=G,k=la,y=C,V=H,z=J,P=Z,E=T,j=K,D=F;return p(),n(D,{modelValue:o(t).style,"onUpdate:modelValue":r[5]||(r[5]=e=>o(t).style=e)},{default:l(()=>[a(j,{"label-width":"80px",model:o(t)},{default:l(()=>[a(y,{header:"\u6837\u5F0F\u8BBE\u7F6E",class:"property-group",shadow:"never"},{default:l(()=>[a(m,{label:"\u6837\u5F0F",prop:"type"},{default:l(()=>[a(s,{modelValue:o(t).type,"onUpdate:modelValue":r[0]||(r[0]=e=>o(t).type=e)},{default:l(()=>[a(f,{class:"item",content:"\u9ED8\u8BA4",placement:"bottom"},{default:l(()=>[a(c,{label:"default"},{default:l(()=>[a(i,{icon:"system-uicons:carousel"})]),_:1})]),_:1}),a(f,{class:"item",content:"\u5361\u7247",placement:"bottom"},{default:l(()=>[a(c,{label:"card"},{default:l(()=>[a(i,{icon:"ic:round-view-carousel"})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}),a(m,{label:"\u6307\u793A\u5668",prop:"indicator"},{default:l(()=>[a(s,{modelValue:o(t).indicator,"onUpdate:modelValue":r[1]||(r[1]=e=>o(t).indicator=e)},{default:l(()=>[a(d,{label:"dot"},{default:l(()=>[u("\u5C0F\u5706\u70B9")]),_:1}),a(d,{label:"number"},{default:l(()=>[u("\u6570\u5B57")]),_:1})]),_:1},8,["modelValue"])]),_:1}),a(m,{label:"\u662F\u5426\u8F6E\u64AD",prop:"autoplay"},{default:l(()=>[a(g,{modelValue:o(t).autoplay,"onUpdate:modelValue":r[2]||(r[2]=e=>o(t).autoplay=e)},null,8,["modelValue"])]),_:1}),o(t).autoplay?(p(),n(m,{key:0,label:"\u64AD\u653E\u95F4\u9694",prop:"interval"},{default:l(()=>[a(v,{modelValue:o(t).interval,"onUpdate:modelValue":r[3]||(r[3]=e=>o(t).interval=e),max:10,min:.5,step:.5,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"]),a(k,{type:"info"},{default:l(()=>[u("\u5355\u4F4D\uFF1A\u79D2")]),_:1})]),_:1})):N("",!0)]),_:1}),a(y,{header:"\u5185\u5BB9\u8BBE\u7F6E",class:"property-group",shadow:"never"},{default:l(()=>[a(E,{modelValue:o(t).items,"onUpdate:modelValue":r[4]||(r[4]=e=>o(t).items=e),"empty-item":{type:"img"}},{default:l(({element:e})=>[a(m,{label:"\u7C7B\u578B",prop:"type",class:"m-b-8px!","label-width":"40px"},{default:l(()=>[a(s,{modelValue:e.type,"onUpdate:modelValue":_=>e.type=_},{default:l(()=>[a(d,{label:"img"},{default:l(()=>[u("\u56FE\u7247")]),_:1}),a(d,{label:"video"},{default:l(()=>[u("\u89C6\u9891")]),_:1})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024),e.type==="img"?(p(),n(m,{key:0,label:"\u56FE\u7247",class:"m-b-8px!","label-width":"40px"},{default:l(()=>[a(V,{modelValue:e.imgUrl,"onUpdate:modelValue":_=>e.imgUrl=_,draggable:"false",height:"80px",width:"100%",class:"min-w-80px"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)):(p(),O(A,{key:1},[a(m,{label:"\u5C01\u9762",class:"m-b-8px!","label-width":"40px"},{default:l(()=>[a(V,{modelValue:e.imgUrl,"onUpdate:modelValue":_=>e.imgUrl=_,draggable:"false",height:"80px",width:"100%",class:"min-w-80px"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),a(m,{label:"\u89C6\u9891",class:"m-b-8px!","label-width":"40px"},{default:l(()=>[a(z,{modelValue:e.videoUrl,"onUpdate:modelValue":_=>e.videoUrl=_,"file-type":["mp4"],limit:1,"file-size":100,class:"min-w-80px"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)],64)),a(m,{label:"\u94FE\u63A5",class:"m-b-8px!","label-width":"40px"},{default:l(()=>[a(P,{modelValue:e.url,"onUpdate:modelValue":_=>e.url=_},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}})});export{fa as __tla,h as default};
