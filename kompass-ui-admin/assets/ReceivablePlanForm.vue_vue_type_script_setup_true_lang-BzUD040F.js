import{d as K,r as c,f as O,o as n,l as f,w as t,i as l,j as D,a,H as S,c as w,k as I,F as g,V as Y,G as Z,y as z,n as Q,I as W,aD as X,e as $,Z as ee,L as le,E as ae,J as te,K as re,s as ue,cc as de,M as se,O as oe,N as ne,R as ce,__tla as ie}from"./index-BUSn51wb.js";import{_ as me,__tla as pe}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{b as _e,c as fe,u as ve,__tla as be}from"./index-Uo5NQqNb.js";import{g as Ve,__tla as ye}from"./index-BYXzDB8j.js";import{a as we,__tla as Ie}from"./index-CD52sTBY.js";import{d as ge,__tla as he}from"./index-DrB1WZUR.js";let F,Ue=Promise.all([(()=>{try{return ie}catch{}})(),(()=>{try{return pe}catch{}})(),(()=>{try{return be}catch{}})(),(()=>{try{return ye}catch{}})(),(()=>{try{return Ie}catch{}})(),(()=>{try{return he}catch{}})()]).then(async()=>{F=K({__name:"ReceivablePlanForm",emits:["success"],setup(ke,{expose:P,emit:L}){const{t:h}=Q(),T=W(),R=c([]),i=c(!1),C=c(""),m=c(!1),p=c(""),r=c({}),M=O({price:[{required:!0,message:"\u8BA1\u5212\u56DE\u6B3E\u91D1\u989D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],returnTime:[{required:!0,message:"\u8BA1\u5212\u56DE\u6B3E\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],customerId:[{required:!0,message:"\u5BA2\u6237\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],contractId:[{required:!0,message:"\u5408\u540C\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],ownerUserId:[{required:!0,message:"\u8D1F\u8D23\u4EBA\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),v=c(),E=c([]),U=c([]);P({open:async(s,u,_,o)=>{if(i.value=!0,C.value=h("action."+s),p.value=s,A(),u){m.value=!0;try{const d=await _e(u);r.value=X(d),await k(d.customerId),r.value.contractId=d==null?void 0:d.contractId}finally{m.value=!1}}R.value=await Ve(),E.value=await we(),p.value==="create"&&(r.value.ownerUserId=$().getUser.id),_&&(r.value.customerId=_,await k(_)),o&&(r.value.contractId=o)}});const N=L,j=async()=>{if(v&&await v.value.validate()){m.value=!0;try{const s=r.value;p.value==="create"?(await fe(s),T.success(h("common.createSuccess"))):(await ve(s),T.success(h("common.updateSuccess"))),i.value=!1,N("success")}finally{m.value=!1}}},A=()=>{var s;r.value={},(s=v.value)==null||s.resetFields()},k=async s=>{r.value.contractId=void 0,s&&(U.value=[],U.value=await ge(s))};return(s,u)=>{const _=ee,o=le,d=ae,b=te,V=re,y=ue,q=de,B=se,G=oe,x=ne,H=me,J=ce;return n(),f(H,{modelValue:a(i),"onUpdate:modelValue":u[10]||(u[10]=e=>z(i)?i.value=e:null),title:a(C)},{footer:t(()=>[l(x,{disabled:a(m),type:"primary",onClick:j},{default:t(()=>[D("\u786E \u5B9A")]),_:1},8,["disabled"]),l(x,{onClick:u[9]||(u[9]=e=>i.value=!1)},{default:t(()=>[D("\u53D6 \u6D88")]),_:1})]),default:t(()=>[S((n(),f(G,{ref_key:"formRef",ref:v,model:a(r),rules:a(M),"label-width":"110px"},{default:t(()=>[l(y,null,{default:t(()=>[l(d,{span:12},{default:t(()=>[l(o,{label:"\u8FD8\u6B3E\u671F\u6570",prop:"period"},{default:t(()=>[l(_,{modelValue:a(r).period,"onUpdate:modelValue":u[0]||(u[0]=e=>a(r).period=e),disabled:"",placeholder:"\u4FDD\u5B58\u65F6\u81EA\u52A8\u751F\u6210"},null,8,["modelValue"])]),_:1})]),_:1}),l(d,{span:12},{default:t(()=>[l(o,{label:"\u8D1F\u8D23\u4EBA",prop:"ownerUserId"},{default:t(()=>[l(V,{modelValue:a(r).ownerUserId,"onUpdate:modelValue":u[1]||(u[1]=e=>a(r).ownerUserId=e),disabled:a(p)!=="create",class:"w-1/1"},{default:t(()=>[(n(!0),w(g,null,I(a(R),e=>(n(),f(b,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),l(y,null,{default:t(()=>[l(d,{span:12},{default:t(()=>[l(o,{label:"\u5BA2\u6237\u540D\u79F0",prop:"customerId"},{default:t(()=>[l(V,{modelValue:a(r).customerId,"onUpdate:modelValue":u[2]||(u[2]=e=>a(r).customerId=e),disabled:a(p)!=="create",class:"w-1/1",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237",onChange:k},{default:t(()=>[(n(!0),w(g,null,I(a(E),e=>(n(),f(b,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),l(d,{span:12},{default:t(()=>[l(o,{label:"\u5408\u540C\u540D\u79F0",prop:"contractId"},{default:t(()=>[l(V,{modelValue:a(r).contractId,"onUpdate:modelValue":u[3]||(u[3]=e=>a(r).contractId=e),disabled:a(p)!=="create"||!a(r).customerId,class:"w-1/1",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u5408\u540C"},{default:t(()=>[(n(!0),w(g,null,I(a(U),e=>(n(),f(b,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),l(y,null,{default:t(()=>[l(d,{span:12},{default:t(()=>[l(o,{label:"\u8BA1\u5212\u56DE\u6B3E\u91D1\u989D",prop:"price"},{default:t(()=>[l(q,{modelValue:a(r).price,"onUpdate:modelValue":u[4]||(u[4]=e=>a(r).price=e),min:.01,precision:2,class:"!w-100%","controls-position":"right",placeholder:"\u8BF7\u8F93\u5165\u8BA1\u5212\u56DE\u6B3E\u91D1\u989D"},null,8,["modelValue"])]),_:1})]),_:1}),l(d,{span:12},{default:t(()=>[l(o,{label:"\u8BA1\u5212\u56DE\u6B3E\u65E5\u671F",prop:"returnTime"},{default:t(()=>[l(B,{modelValue:a(r).returnTime,"onUpdate:modelValue":u[5]||(u[5]=e=>a(r).returnTime=e),placeholder:"\u9009\u62E9\u8BA1\u5212\u56DE\u6B3E\u65E5\u671F",type:"date","value-format":"x"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(y,null,{default:t(()=>[l(d,{span:12},{default:t(()=>[l(o,{label:"\u63D0\u524D\u51E0\u5929\u63D0\u9192",prop:"remindDays"},{default:t(()=>[l(q,{modelValue:a(r).remindDays,"onUpdate:modelValue":u[6]||(u[6]=e=>a(r).remindDays=e),class:"!w-100%","controls-position":"right",placeholder:"\u8BF7\u8F93\u5165\u63D0\u524D\u51E0\u5929\u63D0\u9192"},null,8,["modelValue"])]),_:1})]),_:1}),l(d,{span:12},{default:t(()=>[l(o,{label:"\u56DE\u6B3E\u65B9\u5F0F",prop:"returnType"},{default:t(()=>[l(V,{modelValue:a(r).returnType,"onUpdate:modelValue":u[7]||(u[7]=e=>a(r).returnType=e),class:"w-1/1",placeholder:"\u8BF7\u9009\u62E9\u56DE\u6B3E\u65B9\u5F0F"},{default:t(()=>[(n(!0),w(g,null,I(a(Y)(a(Z).CRM_RECEIVABLE_RETURN_TYPE),e=>(n(),f(b,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(d,{span:24},{default:t(()=>[l(o,{label:"\u5907\u6CE8",prop:"remark"},{default:t(()=>[l(_,{modelValue:a(r).remark,"onUpdate:modelValue":u[8]||(u[8]=e=>a(r).remark=e),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])),[[J,a(m)]])]),_:1},8,["modelValue","title"])}}})});export{F as _,Ue as __tla};
