import{by as n,d as q,n as H,I as J,r,f as z,o as _,l as I,w as u,i as d,a as e,j as V,H as B,c as D,F as G,k as P,a9 as W,y as X,J as Z,K as $,L as ee,cc as ae,ce as le,O as te,N as se,R as ue,__tla as oe}from"./index-BUSn51wb.js";import{_ as de,__tla as re}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{E as ie,__tla as me}from"./el-tree-select-CBuha0HW.js";import{g as ce,__tla as ne}from"./index-Bqt292RI.js";import{d as _e,h as pe}from"./tree-BMa075Oj.js";import{g as fe,__tla as ye}from"./index-BYXzDB8j.js";let i,h,w,O,Ie=Promise.all([(()=>{try{return oe}catch{}})(),(()=>{try{return re}catch{}})(),(()=>{try{return me}catch{}})(),(()=>{try{return ne}catch{}})(),(()=>{try{return ye}catch{}})()]).then(async()=>{i=(s=>(s[s.CUSTOMER_QUANTITY_LIMIT=1]="CUSTOMER_QUANTITY_LIMIT",s[s.CUSTOMER_LOCK_LIMIT=2]="CUSTOMER_LOCK_LIMIT",s))(i||{}),O=async s=>await n.get({url:"/crm/customer-limit-config/page",params:s}),w=async s=>await n.delete({url:"/crm/customer-limit-config/delete?id="+s}),h=q({__name:"CustomerLimitConfigForm",emits:["success"],setup(s,{expose:E,emit:R}){const{t:v}=H(),T=J(),m=r(!1),g=r(""),c=r(!1),b=r(""),l=r({id:void 0,type:i.CUSTOMER_LOCK_LIMIT,userIds:void 0,deptIds:void 0,maxCount:void 0,dealCountEnabled:!1}),k=z({type:[{required:!0,message:"\u89C4\u5219\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],maxCount:[{required:!0,message:"\u6570\u91CF\u4E0A\u9650\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),p=r(),U=r(),M=r([]);E({open:async(o,a,f)=>{if(m.value=!0,g.value=v("action."+o),b.value=o,K(),f){c.value=!0;try{l.value=await(async C=>await n.get({url:"/crm/customer-limit-config/get?id="+C}))(f)}finally{c.value=!1}}else l.value.type=a;U.value=pe(await ce()),M.value=await fe()}});const S=R,x=async()=>{if(p&&await p.value.validate()){c.value=!0;try{const o=l.value;b.value==="create"?(await(async a=>await n.post({url:"/crm/customer-limit-config/create",data:a}))(o),T.success(v("common.createSuccess"))):(await(async a=>await n.put({url:"/crm/customer-limit-config/update",data:a}))(o),T.success(v("common.updateSuccess"))),m.value=!1,S("success")}finally{c.value=!1}}},K=()=>{var o;l.value={id:void 0,type:i.CUSTOMER_LOCK_LIMIT,userIds:void 0,deptIds:void 0,maxCount:void 0,dealCountEnabled:!1},(o=p.value)==null||o.resetFields()};return(o,a)=>{const f=Z,C=$,y=ee,N=ie,A=ae,Q=le,Y=te,L=se,F=de,j=ue;return _(),I(F,{title:e(g),modelValue:e(m),"onUpdate:modelValue":a[5]||(a[5]=t=>X(m)?m.value=t:null)},{footer:u(()=>[d(L,{onClick:x,type:"primary",disabled:e(c)},{default:u(()=>[V("\u786E \u5B9A")]),_:1},8,["disabled"]),d(L,{onClick:a[4]||(a[4]=t=>m.value=!1)},{default:u(()=>[V("\u53D6 \u6D88")]),_:1})]),default:u(()=>[B((_(),I(Y,{ref_key:"formRef",ref:p,model:e(l),rules:e(k),"label-width":"200px"},{default:u(()=>[d(y,{label:"\u89C4\u5219\u9002\u7528\u4EBA\u7FA4",prop:"userIds"},{default:u(()=>[d(C,{multiple:"",filterable:"",modelValue:e(l).userIds,"onUpdate:modelValue":a[0]||(a[0]=t=>e(l).userIds=t)},{default:u(()=>[(_(!0),D(G,null,P(e(M),t=>(_(),I(f,{key:t.id,label:t.nickname,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),d(y,{label:"\u89C4\u5219\u9002\u7528\u90E8\u95E8",prop:"deptIds"},{default:u(()=>[d(N,{modelValue:e(l).deptIds,"onUpdate:modelValue":a[1]||(a[1]=t=>e(l).deptIds=t),data:e(U),props:e(_e),multiple:"",filterable:"","check-strictly":"","node-key":"id",placeholder:"\u8BF7\u9009\u62E9\u89C4\u5219\u9002\u7528\u90E8\u95E8"},null,8,["modelValue","data","props"])]),_:1}),d(y,{label:e(l).type===e(i).CUSTOMER_QUANTITY_LIMIT?"\u62E5\u6709\u5BA2\u6237\u6570\u4E0A\u9650":"\u9501\u5B9A\u5BA2\u6237\u6570\u4E0A\u9650",prop:"maxCount"},{default:u(()=>[d(A,{modelValue:e(l).maxCount,"onUpdate:modelValue":a[2]||(a[2]=t=>e(l).maxCount=t),placeholder:"\u8BF7\u8F93\u5165\u6570\u91CF\u4E0A\u9650"},null,8,["modelValue"])]),_:1},8,["label"]),e(l).type===e(i).CUSTOMER_QUANTITY_LIMIT?(_(),I(y,{key:0,label:"\u6210\u4EA4\u5BA2\u6237\u662F\u5426\u5360\u7528\u62E5\u6709\u5BA2\u6237\u6570",prop:"dealCountEnabled"},{default:u(()=>[d(Q,{modelValue:e(l).dealCountEnabled,"onUpdate:modelValue":a[3]||(a[3]=t=>e(l).dealCountEnabled=t)},null,8,["modelValue"])]),_:1})):W("",!0)]),_:1},8,["model","rules"])),[[j,e(c)]])]),_:1},8,["title","modelValue"])}}})});export{i as L,h as _,Ie as __tla,w as d,O as g};
