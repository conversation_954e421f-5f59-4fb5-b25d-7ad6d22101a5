import{r as c,b as l,a as o,d as m,o as _,c as u,t as O,n as R,__tla as T}from"./index-BUSn51wb.js";let e,r,S,s,L=Promise.all([(()=>{try{return T}catch{}})()]).then(async()=>{e=(n=>(n[n.LOGIN=0]="LOGIN",n[n.REGISTER=1]="REGISTER",n[n.RESET_PASSWORD=2]="RESET_PASSWORD",n[n.MOBILE=3]="MOBILE",n[n.QR_CODE=4]="QR_CODE",n[n.SSO=5]="SSO",n))(e||{});const a=c(0);s=function(){function n(t){a.value=t}return{setLoginState:n,getLoginState:l(()=>a.value),handleBackLogin:function(){n(0)}}},S=function(n){return{validForm:async function(){const t=o(n);if(t)return await t.validate()}}};let i;i={class:"enter-x mb-3 text-center text-2xl font-bold xl:text-center xl:text-3xl"},r=m({name:"LoginFormTitle",__name:"LoginFormTitle",setup(n){const{t}=R(),{getLoginState:g}=s(),E=l(()=>({[e.RESET_PASSWORD]:t("sys.login.forgetFormTitle"),[e.LOGIN]:t("sys.login.signInFormTitle"),[e.REGISTER]:t("sys.login.signUpFormTitle"),[e.MOBILE]:t("sys.login.mobileSignInFormTitle"),[e.QR_CODE]:t("sys.login.qrSignInFormTitle"),[e.SSO]:t("sys.login.ssoFormTitle")})[o(g)]);return(I,x)=>(_(),u("h2",i,O(o(E)),1))}})});export{e as L,r as _,L as __tla,S as a,s as u};
