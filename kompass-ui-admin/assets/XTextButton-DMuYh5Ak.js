import{d as f,p as n,b as u,o as a,l as s,w as _,ao as m,a as I,bs as k,N as y,a9 as c,j as b,t as g,_ as x,B as C,__tla as B}from"./index-BUSn51wb.js";let r,h=Promise.all([(()=>{try{return B}catch{}})()]).then(async()=>{r=C(f({name:"XTextButton",__name:"XTextButton",props:{modelValue:n.bool.def(!1),loading:n.bool.def(!1),preIcon:n.string.def(""),postIcon:n.string.def(""),title:n.string.def(""),type:n.oneOf(["","primary","success","warning","danger","info"]).def("primary"),circle:n.bool.def(!1),round:n.bool.def(!1),plain:n.bool.def(!1),onClick:{type:Function,default:null}},setup(e){const i=e,d=u(()=>{const l=["title","preIcon","postIcon","onClick"],o={...k(),...i};for(const t in o)l.indexOf(t)!==-1&&delete o[t];return o});return(l,o)=>{const t=x,p=y;return a(),s(p,m({link:""},I(d),{onClick:e.onClick}),{default:_(()=>[e.preIcon?(a(),s(t,{key:0,icon:e.preIcon,class:"mr-1px"},null,8,["icon"])):c("",!0),b(" "+g(e.title?e.title:"")+" ",1),e.postIcon?(a(),s(t,{key:1,icon:e.postIcon,class:"mr-1px"},null,8,["icon"])):c("",!0)]),_:1},16,["onClick"])}}}),[["__scopeId","data-v-f7cd2dd8"]])});export{r as _,h as __tla};
