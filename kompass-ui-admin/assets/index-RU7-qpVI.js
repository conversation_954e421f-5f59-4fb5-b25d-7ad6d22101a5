import{d as le,I as te,n as re,r as d,f as oe,C as ce,T as se,o as c,c as v,i as a,w as r,a as l,U as L,F as g,k as S,l as u,V as ue,G as W,j as n,H as _,eo as ie,dV as de,Z as ne,L as pe,J as _e,K as me,M as fe,_ as ke,N as he,O as ye,P as we,Q as be,R as ve,__tla as ge}from"./index-BUSn51wb.js";import{_ as Ce,__tla as xe}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as Ve,__tla as Se}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as Ue,__tla as Te}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as Ie,__tla as Pe}from"./index-COobLwz-.js";import{b as Ne,__tla as De}from"./formatTime-DWdBpgsM.js";import{d as Me}from"./download-e0EdwhTv.js";import{_ as Re,S as U,__tla as Ye}from"./StockCheckForm.vue_vue_type_script_setup_true_lang-BuqjLLLE.js";import{P as ze,__tla as Ae}from"./index-B00QUU3o.js";import{W as Ee,__tla as He}from"./index-B5GxX3eg.js";import{g as Ke,__tla as Fe}from"./index-BYXzDB8j.js";import{__tla as Le}from"./index-Cch5e1V0.js";import"./color-BN7ZL7BD.js";import{__tla as We}from"./el-card-CJbXGyyg.js";import{__tla as qe}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as je}from"./StockCheckItemForm.vue_vue_type_script_setup_true_lang-k1XaBZSi.js";import{__tla as Ge}from"./index-BCEOZol9.js";let q,Je=Promise.all([(()=>{try{return ge}catch{}})(),(()=>{try{return xe}catch{}})(),(()=>{try{return Se}catch{}})(),(()=>{try{return Te}catch{}})(),(()=>{try{return Pe}catch{}})(),(()=>{try{return De}catch{}})(),(()=>{try{return Ye}catch{}})(),(()=>{try{return Ae}catch{}})(),(()=>{try{return He}catch{}})(),(()=>{try{return Fe}catch{}})(),(()=>{try{return Le}catch{}})(),(()=>{try{return We}catch{}})(),(()=>{try{return qe}catch{}})(),(()=>{try{return je}catch{}})(),(()=>{try{return Ge}catch{}})()]).then(async()=>{q=le({name:"ErpStockCheck",__name:"index",setup(Oe){const y=te(),{t:j}=re(),T=d(!0),N=d([]),D=d(0),o=oe({pageNo:1,pageSize:10,no:void 0,productId:void 0,warehouseId:void 0,checkTime:[],status:void 0,remark:void 0,creator:void 0}),M=d(),I=d(!1),R=d([]),Y=d([]),z=d([]),k=async()=>{T.value=!0;try{const s=await U.getStockCheckPage(o);N.value=s.list,D.value=s.total}finally{T.value=!1}},C=()=>{o.pageNo=1,k()},G=()=>{M.value.resetFields(),C()},A=d(),P=(s,t)=>{A.value.open(s,t)},E=async s=>{try{await y.delConfirm(),await U.deleteStockCheck(s),y.success(j("common.delSuccess")),await k(),w.value=w.value.filter(t=>!s.includes(t.id))}catch{}},H=async(s,t)=>{try{await y.confirm(`\u786E\u5B9A${t===20?"\u5BA1\u6279":"\u53CD\u5BA1\u6279"}\u8BE5\u76D8\u70B9\u5355\u5417\uFF1F`),await U.updateStockCheckStatus(s,t),y.success((t===20?"\u5BA1\u6279":"\u53CD\u5BA1\u6279")+"\u6210\u529F"),await k()}catch{}},J=async()=>{try{await y.exportConfirm(),I.value=!0;const s=await U.exportStockCheck(o);Me.excel(s,"\u5176\u5B83\u76D8\u70B9\u5355.xls")}catch{}finally{I.value=!1}},w=d([]),O=s=>{w.value=s};return ce(async()=>{await k(),R.value=await ze.getProductSimpleList(),Y.value=await Ee.getWarehouseSimpleList(),z.value=await Ke()}),(s,t)=>{const Q=Ie,K=ne,m=pe,x=_e,V=me,Z=fe,b=ke,i=he,$=ye,F=Ue,p=we,B=Ve,X=be,ee=Ce,f=se("hasPermi"),ae=ve;return c(),v(g,null,[a(Q,{title:"\u3010\u5E93\u5B58\u3011\u5E93\u5B58\u8C03\u62E8\u3001\u5E93\u5B58\u76D8\u70B9",url:"https://doc.iocoder.cn/erp/stock-move-check/"}),a(F,null,{default:r(()=>[a($,{class:"-mb-15px",model:l(o),ref_key:"queryFormRef",ref:M,inline:!0,"label-width":"68px"},{default:r(()=>[a(m,{label:"\u76D8\u70B9\u5355\u53F7",prop:"no"},{default:r(()=>[a(K,{modelValue:l(o).no,"onUpdate:modelValue":t[0]||(t[0]=e=>l(o).no=e),placeholder:"\u8BF7\u8F93\u5165\u76D8\u70B9\u5355\u53F7",clearable:"",onKeyup:L(C,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(m,{label:"\u4EA7\u54C1",prop:"productId"},{default:r(()=>[a(V,{modelValue:l(o).productId,"onUpdate:modelValue":t[1]||(t[1]=e=>l(o).productId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1",class:"!w-240px"},{default:r(()=>[(c(!0),v(g,null,S(l(R),e=>(c(),u(x,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(m,{label:"\u76D8\u70B9\u65F6\u95F4",prop:"checkTime"},{default:r(()=>[a(Z,{modelValue:l(o).checkTime,"onUpdate:modelValue":t[2]||(t[2]=e=>l(o).checkTime=e),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),a(m,{label:"\u4ED3\u5E93",prop:"warehouseId"},{default:r(()=>[a(V,{modelValue:l(o).warehouseId,"onUpdate:modelValue":t[3]||(t[3]=e=>l(o).warehouseId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93",class:"!w-240px"},{default:r(()=>[(c(!0),v(g,null,S(l(Y),e=>(c(),u(x,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(m,{label:"\u521B\u5EFA\u4EBA",prop:"creator"},{default:r(()=>[a(V,{modelValue:l(o).creator,"onUpdate:modelValue":t[4]||(t[4]=e=>l(o).creator=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u521B\u5EFA\u4EBA",class:"!w-240px"},{default:r(()=>[(c(!0),v(g,null,S(l(z),e=>(c(),u(x,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(m,{label:"\u72B6\u6001",prop:"status"},{default:r(()=>[a(V,{modelValue:l(o).status,"onUpdate:modelValue":t[5]||(t[5]=e=>l(o).status=e),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",clearable:"",class:"!w-240px"},{default:r(()=>[(c(!0),v(g,null,S(l(ue)(l(W).ERP_AUDIT_STATUS),e=>(c(),u(x,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(m,{label:"\u5907\u6CE8",prop:"remark"},{default:r(()=>[a(K,{modelValue:l(o).remark,"onUpdate:modelValue":t[6]||(t[6]=e=>l(o).remark=e),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",clearable:"",onKeyup:L(C,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(m,null,{default:r(()=>[a(i,{onClick:C},{default:r(()=>[a(b,{icon:"ep:search",class:"mr-5px"}),n(" \u641C\u7D22")]),_:1}),a(i,{onClick:G},{default:r(()=>[a(b,{icon:"ep:refresh",class:"mr-5px"}),n(" \u91CD\u7F6E")]),_:1}),_((c(),u(i,{type:"primary",plain:"",onClick:t[7]||(t[7]=e=>P("create"))},{default:r(()=>[a(b,{icon:"ep:plus",class:"mr-5px"}),n(" \u65B0\u589E ")]),_:1})),[[f,["erp:stock-check:create"]]]),_((c(),u(i,{type:"success",plain:"",onClick:J,loading:l(I)},{default:r(()=>[a(b,{icon:"ep:download",class:"mr-5px"}),n(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[f,["erp:stock-check:export"]]]),_((c(),u(i,{type:"danger",plain:"",onClick:t[8]||(t[8]=e=>E(l(w).map(h=>h.id))),disabled:l(w).length===0},{default:r(()=>[a(b,{icon:"ep:delete",class:"mr-5px"}),n(" \u5220\u9664 ")]),_:1},8,["disabled"])),[[f,["erp:stock-check:delete"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(F,null,{default:r(()=>[_((c(),u(X,{data:l(N),stripe:!0,"show-overflow-tooltip":!0,onSelectionChange:O},{default:r(()=>[a(p,{width:"30",label:"\u9009\u62E9",type:"selection"}),a(p,{"min-width":"180",label:"\u76D8\u70B9\u5355\u53F7",align:"center",prop:"no"}),a(p,{label:"\u4EA7\u54C1\u4FE1\u606F",align:"center",prop:"productNames","min-width":"200"}),a(p,{label:"\u76D8\u70B9\u65F6\u95F4",align:"center",prop:"checkTime",formatter:l(Ne),width:"120px"},null,8,["formatter"]),a(p,{label:"\u521B\u5EFA\u4EBA",align:"center",prop:"creatorName"}),a(p,{label:"\u6570\u91CF",align:"center",prop:"totalCount",formatter:l(ie)},null,8,["formatter"]),a(p,{label:"\u91D1\u989D",align:"center",prop:"totalPrice",formatter:l(de)},null,8,["formatter"]),a(p,{label:"\u72B6\u6001",align:"center",fixed:"right",width:"90",prop:"status"},{default:r(e=>[a(B,{type:l(W).ERP_AUDIT_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),a(p,{label:"\u64CD\u4F5C",align:"center",fixed:"right",width:"220"},{default:r(e=>[_((c(),u(i,{link:"",onClick:h=>P("detail",e.row.id)},{default:r(()=>[n(" \u8BE6\u60C5 ")]),_:2},1032,["onClick"])),[[f,["erp:stock-check:query"]]]),_((c(),u(i,{link:"",type:"primary",onClick:h=>P("update",e.row.id),disabled:e.row.status===20},{default:r(()=>[n(" \u7F16\u8F91 ")]),_:2},1032,["onClick","disabled"])),[[f,["erp:stock-check:update"]]]),e.row.status===10?_((c(),u(i,{key:0,link:"",type:"primary",onClick:h=>H(e.row.id,20)},{default:r(()=>[n(" \u5BA1\u6279 ")]),_:2},1032,["onClick"])),[[f,["erp:stock-check:update-status"]]]):_((c(),u(i,{key:1,link:"",type:"danger",onClick:h=>H(e.row.id,10)},{default:r(()=>[n(" \u53CD\u5BA1\u6279 ")]),_:2},1032,["onClick"])),[[f,["erp:stock-check:update-status"]]]),_((c(),u(i,{link:"",type:"danger",onClick:h=>E([e.row.id])},{default:r(()=>[n(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[f,["erp:stock-check:delete"]]])]),_:1})]),_:1},8,["data"])),[[ae,l(T)]]),a(ee,{total:l(D),page:l(o).pageNo,"onUpdate:page":t[9]||(t[9]=e=>l(o).pageNo=e),limit:l(o).pageSize,"onUpdate:limit":t[10]||(t[10]=e=>l(o).pageSize=e),onPagination:k},null,8,["total","page","limit"])]),_:1}),a(Re,{ref_key:"formRef",ref:A,onSuccess:k},null,512)],64)}}})});export{Je as __tla,q as default};
