import{by as i,d as Q,n as W,I as X,r as p,b as Y,f as $,o as r,l as c,w as s,i as o,a as e,j as w,H as ee,c as M,F as R,k as S,a9 as f,D as ae,G as A,t as E,V as le,y as te,Z as se,L as ue,cl as oe,J as re,K as de,am as ie,an as ce,cc as me,O as pe,N as ne,R as ye,__tla as ge}from"./index-BUSn51wb.js";import{_ as ve,__tla as _e}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{C as k}from"./constants-A8BI3pz7.js";import{C as be,__tla as he}from"./index-DrcFYyNA.js";let n,L,fe=Promise.all([(()=>{try{return ge}catch{}})(),(()=>{try{return _e}catch{}})(),(()=>{try{return he}catch{}})()]).then(async()=>{n={getChatRolePage:async u=>await i.get({url:"/ai/chat-role/page",params:u}),getChatRole:async u=>await i.get({url:"/ai/chat-role/get?id="+u}),createChatRole:async u=>await i.post({url:"/ai/chat-role/create",data:u}),updateChatRole:async u=>await i.put({url:"/ai/chat-role/update",data:u}),deleteChatRole:async u=>await i.delete({url:"/ai/chat-role/delete?id="+u}),getMyPage:async u=>await i.get({url:"/ai/chat-role/my-page",params:u}),getCategoryList:async()=>await i.get({url:"/ai/chat-role/category-list"}),createMy:async u=>await i.post({url:"/ai/chat-role/create-my",data:u}),updateMy:async u=>await i.put({url:"/ai/chat-role/update-my",data:u}),deleteMy:async u=>await i.delete({url:"/ai/chat-role/delete-my?id="+u})},L=Q({name:"ChatRoleForm",__name:"ChatRoleForm",emits:["success"],setup(u,{expose:F,emit:O}){const{t:b}=W(),V=X(),y=p(!1),U=p(""),g=p(!1),v=p(""),t=p({id:void 0,modelId:void 0,name:void 0,avatar:void 0,category:void 0,sort:void 0,description:void 0,systemMessage:void 0,publicStatus:!0,status:k.ENABLE}),C=p(),I=p([]),h=Y(()=>v.value==="my-create"||v.value==="my-update"),B=$({name:[{required:!0,message:"\u89D2\u8272\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],avatar:[{required:!0,message:"\u89D2\u8272\u5934\u50CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],category:[{required:!0,message:"\u89D2\u8272\u7C7B\u522B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],sort:[{required:!0,message:"\u89D2\u8272\u6392\u5E8F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],description:[{required:!0,message:"\u89D2\u8272\u63CF\u8FF0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],systemMessage:[{required:!0,message:"\u89D2\u8272\u8BBE\u5B9A\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],publicStatus:[{required:!0,message:"\u662F\u5426\u516C\u5F00\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]});F({open:async(d,l,_)=>{if(y.value=!0,U.value=_||b("action."+d),v.value=d,j(),l){g.value=!0;try{t.value=await n.getChatRole(l)}finally{g.value=!1}}I.value=await be.getChatModelSimpleList(k.ENABLE)}});const T=O,P=async()=>{await C.value.validate(),g.value=!0;try{const d=t.value;v.value==="my-create"?(await n.createMy(d),V.success(b("common.createSuccess"))):v.value==="my-update"?(await n.updateMy(d),V.success(b("common.updateSuccess"))):v.value==="create"?(await n.createChatRole(d),V.success(b("common.createSuccess"))):(await n.updateChatRole(d),V.success(b("common.updateSuccess"))),y.value=!1,T("success")}finally{g.value=!1}},j=()=>{var d;t.value={id:void 0,modelId:void 0,name:void 0,avatar:void 0,category:void 0,sort:void 0,description:void 0,systemMessage:void 0,publicStatus:!0,status:k.ENABLE},(d=C.value)==null||d.resetFields()};return(d,l)=>{const _=se,m=ue,G=oe,H=re,D=de,N=ie,q=ce,J=me,K=pe,x=ne,Z=ve,z=ye;return r(),c(Z,{title:e(U),modelValue:e(y),"onUpdate:modelValue":l[10]||(l[10]=a=>te(y)?y.value=a:null)},{footer:s(()=>[o(x,{onClick:P,type:"primary",disabled:e(g)},{default:s(()=>[w("\u786E \u5B9A")]),_:1},8,["disabled"]),o(x,{onClick:l[9]||(l[9]=a=>y.value=!1)},{default:s(()=>[w("\u53D6 \u6D88")]),_:1})]),default:s(()=>[ee((r(),c(K,{ref_key:"formRef",ref:C,model:e(t),rules:e(B),"label-width":"100px"},{default:s(()=>[o(m,{label:"\u89D2\u8272\u540D\u79F0",prop:"name"},{default:s(()=>[o(_,{modelValue:e(t).name,"onUpdate:modelValue":l[0]||(l[0]=a=>e(t).name=a),placeholder:"\u8BF7\u8F93\u5165\u89D2\u8272\u540D\u79F0"},null,8,["modelValue"])]),_:1}),o(m,{label:"\u89D2\u8272\u5934\u50CF",prop:"avatar"},{default:s(()=>[o(G,{modelValue:e(t).avatar,"onUpdate:modelValue":l[1]||(l[1]=a=>e(t).avatar=a),height:"60px",width:"60px"},null,8,["modelValue"])]),_:1}),e(h)?f("",!0):(r(),c(m,{key:0,label:"\u7ED1\u5B9A\u6A21\u578B",prop:"modelId"},{default:s(()=>[o(D,{modelValue:e(t).modelId,"onUpdate:modelValue":l[2]||(l[2]=a=>e(t).modelId=a),placeholder:"\u8BF7\u9009\u62E9\u6A21\u578B",clearable:""},{default:s(()=>[(r(!0),M(R,null,S(e(I),a=>(r(),c(H,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})),e(h)?f("",!0):(r(),c(m,{key:1,label:"\u89D2\u8272\u7C7B\u522B",prop:"category"},{default:s(()=>[o(_,{modelValue:e(t).category,"onUpdate:modelValue":l[3]||(l[3]=a=>e(t).category=a),placeholder:"\u8BF7\u8F93\u5165\u89D2\u8272\u7C7B\u522B"},null,8,["modelValue"])]),_:1})),o(m,{label:"\u89D2\u8272\u63CF\u8FF0",prop:"description"},{default:s(()=>[o(_,{type:"textarea",modelValue:e(t).description,"onUpdate:modelValue":l[4]||(l[4]=a=>e(t).description=a),placeholder:"\u8BF7\u8F93\u5165\u89D2\u8272\u63CF\u8FF0"},null,8,["modelValue"])]),_:1}),o(m,{label:"\u89D2\u8272\u8BBE\u5B9A",prop:"systemMessage"},{default:s(()=>[o(_,{type:"textarea",modelValue:e(t).systemMessage,"onUpdate:modelValue":l[5]||(l[5]=a=>e(t).systemMessage=a),placeholder:"\u8BF7\u8F93\u5165\u89D2\u8272\u8BBE\u5B9A"},null,8,["modelValue"])]),_:1}),e(h)?f("",!0):(r(),c(m,{key:2,label:"\u662F\u5426\u516C\u5F00",prop:"publicStatus"},{default:s(()=>[o(q,{modelValue:e(t).publicStatus,"onUpdate:modelValue":l[6]||(l[6]=a=>e(t).publicStatus=a)},{default:s(()=>[(r(!0),M(R,null,S(e(ae)(e(A).INFRA_BOOLEAN_STRING),a=>(r(),c(N,{key:a.value,label:a.value},{default:s(()=>[w(E(a.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})),e(h)?f("",!0):(r(),c(m,{key:3,label:"\u89D2\u8272\u6392\u5E8F",prop:"sort"},{default:s(()=>[o(J,{modelValue:e(t).sort,"onUpdate:modelValue":l[7]||(l[7]=a=>e(t).sort=a),placeholder:"\u8BF7\u8F93\u5165\u89D2\u8272\u6392\u5E8F",class:"!w-1/1"},null,8,["modelValue"])]),_:1})),e(h)?f("",!0):(r(),c(m,{key:4,label:"\u5F00\u542F\u72B6\u6001",prop:"status"},{default:s(()=>[o(q,{modelValue:e(t).status,"onUpdate:modelValue":l[8]||(l[8]=a=>e(t).status=a)},{default:s(()=>[(r(!0),M(R,null,S(e(le)(e(A).COMMON_STATUS),a=>(r(),c(N,{key:a.value,label:a.value},{default:s(()=>[w(E(a.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}))]),_:1},8,["model","rules"])),[[z,e(g)]])]),_:1},8,["title","modelValue"])}}})});export{n as C,L as _,fe as __tla};
