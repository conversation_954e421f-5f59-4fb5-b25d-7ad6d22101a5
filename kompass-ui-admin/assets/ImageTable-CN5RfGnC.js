import{d as w,T as b,H as d,o as e,c as l,F as B,k as C,g as s,t as I,i as m,w as o,l as j,_ as x,N as P,s as T,R as F,B as H,__tla as N}from"./index-BUSn51wb.js";let _,R=Promise.all([(()=>{try{return N}catch{}})()]).then(async()=>{let t,r,i,n;t={class:"waterfall"},r=["href"],i=["src"],n={class:"item-name"},_=H(w({__name:"ImageTable",props:{list:{},loading:{type:Boolean}},emits:["delete"],setup(u,{emit:p}){const c=u,f=p;return(q,z)=>{const g=x,y=P,h=T,k=b("hasPermi"),v=F;return d((e(),l("div",t,[(e(!0),l(B,null,C(c.list,a=>(e(),l("div",{class:"waterfall-item",key:a.id},[s("a",{target:"_blank",href:a.url},[s("img",{class:"material-img",src:a.url},null,8,i),s("div",n,I(a.name),1)],8,r),m(h,{justify:"center"},{default:o(()=>[d((e(),j(y,{type:"danger",circle:"",onClick:A=>f("delete",a.id)},{default:o(()=>[m(g,{icon:"ep:delete"})]),_:2},1032,["onClick"])),[[k,["mp:material:delete"]]])]),_:2},1024)]))),128))])),[[v,c.loading]])}}}),[["__scopeId","data-v-61ac441d"]])});export{R as __tla,_ as default};
