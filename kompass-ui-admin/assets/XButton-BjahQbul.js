import{d as f,p as n,b as u,o as s,l as a,w as _,ao as I,a as b,bs as m,N as k,a9 as c,j as y,t as g,_ as C,B as x,__tla as B}from"./index-BUSn51wb.js";let r,h=Promise.all([(()=>{try{return B}catch{}})()]).then(async()=>{r=x(f({name:"XButton",__name:"XButton",props:{modelValue:n.bool.def(!1),loading:n.bool.def(!1),preIcon:n.string.def(""),postIcon:n.string.def(""),title:n.string.def(""),type:n.oneOf(["","primary","success","warning","danger","info"]).def(""),link:n.bool.def(!1),circle:n.bool.def(!1),round:n.bool.def(!1),plain:n.bool.def(!1),onClick:{type:Function,default:null}},setup(e){const i=e,d=u(()=>{const l=["title","preIcon","postIcon","onClick"],t={...m(),...i};for(const o in t)l.indexOf(o)!==-1&&delete t[o];return t});return(l,t)=>{const o=C,p=k;return s(),a(p,I(b(d),{onClick:e.onClick}),{default:_(()=>[e.preIcon?(s(),a(o,{key:0,icon:e.preIcon,class:"mr-1px"},null,8,["icon"])):c("",!0),y(" "+g(e.title?e.title:"")+" ",1),e.postIcon?(s(),a(o,{key:1,icon:e.postIcon,class:"mr-1px"},null,8,["icon"])):c("",!0)]),_:1},16,["onClick"])}}}),[["__scopeId","data-v-cc9e668e"]])});export{r as _,h as __tla};
