import{d as J,r as u,f as K,u as M,bc as O,C as Q,T as X,o,c as N,i as a,w as l,a as r,F as P,k as Y,l as p,H as E,j as c,t as d,dV as W,G as Z,dX as b,g as $,J as ee,K as ae,L as re,O as le,v as te,P as ie,N as oe,Q as ne,R as pe,__tla as ce}from"./index-BUSn51wb.js";import{_ as se,__tla as de}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{E as me,__tla as ue}from"./el-text-CIwNlU-U.js";import{_ as _e,__tla as fe}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as be,__tla as we}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{b as v,d as U,__tla as ye}from"./formatTime-DWdBpgsM.js";import{e as ve,__tla as he}from"./index-Uo5NQqNb.js";import{R as ge}from"./common-BQQO87UM.js";import{_ as xe,__tla as ke}from"./ReceivableForm.vue_vue_type_script_setup_true_lang-37IhTDRa.js";let V,Te=Promise.all([(()=>{try{return ce}catch{}})(),(()=>{try{return de}catch{}})(),(()=>{try{return ue}catch{}})(),(()=>{try{return fe}catch{}})(),(()=>{try{return we}catch{}})(),(()=>{try{return ye}catch{}})(),(()=>{try{return he}catch{}})(),(()=>{try{return ke}catch{}})()]).then(async()=>{let h;h=$("div",{class:"pb-5 text-xl"},"\u5F85\u56DE\u6B3E\u63D0\u9192",-1),V=J({name:"ReceivablePlanRemindList",__name:"ReceivablePlanRemindList",setup(Ce){const w=u(!0),g=u(0),x=u([]),i=K({pageNo:1,pageSize:10,remindType:1}),z=u(),m=async()=>{w.value=!0;try{const y=await ve(i);x.value=y.list,g.value=y.total}finally{w.value=!1}},L=()=>{i.pageNo=1,m()},k=u(),{push:T}=M();return O(async()=>{await m()}),Q(async()=>{await m()}),(y,s)=>{const S=ee,D=ae,F=re,I=le,C=be,R=te,t=ie,B=_e,_=me,j=oe,q=ne,A=se,G=X("hasPermi"),H=pe;return o(),N(P,null,[a(C,null,{default:l(()=>[h,a(I,{ref_key:"queryFormRef",ref:z,inline:!0,model:r(i),class:"-mb-15px","label-width":"68px"},{default:l(()=>[a(F,{label:"\u5408\u540C\u72B6\u6001",prop:"remindType"},{default:l(()=>[a(D,{modelValue:r(i).remindType,"onUpdate:modelValue":s[0]||(s[0]=e=>r(i).remindType=e),class:"!w-240px",placeholder:"\u72B6\u6001",onChange:L},{default:l(()=>[(o(!0),N(P,null,Y(r(ge),(e,f)=>(o(),p(S,{label:e.label,value:e.value,key:f},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),a(C,null,{default:l(()=>[E((o(),p(q,{data:r(x),stripe:!0,"show-overflow-tooltip":!0},{default:l(()=>[a(t,{align:"center",fixed:"left",label:"\u5BA2\u6237\u540D\u79F0",prop:"customerName",width:"150"},{default:l(e=>[a(R,{underline:!1,type:"primary",onClick:f=>{return n=e.row.customerId,void T({name:"CrmCustomerDetail",params:{id:n}});var n}},{default:l(()=>[c(d(e.row.customerName),1)]),_:2},1032,["onClick"])]),_:1}),a(t,{align:"center",label:"\u5408\u540C\u7F16\u53F7",prop:"contractNo",width:"200px"}),a(t,{align:"center",label:"\u671F\u6570",prop:"period"},{default:l(e=>[a(R,{underline:!1,type:"primary",onClick:f=>{return n=e.row.id,void T({name:"CrmReceivablePlanDetail",params:{id:n}});var n}},{default:l(()=>[c(d(e.row.period),1)]),_:2},1032,["onClick"])]),_:1}),a(t,{align:"center",label:"\u8BA1\u5212\u56DE\u6B3E\u91D1\u989D\uFF08\u5143\uFF09",prop:"price",width:"160",formatter:r(W)},null,8,["formatter"]),a(t,{formatter:r(v),align:"center",label:"\u8BA1\u5212\u56DE\u6B3E\u65E5\u671F",prop:"returnTime",width:"180px"},null,8,["formatter"]),a(t,{align:"center",label:"\u63D0\u524D\u51E0\u5929\u63D0\u9192",prop:"remindDays",width:"150"}),a(t,{align:"center",label:"\u63D0\u9192\u65E5\u671F",prop:"remindTime",width:"180px",formatter:r(v)},null,8,["formatter"]),a(t,{align:"center",label:"\u56DE\u6B3E\u65B9\u5F0F",prop:"returnType",width:"130px"},{default:l(e=>[a(B,{type:r(Z).CRM_RECEIVABLE_RETURN_TYPE,value:e.row.returnType},null,8,["type","value"])]),_:1}),a(t,{align:"center",label:"\u5907\u6CE8",prop:"remark"}),a(t,{label:"\u8D1F\u8D23\u4EBA",prop:"ownerUserName",width:"120"}),a(t,{align:"center",label:"\u5B9E\u9645\u56DE\u6B3E\u91D1\u989D\uFF08\u5143\uFF09",prop:"receivable.price",width:"160"},{default:l(e=>[e.row.receivable?(o(),p(_,{key:0},{default:l(()=>[c(d(r(b)(e.row.receivable.price)),1)]),_:2},1024)):(o(),p(_,{key:1},{default:l(()=>[c(d(r(b)(0)),1)]),_:1}))]),_:1}),a(t,{align:"center",label:"\u5B9E\u9645\u56DE\u6B3E\u65E5\u671F",prop:"receivable.returnTime",width:"180px",formatter:r(v)},null,8,["formatter"]),a(t,{align:"center",label:"\u5B9E\u9645\u56DE\u6B3E\u91D1\u989D\uFF08\u5143\uFF09",prop:"receivable.price",width:"160"},{default:l(e=>[e.row.receivable?(o(),p(_,{key:0},{default:l(()=>[c(d(r(b)(e.row.price-e.row.receivable.price)),1)]),_:2},1024)):(o(),p(_,{key:1},{default:l(()=>[c(d(r(b)(e.row.price)),1)]),_:2},1024))]),_:1}),a(t,{formatter:r(U),align:"center",label:"\u66F4\u65B0\u65F6\u95F4",prop:"updateTime",width:"180px"},null,8,["formatter"]),a(t,{formatter:r(U),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"]),a(t,{align:"center",label:"\u521B\u5EFA\u4EBA",prop:"creatorName",width:"100px"}),a(t,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"180px"},{default:l(e=>[E((o(),p(j,{link:"",type:"success",onClick:f=>{return n=e.row,void k.value.open("create",void 0,n);var n},disabled:e.row.receivableId},{default:l(()=>[c(" \u521B\u5EFA\u56DE\u6B3E ")]),_:2},1032,["onClick","disabled"])),[[G,["crm:receivable:create"]]])]),_:1})]),_:1},8,["data"])),[[H,r(w)]]),a(A,{total:r(g),page:r(i).pageNo,"onUpdate:page":s[1]||(s[1]=e=>r(i).pageNo=e),limit:r(i).pageSize,"onUpdate:limit":s[2]||(s[2]=e=>r(i).pageSize=e),onPagination:m},null,8,["total","page","limit"])]),_:1}),a(xe,{ref_key:"receivableFormRef",ref:k,onSuccess:m},null,512)],64)}}})});export{V as _,Te as __tla};
