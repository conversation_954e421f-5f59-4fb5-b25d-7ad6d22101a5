import{by as a,e9 as c,ea as g,__tla as m}from"./index-BUSn51wb.js";import{f as y}from"./fetch-D5K_4anA.js";let t,h=Promise.all([(()=>{try{return m}catch{}})()]).then(async()=>{t={getChatMessageListByConversationId:async e=>await a.get({url:`/ai/chat/message/list-by-conversation-id?conversationId=${e}`}),sendChatMessageStream:async(e,s,n,i,o,r,d)=>{const l=c();return y(`${g.base_url}/ai/chat/message/send-stream`,{method:"post",headers:{"Content-Type":"application/json",Authorization:`Bearer ${l}`},openWhenHidden:!0,body:JSON.stringify({conversationId:e,content:s,useContext:i}),onmessage:o,onerror:r,onclose:d,signal:n.signal})},deleteChatMessage:async e=>await a.delete({url:`/ai/chat/message/delete?id=${e}`}),deleteByConversationId:async e=>await a.delete({url:`/ai/chat/message/delete-by-conversation-id?conversationId=${e}`}),getChatMessagePage:async e=>await a.get({url:"/ai/chat/message/page",params:e}),deleteChatMessageByAdmin:async e=>await a.delete({url:`/ai/chat/message/delete-by-admin?id=${e}`})}});export{t as C,h as __tla};
