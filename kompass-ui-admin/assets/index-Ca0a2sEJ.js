import{d as oe,u as ue,r as s,f as de,at as pe,C as se,T as ie,o as u,c as i,i as l,w as t,a,F as _,k as y,V as C,G as V,l as d,dR as _e,a9 as T,U as ne,H as g,a8 as ce,j as h,g as me,J as ye,K as ve,L as fe,M as be,Z as he,_ as ke,N as Ve,O as Ue,Q as we,R as Ce,__tla as Te}from"./index-BUSn51wb.js";import{_ as ge,__tla as Ee}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{E as xe,a as Pe,b as Ie,__tla as Re}from"./el-dropdown-item-CIJXMVYa.js";import{_ as Se,__tla as De}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as Ne,__tla as qe}from"./index-COobLwz-.js";import{_ as Ye,__tla as Ae}from"./OrderDeliveryForm.vue_vue_type_script_setup_true_lang-MqxSrqAW.js";import{_ as Oe,__tla as Le}from"./OrderUpdateRemarkForm.vue_vue_type_script_setup_true_lang-BDmCxfGu.js";import{e as Me,__tla as Fe}from"./index-BQq32Shw.js";import{a as He,__tla as ze}from"./index-BmYfnmm4.js";import{g as Ke,__tla as Je}from"./index-H6D82e8c.js";import{D as E,T as je}from"./constants-A8BI3pz7.js";import Xe,{__tla as Ge}from"./OrderTableColumn-AjIStixC.js";import{__tla as Qe}from"./index-Cch5e1V0.js";import{__tla as Ze}from"./el-card-CJbXGyyg.js";import{__tla as Be}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as We}from"./el-image-BjHZRFih.js";import{__tla as $e}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import"./color-BN7ZL7BD.js";import{__tla as ea}from"./formatTime-DWdBpgsM.js";let X,aa=Promise.all([(()=>{try{return Te}catch{}})(),(()=>{try{return Ee}catch{}})(),(()=>{try{return Re}catch{}})(),(()=>{try{return De}catch{}})(),(()=>{try{return qe}catch{}})(),(()=>{try{return Ae}catch{}})(),(()=>{try{return Le}catch{}})(),(()=>{try{return Fe}catch{}})(),(()=>{try{return ze}catch{}})(),(()=>{try{return Je}catch{}})(),(()=>{try{return Ge}catch{}})(),(()=>{try{return Qe}catch{}})(),(()=>{try{return Ze}catch{}})(),(()=>{try{return Be}catch{}})(),(()=>{try{return We}catch{}})(),(()=>{try{return $e}catch{}})(),(()=>{try{return ea}catch{}})()]).then(async()=>{let S;S={class:"flex items-center justify-center"},X=oe({name:"TradeOrder",__name:"index",setup(la){const{currentRoute:G,push:Q}=ue(),x=s(!0),D=s(2),P=s([]),N=s(),r=s({pageNo:1,pageSize:10,status:void 0,payChannelCode:void 0,createTime:void 0,terminal:void 0,type:void 0,deliveryType:void 0,logisticsId:void 0,pickUpStoreId:void 0,pickUpVerifyCode:void 0}),k=de({queryParam:""}),q=s([{value:"no",label:"\u8BA2\u5355\u53F7"},{value:"userId",label:"\u7528\u6237UID"},{value:"userNickname",label:"\u7528\u6237\u6635\u79F0"},{value:"userMobile",label:"\u7528\u6237\u7535\u8BDD"}]),Z=n=>{var o;(o=q.value.filter(v=>v.value!==n))==null||o.forEach(v=>{r.value.hasOwnProperty(v.value)&&delete r.value[v.value]})},f=async()=>{x.value=!0;try{const n=await Me(a(r));P.value=n.list,D.value=n.total}finally{x.value=!1}},I=async()=>{r.value.pageNo=1,await f()},B=()=>{var n;(n=N.value)==null||n.resetFields(),r.value={pageNo:1,pageSize:10,status:void 0,payChannelCode:void 0,createTime:void 0,terminal:void 0,type:void 0,deliveryType:void 0,logisticsId:void 0,pickUpStoreId:void 0,pickUpVerifyCode:void 0},I()},Y=s(),A=s();pe(()=>G.value,()=>{f()});const R=s([]),O=s([]);return se(async()=>{await f(),R.value=await He(),O.value=await Ke()}),(n,o)=>{const v=Ne,c=ye,m=ve,p=fe,W=be,L=he,b=ke,U=Ve,$=Ue,M=Se,F=xe,ee=Pe,ae=Ie,le=we,te=ge,H=ie("hasPermi"),re=Ce;return u(),i(_,null,[l(v,{title:"\u3010\u4EA4\u6613\u3011\u4EA4\u6613\u8BA2\u5355",url:"https://doc.iocoder.cn/mall/trade-order/"}),l(v,{title:"\u3010\u4EA4\u6613\u3011\u8D2D\u7269\u8F66",url:"https://doc.iocoder.cn/mall/trade-cart/"}),l(M,null,{default:t(()=>[l($,{ref_key:"queryFormRef",ref:N,inline:!0,model:a(r),class:"-mb-15px","label-width":"68px"},{default:t(()=>[l(p,{label:"\u8BA2\u5355\u72B6\u6001",prop:"status"},{default:t(()=>[l(m,{modelValue:a(r).status,"onUpdate:modelValue":o[0]||(o[0]=e=>a(r).status=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(u(!0),i(_,null,y(a(C)(a(V).TRADE_ORDER_STATUS),e=>(u(),d(c,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"\u652F\u4ED8\u65B9\u5F0F",prop:"payChannelCode"},{default:t(()=>[l(m,{modelValue:a(r).payChannelCode,"onUpdate:modelValue":o[1]||(o[1]=e=>a(r).payChannelCode=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(u(!0),i(_,null,y(a(_e)(a(V).PAY_CHANNEL_CODE),e=>(u(),d(c,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:t(()=>[l(W,{modelValue:a(r).createTime,"onUpdate:modelValue":o[2]||(o[2]=e=>a(r).createTime=e),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-280px","end-placeholder":"\u81EA\u5B9A\u4E49\u65F6\u95F4","start-placeholder":"\u81EA\u5B9A\u4E49\u65F6\u95F4",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),l(p,{label:"\u8BA2\u5355\u6765\u6E90",prop:"terminal"},{default:t(()=>[l(m,{modelValue:a(r).terminal,"onUpdate:modelValue":o[3]||(o[3]=e=>a(r).terminal=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(u(!0),i(_,null,y(a(C)(a(V).TERMINAL),e=>(u(),d(c,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"\u8BA2\u5355\u7C7B\u578B",prop:"type"},{default:t(()=>[l(m,{modelValue:a(r).type,"onUpdate:modelValue":o[4]||(o[4]=e=>a(r).type=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(u(!0),i(_,null,y(a(C)(a(V).TRADE_ORDER_TYPE),e=>(u(),d(c,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"\u914D\u9001\u65B9\u5F0F",prop:"deliveryType"},{default:t(()=>[l(m,{modelValue:a(r).deliveryType,"onUpdate:modelValue":o[5]||(o[5]=e=>a(r).deliveryType=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(u(!0),i(_,null,y(a(C)(a(V).TRADE_DELIVERY_TYPE),e=>(u(),d(c,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(r).deliveryType===a(E).EXPRESS.type?(u(),d(p,{key:0,label:"\u5FEB\u9012\u516C\u53F8",prop:"logisticsId"},{default:t(()=>[l(m,{modelValue:a(r).logisticsId,"onUpdate:modelValue":o[6]||(o[6]=e=>a(r).logisticsId=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(u(!0),i(_,null,y(a(O),e=>(u(),d(c,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):T("",!0),a(r).deliveryType===a(E).PICK_UP.type?(u(),d(p,{key:1,label:"\u81EA\u63D0\u95E8\u5E97",prop:"pickUpStoreId"},{default:t(()=>[l(m,{modelValue:a(r).pickUpStoreId,"onUpdate:modelValue":o[7]||(o[7]=e=>a(r).pickUpStoreId=e),class:"!w-280px",clearable:"",multiple:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(u(!0),i(_,null,y(a(R),e=>(u(),d(c,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):T("",!0),a(r).deliveryType===a(E).PICK_UP.type?(u(),d(p,{key:2,label:"\u6838\u9500\u7801",prop:"pickUpVerifyCode"},{default:t(()=>[l(L,{modelValue:a(r).pickUpVerifyCode,"onUpdate:modelValue":o[8]||(o[8]=e=>a(r).pickUpVerifyCode=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u81EA\u63D0\u6838\u9500\u7801",onKeyup:ne(I,["enter"])},null,8,["modelValue"])]),_:1})):T("",!0),l(p,{label:"\u805A\u5408\u641C\u7D22"},{default:t(()=>[g(l(L,{modelValue:a(r)[a(k).queryParam],"onUpdate:modelValue":o[10]||(o[10]=e=>a(r)[a(k).queryParam]=e),type:a(k).queryParam==="userId"?"number":"text",class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165"},{prepend:t(()=>[l(m,{modelValue:a(k).queryParam,"onUpdate:modelValue":o[9]||(o[9]=e=>a(k).queryParam=e),class:"!w-110px",clearable:"",placeholder:"\u5168\u90E8",onChange:Z},{default:t(()=>[(u(!0),i(_,null,y(a(q),e=>(u(),d(c,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["modelValue","type"]),[[ce,!0]])]),_:1}),l(p,null,{default:t(()=>[l(U,{onClick:I},{default:t(()=>[l(b,{class:"mr-5px",icon:"ep:search"}),h(" \u641C\u7D22 ")]),_:1}),l(U,{onClick:B},{default:t(()=>[l(b,{class:"mr-5px",icon:"ep:refresh"}),h(" \u91CD\u7F6E ")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),l(M,null,{default:t(()=>[g((u(),d(le,{data:a(P),"row-key":"id"},{default:t(()=>[l(a(Xe),{list:a(P),"pick-up-store-list":a(R)},{default:t(({row:e})=>[me("div",S,[g((u(),d(U,{link:"",type:"primary",onClick:z=>{return w=e.id,void Q({name:"TradeOrderDetail",params:{id:w}});var w}},{default:t(()=>[l(b,{icon:"ep:notification"}),h(" \u8BE6\u60C5 ")]),_:2},1032,["onClick"])),[[H,["trade:order:query"]]]),g((u(),d(ae,{onCommand:z=>((w,K)=>{var J,j;switch(w){case"remark":(J=A.value)==null||J.open(K);break;case"delivery":(j=Y.value)==null||j.open(K)}})(z,e)},{dropdown:t(()=>[l(ee,null,{default:t(()=>[e.deliveryType===a(E).EXPRESS.type&&e.status===a(je).UNDELIVERED.status?(u(),d(F,{key:0,command:"delivery"},{default:t(()=>[l(b,{icon:"ep:takeaway-box"}),h(" \u53D1\u8D27 ")]),_:1})):T("",!0),l(F,{command:"remark"},{default:t(()=>[l(b,{icon:"ep:chat-line-square"}),h(" \u5907\u6CE8 ")]),_:1})]),_:2},1024)]),default:t(()=>[l(U,{link:"",type:"primary"},{default:t(()=>[l(b,{icon:"ep:d-arrow-right"}),h(" \u66F4\u591A ")]),_:1})]),_:2},1032,["onCommand"])),[[H,["trade:order:update"]]])])]),_:1},8,["list","pick-up-store-list"])]),_:1},8,["data"])),[[re,a(x)]]),l(te,{limit:a(r).pageSize,"onUpdate:limit":o[11]||(o[11]=e=>a(r).pageSize=e),page:a(r).pageNo,"onUpdate:page":o[12]||(o[12]=e=>a(r).pageNo=e),total:a(D),onPagination:f},null,8,["limit","page","total"])]),_:1}),l(Ye,{ref_key:"deliveryFormRef",ref:Y,onSuccess:f},null,512),l(Oe,{ref_key:"updateRemarkForm",ref:A,onSuccess:f},null,512)],64)}}})});export{aa as __tla,X as default};
