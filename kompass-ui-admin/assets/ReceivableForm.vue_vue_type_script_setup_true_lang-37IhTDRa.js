import{d as z,r as i,f as A,o as c,l as m,w as r,i as t,j as F,a as l,H as D,c as I,k as V,F as w,V as Q,G as W,y as X,n as $,I as ee,e as ae,Z as le,L as te,E as ue,J as re,K as de,s as se,cc as oe,M as ne,O as ce,N as ie,R as pe,__tla as me}from"./index-BUSn51wb.js";import{_ as _e,__tla as fe}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{a as ve,__tla as be}from"./index-Uo5NQqNb.js";import{a as ye,c as Ie,u as Ve,__tla as we}from"./index-D3Ji6shA.js";import{g as he,__tla as ge}from"./index-BYXzDB8j.js";import{a as Ue,__tla as ke}from"./index-CD52sTBY.js";import{d as Te,__tla as Ce}from"./index-DrB1WZUR.js";let S,Ee=Promise.all([(()=>{try{return me}catch{}})(),(()=>{try{return fe}catch{}})(),(()=>{try{return be}catch{}})(),(()=>{try{return we}catch{}})(),(()=>{try{return ge}catch{}})(),(()=>{try{return ke}catch{}})(),(()=>{try{return Ce}catch{}})()]).then(async()=>{S=z({__name:"ReceivableForm",emits:["success"],setup(Re,{expose:L,emit:M}){const{t:k}=$(),C=ee(),E=i([]),_=i(!1),R=i(""),f=i(!1),p=i(""),a=i({}),N=A({customerId:[{required:!0,message:"\u5BA2\u6237\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],contractId:[{required:!0,message:"\u5408\u540C\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],returnTime:[{required:!0,message:"\u56DE\u6B3E\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],price:[{required:!0,message:"\u56DE\u6B3E\u91D1\u989D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),h=i(),q=i([]),g=i([]),U=i([]);L({open:async(d,u,s)=>{var n;if(_.value=!0,R.value=k("action."+d),p.value=d,G(),u){f.value=!0;try{const o=await ye(u);a.value=o,await T(o.customerId),a.value.contractId=(n=o==null?void 0:o.contract)==null?void 0:n.id}finally{f.value=!1}}E.value=await he(),q.value=await Ue(),p.value==="create"&&(a.value.ownerUserId=ae().getUser.id),s&&(a.value.customerId=s.customerId,await T(s.customerId),a.value.contractId=s.contractId,await x(s.contractId),s.id&&(a.value.planId=s.id,a.value.price=s.price,a.value.returnType=s.returnType))}});const j=M,B=async()=>{if(h&&await h.value.validate()){f.value=!0;try{const d=a.value;p.value==="create"?(await Ie(d),C.success(k("common.createSuccess"))):(await Ve(d),C.success(k("common.updateSuccess"))),_.value=!1,j("success")}finally{f.value=!1}}},G=()=>{var d;a.value={},(d=h.value)==null||d.resetFields()},T=async d=>{a.value.contractId=void 0,d&&(g.value=[],g.value=await Te(d))},x=async d=>{if(a.value.planId=void 0,d){U.value=[],U.value=await ve(a.value.customerId,d);const u=g.value.find(s=>s.id===d);u&&(a.value.price=u.totalPrice-u.totalReceivablePrice)}},H=d=>{if(!d)return;const u=U.value.find(s=>s.id===d);u&&(a.value.price=u.price,a.value.returnType=u.returnType)};return(d,u)=>{const s=le,n=te,o=ue,v=re,b=de,y=se,J=oe,K=ne,O=ce,P=ie,Y=_e,Z=pe;return c(),m(Y,{modelValue:l(_),"onUpdate:modelValue":u[10]||(u[10]=e=>X(_)?_.value=e:null),title:l(R)},{footer:r(()=>[t(P,{disabled:l(f),type:"primary",onClick:B},{default:r(()=>[F("\u786E \u5B9A")]),_:1},8,["disabled"]),t(P,{onClick:u[9]||(u[9]=e=>_.value=!1)},{default:r(()=>[F("\u53D6 \u6D88")]),_:1})]),default:r(()=>[D((c(),m(O,{ref_key:"formRef",ref:h,model:l(a),rules:l(N),"label-width":"100px"},{default:r(()=>[t(y,null,{default:r(()=>[t(o,{span:12},{default:r(()=>[t(n,{label:"\u56DE\u6B3E\u7F16\u53F7",prop:"no"},{default:r(()=>[t(s,{modelValue:l(a).no,"onUpdate:modelValue":u[0]||(u[0]=e=>l(a).no=e),disabled:"",placeholder:"\u4FDD\u5B58\u65F6\u81EA\u52A8\u751F\u6210"},null,8,["modelValue"])]),_:1})]),_:1}),t(o,{span:12},{default:r(()=>[t(n,{label:"\u8D1F\u8D23\u4EBA",prop:"ownerUserId"},{default:r(()=>[t(b,{modelValue:l(a).ownerUserId,"onUpdate:modelValue":u[1]||(u[1]=e=>l(a).ownerUserId=e),disabled:l(p)!=="create",class:"w-1/1"},{default:r(()=>[(c(!0),I(w,null,V(l(E),e=>(c(),m(v,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),t(y,null,{default:r(()=>[t(o,{span:12},{default:r(()=>[t(n,{label:"\u5BA2\u6237\u540D\u79F0",prop:"customerId"},{default:r(()=>[t(b,{modelValue:l(a).customerId,"onUpdate:modelValue":u[2]||(u[2]=e=>l(a).customerId=e),disabled:l(p)!=="create",class:"w-1/1",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237",onChange:T},{default:r(()=>[(c(!0),I(w,null,V(l(q),e=>(c(),m(v,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),t(o,{span:12},{default:r(()=>[t(n,{label:"\u5408\u540C\u540D\u79F0",prop:"contractId"},{default:r(()=>[t(b,{modelValue:l(a).contractId,"onUpdate:modelValue":u[3]||(u[3]=e=>l(a).contractId=e),disabled:l(p)!=="create"||!l(a).customerId,class:"w-1/1",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u5408\u540C",onChange:x},{default:r(()=>[(c(!0),I(w,null,V(l(g),e=>(c(),m(v,{key:e.id,disabled:e.auditStatus!==20,label:e.name,value:e.id},null,8,["disabled","label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),t(y,null,{default:r(()=>[t(o,{span:12},{default:r(()=>[t(n,{label:"\u56DE\u6B3E\u671F\u6570",prop:"planId"},{default:r(()=>[t(b,{modelValue:l(a).planId,"onUpdate:modelValue":u[4]||(u[4]=e=>l(a).planId=e),disabled:l(p)!=="create"||!l(a).contractId,class:"!w-1/1",placeholder:"\u8BF7\u9009\u62E9\u56DE\u6B3E\u671F\u6570",onChange:H},{default:r(()=>[(c(!0),I(w,null,V(l(U),e=>(c(),m(v,{key:e.id,disabled:e.receivableId,label:"\u7B2C "+e.period+" \u671F",value:e.id},null,8,["disabled","label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),t(o,{span:12},{default:r(()=>[t(n,{label:"\u56DE\u6B3E\u65B9\u5F0F",prop:"returnType"},{default:r(()=>[t(b,{modelValue:l(a).returnType,"onUpdate:modelValue":u[5]||(u[5]=e=>l(a).returnType=e),class:"w-1/1",placeholder:"\u8BF7\u9009\u62E9\u56DE\u6B3E\u65B9\u5F0F"},{default:r(()=>[(c(!0),I(w,null,V(l(Q)(l(W).CRM_RECEIVABLE_RETURN_TYPE),e=>(c(),m(v,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),t(y,null,{default:r(()=>[t(o,{span:12},{default:r(()=>[t(n,{label:"\u56DE\u6B3E\u91D1\u989D",prop:"price"},{default:r(()=>[t(J,{modelValue:l(a).price,"onUpdate:modelValue":u[6]||(u[6]=e=>l(a).price=e),min:.01,precision:2,class:"!w-100%","controls-position":"right",placeholder:"\u8BF7\u8F93\u5165\u56DE\u6B3E\u91D1\u989D"},null,8,["modelValue"])]),_:1})]),_:1}),t(o,{span:12},{default:r(()=>[t(n,{label:"\u56DE\u6B3E\u65E5\u671F",prop:"returnTime"},{default:r(()=>[t(K,{modelValue:l(a).returnTime,"onUpdate:modelValue":u[7]||(u[7]=e=>l(a).returnTime=e),placeholder:"\u9009\u62E9\u56DE\u6B3E\u65E5\u671F",type:"date","value-format":"x"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),t(y,null,{default:r(()=>[t(o,{span:24},{default:r(()=>[t(n,{label:"\u5907\u6CE8",prop:"remark"},{default:r(()=>[t(s,{modelValue:l(a).remark,"onUpdate:modelValue":u[8]||(u[8]=e=>l(a).remark=e),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])),[[Z,l(f)]])]),_:1},8,["modelValue","title"])}}})});export{S as _,Ee as __tla};
