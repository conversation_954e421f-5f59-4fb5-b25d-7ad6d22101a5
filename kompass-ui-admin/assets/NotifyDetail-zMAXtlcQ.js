import{_ as t,__tla as r}from"./NotifyDetail.vue_vue_type_script_setup_true_lang-D8zj-0nj.js";import{__tla as _}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as a}from"./index-BUSn51wb.js";import{__tla as l}from"./el-descriptions-item-dD3qa0ub.js";import{__tla as o}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import"./color-BN7ZL7BD.js";import{__tla as m}from"./formatTime-DWdBpgsM.js";let c=Promise.all([(()=>{try{return r}catch{}})(),(()=>{try{return _}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return m}catch{}})()]).then(async()=>{});export{c as __tla,t as default};
