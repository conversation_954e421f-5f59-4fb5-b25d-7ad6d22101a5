import{d as J,n as j,I as A,r as d,o as m,c as b,i as r,w as o,a as t,j as f,H as E,l as g,F as L,k as M,cJ as T,G as W,t as q,y as B,am as G,an as H,L as P,Z,O as z,N as D,R as K,__tla as Q}from"./index-BUSn51wb.js";import{_ as X,__tla as Y}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{C as $}from"./constants-A8BI3pz7.js";import{g as aa,c as ea,u as la,__tla as ta}from"./index-BUKPQywH.js";let h,sa=Promise.all([(()=>{try{return Q}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return ta}catch{}})()]).then(async()=>{h=J({name:"WalletChannelForm",__name:"WalletChannelForm",emits:["success"],setup(ua,{expose:w,emit:V}){const{t:_}=j(),p=A(),n=d(!1),v=d(""),c=d(!1),e=d({appId:"",code:"",status:void 0,feeRate:0,remark:"",config:{name:"mock-conf"}}),C={status:[{required:!0,message:"\u6E20\u9053\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]},i=d();w({open:async(l,a)=>{n.value=!0,c.value=!0,I(l,a);try{const u=await aa(l,a);u&&u.id&&(e.value=u,e.value.config=JSON.parse(u.config)),v.value=e.value.id?"\u7F16\u8F91\u652F\u4ED8\u6E20\u9053":"\u521B\u5EFA\u652F\u4ED8\u6E20\u9053"}finally{c.value=!1}}});const S=V,x=async()=>{if(i&&await i.value.validate()){c.value=!0;try{const l={...e.value};l.config=JSON.stringify(e.value.config),l.id?(await la(l),p.success(_("common.updateSuccess"))):(await ea(l),p.success(_("common.createSuccess"))),n.value=!1,S("success")}finally{c.value=!1}}},I=(l,a)=>{var u;e.value={appId:l,code:a,status:$.ENABLE,remark:"",feeRate:0,config:{name:"mock-conf"}},(u=i.value)==null||u.resetFields()};return(l,a)=>{const u=G,N=H,y=P,O=Z,F=z,k=D,R=X,U=K;return m(),b("div",null,[r(R,{modelValue:t(n),"onUpdate:modelValue":a[3]||(a[3]=s=>B(n)?n.value=s:null),title:t(v),onClosed:l.close,width:"800px"},{footer:o(()=>[r(k,{disabled:t(c),type:"primary",onClick:x},{default:o(()=>[f("\u786E \u5B9A")]),_:1},8,["disabled"]),r(k,{onClick:a[2]||(a[2]=s=>n.value=!1)},{default:o(()=>[f("\u53D6 \u6D88")]),_:1})]),default:o(()=>[E((m(),g(F,{ref_key:"formRef",ref:i,model:t(e),rules:C,"label-width":"100px"},{default:o(()=>[r(y,{"label-width":"180px",label:"\u6E20\u9053\u72B6\u6001",prop:"status"},{default:o(()=>[r(N,{modelValue:t(e).status,"onUpdate:modelValue":a[0]||(a[0]=s=>t(e).status=s)},{default:o(()=>[(m(!0),b(L,null,M(t(T)(t(W).COMMON_STATUS),s=>(m(),g(u,{key:parseInt(s.value),label:parseInt(s.value)},{default:o(()=>[f(q(s.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),r(y,{"label-width":"180px",label:"\u5907\u6CE8",prop:"remark"},{default:o(()=>[r(O,{modelValue:t(e).remark,"onUpdate:modelValue":a[1]||(a[1]=s=>t(e).remark=s),style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])),[[U,t(c)]])]),_:1},8,["modelValue","title","onClosed"])])}}})});export{h as _,sa as __tla};
