import{by as I,d as P,S as R,u as B,r as T,f as U,b as D,a as l,at as E,H as G,a8 as K,o as p,c as _,i as n,w as c,g as M,j as w,F as Q,k as W,l as X,t as Y,b1 as z,z as Z,A as $,ai as ee,ca as se,L as ae,N as te,O as oe,__tla as re}from"./index-BUSn51wb.js";import{u as le,L as C,_ as ne,__tla as ce}from"./LoginFormTitle.vue_vue_type_script_setup_true_lang-DUoWRVM3.js";let V,ie=Promise.all([(()=>{try{return re}catch{}})(),(()=>{try{return ce}catch{}})()]).then(async()=>{let k,b,O;k={class:"form-cont"},b={key:0},O={key:1},V=P({name:"SSOLogin",__name:"SSOLogin",setup(ue){const i=R(),{currentRoute:F}=B(),{getLoginState:N,setLoginState:j}=le(),q=T({name:"",logo:""}),s=U({responseType:"",clientId:"",redirectUri:"",state:"",scopes:[]}),A=D(()=>l(N)===C.SSO),u=U({scopes:[]}),d=T(!1),H=async()=>{if(i.query.client_id===void 0)return;if(s.responseType=i.query.response_type,s.clientId=i.query.client_id,s.redirectUri=i.query.redirect_uri,s.state=i.query.state,i.query.scope&&(s.scopes=i.query.scope.split(" ")),s.scopes.length>0){const e=await x(!0,s.scopes,[]);if(e)return void(location.href=e)}const t=await(a=s.clientId,I.get({url:"/system/oauth2/authorize?clientId="+a}));var a;let o;if(q.value=t.client,s.scopes.length>0){o=[];for(const e of t.scopes)s.scopes.indexOf(e.key)>=0&&o.push(e)}else{o=t.scopes;for(const e of o)s.scopes.push(e.key)}for(const e of o)e.value&&u.scopes.push(e.key)},L=async t=>{let a,o;t?(a=u.scopes,o=s.scopes.filter(e=>a.indexOf(e)===-1)):(a=[],o=s.scopes),d.value=!0;try{const e=await x(!1,a,o);if(!e)return;location.href=e}finally{d.value=!1}},x=(t,a,o)=>((e,m,h,f,y,g,r)=>{const v={};for(const S of g)v[S]=!0;for(const S of r)v[S]=!1;return I.post({url:"/system/oauth2/authorize",headers:{"Content-type":"application/x-www-form-urlencoded"},params:{response_type:e,client_id:m,redirect_uri:h,state:f,auto_approve:y,scope:JSON.stringify(v)}})})(s.responseType,s.clientId,s.redirectUri,s.state,t,a,o),J=t=>{switch(t){case"user.read":return"\u8BBF\u95EE\u4F60\u7684\u4E2A\u4EBA\u4FE1\u606F";case"user.write":return"\u4FEE\u6539\u4F60\u7684\u4E2A\u4EBA\u4FE1\u606F";default:return t}};return E(()=>F.value,t=>{t.name==="SSOLogin"&&(j(C.SSO),H())},{immediate:!0}),(t,a)=>{const o=Z,e=$,m=ee,h=se,f=ae,y=te,g=oe;return G((p(),_("div",k,[n(ne,{style:{width:"100%"}}),n(e,{class:"form",style:{float:"none"},value:"uname"},{default:c(()=>[n(o,{label:l(q).name,name:"uname"},null,8,["label"])]),_:1}),M("div",null,[n(g,{model:l(u),class:"login-form"},{default:c(()=>[w(" \u6B64\u7B2C\u4E09\u65B9\u5E94\u7528\u8BF7\u6C42\u83B7\u5F97\u4EE5\u4E0B\u6743\u9650\uFF1A "),n(f,{prop:"scopes"},{default:c(()=>[n(h,{modelValue:l(u).scopes,"onUpdate:modelValue":a[0]||(a[0]=r=>l(u).scopes=r)},{default:c(()=>[(p(!0),_(Q,null,W(l(s).scopes,r=>(p(),X(m,{key:r,label:r,style:{display:"block","margin-bottom":"-10px"}},{default:c(()=>[w(Y(J(r)),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),n(f,{class:"w-1/1"},{default:c(()=>[n(y,{loading:l(d),class:"w-6/10",type:"primary",onClick:a[1]||(a[1]=z(r=>L(!0),["prevent"]))},{default:c(()=>[l(d)?(p(),_("span",O,"\u6388 \u6743 \u4E2D...")):(p(),_("span",b,"\u540C\u610F\u6388\u6743"))]),_:1},8,["loading"]),n(y,{class:"w-3/10",onClick:a[2]||(a[2]=z(r=>L(!1),["prevent"]))},{default:c(()=>[w("\u62D2\u7EDD")]),_:1})]),_:1})]),_:1},8,["model"])])],512)),[[K,l(A)]])}}})});export{V as _,ie as __tla};
