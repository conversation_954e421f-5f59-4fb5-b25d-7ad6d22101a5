import{d as j,n as G,I as Z,r as o,f as z,o as h,l as V,w as s,i as u,a,j as v,H as D,y as g,eh as J,c as Q,F as W,k as X,V as Y,G as $,t as ee,Z as ae,L as le,ce as te,am as se,an as ue,O as re,N as oe,R as ne,B as de,__tla as me}from"./index-BUSn51wb.js";import{_ as ce,__tla as ie}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{E as _e,__tla as pe}from"./el-card-CJbXGyyg.js";import{C as I}from"./constants-A8BI3pz7.js";import{d as ve,h as fe}from"./tree-BMa075Oj.js";import{a as he,c as ye,u as ke,__tla as Ve}from"./index-BVb6TNjB.js";import{g as ge,__tla as be}from"./index-B77mwhR6.js";let E,xe=Promise.all([(()=>{try{return me}catch{}})(),(()=>{try{return ie}catch{}})(),(()=>{try{return pe}catch{}})(),(()=>{try{return Ve}catch{}})(),(()=>{try{return be}catch{}})()]).then(async()=>{E=de(j({name:"SystemTenantPackageForm",__name:"TenantPackageForm",emits:["success"],setup(Ce,{expose:N,emit:S}){const{t:y}=G(),b=Z(),m=o(!1),x=o(""),c=o(!1),C=o(""),r=o({id:null,name:null,remark:null,menuIds:[],status:I.ENABLE}),F=z({name:[{required:!0,message:"\u5957\u9910\u540D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],menuIds:[{required:!0,message:"\u5173\u8054\u7684\u83DC\u5355\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),f=o(),k=o([]),i=o(!1),d=o(),p=o(!1);N({open:async(t,e)=>{if(m.value=!0,x.value=y("action."+t),C.value=t,A(),k.value=fe(await ge()),e){c.value=!0;try{const n=await he(e);r.value=n,n.menuIds.forEach(_=>{d.value.setChecked(_,!0,!1)})}finally{c.value=!1}}}});const T=S,q=async()=>{if(f&&await f.value.validate()){c.value=!0;try{const t=r.value;t.menuIds=[...d.value.getCheckedKeys(!1),...d.value.getHalfCheckedKeys()],C.value==="create"?(await ye(t),b.success(y("common.createSuccess"))):(await ke(t),b.success(y("common.updateSuccess"))),m.value=!1,T("success")}finally{c.value=!1}}},A=()=>{var t,e;p.value=!1,i.value=!1,r.value={id:null,name:null,remark:null,menuIds:[],status:I.ENABLE},(t=d.value)==null||t.setCheckedNodes([]),(e=f.value)==null||e.resetFields()},H=()=>{d.value.setCheckedNodes(p.value?k.value:[])},B=()=>{var e;const t=(e=d.value)==null?void 0:e.store.nodesMap;for(let n in t)t[n].expanded!==i.value&&(t[n].expanded=i.value)};return(t,e)=>{const n=ae,_=le,w=te,L=_e,M=se,O=ue,P=re,U=oe,R=ce,K=ne;return h(),V(R,{modelValue:a(m),"onUpdate:modelValue":e[6]||(e[6]=l=>g(m)?m.value=l:null),title:a(x)},{footer:s(()=>[u(U,{disabled:a(c),type:"primary",onClick:q},{default:s(()=>[v("\u786E \u5B9A")]),_:1},8,["disabled"]),u(U,{onClick:e[5]||(e[5]=l=>m.value=!1)},{default:s(()=>[v("\u53D6 \u6D88")]),_:1})]),default:s(()=>[D((h(),V(P,{ref_key:"formRef",ref:f,model:a(r),rules:a(F),"label-width":"80px"},{default:s(()=>[u(_,{label:"\u5957\u9910\u540D",prop:"name"},{default:s(()=>[u(n,{modelValue:a(r).name,"onUpdate:modelValue":e[0]||(e[0]=l=>a(r).name=l),placeholder:"\u8BF7\u8F93\u5165\u5957\u9910\u540D"},null,8,["modelValue"])]),_:1}),u(_,{label:"\u83DC\u5355\u6743\u9650"},{default:s(()=>[u(L,{class:"cardHeight"},{header:s(()=>[v(" \u5168\u9009/\u5168\u4E0D\u9009: "),u(w,{modelValue:a(p),"onUpdate:modelValue":e[1]||(e[1]=l=>g(p)?p.value=l:null),"active-text":"\u662F","inactive-text":"\u5426","inline-prompt":"",onChange:H},null,8,["modelValue"]),v(" \u5168\u90E8\u5C55\u5F00/\u6298\u53E0: "),u(w,{modelValue:a(i),"onUpdate:modelValue":e[2]||(e[2]=l=>g(i)?i.value=l:null),"active-text":"\u5C55\u5F00","inactive-text":"\u6298\u53E0","inline-prompt":"",onChange:B},null,8,["modelValue"])]),default:s(()=>[u(a(J),{ref_key:"treeRef",ref:d,data:a(k),props:a(ve),"empty-text":"\u52A0\u8F7D\u4E2D\uFF0C\u8BF7\u7A0D\u5019","node-key":"id","show-checkbox":""},null,8,["data","props"])]),_:1})]),_:1}),u(_,{label:"\u72B6\u6001",prop:"status"},{default:s(()=>[u(O,{modelValue:a(r).status,"onUpdate:modelValue":e[3]||(e[3]=l=>a(r).status=l)},{default:s(()=>[(h(!0),Q(W,null,X(a(Y)(a($).COMMON_STATUS),l=>(h(),V(M,{key:l.value,label:l.value},{default:s(()=>[v(ee(l.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(_,{label:"\u5907\u6CE8",prop:"remark"},{default:s(()=>[u(n,{modelValue:a(r).remark,"onUpdate:modelValue":e[4]||(e[4]=l=>a(r).remark=l),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[K,a(c)]])]),_:1},8,["modelValue","title"])}}}),[["__scopeId","data-v-5f5e2086"]])});export{xe as __tla,E as default};
