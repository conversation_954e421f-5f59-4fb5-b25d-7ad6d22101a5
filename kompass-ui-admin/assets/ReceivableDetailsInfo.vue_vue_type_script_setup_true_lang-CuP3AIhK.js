import{_ as I,__tla as h}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{d as T,r as R,o as Y,l as x,w as e,i as a,j as r,t as _,a as s,dX as D,G as N,y as V,g as d,__tla as M}from"./index-BUSn51wb.js";import{E as U,a as g,__tla as j}from"./el-collapse-item-B_QvnH_b.js";import{E as w,a as C,__tla as P}from"./el-descriptions-item-dD3qa0ub.js";import{_ as k,__tla as A}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{f as o,__tla as B}from"./formatTime-DWdBpgsM.js";let p,G=Promise.all([(()=>{try{return h}catch{}})(),(()=>{try{return M}catch{}})(),(()=>{try{return j}catch{}})(),(()=>{try{return P}catch{}})(),(()=>{try{return A}catch{}})(),(()=>{try{return B}catch{}})()]).then(async()=>{let n,m;n=d("span",{class:"text-base font-bold"},"\u57FA\u672C\u4FE1\u606F",-1),m=d("span",{class:"text-base font-bold"},"\u7CFB\u7EDF\u4FE1\u606F",-1),p=T({__name:"ReceivableDetailsInfo",props:{receivable:{}},setup(L){const c=R(["basicInfo","systemInfo"]);return(l,f)=>{const t=w,v=k,b=C,i=U,y=g,E=I;return Y(),x(E,null,{default:e(()=>[a(y,{modelValue:s(c),"onUpdate:modelValue":f[0]||(f[0]=u=>V(c)?c.value=u:null)},{default:e(()=>[a(i,{name:"basicInfo"},{title:e(()=>[n]),default:e(()=>[a(b,{column:4},{default:e(()=>[a(t,{label:"\u56DE\u6B3E\u7F16\u53F7"},{default:e(()=>[r(_(l.receivable.no),1)]),_:1}),a(t,{label:"\u5BA2\u6237\u540D\u79F0"},{default:e(()=>[r(_(l.receivable.customerName),1)]),_:1}),a(t,{label:"\u5408\u540C\u7F16\u53F7"},{default:e(()=>{var u;return[r(_((u=l.receivable.contract)==null?void 0:u.no),1)]}),_:1}),a(t,{label:"\u56DE\u6B3E\u65E5\u671F"},{default:e(()=>[r(_(s(o)(l.receivable.returnTime,"YYYY-MM-DD")),1)]),_:1}),a(t,{label:"\u56DE\u6B3E\u91D1\u989D"},{default:e(()=>[r(_(s(D)(l.receivable.price)),1)]),_:1}),a(t,{label:"\u56DE\u6B3E\u65B9\u5F0F"},{default:e(()=>[a(v,{type:s(N).CRM_RECEIVABLE_RETURN_TYPE,value:l.receivable.returnType},null,8,["type","value"])]),_:1}),a(t,{label:"\u5907\u6CE8"},{default:e(()=>[r(_(l.receivable.remark),1)]),_:1})]),_:1})]),_:1}),a(i,{name:"systemInfo"},{title:e(()=>[m]),default:e(()=>[a(b,{column:4},{default:e(()=>[a(t,{label:"\u8D1F\u8D23\u4EBA"},{default:e(()=>[r(_(l.receivable.ownerUserName),1)]),_:1}),a(t,{label:"\u521B\u5EFA\u4EBA"},{default:e(()=>[r(_(l.receivable.creatorName),1)]),_:1}),a(t,{label:"\u521B\u5EFA\u65F6\u95F4"},{default:e(()=>[r(_(s(o)(l.receivable.createTime)),1)]),_:1}),a(t,{label:"\u66F4\u65B0\u65F6\u95F4"},{default:e(()=>[r(_(s(o)(l.receivable.updateTime)),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})}}})});export{p as _,G as __tla};
