import{d as W,r as d,f as D,C as Q,o,l as m,w as l,i as s,j as n,a as e,H as X,c as b,k as S,F as R,y as E,t as Y,a9 as T,V as Z,G as $,I as ee,J as ae,K as le,L as te,am as re,an as se,ai as ue,ca as _e,O as oe,N as ne,R as de,__tla as ie}from"./index-BUSn51wb.js";import{_ as me,__tla as ce}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{g as fe,__tla as Ce}from"./index-BYXzDB8j.js";import{t as pe,__tla as ye}from"./index-M52UJVMY.js";import{a as we,__tla as ve}from"./index-CRiW4Z5g.js";import{t as be,__tla as Re}from"./index-9ux5MgCS.js";import{t as Te,__tla as Oe}from"./index-CD52sTBY.js";import{t as Me,__tla as Ue}from"./index-DrB1WZUR.js";import{P as Ve,B as r,__tla as he}from"./index-pKzyIv29.js";let N,Se=Promise.all([(()=>{try{return ie}catch{}})(),(()=>{try{return ce}catch{}})(),(()=>{try{return Ce}catch{}})(),(()=>{try{return ye}catch{}})(),(()=>{try{return ve}catch{}})(),(()=>{try{return Re}catch{}})(),(()=>{try{return Oe}catch{}})(),(()=>{try{return Ue}catch{}})(),(()=>{try{return he}catch{}})()]).then(async()=>{N=W({name:"CrmTransferForm",__name:"TransferForm",props:{bizType:{}},emits:["success"],setup(k,{expose:I,emit:L}){const O=k,M=ee(),i=d(!1),y=d(""),c=d(!1),U=d([]),f=d(!1),_=d({}),g=D({newOwnerUserId:[{required:!0,message:"\u65B0\u8D1F\u8D23\u4EBA\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],oldOwnerPermissionLevel:[{required:!0,message:"\u8001\u8D1F\u8D23\u4EBA\u52A0\u5165\u56E2\u961F\u540E\u7684\u6743\u9650\u7EA7\u522B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),C=d();I({open:async t=>{i.value=!0,y.value=F(),x(),_.value.id=t}});const z=t=>{t||(_.value.oldOwnerPermissionLevel=void 0)},A=L,P=async()=>{if(C&&await C.value.validate()){c.value=!0;try{const t=_.value;await B(e(t)),M.success(y.value+"\u6210\u529F"),i.value=!1,A("success")}finally{c.value=!1}}},B=async t=>{switch(O.bizType){case r.CRM_CLUE:return await we(t);case r.CRM_CUSTOMER:return await Te(t);case r.CRM_CONTACT:return await be(t);case r.CRM_BUSINESS:return await pe(t);case r.CRM_CONTRACT:return await Me(t);default:throw M.error("\u3010\u8F6C\u79FB\u5931\u8D25\u3011\u6CA1\u6709\u8F6C\u79FB\u63A5\u53E3"),new Error("\u3010\u8F6C\u79FB\u5931\u8D25\u3011\u6CA1\u6709\u8F6C\u79FB\u63A5\u53E3")}},F=()=>{switch(O.bizType){case r.CRM_CLUE:return"\u7EBF\u7D22\u8F6C\u79FB";case r.CRM_CUSTOMER:return"\u5BA2\u6237\u8F6C\u79FB";case r.CRM_CONTACT:return"\u8054\u7CFB\u4EBA\u8F6C\u79FB";case r.CRM_BUSINESS:return"\u5546\u673A\u8F6C\u79FB";case r.CRM_CONTRACT:return"\u5408\u540C\u8F6C\u79FB";default:return"\u8F6C\u79FB"}},x=()=>{var t;(t=C.value)==null||t.resetFields(),_.value={}};return Q(async()=>{U.value=await fe()}),(t,u)=>{const j=ae,q=le,p=te,w=re,V=se,v=ue,G=_e,H=oe,h=ne,J=me,K=de;return o(),m(J,{modelValue:e(i),"onUpdate:modelValue":u[5]||(u[5]=a=>E(i)?i.value=a:null),title:e(y),width:"30%"},{footer:l(()=>[s(h,{disabled:e(c),type:"primary",onClick:P},{default:l(()=>[n("\u786E \u5B9A")]),_:1},8,["disabled"]),s(h,{onClick:u[4]||(u[4]=a=>i.value=!1)},{default:l(()=>[n("\u53D6 \u6D88")]),_:1})]),default:l(()=>[X((o(),m(H,{ref_key:"formRef",ref:C,model:e(_),rules:e(g),"label-width":"150px"},{default:l(()=>[s(p,{label:"\u9009\u62E9\u65B0\u8D1F\u8D23\u4EBA",prop:"newOwnerUserId"},{default:l(()=>[s(q,{modelValue:e(_).newOwnerUserId,"onUpdate:modelValue":u[0]||(u[0]=a=>e(_).newOwnerUserId=a)},{default:l(()=>[(o(!0),b(R,null,S(e(U),a=>(o(),m(j,{key:a.id,label:a.nickname,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(p,{label:"\u8001\u8D1F\u8D23\u4EBA"},{default:l(()=>[s(V,{modelValue:e(f),"onUpdate:modelValue":u[1]||(u[1]=a=>E(f)?f.value=a:null),onChange:z},{default:l(()=>[s(w,{label:!1,size:"large"},{default:l(()=>[n("\u79FB\u9664")]),_:1}),s(w,{label:!0,size:"large"},{default:l(()=>[n("\u52A0\u5165\u56E2\u961F")]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(f)?(o(),m(p,{key:0,label:"\u8001\u8D1F\u8D23\u4EBA\u6743\u9650\u7EA7\u522B",prop:"oldOwnerPermissionLevel"},{default:l(()=>[s(V,{modelValue:e(_).oldOwnerPermissionLevel,"onUpdate:modelValue":u[2]||(u[2]=a=>e(_).oldOwnerPermissionLevel=a)},{default:l(()=>[(o(!0),b(R,null,S(e(Z)(e($).CRM_PERMISSION_LEVEL),a=>(o(),b(R,{key:a.value},[a.value!=e(Ve).OWNER?(o(),m(w,{key:0,label:a.value},{default:l(()=>[n(Y(a.label),1)]),_:2},1032,["label"])):T("",!0)],64))),128))]),_:1},8,["modelValue"])]),_:1})):T("",!0),t.bizType===e(r).CRM_CUSTOMER?(o(),m(p,{key:1,label:"\u540C\u65F6\u8F6C\u79FB"},{default:l(()=>[s(G,{modelValue:e(_).toBizTypes,"onUpdate:modelValue":u[3]||(u[3]=a=>e(_).toBizTypes=a)},{default:l(()=>[s(v,{label:e(r).CRM_CONTACT},{default:l(()=>[n("\u8054\u7CFB\u4EBA")]),_:1},8,["label"]),s(v,{label:e(r).CRM_BUSINESS},{default:l(()=>[n("\u5546\u673A")]),_:1},8,["label"]),s(v,{label:e(r).CRM_CONTRACT},{default:l(()=>[n("\u5408\u540C")]),_:1},8,["label"])]),_:1},8,["modelValue"])]),_:1})):T("",!0)]),_:1},8,["model","rules"])),[[K,e(c)]])]),_:1},8,["modelValue","title"])}}})});export{N as _,Se as __tla};
