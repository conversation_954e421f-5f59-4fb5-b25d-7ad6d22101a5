import{d as H,r as f,f as J,u as K,bc as M,C as O,T as Q,o as u,c as S,i as a,w as l,a as t,F as P,k as X,l as b,H as T,j as p,t as m,dV as h,dX as E,G as W,g as Y,J as Z,K as $,L as aa,O as ea,P as ta,v as ra,N as la,Q as ia,R as na,__tla as oa}from"./index-BUSn51wb.js";import{_ as sa,__tla as pa}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as da,__tla as ua}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as ma,__tla as ca}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{b as y,d as v,__tla as _a}from"./formatTime-DWdBpgsM.js";import{e as fa,__tla as wa}from"./index-DrB1WZUR.js";import{A as ga}from"./common-BQQO87UM.js";let D,ba=Promise.all([(()=>{try{return oa}catch{}})(),(()=>{try{return pa}catch{}})(),(()=>{try{return ua}catch{}})(),(()=>{try{return ca}catch{}})(),(()=>{try{return _a}catch{}})(),(()=>{try{return wa}catch{}})()]).then(async()=>{let C;C=Y("div",{class:"pb-5 text-xl"},"\u5F85\u5BA1\u6838\u5408\u540C",-1),D=H({__name:"ContractAuditList",setup(ha){const w=f(!0),k=f(0),N=f([]),n=J({pageNo:1,pageSize:10,sceneType:1,auditStatus:10}),U=f(),c=async()=>{w.value=!0;try{const g=await fa(n);N.value=g.list,k.value=g.total}finally{w.value=!1}},I=()=>{n.pageNo=1,c()},{push:d}=K();return M(async()=>{await c()}),O(()=>{c()}),(g,o)=>{const R=Z,z=$,A=aa,V=ea,x=ma,r=ta,_=ra,B=da,L=la,q=ia,F=sa,j=Q("hasPermi"),G=na;return u(),S(P,null,[a(x,null,{default:l(()=>[C,a(V,{ref_key:"queryFormRef",ref:U,inline:!0,model:t(n),class:"-mb-15px","label-width":"68px"},{default:l(()=>[a(A,{label:"\u5408\u540C\u72B6\u6001",prop:"auditStatus"},{default:l(()=>[a(z,{modelValue:t(n).auditStatus,"onUpdate:modelValue":o[0]||(o[0]=e=>t(n).auditStatus=e),class:"!w-240px",placeholder:"\u72B6\u6001",onChange:I},{default:l(()=>[(u(!0),S(P,null,X(t(ga),(e,s)=>(u(),b(R,{label:e.label,value:e.value,key:s},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),a(x,null,{default:l(()=>[T((u(),b(q,{data:t(N),"show-overflow-tooltip":!0,stripe:!0},{default:l(()=>[a(r,{align:"center",fixed:"left",label:"\u5408\u540C\u7F16\u53F7",prop:"no",width:"180"}),a(r,{align:"center",fixed:"left",label:"\u5408\u540C\u540D\u79F0",prop:"name",width:"160"},{default:l(e=>[a(_,{underline:!1,type:"primary",onClick:s=>{return i=e.row.id,void d({name:"CrmContractDetail",params:{id:i}});var i}},{default:l(()=>[p(m(e.row.name),1)]),_:2},1032,["onClick"])]),_:1}),a(r,{align:"center",label:"\u5BA2\u6237\u540D\u79F0",prop:"customerName",width:"120"},{default:l(e=>[a(_,{underline:!1,type:"primary",onClick:s=>{return i=e.row.customerId,void d({name:"CrmCustomerDetail",params:{id:i}});var i}},{default:l(()=>[p(m(e.row.customerName),1)]),_:2},1032,["onClick"])]),_:1}),a(r,{align:"center",label:"\u5546\u673A\u540D\u79F0",prop:"businessName",width:"130"},{default:l(e=>[a(_,{underline:!1,type:"primary",onClick:s=>{return i=e.row.businessId,void d({name:"CrmBusinessDetail",params:{id:i}});var i}},{default:l(()=>[p(m(e.row.businessName),1)]),_:2},1032,["onClick"])]),_:1}),a(r,{align:"center",label:"\u5408\u540C\u91D1\u989D\uFF08\u5143\uFF09",prop:"totalPrice",width:"140",formatter:t(h)},null,8,["formatter"]),a(r,{align:"center",label:"\u4E0B\u5355\u65F6\u95F4",prop:"orderDate",width:"120",formatter:t(y)},null,8,["formatter"]),a(r,{align:"center",label:"\u5408\u540C\u5F00\u59CB\u65F6\u95F4",prop:"startTime",width:"120",formatter:t(y)},null,8,["formatter"]),a(r,{align:"center",label:"\u5408\u540C\u7ED3\u675F\u65F6\u95F4",prop:"endTime",width:"120",formatter:t(y)},null,8,["formatter"]),a(r,{align:"center",label:"\u5BA2\u6237\u7B7E\u7EA6\u4EBA",prop:"contactName",width:"130"},{default:l(e=>[a(_,{underline:!1,type:"primary",onClick:s=>{return i=e.row.signContactId,void d({name:"CrmContactDetail",params:{id:i}});var i}},{default:l(()=>[p(m(e.row.signContactName),1)]),_:2},1032,["onClick"])]),_:1}),a(r,{align:"center",label:"\u516C\u53F8\u7B7E\u7EA6\u4EBA",prop:"signUserName",width:"130"}),a(r,{align:"center",label:"\u5907\u6CE8",prop:"remark",width:"200"}),a(r,{align:"center",label:"\u5DF2\u56DE\u6B3E\u91D1\u989D\uFF08\u5143\uFF09",prop:"totalReceivablePrice",width:"140",formatter:t(h)},null,8,["formatter"]),a(r,{align:"center",label:"\u672A\u56DE\u6B3E\u91D1\u989D\uFF08\u5143\uFF09",prop:"totalReceivablePrice",width:"140",formatter:t(h)},{default:l(e=>[p(m(t(E)(e.row.totalPrice-e.row.totalReceivablePrice)),1)]),_:1},8,["formatter"]),a(r,{formatter:t(v),align:"center",label:"\u6700\u540E\u8DDF\u8FDB\u65F6\u95F4",prop:"contactLastTime",width:"180px"},null,8,["formatter"]),a(r,{align:"center",label:"\u8D1F\u8D23\u4EBA",prop:"ownerUserName",width:"120"}),a(r,{align:"center",label:"\u6240\u5C5E\u90E8\u95E8",prop:"ownerUserDeptName",width:"100px"}),a(r,{formatter:t(v),align:"center",label:"\u66F4\u65B0\u65F6\u95F4",prop:"updateTime",width:"180px"},null,8,["formatter"]),a(r,{formatter:t(v),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"]),a(r,{align:"center",label:"\u521B\u5EFA\u4EBA",prop:"creatorName",width:"120"}),a(r,{align:"center",fixed:"right",label:"\u5408\u540C\u72B6\u6001",prop:"auditStatus",width:"120"},{default:l(e=>[a(B,{type:t(W).CRM_AUDIT_STATUS,value:e.row.auditStatus},null,8,["type","value"])]),_:1}),a(r,{fixed:"right",label:"\u64CD\u4F5C",width:"90"},{default:l(e=>[T((u(),b(L,{link:"",type:"primary",onClick:s=>{return i=e.row,void d({name:"BpmProcessInstanceDetail",query:{id:i.processInstanceId}});var i}},{default:l(()=>[p(" \u67E5\u770B\u5BA1\u6279 ")]),_:2},1032,["onClick"])),[[j,["crm:contract:update"]]])]),_:1})]),_:1},8,["data"])),[[G,t(w)]]),a(F,{limit:t(n).pageSize,"onUpdate:limit":o[1]||(o[1]=e=>t(n).pageSize=e),page:t(n).pageNo,"onUpdate:page":o[2]||(o[2]=e=>t(n).pageNo=e),total:t(k),onPagination:c},null,8,["limit","page","total"])]),_:1})],64)}}})});export{D as _,ba as __tla};
