import{by as e,__tla as b}from"./index-BUSn51wb.js";let r,t,c,l,s,i,u,m,n,y=Promise.all([(()=>{try{return b}catch{}})()]).then(async()=>{t=async a=>await e.get({url:"/crm/receivable/page",params:a}),u=async a=>await e.get({url:"/crm/receivable/page-by-customer",params:a}),r=async a=>await e.get({url:"/crm/receivable/get?id="+a}),c=async a=>await e.post({url:"/crm/receivable/create",data:a}),n=async a=>await e.put({url:"/crm/receivable/update",data:a}),l=async a=>await e.delete({url:"/crm/receivable/delete?id="+a}),i=async a=>await e.download({url:"/crm/receivable/export-excel",params:a}),m=async a=>await e.put({url:`/crm/receivable/submit?id=${a}`}),s=async()=>await e.get({url:"/crm/receivable/audit-count"})});export{y as __tla,r as a,t as b,c,l as d,s as e,i as f,u as g,m as s,n as u};
