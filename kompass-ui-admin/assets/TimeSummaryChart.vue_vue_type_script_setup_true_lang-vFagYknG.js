import{d as m,p as s,f as u,at as p,o as d,l as h,w as r,i,a as o,__tla as y}from"./index-BUSn51wb.js";import{E as x,__tla as g}from"./el-card-CJbXGyyg.js";import{_ as f,__tla as b}from"./Echart.vue_vue_type_script_setup_true_lang-DnLZLH3w.js";import{C as w,__tla as v}from"./CardTitle-Dm4BG9kg.js";let l,A=Promise.all([(()=>{try{return y}catch{}})(),(()=>{try{return g}catch{}})(),(()=>{try{return b}catch{}})(),(()=>{try{return v}catch{}})()]).then(async()=>{l=m({name:"MemberStatisticsCard",__name:"TimeSummaryChart",props:{title:s.string.def("").isRequired,value:s.object.isRequired},setup(n){const a=n,t=u({dataset:{dimensions:["time","price"],source:[]},grid:{left:20,right:20,bottom:20,top:80,containLabel:!0},legend:{top:50},series:[{name:"\u91D1\u989D",type:"line",smooth:!0,areaStyle:{}}],toolbox:{feature:{dataZoom:{yAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:a.title}}},tooltip:{trigger:"axis",axisPointer:{type:"cross"},padding:[5,10]},xAxis:{type:"category",boundaryGap:!1,axisTick:{show:!1}},yAxis:{axisTick:{show:!1}}});return p(()=>a.value,e=>{e&&t.dataset&&t.dataset.source&&(t.dataset.source=e)}),(e,C)=>{const _=f,c=x;return d(),h(c,{shadow:"never"},{header:r(()=>[i(o(w),{title:a.title},null,8,["title"])]),default:r(()=>[i(_,{height:300,options:o(t)},null,8,["options"])]),_:1})}}})});export{l as _,A as __tla};
