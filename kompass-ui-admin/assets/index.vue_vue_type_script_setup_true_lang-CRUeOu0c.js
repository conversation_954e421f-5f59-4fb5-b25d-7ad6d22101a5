import{d as x,b6 as _,o as g,c as b,g as l,i as r,a as v,t as n,_ as y,__tla as I}from"./index-BUSn51wb.js";import{E as h,__tla as w}from"./el-image-BjHZRFih.js";let c,j=Promise.all([(()=>{try{return I}catch{}})(),(()=>{try{return w}catch{}})()]).then(async()=>{let s,e,a,o;s={class:"flex bg-[var(--el-bg-color-overlay)] p-12px mb-12px rounded-1"},e={class:"bg-black bg-op-40 absolute top-0 left-0 w-full h-full flex items-center justify-center cursor-pointer"},a={class:"ml-8px"},o={class:"mt-8px text-12px text-[var(--el-text-color-secondary)] line-clamp-2"},c=x({name:"Index",__name:"index",props:{songInfo:{type:Object,default:()=>({})}},emits:["play"],setup(t,{emit:i}){const p=i,d=_("currentSong",{});function u(){p("play")}return(k,z)=>{const f=h,m=y;return g(),b("div",s,[l("div",{class:"relative",onClick:u},[r(f,{src:t.songInfo.imageUrl,class:"flex-none w-80px"},null,8,["src"]),l("div",e,[r(m,{icon:v(d).id===t.songInfo.id?"solar:pause-circle-bold":"mdi:arrow-right-drop-circle",size:30},null,8,["icon"])])]),l("div",a,[l("div",null,n(t.songInfo.title),1),l("div",o,n(t.songInfo.desc),1)])])}}})});export{c as _,j as __tla};
