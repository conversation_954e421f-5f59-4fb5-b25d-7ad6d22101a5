import{d as q,I as O,n as Q,r as m,f as Z,C as B,T as E,o as c,c as G,i as e,w as n,a,U as g,j as _,H as y,l as w,F as J,Z as W,L as X,M as $,_ as ee,N as ae,O as le,P as te,Q as re,R as ne,__tla as oe}from"./index-BUSn51wb.js";import{_ as se,__tla as de}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as ie,__tla as ue}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{d as Y,__tla as pe}from"./formatTime-DWdBpgsM.js";import{d as me}from"./download-e0EdwhTv.js";import{_ as ce,A as I,__tla as ge}from"./AgreementSignatureForm.vue_vue_type_script_setup_true_lang-BltK-Rnl.js";import{__tla as _e}from"./index-Cch5e1V0.js";import{__tla as fe}from"./el-card-CJbXGyyg.js";import{__tla as ye}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";let P,we=Promise.all([(()=>{try{return oe}catch{}})(),(()=>{try{return de}catch{}})(),(()=>{try{return ue}catch{}})(),(()=>{try{return pe}catch{}})(),(()=>{try{return ge}catch{}})(),(()=>{try{return _e}catch{}})(),(()=>{try{return fe}catch{}})(),(()=>{try{return ye}catch{}})()]).then(async()=>{P=q({name:"AgreementSignature",__name:"index",setup(be){const h=O(),{t:T}=Q(),x=m(!0),v=m([]),U=m(0),l=Z({pageNo:1,pageSize:10,agreementId:void 0,agreementKey:void 0,agreementVersion:void 0,userId:void 0,signatureUrl:void 0,ipAddress:void 0,userAgent:void 0,signedAt:void 0,signedAt:[],createTime:[]}),k=m(),A=m(!1),f=async()=>{x.value=!0;try{const i=await I.getAgreementSignaturePage(l);v.value=i.list,U.value=i.total}finally{x.value=!1}},d=()=>{l.pageNo=1,f()},M=()=>{k.value.resetFields(),d()},C=m(),K=(i,t)=>{C.value.open(i,t)},N=async()=>{try{await h.exportConfirm(),A.value=!0;const i=await I.exportAgreementSignature(l);me.excel(i,"\u534F\u8BAE\u7B7E\u7F72\u8BB0\u5F55.xls")}catch{}finally{A.value=!1}};return B(()=>{f()}),(i,t)=>{const u=W,s=X,S=$,b=ee,p=ae,R=le,D=ie,o=te,z=re,F=se,V=E("hasPermi"),H=ne;return c(),G(J,null,[e(D,null,{default:n(()=>[e(R,{class:"-mb-15px",model:a(l),ref_key:"queryFormRef",ref:k,inline:!0,"label-width":"68px"},{default:n(()=>[e(s,{label:"\u534F\u8BAEID",prop:"agreementId"},{default:n(()=>[e(u,{modelValue:a(l).agreementId,"onUpdate:modelValue":t[0]||(t[0]=r=>a(l).agreementId=r),clearable:"",onKeyup:g(d,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(s,{label:"\u552F\u4E00\u6807\u8BC6",prop:"agreementKey"},{default:n(()=>[e(u,{modelValue:a(l).agreementKey,"onUpdate:modelValue":t[1]||(t[1]=r=>a(l).agreementKey=r),clearable:"",onKeyup:g(d,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(s,{label:"\u7248\u672C",prop:"agreementVersion"},{default:n(()=>[e(u,{modelValue:a(l).agreementVersion,"onUpdate:modelValue":t[2]||(t[2]=r=>a(l).agreementVersion=r),clearable:"",onKeyup:g(d,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(s,{label:"\u7528\u6237ID",prop:"userId"},{default:n(()=>[e(u,{modelValue:a(l).userId,"onUpdate:modelValue":t[3]||(t[3]=r=>a(l).userId=r),clearable:"",onKeyup:g(d,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(s,{label:"IP\u5730\u5740",prop:"ipAddress"},{default:n(()=>[e(u,{modelValue:a(l).ipAddress,"onUpdate:modelValue":t[4]||(t[4]=r=>a(l).ipAddress=r),clearable:"",onKeyup:g(d,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(s,{label:"\u4EE3\u7406\u4FE1\u606F",prop:"userAgent"},{default:n(()=>[e(u,{modelValue:a(l).userAgent,"onUpdate:modelValue":t[5]||(t[5]=r=>a(l).userAgent=r),clearable:"",onKeyup:g(d,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(s,{label:"\u7B7E\u7F72\u65F6\u95F4",prop:"signedAt"},{default:n(()=>[e(S,{modelValue:a(l).signedAt,"onUpdate:modelValue":t[6]||(t[6]=r=>a(l).signedAt=r),"value-format":"YYYY-MM-DD",type:"date",clearable:"",class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(s,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:n(()=>[e(S,{modelValue:a(l).createTime,"onUpdate:modelValue":t[7]||(t[7]=r=>a(l).createTime=r),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-220px"},null,8,["modelValue","default-time"])]),_:1}),e(s,null,{default:n(()=>[e(p,{onClick:d},{default:n(()=>[e(b,{icon:"ep:search",class:"mr-5px"}),_(" \u641C\u7D22")]),_:1}),e(p,{onClick:M},{default:n(()=>[e(b,{icon:"ep:refresh",class:"mr-5px"}),_(" \u91CD\u7F6E")]),_:1}),y((c(),w(p,{type:"primary",plain:"",onClick:t[8]||(t[8]=r=>K("create"))},{default:n(()=>[e(b,{icon:"ep:plus",class:"mr-5px"}),_(" \u65B0\u589E ")]),_:1})),[[V,["als:agreement-signature:create"]]]),y((c(),w(p,{type:"success",plain:"",onClick:N,loading:a(A)},{default:n(()=>[e(b,{icon:"ep:download",class:"mr-5px"}),_(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[V,["als:agreement-signature:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(D,null,{default:n(()=>[y((c(),w(z,{data:a(v),stripe:!0,"show-overflow-tooltip":!0,border:""},{default:n(()=>[e(o,{label:"\u4E3B\u952EID",align:"center",prop:"agreementSignatureId",width:"80"}),e(o,{label:"\u534F\u8BAEID",align:"center",prop:"agreementId",width:"80"}),e(o,{label:"\u552F\u4E00\u6807\u8BC6",align:"center",prop:"agreementKey",width:"250"}),e(o,{label:"\u7248\u672C",align:"center",prop:"agreementVersion",width:"80"}),e(o,{label:"\u7528\u6237ID",align:"center",prop:"userId",width:"80"}),e(o,{label:"\u7B7E\u540D\u56FE\u7247URL",align:"center",prop:"signatureUrl"}),e(o,{label:"\u7B7E\u7F72IP\u5730\u5740",align:"center",prop:"ipAddress"}),e(o,{label:"\u7528\u6237\u4EE3\u7406\u4FE1\u606F",align:"center",prop:"userAgent"}),e(o,{label:"\u7B7E\u7F72\u65F6\u95F4",align:"center",prop:"signedAt",formatter:a(Y),width:"180px"},null,8,["formatter"]),e(o,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:a(Y),width:"180px"},null,8,["formatter"]),e(o,{label:"\u64CD\u4F5C",align:"center","min-width":"120px",fixed:"right"},{default:n(r=>[y((c(),w(p,{link:"",type:"primary",onClick:L=>K("update",r.row.agreementSignatureId)},{default:n(()=>[_(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[V,["als:agreement-signature:update"]]]),y((c(),w(p,{link:"",type:"danger",onClick:L=>(async j=>{try{await h.delConfirm(),await I.deleteAgreementSignature(j),h.success(T("common.delSuccess")),await f()}catch{}})(r.row.agreementSignatureId)},{default:n(()=>[_(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[V,["als:agreement-signature:delete"]]])]),_:1})]),_:1},8,["data"])),[[H,a(x)]]),e(F,{total:a(U),page:a(l).pageNo,"onUpdate:page":t[9]||(t[9]=r=>a(l).pageNo=r),limit:a(l).pageSize,"onUpdate:limit":t[10]||(t[10]=r=>a(l).pageSize=r),onPagination:f},null,8,["total","page","limit"])]),_:1}),e(ce,{ref_key:"formRef",ref:C,onSuccess:f},null,512)],64)}}})});export{we as __tla,P as default};
