import{by as a,__tla as c}from"./index-BUSn51wb.js";let e,l=Promise.all([(()=>{try{return c}catch{}})()]).then(async()=>{e={getAttachmentPage:async t=>await a.get({url:"/als/attachment/page",params:t}),getAttachment:async t=>await a.get({url:"/als/attachment/get?id="+t}),createAttachment:async t=>await a.post({url:"/als/attachment/create",data:t}),updateAttachment:async t=>await a.put({url:"/als/attachment/update",data:t}),deleteAttachment:async t=>await a.delete({url:"/als/attachment/delete?id="+t}),exportAttachment:async t=>await a.download({url:"/als/attachment/export-excel",params:t}),updateTeacherAttachment:async t=>await a.put({url:"/als/teacher/uploadAttachment",data:t}),queryTeacherAttachment:async t=>await a.get({url:"/als/teacher/queryAttachment?id="+t})}});export{e as A,l as __tla};
