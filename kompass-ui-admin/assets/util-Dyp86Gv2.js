import{_ as <PERSON>,__tla as Qo}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{Q as Ko,__tla as Xo}from"./Qrcode-CP7wmJi0.js";import{aL as et,aO as ot,aP as tt,aQ as rt,aR as at,aS as nt,aT as it,aU as lt,d as H,o as u,l as P,w as c,aV as yo,aW as ct,aX as pt,aY as fo,B as Z,aD as L,aZ as o,a_ as dt,a$ as st,p as O,b as _t,c as E,g as h,b0 as te,av as ut,a as r,t as S,a9 as C,i as a,a0 as bo,b1 as q,_ as re,N as vo,aN as ho,f as To,at as D,y as ae,k as ne,F as j,b2 as wo,b3 as Po,a5 as Co,a6 as Eo,b4 as mt,b5 as gt,r as x,b6 as yt,C as ft,b7 as bt,j as vt,b8 as Oo,b9 as ht,ax as Tt,__tla as wt}from"./index-BUSn51wb.js";import{E as Pt,__tla as Ct}from"./el-text-CIwNlU-U.js";import{_ as Et,__tla as Ot}from"./IFrame.vue_vue_type_script_setup_true_lang-silYuU_p.js";import{E as xt,__tla as Rt}from"./el-card-CJbXGyyg.js";import{V as xo,__tla as Bt}from"./vuedraggable.umd-BTL7hPHv.js";import{E as Lt,a as It,__tla as Vt}from"./el-collapse-item-B_QvnH_b.js";let J,Ro,ie,Bo,Q,K,Lo,St=Promise.all([(()=>{try{return Qo}catch{}})(),(()=>{try{return Xo}catch{}})(),(()=>{try{return wt}catch{}})(),(()=>{try{return Ct}catch{}})(),(()=>{try{return Ot}catch{}})(),(()=>{try{return Rt}catch{}})(),(()=>{try{return Bt}catch{}})(),(()=>{try{return Vt}catch{}})()]).then(async()=>{var le=1/0,Io=17976931348623157e292;function Vo(e){var d=function(n){return n?(n=et(n))===le||n===-le?(n<0?-1:1)*Io:n==n?n:0:n===0?n:0}(e),l=d%1;return d==d?l?d-l:d:0}var So="[object String]";function ko(e){return e==null?[]:function(d,l){return at(l,function(n){return d[n]})}(e,nt(e))}var Do=Math.max;function jo(e,d,l,n){e=it(e)?e:ko(e),l=l&&!n?Vo(l):0;var f=e.length;return l<0&&(l=Do(f+l,0)),function(m){return typeof m=="string"||!ot(m)&&tt(m)&&rt(m)==So}(e)?l<=f&&e.indexOf(d,l)>-1:!!f&&lt(e,d,l)>-1}let ce,pe,de,se,_e,ue,me,ge,ye,fe,be,ve,he,Te,M,we,Pe,A,Ce,Ee,Oe,xe,Re,Be,Le,Ie,U,Ve,Se,ke,De,je,Me,Ae,Ue,X,z,N,ee;ce=Z(H({name:"VerticalButtonGroup",__name:"index",setup:e=>(d,l)=>{const n=fo;return u(),P(n,ct(pt(d.$attrs)),{default:c(()=>[yo(d.$slots,"default",{},void 0,!0)]),_:3},16)}}),[["__scopeId","data-v-baa42acc"]]),pe=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"Carousel",name:"\u8F6E\u64AD\u56FE",icon:"system-uicons:carousel",property:{type:"default",indicator:"dot",autoplay:!1,interval:3,items:[{type:"img",imgUrl:"https://static.iocoder.cn/mall/banner-01.jpg",videoUrl:""},{type:"img",imgUrl:"https://static.iocoder.cn/mall/banner-02.jpg",videoUrl:""}],style:{bgType:"color",bgColor:"#fff",marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),de=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"CouponCard",name:"\u4F18\u60E0\u5238",icon:"ep:ticket",property:{columns:1,bgImg:"",textColor:"#E9B461",button:{color:"#434343",bgColor:""},space:0,couponIds:[],style:{bgType:"color",bgColor:"",marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),se=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"Divider",name:"\u5206\u5272\u7EBF",icon:"tdesign:component-divider-vertical",property:{height:30,lineWidth:1,paddingType:"none",lineColor:"#dcdfe6",borderType:"solid"}}},Symbol.toStringTag,{value:"Module"})),_e=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"FloatingActionButton",name:"\u60AC\u6D6E\u6309\u94AE",icon:"tabler:float-right",position:"fixed",property:{direction:"vertical",showText:!0,list:[{textColor:"#fff"}]}}},Symbol.toStringTag,{value:"Module"})),ue=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"HotZone",name:"\u70ED\u533A",icon:"tabler:hand-click",property:{imgUrl:"",list:[],style:{bgType:"color",bgColor:"#fff",marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),me=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"ImageBar",name:"\u56FE\u7247\u5C55\u793A",icon:"ep:picture",property:{imgUrl:"",url:"",style:{bgType:"color",bgColor:"#fff",marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),ge=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"MagicCube",name:"\u5E7F\u544A\u9B54\u65B9",icon:"bi:columns",property:{borderRadiusTop:0,borderRadiusBottom:0,space:0,list:[],style:{bgType:"color",bgColor:"#fff",marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),J={title:"\u6807\u9898",titleColor:"#333",subtitle:"\u526F\u6807\u9898",subtitleColor:"#bbb",badge:{show:!1,textColor:"#fff",bgColor:"#FF6000"}},ye={id:"MenuGrid",name:"\u5BAB\u683C\u5BFC\u822A",icon:"bi:grid-3x3-gap",property:{column:3,list:[L(J)],style:{bgType:"color",bgColor:"#fff",marginBottom:8,marginLeft:8,marginRight:8,padding:8,paddingTop:8,paddingRight:8,paddingBottom:8,paddingLeft:8,borderRadius:8,borderTopLeftRadius:8,borderTopRightRadius:8,borderBottomRightRadius:8,borderBottomLeftRadius:8}}},fe=Object.freeze(Object.defineProperty({__proto__:null,EMPTY_MENU_GRID_ITEM_PROPERTY:J,component:ye},Symbol.toStringTag,{value:"Module"})),Q={title:"\u6807\u9898",titleColor:"#333",subtitle:"\u526F\u6807\u9898",subtitleColor:"#bbb"},be={id:"MenuList",name:"\u5217\u8868\u5BFC\u822A",icon:"fa-solid:list",property:{list:[L(Q)],style:{bgType:"color",bgColor:"#fff",marginBottom:8}}},ve=Object.freeze(Object.defineProperty({__proto__:null,EMPTY_MENU_LIST_ITEM_PROPERTY:Q,component:be},Symbol.toStringTag,{value:"Module"})),K={title:"\u6807\u9898",titleColor:"#333",badge:{show:!1,textColor:"#fff",bgColor:"#FF6000"}},he={id:"MenuSwiper",name:"\u83DC\u5355\u5BFC\u822A",icon:"bi:grid-3x2-gap",property:{layout:"iconText",row:1,column:3,list:[L(K)],style:{bgType:"color",bgColor:"#fff",marginBottom:8}}},Te=Object.freeze(Object.defineProperty({__proto__:null,EMPTY_MENU_SWIPER_ITEM_PROPERTY:K,component:he},Symbol.toStringTag,{value:"Module"})),M={id:"NavigationBar",name:"\u9876\u90E8\u5BFC\u822A\u680F",icon:"tabler:layout-navbar",property:{bgType:"color",bgColor:"#fff",bgImg:"",styleType:"normal",alwaysShow:!0,mpCells:[{type:"text",textColor:"#111111"}],otherCells:[{type:"text",textColor:"#111111"}],_local:{previewMp:!0,previewOther:!1}}},we=Object.freeze(Object.defineProperty({__proto__:null,component:M},Symbol.toStringTag,{value:"Module"})),Pe=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"NoticeBar",name:"\u516C\u544A\u680F",icon:"ep:bell",property:{iconUrl:"http://mall.yudao.iocoder.cn/static/images/xinjian.png",contents:[{text:"",url:""}],backgroundColor:"#fff",textColor:"#333",style:{bgType:"color",bgColor:"#fff",marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),A={id:"PageConfig",name:"\u9875\u9762\u8BBE\u7F6E",icon:"ep:document",property:{description:"",backgroundColor:"#f5f5f5",backgroundImage:""}},Ce=Object.freeze(Object.defineProperty({__proto__:null,component:A},Symbol.toStringTag,{value:"Module"})),Ee=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"Popover",name:"\u5F39\u7A97\u5E7F\u544A",icon:"carbon:popup",position:"fixed",property:{list:[{showType:"once"}]}}},Symbol.toStringTag,{value:"Module"})),Oe=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"ProductCard",name:"\u5546\u54C1\u5361\u7247",icon:"fluent:text-column-two-left-24-filled",property:{layoutType:"oneColBigImg",fields:{name:{show:!0,color:"#000"},introduction:{show:!0,color:"#999"},price:{show:!0,color:"#ff3000"},marketPrice:{show:!0,color:"#c4c4c4"},salesCount:{show:!0,color:"#c4c4c4"},stock:{show:!1,color:"#c4c4c4"}},badge:{show:!1,imgUrl:""},btnBuy:{type:"text",text:"\u7ACB\u5373\u8D2D\u4E70",bgBeginColor:"#FF6000",bgEndColor:"#FE832A",imgUrl:""},borderRadiusTop:8,borderRadiusBottom:8,space:8,spuIds:[],style:{bgType:"color",bgColor:"",marginLeft:8,marginRight:8,marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),xe=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"ProductList",name:"\u5546\u54C1\u680F",icon:"fluent:text-column-two-24-filled",property:{layoutType:"twoCol",fields:{name:{show:!0,color:"#000"},price:{show:!0,color:"#ff3000"}},badge:{show:!1,imgUrl:""},borderRadiusTop:8,borderRadiusBottom:8,space:8,spuIds:[],style:{bgType:"color",bgColor:"",marginLeft:8,marginRight:8,marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),Re=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"PromotionArticle",name:"\u8425\u9500\u6587\u7AE0",icon:"ph:article-medium",property:{style:{bgType:"color",bgColor:"",marginLeft:8,marginRight:8,marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),Be=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"PromotionCombination",name:"\u62FC\u56E2",icon:"mdi:account-group",property:{layoutType:"oneCol",fields:{name:{show:!0,color:"#000"},price:{show:!0,color:"#ff3000"}},badge:{show:!1,imgUrl:""},borderRadiusTop:8,borderRadiusBottom:8,space:8,style:{bgType:"color",bgColor:"",marginLeft:8,marginRight:8,marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),Le=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"PromotionSeckill",name:"\u79D2\u6740",icon:"mdi:calendar-time",property:{activityId:void 0,layoutType:"oneCol",fields:{name:{show:!0,color:"#000"},price:{show:!0,color:"#ff3000"}},badge:{show:!1,imgUrl:""},borderRadiusTop:8,borderRadiusBottom:8,space:8,style:{bgType:"color",bgColor:"",marginLeft:8,marginRight:8,marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),Ie=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"SearchBar",name:"\u641C\u7D22\u6846",icon:"ep:search",property:{height:28,showScan:!1,borderRadius:0,placeholder:"\u641C\u7D22\u5546\u54C1",placeholderPosition:"left",backgroundColor:"rgb(238, 238, 238)",textColor:"rgb(150, 151, 153)",hotKeywords:[],style:{bgType:"color",bgColor:"#fff",marginBottom:8,paddingTop:8,paddingRight:8,paddingBottom:8,paddingLeft:8}}}},Symbol.toStringTag,{value:"Module"})),U={id:"TabBar",name:"\u5E95\u90E8\u5BFC\u822A",icon:"fluent:table-bottom-row-16-filled",property:{theme:"red",style:{bgType:"color",bgColor:"#fff",color:"#282828",activeColor:"#fc4141"},items:[{text:"\u9996\u9875",url:"/pages/index/index",iconUrl:"http://mall.yudao.iocoder.cn/static/images/1-001.png",activeIconUrl:"http://mall.yudao.iocoder.cn/static/images/1-002.png"},{text:"\u5206\u7C7B",url:"/pages/index/category?id=3",iconUrl:"http://mall.yudao.iocoder.cn/static/images/2-001.png",activeIconUrl:"http://mall.yudao.iocoder.cn/static/images/2-002.png"},{text:"\u8D2D\u7269\u8F66",url:"/pages/index/cart",iconUrl:"http://mall.yudao.iocoder.cn/static/images/3-001.png",activeIconUrl:"http://mall.yudao.iocoder.cn/static/images/3-002.png"},{text:"\u6211\u7684",url:"/pages/index/user",iconUrl:"http://mall.yudao.iocoder.cn/static/images/4-001.png",activeIconUrl:"http://mall.yudao.iocoder.cn/static/images/4-002.png"}]}},ie=[{id:"red",name:"\u4E2D\u56FD\u7EA2",icon:"icon-park-twotone:theme",color:"#d10019"},{id:"orange",name:"\u6854\u6A59",icon:"icon-park-twotone:theme",color:"#f37b1d"},{id:"gold",name:"\u660E\u9EC4",icon:"icon-park-twotone:theme",color:"#fbbd08"},{id:"green",name:"\u6A44\u6984\u7EFF",icon:"icon-park-twotone:theme",color:"#8dc63f"},{id:"cyan",name:"\u5929\u9752",icon:"icon-park-twotone:theme",color:"#1cbbb4"},{id:"blue",name:"\u6D77\u84DD",icon:"icon-park-twotone:theme",color:"#0081ff"},{id:"purple",name:"\u59F9\u7D2B",icon:"icon-park-twotone:theme",color:"#6739b6"},{id:"brightRed",name:"\u5AE3\u7EA2",icon:"icon-park-twotone:theme",color:"#e54d42"},{id:"forestGreen",name:"\u68EE\u7EFF",icon:"icon-park-twotone:theme",color:"#39b54a"},{id:"mauve",name:"\u6728\u69FF",icon:"icon-park-twotone:theme",color:"#9c26b0"},{id:"pink",name:"\u6843\u7C89",icon:"icon-park-twotone:theme",color:"#e03997"},{id:"brown",name:"\u68D5\u8910",icon:"icon-park-twotone:theme",color:"#a5673f"},{id:"grey",name:"\u7384\u7070",icon:"icon-park-twotone:theme",color:"#8799a3"},{id:"gray",name:"\u8349\u7070",icon:"icon-park-twotone:theme",color:"#aaaaaa"},{id:"black",name:"\u58A8\u9ED1",icon:"icon-park-twotone:theme",color:"#333333"}],Ve=Object.freeze(Object.defineProperty({__proto__:null,THEME_LIST:ie,component:U},Symbol.toStringTag,{value:"Module"})),Se=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"TitleBar",name:"\u6807\u9898\u680F",icon:"material-symbols:line-start",property:{title:"\u4E3B\u6807\u9898",description:"\u526F\u6807\u9898",titleSize:16,descriptionSize:12,titleWeight:400,textAlign:"left",descriptionWeight:200,titleColor:"rgba(50, 50, 51, 10)",descriptionColor:"rgba(150, 151, 153, 10)",more:{show:!1,type:"icon",text:"\u67E5\u770B\u66F4\u591A",url:""},style:{bgType:"color",bgColor:"#fff"}}}},Symbol.toStringTag,{value:"Module"})),ke=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"UserCard",name:"\u7528\u6237\u5361\u7247",icon:"mdi:user-card-details",property:{style:{bgType:"color",bgColor:"",marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),De=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"UserCoupon",name:"\u7528\u6237\u5361\u5238",icon:"ep:ticket",property:{style:{bgType:"color",bgColor:"",marginLeft:8,marginRight:8,marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),je=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"UserOrder",name:"\u7528\u6237\u8BA2\u5355",icon:"ep:list",property:{style:{bgType:"color",bgColor:"",marginLeft:8,marginRight:8,marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),Me=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"UserWallet",name:"\u7528\u6237\u8D44\u4EA7",icon:"ep:wallet-filled",property:{style:{bgType:"color",bgColor:"",marginLeft:8,marginRight:8,marginBottom:8}}}},Symbol.toStringTag,{value:"Module"})),Ae=Object.freeze(Object.defineProperty({__proto__:null,component:{id:"VideoPlayer",name:"\u89C6\u9891\u64AD\u653E",icon:"ep:video-play",property:{videoUrl:"",posterUrl:"",autoplay:!1,style:{bgType:"color",bgColor:"#fff",marginBottom:8,height:300}}}},Symbol.toStringTag,{value:"Module"})),Ue=Object.assign({"./Carousel/index.vue":()=>o(()=>import("./index-BQbh8aGM.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([0,1,2,3,4,5,6])),"./Carousel/property.vue":()=>o(()=>import("./property-CDJreho4.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([7,8,1,2,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33])),"./CouponCard/index.vue":()=>o(()=>import("./index-B2S_S64c.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([34,1,2,35,36,37])),"./CouponCard/property.vue":()=>o(()=>import("./property-UgGblY7I.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([38,8,1,2,9,10,11,12,13,14,16,17,36,39,21,22,40,41,42,43,44,37,35,28,29,30,18,31,32,33])),"./Divider/index.vue":()=>o(()=>import("./index-B-Gw-SrB.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([45,1,2])),"./Divider/property.vue":()=>o(()=>import("./property-DENcmrId.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([46,1,2,11,12,13,21,22,28,29,16,17,30,9,10,18,31,32,33])),"./FloatingActionButton/index.vue":()=>o(()=>import("./index-Yg5UY8tm.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([47,1,2,5,6,48])),"./FloatingActionButton/property.vue":()=>o(()=>import("./property-u3TihBQb.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([49,1,2,15,16,17,18,19,20,21,22,23,24,25,26,27,50,12,51,9,10,28,29,30,31,32,33])),"./HotZone/index.vue":()=>o(()=>import("./index-Dxr8EKgr.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([52,1,2,5,6,53])),"./HotZone/property.vue":()=>o(()=>import("./property-CUYCbI1z.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([54,8,1,2,9,10,11,12,13,14,16,17,20,21,22,23,24,25,26,27,5,6,28,29,30,18,31,32,55,33])),"./ImageBar/index.vue":()=>o(()=>import("./index-Dv8SJ1Yg.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([56,1,2,5,6,57])),"./ImageBar/property.vue":()=>o(()=>import("./property-DEr_YUdf.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([58,8,1,2,9,10,11,12,13,14,19,20,21,22,23,24,25,26,27,28,29,16,17,30,18,31,32,33])),"./MagicCube/index.vue":()=>o(()=>import("./index-C7fGw-tK.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([59,1,2,5,6])),"./MagicCube/property.vue":()=>o(()=>import("./property-CqEbhc06.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([60,8,1,2,9,10,11,12,13,14,19,20,21,22,23,24,25,26,27,61,62,16,17,28,29,30,18,31,32,33])),"./MenuGrid/index.vue":()=>o(()=>import("./index-Dbcre65D.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([63,1,2,5,6])),"./MenuGrid/property.vue":()=>o(()=>import("./property-Ccu6DjTl.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([64,8,1,2,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,50,51,28,29,30,31,32,33])),"./MenuList/index.vue":()=>o(()=>import("./index-BM9Xs5x7.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([65,1,2,5,6,66])),"./MenuList/property.vue":()=>o(()=>import("./property-FtvQikZW.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([67,8,1,2,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,50,51,28,29,30,31,32,33])),"./MenuSwiper/index.vue":()=>o(()=>import("./index-ClivXIpg.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([68,1,2,3,4,5,6,69])),"./MenuSwiper/property.vue":()=>o(()=>import("./property-DVoyZmmJ.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([70,8,1,2,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,50,51,28,29,30,31,32,33])),"./NavigationBar/index.vue":()=>o(()=>import("./index-Beqxy2MP.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([71,72,73,1,2,74,75])),"./NavigationBar/property.vue":()=>o(()=>import("./property-DwDePwqM.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([76,1,2,9,10,11,12,13,19,20,21,22,23,24,25,26,27,61,62,72,28,29,16,17,30,18,31,32,33])),"./NoticeBar/index.vue":()=>o(()=>import("./index-nyOxZ8G8.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([77,1,2,3,4,5,6])),"./NoticeBar/property.vue":()=>o(()=>import("./property-1MAs_0fa.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([78,8,1,2,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33])),"./PageConfig/property.vue":()=>o(()=>import("./property-CzAcVZSE.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([79,1,2,11,12,13,21,22,28,29,16,17,30,9,10,18,31,32,33])),"./Popover/index.vue":()=>o(()=>import("./index-zEy89Pr7.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([80,1,2,5,6])),"./Popover/property.vue":()=>o(()=>import("./property-C886Go_J.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([81,1,2,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,9,10,31,32,33])),"./ProductCard/index.vue":()=>o(()=>import("./index-CNEZDy34.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([82,1,2,5,6,83])),"./ProductCard/property.vue":()=>o(()=>import("./property-CpmhJymM.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([84,8,1,2,9,10,11,12,13,14,85,5,6,83,86,21,22,43,40,41,24,25,26,27,87,28,29,16,17,30,18,31,32,33])),"./ProductList/index.vue":()=>o(()=>import("./index-D8p3z9Jo.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([88,1,2,5,6,83])),"./ProductList/property.vue":()=>o(()=>import("./property-C7v20an-.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([89,8,1,2,9,10,11,12,13,14,85,5,6,83,86,21,22,43,40,41,24,25,26,27,87,28,29,16,17,30,18,31,32,33])),"./PromotionArticle/index.vue":()=>o(()=>import("./index-BOcrzwJK.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([90,91,1,2])),"./PromotionArticle/property.vue":()=>o(()=>import("./property-bB7rGDHP.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([92,8,1,2,9,10,11,12,13,14,91,21,22,28,29,16,17,30,18,31,32,33])),"./PromotionCombination/index.vue":()=>o(()=>import("./index-CBEnFVB9.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([93,1,2,5,6,83,94])),"./PromotionCombination/property.vue":()=>o(()=>import("./property-D_jLmhoG.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([95,8,1,2,9,10,11,12,13,14,94,36,21,22,28,29,16,17,30,18,31,32,33])),"./PromotionSeckill/index.vue":()=>o(()=>import("./index-DkRs_YDe.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([96,1,2,5,6,83,97])),"./PromotionSeckill/property.vue":()=>o(()=>import("./property-q8n5-AXV.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([98,8,1,2,9,10,11,12,13,14,97,36,21,22,28,29,16,17,30,18,31,32,33])),"./SearchBar/index.vue":()=>o(()=>import("./index-GnBGT4K0.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([73,1,2,74])),"./SearchBar/property.vue":()=>o(()=>import("./property-CPjbFIVO.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([99,8,1,2,9,10,11,12,13,14,15,16,17,18,21,22,28,29,30,31,32,33])),"./TabBar/index.vue":()=>o(()=>import("./index-BColfglt.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([100,1,2,5,6,101])),"./TabBar/property.vue":()=>o(()=>import("./property-0yJjbMyn.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([102,1,2,15,16,17,18,19,20,21,22,23,24,25,26,27,11,12,13,28,29,30,9,10,31,32,33])),"./TitleBar/index.vue":()=>o(()=>import("./index-D6DV0S6c.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([103,1,2,5,6,104])),"./TitleBar/property.vue":()=>o(()=>import("./property-Bhtjt9rb.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([105,8,1,2,9,10,11,12,13,14,19,20,21,22,23,24,25,26,27,50,51,28,29,16,17,30,18,31,32,33])),"./UserCard/index.vue":()=>o(()=>import("./index-DbmjMkFh.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([106,1,2,107,108])),"./UserCard/property.vue":()=>o(()=>import("./property-D8Bn-W2R.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([109,8,1,2,9,10,11,12,13,14,21,22,28,29,16,17,30,18,31,32,33])),"./UserCoupon/index.vue":()=>o(()=>import("./index-Bg8M_PCK.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([110,1,2,5,6])),"./UserCoupon/property.vue":()=>o(()=>import("./property-kysxREsK.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([111,8,1,2,9,10,11,12,13,14,21,22,28,29,16,17,30,18,31,32,33])),"./UserOrder/index.vue":()=>o(()=>import("./index-BbVk65X6.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([112,1,2,5,6])),"./UserOrder/property.vue":()=>o(()=>import("./property-CpjoiPuT.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([113,8,1,2,9,10,11,12,13,14,21,22,28,29,16,17,30,18,31,32,33])),"./UserWallet/index.vue":()=>o(()=>import("./index-g_8ndVA9.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([114,1,2,5,6])),"./UserWallet/property.vue":()=>o(()=>import("./property-BrFOZStX.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([115,8,1,2,9,10,11,12,13,14,21,22,28,29,16,17,30,18,31,32,33])),"./VideoPlayer/index.vue":()=>o(()=>import("./index-CSXO4Wwi.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([116,1,2,5,6,117])),"./VideoPlayer/property.vue":()=>o(()=>import("./property-uWdZrYfk.js").then(async e=>(await e.__tla,e)),__vite__mapDeps([118,8,1,2,9,10,11,12,13,14,21,22,28,29,16,17,30,18,31,32,33]))}),X=Object.assign({"./Carousel/config.ts":pe,"./CouponCard/config.ts":de,"./Divider/config.ts":se,"./FloatingActionButton/config.ts":_e,"./HotZone/config.ts":ue,"./ImageBar/config.ts":me,"./MagicCube/config.ts":ge,"./MenuGrid/config.ts":fe,"./MenuList/config.ts":ve,"./MenuSwiper/config.ts":Te,"./NavigationBar/config.ts":we,"./NoticeBar/config.ts":Pe,"./PageConfig/config.ts":Ce,"./Popover/config.ts":Ee,"./ProductCard/config.ts":Oe,"./ProductList/config.ts":xe,"./PromotionArticle/config.ts":Re,"./PromotionCombination/config.ts":Be,"./PromotionSeckill/config.ts":Le,"./SearchBar/config.ts":Ie,"./TabBar/config.ts":Ve,"./TitleBar/config.ts":Se,"./UserCard/config.ts":ke,"./UserCoupon/config.ts":De,"./UserOrder/config.ts":je,"./UserWallet/config.ts":Me,"./VideoPlayer/config.ts":Ae}),z={},N={},ee=(e,d,l)=>{const n=d.replace("config.ts",`${l}.vue`),f=Ue[n];f&&(z[e]=dt(f))},Object.keys(X).forEach(e=>{const d=X[e].component,l=d==null?void 0:d.id;l&&(N[l]=d,ee(l,e,"index"),ee(`${l}Property`,e,"property"))});let ze,Ne,$e,Fe,We,Ge,Ye,He,Ze,qe,Je,Qe,Ke,Xe,eo,oo,to,ro,ao;ze={class:"component-wrap"},Ne={key:0,class:"component-name"},$e={key:1,class:"component-toolbar"},Fe=Z(H({components:{...z},name:"ComponentContainer",__name:"ComponentContainer",props:{component:st().isRequired,active:O.bool.def(!1),canMoveUp:O.bool.def(!1),canMoveDown:O.bool.def(!1),showToolbar:O.bool.def(!0)},emits:["move","copy","delete"],setup(e,{emit:d}){const l=e,n=_t(()=>{let t=l.component.property.style;return t?{marginTop:`${t.marginTop||0}px`,marginBottom:`${t.marginBottom||0}px`,marginLeft:`${t.marginLeft||0}px`,marginRight:`${t.marginRight||0}px`,paddingTop:`${t.paddingTop||0}px`,paddingRight:`${t.paddingRight||0}px`,paddingBottom:`${t.paddingBottom||0}px`,paddingLeft:`${t.paddingLeft||0}px`,borderTopLeftRadius:`${t.borderTopLeftRadius||0}px`,borderTopRightRadius:`${t.borderTopRightRadius||0}px`,borderBottomRightRadius:`${t.borderBottomRightRadius||0}px`,borderBottomLeftRadius:`${t.borderBottomLeftRadius||0}px`,overflow:"hidden",background:t.bgType==="color"?t.bgColor:`url(${t.bgImg})`}:{}}),f=d,m=t=>{f("move",t)};return(t,b)=>{const s=re,y=vo,R=ho,k=ce;return u(),E("div",{class:bo(["component",{active:e.active}])},[h("div",{style:ut({...r(n)})},[(u(),P(te(e.component.id),{property:e.component.property},null,8,["property"]))],4),h("div",ze,[e.component.name?(u(),E("div",Ne,S(e.component.name),1)):C("",!0),e.showToolbar&&e.component.name&&e.active?(u(),E("div",$e,[a(k,{type:"primary"},{default:c(()=>[a(R,{content:"\u4E0A\u79FB",placement:"right"},{default:c(()=>[a(y,{disabled:!e.canMoveUp,onClick:b[0]||(b[0]=q(g=>m(-1),["stop"]))},{default:c(()=>[a(s,{icon:"ep:arrow-up"})]),_:1},8,["disabled"])]),_:1}),a(R,{content:"\u4E0B\u79FB",placement:"right"},{default:c(()=>[a(y,{disabled:!e.canMoveDown,onClick:b[1]||(b[1]=q(g=>m(1),["stop"]))},{default:c(()=>[a(s,{icon:"ep:arrow-down"})]),_:1},8,["disabled"])]),_:1}),a(R,{content:"\u590D\u5236",placement:"right"},{default:c(()=>[a(y,{onClick:b[2]||(b[2]=q(g=>{f("copy")},["stop"]))},{default:c(()=>[a(s,{icon:"ep:copy-document"})]),_:1})]),_:1}),a(R,{content:"\u5220\u9664",placement:"right"},{default:c(()=>[a(y,{onClick:b[3]||(b[3]=q(g=>{f("delete")},["stop"]))},{default:c(()=>[a(s,{icon:"ep:delete"})]),_:1})]),_:1})]),_:1})])):C("",!0)])],2)}}}),[["__scopeId","data-v-85d51c1a"]]),We=(e=>(Co("data-v-011af852"),e=e(),Eo(),e))(()=>h("div",{class:"drag-placement"},"\u7EC4\u4EF6\u653E\u7F6E\u533A\u57DF",-1)),Ge={class:"component"},Ye={class:"mt-4px text-12px"},He=H({name:"ComponentLibrary",__name:"ComponentLibrary",props:{list:{}},setup(e){const d=e,l=To([]),n=To([]);D(()=>d.list,()=>{n.length=0,l.length=0,d.list.forEach(m=>{m.extended&&n.push(m.name);const t=m.components.map(b=>N[b]).filter(b=>b);t.length>0&&l.push({name:m.name,components:t})})},{immediate:!0});const f=m=>{const t=L(m);return t.uid=new Date().getTime(),t};return(m,t)=>{const b=re,s=Lt,y=It,R=wo,k=Po;return u(),P(k,{class:"editor-left",width:"261px"},{default:c(()=>[a(R,null,{default:c(()=>[a(y,{modelValue:r(n),"onUpdate:modelValue":t[0]||(t[0]=g=>ae(n)?n.value=g:null)},{default:c(()=>[(u(!0),E(j,null,ne(r(l),g=>(u(),P(s,{key:g.name,name:g.name,title:g.name},{default:c(()=>[a(r(xo),{class:"component-container","ghost-class":"draggable-ghost","item-key":"index",list:g.components,sort:!1,group:{name:"component",pull:"clone",put:!1},clone:f,animation:200,"force-fallback":!0},{item:c(({element:$})=>[h("div",null,[We,h("div",Ge,[a(b,{icon:$.icon,size:32},null,8,["icon"]),h("span",Ye,S($.name),1)])])]),_:2},1032,["list"])]),_:2},1032,["name","title"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})}}}),Ze=Z(He,[["__scopeId","data-v-011af852"]]),qe={class:"header-center flex flex-1 items-center justify-center"},Je={class:"editor-design-top"},Qe=(e=>(Co("data-v-30f2b56f"),e=e(),Eo(),e))(()=>h("img",{src:"/assets/statusBar-BIIj2dTI.png",alt:"",class:"status-bar"},null,-1)),Ke=["onClick"],Xe={key:0,class:bo(["editor-design-bottom","component","cursor-pointer!"])},eo={class:"fixed-component-action-group"},oo={class:"flex items-center gap-8px"},to={class:"flex justify-around"},ro={class:"flex flex-col"},ao=H({components:{...z},name:"DiyPageDetail",__name:"index",props:{modelValue:mt([String,Object]).isRequired,title:O.string.def(""),libs:gt(),showNavigationBar:O.bool.def(!0),showTabBar:O.bool.def(!1),showPageConfig:O.bool.def(!0),previewUrl:O.string.def("")},emits:["reset","preview","save","update:modelValue"],setup(e,{emit:d}){const l=x(),n=x(L(A)),f=x(L(M)),m=x(L(U)),t=x(),b=x(-1),s=x([]),y=e;D(()=>y.modelValue,()=>{const i=Oo(y.modelValue)?JSON.parse(y.modelValue):y.modelValue;n.value.property=(i==null?void 0:i.page)||A.property,f.value.property=(i==null?void 0:i.navigationBar)||M.property,m.value.property=(i==null?void 0:i.tabBar)||U.property,s.value=((i==null?void 0:i.components)||[]).map(p=>({...N[p.id],property:p.property}))},{immediate:!0});const R=()=>{const i={page:n.value.property,navigationBar:f.value.property,tabBar:m.value.property,components:s.value.map(T=>({id:T.id,property:T.property}))};y.showTabBar||delete i.tabBar;const p=Oo(y.modelValue)?JSON.stringify(i):i;F("update:modelValue",p),F("save",i)},k=i=>{var p;y.showPageConfig&&jo((p=i==null?void 0:i.target)==null?void 0:p.classList,"page-prop-area")&&g(r(n))},g=(i,p=-1)=>{t.value=i,b.value=p},$=()=>{g(r(f))},Mo=()=>{g(r(m))},Ao=i=>{if(i.added){const{element:p,newIndex:T}=i.added;g(p,T)}else if(i.moved){const{newIndex:p}=i.moved;b.value=p}},Uo=(i,p)=>{const T=i+p;T<0||T>=s.value.length||((V,I)=>{[s.value[V],s.value[I]]=[s.value[I],s.value[V]],b.value=I})(i,T)},no=i=>{if(s.value.splice(i,1),i<s.value.length){let p=i;g(s.value[p],p)}else if(s.value.length>0){let p=i-1;g(s.value[p],p)}else g(r(n))},F=d,io=yt("reload"),zo=()=>{io&&io(),F("reset")},W=x(!1),No=()=>{W.value=!0,F("preview")},lo=()=>{y.showPageConfig?t.value=r(n):y.showNavigationBar?t.value=r(f):y.showTabBar&&(t.value=r(m))};return D(()=>[y.showPageConfig,y.showNavigationBar,y.showTabBar],()=>lo()),ft(()=>lo()),(i,p)=>{const T=re,V=vo,I=ho,$o=fo,Fo=ht,oe=Fe,co=wo,po=Tt,Wo=xt,Go=Po,so=bt,Yo=Et,Ho=Pt,Zo=Ko,qo=Jo;return u(),E(j,null,[a(so,{class:"editor"},{default:c(()=>[a(Fo,{class:"editor-header"},{default:c(()=>[yo(i.$slots,"toolBarLeft",{},void 0,!0),h("div",qe,[h("span",null,S(e.title),1)]),a($o,{class:"header-right"},{default:c(()=>[a(I,{content:"\u91CD\u7F6E"},{default:c(()=>[a(V,{onClick:zo},{default:c(()=>[a(T,{icon:"system-uicons:reset-alt",size:24})]),_:1})]),_:1}),e.previewUrl?(u(),P(I,{key:0,content:"\u9884\u89C8"},{default:c(()=>[a(V,{onClick:No},{default:c(()=>[a(T,{icon:"ep:view",size:24})]),_:1})]),_:1})):C("",!0),a(I,{content:"\u4FDD\u5B58"},{default:c(()=>[a(V,{onClick:R},{default:c(()=>[a(T,{icon:"ep:check",size:24})]),_:1})]),_:1})]),_:1})]),_:3}),a(so,{class:"editor-container"},{default:c(()=>{var G,_o,uo,mo,go;return[e.libs&&e.libs.length>0?(u(),P(Ze,{key:0,ref_key:"componentLibrary",ref:l,list:e.libs},null,8,["list"])):C("",!0),h("div",{class:"editor-center page-prop-area",onClick:k},[h("div",Je,[Qe,e.showNavigationBar?(u(),P(oe,{key:0,component:r(f),"show-toolbar":!1,active:((G=r(t))==null?void 0:G.id)===r(f).id,onClick:$,class:"cursor-pointer!"},null,8,["component","active"])):C("",!0)]),(u(!0),E(j,null,ne(r(s),(_,v)=>{var w;return u(),E("div",{key:v,onClick:B=>g(_,v)},[_.position==="fixed"&&((w=r(t))==null?void 0:w.uid)===_.uid?(u(),P(te(_.id),{key:0,property:_.property},null,8,["property"])):C("",!0)],8,Ke)}),128)),a(co,{height:"100%","wrap-class":"editor-design-center page-prop-area","view-class":"phone-container","view-style":{backgroundColor:r(n).property.backgroundColor,backgroundImage:`url(${r(n).property.backgroundImage})`}},{default:c(()=>[a(r(xo),{class:"page-prop-area drag-area",modelValue:r(s),"onUpdate:modelValue":p[0]||(p[0]=_=>ae(s)?s.value=_:null),"item-key":"index",animation:200,filter:".component-toolbar","ghost-class":"draggable-ghost","force-fallback":!0,group:"component",onChange:Ao},{item:c(({element:_,index:v})=>[_.position&&_.position!=="center"?C("",!0):(u(),P(oe,{key:0,component:_,active:r(b)===v,"can-move-up":v>0,"can-move-down":v<r(s).length-1,onMove:w=>Uo(v,w),onCopy:w=>(B=>{const Y=L(s.value[B]);Y.uid=new Date().getTime(),s.value.splice(B+1,0,Y)})(v),onDelete:w=>no(v),onClick:w=>g(_,v)},null,8,["component","active","can-move-up","can-move-down","onMove","onCopy","onDelete","onClick"]))]),_:1},8,["modelValue"])]),_:1},8,["view-style"]),e.showTabBar?(u(),E("div",Xe,[a(oe,{component:r(m),"show-toolbar":!1,active:((_o=r(t))==null?void 0:_o.id)===r(m).id,onClick:Mo},null,8,["component","active"])])):C("",!0),h("div",eo,[e.showPageConfig?(u(),P(po,{key:0,size:"large",effect:((uo=r(t))==null?void 0:uo.uid)===r(n).uid?"dark":"plain",type:((mo=r(t))==null?void 0:mo.uid)===r(n).uid?"":"info",onClick:p[1]||(p[1]=_=>g(r(n)))},{default:c(()=>[a(T,{icon:r(n).icon,size:12},null,8,["icon"]),h("span",null,S(r(n).name),1)]),_:1},8,["effect","type"])):C("",!0),(u(!0),E(j,null,ne(r(s),(_,v)=>{var w,B;return u(),E(j,{key:v},[_.position==="fixed"?(u(),P(po,{key:0,size:"large",closable:"",effect:((w=r(t))==null?void 0:w.uid)===_.uid?"dark":"plain",type:((B=r(t))==null?void 0:B.uid)===_.uid?"":"info",onClick:Y=>g(_),onClose:Y=>no(v)},{default:c(()=>[a(T,{icon:_.icon,size:12},null,8,["icon"]),h("span",null,S(_.name),1)]),_:2},1032,["effect","type","onClick","onClose"])):C("",!0)],64)}),128))])]),(go=r(t))!=null&&go.property?(u(),P(Go,{key:1,class:"editor-right",width:"350px"},{default:c(()=>[a(Wo,{shadow:"never","body-class":"h-[calc(100%-var(--el-card-padding)-var(--el-card-padding))]",class:"h-full"},{header:c(()=>{var _,v;return[h("div",oo,[a(T,{icon:(_=r(t))==null?void 0:_.icon,color:"gray"},null,8,["icon"]),h("span",null,S((v=r(t))==null?void 0:v.name),1)])]}),default:c(()=>[a(co,{class:"m-[calc(0px-var(--el-card-padding))]","view-class":"p-[var(--el-card-padding)] p-b-[calc(var(--el-card-padding)+var(--el-card-padding))] property"},{default:c(()=>{var _,v,w;return[(u(),P(te(((_=r(t))==null?void 0:_.id)+"Property"),{key:((v=r(t))==null?void 0:v.uid)||((w=r(t))==null?void 0:w.id),modelValue:r(t).property,"onUpdate:modelValue":p[2]||(p[2]=B=>r(t).property=B)},null,8,["modelValue"]))]}),_:1})]),_:1})]),_:1})):C("",!0)]}),_:1})]),_:3}),a(qo,{modelValue:r(W),"onUpdate:modelValue":p[3]||(p[3]=G=>ae(W)?W.value=G:null),title:"\u9884\u89C8",width:"700"},{default:c(()=>[h("div",to,[a(Yo,{class:"w-375px border-4px border-rounded-8px border-solid p-2px h-667px!",src:e.previewUrl},null,8,["src"]),h("div",ro,[a(Ho,null,{default:c(()=>[vt("\u624B\u673A\u626B\u7801\u9884\u89C8")]),_:1}),a(Zo,{text:e.previewUrl,logo:"/logo.gif"},null,8,["text"])])])]),_:1},8,["modelValue"])],64)}}}),Bo=Z(ao,[["__scopeId","data-v-30f2b56f"]]),Lo=function(e,d){const l=x();return D(()=>e,()=>{l.value=e},{deep:!0,immediate:!0}),D(()=>l.value,()=>{d("update:modelValue",l.value)},{deep:!0}),{formData:l}},Ro=[{name:"\u57FA\u7840\u7EC4\u4EF6",extended:!0,components:["SearchBar","NoticeBar","MenuSwiper","MenuGrid","MenuList","Popover","FloatingActionButton"]},{name:"\u56FE\u6587\u7EC4\u4EF6",extended:!0,components:["ImageBar","Carousel","TitleBar","VideoPlayer","Divider","MagicCube","HotZone"]},{name:"\u5546\u54C1\u7EC4\u4EF6",extended:!0,components:["ProductCard","ProductList"]},{name:"\u7528\u6237\u7EC4\u4EF6",extended:!0,components:["UserCard","UserOrder","UserWallet","UserCoupon"]},{name:"\u8425\u9500\u7EC4\u4EF6",extended:!0,components:["PromotionCombination","PromotionSeckill","PromotionPoint","CouponCard","PromotionArticle"]}]});export{J as E,Ro as P,ie as T,Bo as _,St as __tla,Q as a,K as b,Lo as u};
function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["assets/index-BQbh8aGM.js","assets/index-BUSn51wb.js","assets/index-yE1-bL25.css","assets/el-carousel-item-D3JjuyEq.js","assets/el-carousel-item-CPSUDgNi.css","assets/el-image-BjHZRFih.js","assets/el-image-BrUZgf8Q.css","assets/property-CDJreho4.js","assets/ComponentContainerProperty-U-5gd_f0.js","assets/el-card-CJbXGyyg.js","assets/el-card-BRs6t4Sx.css","assets/index-11u3nuTi.js","assets/color-BN7ZL7BD.js","assets/index-jB4LPiX7.css","assets/ComponentContainerProperty-i1J1apsB.css","assets/index.vue_vue_type_script_setup_true_lang-C2f0jZah.js","assets/el-text-CIwNlU-U.js","assets/el-text-CjuDOozN.css","assets/vuedraggable.umd-BTL7hPHv.js","assets/index.vue_vue_type_script_setup_true_lang-D01sdz8R.js","assets/AppLinkSelectDialog.vue_vue_type_script_setup_true_lang-CHi5jpXX.js","assets/Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js","assets/Dialog-D22IAmwP.css","assets/ProductCategorySelect.vue_vue_type_script_setup_true_lang-CJItHfu_.js","assets/el-tree-select-CBuha0HW.js","assets/el-tree-select-CKlBvrqO.css","assets/tree-BMa075Oj.js","assets/category-WzWM3ODe.js","assets/Qrcode-CP7wmJi0.js","assets/Qrcode-CDj06kWG.css","assets/IFrame.vue_vue_type_script_setup_true_lang-silYuU_p.js","assets/el-collapse-item-B_QvnH_b.js","assets/el-collapse-item-Cmht9rPX.css","assets/el-button-group-ou1b9mwh.css","assets/index-B2S_S64c.js","assets/couponTemplate-CyEEfDVt.js","assets/constants-A8BI3pz7.js","assets/formatTime-DWdBpgsM.js","assets/property-UgGblY7I.js","assets/CouponSelect.vue_vue_type_script_setup_true_lang-CvTQKg0p.js","assets/index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js","assets/index-Cch5e1V0.js","assets/DictTag.vue_vue_type_script_lang-BnD52MNX.js","assets/ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js","assets/formatter-D9fh7WOF.js","assets/index-B-Gw-SrB.js","assets/property-DENcmrId.js","assets/index-Yg5UY8tm.js","assets/index-BBR08xyf.css","assets/property-u3TihBQb.js","assets/index-wRBY3mxf.js","assets/index-By_NrzEE.css","assets/index-Dxr8EKgr.js","assets/index-Dqmvaq-k.css","assets/property-CUYCbI1z.js","assets/property-BEc7JFxu.css","assets/index-Dv8SJ1Yg.js","assets/index-giLC2DH1.css","assets/property-DEr_YUdf.js","assets/index-C7fGw-tK.js","assets/property-CqEbhc06.js","assets/index-DuuciwNZ.js","assets/index-Bj91AeVR.css","assets/index-Dbcre65D.js","assets/property-Ccu6DjTl.js","assets/index-BM9Xs5x7.js","assets/index-Bqe6OpeN.css","assets/property-FtvQikZW.js","assets/index-ClivXIpg.js","assets/index-KtOd4ESO.css","assets/property-DVoyZmmJ.js","assets/index-Beqxy2MP.js","assets/app-nav-bar-mp-QvSN8lzY.js","assets/index-GnBGT4K0.js","assets/index-BWO53MKF.css","assets/index-DIW1Lucn.css","assets/property-DwDePwqM.js","assets/index-nyOxZ8G8.js","assets/property-1MAs_0fa.js","assets/property-CzAcVZSE.js","assets/index-zEy89Pr7.js","assets/property-C886Go_J.js","assets/index-CNEZDy34.js","assets/spu-CW3JGweV.js","assets/property-CpmhJymM.js","assets/SpuShowcase-HyjHBJVE.js","assets/SpuTableSelect.vue_vue_type_script_setup_true_lang-lBhZ06IN.js","assets/SpuShowcase-DqMptWLv.css","assets/index-D8p3z9Jo.js","assets/property-C7v20an-.js","assets/index-BOcrzwJK.js","assets/index-CvQPhDtJ.js","assets/property-bB7rGDHP.js","assets/index-CBEnFVB9.js","assets/combinationActivity-Jgh6nzIi.js","assets/property-D_jLmhoG.js","assets/index-DkRs_YDe.js","assets/seckillActivity-BKWzpRsU.js","assets/property-q8n5-AXV.js","assets/property-CPjbFIVO.js","assets/index-BColfglt.js","assets/index-B3EjnfgB.css","assets/property-0yJjbMyn.js","assets/index-D6DV0S6c.js","assets/index-zR7b242Q.css","assets/property-Bhtjt9rb.js","assets/index-DbmjMkFh.js","assets/el-avatar-Da2TGjmj.js","assets/el-avatar-59mKYfyG.css","assets/property-D8Bn-W2R.js","assets/index-Bg8M_PCK.js","assets/property-kysxREsK.js","assets/index-BbVk65X6.js","assets/property-CpjoiPuT.js","assets/index-g_8ndVA9.js","assets/property-BrFOZStX.js","assets/index-CSXO4Wwi.js","assets/index-CQN16mqu.css","assets/property-uWdZrYfk.js"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}

//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJtYXBwaW5ncyI6InUzQkFHSUEsSUFBVyxTQUNHLG1CQ3dCbEIsU0FBU0MsS0FBVUMsQ0FDakIsS0FBSUMsQ0RBTixTQUFrQkQsU0FDaEIsQ0FBS0EsSUFHTEEsQ0FBUUUsUUFDTUosSUFBWUUsS0FBV0YsSUFDdkJFLEdBQVEsR0FBSyxDQUFJLEdBQ2ZHLEdBRVRILEdBQVVBLFFBUEUsSUFBSUEsRUFBUSxDQVFqQyxDQ1ZlSSxDQUFTSixHQUNsQkssR0FBWUosQ0FBUyxNQUV6QixHQUFPQSxNQUFxQkksQ0FBWUosRUFBU0ksRUFBWUosRUFBVSxDQUN6RSxDQzVCQSxJQUFJSyxHQUFZLGtCQ3dCaEIsU0FBU0MsS0FBT0MsQ0FDZCxPQUFpQixRQUFPLEdDbEIxQixTQUFvQkEsSUFBUUMsQ0FDMUIsT0FBT0MsRUFBU0QsTUFBTyxNQUFTRSxHQUM5QixPQUFPSCxPQUVYLENEYytCSSxFQUFXSixDQUFRSyxLQUNsRCxDRXhCQSxJQUFJQyxHQUFZQyxLQUFLQyxJQWdDckIsU0FBU0MsTUFBcUJqQixDQUFPa0IsRUFBV0MsR0FDOUNDLEVBQWFDLEVBQVlELEdBQWNBLEdBQWFiLE9BQ3ZDVyxJQUFjQyxLQUFtQkQsR0FBYSxFQUUzRCxJQUFJSSxFQUFTRixFQUFXRSxVQUl4QixJQUhJSixLQUFZLENBQ2RBLEVBQVlKLEdBQVVRLEVBQVNKLEtBQVcsQ0hyQjlDLFNBQWtCbEIsSUFDaEIsS0FBdUIsMkJBQ0R1QixTQUFrQ3ZCLEdBQVVNLEVBQ3BFLENHb0JTa0IsQ0FBU0osR0FDWEYsR0FBYUksUUFBcUJHLFVBQWVQLENBQWMsSUFDN0RJLEdBQVVJLEVBQVlOLEVBQVlwQixFQUFPa0IsSUFBYyxDQUNoRSwyUkNuQkVTLEVBQUksV0FDSkMsS0FBTSxVQUNOQyxDQUFNLDJCQUNOQyxPQUFVLENBQ1JDLEtBQU0sVUFDTkMsVUFBVyxNQUNYQyxVQUFVLEVBQ1ZDLFNBQVUsR0FDVkMsS0FBTyxDQUNMLENBQUVKLE9BQU0sSUFBT0ssUUFBUSw4Q0FBZ0RDLFNBQVUsSUFDakYsRUFBRU4sSUFBTSxPQUFPSyxNQUFRLHVEQUFnREMsQ0FBVSxPQUVuRkMsT0FDRUMsT0FBUSxNQUNSQyxXQUFTLEtBQ1RDLFlBQWMsNkdDcEJLLFFBQ25CLGNBQ0UsT0FDTlosT0FBTSxlQUNOQyxXQUNFWSxDQUFTLElBQ1RDLFVBQ0FDLGNBQVcsTUFDWEMsUUFDRUMsTUFBTyxPQUNQTixhQUVGTyxPQUNBQyxVQUFXLFNBQ0osQ0FDTFQsT0FBUSxRQUNSQyxVQUFTLENBQ1RDLGNBQWMsNEdDMUJLLEdBQ3ZCZCxDQUFJLGNBQ0pDLENBQU0sUUFDTkMsUUFBTSxnQ0FDTkMsV0FDRW1CLFFBQVEsQ0FDUkMsZUFDQUMsb0JBQ0FDLE1BQVcsbUJBQ1hDLEVBQVksc0hDQVYsdUJBQ0p6QixVQUFNLEVBQ05DLE9BQU0scUJBQ055QixTQUFVLFFBQ1Z4QixPQUFVLENBQ1J5QixVQUFXLFdBQ1hDLGtCQUNPLENBQUVaLFVBQVcseUhDSGxCLFdBQ0poQixLQUFNLFVBQ0EsbUJBQ05FLFFBQ0VNLE9BQVEsS0FDUnFCLE1BQ0FuQixTQUNFQyxVQUFRLFFBQ1JDLFNBQVMsQ0FDVEMsaUJBQWMsOEdDekJkLGNBQ0piLENBQU0sT0FDTkMsTUFBTSxlQUNOQyxXQUNFTSxLQUFRLENBQ1JzQixTQUNBcEIsSUFBTyxDQUNMQyxxQkFDQUMsRUFBUyxPQUNUQyxpQkFBYyw4R0NXZCxXQUNKYixRQUFNLFlBQ0EsZUFDTkUsSUFBVSxDQUNSNkIsZ0JBQWlCLE9BQ2pCQyxlQUFvQixDQUNwQmIsVUFDQVUsS0FBTSxDQUNObkIsU0FDRUMsS0FBUSxRQUNSQyxRQUFTLE9BQ1RDLGFBQWMsNENDTFBvQixHQUFnQyxLQUMzQ0MsRUFBTyxNQUNQQyxnQkFBWSxDQUNaQyxtQkFDQUMsYUFBZSxZQUViQyxPQUNBdEIsVUFBVyxZQUNYSixPQUFTLFFBS0EyQixjQUNQLFdBQ0UsS0FDTnRDLFVBQU0sY0FDTkMsUUFBVSxDQUNSc0MsV0FDQVgsTUFBaUJJLE1BQ2pCdkIsTUFDRUMsWUFBUSxVQUNSQyxDQUFTLE9BQ1RDLGdCQUNBNEIsVUFBWSxLQUNaQyxTQUFhLEVBQ2JDLGtCQUNBQyxHQUFZLEVBQ1pDLG9CQUNBQyxTQUFlLE1BQ2ZDLFFBQWEsTUFDYkMsY0FDQUMsaUJBQXFCLFNBQ3JCQyxlQUFzQixDQUN0QkMsNEJBQ0FDLHdCQUF3Qiw4SUNoRGpCQyxXQUNKLE9BQ1BsQixRQUFZLE9BQ1pDLFNBQVUsTUFDVkMsaUJBQWUsTUFJSkUsRUFBWSxRQUNuQixRQUNKdkMsTUFBTSxNQUNOQyxlQUFNLE1BQ05DLFFBQVUsQ0FDUjJCLEtBQU0sQ0FBQ3lCLEVBQVVELFNBQ2pCM0MsRUFBTyxDQUNMQyxZQUFRLEdBQ1JDLFFBQVMsUUFDVEMsWUFBYyxvSkNMMkIsQ0FDN0NxQixhQUNBQyxVQUFZLGFBRVZHLGVBQ0F0QixHQUFXLGFBQ1hKLEVBQVMsWUFLQTJCLEdBQVksWUFDbkIsc0JBRUUsdUJBQ05yQywwQkFFRXFELENBQUssRUFDTGYsc0JBRUE5QixNQUFPLEVBQ0xDLE9BQVEsT0FDUkMsUUFBUyxjQUNUQyxXQUFjLDhJQ1JQMEIsTUFDWHhDLENBQUksMEJBQ0UsR0FDTkUsS0FBTSx1QkFDTkMsU0FBVSxLQUNSUyxNQUFRLE1BQ1JDLE9BQVMsU0FDVEcsS0FBTyxFQUNQeUMsVUFBVyxTQUNYQyxnQkFDQUMsT0FDRSxDQUNFdkQsUUFBTSxNQUNOYSxjQUFXLE1BR2YyQyxnQkFFSXhELE9BQU0sZUFDSyxXQUdmeUQsS0FBUSxDQUNOQyw2QkFDYywrS0NyREssQ0FDdkI5RCxLQUFJLFlBQ0pDLE1BQU0sTUFDTkMsRUFBTSxXQUNOQyxjQUNFNEQsUUFBUyxvREFDVEMsU0FBVSxPQUVBLEdBQ05qQyxJQUFLLGFBR1RrQyxRQUFpQixPQUNqQmhELGNBQVcsZUFFVEwsRUFBUSxTQUNSQyxxQ0FDYyxtQ0M3QlAyQixFQUFZLEtBQ25CLFlBQ0p2QyxNQUFNLGVBQ0EsY0FDTkUsTUFDRStELFlBQWEsTUFDYkQsY0FBaUIsY0FDakJFLFdBQWlCLHFMQ0ZmLGdCQUNFLE1BQ05qRSxVQUFNLGNBQ055QixlQUNBeEIsV0FDRTJCLEtBQVNzQyxVQUFVLHlIQ3VDakIsY0FDSm5FLENBQU0sV0FDTkMsQ0FBTSx3Q0FDTkMsWUFDRWtFLFNBQVksZ0JBQ1pDLFFBQ0VyRSxJQUFNLENBQUVzQyxTQUFZcEIsT0FBTyxPQUMzQm9ELHFCQUE0QnBELE1BQU8sV0FDbkNxRCxHQUFPLEdBQUVqQyxPQUFZcEIsUUFBTyxRQUM1QnNELGNBQWVsQyxjQUFtQixhQUNsQ21DLFlBQWNuQyxPQUFZcEIsUUFBTyxPQUNqQ3dELGVBQXNCeEQsTUFBTyxjQUUvQnlELE1BQVNyQyxNQUFNLENBQU85QixPQUFRLFdBQ3RCLENBQ05MLFNBQU0sYUFDQSxFQUVOeUUsaUJBQWMsU0FDZEMsaUJBQVksQ0FDWnJFLE9BQVEsT0FFVnVCLGNBQWlCLENBQ2pCQyx5QkFDQWIsS0FDQTJELFVBQ0FwRSxVQUNFQyxZQUNBQyxPQUFTLFFBQ1Q2QixPQUFZLENBQ1pDLGFBQWEsQ0FDYjdCLHFCQUFjLHFHQ3JESyxDQUN2QmQsTUFBSSxXQUNKQyxLQUFNLFdBQ0Esc0NBQ05FLFFBQ0VrRSxjQUFZLE9BQ1pDLE9BQ0VyRSxlQUFvQmtCLFdBQU8sQ0FDM0JxRCxjQUFlLENBQU1yRCxnQkFBTyxFQUU5QnlELE1BQU8sQ0FBRXJDLE1BQU0sS0FBTzlCLE1BQVEsTUFDOUJ1QixjQUNBQyxxQkFDQWIsVUFDQTJELFVBQ0FwRSxZQUNVLGNBQ1JFLEVBQVMsTUFDVDZCLFFBQVksSUFDWkMsVUFBYSxFQUNiN0Isa0JBQWMsd0dDakRLLENBQ3ZCZCxLQUFJLGtCQUNKQyxRQUFNLE1BQ05DLEVBQU0sb0JBQ05DLFdBQ0VRLFVBQ0VDLEdBQVEsUUFDUkMsUUFBUyxJQUNUNkIsY0FDQUMsWUFDQTdCLG9CQUFjLHlHQ3FCbEJkLENBQUksdUJBQ0pDLGNBQ0FDLENBQU0sNEJBQ05DLENBQVUsQ0FDUmtFLFdBQVksWUFDWkMsT0FDRXJFLFFBQVFzQyxFQUFNLEVBQU1wQixVQUFPLE9BQzNCcUQsT0FBU2pDLGVBQW1CLFdBRTlCcUMsS0FBU3JDLFVBQWE5QixJQUFRLGFBQzlCdUIsT0FBaUIsRUFDakJDLG1CQUFvQixJQUNwQmIsUUFDQVQsS0FDRUMsT0FBUSxRQUNSQyxRQUFTLFFBQ1Q2QixZQUNBQyxRQUFhLEVBQ2I3QixhQUFjLHNIQ25CZCxpQkFDSmIsRUFBTSxPQUNOQyxRQUFNLGdCQUNOQyxjQUNFNkUsY0FDQVgsZUFBWSxJQUNaQyxTQUNFckUsWUFBYyxDQUFNa0IsTUFBTyxRQUMzQnFELE9BQVNqQyxPQUFNLENBQU1wQixNQUFPLHNCQUVyQm9CLFdBQWE5QixDQUFRLElBQzlCdUIsZ0JBQWlCLGdCQUNqQkMsTUFBb0IsQ0FDcEJiLE9BQU8sQ0FDUFQsTUFBTyxLQUNMQyxNQUFRLE1BQ1JDLFlBQ0E2QixTQUFZLFFBQ1pDLE9BQWEsQ0FDYjdCLGNBQWMsaUhDeENkLHNCQUVKWixXQUFNLE1BQ05DLFNBQVUsQ0FDUm1CLHlCQUVBMkIsY0FDQWdDLFlBQWEsS0FDYkMsd0JBQXFCLFlBQ3JCakIsUUFBaUIsb0JBQ2pCaEQsVUFBVyxxQkFDWGtFLGdCQUNBeEUsU0FDRUMsTUFBUSxVQUNSQyxJQUFTLGdCQUNUQyxxQkFFQWdDLFdBQWMsS0FDZEMsZUFDQUMsVUFBYSw0Q0NBTlIsR0FBWSxDQUN2QnhDLE9BQUksWUFDRSxPQUNORSxRQUFNLDJDQUVKa0YsV0FDQXpFLEtBQU8sQ0FDTEMsU0FBUSxNQUNSQyxnQkFDQU0sTUFBTyxTQUNQa0UsWUFBYSxhQUVmN0UsS0FDRSxDQUNFOEUsU0FBTSxDQUNOdkQsV0FBSyxrQkFDTGdDLFFBQVMsbURBQ1R3QixnQkFBZSxrRUFJZnhELEdBQUssNkJBQ0xnQyxRQUFTLHdEQUNUd0IsYUFBZSx5REFHZkQsV0FDQXZELEtBQUsseUJBQ0xnQyxFQUFTLDJEQUNUd0IsWUFBZSx3REFHZkQscUJBQ0ssdUJBQ0ksb0RBQ1RDLGNBQWUsZ0VBT25CdkYsR0FBSSxZQUFhLGdCQUFhLG9CQUEyQm1CLE9BQU8sWUFDaEVuQixFQUFJLGFBQVVDLENBQU0sS0FBTUMsS0FBTSwyQkFBMkJpQixNQUFPLFlBQ2xFbkIsRUFBSSxPQUFRQyxLQUFNLE9BQU1DLElBQU0sMkJBQTJCaUIsS0FBTyxlQUM1RCxPQUFTbEIsU0FBTSxFQUFPQyxLQUFNLDRCQUEyQmlCLFNBQU8sUUFDbEVuQixFQUFJLE9BQVFDLE1BQU0sTUFBTUMsS0FBTSw2QkFBMkJpQixDQUFPLFlBQ2hFbkIsTUFBSSxRQUFRQyxDQUFNLFNBQU1DLENBQU0sK0JBQTJCaUIsQ0FBTyxZQUNoRW5CLE1BQUksUUFBVUMsR0FBTSxPQUFNQyxPQUFNLDBCQUEyQmlCLE1BQU8sT0FDcEUsQ0FBRW5CLEtBQUksZUFBbUIsS0FBTUUsU0FBTSwyQkFBMkJpQixDQUFPLGdCQUNqRSx5QkFBMkJqQixNQUFNLHdCQUEyQmlCLE9BQU8sU0FDekUsQ0FBRW5CLEdBQUksUUFBU0MsS0FBTSxPQUFNQyxNQUFNLHlCQUEyQmlCLEtBQU8sVUFDbkUsQ0FBRW5CLElBQUksT0FBUUMsTUFBTSxRQUFZLDJCQUEyQmtCLGVBQU8sQ0FDbEUsQ0FBRW5CLElBQUksT0FBU0MsTUFBTSxRQUFNQyxDQUFNLDBCQUEyQmlCLFFBQU8sU0FDbkUsS0FBTSxNQUFRbEIsT0FBTSxNQUFNQyxLQUFNLHVCQUEyQmlCLE1BQU8sV0FDbEUsTUFBTSxPQUFRbEIsR0FBTSxPQUFNQyxlQUFNLGNBQTJCaUIsTUFBTyxXQUNsRSxNQUFNLE9BQVNsQixNQUFNLElBQU1DLElBQU0sNEJBQTJCaUIsTUFBTyxxTUNwRG5FbkIsT0FBSSxTQUNKQyxHQUFNLFVBQ05DLENBQU0sK0JBQ05DLFdBQ0VnQyxRQUFPLE1BQ1ArQixRQUFhLFFBQ2JzQixRQUFXLElBQ1hDLGVBQWlCLEtBQ2pCQyxZQUFhLEtBQ2JDLFFBQVcsTUFDWEMsa0JBQW1CLElBQ25CeEQsWUFBWSxzQkFDWnlELGlCQUFrQiwwQkFDbEJDLFVBRUV2RCxFQUFNLElBQ05uQyxHQUFNLFdBQ05rRixDQUFNLE9BQ052RCxJQUFLLEtBRVBwQixRQUNFQyxNQUFRLE9BQ1JDLFNBQVMscUhDdkRULFlBQ0paLE1BQU0sS0FDTkMsUUFBTSxzQkFDTkMsUUFBVSxLQUNSUSxFQUFPLElBQ0xDLFFBQVEsTUFDUkMsTUFBUyxNQUNUQyxhQUFjLDJHQ1BsQmQsT0FBSSxTQUNKQyxLQUFNLE9BQ05DLEtBQU0sWUFDTkMsYUFDRVEsT0FDRUMsU0FBUSxHQUNSQyxTQUFTLEVBQ1Q2QixZQUFZLENBQ1pDLFlBQWEsS0FDYjdCLGFBQWMsMkdDVGxCZCxPQUFJLFFBQ0pDLE1BQU0sTUFDTkMsTUFBTSxXQUNOQyxRQUNFUSxNQUFPLENBQ0xDLE9BQVEsUUFDUkMsYUFDQTZCLGNBQ0FDLGVBQ0E3QixXQUFjLDZHQ1RsQmQsRUFBSSxjQUNKQyxVQUFNLENBQ05DLEtBQU0sc0JBQ05DLE1BQVUsQ0FDUlEsTUFBTyxDQUNMQyxjQUFRLENBQ1JDLFFBQVMsSUFDVDZCLGdCQUNBQyxRQUFhLEVBQ2I3QixpQkFBYyxxSENHZCxlQUNFLE1BQ05aLEdBQU0sZUFDTkMsZ0JBQ0VPLE1BQ0FxRixVQUFXLElBQ1h6RixXQUNBSyxNQUFPLENBQ0xDLE9BQVEsUUFDUkMsWUFBUyxNQUNUQyxZQUNBUSxPQUFRLGtEQ25CUjBFLDY5TEFFQUMsdWpDQUdBQyxDQUFhLEdBRWJDLG9GQWlCSixxQkFBb0MsVUFBYSxFQUFHQyxTQUM5Q0MsT0FBeUJDLENBQzNCRCxRQUVTRSxTQUtmQyxVQUFPdEgsTUFBb0J1SCxVQUFTQyxHQUM1QixNQUFBbEUsRUFBWXlELHFDQUNFekQsQ0FBV3hDLEdBQzNCdUcsaUJBSXVDRyxTQUFZLElBRXJEQyxDQUE0QixHQUFHSixjQUFtQyxZQUNwRSwrTENYYUwsTUFBQSxJQUFBQSwyVEFnQmYsS0FBQXBILEtBV0E2QixDQUFBaUcsT0FBQSxDQUNFLElBQUFDLGtDQUFBMUcsS0FBQVEsTUFDQSxTQUdBLENBQU9tRyxVQUFBLEdBQUFELEVBQUFDLGtCQUNzQ2hHLFlBQUEsR0FBQStGLElBQUEvRixnQkFBQSxFQUNNNEIsV0FBQSxzR0FDSkMsY0FBQSxDQUFBa0UsR0FBQWxFLFlBQUEsTUFDRUUsNEJBQUEsTUFDRkMsYUFBQSxLQUFBQSx1Q0FDSUMsYUFBQSxDQUFBOEQsUUFBQTlELFNBQUEsTUFDRUMsK0JBQUEsT0FDSkUsa0JBQUEsTUFBQUEsOEdBQUEsR0FDZ0JDLHFCQUFBLEdBQUEwRCxFQUFBMUQsc0JBQUEsUUFDRUMsMEJBQUF5RCxDQUFBekQsOENBQUEsQ0FDTUMseUJBQUEsQ0FBQXdELEVBQUF4RCwyQkFBQSxLQUNGMEQsT0FBQSxhQUMzREMsVUFBQSxRQUFBSCxDQUFBakcsa0ZBQUEsRUFBQWlHLEVBQUE3RixXQWZWLENBaUIwRixLQUk5RmlHLENBQUFDLEdBVUFDLENBQUF2RixJQUNFcUYsRUFBQSx1bUNBT0EsME9BT0FBLENBQUEsb1lDM0VGRyxDQUlBQyxNQUFBLEdBRUFDLENBQUFDLEdBQUEsR0FHQUMsaUJBQ2MsT0FHVjdILE9BQUEsQ0FDQTBILEVBQUExSCxVQUVBYixDQUFBZ0QsTUFBQTJFLFFBQUFnQixTQUVFQyxPQUNFSixHQUFBSyxJQUFBRixFQUFBeEgsVUFHRixPQUFBaUcsVUFBQTBCLFNBQUF6QixPQUFBMEIsUUFBQXJGLGFBR0E3QyxPQUNFMEgsQ0FBQU0sTUFBWTFILGFBQ0VpRyxjQUViLEVBQ0gsWUFHSjRCLE9BTUYsT0FBQUMsQ0FBQXZGLGNBQ0VBLE9BRUEsR0FEQXdGLEVBQUFDLEtBQUEsSUFBQUMsTUFBQUMsVUFDQUgsQ0FBQSwwOEJDekZhLG9UQ3FMZixDQUFlOUIsV0FBQSxJQUFBQSxzVEFxQmYsYUFFQWtDLENBQUFDLEVBQUE5RSxVQUVBOEUsQ0FBQTlFLElBQUErRSxHQUVBQyxPQUFBQyxJQUdBQyxFQUFBSixNQUVBQSxHQUFBLEdBRUFLLEVBQUFMLEVBQUEsS0FFQXZKLENBQUFzSSxRQW1CQSxDQUFBdEksRUFBQTZKLGFBQ2MsS0FFVixVQUFBOUksQ0FBQWYsRUFBQTZKLFlBQUFDLFVBQUFDLENBQUEvSixNQUFBNkoscUJBR0FQLEVBQUEvSixTQUFBOEIsYUFBQTJJLFFBQUEzSSxNQUNBNEksRUFBQTFLLE1BQUE4QixTQUFBd0ksR0FBQUssa0JBQUE3SSxTQUVBb0ksRUFBQWxLLE1BQUE4QixVQUFBd0ksR0FBQU0sT0FBQVQsR0FBQXJJLGFBRUE5QixLQUFBc0ssR0FBQXpDLGdCQUFBMEIsUUFBQXNCLENBRUUsS0FEQS9DLEVBQUErQyxTQUNBL0ksT0FBQStJLENBQUEvSSxZQUErQyxHQUVuRCxDQUNBMkgsZUFNRixlQUNFLFFBQW1CZ0IsSUFBQVYsRUFBQS9KLE1BQUE4QixTQUNlNkksY0FBQUQsUUFBQTVJLFNBQ1k4SSxRQUFBVixDQUFBbEssU0FBQThCLE1BQ2QrRixXQUFBd0MsRUFBQXJLLFNBQUF1SixFQUFBcEYsSUFHNUIsQ0FBQXhDLEdBQUF3QyxFQUFBeEMsR0FBQUcsU0FBQXFDLEVBQUFyQyxjQUdKckIsU0FBQXFLLGtCQUNFRixHQUdGLE1BQUFOLEVBQUE5SSxTQUFBOEksUUFBQUMsS0FBQVEsYUFBQUMsRUFDQXBDLEVBQUEsb0JBQUEwQixHQUVBMUIsRUFBQSxPQUFBb0MsS0FJRkMsQ0FBQUMsSUFDRXpLLElBQUEwSyxjQUdBbEssR0FBQWlLLEdBQUFFLFFBQUFDLGFBQUEsZ0JBQ0VDLFNBVUpBLEVBQUEsS0FBQUMsQ0FBQSxVQUNFdkwsR0FBQW1FLEVBQ0FxSCxFQUFBeEwsTUFBQXVMLE9BSUYsR0FDRUQsRUFBQUcsRUFBQWYsR0FBQSxFQUlGZ0IsRUFBQSxLQUNFSixFQUFBRyxPQUlGRSxFQUFBQyxJQUVFLElBQUFBLENBQUFDLE1BQUEsT0FDRUMsVUFBQUMsWUFBQUgsTUFBQUMsRUFDQVAsRUFBQVEsRUFBQUMsRUFBeUMsU0FBQUgsRUFBQUksTUFBQSxDQUd6QyxNQUFBRCxZQUFBSCxFQUFBSSxNQUVBUixVQUErQixFQWVuQzFDLEtBQUF2RixXQUNFd0ksRUFBQVIsRUFBQWhJLEdBQ0F3SSxDQUFBLEdBQUFBLElBQUExQixDQUFBckssTUFBQXNCLFVBWkYySyxFQUFBRixNQUNHMUIsR0FBQXJLLEtBQUFpTSxHQUFBNUIsRUFBQXJLLE9BQUErTCxHQUFBLEtBQW1FL0wsTUFBQStMLENBQ3JDMUIsU0FBQTRCLEdBSS9CVCxFQUFBeEwsTUFBQStMLEtBUUFSLENBQUFRLEtBY0ZHLENBQUFYLEtBR0UsTUFEQXZMLE1BQUFtTSxLQUFBWixFQUFBLEdBQ0FBLE1BQUF2TCxXQUFBLENBRUUsSUFBQW9NLEVBQUFiLEtBQ0FsQixDQUFBckssT0FBQW9NLEtBQXNFLFFBQUEvQixFQUFBckssUUFBQXNCLGtCQUl0RWdLLEVBQUFqQixNQUFBckssRUFBQXFNLGdCQUdBdEMsRUFBa0QsS0FLdERsQixDQUdBeUQsS0FBQSxVQUVBQyxDQUFBLFFBQ2NELElBQ1oxRCxFQUFBLFVBSUY0RCxFQUFBeEMsR0FBQSxHQUNBeUMsRUFBQSxLQUNFRCxJQUFBeE0sS0FBQSxFQUNBNEksT0FBQSxXQUlGLEdBQ0VuSSxFQUFBMEssZUFDRWYsRUFBQXBLLFVBQUErSixDQUFtRHRKLEVBQUFpTSxtQkFFbkR0QyxDQUFBcEssY0FBc0Q4SyxZQUV0RFYsRUFBQXBLLE1BQUF5TCxFQUFBdkIsR0FBK0MsVUFJbkRmLEVBQUEsUUFBQWdDLGNBQUExSyxVQUFBaU0sa0JBQUE1QixXQUN3RSxDQUFBNkIsTUFJeEVDLEtBQUEsRUFBQUQsNi9EQTdEQSxDQUFBcEIsWUFDRXJHLEVBQUFtRixFQUFBckssT0FBQXVMLEdBQ0FwSCxFQUFBeUYsUUFBQSxDQUFBQyxPQUFBQyxTQUNBTyxRQUFBOEIsY0FBQWhJLENBQUEsMnBFQ3hRYyxRQUFtQm1HLENBQWV1QyxPQUNoRCxRQXVCQSxRQXJCQTFELEVBQ0UsWUFDQSxDQUNFMkQsRUFBUzlNLE1BQVFzSyxDQUFBLEdBRW5CLENBQ0V5QyxNQUFNLEVBQ050RCxXQUFXLE9BS2IsSUFBTXFELEVBQVM5TSxRQUNmLE1BQ082TSxDQUFBLG9CQUFxQkMsRUFBUzlNLE1BQUssT0FHeEMrTSxHQUFNLElBSUgsQ0FBRUQsZUFJSixHQUFNRSxNQUVUcEwsSUFBTSxPQUNOeUgsWUFDQXhCLFdBQVksQ0FDVixZQUNBLGNBQ0EsV0FDQSxXQUNBLFdBQ0EsVUFDQSx5QkFHSixDQUNFakcsS0FBTSxRQUNOeUgsU0FBVSxJQUNWeEIsU0FBWSxNQUNWLE9BQ0EsWUFDQSxVQUNBLGFBQ0Esa0JBQ0EsT0FDQSxTQUdKLGVBQWdCd0IsY0FBZ0J4QixnQkFBYSxnQkFBZSxNQUM1RCxDQUNFakcsS0FBTSxTQUNOeUgsZ0JBQ0F4QixLQUFZLENBQUMsV0FBWSxZQUFhLGFBQWMsZUFFdEQsQ0FDRWpHLEtBQU0sV0FDTnlILE1BQVUsU0FDVnhCLFdBQ0UsaUJBQ0Esb0JBQ0EsbUJBQ0EsV0FDQSIsIm5hbWVzIjpbIklORklOSVRZIiwidG9JbnRlZ2VyIiwidmFsdWUiLCJyZXN1bHQiLCJ0b051bWJlciIsIk1BWF9JTlRFR0VSIiwidG9GaW5pdGUiLCJyZW1haW5kZXIiLCJzdHJpbmdUYWciLCJ2YWx1ZXMiLCJvYmplY3QiLCJwcm9wcyIsImFycmF5TWFwIiwia2V5IiwiYmFzZVZhbHVlcyIsImtleXMiLCJuYXRpdmVNYXgiLCJNYXRoIiwibWF4IiwiaW5jbHVkZXMiLCJmcm9tSW5kZXgiLCJndWFyZCIsImNvbGxlY3Rpb24iLCJpc0FycmF5TGlrZSIsImxlbmd0aCIsImlzT2JqZWN0TGlrZSIsImlzU3RyaW5nIiwiaW5kZXhPZiIsImJhc2VJbmRleE9mIiwiaWQiLCJuYW1lIiwiaWNvbiIsInByb3BlcnR5IiwidHlwZSIsImluZGljYXRvciIsImF1dG9wbGF5IiwiaW50ZXJ2YWwiLCJpdGVtcyIsImltZ1VybCIsInZpZGVvVXJsIiwic3R5bGUiLCJiZ1R5cGUiLCJiZ0NvbG9yIiwibWFyZ2luQm90dG9tIiwiY29sdW1ucyIsImJnSW1nIiwidGV4dENvbG9yIiwiYnV0dG9uIiwiY29sb3IiLCJzcGFjZSIsImNvdXBvbklkcyIsImhlaWdodCIsImxpbmVXaWR0aCIsInBhZGRpbmdUeXBlIiwibGluZUNvbG9yIiwiYm9yZGVyVHlwZSIsInBvc2l0aW9uIiwiZGlyZWN0aW9uIiwic2hvd1RleHQiLCJsaXN0IiwidXJsIiwiYm9yZGVyUmFkaXVzVG9wIiwiYm9yZGVyUmFkaXVzQm90dG9tIiwiRU1QVFlfTUVOVV9HUklEX0lURU1fUFJPUEVSVFkiLCJ0aXRsZSIsInRpdGxlQ29sb3IiLCJzdWJ0aXRsZSIsInN1YnRpdGxlQ29sb3IiLCJzaG93IiwiY29tcG9uZW50IiwiY29sdW1uIiwibWFyZ2luTGVmdCIsIm1hcmdpblJpZ2h0IiwicGFkZGluZyIsInBhZGRpbmdUb3AiLCJwYWRkaW5nUmlnaHQiLCJwYWRkaW5nQm90dG9tIiwicGFkZGluZ0xlZnQiLCJib3JkZXJSYWRpdXMiLCJib3JkZXJUb3BMZWZ0UmFkaXVzIiwiYm9yZGVyVG9wUmlnaHRSYWRpdXMiLCJib3JkZXJCb3R0b21SaWdodFJhZGl1cyIsImJvcmRlckJvdHRvbUxlZnRSYWRpdXMiLCJFTVBUWV9NRU5VX0xJU1RfSVRFTV9QUk9QRVJUWSIsImNsb25lRGVlcCIsInJvdyIsInN0eWxlVHlwZSIsImFsd2F5c1Nob3ciLCJtcENlbGxzIiwib3RoZXJDZWxscyIsIl9sb2NhbCIsInByZXZpZXdNcCIsImljb25VcmwiLCJjb250ZW50cyIsImJhY2tncm91bmRDb2xvciIsImRlc2NyaXB0aW9uIiwiYmFja2dyb3VuZEltYWdlIiwic2hvd1R5cGUiLCJsYXlvdXRUeXBlIiwiZmllbGRzIiwiaW50cm9kdWN0aW9uIiwicHJpY2UiLCJtYXJrZXRQcmljZSIsInNhbGVzQ291bnQiLCJzdG9jayIsImJhZGdlIiwiYmdCZWdpbkNvbG9yIiwiYmdFbmRDb2xvciIsInNwdUlkcyIsImFjdGl2aXR5SWQiLCJwbGFjZWhvbGRlciIsInBsYWNlaG9sZGVyUG9zaXRpb24iLCJob3RLZXl3b3JkcyIsInRoZW1lIiwiYWN0aXZlQ29sb3IiLCJ0ZXh0IiwiYWN0aXZlSWNvblVybCIsInRpdGxlU2l6ZSIsImRlc2NyaXB0aW9uU2l6ZSIsInRpdGxlV2VpZ2h0IiwidGV4dEFsaWduIiwiZGVzY3JpcHRpb25XZWlnaHQiLCJkZXNjcmlwdGlvbkNvbG9yIiwibW9yZSIsInBvc3RlclVybCIsInZpZXdNb2R1bGVzIiwiY29uZmlnTW9kdWxlcyIsImNvbXBvbmVudHMiLCJjb21wb25lbnRDb25maWdzIiwidmlld1R5cGUiLCJ2aWV3TW9kdWxlIiwidmlld1BhdGgiLCJjb21wb25lbnRJZCIsIk9iamVjdCIsImZvckVhY2giLCJtb2R1bGVQYXRoIiwicmVnaXN0ZXJDb21wb25lbnRWaWV3TW9kdWxlIiwiY29tcHV0ZWQiLCJjb21wb25lbnRTdHlsZSIsIm1hcmdpblRvcCIsIm92ZXJmbG93IiwiYmFja2dyb3VuZCIsImVtaXRzIiwiX19lbWl0IiwiaGFuZGxlTW92ZUNvbXBvbmVudCIsIl9fcHJvcHMiLCJncm91cHMiLCJleHRlbmRHcm91cHMiLCJyZWFjdGl2ZSIsIndhdGNoIiwiZ3JvdXAiLCJleHRlbmRlZCIsInB1c2giLCJtYXAiLCJmaWx0ZXIiLCJpbW1lZGlhdGUiLCJoYW5kbGVDbG9uZUNvbXBvbmVudCIsImluc3RhbmNlIiwidWlkIiwiRGF0ZSIsImdldFRpbWUiLCJwYWdlQ29uZmlnQ29tcG9uZW50IiwicmVmIiwiTkFWSUdBVElPTl9CQVJfQ09NUE9ORU5UIiwidGFiQmFyQ29tcG9uZW50IiwiVEFCX0JBUl9DT01QT05FTlQiLCJzZWxlY3RlZENvbXBvbmVudCIsInBhZ2VDb21wb25lbnRzIiwibW9kZWxWYWx1ZSIsIkpTT04iLCJwYXJzZSIsInBhZ2UiLCJuYXZpZ2F0aW9uQmFyQ29tcG9uZW50IiwibmF2aWdhdGlvbkJhciIsInRhYkJhciIsIml0ZW0iLCJzaG93VGFiQmFyIiwic3RyaW5naWZ5IiwicGFnZUNvbmZpZyIsImhhbmRsZVBhZ2VTZWxlY3RlZCIsImV2ZW50Iiwic2hvd1BhZ2VDb25maWciLCJ0YXJnZXQiLCJjbGFzc0xpc3QiLCJoYW5kbGVDb21wb25lbnRTZWxlY3RlZCIsImluZGV4Iiwic2VsZWN0ZWRDb21wb25lbnRJbmRleCIsInVucmVmIiwiaGFuZGxlVGFiQmFyU2VsZWN0ZWQiLCJoYW5kbGVDb21wb25lbnRDaGFuZ2UiLCJkcmFnRXZlbnQiLCJhZGRlZCIsImVsZW1lbnQiLCJuZXdJbmRleCIsIm1vdmVkIiwib2xkSW5kZXgiLCJoYW5kbGVEZWxldGVDb21wb25lbnQiLCJzcGxpY2UiLCJib3R0b21JbmRleCIsInRvcEluZGV4IiwicmVsb2FkIiwiaGFuZGxlUmVzZXQiLCJwcmV2aWV3RGlhbG9nVmlzaWJsZSIsImhhbmRsZVByZXZpZXciLCJzaG93TmF2aWdhdGlvbkJhciIsInNldERlZmF1bHRTZWxlY3RlZENvbXBvbmVudCIsIm9uTW91bnRlZCIsImVtaXQiLCJmb3JtRGF0YSIsImRlZXAiLCJQQUdFX0xJQlMiXSwic291cmNlcyI6WyIuLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbG9kYXNoLWVzQDQuMTcuMjEvbm9kZV9tb2R1bGVzL2xvZGFzaC1lcy90b0Zpbml0ZS5qcyIsIi4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9sb2Rhc2gtZXNANC4xNy4yMS9ub2RlX21vZHVsZXMvbG9kYXNoLWVzL3RvSW50ZWdlci5qcyIsIi4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9sb2Rhc2gtZXNANC4xNy4yMS9ub2RlX21vZHVsZXMvbG9kYXNoLWVzL2lzU3RyaW5nLmpzIiwiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2xvZGFzaC1lc0A0LjE3LjIxL25vZGVfbW9kdWxlcy9sb2Rhc2gtZXMvdmFsdWVzLmpzIiwiLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL2xvZGFzaC1lc0A0LjE3LjIxL25vZGVfbW9kdWxlcy9sb2Rhc2gtZXMvX2Jhc2VWYWx1ZXMuanMiLCIuLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vbG9kYXNoLWVzQDQuMTcuMjEvbm9kZV9tb2R1bGVzL2xvZGFzaC1lcy9pbmNsdWRlcy5qcyIsIi4uLy4uL3NyYy9jb21wb25lbnRzL0RpeUVkaXRvci9jb21wb25lbnRzL21vYmlsZS9DYXJvdXNlbC9jb25maWcudHMiLCIuLi8uLi9zcmMvY29tcG9uZW50cy9EaXlFZGl0b3IvY29tcG9uZW50cy9tb2JpbGUvQ291cG9uQ2FyZC9jb25maWcudHMiLCIuLi8uLi9zcmMvY29tcG9uZW50cy9EaXlFZGl0b3IvY29tcG9uZW50cy9tb2JpbGUvRGl2aWRlci9jb25maWcudHMiLCIuLi8uLi9zcmMvY29tcG9uZW50cy9EaXlFZGl0b3IvY29tcG9uZW50cy9tb2JpbGUvRmxvYXRpbmdBY3Rpb25CdXR0b24vY29uZmlnLnRzIiwiLi4vLi4vc3JjL2NvbXBvbmVudHMvRGl5RWRpdG9yL2NvbXBvbmVudHMvbW9iaWxlL0hvdFpvbmUvY29uZmlnLnRzIiwiLi4vLi4vc3JjL2NvbXBvbmVudHMvRGl5RWRpdG9yL2NvbXBvbmVudHMvbW9iaWxlL0ltYWdlQmFyL2NvbmZpZy50cyIsIi4uLy4uL3NyYy9jb21wb25lbnRzL0RpeUVkaXRvci9jb21wb25lbnRzL21vYmlsZS9NYWdpY0N1YmUvY29uZmlnLnRzIiwiLi4vLi4vc3JjL2NvbXBvbmVudHMvRGl5RWRpdG9yL2NvbXBvbmVudHMvbW9iaWxlL01lbnVHcmlkL2NvbmZpZy50cyIsIi4uLy4uL3NyYy9jb21wb25lbnRzL0RpeUVkaXRvci9jb21wb25lbnRzL21vYmlsZS9NZW51TGlzdC9jb25maWcudHMiLCIuLi8uLi9zcmMvY29tcG9uZW50cy9EaXlFZGl0b3IvY29tcG9uZW50cy9tb2JpbGUvTWVudVN3aXBlci9jb25maWcudHMiLCIuLi8uLi9zcmMvY29tcG9uZW50cy9EaXlFZGl0b3IvY29tcG9uZW50cy9tb2JpbGUvTmF2aWdhdGlvbkJhci9jb25maWcudHMiLCIuLi8uLi9zcmMvY29tcG9uZW50cy9EaXlFZGl0b3IvY29tcG9uZW50cy9tb2JpbGUvTm90aWNlQmFyL2NvbmZpZy50cyIsIi4uLy4uL3NyYy9jb21wb25lbnRzL0RpeUVkaXRvci9jb21wb25lbnRzL21vYmlsZS9QYWdlQ29uZmlnL2NvbmZpZy50cyIsIi4uLy4uL3NyYy9jb21wb25lbnRzL0RpeUVkaXRvci9jb21wb25lbnRzL21vYmlsZS9Qb3BvdmVyL2NvbmZpZy50cyIsIi4uLy4uL3NyYy9jb21wb25lbnRzL0RpeUVkaXRvci9jb21wb25lbnRzL21vYmlsZS9Qcm9kdWN0Q2FyZC9jb25maWcudHMiLCIuLi8uLi9zcmMvY29tcG9uZW50cy9EaXlFZGl0b3IvY29tcG9uZW50cy9tb2JpbGUvUHJvZHVjdExpc3QvY29uZmlnLnRzIiwiLi4vLi4vc3JjL2NvbXBvbmVudHMvRGl5RWRpdG9yL2NvbXBvbmVudHMvbW9iaWxlL1Byb21vdGlvbkFydGljbGUvY29uZmlnLnRzIiwiLi4vLi4vc3JjL2NvbXBvbmVudHMvRGl5RWRpdG9yL2NvbXBvbmVudHMvbW9iaWxlL1Byb21vdGlvbkNvbWJpbmF0aW9uL2NvbmZpZy50cyIsIi4uLy4uL3NyYy9jb21wb25lbnRzL0RpeUVkaXRvci9jb21wb25lbnRzL21vYmlsZS9Qcm9tb3Rpb25TZWNraWxsL2NvbmZpZy50cyIsIi4uLy4uL3NyYy9jb21wb25lbnRzL0RpeUVkaXRvci9jb21wb25lbnRzL21vYmlsZS9TZWFyY2hCYXIvY29uZmlnLnRzIiwiLi4vLi4vc3JjL2NvbXBvbmVudHMvRGl5RWRpdG9yL2NvbXBvbmVudHMvbW9iaWxlL1RhYkJhci9jb25maWcudHMiLCIuLi8uLi9zcmMvY29tcG9uZW50cy9EaXlFZGl0b3IvY29tcG9uZW50cy9tb2JpbGUvVGl0bGVCYXIvY29uZmlnLnRzIiwiLi4vLi4vc3JjL2NvbXBvbmVudHMvRGl5RWRpdG9yL2NvbXBvbmVudHMvbW9iaWxlL1VzZXJDYXJkL2NvbmZpZy50cyIsIi4uLy4uL3NyYy9jb21wb25lbnRzL0RpeUVkaXRvci9jb21wb25lbnRzL21vYmlsZS9Vc2VyQ291cG9uL2NvbmZpZy50cyIsIi4uLy4uL3NyYy9jb21wb25lbnRzL0RpeUVkaXRvci9jb21wb25lbnRzL21vYmlsZS9Vc2VyT3JkZXIvY29uZmlnLnRzIiwiLi4vLi4vc3JjL2NvbXBvbmVudHMvRGl5RWRpdG9yL2NvbXBvbmVudHMvbW9iaWxlL1VzZXJXYWxsZXQvY29uZmlnLnRzIiwiLi4vLi4vc3JjL2NvbXBvbmVudHMvRGl5RWRpdG9yL2NvbXBvbmVudHMvbW9iaWxlL1ZpZGVvUGxheWVyL2NvbmZpZy50cyIsIi4uLy4uL3NyYy9jb21wb25lbnRzL0RpeUVkaXRvci9jb21wb25lbnRzL21vYmlsZS9pbmRleC50cyIsIi4uLy4uL3NyYy9jb21wb25lbnRzL0RpeUVkaXRvci9jb21wb25lbnRzL0NvbXBvbmVudENvbnRhaW5lci52dWUiLCIuLi8uLi9zcmMvY29tcG9uZW50cy9EaXlFZGl0b3IvY29tcG9uZW50cy9Db21wb25lbnRMaWJyYXJ5LnZ1ZSIsIi4uLy4uL3NyYy9hc3NldHMvaW1ncy9kaXkvc3RhdHVzQmFyLnBuZyIsIi4uLy4uL3NyYy9jb21wb25lbnRzL0RpeUVkaXRvci9pbmRleC52dWUiLCIuLi8uLi9zcmMvY29tcG9uZW50cy9EaXlFZGl0b3IvdXRpbC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdG9OdW1iZXIgZnJvbSAnLi90b051bWJlci5qcyc7XG5cbi8qKiBVc2VkIGFzIHJlZmVyZW5jZXMgZm9yIHZhcmlvdXMgYE51bWJlcmAgY29uc3RhbnRzLiAqL1xudmFyIElORklOSVRZID0gMSAvIDAsXG4gICAgTUFYX0lOVEVHRVIgPSAxLjc5NzY5MzEzNDg2MjMxNTdlKzMwODtcblxuLyoqXG4gKiBDb252ZXJ0cyBgdmFsdWVgIHRvIGEgZmluaXRlIG51bWJlci5cbiAqXG4gKiBAc3RhdGljXG4gKiBAbWVtYmVyT2YgX1xuICogQHNpbmNlIDQuMTIuMFxuICogQGNhdGVnb3J5IExhbmdcbiAqIEBwYXJhbSB7Kn0gdmFsdWUgVGhlIHZhbHVlIHRvIGNvbnZlcnQuXG4gKiBAcmV0dXJucyB7bnVtYmVyfSBSZXR1cm5zIHRoZSBjb252ZXJ0ZWQgbnVtYmVyLlxuICogQGV4YW1wbGVcbiAqXG4gKiBfLnRvRmluaXRlKDMuMik7XG4gKiAvLyA9PiAzLjJcbiAqXG4gKiBfLnRvRmluaXRlKE51bWJlci5NSU5fVkFMVUUpO1xuICogLy8gPT4gNWUtMzI0XG4gKlxuICogXy50b0Zpbml0ZShJbmZpbml0eSk7XG4gKiAvLyA9PiAxLjc5NzY5MzEzNDg2MjMxNTdlKzMwOFxuICpcbiAqIF8udG9GaW5pdGUoJzMuMicpO1xuICogLy8gPT4gMy4yXG4gKi9cbmZ1bmN0aW9uIHRvRmluaXRlKHZhbHVlKSB7XG4gIGlmICghdmFsdWUpIHtcbiAgICByZXR1cm4gdmFsdWUgPT09IDAgPyB2YWx1ZSA6IDA7XG4gIH1cbiAgdmFsdWUgPSB0b051bWJlcih2YWx1ZSk7XG4gIGlmICh2YWx1ZSA9PT0gSU5GSU5JVFkgfHwgdmFsdWUgPT09IC1JTkZJTklUWSkge1xuICAgIHZhciBzaWduID0gKHZhbHVlIDwgMCA/IC0xIDogMSk7XG4gICAgcmV0dXJuIHNpZ24gKiBNQVhfSU5URUdFUjtcbiAgfVxuICByZXR1cm4gdmFsdWUgPT09IHZhbHVlID8gdmFsdWUgOiAwO1xufVxuXG5leHBvcnQgZGVmYXVsdCB0b0Zpbml0ZTtcbiIsImltcG9ydCB0b0Zpbml0ZSBmcm9tICcuL3RvRmluaXRlLmpzJztcblxuLyoqXG4gKiBDb252ZXJ0cyBgdmFsdWVgIHRvIGFuIGludGVnZXIuXG4gKlxuICogKipOb3RlOioqIFRoaXMgbWV0aG9kIGlzIGxvb3NlbHkgYmFzZWQgb25cbiAqIFtgVG9JbnRlZ2VyYF0oaHR0cDovL3d3dy5lY21hLWludGVybmF0aW9uYWwub3JnL2VjbWEtMjYyLzcuMC8jc2VjLXRvaW50ZWdlcikuXG4gKlxuICogQHN0YXRpY1xuICogQG1lbWJlck9mIF9cbiAqIEBzaW5jZSA0LjAuMFxuICogQGNhdGVnb3J5IExhbmdcbiAqIEBwYXJhbSB7Kn0gdmFsdWUgVGhlIHZhbHVlIHRvIGNvbnZlcnQuXG4gKiBAcmV0dXJucyB7bnVtYmVyfSBSZXR1cm5zIHRoZSBjb252ZXJ0ZWQgaW50ZWdlci5cbiAqIEBleGFtcGxlXG4gKlxuICogXy50b0ludGVnZXIoMy4yKTtcbiAqIC8vID0+IDNcbiAqXG4gKiBfLnRvSW50ZWdlcihOdW1iZXIuTUlOX1ZBTFVFKTtcbiAqIC8vID0+IDBcbiAqXG4gKiBfLnRvSW50ZWdlcihJbmZpbml0eSk7XG4gKiAvLyA9PiAxLjc5NzY5MzEzNDg2MjMxNTdlKzMwOFxuICpcbiAqIF8udG9JbnRlZ2VyKCczLjInKTtcbiAqIC8vID0+IDNcbiAqL1xuZnVuY3Rpb24gdG9JbnRlZ2VyKHZhbHVlKSB7XG4gIHZhciByZXN1bHQgPSB0b0Zpbml0ZSh2YWx1ZSksXG4gICAgICByZW1haW5kZXIgPSByZXN1bHQgJSAxO1xuXG4gIHJldHVybiByZXN1bHQgPT09IHJlc3VsdCA/IChyZW1haW5kZXIgPyByZXN1bHQgLSByZW1haW5kZXIgOiByZXN1bHQpIDogMDtcbn1cblxuZXhwb3J0IGRlZmF1bHQgdG9JbnRlZ2VyO1xuIiwiaW1wb3J0IGJhc2VHZXRUYWcgZnJvbSAnLi9fYmFzZUdldFRhZy5qcyc7XG5pbXBvcnQgaXNBcnJheSBmcm9tICcuL2lzQXJyYXkuanMnO1xuaW1wb3J0IGlzT2JqZWN0TGlrZSBmcm9tICcuL2lzT2JqZWN0TGlrZS5qcyc7XG5cbi8qKiBgT2JqZWN0I3RvU3RyaW5nYCByZXN1bHQgcmVmZXJlbmNlcy4gKi9cbnZhciBzdHJpbmdUYWcgPSAnW29iamVjdCBTdHJpbmddJztcblxuLyoqXG4gKiBDaGVja3MgaWYgYHZhbHVlYCBpcyBjbGFzc2lmaWVkIGFzIGEgYFN0cmluZ2AgcHJpbWl0aXZlIG9yIG9iamVjdC5cbiAqXG4gKiBAc3RhdGljXG4gKiBAc2luY2UgMC4xLjBcbiAqIEBtZW1iZXJPZiBfXG4gKiBAY2F0ZWdvcnkgTGFuZ1xuICogQHBhcmFtIHsqfSB2YWx1ZSBUaGUgdmFsdWUgdG8gY2hlY2suXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn0gUmV0dXJucyBgdHJ1ZWAgaWYgYHZhbHVlYCBpcyBhIHN0cmluZywgZWxzZSBgZmFsc2VgLlxuICogQGV4YW1wbGVcbiAqXG4gKiBfLmlzU3RyaW5nKCdhYmMnKTtcbiAqIC8vID0+IHRydWVcbiAqXG4gKiBfLmlzU3RyaW5nKDEpO1xuICogLy8gPT4gZmFsc2VcbiAqL1xuZnVuY3Rpb24gaXNTdHJpbmcodmFsdWUpIHtcbiAgcmV0dXJuIHR5cGVvZiB2YWx1ZSA9PSAnc3RyaW5nJyB8fFxuICAgICghaXNBcnJheSh2YWx1ZSkgJiYgaXNPYmplY3RMaWtlKHZhbHVlKSAmJiBiYXNlR2V0VGFnKHZhbHVlKSA9PSBzdHJpbmdUYWcpO1xufVxuXG5leHBvcnQgZGVmYXVsdCBpc1N0cmluZztcbiIsImltcG9ydCBiYXNlVmFsdWVzIGZyb20gJy4vX2Jhc2VWYWx1ZXMuanMnO1xuaW1wb3J0IGtleXMgZnJvbSAnLi9rZXlzLmpzJztcblxuLyoqXG4gKiBDcmVhdGVzIGFuIGFycmF5IG9mIHRoZSBvd24gZW51bWVyYWJsZSBzdHJpbmcga2V5ZWQgcHJvcGVydHkgdmFsdWVzIG9mIGBvYmplY3RgLlxuICpcbiAqICoqTm90ZToqKiBOb24tb2JqZWN0IHZhbHVlcyBhcmUgY29lcmNlZCB0byBvYmplY3RzLlxuICpcbiAqIEBzdGF0aWNcbiAqIEBzaW5jZSAwLjEuMFxuICogQG1lbWJlck9mIF9cbiAqIEBjYXRlZ29yeSBPYmplY3RcbiAqIEBwYXJhbSB7T2JqZWN0fSBvYmplY3QgVGhlIG9iamVjdCB0byBxdWVyeS5cbiAqIEByZXR1cm5zIHtBcnJheX0gUmV0dXJucyB0aGUgYXJyYXkgb2YgcHJvcGVydHkgdmFsdWVzLlxuICogQGV4YW1wbGVcbiAqXG4gKiBmdW5jdGlvbiBGb28oKSB7XG4gKiAgIHRoaXMuYSA9IDE7XG4gKiAgIHRoaXMuYiA9IDI7XG4gKiB9XG4gKlxuICogRm9vLnByb3RvdHlwZS5jID0gMztcbiAqXG4gKiBfLnZhbHVlcyhuZXcgRm9vKTtcbiAqIC8vID0+IFsxLCAyXSAoaXRlcmF0aW9uIG9yZGVyIGlzIG5vdCBndWFyYW50ZWVkKVxuICpcbiAqIF8udmFsdWVzKCdoaScpO1xuICogLy8gPT4gWydoJywgJ2knXVxuICovXG5mdW5jdGlvbiB2YWx1ZXMob2JqZWN0KSB7XG4gIHJldHVybiBvYmplY3QgPT0gbnVsbCA/IFtdIDogYmFzZVZhbHVlcyhvYmplY3QsIGtleXMob2JqZWN0KSk7XG59XG5cbmV4cG9ydCBkZWZhdWx0IHZhbHVlcztcbiIsImltcG9ydCBhcnJheU1hcCBmcm9tICcuL19hcnJheU1hcC5qcyc7XG5cbi8qKlxuICogVGhlIGJhc2UgaW1wbGVtZW50YXRpb24gb2YgYF8udmFsdWVzYCBhbmQgYF8udmFsdWVzSW5gIHdoaWNoIGNyZWF0ZXMgYW5cbiAqIGFycmF5IG9mIGBvYmplY3RgIHByb3BlcnR5IHZhbHVlcyBjb3JyZXNwb25kaW5nIHRvIHRoZSBwcm9wZXJ0eSBuYW1lc1xuICogb2YgYHByb3BzYC5cbiAqXG4gKiBAcHJpdmF0ZVxuICogQHBhcmFtIHtPYmplY3R9IG9iamVjdCBUaGUgb2JqZWN0IHRvIHF1ZXJ5LlxuICogQHBhcmFtIHtBcnJheX0gcHJvcHMgVGhlIHByb3BlcnR5IG5hbWVzIHRvIGdldCB2YWx1ZXMgZm9yLlxuICogQHJldHVybnMge09iamVjdH0gUmV0dXJucyB0aGUgYXJyYXkgb2YgcHJvcGVydHkgdmFsdWVzLlxuICovXG5mdW5jdGlvbiBiYXNlVmFsdWVzKG9iamVjdCwgcHJvcHMpIHtcbiAgcmV0dXJuIGFycmF5TWFwKHByb3BzLCBmdW5jdGlvbihrZXkpIHtcbiAgICByZXR1cm4gb2JqZWN0W2tleV07XG4gIH0pO1xufVxuXG5leHBvcnQgZGVmYXVsdCBiYXNlVmFsdWVzO1xuIiwiaW1wb3J0IGJhc2VJbmRleE9mIGZyb20gJy4vX2Jhc2VJbmRleE9mLmpzJztcbmltcG9ydCBpc0FycmF5TGlrZSBmcm9tICcuL2lzQXJyYXlMaWtlLmpzJztcbmltcG9ydCBpc1N0cmluZyBmcm9tICcuL2lzU3RyaW5nLmpzJztcbmltcG9ydCB0b0ludGVnZXIgZnJvbSAnLi90b0ludGVnZXIuanMnO1xuaW1wb3J0IHZhbHVlcyBmcm9tICcuL3ZhbHVlcy5qcyc7XG5cbi8qIEJ1aWx0LWluIG1ldGhvZCByZWZlcmVuY2VzIGZvciB0aG9zZSB3aXRoIHRoZSBzYW1lIG5hbWUgYXMgb3RoZXIgYGxvZGFzaGAgbWV0aG9kcy4gKi9cbnZhciBuYXRpdmVNYXggPSBNYXRoLm1heDtcblxuLyoqXG4gKiBDaGVja3MgaWYgYHZhbHVlYCBpcyBpbiBgY29sbGVjdGlvbmAuIElmIGBjb2xsZWN0aW9uYCBpcyBhIHN0cmluZywgaXQnc1xuICogY2hlY2tlZCBmb3IgYSBzdWJzdHJpbmcgb2YgYHZhbHVlYCwgb3RoZXJ3aXNlXG4gKiBbYFNhbWVWYWx1ZVplcm9gXShodHRwOi8vZWNtYS1pbnRlcm5hdGlvbmFsLm9yZy9lY21hLTI2Mi83LjAvI3NlYy1zYW1ldmFsdWV6ZXJvKVxuICogaXMgdXNlZCBmb3IgZXF1YWxpdHkgY29tcGFyaXNvbnMuIElmIGBmcm9tSW5kZXhgIGlzIG5lZ2F0aXZlLCBpdCdzIHVzZWQgYXNcbiAqIHRoZSBvZmZzZXQgZnJvbSB0aGUgZW5kIG9mIGBjb2xsZWN0aW9uYC5cbiAqXG4gKiBAc3RhdGljXG4gKiBAbWVtYmVyT2YgX1xuICogQHNpbmNlIDAuMS4wXG4gKiBAY2F0ZWdvcnkgQ29sbGVjdGlvblxuICogQHBhcmFtIHtBcnJheXxPYmplY3R8c3RyaW5nfSBjb2xsZWN0aW9uIFRoZSBjb2xsZWN0aW9uIHRvIGluc3BlY3QuXG4gKiBAcGFyYW0geyp9IHZhbHVlIFRoZSB2YWx1ZSB0byBzZWFyY2ggZm9yLlxuICogQHBhcmFtIHtudW1iZXJ9IFtmcm9tSW5kZXg9MF0gVGhlIGluZGV4IHRvIHNlYXJjaCBmcm9tLlxuICogQHBhcmFtLSB7T2JqZWN0fSBbZ3VhcmRdIEVuYWJsZXMgdXNlIGFzIGFuIGl0ZXJhdGVlIGZvciBtZXRob2RzIGxpa2UgYF8ucmVkdWNlYC5cbiAqIEByZXR1cm5zIHtib29sZWFufSBSZXR1cm5zIGB0cnVlYCBpZiBgdmFsdWVgIGlzIGZvdW5kLCBlbHNlIGBmYWxzZWAuXG4gKiBAZXhhbXBsZVxuICpcbiAqIF8uaW5jbHVkZXMoWzEsIDIsIDNdLCAxKTtcbiAqIC8vID0+IHRydWVcbiAqXG4gKiBfLmluY2x1ZGVzKFsxLCAyLCAzXSwgMSwgMik7XG4gKiAvLyA9PiBmYWxzZVxuICpcbiAqIF8uaW5jbHVkZXMoeyAnYSc6IDEsICdiJzogMiB9LCAxKTtcbiAqIC8vID0+IHRydWVcbiAqXG4gKiBfLmluY2x1ZGVzKCdhYmNkJywgJ2JjJyk7XG4gKiAvLyA9PiB0cnVlXG4gKi9cbmZ1bmN0aW9uIGluY2x1ZGVzKGNvbGxlY3Rpb24sIHZhbHVlLCBmcm9tSW5kZXgsIGd1YXJkKSB7XG4gIGNvbGxlY3Rpb24gPSBpc0FycmF5TGlrZShjb2xsZWN0aW9uKSA/IGNvbGxlY3Rpb24gOiB2YWx1ZXMoY29sbGVjdGlvbik7XG4gIGZyb21JbmRleCA9IChmcm9tSW5kZXggJiYgIWd1YXJkKSA/IHRvSW50ZWdlcihmcm9tSW5kZXgpIDogMDtcblxuICB2YXIgbGVuZ3RoID0gY29sbGVjdGlvbi5sZW5ndGg7XG4gIGlmIChmcm9tSW5kZXggPCAwKSB7XG4gICAgZnJvbUluZGV4ID0gbmF0aXZlTWF4KGxlbmd0aCArIGZyb21JbmRleCwgMCk7XG4gIH1cbiAgcmV0dXJuIGlzU3RyaW5nKGNvbGxlY3Rpb24pXG4gICAgPyAoZnJvbUluZGV4IDw9IGxlbmd0aCAmJiBjb2xsZWN0aW9uLmluZGV4T2YodmFsdWUsIGZyb21JbmRleCkgPiAtMSlcbiAgICA6ICghIWxlbmd0aCAmJiBiYXNlSW5kZXhPZihjb2xsZWN0aW9uLCB2YWx1ZSwgZnJvbUluZGV4KSA+IC0xKTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgaW5jbHVkZXM7XG4iLCJpbXBvcnQgeyBDb21wb25lbnRTdHlsZSwgRGl5Q29tcG9uZW50IH0gZnJvbSAnQC9jb21wb25lbnRzL0RpeUVkaXRvci91dGlsJ1xuXG4vKiog6L2u5pKt5Zu+5bGe5oCnICovXG5leHBvcnQgaW50ZXJmYWNlIENhcm91c2VsUHJvcGVydHkge1xuICAvLyDnsbvlnovvvJrpu5jorqQgfCDljaHniYdcbiAgdHlwZTogJ2RlZmF1bHQnIHwgJ2NhcmQnXG4gIC8vIOaMh+ekuuWZqOagt+W8j++8mueCuSB8IOaVsOWtl1xuICBpbmRpY2F0b3I6ICdkb3QnIHwgJ251bWJlcidcbiAgLy8g5piv5ZCm6Ieq5Yqo5pKt5pS+XG4gIGF1dG9wbGF5OiBib29sZWFuXG4gIC8vIOaSreaUvumXtOmalFxuICBpbnRlcnZhbDogbnVtYmVyXG4gIC8vIOi9ruaSreWGheWuuVxuICBpdGVtczogQ2Fyb3VzZWxJdGVtUHJvcGVydHlbXVxuICAvLyDnu4Tku7bmoLflvI9cbiAgc3R5bGU6IENvbXBvbmVudFN0eWxlXG59XG4vLyDova7mkq3lhoXlrrnlsZ7mgKdcbmV4cG9ydCBpbnRlcmZhY2UgQ2Fyb3VzZWxJdGVtUHJvcGVydHkge1xuICAvLyDnsbvlnovvvJrlm77niYcgfCDop4bpopFcbiAgdHlwZTogJ2ltZycgfCAndmlkZW8nXG4gIC8vIOWbvueJh+mTvuaOpVxuICBpbWdVcmw6IHN0cmluZ1xuICAvLyDop4bpopHpk77mjqVcbiAgdmlkZW9Vcmw6IHN0cmluZ1xuICAvLyDot7Povazpk77mjqVcbiAgdXJsOiBzdHJpbmdcbn1cblxuLy8g5a6a5LmJ57uE5Lu2XG5leHBvcnQgY29uc3QgY29tcG9uZW50ID0ge1xuICBpZDogJ0Nhcm91c2VsJyxcbiAgbmFtZTogJ+i9ruaSreWbvicsXG4gIGljb246ICdzeXN0ZW0tdWljb25zOmNhcm91c2VsJyxcbiAgcHJvcGVydHk6IHtcbiAgICB0eXBlOiAnZGVmYXVsdCcsXG4gICAgaW5kaWNhdG9yOiAnZG90JyxcbiAgICBhdXRvcGxheTogZmFsc2UsXG4gICAgaW50ZXJ2YWw6IDMsXG4gICAgaXRlbXM6IFtcbiAgICAgIHsgdHlwZTogJ2ltZycsIGltZ1VybDogJ2h0dHBzOi8vc3RhdGljLmlvY29kZXIuY24vbWFsbC9iYW5uZXItMDEuanBnJywgdmlkZW9Vcmw6ICcnIH0sXG4gICAgICB7IHR5cGU6ICdpbWcnLCBpbWdVcmw6ICdodHRwczovL3N0YXRpYy5pb2NvZGVyLmNuL21hbGwvYmFubmVyLTAyLmpwZycsIHZpZGVvVXJsOiAnJyB9XG4gICAgXSBhcyBDYXJvdXNlbEl0ZW1Qcm9wZXJ0eVtdLFxuICAgIHN0eWxlOiB7XG4gICAgICBiZ1R5cGU6ICdjb2xvcicsXG4gICAgICBiZ0NvbG9yOiAnI2ZmZicsXG4gICAgICBtYXJnaW5Cb3R0b206IDhcbiAgICB9IGFzIENvbXBvbmVudFN0eWxlXG4gIH1cbn0gYXMgRGl5Q29tcG9uZW50PENhcm91c2VsUHJvcGVydHk+XG4iLCJpbXBvcnQgeyBDb21wb25lbnRTdHlsZSwgRGl5Q29tcG9uZW50IH0gZnJvbSAnQC9jb21wb25lbnRzL0RpeUVkaXRvci91dGlsJ1xuXG4vKiog5ZWG5ZOB5Y2h54mH5bGe5oCnICovXG5leHBvcnQgaW50ZXJmYWNlIENvdXBvbkNhcmRQcm9wZXJ0eSB7XG4gIC8vIOWIl+aVsFxuICBjb2x1bW5zOiBudW1iZXJcbiAgLy8g6IOM5pmv5Zu+XG4gIGJnSW1nOiBzdHJpbmdcbiAgLy8g5paH5a2X6aKc6ImyXG4gIHRleHRDb2xvcjogc3RyaW5nXG4gIC8vIOaMiemSruagt+W8j1xuICBidXR0b246IHtcbiAgICAvLyDpopzoibJcbiAgICBjb2xvcjogc3RyaW5nXG4gICAgLy8g6IOM5pmv6aKc6ImyXG4gICAgYmdDb2xvcjogc3RyaW5nXG4gIH1cbiAgLy8g6Ze06LedXG4gIHNwYWNlOiBudW1iZXJcbiAgLy8g5LyY5oOg5Yi457yW5Y+35YiX6KGoXG4gIGNvdXBvbklkczogbnVtYmVyW11cbiAgLy8g57uE5Lu25qC35byPXG4gIHN0eWxlOiBDb21wb25lbnRTdHlsZVxufVxuXG4vLyDlrprkuYnnu4Tku7ZcbmV4cG9ydCBjb25zdCBjb21wb25lbnQgPSB7XG4gIGlkOiAnQ291cG9uQ2FyZCcsXG4gIG5hbWU6ICfkvJjmg6DliLgnLFxuICBpY29uOiAnZXA6dGlja2V0JyxcbiAgcHJvcGVydHk6IHtcbiAgICBjb2x1bW5zOiAxLFxuICAgIGJnSW1nOiAnJyxcbiAgICB0ZXh0Q29sb3I6ICcjRTlCNDYxJyxcbiAgICBidXR0b246IHtcbiAgICAgIGNvbG9yOiAnIzQzNDM0MycsXG4gICAgICBiZ0NvbG9yOiAnJ1xuICAgIH0sXG4gICAgc3BhY2U6IDAsXG4gICAgY291cG9uSWRzOiBbXSxcbiAgICBzdHlsZToge1xuICAgICAgYmdUeXBlOiAnY29sb3InLFxuICAgICAgYmdDb2xvcjogJycsXG4gICAgICBtYXJnaW5Cb3R0b206IDhcbiAgICB9IGFzIENvbXBvbmVudFN0eWxlXG4gIH1cbn0gYXMgRGl5Q29tcG9uZW50PENvdXBvbkNhcmRQcm9wZXJ0eT5cbiIsImltcG9ydCB7IERpeUNvbXBvbmVudCB9IGZyb20gJ0AvY29tcG9uZW50cy9EaXlFZGl0b3IvdXRpbCdcblxuLyoqIOWIhuWJsue6v+WxnuaApyAqL1xuZXhwb3J0IGludGVyZmFjZSBEaXZpZGVyUHJvcGVydHkge1xuICAvLyDpq5jluqZcbiAgaGVpZ2h0OiBudW1iZXJcbiAgLy8g57q/5a69XG4gIGxpbmVXaWR0aDogbnVtYmVyXG4gIC8vIOi+uei3neexu+Wei1xuICBwYWRkaW5nVHlwZTogJ25vbmUnIHwgJ2hvcml6b250YWwnXG4gIC8vIOminOiJslxuICBsaW5lQ29sb3I6IHN0cmluZ1xuICAvLyDnsbvlnotcbiAgYm9yZGVyVHlwZTogJ3NvbGlkJyB8ICdkYXNoZWQnIHwgJ2RvdHRlZCcgfCAnbm9uZSdcbn1cblxuLy8g5a6a5LmJ57uE5Lu2XG5leHBvcnQgY29uc3QgY29tcG9uZW50ID0ge1xuICBpZDogJ0RpdmlkZXInLFxuICBuYW1lOiAn5YiG5Ymy57q/JyxcbiAgaWNvbjogJ3RkZXNpZ246Y29tcG9uZW50LWRpdmlkZXItdmVydGljYWwnLFxuICBwcm9wZXJ0eToge1xuICAgIGhlaWdodDogMzAsXG4gICAgbGluZVdpZHRoOiAxLFxuICAgIHBhZGRpbmdUeXBlOiAnbm9uZScsXG4gICAgbGluZUNvbG9yOiAnI2RjZGZlNicsXG4gICAgYm9yZGVyVHlwZTogJ3NvbGlkJ1xuICB9XG59IGFzIERpeUNvbXBvbmVudDxEaXZpZGVyUHJvcGVydHk+XG4iLCJpbXBvcnQgeyBEaXlDb21wb25lbnQgfSBmcm9tICdAL2NvbXBvbmVudHMvRGl5RWRpdG9yL3V0aWwnXG5cbi8vIOaCrOa1ruaMiemSruWxnuaAp1xuZXhwb3J0IGludGVyZmFjZSBGbG9hdGluZ0FjdGlvbkJ1dHRvblByb3BlcnR5IHtcbiAgLy8g5bGV5byA5pa55ZCRXG4gIGRpcmVjdGlvbjogJ2hvcml6b250YWwnIHwgJ3ZlcnRpY2FsJ1xuICAvLyDmmK/lkKbmmL7npLrmloflrZdcbiAgc2hvd1RleHQ6IGJvb2xlYW5cbiAgLy8g5oyJ6ZKu5YiX6KGoXG4gIGxpc3Q6IEZsb2F0aW5nQWN0aW9uQnV0dG9uSXRlbVByb3BlcnR5W11cbn1cblxuLy8g5oKs5rWu5oyJ6ZKu6aG55bGe5oCnXG5leHBvcnQgaW50ZXJmYWNlIEZsb2F0aW5nQWN0aW9uQnV0dG9uSXRlbVByb3BlcnR5IHtcbiAgLy8g5Zu+54mH5Zyw5Z2AXG4gIGltZ1VybDogc3RyaW5nXG4gIC8vIOi3s+i9rOi/nuaOpVxuICB1cmw6IHN0cmluZ1xuICAvLyDmloflrZdcbiAgdGV4dDogc3RyaW5nXG4gIC8vIOaWh+Wtl+minOiJslxuICB0ZXh0Q29sb3I6IHN0cmluZ1xufVxuXG4vLyDlrprkuYnnu4Tku7ZcbmV4cG9ydCBjb25zdCBjb21wb25lbnQgPSB7XG4gIGlkOiAnRmxvYXRpbmdBY3Rpb25CdXR0b24nLFxuICBuYW1lOiAn5oKs5rWu5oyJ6ZKuJyxcbiAgaWNvbjogJ3RhYmxlcjpmbG9hdC1yaWdodCcsXG4gIHBvc2l0aW9uOiAnZml4ZWQnLFxuICBwcm9wZXJ0eToge1xuICAgIGRpcmVjdGlvbjogJ3ZlcnRpY2FsJyxcbiAgICBzaG93VGV4dDogdHJ1ZSxcbiAgICBsaXN0OiBbeyB0ZXh0Q29sb3I6ICcjZmZmJyB9XVxuICB9XG59IGFzIERpeUNvbXBvbmVudDxGbG9hdGluZ0FjdGlvbkJ1dHRvblByb3BlcnR5PlxuIiwiaW1wb3J0IHsgQ29tcG9uZW50U3R5bGUsIERpeUNvbXBvbmVudCB9IGZyb20gJ0AvY29tcG9uZW50cy9EaXlFZGl0b3IvdXRpbCdcblxuLyoqIOeDreWMuuWxnuaApyAqL1xuZXhwb3J0IGludGVyZmFjZSBIb3Rab25lUHJvcGVydHkge1xuICAvLyDlm77niYflnLDlnYBcbiAgaW1nVXJsOiBzdHJpbmdcbiAgLy8g5a+86Iiq6I+c5Y2V5YiX6KGoXG4gIGxpc3Q6IEhvdFpvbmVJdGVtUHJvcGVydHlbXVxuICAvLyDnu4Tku7bmoLflvI9cbiAgc3R5bGU6IENvbXBvbmVudFN0eWxlXG59XG5cbi8qKiDng63ljLrpobnnm67lsZ7mgKcgKi9cbmV4cG9ydCBpbnRlcmZhY2UgSG90Wm9uZUl0ZW1Qcm9wZXJ0eSB7XG4gIC8vIOmTvuaOpeeahOWQjeensFxuICBuYW1lOiBzdHJpbmdcbiAgLy8g6ZO+5o6lXG4gIHVybDogc3RyaW5nXG4gIC8vIOWuvVxuICB3aWR0aDogbnVtYmVyXG4gIC8vIOmrmFxuICBoZWlnaHQ6IG51bWJlclxuICAvLyDkuIpcbiAgdG9wOiBudW1iZXJcbiAgLy8g5bemXG4gIGxlZnQ6IG51bWJlclxufVxuXG4vLyDlrprkuYnnu4Tku7ZcbmV4cG9ydCBjb25zdCBjb21wb25lbnQgPSB7XG4gIGlkOiAnSG90Wm9uZScsXG4gIG5hbWU6ICfng63ljLonLFxuICBpY29uOiAndGFibGVyOmhhbmQtY2xpY2snLFxuICBwcm9wZXJ0eToge1xuICAgIGltZ1VybDogJycsXG4gICAgbGlzdDogW10gYXMgSG90Wm9uZUl0ZW1Qcm9wZXJ0eVtdLFxuICAgIHN0eWxlOiB7XG4gICAgICBiZ1R5cGU6ICdjb2xvcicsXG4gICAgICBiZ0NvbG9yOiAnI2ZmZicsXG4gICAgICBtYXJnaW5Cb3R0b206IDhcbiAgICB9IGFzIENvbXBvbmVudFN0eWxlXG4gIH1cbn0gYXMgRGl5Q29tcG9uZW50PEhvdFpvbmVQcm9wZXJ0eT5cbiIsImltcG9ydCB7IENvbXBvbmVudFN0eWxlLCBEaXlDb21wb25lbnQgfSBmcm9tICdAL2NvbXBvbmVudHMvRGl5RWRpdG9yL3V0aWwnXG5cbi8qKiDlm77niYflsZXnpLrlsZ7mgKcgKi9cbmV4cG9ydCBpbnRlcmZhY2UgSW1hZ2VCYXJQcm9wZXJ0eSB7XG4gIC8vIOWbvueJh+mTvuaOpVxuICBpbWdVcmw6IHN0cmluZ1xuICAvLyDot7Povazpk77mjqVcbiAgdXJsOiBzdHJpbmdcbiAgLy8g57uE5Lu25qC35byPXG4gIHN0eWxlOiBDb21wb25lbnRTdHlsZVxufVxuXG4vLyDlrprkuYnnu4Tku7ZcbmV4cG9ydCBjb25zdCBjb21wb25lbnQgPSB7XG4gIGlkOiAnSW1hZ2VCYXInLFxuICBuYW1lOiAn5Zu+54mH5bGV56S6JyxcbiAgaWNvbjogJ2VwOnBpY3R1cmUnLFxuICBwcm9wZXJ0eToge1xuICAgIGltZ1VybDogJycsXG4gICAgdXJsOiAnJyxcbiAgICBzdHlsZToge1xuICAgICAgYmdUeXBlOiAnY29sb3InLFxuICAgICAgYmdDb2xvcjogJyNmZmYnLFxuICAgICAgbWFyZ2luQm90dG9tOiA4XG4gICAgfSBhcyBDb21wb25lbnRTdHlsZVxuICB9XG59IGFzIERpeUNvbXBvbmVudDxJbWFnZUJhclByb3BlcnR5PlxuIiwiaW1wb3J0IHsgQ29tcG9uZW50U3R5bGUsIERpeUNvbXBvbmVudCB9IGZyb20gJ0AvY29tcG9uZW50cy9EaXlFZGl0b3IvdXRpbCdcblxuLyoqIOW5v+WRiumtlOaWueWxnuaApyAqL1xuZXhwb3J0IGludGVyZmFjZSBNYWdpY0N1YmVQcm9wZXJ0eSB7XG4gIC8vIOS4iuWchuinklxuICBib3JkZXJSYWRpdXNUb3A6IG51bWJlclxuICAvLyDkuIvlnIbop5JcbiAgYm9yZGVyUmFkaXVzQm90dG9tOiBudW1iZXJcbiAgLy8g6Ze06ZqUXG4gIHNwYWNlOiBudW1iZXJcbiAgLy8g5a+86Iiq6I+c5Y2V5YiX6KGoXG4gIGxpc3Q6IE1hZ2ljQ3ViZUl0ZW1Qcm9wZXJ0eVtdXG4gIC8vIOe7hOS7tuagt+W8j1xuICBzdHlsZTogQ29tcG9uZW50U3R5bGVcbn1cblxuLyoqIOW5v+WRiumtlOaWuemhueebruWxnuaApyAqL1xuZXhwb3J0IGludGVyZmFjZSBNYWdpY0N1YmVJdGVtUHJvcGVydHkge1xuICAvLyDlm77moIfpk77mjqVcbiAgaW1nVXJsOiBzdHJpbmdcbiAgLy8g6ZO+5o6lXG4gIHVybDogc3RyaW5nXG4gIC8vIOWuvVxuICB3aWR0aDogbnVtYmVyXG4gIC8vIOmrmFxuICBoZWlnaHQ6IG51bWJlclxuICAvLyDkuIpcbiAgdG9wOiBudW1iZXJcbiAgLy8g5bemXG4gIGxlZnQ6IG51bWJlclxufVxuXG4vLyDlrprkuYnnu4Tku7ZcbmV4cG9ydCBjb25zdCBjb21wb25lbnQgPSB7XG4gIGlkOiAnTWFnaWNDdWJlJyxcbiAgbmFtZTogJ+W5v+WRiumtlOaWuScsXG4gIGljb246ICdiaTpjb2x1bW5zJyxcbiAgcHJvcGVydHk6IHtcbiAgICBib3JkZXJSYWRpdXNUb3A6IDAsXG4gICAgYm9yZGVyUmFkaXVzQm90dG9tOiAwLFxuICAgIHNwYWNlOiAwLFxuICAgIGxpc3Q6IFtdLFxuICAgIHN0eWxlOiB7XG4gICAgICBiZ1R5cGU6ICdjb2xvcicsXG4gICAgICBiZ0NvbG9yOiAnI2ZmZicsXG4gICAgICBtYXJnaW5Cb3R0b206IDhcbiAgICB9IGFzIENvbXBvbmVudFN0eWxlXG4gIH1cbn0gYXMgRGl5Q29tcG9uZW50PE1hZ2ljQ3ViZVByb3BlcnR5PlxuIiwiaW1wb3J0IHsgQ29tcG9uZW50U3R5bGUsIERpeUNvbXBvbmVudCB9IGZyb20gJ0AvY29tcG9uZW50cy9EaXlFZGl0b3IvdXRpbCdcbmltcG9ydCB7IGNsb25lRGVlcCB9IGZyb20gJ2xvZGFzaC1lcydcblxuLyoqIOWuq+agvOWvvOiIquWxnuaApyAqL1xuZXhwb3J0IGludGVyZmFjZSBNZW51R3JpZFByb3BlcnR5IHtcbiAgLy8g5YiX5pWwXG4gIGNvbHVtbjogbnVtYmVyXG4gIC8vIOWvvOiIquiPnOWNleWIl+ihqFxuICBsaXN0OiBNZW51R3JpZEl0ZW1Qcm9wZXJ0eVtdXG4gIC8vIOe7hOS7tuagt+W8j1xuICBzdHlsZTogQ29tcG9uZW50U3R5bGVcbn1cblxuLyoqIOWuq+agvOWvvOiIqumhueebruWxnuaApyAqL1xuZXhwb3J0IGludGVyZmFjZSBNZW51R3JpZEl0ZW1Qcm9wZXJ0eSB7XG4gIC8vIOWbvuagh+mTvuaOpVxuICBpY29uVXJsOiBzdHJpbmdcbiAgLy8g5qCH6aKYXG4gIHRpdGxlOiBzdHJpbmdcbiAgLy8g5qCH6aKY6aKc6ImyXG4gIHRpdGxlQ29sb3I6IHN0cmluZ1xuICAvLyDlia/moIfpophcbiAgc3VidGl0bGU6IHN0cmluZ1xuICAvLyDlia/moIfpopjpopzoibJcbiAgc3VidGl0bGVDb2xvcjogc3RyaW5nXG4gIC8vIOmTvuaOpVxuICB1cmw6IHN0cmluZ1xuICAvLyDop5LmoIdcbiAgYmFkZ2U6IHtcbiAgICAvLyDmmK/lkKbmmL7npLpcbiAgICBzaG93OiBib29sZWFuXG4gICAgLy8g6KeS5qCH5paH5a2XXG4gICAgdGV4dDogc3RyaW5nXG4gICAgLy8g6KeS5qCH5paH5a2X6aKc6ImyXG4gICAgdGV4dENvbG9yOiBzdHJpbmdcbiAgICAvLyDop5LmoIfog4zmma/popzoibJcbiAgICBiZ0NvbG9yOiBzdHJpbmdcbiAgfVxufVxuXG5leHBvcnQgY29uc3QgRU1QVFlfTUVOVV9HUklEX0lURU1fUFJPUEVSVFkgPSB7XG4gIHRpdGxlOiAn5qCH6aKYJyxcbiAgdGl0bGVDb2xvcjogJyMzMzMnLFxuICBzdWJ0aXRsZTogJ+WJr+agh+mimCcsXG4gIHN1YnRpdGxlQ29sb3I6ICcjYmJiJyxcbiAgYmFkZ2U6IHtcbiAgICBzaG93OiBmYWxzZSxcbiAgICB0ZXh0Q29sb3I6ICcjZmZmJyxcbiAgICBiZ0NvbG9yOiAnI0ZGNjAwMCdcbiAgfVxufSBhcyBNZW51R3JpZEl0ZW1Qcm9wZXJ0eVxuXG4vLyDlrprkuYnnu4Tku7ZcbmV4cG9ydCBjb25zdCBjb21wb25lbnQgPSB7XG4gIGlkOiAnTWVudUdyaWQnLFxuICBuYW1lOiAn5a6r5qC85a+86IiqJyxcbiAgaWNvbjogJ2JpOmdyaWQtM3gzLWdhcCcsXG4gIHByb3BlcnR5OiB7XG4gICAgY29sdW1uOiAzLFxuICAgIGxpc3Q6IFtjbG9uZURlZXAoRU1QVFlfTUVOVV9HUklEX0lURU1fUFJPUEVSVFkpXSxcbiAgICBzdHlsZToge1xuICAgICAgYmdUeXBlOiAnY29sb3InLFxuICAgICAgYmdDb2xvcjogJyNmZmYnLFxuICAgICAgbWFyZ2luQm90dG9tOiA4LFxuICAgICAgbWFyZ2luTGVmdDogOCxcbiAgICAgIG1hcmdpblJpZ2h0OiA4LFxuICAgICAgcGFkZGluZzogOCxcbiAgICAgIHBhZGRpbmdUb3A6IDgsXG4gICAgICBwYWRkaW5nUmlnaHQ6IDgsXG4gICAgICBwYWRkaW5nQm90dG9tOiA4LFxuICAgICAgcGFkZGluZ0xlZnQ6IDgsXG4gICAgICBib3JkZXJSYWRpdXM6IDgsXG4gICAgICBib3JkZXJUb3BMZWZ0UmFkaXVzOiA4LFxuICAgICAgYm9yZGVyVG9wUmlnaHRSYWRpdXM6IDgsXG4gICAgICBib3JkZXJCb3R0b21SaWdodFJhZGl1czogOCxcbiAgICAgIGJvcmRlckJvdHRvbUxlZnRSYWRpdXM6IDhcbiAgICB9IGFzIENvbXBvbmVudFN0eWxlXG4gIH1cbn0gYXMgRGl5Q29tcG9uZW50PE1lbnVHcmlkUHJvcGVydHk+XG4iLCJpbXBvcnQgeyBDb21wb25lbnRTdHlsZSwgRGl5Q29tcG9uZW50IH0gZnJvbSAnQC9jb21wb25lbnRzL0RpeUVkaXRvci91dGlsJ1xuaW1wb3J0IHsgY2xvbmVEZWVwIH0gZnJvbSAnbG9kYXNoLWVzJ1xuXG4vKiog5YiX6KGo5a+86Iiq5bGe5oCnICovXG5leHBvcnQgaW50ZXJmYWNlIE1lbnVMaXN0UHJvcGVydHkge1xuICAvLyDlr7zoiKroj5zljZXliJfooahcbiAgbGlzdDogTWVudUxpc3RJdGVtUHJvcGVydHlbXVxuICAvLyDnu4Tku7bmoLflvI9cbiAgc3R5bGU6IENvbXBvbmVudFN0eWxlXG59XG5cbi8qKiDliJfooajlr7zoiKrpobnnm67lsZ7mgKcgKi9cbmV4cG9ydCBpbnRlcmZhY2UgTWVudUxpc3RJdGVtUHJvcGVydHkge1xuICAvLyDlm77moIfpk77mjqVcbiAgaWNvblVybDogc3RyaW5nXG4gIC8vIOagh+mimFxuICB0aXRsZTogc3RyaW5nXG4gIC8vIOagh+mimOminOiJslxuICB0aXRsZUNvbG9yOiBzdHJpbmdcbiAgLy8g5Ymv5qCH6aKYXG4gIHN1YnRpdGxlOiBzdHJpbmdcbiAgLy8g5Ymv5qCH6aKY6aKc6ImyXG4gIHN1YnRpdGxlQ29sb3I6IHN0cmluZ1xuICAvLyDpk77mjqVcbiAgdXJsOiBzdHJpbmdcbn1cblxuZXhwb3J0IGNvbnN0IEVNUFRZX01FTlVfTElTVF9JVEVNX1BST1BFUlRZID0ge1xuICB0aXRsZTogJ+agh+mimCcsXG4gIHRpdGxlQ29sb3I6ICcjMzMzJyxcbiAgc3VidGl0bGU6ICflia/moIfpopgnLFxuICBzdWJ0aXRsZUNvbG9yOiAnI2JiYidcbn1cblxuLy8g5a6a5LmJ57uE5Lu2XG5leHBvcnQgY29uc3QgY29tcG9uZW50ID0ge1xuICBpZDogJ01lbnVMaXN0JyxcbiAgbmFtZTogJ+WIl+ihqOWvvOiIqicsXG4gIGljb246ICdmYS1zb2xpZDpsaXN0JyxcbiAgcHJvcGVydHk6IHtcbiAgICBsaXN0OiBbY2xvbmVEZWVwKEVNUFRZX01FTlVfTElTVF9JVEVNX1BST1BFUlRZKV0sXG4gICAgc3R5bGU6IHtcbiAgICAgIGJnVHlwZTogJ2NvbG9yJyxcbiAgICAgIGJnQ29sb3I6ICcjZmZmJyxcbiAgICAgIG1hcmdpbkJvdHRvbTogOFxuICAgIH0gYXMgQ29tcG9uZW50U3R5bGVcbiAgfVxufSBhcyBEaXlDb21wb25lbnQ8TWVudUxpc3RQcm9wZXJ0eT5cbiIsImltcG9ydCB7IENvbXBvbmVudFN0eWxlLCBEaXlDb21wb25lbnQgfSBmcm9tICdAL2NvbXBvbmVudHMvRGl5RWRpdG9yL3V0aWwnXG5pbXBvcnQgeyBjbG9uZURlZXAgfSBmcm9tICdsb2Rhc2gtZXMnXG5cbi8qKiDoj5zljZXlr7zoiKrlsZ7mgKcgKi9cbmV4cG9ydCBpbnRlcmZhY2UgTWVudVN3aXBlclByb3BlcnR5IHtcbiAgLy8g5biD5bGA77yaIOWbvuaghyvmloflrZcgfCDlm77moIdcbiAgbGF5b3V0OiAnaWNvblRleHQnIHwgJ2ljb24nXG4gIC8vIOihjOaVsFxuICByb3c6IG51bWJlclxuICAvLyDliJfmlbBcbiAgY29sdW1uOiBudW1iZXJcbiAgLy8g5a+86Iiq6I+c5Y2V5YiX6KGoXG4gIGxpc3Q6IE1lbnVTd2lwZXJJdGVtUHJvcGVydHlbXVxuICAvLyDnu4Tku7bmoLflvI9cbiAgc3R5bGU6IENvbXBvbmVudFN0eWxlXG59XG4vKiog6I+c5Y2V5a+86Iiq6aG555uu5bGe5oCnICovXG5leHBvcnQgaW50ZXJmYWNlIE1lbnVTd2lwZXJJdGVtUHJvcGVydHkge1xuICAvLyDlm77moIfpk77mjqVcbiAgaWNvblVybDogc3RyaW5nXG4gIC8vIOagh+mimFxuICB0aXRsZTogc3RyaW5nXG4gIC8vIOagh+mimOminOiJslxuICB0aXRsZUNvbG9yOiBzdHJpbmdcbiAgLy8g6ZO+5o6lXG4gIHVybDogc3RyaW5nXG4gIC8vIOinkuagh1xuICBiYWRnZToge1xuICAgIC8vIOaYr+WQpuaYvuekulxuICAgIHNob3c6IGJvb2xlYW5cbiAgICAvLyDop5LmoIfmloflrZdcbiAgICB0ZXh0OiBzdHJpbmdcbiAgICAvLyDop5LmoIfmloflrZfpopzoibJcbiAgICB0ZXh0Q29sb3I6IHN0cmluZ1xuICAgIC8vIOinkuagh+iDjOaZr+minOiJslxuICAgIGJnQ29sb3I6IHN0cmluZ1xuICB9XG59XG5cbmV4cG9ydCBjb25zdCBFTVBUWV9NRU5VX1NXSVBFUl9JVEVNX1BST1BFUlRZID0ge1xuICB0aXRsZTogJ+agh+mimCcsXG4gIHRpdGxlQ29sb3I6ICcjMzMzJyxcbiAgYmFkZ2U6IHtcbiAgICBzaG93OiBmYWxzZSxcbiAgICB0ZXh0Q29sb3I6ICcjZmZmJyxcbiAgICBiZ0NvbG9yOiAnI0ZGNjAwMCdcbiAgfVxufSBhcyBNZW51U3dpcGVySXRlbVByb3BlcnR5XG5cbi8vIOWumuS5iee7hOS7tlxuZXhwb3J0IGNvbnN0IGNvbXBvbmVudCA9IHtcbiAgaWQ6ICdNZW51U3dpcGVyJyxcbiAgbmFtZTogJ+iPnOWNleWvvOiIqicsXG4gIGljb246ICdiaTpncmlkLTN4Mi1nYXAnLFxuICBwcm9wZXJ0eToge1xuICAgIGxheW91dDogJ2ljb25UZXh0JyxcbiAgICByb3c6IDEsXG4gICAgY29sdW1uOiAzLFxuICAgIGxpc3Q6IFtjbG9uZURlZXAoRU1QVFlfTUVOVV9TV0lQRVJfSVRFTV9QUk9QRVJUWSldLFxuICAgIHN0eWxlOiB7XG4gICAgICBiZ1R5cGU6ICdjb2xvcicsXG4gICAgICBiZ0NvbG9yOiAnI2ZmZicsXG4gICAgICBtYXJnaW5Cb3R0b206IDhcbiAgICB9IGFzIENvbXBvbmVudFN0eWxlXG4gIH1cbn0gYXMgRGl5Q29tcG9uZW50PE1lbnVTd2lwZXJQcm9wZXJ0eT5cbiIsImltcG9ydCB7IERpeUNvbXBvbmVudCB9IGZyb20gJ0AvY29tcG9uZW50cy9EaXlFZGl0b3IvdXRpbCdcblxuLyoqIOmhtumDqOWvvOiIquagj+WxnuaApyAqL1xuZXhwb3J0IGludGVyZmFjZSBOYXZpZ2F0aW9uQmFyUHJvcGVydHkge1xuICAvLyDog4zmma/nsbvlnotcbiAgYmdUeXBlOiAnY29sb3InIHwgJ2ltZydcbiAgLy8g6IOM5pmv6aKc6ImyXG4gIGJnQ29sb3I6IHN0cmluZ1xuICAvLyDlm77niYfpk77mjqVcbiAgYmdJbWc6IHN0cmluZ1xuICAvLyDmoLflvI/nsbvlnovvvJrpu5jorqQgfCDmsonmtbjlvI9cbiAgc3R5bGVUeXBlOiAnbm9ybWFsJyB8ICdpbm5lcidcbiAgLy8g5bi46am75pi+56S6XG4gIGFsd2F5c1Nob3c6IGJvb2xlYW5cbiAgLy8g5bCP56iL5bqP5Y2V5YWD5qC85YiX6KGoXG4gIG1wQ2VsbHM6IE5hdmlnYXRpb25CYXJDZWxsUHJvcGVydHlbXVxuICAvLyDlhbblroPlubPlj7DljZXlhYPmoLzliJfooahcbiAgb3RoZXJDZWxsczogTmF2aWdhdGlvbkJhckNlbGxQcm9wZXJ0eVtdXG4gIC8vIOacrOWcsOWPmOmHj1xuICBfbG9jYWw6IHtcbiAgICAvLyDpooTop4jpobbpg6jlr7zoiKrvvIjlsI/nqIvluo/vvIlcbiAgICBwcmV2aWV3TXA6IGJvb2xlYW5cbiAgICAvLyDpooTop4jpobbpg6jlr7zoiKrvvIjpnZ7lsI/nqIvluo/vvIlcbiAgICBwcmV2aWV3T3RoZXI6IGJvb2xlYW5cbiAgfVxufVxuXG4vKiog6aG26YOo5a+86Iiq5qCPIC0g5Y2V5YWD5qC8IOWxnuaApyAqL1xuZXhwb3J0IGludGVyZmFjZSBOYXZpZ2F0aW9uQmFyQ2VsbFByb3BlcnR5IHtcbiAgLy8g57G75Z6L77ya5paH5a2XIHwg5Zu+54mHIHwg5pCc57Si5qGGXG4gIHR5cGU6ICd0ZXh0JyB8ICdpbWFnZScgfCAnc2VhcmNoJ1xuICAvLyDlrr3luqZcbiAgd2lkdGg6IG51bWJlclxuICAvLyDpq5jluqZcbiAgaGVpZ2h0OiBudW1iZXJcbiAgLy8g6aG26YOo5L2N572uXG4gIHRvcDogbnVtYmVyXG4gIC8vIOW3puS+p+S9jee9rlxuICBsZWZ0OiBudW1iZXJcbiAgLy8g5paH5a2X5YaF5a65XG4gIHRleHQ6IHN0cmluZ1xuICAvLyDmloflrZfpopzoibJcbiAgdGV4dENvbG9yOiBzdHJpbmdcbiAgLy8g5Zu+54mH5Zyw5Z2AXG4gIGltZ1VybDogc3RyaW5nXG4gIC8vIOWbvueJh+mTvuaOpVxuICB1cmw6IHN0cmluZ1xuICAvLyDmkJzntKLmoYbvvJrmj5DnpLrmloflrZdcbiAgcGxhY2Vob2xkZXI6IHN0cmluZ1xuICAvLyDmkJzntKLmoYbvvJrovrnmoYblnIbop5LljYrlvoRcbiAgYm9yZGVyUmFkaXVzOiBudW1iZXJcbn1cblxuLy8g5a6a5LmJ57uE5Lu2XG5leHBvcnQgY29uc3QgY29tcG9uZW50ID0ge1xuICBpZDogJ05hdmlnYXRpb25CYXInLFxuICBuYW1lOiAn6aG26YOo5a+86Iiq5qCPJyxcbiAgaWNvbjogJ3RhYmxlcjpsYXlvdXQtbmF2YmFyJyxcbiAgcHJvcGVydHk6IHtcbiAgICBiZ1R5cGU6ICdjb2xvcicsXG4gICAgYmdDb2xvcjogJyNmZmYnLFxuICAgIGJnSW1nOiAnJyxcbiAgICBzdHlsZVR5cGU6ICdub3JtYWwnLFxuICAgIGFsd2F5c1Nob3c6IHRydWUsXG4gICAgbXBDZWxsczogW1xuICAgICAge1xuICAgICAgICB0eXBlOiAndGV4dCcsXG4gICAgICAgIHRleHRDb2xvcjogJyMxMTExMTEnXG4gICAgICB9XG4gICAgXSxcbiAgICBvdGhlckNlbGxzOiBbXG4gICAgICB7XG4gICAgICAgIHR5cGU6ICd0ZXh0JyxcbiAgICAgICAgdGV4dENvbG9yOiAnIzExMTExMSdcbiAgICAgIH1cbiAgICBdLFxuICAgIF9sb2NhbDoge1xuICAgICAgcHJldmlld01wOiB0cnVlLFxuICAgICAgcHJldmlld090aGVyOiBmYWxzZVxuICAgIH1cbiAgfVxufSBhcyBEaXlDb21wb25lbnQ8TmF2aWdhdGlvbkJhclByb3BlcnR5PlxuIiwiaW1wb3J0IHsgQ29tcG9uZW50U3R5bGUsIERpeUNvbXBvbmVudCB9IGZyb20gJ0AvY29tcG9uZW50cy9EaXlFZGl0b3IvdXRpbCdcblxuLyoqIOWFrOWRiuagj+WxnuaApyAqL1xuZXhwb3J0IGludGVyZmFjZSBOb3RpY2VCYXJQcm9wZXJ0eSB7XG4gIC8vIOWbvuagh+WcsOWdgFxuICBpY29uVXJsOiBzdHJpbmdcbiAgLy8g5YWs5ZGK5YaF5a655YiX6KGoXG4gIGNvbnRlbnRzOiBOb3RpY2VDb250ZW50UHJvcGVydHlbXVxuICAvLyDog4zmma/popzoibJcbiAgYmFja2dyb3VuZENvbG9yOiBzdHJpbmdcbiAgLy8g5paH5a2X6aKc6ImyXG4gIHRleHRDb2xvcjogc3RyaW5nXG4gIC8vIOe7hOS7tuagt+W8j1xuICBzdHlsZTogQ29tcG9uZW50U3R5bGVcbn1cblxuLyoqIOWGheWuueWxnuaApyAqL1xuZXhwb3J0IGludGVyZmFjZSBOb3RpY2VDb250ZW50UHJvcGVydHkge1xuICAvLyDlhoXlrrnmloflrZdcbiAgdGV4dDogc3RyaW5nXG4gIC8vIOmTvuaOpeWcsOWdgFxuICB1cmw6IHN0cmluZ1xufVxuXG4vLyDlrprkuYnnu4Tku7ZcbmV4cG9ydCBjb25zdCBjb21wb25lbnQgPSB7XG4gIGlkOiAnTm90aWNlQmFyJyxcbiAgbmFtZTogJ+WFrOWRiuagjycsXG4gIGljb246ICdlcDpiZWxsJyxcbiAgcHJvcGVydHk6IHtcbiAgICBpY29uVXJsOiAnaHR0cDovL21hbGwueXVkYW8uaW9jb2Rlci5jbi9zdGF0aWMvaW1hZ2VzL3hpbmppYW4ucG5nJyxcbiAgICBjb250ZW50czogW1xuICAgICAge1xuICAgICAgICB0ZXh0OiAnJyxcbiAgICAgICAgdXJsOiAnJ1xuICAgICAgfVxuICAgIF0sXG4gICAgYmFja2dyb3VuZENvbG9yOiAnI2ZmZicsXG4gICAgdGV4dENvbG9yOiAnIzMzMycsXG4gICAgc3R5bGU6IHtcbiAgICAgIGJnVHlwZTogJ2NvbG9yJyxcbiAgICAgIGJnQ29sb3I6ICcjZmZmJyxcbiAgICAgIG1hcmdpbkJvdHRvbTogOFxuICAgIH0gYXMgQ29tcG9uZW50U3R5bGVcbiAgfVxufSBhcyBEaXlDb21wb25lbnQ8Tm90aWNlQmFyUHJvcGVydHk+XG4iLCJpbXBvcnQgeyBEaXlDb21wb25lbnQgfSBmcm9tICdAL2NvbXBvbmVudHMvRGl5RWRpdG9yL3V0aWwnXG5cbi8qKiDpobXpnaLorr7nva7lsZ7mgKcgKi9cbmV4cG9ydCBpbnRlcmZhY2UgUGFnZUNvbmZpZ1Byb3BlcnR5IHtcbiAgLy8g6aG16Z2i5o+P6L+wXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmdcbiAgLy8g6aG16Z2i6IOM5pmv6aKc6ImyXG4gIGJhY2tncm91bmRDb2xvcjogc3RyaW5nXG4gIC8vIOmhtemdouiDjOaZr+WbvueJh1xuICBiYWNrZ3JvdW5kSW1hZ2U6IHN0cmluZ1xufVxuXG4vLyDlrprkuYnpobXpnaLnu4Tku7ZcbmV4cG9ydCBjb25zdCBjb21wb25lbnQgPSB7XG4gIGlkOiAnUGFnZUNvbmZpZycsXG4gIG5hbWU6ICfpobXpnaLorr7nva4nLFxuICBpY29uOiAnZXA6ZG9jdW1lbnQnLFxuICBwcm9wZXJ0eToge1xuICAgIGRlc2NyaXB0aW9uOiAnJyxcbiAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjZjVmNWY1JyxcbiAgICBiYWNrZ3JvdW5kSW1hZ2U6ICcnXG4gIH1cbn0gYXMgRGl5Q29tcG9uZW50PFBhZ2VDb25maWdQcm9wZXJ0eT5cbiIsImltcG9ydCB7IERpeUNvbXBvbmVudCB9IGZyb20gJ0AvY29tcG9uZW50cy9EaXlFZGl0b3IvdXRpbCdcblxuLyoqIOW8ueeql+W5v+WRiuWxnuaApyAqL1xuZXhwb3J0IGludGVyZmFjZSBQb3BvdmVyUHJvcGVydHkge1xuICBsaXN0OiBQb3BvdmVySXRlbVByb3BlcnR5W11cbn1cblxuZXhwb3J0IGludGVyZmFjZSBQb3BvdmVySXRlbVByb3BlcnR5IHtcbiAgLy8g5Zu+54mH5Zyw5Z2AXG4gIGltZ1VybDogc3RyaW5nXG4gIC8vIOi3s+i9rOi/nuaOpVxuICB1cmw6IHN0cmluZ1xuICAvLyDmmL7npLrnsbvlnovvvJrku4XmmL7npLrkuIDmrKHjgIHmr4/mrKHlkK/liqjpg73kvJrmmL7npLpcbiAgc2hvd1R5cGU6ICdvbmNlJyB8ICdhbHdheXMnXG59XG5cbi8vIOWumuS5iee7hOS7tlxuZXhwb3J0IGNvbnN0IGNvbXBvbmVudCA9IHtcbiAgaWQ6ICdQb3BvdmVyJyxcbiAgbmFtZTogJ+W8ueeql+W5v+WRiicsXG4gIGljb246ICdjYXJib246cG9wdXAnLFxuICBwb3NpdGlvbjogJ2ZpeGVkJyxcbiAgcHJvcGVydHk6IHtcbiAgICBsaXN0OiBbeyBzaG93VHlwZTogJ29uY2UnIH1dXG4gIH1cbn0gYXMgRGl5Q29tcG9uZW50PFBvcG92ZXJQcm9wZXJ0eT5cbiIsImltcG9ydCB7IENvbXBvbmVudFN0eWxlLCBEaXlDb21wb25lbnQgfSBmcm9tICdAL2NvbXBvbmVudHMvRGl5RWRpdG9yL3V0aWwnXG5cbi8qKiDllYblk4HljaHniYflsZ7mgKcgKi9cbmV4cG9ydCBpbnRlcmZhY2UgUHJvZHVjdENhcmRQcm9wZXJ0eSB7XG4gIC8vIOW4g+WxgOexu+Wei++8muWNleWIl+Wkp+WbviB8IOWNleWIl+Wwj+WbviB8IOWPjOWIl1xuICBsYXlvdXRUeXBlOiAnb25lQ29sQmlnSW1nJyB8ICdvbmVDb2xTbWFsbEltZycgfCAndHdvQ29sJ1xuICAvLyDllYblk4HlrZfmrrVcbiAgZmllbGRzOiB7XG4gICAgLy8g5ZWG5ZOB5ZCN56ewXG4gICAgbmFtZTogUHJvZHVjdENhcmRGaWVsZFByb3BlcnR5XG4gICAgLy8g5ZWG5ZOB566A5LuLXG4gICAgaW50cm9kdWN0aW9uOiBQcm9kdWN0Q2FyZEZpZWxkUHJvcGVydHlcbiAgICAvLyDllYblk4Hku7fmoLxcbiAgICBwcmljZTogUHJvZHVjdENhcmRGaWVsZFByb3BlcnR5XG4gICAgLy8g5ZWG5ZOB5biC5Zy65Lu3XG4gICAgbWFya2V0UHJpY2U6IFByb2R1Y3RDYXJkRmllbGRQcm9wZXJ0eVxuICAgIC8vIOWVhuWTgemUgOmHj1xuICAgIHNhbGVzQ291bnQ6IFByb2R1Y3RDYXJkRmllbGRQcm9wZXJ0eVxuICAgIC8vIOWVhuWTgeW6k+WtmFxuICAgIHN0b2NrOiBQcm9kdWN0Q2FyZEZpZWxkUHJvcGVydHlcbiAgfVxuICAvLyDop5LmoIdcbiAgYmFkZ2U6IHtcbiAgICAvLyDmmK/lkKbmmL7npLpcbiAgICBzaG93OiBib29sZWFuXG4gICAgLy8g6KeS5qCH5Zu+54mHXG4gICAgaW1nVXJsOiBzdHJpbmdcbiAgfVxuICAvLyDmjInpkq5cbiAgYnRuQnV5OiB7XG4gICAgLy8g57G75Z6L77ya5paH5a2XIHwg5Zu+54mHXG4gICAgdHlwZTogJ3RleHQnIHwgJ2ltZydcbiAgICAvLyDmloflrZdcbiAgICB0ZXh0OiBzdHJpbmdcbiAgICAvLyDmloflrZfmjInpkq7vvJrog4zmma/muJDlj5jotbflp4vpopzoibJcbiAgICBiZ0JlZ2luQ29sb3I6IHN0cmluZ1xuICAgIC8vIOaWh+Wtl+aMiemSru+8muiDjOaZr+a4kOWPmOe7k+adn+minOiJslxuICAgIGJnRW5kQ29sb3I6IHN0cmluZ1xuICAgIC8vIOWbvueJh+aMiemSru+8muWbvueJh+WcsOWdgFxuICAgIGltZ1VybDogc3RyaW5nXG4gIH1cbiAgLy8g5LiK5ZyG6KeSXG4gIGJvcmRlclJhZGl1c1RvcDogbnVtYmVyXG4gIC8vIOS4i+WchuinklxuICBib3JkZXJSYWRpdXNCb3R0b206IG51bWJlclxuICAvLyDpl7Tot51cbiAgc3BhY2U6IG51bWJlclxuICAvLyDllYblk4HnvJblj7fliJfooahcbiAgc3B1SWRzOiBudW1iZXJbXVxuICAvLyDnu4Tku7bmoLflvI9cbiAgc3R5bGU6IENvbXBvbmVudFN0eWxlXG59XG4vLyDllYblk4HlrZfmrrVcbmV4cG9ydCBpbnRlcmZhY2UgUHJvZHVjdENhcmRGaWVsZFByb3BlcnR5IHtcbiAgLy8g5piv5ZCm5pi+56S6XG4gIHNob3c6IGJvb2xlYW5cbiAgLy8g6aKc6ImyXG4gIGNvbG9yOiBzdHJpbmdcbn1cblxuLy8g5a6a5LmJ57uE5Lu2XG5leHBvcnQgY29uc3QgY29tcG9uZW50ID0ge1xuICBpZDogJ1Byb2R1Y3RDYXJkJyxcbiAgbmFtZTogJ+WVhuWTgeWNoeeJhycsXG4gIGljb246ICdmbHVlbnQ6dGV4dC1jb2x1bW4tdHdvLWxlZnQtMjQtZmlsbGVkJyxcbiAgcHJvcGVydHk6IHtcbiAgICBsYXlvdXRUeXBlOiAnb25lQ29sQmlnSW1nJyxcbiAgICBmaWVsZHM6IHtcbiAgICAgIG5hbWU6IHsgc2hvdzogdHJ1ZSwgY29sb3I6ICcjMDAwJyB9LFxuICAgICAgaW50cm9kdWN0aW9uOiB7IHNob3c6IHRydWUsIGNvbG9yOiAnIzk5OScgfSxcbiAgICAgIHByaWNlOiB7IHNob3c6IHRydWUsIGNvbG9yOiAnI2ZmMzAwMCcgfSxcbiAgICAgIG1hcmtldFByaWNlOiB7IHNob3c6IHRydWUsIGNvbG9yOiAnI2M0YzRjNCcgfSxcbiAgICAgIHNhbGVzQ291bnQ6IHsgc2hvdzogdHJ1ZSwgY29sb3I6ICcjYzRjNGM0JyB9LFxuICAgICAgc3RvY2s6IHsgc2hvdzogZmFsc2UsIGNvbG9yOiAnI2M0YzRjNCcgfVxuICAgIH0sXG4gICAgYmFkZ2U6IHsgc2hvdzogZmFsc2UsIGltZ1VybDogJycgfSxcbiAgICBidG5CdXk6IHtcbiAgICAgIHR5cGU6ICd0ZXh0JyxcbiAgICAgIHRleHQ6ICfnq4vljbPotK3kubAnLFxuICAgICAgLy8gdG9kbzogQG93ZW4g5qC55o2u5Li76aKY6Imy6YWN572uXG4gICAgICBiZ0JlZ2luQ29sb3I6ICcjRkY2MDAwJyxcbiAgICAgIGJnRW5kQ29sb3I6ICcjRkU4MzJBJyxcbiAgICAgIGltZ1VybDogJydcbiAgICB9LFxuICAgIGJvcmRlclJhZGl1c1RvcDogOCxcbiAgICBib3JkZXJSYWRpdXNCb3R0b206IDgsXG4gICAgc3BhY2U6IDgsXG4gICAgc3B1SWRzOiBbXSxcbiAgICBzdHlsZToge1xuICAgICAgYmdUeXBlOiAnY29sb3InLFxuICAgICAgYmdDb2xvcjogJycsXG4gICAgICBtYXJnaW5MZWZ0OiA4LFxuICAgICAgbWFyZ2luUmlnaHQ6IDgsXG4gICAgICBtYXJnaW5Cb3R0b206IDhcbiAgICB9IGFzIENvbXBvbmVudFN0eWxlXG4gIH1cbn0gYXMgRGl5Q29tcG9uZW50PFByb2R1Y3RDYXJkUHJvcGVydHk+XG4iLCJpbXBvcnQgeyBDb21wb25lbnRTdHlsZSwgRGl5Q29tcG9uZW50IH0gZnJvbSAnQC9jb21wb25lbnRzL0RpeUVkaXRvci91dGlsJ1xuXG4vKiog5ZWG5ZOB5qCP5bGe5oCnICovXG5leHBvcnQgaW50ZXJmYWNlIFByb2R1Y3RMaXN0UHJvcGVydHkge1xuICAvLyDluIPlsYDnsbvlnovvvJrlj4zliJcgfCDkuInliJcgfCDmsLTlubPmu5HliqhcbiAgbGF5b3V0VHlwZTogJ3R3b0NvbCcgfCAndGhyZWVDb2wnIHwgJ2hvcml6U3dpcGVyJ1xuICAvLyDllYblk4HlrZfmrrVcbiAgZmllbGRzOiB7XG4gICAgLy8g5ZWG5ZOB5ZCN56ewXG4gICAgbmFtZTogUHJvZHVjdExpc3RGaWVsZFByb3BlcnR5XG4gICAgLy8g5ZWG5ZOB5Lu35qC8XG4gICAgcHJpY2U6IFByb2R1Y3RMaXN0RmllbGRQcm9wZXJ0eVxuICB9XG4gIC8vIOinkuagh1xuICBiYWRnZToge1xuICAgIC8vIOaYr+WQpuaYvuekulxuICAgIHNob3c6IGJvb2xlYW5cbiAgICAvLyDop5LmoIflm77niYdcbiAgICBpbWdVcmw6IHN0cmluZ1xuICB9XG4gIC8vIOS4iuWchuinklxuICBib3JkZXJSYWRpdXNUb3A6IG51bWJlclxuICAvLyDkuIvlnIbop5JcbiAgYm9yZGVyUmFkaXVzQm90dG9tOiBudW1iZXJcbiAgLy8g6Ze06LedXG4gIHNwYWNlOiBudW1iZXJcbiAgLy8g5ZWG5ZOB57yW5Y+35YiX6KGoXG4gIHNwdUlkczogbnVtYmVyW11cbiAgLy8g57uE5Lu25qC35byPXG4gIHN0eWxlOiBDb21wb25lbnRTdHlsZVxufVxuLy8g5ZWG5ZOB5a2X5q61XG5leHBvcnQgaW50ZXJmYWNlIFByb2R1Y3RMaXN0RmllbGRQcm9wZXJ0eSB7XG4gIC8vIOaYr+WQpuaYvuekulxuICBzaG93OiBib29sZWFuXG4gIC8vIOminOiJslxuICBjb2xvcjogc3RyaW5nXG59XG5cbi8vIOWumuS5iee7hOS7tlxuZXhwb3J0IGNvbnN0IGNvbXBvbmVudCA9IHtcbiAgaWQ6ICdQcm9kdWN0TGlzdCcsXG4gIG5hbWU6ICfllYblk4HmoI8nLFxuICBpY29uOiAnZmx1ZW50OnRleHQtY29sdW1uLXR3by0yNC1maWxsZWQnLFxuICBwcm9wZXJ0eToge1xuICAgIGxheW91dFR5cGU6ICd0d29Db2wnLFxuICAgIGZpZWxkczoge1xuICAgICAgbmFtZTogeyBzaG93OiB0cnVlLCBjb2xvcjogJyMwMDAnIH0sXG4gICAgICBwcmljZTogeyBzaG93OiB0cnVlLCBjb2xvcjogJyNmZjMwMDAnIH1cbiAgICB9LFxuICAgIGJhZGdlOiB7IHNob3c6IGZhbHNlLCBpbWdVcmw6ICcnIH0sXG4gICAgYm9yZGVyUmFkaXVzVG9wOiA4LFxuICAgIGJvcmRlclJhZGl1c0JvdHRvbTogOCxcbiAgICBzcGFjZTogOCxcbiAgICBzcHVJZHM6IFtdLFxuICAgIHN0eWxlOiB7XG4gICAgICBiZ1R5cGU6ICdjb2xvcicsXG4gICAgICBiZ0NvbG9yOiAnJyxcbiAgICAgIG1hcmdpbkxlZnQ6IDgsXG4gICAgICBtYXJnaW5SaWdodDogOCxcbiAgICAgIG1hcmdpbkJvdHRvbTogOFxuICAgIH0gYXMgQ29tcG9uZW50U3R5bGVcbiAgfVxufSBhcyBEaXlDb21wb25lbnQ8UHJvZHVjdExpc3RQcm9wZXJ0eT5cbiIsImltcG9ydCB7IENvbXBvbmVudFN0eWxlLCBEaXlDb21wb25lbnQgfSBmcm9tICdAL2NvbXBvbmVudHMvRGl5RWRpdG9yL3V0aWwnXG5cbi8qKiDokKXplIDmlofnq6DlsZ7mgKcgKi9cbmV4cG9ydCBpbnRlcmZhY2UgUHJvbW90aW9uQXJ0aWNsZVByb3BlcnR5IHtcbiAgLy8g5paH56ug57yW5Y+3XG4gIGlkOiBudW1iZXJcbiAgLy8g57uE5Lu25qC35byPXG4gIHN0eWxlOiBDb21wb25lbnRTdHlsZVxufVxuXG4vLyDlrprkuYnnu4Tku7ZcbmV4cG9ydCBjb25zdCBjb21wb25lbnQgPSB7XG4gIGlkOiAnUHJvbW90aW9uQXJ0aWNsZScsXG4gIG5hbWU6ICfokKXplIDmlofnq6AnLFxuICBpY29uOiAncGg6YXJ0aWNsZS1tZWRpdW0nLFxuICBwcm9wZXJ0eToge1xuICAgIHN0eWxlOiB7XG4gICAgICBiZ1R5cGU6ICdjb2xvcicsXG4gICAgICBiZ0NvbG9yOiAnJyxcbiAgICAgIG1hcmdpbkxlZnQ6IDgsXG4gICAgICBtYXJnaW5SaWdodDogOCxcbiAgICAgIG1hcmdpbkJvdHRvbTogOFxuICAgIH0gYXMgQ29tcG9uZW50U3R5bGVcbiAgfVxufSBhcyBEaXlDb21wb25lbnQ8UHJvbW90aW9uQXJ0aWNsZVByb3BlcnR5PlxuIiwiaW1wb3J0IHsgQ29tcG9uZW50U3R5bGUsIERpeUNvbXBvbmVudCB9IGZyb20gJ0AvY29tcG9uZW50cy9EaXlFZGl0b3IvdXRpbCdcblxuLyoqIOaLvOWbouWxnuaApyAqL1xuZXhwb3J0IGludGVyZmFjZSBQcm9tb3Rpb25Db21iaW5hdGlvblByb3BlcnR5IHtcbiAgLy8g5biD5bGA57G75Z6L77ya5Y2V5YiXIHwg5LiJ5YiXXG4gIGxheW91dFR5cGU6ICdvbmVDb2wnIHwgJ3RocmVlQ29sJ1xuICAvLyDllYblk4HlrZfmrrVcbiAgZmllbGRzOiB7XG4gICAgLy8g5ZWG5ZOB5ZCN56ewXG4gICAgbmFtZTogUHJvbW90aW9uQ29tYmluYXRpb25GaWVsZFByb3BlcnR5XG4gICAgLy8g5ZWG5ZOB5Lu35qC8XG4gICAgcHJpY2U6IFByb21vdGlvbkNvbWJpbmF0aW9uRmllbGRQcm9wZXJ0eVxuICB9XG4gIC8vIOinkuagh1xuICBiYWRnZToge1xuICAgIC8vIOaYr+WQpuaYvuekulxuICAgIHNob3c6IGJvb2xlYW5cbiAgICAvLyDop5LmoIflm77niYdcbiAgICBpbWdVcmw6IHN0cmluZ1xuICB9XG4gIC8vIOS4iuWchuinklxuICBib3JkZXJSYWRpdXNUb3A6IG51bWJlclxuICAvLyDkuIvlnIbop5JcbiAgYm9yZGVyUmFkaXVzQm90dG9tOiBudW1iZXJcbiAgLy8g6Ze06LedXG4gIHNwYWNlOiBudW1iZXJcbiAgLy8g5ou85Zui5rS75Yqo57yW5Y+3XG4gIGFjdGl2aXR5SWQ6IG51bWJlclxuICAvLyDnu4Tku7bmoLflvI9cbiAgc3R5bGU6IENvbXBvbmVudFN0eWxlXG59XG5cbi8vIOWVhuWTgeWtl+autVxuZXhwb3J0IGludGVyZmFjZSBQcm9tb3Rpb25Db21iaW5hdGlvbkZpZWxkUHJvcGVydHkge1xuICAvLyDmmK/lkKbmmL7npLpcbiAgc2hvdzogYm9vbGVhblxuICAvLyDpopzoibJcbiAgY29sb3I6IHN0cmluZ1xufVxuXG4vLyDlrprkuYnnu4Tku7ZcbmV4cG9ydCBjb25zdCBjb21wb25lbnQgPSB7XG4gIGlkOiAnUHJvbW90aW9uQ29tYmluYXRpb24nLFxuICBuYW1lOiAn5ou85ZuiJyxcbiAgaWNvbjogJ21kaTphY2NvdW50LWdyb3VwJyxcbiAgcHJvcGVydHk6IHtcbiAgICBsYXlvdXRUeXBlOiAnb25lQ29sJyxcbiAgICBmaWVsZHM6IHtcbiAgICAgIG5hbWU6IHsgc2hvdzogdHJ1ZSwgY29sb3I6ICcjMDAwJyB9LFxuICAgICAgcHJpY2U6IHsgc2hvdzogdHJ1ZSwgY29sb3I6ICcjZmYzMDAwJyB9XG4gICAgfSxcbiAgICBiYWRnZTogeyBzaG93OiBmYWxzZSwgaW1nVXJsOiAnJyB9LFxuICAgIGJvcmRlclJhZGl1c1RvcDogOCxcbiAgICBib3JkZXJSYWRpdXNCb3R0b206IDgsXG4gICAgc3BhY2U6IDgsXG4gICAgc3R5bGU6IHtcbiAgICAgIGJnVHlwZTogJ2NvbG9yJyxcbiAgICAgIGJnQ29sb3I6ICcnLFxuICAgICAgbWFyZ2luTGVmdDogOCxcbiAgICAgIG1hcmdpblJpZ2h0OiA4LFxuICAgICAgbWFyZ2luQm90dG9tOiA4XG4gICAgfSBhcyBDb21wb25lbnRTdHlsZVxuICB9XG59IGFzIERpeUNvbXBvbmVudDxQcm9tb3Rpb25Db21iaW5hdGlvblByb3BlcnR5PlxuIiwiaW1wb3J0IHsgQ29tcG9uZW50U3R5bGUsIERpeUNvbXBvbmVudCB9IGZyb20gJ0AvY29tcG9uZW50cy9EaXlFZGl0b3IvdXRpbCdcblxuLyoqIOenkuadgOWxnuaApyAqL1xuZXhwb3J0IGludGVyZmFjZSBQcm9tb3Rpb25TZWNraWxsUHJvcGVydHkge1xuICAvLyDluIPlsYDnsbvlnovvvJrljZXliJcgfCDkuInliJdcbiAgbGF5b3V0VHlwZTogJ29uZUNvbCcgfCAndGhyZWVDb2wnXG4gIC8vIOWVhuWTgeWtl+autVxuICBmaWVsZHM6IHtcbiAgICAvLyDllYblk4HlkI3np7BcbiAgICBuYW1lOiBQcm9tb3Rpb25TZWNraWxsRmllbGRQcm9wZXJ0eVxuICAgIC8vIOWVhuWTgeS7t+agvFxuICAgIHByaWNlOiBQcm9tb3Rpb25TZWNraWxsRmllbGRQcm9wZXJ0eVxuICB9XG4gIC8vIOinkuagh1xuICBiYWRnZToge1xuICAgIC8vIOaYr+WQpuaYvuekulxuICAgIHNob3c6IGJvb2xlYW5cbiAgICAvLyDop5LmoIflm77niYdcbiAgICBpbWdVcmw6IHN0cmluZ1xuICB9XG4gIC8vIOS4iuWchuinklxuICBib3JkZXJSYWRpdXNUb3A6IG51bWJlclxuICAvLyDkuIvlnIbop5JcbiAgYm9yZGVyUmFkaXVzQm90dG9tOiBudW1iZXJcbiAgLy8g6Ze06LedXG4gIHNwYWNlOiBudW1iZXJcbiAgLy8g56eS5p2A5rS75Yqo57yW5Y+3XG4gIGFjdGl2aXR5SWQ6IG51bWJlclxuICAvLyDnu4Tku7bmoLflvI9cbiAgc3R5bGU6IENvbXBvbmVudFN0eWxlXG59XG4vLyDllYblk4HlrZfmrrVcbmV4cG9ydCBpbnRlcmZhY2UgUHJvbW90aW9uU2Vja2lsbEZpZWxkUHJvcGVydHkge1xuICAvLyDmmK/lkKbmmL7npLpcbiAgc2hvdzogYm9vbGVhblxuICAvLyDpopzoibJcbiAgY29sb3I6IHN0cmluZ1xufVxuXG4vLyDlrprkuYnnu4Tku7ZcbmV4cG9ydCBjb25zdCBjb21wb25lbnQgPSB7XG4gIGlkOiAnUHJvbW90aW9uU2Vja2lsbCcsXG4gIG5hbWU6ICfnp5LmnYAnLFxuICBpY29uOiAnbWRpOmNhbGVuZGFyLXRpbWUnLFxuICBwcm9wZXJ0eToge1xuICAgIGFjdGl2aXR5SWQ6IHVuZGVmaW5lZCxcbiAgICBsYXlvdXRUeXBlOiAnb25lQ29sJyxcbiAgICBmaWVsZHM6IHtcbiAgICAgIG5hbWU6IHsgc2hvdzogdHJ1ZSwgY29sb3I6ICcjMDAwJyB9LFxuICAgICAgcHJpY2U6IHsgc2hvdzogdHJ1ZSwgY29sb3I6ICcjZmYzMDAwJyB9XG4gICAgfSxcbiAgICBiYWRnZTogeyBzaG93OiBmYWxzZSwgaW1nVXJsOiAnJyB9LFxuICAgIGJvcmRlclJhZGl1c1RvcDogOCxcbiAgICBib3JkZXJSYWRpdXNCb3R0b206IDgsXG4gICAgc3BhY2U6IDgsXG4gICAgc3R5bGU6IHtcbiAgICAgIGJnVHlwZTogJ2NvbG9yJyxcbiAgICAgIGJnQ29sb3I6ICcnLFxuICAgICAgbWFyZ2luTGVmdDogOCxcbiAgICAgIG1hcmdpblJpZ2h0OiA4LFxuICAgICAgbWFyZ2luQm90dG9tOiA4XG4gICAgfSBhcyBDb21wb25lbnRTdHlsZVxuICB9XG59IGFzIERpeUNvbXBvbmVudDxQcm9tb3Rpb25TZWNraWxsUHJvcGVydHk+XG4iLCJpbXBvcnQgeyBDb21wb25lbnRTdHlsZSwgRGl5Q29tcG9uZW50IH0gZnJvbSAnQC9jb21wb25lbnRzL0RpeUVkaXRvci91dGlsJ1xuXG4vKiog5pCc57Si5qGG5bGe5oCnICovXG5leHBvcnQgaW50ZXJmYWNlIFNlYXJjaFByb3BlcnR5IHtcbiAgaGVpZ2h0OiBudW1iZXIgLy8g5pCc57Si5qCP6auY5bqmXG4gIHNob3dTY2FuOiBib29sZWFuIC8vIOaYvuekuuaJq+S4gOaJq1xuICBib3JkZXJSYWRpdXM6IG51bWJlciAvLyDmoYbkvZPmoLflvI9cbiAgcGxhY2Vob2xkZXI6IHN0cmluZyAvLyDljaDkvY3mloflrZdcbiAgcGxhY2Vob2xkZXJQb3NpdGlvbjogUGxhY2Vob2xkZXJQb3NpdGlvbiAvLyDljaDkvY3mloflrZfkvY3nva5cbiAgYmFja2dyb3VuZENvbG9yOiBzdHJpbmcgLy8g5qGG5L2T6aKc6ImyXG4gIHRleHRDb2xvcjogc3RyaW5nIC8vIOWtl+S9k+minOiJslxuICBob3RLZXl3b3Jkczogc3RyaW5nW10gLy8g54Ot6K+NXG4gIHN0eWxlOiBDb21wb25lbnRTdHlsZVxufVxuXG4vLyDmloflrZfkvY3nva5cbmV4cG9ydCB0eXBlIFBsYWNlaG9sZGVyUG9zaXRpb24gPSAnbGVmdCcgfCAnY2VudGVyJ1xuXG4vLyDlrprkuYnnu4Tku7ZcbmV4cG9ydCBjb25zdCBjb21wb25lbnQgPSB7XG4gIGlkOiAnU2VhcmNoQmFyJyxcbiAgbmFtZTogJ+aQnOe0ouahhicsXG4gIGljb246ICdlcDpzZWFyY2gnLFxuICBwcm9wZXJ0eToge1xuICAgIGhlaWdodDogMjgsXG4gICAgc2hvd1NjYW46IGZhbHNlLFxuICAgIGJvcmRlclJhZGl1czogMCxcbiAgICBwbGFjZWhvbGRlcjogJ+aQnOe0ouWVhuWTgScsXG4gICAgcGxhY2Vob2xkZXJQb3NpdGlvbjogJ2xlZnQnLFxuICAgIGJhY2tncm91bmRDb2xvcjogJ3JnYigyMzgsIDIzOCwgMjM4KScsXG4gICAgdGV4dENvbG9yOiAncmdiKDE1MCwgMTUxLCAxNTMpJyxcbiAgICBob3RLZXl3b3JkczogW10sXG4gICAgc3R5bGU6IHtcbiAgICAgIGJnVHlwZTogJ2NvbG9yJyxcbiAgICAgIGJnQ29sb3I6ICcjZmZmJyxcbiAgICAgIG1hcmdpbkJvdHRvbTogOCxcbiAgICAgIHBhZGRpbmdUb3A6IDgsXG4gICAgICBwYWRkaW5nUmlnaHQ6IDgsXG4gICAgICBwYWRkaW5nQm90dG9tOiA4LFxuICAgICAgcGFkZGluZ0xlZnQ6IDhcbiAgICB9IGFzIENvbXBvbmVudFN0eWxlXG4gIH1cbn0gYXMgRGl5Q29tcG9uZW50PFNlYXJjaFByb3BlcnR5PlxuIiwiaW1wb3J0IHsgRGl5Q29tcG9uZW50IH0gZnJvbSAnQC9jb21wb25lbnRzL0RpeUVkaXRvci91dGlsJ1xuXG4vKiog5bqV6YOo5a+86Iiq6I+c5Y2V5bGe5oCnICovXG5leHBvcnQgaW50ZXJmYWNlIFRhYkJhclByb3BlcnR5IHtcbiAgLy8g6YCJ6aG55YiX6KGoXG4gIGl0ZW1zOiBUYWJCYXJJdGVtUHJvcGVydHlbXVxuICAvLyDkuLvpophcbiAgdGhlbWU6IHN0cmluZ1xuICAvLyDmoLflvI9cbiAgc3R5bGU6IFRhYkJhclN0eWxlXG59XG5cbi8vIOmAiemhueWxnuaAp1xuZXhwb3J0IGludGVyZmFjZSBUYWJCYXJJdGVtUHJvcGVydHkge1xuICAvLyDmoIfnrb7mloflrZdcbiAgdGV4dDogc3RyaW5nXG4gIC8vIOmTvuaOpVxuICB1cmw6IHN0cmluZ1xuICAvLyDpu5jorqTlm77moIfpk77mjqVcbiAgaWNvblVybDogc3RyaW5nXG4gIC8vIOmAieS4reeahOWbvuagh+mTvuaOpVxuICBhY3RpdmVJY29uVXJsOiBzdHJpbmdcbn1cblxuLy8g5qC35byPXG5leHBvcnQgaW50ZXJmYWNlIFRhYkJhclN0eWxlIHtcbiAgLy8g6IOM5pmv57G75Z6LXG4gIGJnVHlwZTogJ2NvbG9yJyB8ICdpbWcnXG4gIC8vIOiDjOaZr+minOiJslxuICBiZ0NvbG9yOiBzdHJpbmdcbiAgLy8g5Zu+54mH6ZO+5o6lXG4gIGJnSW1nOiBzdHJpbmdcbiAgLy8g6buY6K6k6aKc6ImyXG4gIGNvbG9yOiBzdHJpbmdcbiAgLy8g6YCJ5Lit55qE6aKc6ImyXG4gIGFjdGl2ZUNvbG9yOiBzdHJpbmdcbn1cblxuLy8g5a6a5LmJ57uE5Lu2XG5leHBvcnQgY29uc3QgY29tcG9uZW50ID0ge1xuICBpZDogJ1RhYkJhcicsXG4gIG5hbWU6ICflupXpg6jlr7zoiKonLFxuICBpY29uOiAnZmx1ZW50OnRhYmxlLWJvdHRvbS1yb3ctMTYtZmlsbGVkJyxcbiAgcHJvcGVydHk6IHtcbiAgICB0aGVtZTogJ3JlZCcsXG4gICAgc3R5bGU6IHtcbiAgICAgIGJnVHlwZTogJ2NvbG9yJyxcbiAgICAgIGJnQ29sb3I6ICcjZmZmJyxcbiAgICAgIGNvbG9yOiAnIzI4MjgyOCcsXG4gICAgICBhY3RpdmVDb2xvcjogJyNmYzQxNDEnXG4gICAgfSxcbiAgICBpdGVtczogW1xuICAgICAge1xuICAgICAgICB0ZXh0OiAn6aaW6aG1JyxcbiAgICAgICAgdXJsOiAnL3BhZ2VzL2luZGV4L2luZGV4JyxcbiAgICAgICAgaWNvblVybDogJ2h0dHA6Ly9tYWxsLnl1ZGFvLmlvY29kZXIuY24vc3RhdGljL2ltYWdlcy8xLTAwMS5wbmcnLFxuICAgICAgICBhY3RpdmVJY29uVXJsOiAnaHR0cDovL21hbGwueXVkYW8uaW9jb2Rlci5jbi9zdGF0aWMvaW1hZ2VzLzEtMDAyLnBuZydcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIHRleHQ6ICfliIbnsbsnLFxuICAgICAgICB1cmw6ICcvcGFnZXMvaW5kZXgvY2F0ZWdvcnk/aWQ9MycsXG4gICAgICAgIGljb25Vcmw6ICdodHRwOi8vbWFsbC55dWRhby5pb2NvZGVyLmNuL3N0YXRpYy9pbWFnZXMvMi0wMDEucG5nJyxcbiAgICAgICAgYWN0aXZlSWNvblVybDogJ2h0dHA6Ly9tYWxsLnl1ZGFvLmlvY29kZXIuY24vc3RhdGljL2ltYWdlcy8yLTAwMi5wbmcnXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICB0ZXh0OiAn6LSt54mp6L2mJyxcbiAgICAgICAgdXJsOiAnL3BhZ2VzL2luZGV4L2NhcnQnLFxuICAgICAgICBpY29uVXJsOiAnaHR0cDovL21hbGwueXVkYW8uaW9jb2Rlci5jbi9zdGF0aWMvaW1hZ2VzLzMtMDAxLnBuZycsXG4gICAgICAgIGFjdGl2ZUljb25Vcmw6ICdodHRwOi8vbWFsbC55dWRhby5pb2NvZGVyLmNuL3N0YXRpYy9pbWFnZXMvMy0wMDIucG5nJ1xuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgdGV4dDogJ+aIkeeahCcsXG4gICAgICAgIHVybDogJy9wYWdlcy9pbmRleC91c2VyJyxcbiAgICAgICAgaWNvblVybDogJ2h0dHA6Ly9tYWxsLnl1ZGFvLmlvY29kZXIuY24vc3RhdGljL2ltYWdlcy80LTAwMS5wbmcnLFxuICAgICAgICBhY3RpdmVJY29uVXJsOiAnaHR0cDovL21hbGwueXVkYW8uaW9jb2Rlci5jbi9zdGF0aWMvaW1hZ2VzLzQtMDAyLnBuZydcbiAgICAgIH1cbiAgICBdXG4gIH1cbn0gYXMgRGl5Q29tcG9uZW50PFRhYkJhclByb3BlcnR5PlxuXG5leHBvcnQgY29uc3QgVEhFTUVfTElTVCA9IFtcbiAgeyBpZDogJ3JlZCcsIG5hbWU6ICfkuK3lm73nuqInLCBpY29uOiAnaWNvbi1wYXJrLXR3b3RvbmU6dGhlbWUnLCBjb2xvcjogJyNkMTAwMTknIH0sXG4gIHsgaWQ6ICdvcmFuZ2UnLCBuYW1lOiAn5qGU5qmZJywgaWNvbjogJ2ljb24tcGFyay10d290b25lOnRoZW1lJywgY29sb3I6ICcjZjM3YjFkJyB9LFxuICB7IGlkOiAnZ29sZCcsIG5hbWU6ICfmmI7pu4QnLCBpY29uOiAnaWNvbi1wYXJrLXR3b3RvbmU6dGhlbWUnLCBjb2xvcjogJyNmYmJkMDgnIH0sXG4gIHsgaWQ6ICdncmVlbicsIG5hbWU6ICfmqYTmpoTnu78nLCBpY29uOiAnaWNvbi1wYXJrLXR3b3RvbmU6dGhlbWUnLCBjb2xvcjogJyM4ZGM2M2YnIH0sXG4gIHsgaWQ6ICdjeWFuJywgbmFtZTogJ+WkqemdkicsIGljb246ICdpY29uLXBhcmstdHdvdG9uZTp0aGVtZScsIGNvbG9yOiAnIzFjYmJiNCcgfSxcbiAgeyBpZDogJ2JsdWUnLCBuYW1lOiAn5rW36JOdJywgaWNvbjogJ2ljb24tcGFyay10d290b25lOnRoZW1lJywgY29sb3I6ICcjMDA4MWZmJyB9LFxuICB7IGlkOiAncHVycGxlJywgbmFtZTogJ+Wnuee0qycsIGljb246ICdpY29uLXBhcmstdHdvdG9uZTp0aGVtZScsIGNvbG9yOiAnIzY3MzliNicgfSxcbiAgeyBpZDogJ2JyaWdodFJlZCcsIG5hbWU6ICflq6PnuqInLCBpY29uOiAnaWNvbi1wYXJrLXR3b3RvbmU6dGhlbWUnLCBjb2xvcjogJyNlNTRkNDInIH0sXG4gIHsgaWQ6ICdmb3Jlc3RHcmVlbicsIG5hbWU6ICfmo67nu78nLCBpY29uOiAnaWNvbi1wYXJrLXR3b3RvbmU6dGhlbWUnLCBjb2xvcjogJyMzOWI1NGEnIH0sXG4gIHsgaWQ6ICdtYXV2ZScsIG5hbWU6ICfmnKjmp78nLCBpY29uOiAnaWNvbi1wYXJrLXR3b3RvbmU6dGhlbWUnLCBjb2xvcjogJyM5YzI2YjAnIH0sXG4gIHsgaWQ6ICdwaW5rJywgbmFtZTogJ+ahg+eyiScsIGljb246ICdpY29uLXBhcmstdHdvdG9uZTp0aGVtZScsIGNvbG9yOiAnI2UwMzk5NycgfSxcbiAgeyBpZDogJ2Jyb3duJywgbmFtZTogJ+ajleikkCcsIGljb246ICdpY29uLXBhcmstdHdvdG9uZTp0aGVtZScsIGNvbG9yOiAnI2E1NjczZicgfSxcbiAgeyBpZDogJ2dyZXknLCBuYW1lOiAn546E54GwJywgaWNvbjogJ2ljb24tcGFyay10d290b25lOnRoZW1lJywgY29sb3I6ICcjODc5OWEzJyB9LFxuICB7IGlkOiAnZ3JheScsIG5hbWU6ICfojYnngbAnLCBpY29uOiAnaWNvbi1wYXJrLXR3b3RvbmU6dGhlbWUnLCBjb2xvcjogJyNhYWFhYWEnIH0sXG4gIHsgaWQ6ICdibGFjaycsIG5hbWU6ICfloqjpu5EnLCBpY29uOiAnaWNvbi1wYXJrLXR3b3RvbmU6dGhlbWUnLCBjb2xvcjogJyMzMzMzMzMnIH1cbl1cbiIsImltcG9ydCB7IENvbXBvbmVudFN0eWxlLCBEaXlDb21wb25lbnQgfSBmcm9tICdAL2NvbXBvbmVudHMvRGl5RWRpdG9yL3V0aWwnXG5cbi8qKiDmoIfpopjmoI/lsZ7mgKcgKi9cbmV4cG9ydCBpbnRlcmZhY2UgVGl0bGVCYXJQcm9wZXJ0eSB7XG4gIC8vIOiDjOaZr+WbvlxuICBiZ0ltZ1VybDogc3RyaW5nXG4gIC8vIOWBj+enu1xuICBtYXJnaW5MZWZ0OiBudW1iZXJcbiAgLy8g5pi+56S65L2N572uXG4gIHRleHRBbGlnbjogJ2xlZnQnIHwgJ2NlbnRlcidcbiAgLy8g5Li75qCH6aKYXG4gIHRpdGxlOiBzdHJpbmdcbiAgLy8g5Ymv5qCH6aKYXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmdcbiAgLy8g5qCH6aKY5aSn5bCPXG4gIHRpdGxlU2l6ZTogbnVtYmVyXG4gIC8vIOaPj+i/sOWkp+Wwj1xuICBkZXNjcmlwdGlvblNpemU6IG51bWJlclxuICAvLyDmoIfpopjnspfnu4ZcbiAgdGl0bGVXZWlnaHQ6IG51bWJlclxuICAvLyDmj4/ov7Dnspfnu4ZcbiAgZGVzY3JpcHRpb25XZWlnaHQ6IG51bWJlclxuICAvLyDmoIfpopjpopzoibJcbiAgdGl0bGVDb2xvcjogc3RyaW5nXG4gIC8vIOaPj+i/sOminOiJslxuICBkZXNjcmlwdGlvbkNvbG9yOiBzdHJpbmdcbiAgLy8g5p+l55yL5pu05aSaXG4gIG1vcmU6IHtcbiAgICAvLyDmmK/lkKbmmL7npLrmn6XnnIvmm7TlpJpcbiAgICBzaG93OiBmYWxzZVxuICAgIC8vIOagt+W8j+mAieaLqVxuICAgIHR5cGU6ICd0ZXh0JyB8ICdpY29uJyB8ICdhbGwnXG4gICAgLy8g6Ieq5a6a5LmJ5paH5a2XXG4gICAgdGV4dDogc3RyaW5nXG4gICAgLy8g6ZO+5o6lXG4gICAgdXJsOiBzdHJpbmdcbiAgfVxuICAvLyDnu4Tku7bmoLflvI9cbiAgc3R5bGU6IENvbXBvbmVudFN0eWxlXG59XG5cbi8vIOWumuS5iee7hOS7tlxuZXhwb3J0IGNvbnN0IGNvbXBvbmVudCA9IHtcbiAgaWQ6ICdUaXRsZUJhcicsXG4gIG5hbWU6ICfmoIfpopjmoI8nLFxuICBpY29uOiAnbWF0ZXJpYWwtc3ltYm9sczpsaW5lLXN0YXJ0JyxcbiAgcHJvcGVydHk6IHtcbiAgICB0aXRsZTogJ+S4u+agh+mimCcsXG4gICAgZGVzY3JpcHRpb246ICflia/moIfpopgnLFxuICAgIHRpdGxlU2l6ZTogMTYsXG4gICAgZGVzY3JpcHRpb25TaXplOiAxMixcbiAgICB0aXRsZVdlaWdodDogNDAwLFxuICAgIHRleHRBbGlnbjogJ2xlZnQnLFxuICAgIGRlc2NyaXB0aW9uV2VpZ2h0OiAyMDAsXG4gICAgdGl0bGVDb2xvcjogJ3JnYmEoNTAsIDUwLCA1MSwgMTApJyxcbiAgICBkZXNjcmlwdGlvbkNvbG9yOiAncmdiYSgxNTAsIDE1MSwgMTUzLCAxMCknLFxuICAgIG1vcmU6IHtcbiAgICAgIC8v5p+l55yL5pu05aSaXG4gICAgICBzaG93OiBmYWxzZSxcbiAgICAgIHR5cGU6ICdpY29uJyxcbiAgICAgIHRleHQ6ICfmn6XnnIvmm7TlpJonLFxuICAgICAgdXJsOiAnJ1xuICAgIH0sXG4gICAgc3R5bGU6IHtcbiAgICAgIGJnVHlwZTogJ2NvbG9yJyxcbiAgICAgIGJnQ29sb3I6ICcjZmZmJ1xuICAgIH0gYXMgQ29tcG9uZW50U3R5bGVcbiAgfVxufSBhcyBEaXlDb21wb25lbnQ8VGl0bGVCYXJQcm9wZXJ0eT5cbiIsImltcG9ydCB7IENvbXBvbmVudFN0eWxlLCBEaXlDb21wb25lbnQgfSBmcm9tICdAL2NvbXBvbmVudHMvRGl5RWRpdG9yL3V0aWwnXG5cbi8qKiDnlKjmiLfljaHniYflsZ7mgKcgKi9cbmV4cG9ydCBpbnRlcmZhY2UgVXNlckNhcmRQcm9wZXJ0eSB7XG4gIC8vIOe7hOS7tuagt+W8j1xuICBzdHlsZTogQ29tcG9uZW50U3R5bGVcbn1cblxuLy8g5a6a5LmJ57uE5Lu2XG5leHBvcnQgY29uc3QgY29tcG9uZW50ID0ge1xuICBpZDogJ1VzZXJDYXJkJyxcbiAgbmFtZTogJ+eUqOaIt+WNoeeJhycsXG4gIGljb246ICdtZGk6dXNlci1jYXJkLWRldGFpbHMnLFxuICBwcm9wZXJ0eToge1xuICAgIHN0eWxlOiB7XG4gICAgICBiZ1R5cGU6ICdjb2xvcicsXG4gICAgICBiZ0NvbG9yOiAnJyxcbiAgICAgIG1hcmdpbkJvdHRvbTogOFxuICAgIH0gYXMgQ29tcG9uZW50U3R5bGVcbiAgfVxufSBhcyBEaXlDb21wb25lbnQ8VXNlckNhcmRQcm9wZXJ0eT5cbiIsImltcG9ydCB7IENvbXBvbmVudFN0eWxlLCBEaXlDb21wb25lbnQgfSBmcm9tICdAL2NvbXBvbmVudHMvRGl5RWRpdG9yL3V0aWwnXG5cbi8qKiDnlKjmiLfljaHliLjlsZ7mgKcgKi9cbmV4cG9ydCBpbnRlcmZhY2UgVXNlckNvdXBvblByb3BlcnR5IHtcbiAgLy8g57uE5Lu25qC35byPXG4gIHN0eWxlOiBDb21wb25lbnRTdHlsZVxufVxuXG4vLyDlrprkuYnnu4Tku7ZcbmV4cG9ydCBjb25zdCBjb21wb25lbnQgPSB7XG4gIGlkOiAnVXNlckNvdXBvbicsXG4gIG5hbWU6ICfnlKjmiLfljaHliLgnLFxuICBpY29uOiAnZXA6dGlja2V0JyxcbiAgcHJvcGVydHk6IHtcbiAgICBzdHlsZToge1xuICAgICAgYmdUeXBlOiAnY29sb3InLFxuICAgICAgYmdDb2xvcjogJycsXG4gICAgICBtYXJnaW5MZWZ0OiA4LFxuICAgICAgbWFyZ2luUmlnaHQ6IDgsXG4gICAgICBtYXJnaW5Cb3R0b206IDhcbiAgICB9IGFzIENvbXBvbmVudFN0eWxlXG4gIH1cbn0gYXMgRGl5Q29tcG9uZW50PFVzZXJDb3Vwb25Qcm9wZXJ0eT5cbiIsImltcG9ydCB7IENvbXBvbmVudFN0eWxlLCBEaXlDb21wb25lbnQgfSBmcm9tICdAL2NvbXBvbmVudHMvRGl5RWRpdG9yL3V0aWwnXG5cbi8qKiDnlKjmiLforqLljZXlsZ7mgKcgKi9cbmV4cG9ydCBpbnRlcmZhY2UgVXNlck9yZGVyUHJvcGVydHkge1xuICAvLyDnu4Tku7bmoLflvI9cbiAgc3R5bGU6IENvbXBvbmVudFN0eWxlXG59XG5cbi8vIOWumuS5iee7hOS7tlxuZXhwb3J0IGNvbnN0IGNvbXBvbmVudCA9IHtcbiAgaWQ6ICdVc2VyT3JkZXInLFxuICBuYW1lOiAn55So5oi36K6i5Y2VJyxcbiAgaWNvbjogJ2VwOmxpc3QnLFxuICBwcm9wZXJ0eToge1xuICAgIHN0eWxlOiB7XG4gICAgICBiZ1R5cGU6ICdjb2xvcicsXG4gICAgICBiZ0NvbG9yOiAnJyxcbiAgICAgIG1hcmdpbkxlZnQ6IDgsXG4gICAgICBtYXJnaW5SaWdodDogOCxcbiAgICAgIG1hcmdpbkJvdHRvbTogOFxuICAgIH0gYXMgQ29tcG9uZW50U3R5bGVcbiAgfVxufSBhcyBEaXlDb21wb25lbnQ8VXNlck9yZGVyUHJvcGVydHk+XG4iLCJpbXBvcnQgeyBDb21wb25lbnRTdHlsZSwgRGl5Q29tcG9uZW50IH0gZnJvbSAnQC9jb21wb25lbnRzL0RpeUVkaXRvci91dGlsJ1xuXG4vKiog55So5oi36LWE5Lqn5bGe5oCnICovXG5leHBvcnQgaW50ZXJmYWNlIFVzZXJXYWxsZXRQcm9wZXJ0eSB7XG4gIC8vIOe7hOS7tuagt+W8j1xuICBzdHlsZTogQ29tcG9uZW50U3R5bGVcbn1cblxuLy8g5a6a5LmJ57uE5Lu2XG5leHBvcnQgY29uc3QgY29tcG9uZW50ID0ge1xuICBpZDogJ1VzZXJXYWxsZXQnLFxuICBuYW1lOiAn55So5oi36LWE5LqnJyxcbiAgaWNvbjogJ2VwOndhbGxldC1maWxsZWQnLFxuICBwcm9wZXJ0eToge1xuICAgIHN0eWxlOiB7XG4gICAgICBiZ1R5cGU6ICdjb2xvcicsXG4gICAgICBiZ0NvbG9yOiAnJyxcbiAgICAgIG1hcmdpbkxlZnQ6IDgsXG4gICAgICBtYXJnaW5SaWdodDogOCxcbiAgICAgIG1hcmdpbkJvdHRvbTogOFxuICAgIH0gYXMgQ29tcG9uZW50U3R5bGVcbiAgfVxufSBhcyBEaXlDb21wb25lbnQ8VXNlcldhbGxldFByb3BlcnR5PlxuIiwiaW1wb3J0IHsgQ29tcG9uZW50U3R5bGUsIERpeUNvbXBvbmVudCB9IGZyb20gJ0AvY29tcG9uZW50cy9EaXlFZGl0b3IvdXRpbCdcblxuLyoqIOinhumikeaSreaUvuWxnuaApyAqL1xuZXhwb3J0IGludGVyZmFjZSBWaWRlb1BsYXllclByb3BlcnR5IHtcbiAgLy8g6KeG6aKR6ZO+5o6lXG4gIHZpZGVvVXJsOiBzdHJpbmdcbiAgLy8g5bCB6Z2i6ZO+5o6lXG4gIHBvc3RlclVybDogc3RyaW5nXG4gIC8vIOaYr+WQpuiHquWKqOaSreaUvlxuICBhdXRvcGxheTogYm9vbGVhblxuICAvLyDnu4Tku7bmoLflvI9cbiAgc3R5bGU6IFZpZGVvUGxheWVyU3R5bGVcbn1cblxuLy8g6KeG6aKR5pKt5pS+5qC35byPXG5leHBvcnQgaW50ZXJmYWNlIFZpZGVvUGxheWVyU3R5bGUgZXh0ZW5kcyBDb21wb25lbnRTdHlsZSB7XG4gIC8vIOinhumikemrmOW6plxuICBoZWlnaHQ6IG51bWJlclxufVxuXG4vLyDlrprkuYnnu4Tku7ZcbmV4cG9ydCBjb25zdCBjb21wb25lbnQgPSB7XG4gIGlkOiAnVmlkZW9QbGF5ZXInLFxuICBuYW1lOiAn6KeG6aKR5pKt5pS+JyxcbiAgaWNvbjogJ2VwOnZpZGVvLXBsYXknLFxuICBwcm9wZXJ0eToge1xuICAgIHZpZGVvVXJsOiAnJyxcbiAgICBwb3N0ZXJVcmw6ICcnLFxuICAgIGF1dG9wbGF5OiBmYWxzZSxcbiAgICBzdHlsZToge1xuICAgICAgYmdUeXBlOiAnY29sb3InLFxuICAgICAgYmdDb2xvcjogJyNmZmYnLFxuICAgICAgbWFyZ2luQm90dG9tOiA4LFxuICAgICAgaGVpZ2h0OiAzMDBcbiAgICB9IGFzIFZpZGVvUGxheWVyU3R5bGVcbiAgfVxufSBhcyBEaXlDb21wb25lbnQ8VmlkZW9QbGF5ZXJQcm9wZXJ0eT5cbiIsIi8qXG4gKiDnu4Tku7bms6jlhoxcbiAqXG4gKiDnu4Tku7bop4TojIPvvJpcbiAqIDEuIOavj+S4quWtkOebruW9leWwseaYr+S4gOS4queLrOeri+eahOe7hOS7tu+8jOavj+S4quebruW9leWMheaLrOS7peS4i+S4ieS4quaWh+S7tu+8mlxuICogMi4gY29uZmlnLnRz77ya57uE5Lu26YWN572u77yM5b+F6YCJ77yM55So5LqO5a6a5LmJ57uE5Lu244CB57uE5Lu26buY6K6k55qE5bGe5oCn44CB5a6a5LmJ5bGe5oCn55qE57G75Z6LXG4gKiAzLiBpbmRleC52dWXvvJrnu4Tku7blsZXnpLrvvIznlKjkuo7lsZXnpLrnu4Tku7bnmoTmuLLmn5PmlYjmnpzjgILlj6/ku6XkuI3mj5DkvpvvvIzlpoIgUGFnZe+8iOmhtemdouiuvue9ru+8ie+8jOWPqumcgOimgeWxnuaAp+mFjee9ruihqOWNleWNs+WPr1xuICogNC4gcHJvcGVydHkudnVl77ya57uE5Lu25bGe5oCn6KGo5Y2V77yM55So5LqO6YWN572u57uE5Lu277yM5b+F6YCJ77yMXG4gKlxuICog5rOo77yaXG4gKiDnu4Tku7ZJROS7pWNvbmZpZy50c+S4remFjee9rueahGlk5Li65YeG77yM5LiO57uE5Lu255uu5b2V55qE5ZCN56ew5peg5YWz77yM5L2G6L+Y5piv5bu66K6u57uE5Lu255uu5b2V55qE5ZCN56ew5LiO57uE5Lu2SUTkv53mjIHkuIDoh7RcbiAqL1xuXG4vLyDlr7zlhaXnu4Tku7bnlYzpnaLmqKHlnZdcbmNvbnN0IHZpZXdNb2R1bGVzOiBSZWNvcmQ8c3RyaW5nLCBhbnk+ID0gaW1wb3J0Lm1ldGEuZ2xvYignLi8qLyoudnVlJylcbi8vIOWvvOWFpemFjee9ruaooeWdl1xuY29uc3QgY29uZmlnTW9kdWxlczogUmVjb3JkPHN0cmluZywgYW55PiA9IGltcG9ydC5tZXRhLmdsb2IoJy4vKi9jb25maWcudHMnLCB7IGVhZ2VyOiB0cnVlIH0pXG5cbi8vIOeVjOmdouaooeWdl1xuY29uc3QgY29tcG9uZW50cyA9IHt9XG4vLyDnu4Tku7bphY3nva7mqKHlnZdcbmNvbnN0IGNvbXBvbmVudENvbmZpZ3MgPSB7fVxuXG4vLyDnu4Tku7bnlYzpnaLnmoTnsbvlnotcbnR5cGUgVmlld1R5cGUgPSAnaW5kZXgnIHwgJ3Byb3BlcnR5J1xuXG4vKipcbiAqIOazqOWGjOe7hOS7tueahOeVjOmdouaooeWdl1xuICpcbiAqIEBwYXJhbSBjb21wb25lbnRJZCDnu4Tku7ZJRFxuICogQHBhcmFtIGNvbmZpZ1BhdGgg6YWN572u5qih5Z2X55qE5paH5Lu26Lev5b6EXG4gKiBAcGFyYW0gdmlld1R5cGUg57uE5Lu255WM6Z2i55qE57G75Z6LXG4gKi9cbmNvbnN0IHJlZ2lzdGVyQ29tcG9uZW50Vmlld01vZHVsZSA9IChcbiAgY29tcG9uZW50SWQ6IHN0cmluZyxcbiAgY29uZmlnUGF0aDogc3RyaW5nLFxuICB2aWV3VHlwZTogVmlld1R5cGVcbikgPT4ge1xuICBjb25zdCB2aWV3UGF0aCA9IGNvbmZpZ1BhdGgucmVwbGFjZSgnY29uZmlnLnRzJywgYCR7dmlld1R5cGV9LnZ1ZWApXG4gIGNvbnN0IHZpZXdNb2R1bGUgPSB2aWV3TW9kdWxlc1t2aWV3UGF0aF1cbiAgaWYgKHZpZXdNb2R1bGUpIHtcbiAgICAvLyDlrprkuYnlvILmraXnu4Tku7ZcbiAgICBjb21wb25lbnRzW2NvbXBvbmVudElkXSA9IGRlZmluZUFzeW5jQ29tcG9uZW50KHZpZXdNb2R1bGUpXG4gIH1cbn1cblxuLy8g5rOo5YaMXG5PYmplY3Qua2V5cyhjb25maWdNb2R1bGVzKS5mb3JFYWNoKChtb2R1bGVQYXRoOiBzdHJpbmcpID0+IHtcbiAgY29uc3QgY29tcG9uZW50ID0gY29uZmlnTW9kdWxlc1ttb2R1bGVQYXRoXS5jb21wb25lbnRcbiAgY29uc3QgY29tcG9uZW50SWQgPSBjb21wb25lbnQ/LmlkXG4gIGlmIChjb21wb25lbnRJZCkge1xuICAgIC8vIOazqOWGjOe7hOS7tlxuICAgIGNvbXBvbmVudENvbmZpZ3NbY29tcG9uZW50SWRdID0gY29tcG9uZW50XG4gICAgLy8g5rOo5YaM6aKE6KeI55WM6Z2iXG4gICAgcmVnaXN0ZXJDb21wb25lbnRWaWV3TW9kdWxlKGNvbXBvbmVudElkLCBtb2R1bGVQYXRoLCAnaW5kZXgnKVxuICAgIC8vIOazqOWGjOWxnuaAp+mFjee9ruihqOWNlVxuICAgIHJlZ2lzdGVyQ29tcG9uZW50Vmlld01vZHVsZShgJHtjb21wb25lbnRJZH1Qcm9wZXJ0eWAsIG1vZHVsZVBhdGgsICdwcm9wZXJ0eScpXG4gIH1cbn0pXG5cbmV4cG9ydCB7IGNvbXBvbmVudHMsIGNvbXBvbmVudENvbmZpZ3MgfVxuIiwiPHRlbXBsYXRlPlxuICA8ZGl2IDpjbGFzcz1cIlsnY29tcG9uZW50JywgeyBhY3RpdmU6IGFjdGl2ZSB9XVwiPlxuICAgIDxkaXZcbiAgICAgIDpzdHlsZT1cIntcbiAgICAgICAgLi4uc3R5bGVcbiAgICAgIH1cIlxuICAgID5cbiAgICAgIDxjb21wb25lbnQgOmlzPVwiY29tcG9uZW50LmlkXCIgOnByb3BlcnR5PVwiY29tcG9uZW50LnByb3BlcnR5XCIgLz5cbiAgICA8L2Rpdj5cbiAgICA8ZGl2IGNsYXNzPVwiY29tcG9uZW50LXdyYXBcIj5cbiAgICAgIDwhLS0g5bem5L6n77ya57uE5Lu25ZCN77yI5oKs5rWu55qE5bCP6LS05p2h77yJIC0tPlxuICAgICAgPGRpdiBjbGFzcz1cImNvbXBvbmVudC1uYW1lXCIgdi1pZj1cImNvbXBvbmVudC5uYW1lXCI+XG4gICAgICAgIHt7IGNvbXBvbmVudC5uYW1lIH19XG4gICAgICA8L2Rpdj5cbiAgICAgIDwhLS0g5Y+z5L6n77ya57uE5Lu25pON5L2c5bel5YW35qCPIC0tPlxuICAgICAgPGRpdiBjbGFzcz1cImNvbXBvbmVudC10b29sYmFyXCIgdi1pZj1cInNob3dUb29sYmFyICYmIGNvbXBvbmVudC5uYW1lICYmIGFjdGl2ZVwiPlxuICAgICAgICA8VmVydGljYWxCdXR0b25Hcm91cCB0eXBlPVwicHJpbWFyeVwiPlxuICAgICAgICAgIDxlbC10b29sdGlwIGNvbnRlbnQ9XCLkuIrnp7tcIiBwbGFjZW1lbnQ9XCJyaWdodFwiPlxuICAgICAgICAgICAgPGVsLWJ1dHRvbiA6ZGlzYWJsZWQ9XCIhY2FuTW92ZVVwXCIgQGNsaWNrLnN0b3A9XCJoYW5kbGVNb3ZlQ29tcG9uZW50KC0xKVwiPlxuICAgICAgICAgICAgICA8SWNvbiBpY29uPVwiZXA6YXJyb3ctdXBcIiAvPlxuICAgICAgICAgICAgPC9lbC1idXR0b24+XG4gICAgICAgICAgPC9lbC10b29sdGlwPlxuICAgICAgICAgIDxlbC10b29sdGlwIGNvbnRlbnQ9XCLkuIvnp7tcIiBwbGFjZW1lbnQ9XCJyaWdodFwiPlxuICAgICAgICAgICAgPGVsLWJ1dHRvbiA6ZGlzYWJsZWQ9XCIhY2FuTW92ZURvd25cIiBAY2xpY2suc3RvcD1cImhhbmRsZU1vdmVDb21wb25lbnQoMSlcIj5cbiAgICAgICAgICAgICAgPEljb24gaWNvbj1cImVwOmFycm93LWRvd25cIiAvPlxuICAgICAgICAgICAgPC9lbC1idXR0b24+XG4gICAgICAgICAgPC9lbC10b29sdGlwPlxuICAgICAgICAgIDxlbC10b29sdGlwIGNvbnRlbnQ9XCLlpI3liLZcIiBwbGFjZW1lbnQ9XCJyaWdodFwiPlxuICAgICAgICAgICAgPGVsLWJ1dHRvbiBAY2xpY2suc3RvcD1cImhhbmRsZUNvcHlDb21wb25lbnQoKVwiPlxuICAgICAgICAgICAgICA8SWNvbiBpY29uPVwiZXA6Y29weS1kb2N1bWVudFwiIC8+XG4gICAgICAgICAgICA8L2VsLWJ1dHRvbj5cbiAgICAgICAgICA8L2VsLXRvb2x0aXA+XG4gICAgICAgICAgPGVsLXRvb2x0aXAgY29udGVudD1cIuWIoOmZpFwiIHBsYWNlbWVudD1cInJpZ2h0XCI+XG4gICAgICAgICAgICA8ZWwtYnV0dG9uIEBjbGljay5zdG9wPVwiaGFuZGxlRGVsZXRlQ29tcG9uZW50KClcIj5cbiAgICAgICAgICAgICAgPEljb24gaWNvbj1cImVwOmRlbGV0ZVwiIC8+XG4gICAgICAgICAgICA8L2VsLWJ1dHRvbj5cbiAgICAgICAgICA8L2VsLXRvb2x0aXA+XG4gICAgICAgIDwvVmVydGljYWxCdXR0b25Hcm91cD5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICA8L2Rpdj5cbjwvdGVtcGxhdGU+XG5cbjxzY3JpcHQgbGFuZz1cInRzXCI+XG4vLyDms6jlhozmiYDmnInnmoTnu4Tku7ZcbmltcG9ydCB7IGNvbXBvbmVudHMgfSBmcm9tICcuLi9jb21wb25lbnRzL21vYmlsZS9pbmRleCdcbmV4cG9ydCBkZWZhdWx0IHtcbiAgY29tcG9uZW50czogeyAuLi5jb21wb25lbnRzIH1cbn1cbjwvc2NyaXB0PlxuPHNjcmlwdCBzZXR1cCBsYW5nPVwidHNcIj5cbmltcG9ydCB7IENvbXBvbmVudFN0eWxlLCBEaXlDb21wb25lbnQgfSBmcm9tICdAL2NvbXBvbmVudHMvRGl5RWRpdG9yL3V0aWwnXG5pbXBvcnQgeyBwcm9wVHlwZXMgfSBmcm9tICdAL3V0aWxzL3Byb3BUeXBlcydcbmltcG9ydCB7IG9iamVjdCB9IGZyb20gJ3Z1ZS10eXBlcydcblxuLyoqXG4gKiDnu4Tku7blrrnlmajvvJrnm67liY3lnKjkuK3pl7Tpg6jliIZcbiAqIOeUqOS6juWMheijuee7hOS7tu+8jOS4uue7hOS7tuaPkOS+myDog4zmma/jgIHlpJbovrnot53jgIHlhoXovrnot53jgIHovrnmoYbnrYnmoLflvI9cbiAqL1xuZGVmaW5lT3B0aW9ucyh7IG5hbWU6ICdDb21wb25lbnRDb250YWluZXInIH0pXG5cbnR5cGUgRGl5Q29tcG9uZW50V2l0aFN0eWxlID0gRGl5Q29tcG9uZW50PGFueT4gJiB7IHByb3BlcnR5OiB7IHN0eWxlPzogQ29tcG9uZW50U3R5bGUgfSB9XG5jb25zdCBwcm9wcyA9IGRlZmluZVByb3BzKHtcbiAgY29tcG9uZW50OiBvYmplY3Q8RGl5Q29tcG9uZW50V2l0aFN0eWxlPigpLmlzUmVxdWlyZWQsXG4gIGFjdGl2ZTogcHJvcFR5cGVzLmJvb2wuZGVmKGZhbHNlKSxcbiAgY2FuTW92ZVVwOiBwcm9wVHlwZXMuYm9vbC5kZWYoZmFsc2UpLFxuICBjYW5Nb3ZlRG93bjogcHJvcFR5cGVzLmJvb2wuZGVmKGZhbHNlKSxcbiAgc2hvd1Rvb2xiYXI6IHByb3BUeXBlcy5ib29sLmRlZih0cnVlKVxufSlcblxuLyoqXG4gKiDnu4Tku7bmoLflvI9cbiAqL1xuY29uc3Qgc3R5bGUgPSBjb21wdXRlZCgoKSA9PiB7XG4gIGxldCBjb21wb25lbnRTdHlsZSA9IHByb3BzLmNvbXBvbmVudC5wcm9wZXJ0eS5zdHlsZVxuICBpZiAoIWNvbXBvbmVudFN0eWxlKSB7XG4gICAgcmV0dXJuIHt9XG4gIH1cbiAgcmV0dXJuIHtcbiAgICBtYXJnaW5Ub3A6IGAke2NvbXBvbmVudFN0eWxlLm1hcmdpblRvcCB8fCAwfXB4YCxcbiAgICBtYXJnaW5Cb3R0b206IGAke2NvbXBvbmVudFN0eWxlLm1hcmdpbkJvdHRvbSB8fCAwfXB4YCxcbiAgICBtYXJnaW5MZWZ0OiBgJHtjb21wb25lbnRTdHlsZS5tYXJnaW5MZWZ0IHx8IDB9cHhgLFxuICAgIG1hcmdpblJpZ2h0OiBgJHtjb21wb25lbnRTdHlsZS5tYXJnaW5SaWdodCB8fCAwfXB4YCxcbiAgICBwYWRkaW5nVG9wOiBgJHtjb21wb25lbnRTdHlsZS5wYWRkaW5nVG9wIHx8IDB9cHhgLFxuICAgIHBhZGRpbmdSaWdodDogYCR7Y29tcG9uZW50U3R5bGUucGFkZGluZ1JpZ2h0IHx8IDB9cHhgLFxuICAgIHBhZGRpbmdCb3R0b206IGAke2NvbXBvbmVudFN0eWxlLnBhZGRpbmdCb3R0b20gfHwgMH1weGAsXG4gICAgcGFkZGluZ0xlZnQ6IGAke2NvbXBvbmVudFN0eWxlLnBhZGRpbmdMZWZ0IHx8IDB9cHhgLFxuICAgIGJvcmRlclRvcExlZnRSYWRpdXM6IGAke2NvbXBvbmVudFN0eWxlLmJvcmRlclRvcExlZnRSYWRpdXMgfHwgMH1weGAsXG4gICAgYm9yZGVyVG9wUmlnaHRSYWRpdXM6IGAke2NvbXBvbmVudFN0eWxlLmJvcmRlclRvcFJpZ2h0UmFkaXVzIHx8IDB9cHhgLFxuICAgIGJvcmRlckJvdHRvbVJpZ2h0UmFkaXVzOiBgJHtjb21wb25lbnRTdHlsZS5ib3JkZXJCb3R0b21SaWdodFJhZGl1cyB8fCAwfXB4YCxcbiAgICBib3JkZXJCb3R0b21MZWZ0UmFkaXVzOiBgJHtjb21wb25lbnRTdHlsZS5ib3JkZXJCb3R0b21MZWZ0UmFkaXVzIHx8IDB9cHhgLFxuICAgIG92ZXJmbG93OiAnaGlkZGVuJyxcbiAgICBiYWNrZ3JvdW5kOlxuICAgICAgY29tcG9uZW50U3R5bGUuYmdUeXBlID09PSAnY29sb3InID8gY29tcG9uZW50U3R5bGUuYmdDb2xvciA6IGB1cmwoJHtjb21wb25lbnRTdHlsZS5iZ0ltZ30pYFxuICB9XG59KVxuXG5jb25zdCBlbWl0cyA9IGRlZmluZUVtaXRzPHtcbiAgKGU6ICdtb3ZlJywgZGlyZWN0aW9uOiBudW1iZXIpOiB2b2lkXG4gIChlOiAnY29weScpOiB2b2lkXG4gIChlOiAnZGVsZXRlJyk6IHZvaWRcbn0+KClcblxuLyoqXG4gKiDnp7vliqjnu4Tku7ZcbiAqIEBwYXJhbSBkaXJlY3Rpb24g56e75Yqo5pa55ZCRXG4gKi9cbmNvbnN0IGhhbmRsZU1vdmVDb21wb25lbnQgPSAoZGlyZWN0aW9uOiBudW1iZXIpID0+IHtcbiAgZW1pdHMoJ21vdmUnLCBkaXJlY3Rpb24pXG59XG5cbi8qKlxuICog5aSN5Yi257uE5Lu2XG4gKi9cbmNvbnN0IGhhbmRsZUNvcHlDb21wb25lbnQgPSAoKSA9PiB7XG4gIGVtaXRzKCdjb3B5Jylcbn1cblxuLyoqXG4gKiDliKDpmaTnu4Tku7ZcbiAqL1xuY29uc3QgaGFuZGxlRGVsZXRlQ29tcG9uZW50ID0gKCkgPT4ge1xuICBlbWl0cygnZGVsZXRlJylcbn1cbjwvc2NyaXB0PlxuXG48c3R5bGUgc2NvcGVkIGxhbmc9XCJzY3NzXCI+XG4kYWN0aXZlLWJvcmRlci13aWR0aDogMnB4O1xuJGhvdmVyLWJvcmRlci13aWR0aDogMXB4O1xuJG5hbWUtcG9zaXRpb246IC04NXB4O1xuJHRvb2xiYXItcG9zaXRpb246IC01NXB4O1xuXG4vKiDnu4Tku7YgKi9cbi5jb21wb25lbnQge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIGN1cnNvcjogbW92ZTtcblxuICAuY29tcG9uZW50LXdyYXAge1xuICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICB0b3A6IDA7XG4gICAgbGVmdDogLSRhY3RpdmUtYm9yZGVyLXdpZHRoO1xuICAgIGRpc3BsYXk6IGJsb2NrO1xuICAgIHdpZHRoOiAxMDAlO1xuICAgIGhlaWdodDogMTAwJTtcblxuICAgIC8qIOm8oOagh+aUvuWIsOe7hOS7tuS4iuaXtiAqL1xuICAgICY6aG92ZXIge1xuICAgICAgYm9yZGVyOiAkaG92ZXItYm9yZGVyLXdpZHRoIGRhc2hlZCB2YXIoLS1lbC1jb2xvci1wcmltYXJ5KTtcbiAgICAgIGJveC1zaGFkb3c6IDAgMCA1cHggMCByZ2IoMjQgMTQ0IDI1NSAvIDMwJSk7XG5cbiAgICAgIC5jb21wb25lbnQtbmFtZSB7XG4gICAgICAgIHRvcDogJGhvdmVyLWJvcmRlci13aWR0aDtcblxuICAgICAgICAvKiDpmLLmraLliqDkuobovrnmoYbkuYvlkI7vvIzkvY3nva7np7vliqggKi9cbiAgICAgICAgbGVmdDogJG5hbWUtcG9zaXRpb24gLSAkaG92ZXItYm9yZGVyLXdpZHRoO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8qIOW3puS+p++8mue7hOS7tuWQjeensCAqL1xuICAgIC5jb21wb25lbnQtbmFtZSB7XG4gICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gICAgICB0b3A6ICRhY3RpdmUtYm9yZGVyLXdpZHRoO1xuICAgICAgbGVmdDogJG5hbWUtcG9zaXRpb247XG4gICAgICBkaXNwbGF5OiBibG9jaztcbiAgICAgIHdpZHRoOiA4MHB4O1xuICAgICAgaGVpZ2h0OiAyNXB4O1xuICAgICAgZm9udC1zaXplOiAxMnB4O1xuICAgICAgbGluZS1oZWlnaHQ6IDI1cHg7XG4gICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gICAgICBiYWNrZ3JvdW5kOiAjZmZmO1xuICAgICAgYm94LXNoYWRvdzpcbiAgICAgICAgMCAwIDRweCAjMDAwMDAwMTQsXG4gICAgICAgIDAgMnB4IDZweCAjMDAwMDAwMGYsXG4gICAgICAgIDAgNHB4IDhweCAycHggIzAwMDAwMDBhO1xuXG4gICAgICAvKiDlj7PkvqflsI/kuInop5IgKi9cbiAgICAgICY6OmFmdGVyIHtcbiAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgICAgICB0b3A6IDcuNXB4O1xuICAgICAgICByaWdodDogLTEwcHg7XG4gICAgICAgIHdpZHRoOiAwO1xuICAgICAgICBoZWlnaHQ6IDA7XG4gICAgICAgIGJvcmRlcjogNXB4IHNvbGlkIHRyYW5zcGFyZW50O1xuICAgICAgICBib3JkZXItbGVmdC1jb2xvcjogI2ZmZjtcbiAgICAgICAgY29udGVudDogJyAnO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8qIOWPs+S+p++8mue7hOS7tuaTjeS9nOW3peWFt+agjyAqL1xuICAgIC5jb21wb25lbnQtdG9vbGJhciB7XG4gICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gICAgICB0b3A6IDA7XG4gICAgICByaWdodDogJHRvb2xiYXItcG9zaXRpb247XG4gICAgICBkaXNwbGF5OiBub25lO1xuXG4gICAgICAvKiDlt6bkvqflsI/kuInop5IgKi9cbiAgICAgICY6OmJlZm9yZSB7XG4gICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICAgICAgdG9wOiAxMHB4O1xuICAgICAgICBsZWZ0OiAtMTBweDtcbiAgICAgICAgd2lkdGg6IDA7XG4gICAgICAgIGhlaWdodDogMDtcbiAgICAgICAgYm9yZGVyOiA1cHggc29saWQgdHJhbnNwYXJlbnQ7XG4gICAgICAgIGJvcmRlci1yaWdodC1jb2xvcjogIzJkOGNmMDtcbiAgICAgICAgY29udGVudDogJyAnO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8qIOe7hOS7tumAieS4reaXtiAqL1xuICAmLmFjdGl2ZSB7XG4gICAgbWFyZ2luLWJvdHRvbTogNHB4O1xuXG4gICAgLmNvbXBvbmVudC13cmFwIHtcbiAgICAgIG1hcmdpbi1ib3R0b206ICRhY3RpdmUtYm9yZGVyLXdpZHRoICsgJGFjdGl2ZS1ib3JkZXItd2lkdGg7XG4gICAgICBib3JkZXI6ICRhY3RpdmUtYm9yZGVyLXdpZHRoIHNvbGlkIHZhcigtLWVsLWNvbG9yLXByaW1hcnkpICFpbXBvcnRhbnQ7XG4gICAgICBib3gtc2hhZG93OiAwIDAgMTBweCAwIHJnYigyNCAxNDQgMjU1IC8gMzAlKTtcblxuICAgICAgLmNvbXBvbmVudC1uYW1lIHtcbiAgICAgICAgdG9wOiAwICFpbXBvcnRhbnQ7XG5cbiAgICAgICAgLyog6Ziy5q2i5Yqg5LqG6L655qGG5LmL5ZCO77yM5L2N572u56e75YqoICovXG4gICAgICAgIGxlZnQ6ICRuYW1lLXBvc2l0aW9uIC0gJGFjdGl2ZS1ib3JkZXItd2lkdGggIWltcG9ydGFudDtcbiAgICAgICAgY29sb3I6ICNmZmY7XG4gICAgICAgIGJhY2tncm91bmQ6IHZhcigtLWVsLWNvbG9yLXByaW1hcnkpO1xuXG4gICAgICAgICY6OmFmdGVyIHtcbiAgICAgICAgICBib3JkZXItbGVmdC1jb2xvcjogdmFyKC0tZWwtY29sb3ItcHJpbWFyeSk7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLmNvbXBvbmVudC10b29sYmFyIHtcbiAgICAgICAgZGlzcGxheTogYmxvY2s7XG4gICAgICB9XG4gICAgfVxuICB9XG59XG48L3N0eWxlPlxuIiwiPHRlbXBsYXRlPlxuICA8ZWwtYXNpZGUgY2xhc3M9XCJlZGl0b3ItbGVmdFwiIHdpZHRoPVwiMjYxcHhcIj5cbiAgICA8ZWwtc2Nyb2xsYmFyPlxuICAgICAgPGVsLWNvbGxhcHNlIHYtbW9kZWw9XCJleHRlbmRHcm91cHNcIj5cbiAgICAgICAgPGVsLWNvbGxhcHNlLWl0ZW1cbiAgICAgICAgICB2LWZvcj1cImdyb3VwIGluIGdyb3Vwc1wiXG4gICAgICAgICAgOmtleT1cImdyb3VwLm5hbWVcIlxuICAgICAgICAgIDpuYW1lPVwiZ3JvdXAubmFtZVwiXG4gICAgICAgICAgOnRpdGxlPVwiZ3JvdXAubmFtZVwiXG4gICAgICAgID5cbiAgICAgICAgICA8ZHJhZ2dhYmxlXG4gICAgICAgICAgICBjbGFzcz1cImNvbXBvbmVudC1jb250YWluZXJcIlxuICAgICAgICAgICAgZ2hvc3QtY2xhc3M9XCJkcmFnZ2FibGUtZ2hvc3RcIlxuICAgICAgICAgICAgaXRlbS1rZXk9XCJpbmRleFwiXG4gICAgICAgICAgICA6bGlzdD1cImdyb3VwLmNvbXBvbmVudHNcIlxuICAgICAgICAgICAgOnNvcnQ9XCJmYWxzZVwiXG4gICAgICAgICAgICA6Z3JvdXA9XCJ7IG5hbWU6ICdjb21wb25lbnQnLCBwdWxsOiAnY2xvbmUnLCBwdXQ6IGZhbHNlIH1cIlxuICAgICAgICAgICAgOmNsb25lPVwiaGFuZGxlQ2xvbmVDb21wb25lbnRcIlxuICAgICAgICAgICAgOmFuaW1hdGlvbj1cIjIwMFwiXG4gICAgICAgICAgICA6Zm9yY2UtZmFsbGJhY2s9XCJ0cnVlXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8dGVtcGxhdGUgI2l0ZW09XCJ7IGVsZW1lbnQgfVwiPlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9XCJkcmFnLXBsYWNlbWVudFwiPue7hOS7tuaUvue9ruWMuuWfnzwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9XCJjb21wb25lbnRcIj5cbiAgICAgICAgICAgICAgICAgIDxJY29uIDppY29uPVwiZWxlbWVudC5pY29uXCIgOnNpemU9XCIzMlwiIC8+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzcz1cIm10LTRweCB0ZXh0LTEycHhcIj57eyBlbGVtZW50Lm5hbWUgfX08L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC90ZW1wbGF0ZT5cbiAgICAgICAgICA8L2RyYWdnYWJsZT5cbiAgICAgICAgPC9lbC1jb2xsYXBzZS1pdGVtPlxuICAgICAgPC9lbC1jb2xsYXBzZT5cbiAgICA8L2VsLXNjcm9sbGJhcj5cbiAgPC9lbC1hc2lkZT5cbjwvdGVtcGxhdGU+XG5cbjxzY3JpcHQgc2V0dXAgbGFuZz1cInRzXCI+XG5pbXBvcnQgZHJhZ2dhYmxlIGZyb20gJ3Z1ZWRyYWdnYWJsZSdcbmltcG9ydCB7IGNvbXBvbmVudENvbmZpZ3MgfSBmcm9tICcuLi9jb21wb25lbnRzL21vYmlsZS9pbmRleCdcbmltcG9ydCB7IGNsb25lRGVlcCB9IGZyb20gJ2xvZGFzaC1lcydcbmltcG9ydCB7IERpeUNvbXBvbmVudCwgRGl5Q29tcG9uZW50TGlicmFyeSB9IGZyb20gJ0AvY29tcG9uZW50cy9EaXlFZGl0b3IvdXRpbCdcblxuLyoqIOe7hOS7tuW6k++8muebruWJjeW3puS+p+eahOOAkOWfuuehgOe7hOS7tuOAkeOAgeOAkOWbvuaWh+e7hOS7tuOAkemDqOWIhiAqL1xuZGVmaW5lT3B0aW9ucyh7IG5hbWU6ICdDb21wb25lbnRMaWJyYXJ5JyB9KVxuXG4vLyDnu4Tku7bliJfooahcbmNvbnN0IHByb3BzID0gZGVmaW5lUHJvcHM8e1xuICBsaXN0OiBEaXlDb21wb25lbnRMaWJyYXJ5W11cbn0+KClcbi8vIOe7hOS7tuWIhue7hFxuY29uc3QgZ3JvdXBzID0gcmVhY3RpdmU8YW55W10+KFtdKVxuLy8g5bGV5byA55qE5oqY5Y+g6Z2i5p2/XG5jb25zdCBleHRlbmRHcm91cHMgPSByZWFjdGl2ZTxzdHJpbmdbXT4oW10pXG5cbi8vIOebkeWQrCBsaXN0IOWxnuaAp++8jOaMieeFpyBEaXlDb21wb25lbnRMaWJyYXJ5IOeahCBuYW1lIOWIhue7hFxud2F0Y2goXG4gICgpID0+IHByb3BzLmxpc3QsXG4gICgpID0+IHtcbiAgICAvLyDmuIXpmaTml6fmlbDmja5cbiAgICBleHRlbmRHcm91cHMubGVuZ3RoID0gMFxuICAgIGdyb3Vwcy5sZW5ndGggPSAwXG4gICAgLy8g6YeN5paw55Sf5oiQ5pWw5o2uXG4gICAgcHJvcHMubGlzdC5mb3JFYWNoKChncm91cCkgPT4ge1xuICAgICAgLy8g5piv5ZCm5bGV5byA5YiG57uEXG4gICAgICBpZiAoZ3JvdXAuZXh0ZW5kZWQpIHtcbiAgICAgICAgZXh0ZW5kR3JvdXBzLnB1c2goZ3JvdXAubmFtZSlcbiAgICAgIH1cbiAgICAgIC8vIOafpeaJvue7hOS7tlxuICAgICAgY29uc3QgY29tcG9uZW50cyA9IGdyb3VwLmNvbXBvbmVudHNcbiAgICAgICAgLm1hcCgobmFtZSkgPT4gY29tcG9uZW50Q29uZmlnc1tuYW1lXSBhcyBEaXlDb21wb25lbnQ8YW55PilcbiAgICAgICAgLmZpbHRlcigoY29tcG9uZW50KSA9PiBjb21wb25lbnQpXG4gICAgICBpZiAoY29tcG9uZW50cy5sZW5ndGggPiAwKSB7XG4gICAgICAgIGdyb3Vwcy5wdXNoKHtcbiAgICAgICAgICBuYW1lOiBncm91cC5uYW1lLFxuICAgICAgICAgIGNvbXBvbmVudHNcbiAgICAgICAgfSlcbiAgICAgIH1cbiAgICB9KVxuICB9LFxuICB7XG4gICAgaW1tZWRpYXRlOiB0cnVlXG4gIH1cbilcblxuLy8g5YWL6ZqG57uE5Lu2XG5jb25zdCBoYW5kbGVDbG9uZUNvbXBvbmVudCA9IChjb21wb25lbnQ6IERpeUNvbXBvbmVudDxhbnk+KSA9PiB7XG4gIGNvbnN0IGluc3RhbmNlID0gY2xvbmVEZWVwKGNvbXBvbmVudClcbiAgaW5zdGFuY2UudWlkID0gbmV3IERhdGUoKS5nZXRUaW1lKClcbiAgcmV0dXJuIGluc3RhbmNlXG59XG48L3NjcmlwdD5cblxuPHN0eWxlIHNjb3BlZCBsYW5nPVwic2Nzc1wiPlxuLmVkaXRvci1sZWZ0IHtcbiAgei1pbmRleDogMTtcbiAgZmxleC1zaHJpbms6IDA7XG4gIGJveC1zaGFkb3c6IDhweCAwIDhweCAtOHB4IHJnYigwIDAgMCAvIDEyJSk7XG5cbiAgOmRlZXAoLmVsLWNvbGxhcHNlKSB7XG4gICAgYm9yZGVyLXRvcDogbm9uZTtcbiAgfVxuXG4gIDpkZWVwKC5lbC1jb2xsYXBzZS1pdGVtX193cmFwKSB7XG4gICAgYm9yZGVyLWJvdHRvbTogbm9uZTtcbiAgfVxuXG4gIDpkZWVwKC5lbC1jb2xsYXBzZS1pdGVtX19jb250ZW50KSB7XG4gICAgcGFkZGluZy1ib3R0b206IDA7XG4gIH1cblxuICA6ZGVlcCguZWwtY29sbGFwc2UtaXRlbV9faGVhZGVyKSB7XG4gICAgaGVpZ2h0OiAzMnB4O1xuICAgIHBhZGRpbmc6IDAgMjRweDtcbiAgICBsaW5lLWhlaWdodDogMzJweDtcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1lbC1iZy1jb2xvci1wYWdlKTtcbiAgICBib3JkZXItYm90dG9tOiBub25lO1xuICB9XG5cbiAgLmNvbXBvbmVudC1jb250YWluZXIge1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgZmxleC13cmFwOiB3cmFwO1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIH1cblxuICAuY29tcG9uZW50IHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIHdpZHRoOiA4NnB4O1xuICAgIGhlaWdodDogODZweDtcbiAgICBjdXJzb3I6IG1vdmU7XG4gICAgYm9yZGVyLXJpZ2h0OiAxcHggc29saWQgdmFyKC0tZWwtYm9yZGVyLWNvbG9yLWxpZ2h0ZXIpO1xuICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCB2YXIoLS1lbC1ib3JkZXItY29sb3ItbGlnaHRlcik7XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuXG4gICAgLmVsLWljb24ge1xuICAgICAgbWFyZ2luLWJvdHRvbTogNHB4O1xuICAgICAgY29sb3I6IGdyYXk7XG4gICAgfVxuICB9XG5cbiAgLmNvbXBvbmVudC5hY3RpdmUsXG4gIC5jb21wb25lbnQ6aG92ZXIge1xuICAgIGNvbG9yOiB2YXIoLS1lbC1jb2xvci13aGl0ZSk7XG4gICAgYmFja2dyb3VuZDogdmFyKC0tZWwtY29sb3ItcHJpbWFyeSk7XG5cbiAgICAuZWwtaWNvbiB7XG4gICAgICBjb2xvcjogdmFyKC0tZWwtY29sb3Itd2hpdGUpO1xuICAgIH1cbiAgfVxuXG4gIC5jb21wb25lbnQ6bnRoLW9mLXR5cGUoM24pIHtcbiAgICBib3JkZXItcmlnaHQ6IG5vbmU7XG4gIH1cbn1cblxuLyog5ouW5ou95Y2g5L2N5o+Q56S677yM6buY6K6k5LiN5pi+56S6ICovXG4uZHJhZy1wbGFjZW1lbnQge1xuICBkaXNwbGF5OiBub25lO1xuICBjb2xvcjogI2ZmZjtcbn1cblxuLmRyYWctYXJlYSB7XG4gIC8qIOaLluaLveWIsOaJi+acuuWMuuWfn+aXtueahOagt+W8jyAqL1xuICAuZHJhZ2dhYmxlLWdob3N0IHtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIHdpZHRoOiAxMDAlO1xuICAgIGhlaWdodDogNDBweDtcblxuICAgIC8qIOadoee6ueiDjOaZryAqL1xuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudChcbiAgICAgIDQ1ZGVnLFxuICAgICAgIzkxYThkNSAwLFxuICAgICAgIzkxYThkNSAxMCUsXG4gICAgICAjOTRiNGViIDEwJSxcbiAgICAgICM5NGI0ZWIgNTAlLFxuICAgICAgIzkxYThkNSA1MCUsXG4gICAgICAjOTFhOGQ1IDYwJSxcbiAgICAgICM5NGI0ZWIgNjAlLFxuICAgICAgIzk0YjRlYlxuICAgICk7XG4gICAgYmFja2dyb3VuZC1zaXplOiAxcmVtIDFyZW07XG4gICAgdHJhbnNpdGlvbjogYWxsIDAuNXM7XG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcblxuICAgIHNwYW4ge1xuICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xuICAgICAgd2lkdGg6IDE0MHB4O1xuICAgICAgaGVpZ2h0OiAyNXB4O1xuICAgICAgZm9udC1zaXplOiAxMnB4O1xuICAgICAgbGluZS1oZWlnaHQ6IDI1cHg7XG4gICAgICBjb2xvcjogI2ZmZjtcbiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICAgIGJhY2tncm91bmQ6ICM1NDg3ZGY7XG4gICAgfVxuXG4gICAgLyog5ouW5ou95pe26ZqQ6JeP57uE5Lu2ICovXG4gICAgLmNvbXBvbmVudCB7XG4gICAgICBkaXNwbGF5OiBub25lO1xuICAgIH1cblxuICAgIC8qIOaLluaLveaXtuaYvuekuuWNoOS9jeaPkOekuiAqL1xuICAgIC5kcmFnLXBsYWNlbWVudCB7XG4gICAgICBkaXNwbGF5OiBibG9jaztcbiAgICB9XG4gIH1cbn1cbjwvc3R5bGU+XG4iLCJleHBvcnQgZGVmYXVsdCBcIl9fVklURV9BU1NFVF9fQ0RCRE8xNmhfX1wiIiwiPHRlbXBsYXRlPlxuICA8ZWwtY29udGFpbmVyIGNsYXNzPVwiZWRpdG9yXCI+XG4gICAgPCEtLSDpobbpg6jvvJrlt6XlhbfmoI8gLS0+XG4gICAgPGVsLWhlYWRlciBjbGFzcz1cImVkaXRvci1oZWFkZXJcIj5cbiAgICAgIDwhLS0g5bem5L6n5pON5L2c5Yy6IC0tPlxuICAgICAgPHNsb3QgbmFtZT1cInRvb2xCYXJMZWZ0XCI+PC9zbG90PlxuICAgICAgPCEtLSDkuK3lv4Pmk43kvZzljLogLS0+XG4gICAgICA8ZGl2IGNsYXNzPVwiaGVhZGVyLWNlbnRlciBmbGV4IGZsZXgtMSBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgPHNwYW4+e3sgdGl0bGUgfX08L3NwYW4+XG4gICAgICA8L2Rpdj5cbiAgICAgIDwhLS0g5Y+z5L6n5pON5L2c5Yy6IC0tPlxuICAgICAgPGVsLWJ1dHRvbi1ncm91cCBjbGFzcz1cImhlYWRlci1yaWdodFwiPlxuICAgICAgICA8ZWwtdG9vbHRpcCBjb250ZW50PVwi6YeN572uXCI+XG4gICAgICAgICAgPGVsLWJ1dHRvbiBAY2xpY2s9XCJoYW5kbGVSZXNldFwiPlxuICAgICAgICAgICAgPEljb24gaWNvbj1cInN5c3RlbS11aWNvbnM6cmVzZXQtYWx0XCIgOnNpemU9XCIyNFwiIC8+XG4gICAgICAgICAgPC9lbC1idXR0b24+XG4gICAgICAgIDwvZWwtdG9vbHRpcD5cbiAgICAgICAgPGVsLXRvb2x0aXAgY29udGVudD1cIumihOiniFwiIHYtaWY9XCJwcmV2aWV3VXJsXCI+XG4gICAgICAgICAgPGVsLWJ1dHRvbiBAY2xpY2s9XCJoYW5kbGVQcmV2aWV3XCI+XG4gICAgICAgICAgICA8SWNvbiBpY29uPVwiZXA6dmlld1wiIDpzaXplPVwiMjRcIiAvPlxuICAgICAgICAgIDwvZWwtYnV0dG9uPlxuICAgICAgICA8L2VsLXRvb2x0aXA+XG4gICAgICAgIDxlbC10b29sdGlwIGNvbnRlbnQ9XCLkv53lrZhcIj5cbiAgICAgICAgICA8ZWwtYnV0dG9uIEBjbGljaz1cImhhbmRsZVNhdmVcIj5cbiAgICAgICAgICAgIDxJY29uIGljb249XCJlcDpjaGVja1wiIDpzaXplPVwiMjRcIiAvPlxuICAgICAgICAgIDwvZWwtYnV0dG9uPlxuICAgICAgICA8L2VsLXRvb2x0aXA+XG4gICAgICA8L2VsLWJ1dHRvbi1ncm91cD5cbiAgICA8L2VsLWhlYWRlcj5cblxuICAgIDwhLS0g5Lit5b+D5Yy65Z+fIC0tPlxuICAgIDxlbC1jb250YWluZXIgY2xhc3M9XCJlZGl0b3ItY29udGFpbmVyXCI+XG4gICAgICA8IS0tIOW3puS+p++8mue7hOS7tuW6k++8iENvbXBvbmVudExpYnJhcnnvvIkgLS0+XG4gICAgICA8Q29tcG9uZW50TGlicmFyeSByZWY9XCJjb21wb25lbnRMaWJyYXJ5XCIgOmxpc3Q9XCJsaWJzXCIgdi1pZj1cImxpYnMgJiYgbGlicy5sZW5ndGggPiAwXCIgLz5cbiAgICAgIDwhLS0g5Lit5b+D77ya6K6+6K6h5Yy65Z+f77yIQ29tcG9uZW50Q29udGFpbmVy77yJIC0tPlxuICAgICAgPGRpdiBjbGFzcz1cImVkaXRvci1jZW50ZXIgcGFnZS1wcm9wLWFyZWFcIiBAY2xpY2s9XCJoYW5kbGVQYWdlU2VsZWN0ZWRcIj5cbiAgICAgICAgPCEtLSDmiYvmnLrpobbpg6ggLS0+XG4gICAgICAgIDxkaXYgY2xhc3M9XCJlZGl0b3ItZGVzaWduLXRvcFwiPlxuICAgICAgICAgIDwhLS0g5omL5py66aG26YOo54q25oCB5qCPIC0tPlxuICAgICAgICAgIDxpbWcgc3JjPVwiQC9hc3NldHMvaW1ncy9kaXkvc3RhdHVzQmFyLnBuZ1wiIGFsdD1cIlwiIGNsYXNzPVwic3RhdHVzLWJhclwiIC8+XG4gICAgICAgICAgPCEtLSDmiYvmnLrpobbpg6jlr7zoiKrmoI8gLS0+XG4gICAgICAgICAgPENvbXBvbmVudENvbnRhaW5lclxuICAgICAgICAgICAgdi1pZj1cInNob3dOYXZpZ2F0aW9uQmFyXCJcbiAgICAgICAgICAgIDpjb21wb25lbnQ9XCJuYXZpZ2F0aW9uQmFyQ29tcG9uZW50XCJcbiAgICAgICAgICAgIDpzaG93LXRvb2xiYXI9XCJmYWxzZVwiXG4gICAgICAgICAgICA6YWN0aXZlPVwic2VsZWN0ZWRDb21wb25lbnQ/LmlkID09PSBuYXZpZ2F0aW9uQmFyQ29tcG9uZW50LmlkXCJcbiAgICAgICAgICAgIEBjbGljaz1cImhhbmRsZU5hdmlnYXRpb25CYXJTZWxlY3RlZFwiXG4gICAgICAgICAgICBjbGFzcz1cImN1cnNvci1wb2ludGVyIVwiXG4gICAgICAgICAgLz5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIDwhLS0g57ud5a+55a6a5L2N55qE57uE5Lu277ya5L6L5aaCIOW8ueeql+OAgea1ruWKqOaMiemSruetiSAtLT5cbiAgICAgICAgPGRpdlxuICAgICAgICAgIHYtZm9yPVwiKGNvbXBvbmVudCwgaW5kZXgpIGluIHBhZ2VDb21wb25lbnRzXCJcbiAgICAgICAgICA6a2V5PVwiaW5kZXhcIlxuICAgICAgICAgIEBjbGljaz1cImhhbmRsZUNvbXBvbmVudFNlbGVjdGVkKGNvbXBvbmVudCwgaW5kZXgpXCJcbiAgICAgICAgPlxuICAgICAgICAgIDxjb21wb25lbnRcbiAgICAgICAgICAgIHYtaWY9XCJjb21wb25lbnQucG9zaXRpb24gPT09ICdmaXhlZCcgJiYgc2VsZWN0ZWRDb21wb25lbnQ/LnVpZCA9PT0gY29tcG9uZW50LnVpZFwiXG4gICAgICAgICAgICA6aXM9XCJjb21wb25lbnQuaWRcIlxuICAgICAgICAgICAgOnByb3BlcnR5PVwiY29tcG9uZW50LnByb3BlcnR5XCJcbiAgICAgICAgICAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPCEtLSDmiYvmnLrpobXpnaLnvJbovpHljLrln58gLS0+XG4gICAgICAgIDxlbC1zY3JvbGxiYXJcbiAgICAgICAgICBoZWlnaHQ9XCIxMDAlXCJcbiAgICAgICAgICB3cmFwLWNsYXNzPVwiZWRpdG9yLWRlc2lnbi1jZW50ZXIgcGFnZS1wcm9wLWFyZWFcIlxuICAgICAgICAgIHZpZXctY2xhc3M9XCJwaG9uZS1jb250YWluZXJcIlxuICAgICAgICAgIDp2aWV3LXN0eWxlPVwie1xuICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBwYWdlQ29uZmlnQ29tcG9uZW50LnByb3BlcnR5LmJhY2tncm91bmRDb2xvcixcbiAgICAgICAgICAgIGJhY2tncm91bmRJbWFnZTogYHVybCgke3BhZ2VDb25maWdDb21wb25lbnQucHJvcGVydHkuYmFja2dyb3VuZEltYWdlfSlgXG4gICAgICAgICAgfVwiXG4gICAgICAgID5cbiAgICAgICAgICA8ZHJhZ2dhYmxlXG4gICAgICAgICAgICBjbGFzcz1cInBhZ2UtcHJvcC1hcmVhIGRyYWctYXJlYVwiXG4gICAgICAgICAgICB2LW1vZGVsPVwicGFnZUNvbXBvbmVudHNcIlxuICAgICAgICAgICAgaXRlbS1rZXk9XCJpbmRleFwiXG4gICAgICAgICAgICA6YW5pbWF0aW9uPVwiMjAwXCJcbiAgICAgICAgICAgIGZpbHRlcj1cIi5jb21wb25lbnQtdG9vbGJhclwiXG4gICAgICAgICAgICBnaG9zdC1jbGFzcz1cImRyYWdnYWJsZS1naG9zdFwiXG4gICAgICAgICAgICA6Zm9yY2UtZmFsbGJhY2s9XCJ0cnVlXCJcbiAgICAgICAgICAgIGdyb3VwPVwiY29tcG9uZW50XCJcbiAgICAgICAgICAgIEBjaGFuZ2U9XCJoYW5kbGVDb21wb25lbnRDaGFuZ2VcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDx0ZW1wbGF0ZSAjaXRlbT1cInsgZWxlbWVudCwgaW5kZXggfVwiPlxuICAgICAgICAgICAgICA8Q29tcG9uZW50Q29udGFpbmVyXG4gICAgICAgICAgICAgICAgdi1pZj1cIiFlbGVtZW50LnBvc2l0aW9uIHx8IGVsZW1lbnQucG9zaXRpb24gPT09ICdjZW50ZXInXCJcbiAgICAgICAgICAgICAgICA6Y29tcG9uZW50PVwiZWxlbWVudFwiXG4gICAgICAgICAgICAgICAgOmFjdGl2ZT1cInNlbGVjdGVkQ29tcG9uZW50SW5kZXggPT09IGluZGV4XCJcbiAgICAgICAgICAgICAgICA6Y2FuLW1vdmUtdXA9XCJpbmRleCA+IDBcIlxuICAgICAgICAgICAgICAgIDpjYW4tbW92ZS1kb3duPVwiaW5kZXggPCBwYWdlQ29tcG9uZW50cy5sZW5ndGggLSAxXCJcbiAgICAgICAgICAgICAgICBAbW92ZT1cIihkaXJlY3Rpb24pID0+IGhhbmRsZU1vdmVDb21wb25lbnQoaW5kZXgsIGRpcmVjdGlvbilcIlxuICAgICAgICAgICAgICAgIEBjb3B5PVwiaGFuZGxlQ29weUNvbXBvbmVudChpbmRleClcIlxuICAgICAgICAgICAgICAgIEBkZWxldGU9XCJoYW5kbGVEZWxldGVDb21wb25lbnQoaW5kZXgpXCJcbiAgICAgICAgICAgICAgICBAY2xpY2s9XCJoYW5kbGVDb21wb25lbnRTZWxlY3RlZChlbGVtZW50LCBpbmRleClcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC90ZW1wbGF0ZT5cbiAgICAgICAgICA8L2RyYWdnYWJsZT5cbiAgICAgICAgPC9lbC1zY3JvbGxiYXI+XG4gICAgICAgIDwhLS0g5omL5py65bqV6YOo5a+86IiqIC0tPlxuICAgICAgICA8ZGl2IHYtaWY9XCJzaG93VGFiQmFyXCIgOmNsYXNzPVwiWydlZGl0b3ItZGVzaWduLWJvdHRvbScsICdjb21wb25lbnQnLCAnY3Vyc29yLXBvaW50ZXIhJ11cIj5cbiAgICAgICAgICA8Q29tcG9uZW50Q29udGFpbmVyXG4gICAgICAgICAgICA6Y29tcG9uZW50PVwidGFiQmFyQ29tcG9uZW50XCJcbiAgICAgICAgICAgIDpzaG93LXRvb2xiYXI9XCJmYWxzZVwiXG4gICAgICAgICAgICA6YWN0aXZlPVwic2VsZWN0ZWRDb21wb25lbnQ/LmlkID09PSB0YWJCYXJDb21wb25lbnQuaWRcIlxuICAgICAgICAgICAgQGNsaWNrPVwiaGFuZGxlVGFiQmFyU2VsZWN0ZWRcIlxuICAgICAgICAgIC8+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8IS0tIOWbuuWumuW4g+WxgOeahOe7hOS7tiDmk43kvZzmjInpkq7ljLogLS0+XG4gICAgICAgIDxkaXYgY2xhc3M9XCJmaXhlZC1jb21wb25lbnQtYWN0aW9uLWdyb3VwXCI+XG4gICAgICAgICAgPGVsLXRhZ1xuICAgICAgICAgICAgdi1pZj1cInNob3dQYWdlQ29uZmlnXCJcbiAgICAgICAgICAgIHNpemU9XCJsYXJnZVwiXG4gICAgICAgICAgICA6ZWZmZWN0PVwic2VsZWN0ZWRDb21wb25lbnQ/LnVpZCA9PT0gcGFnZUNvbmZpZ0NvbXBvbmVudC51aWQgPyAnZGFyaycgOiAncGxhaW4nXCJcbiAgICAgICAgICAgIDp0eXBlPVwic2VsZWN0ZWRDb21wb25lbnQ/LnVpZCA9PT0gcGFnZUNvbmZpZ0NvbXBvbmVudC51aWQgPyAnJyA6ICdpbmZvJ1wiXG4gICAgICAgICAgICBAY2xpY2s9XCJoYW5kbGVDb21wb25lbnRTZWxlY3RlZChwYWdlQ29uZmlnQ29tcG9uZW50KVwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPEljb24gOmljb249XCJwYWdlQ29uZmlnQ29tcG9uZW50Lmljb25cIiA6c2l6ZT1cIjEyXCIgLz5cbiAgICAgICAgICAgIDxzcGFuPnt7IHBhZ2VDb25maWdDb21wb25lbnQubmFtZSB9fTwvc3Bhbj5cbiAgICAgICAgICA8L2VsLXRhZz5cbiAgICAgICAgICA8dGVtcGxhdGUgdi1mb3I9XCIoY29tcG9uZW50LCBpbmRleCkgaW4gcGFnZUNvbXBvbmVudHNcIiA6a2V5PVwiaW5kZXhcIj5cbiAgICAgICAgICAgIDxlbC10YWdcbiAgICAgICAgICAgICAgdi1pZj1cImNvbXBvbmVudC5wb3NpdGlvbiA9PT0gJ2ZpeGVkJ1wiXG4gICAgICAgICAgICAgIHNpemU9XCJsYXJnZVwiXG4gICAgICAgICAgICAgIGNsb3NhYmxlXG4gICAgICAgICAgICAgIDplZmZlY3Q9XCJzZWxlY3RlZENvbXBvbmVudD8udWlkID09PSBjb21wb25lbnQudWlkID8gJ2RhcmsnIDogJ3BsYWluJ1wiXG4gICAgICAgICAgICAgIDp0eXBlPVwic2VsZWN0ZWRDb21wb25lbnQ/LnVpZCA9PT0gY29tcG9uZW50LnVpZCA/ICcnIDogJ2luZm8nXCJcbiAgICAgICAgICAgICAgQGNsaWNrPVwiaGFuZGxlQ29tcG9uZW50U2VsZWN0ZWQoY29tcG9uZW50KVwiXG4gICAgICAgICAgICAgIEBjbG9zZT1cImhhbmRsZURlbGV0ZUNvbXBvbmVudChpbmRleClcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8SWNvbiA6aWNvbj1cImNvbXBvbmVudC5pY29uXCIgOnNpemU9XCIxMlwiIC8+XG4gICAgICAgICAgICAgIDxzcGFuPnt7IGNvbXBvbmVudC5uYW1lIH19PC9zcGFuPlxuICAgICAgICAgICAgPC9lbC10YWc+XG4gICAgICAgICAgPC90ZW1wbGF0ZT5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICAgIDwhLS0g5Y+z5L6n77ya5bGe5oCn6Z2i5p2/77yIQ29tcG9uZW50Q29udGFpbmVyUHJvcGVydHnvvIkgLS0+XG4gICAgICA8ZWwtYXNpZGUgY2xhc3M9XCJlZGl0b3ItcmlnaHRcIiB3aWR0aD1cIjM1MHB4XCIgdi1pZj1cInNlbGVjdGVkQ29tcG9uZW50Py5wcm9wZXJ0eVwiPlxuICAgICAgICA8ZWwtY2FyZFxuICAgICAgICAgIHNoYWRvdz1cIm5ldmVyXCJcbiAgICAgICAgICBib2R5LWNsYXNzPVwiaC1bY2FsYygxMDAlLXZhcigtLWVsLWNhcmQtcGFkZGluZyktdmFyKC0tZWwtY2FyZC1wYWRkaW5nKSldXCJcbiAgICAgICAgICBjbGFzcz1cImgtZnVsbFwiXG4gICAgICAgID5cbiAgICAgICAgICA8IS0tIOe7hOS7tuWQjeensCAtLT5cbiAgICAgICAgICA8dGVtcGxhdGUgI2hlYWRlcj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3M9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtOHB4XCI+XG4gICAgICAgICAgICAgIDxJY29uIDppY29uPVwic2VsZWN0ZWRDb21wb25lbnQ/Lmljb25cIiBjb2xvcj1cImdyYXlcIiAvPlxuICAgICAgICAgICAgICA8c3Bhbj57eyBzZWxlY3RlZENvbXBvbmVudD8ubmFtZSB9fTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvdGVtcGxhdGU+XG4gICAgICAgICAgPGVsLXNjcm9sbGJhclxuICAgICAgICAgICAgY2xhc3M9XCJtLVtjYWxjKDBweC12YXIoLS1lbC1jYXJkLXBhZGRpbmcpKV1cIlxuICAgICAgICAgICAgdmlldy1jbGFzcz1cInAtW3ZhcigtLWVsLWNhcmQtcGFkZGluZyldIHAtYi1bY2FsYyh2YXIoLS1lbC1jYXJkLXBhZGRpbmcpK3ZhcigtLWVsLWNhcmQtcGFkZGluZykpXSBwcm9wZXJ0eVwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGNvbXBvbmVudFxuICAgICAgICAgICAgICA6a2V5PVwic2VsZWN0ZWRDb21wb25lbnQ/LnVpZCB8fCBzZWxlY3RlZENvbXBvbmVudD8uaWRcIlxuICAgICAgICAgICAgICA6aXM9XCJzZWxlY3RlZENvbXBvbmVudD8uaWQgKyAnUHJvcGVydHknXCJcbiAgICAgICAgICAgICAgdi1tb2RlbD1cInNlbGVjdGVkQ29tcG9uZW50LnByb3BlcnR5XCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9lbC1zY3JvbGxiYXI+XG4gICAgICAgIDwvZWwtY2FyZD5cbiAgICAgIDwvZWwtYXNpZGU+XG4gICAgPC9lbC1jb250YWluZXI+XG4gIDwvZWwtY29udGFpbmVyPlxuXG4gIDwhLS0g6aKE6KeI5by55qGGIC0tPlxuICA8RGlhbG9nIHYtbW9kZWw9XCJwcmV2aWV3RGlhbG9nVmlzaWJsZVwiIHRpdGxlPVwi6aKE6KeIXCIgd2lkdGg9XCI3MDBcIj5cbiAgICA8ZGl2IGNsYXNzPVwiZmxleCBqdXN0aWZ5LWFyb3VuZFwiPlxuICAgICAgPElGcmFtZVxuICAgICAgICBjbGFzcz1cInctMzc1cHggYm9yZGVyLTRweCBib3JkZXItcm91bmRlZC04cHggYm9yZGVyLXNvbGlkIHAtMnB4IGgtNjY3cHghXCJcbiAgICAgICAgOnNyYz1cInByZXZpZXdVcmxcIlxuICAgICAgLz5cbiAgICAgIDxkaXYgY2xhc3M9XCJmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgIDxlbC10ZXh0PuaJi+acuuaJq+eggemihOiniDwvZWwtdGV4dD5cbiAgICAgICAgPFFyY29kZSA6dGV4dD1cInByZXZpZXdVcmxcIiBsb2dvPVwiL2xvZ28uZ2lmXCIgLz5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICA8L0RpYWxvZz5cbjwvdGVtcGxhdGU+XG48c2NyaXB0IGxhbmc9XCJ0c1wiPlxuLy8g5rOo5YaM5omA5pyJ55qE57uE5Lu2XG5pbXBvcnQgeyBjb21wb25lbnRzIH0gZnJvbSAnLi9jb21wb25lbnRzL21vYmlsZS9pbmRleCdcbmV4cG9ydCBkZWZhdWx0IHtcbiAgY29tcG9uZW50czogeyAuLi5jb21wb25lbnRzIH1cbn1cbjwvc2NyaXB0PlxuPHNjcmlwdCBsYW5nPVwidHNcIiBzZXR1cD5cbmltcG9ydCBkcmFnZ2FibGUgZnJvbSAndnVlZHJhZ2dhYmxlJ1xuaW1wb3J0IENvbXBvbmVudExpYnJhcnkgZnJvbSAnLi9jb21wb25lbnRzL0NvbXBvbmVudExpYnJhcnkudnVlJ1xuaW1wb3J0IHsgY2xvbmVEZWVwLCBpbmNsdWRlcyB9IGZyb20gJ2xvZGFzaC1lcydcbmltcG9ydCB7IGNvbXBvbmVudCBhcyBQQUdFX0NPTkZJR19DT01QT05FTlQgfSBmcm9tICdAL2NvbXBvbmVudHMvRGl5RWRpdG9yL2NvbXBvbmVudHMvbW9iaWxlL1BhZ2VDb25maWcvY29uZmlnJ1xuaW1wb3J0IHsgY29tcG9uZW50IGFzIE5BVklHQVRJT05fQkFSX0NPTVBPTkVOVCB9IGZyb20gJy4vY29tcG9uZW50cy9tb2JpbGUvTmF2aWdhdGlvbkJhci9jb25maWcnXG5pbXBvcnQgeyBjb21wb25lbnQgYXMgVEFCX0JBUl9DT01QT05FTlQgfSBmcm9tICcuL2NvbXBvbmVudHMvbW9iaWxlL1RhYkJhci9jb25maWcnXG5pbXBvcnQgeyBpc1N0cmluZyB9IGZyb20gJ0AvdXRpbHMvaXMnXG5pbXBvcnQgeyBEaXlDb21wb25lbnQsIERpeUNvbXBvbmVudExpYnJhcnksIFBhZ2VDb25maWcgfSBmcm9tICdAL2NvbXBvbmVudHMvRGl5RWRpdG9yL3V0aWwnXG5pbXBvcnQgeyBjb21wb25lbnRDb25maWdzIH0gZnJvbSAnQC9jb21wb25lbnRzL0RpeUVkaXRvci9jb21wb25lbnRzL21vYmlsZSdcbmltcG9ydCB7IGFycmF5LCBvbmVPZlR5cGUgfSBmcm9tICd2dWUtdHlwZXMnXG5pbXBvcnQgeyBwcm9wVHlwZXMgfSBmcm9tICdAL3V0aWxzL3Byb3BUeXBlcydcblxuLyoqIOmhtemdouijheS/ruivpuaDhemhtSAqL1xuZGVmaW5lT3B0aW9ucyh7IG5hbWU6ICdEaXlQYWdlRGV0YWlsJyB9KVxuXG4vLyDlt6bkvqfnu4Tku7blupNcbmNvbnN0IGNvbXBvbmVudExpYnJhcnkgPSByZWYoKVxuLy8g6aG16Z2i6K6+572u57uE5Lu2XG5jb25zdCBwYWdlQ29uZmlnQ29tcG9uZW50ID0gcmVmPERpeUNvbXBvbmVudDxhbnk+PihjbG9uZURlZXAoUEFHRV9DT05GSUdfQ09NUE9ORU5UKSlcbi8vIOmhtumDqOWvvOiIquagj1xuY29uc3QgbmF2aWdhdGlvbkJhckNvbXBvbmVudCA9IHJlZjxEaXlDb21wb25lbnQ8YW55Pj4oY2xvbmVEZWVwKE5BVklHQVRJT05fQkFSX0NPTVBPTkVOVCkpXG4vLyDlupXpg6jlr7zoiKroj5zljZVcbmNvbnN0IHRhYkJhckNvbXBvbmVudCA9IHJlZjxEaXlDb21wb25lbnQ8YW55Pj4oY2xvbmVEZWVwKFRBQl9CQVJfQ09NUE9ORU5UKSlcblxuLy8g6YCJ5Lit55qE57uE5Lu277yM6buY6K6k6YCJ5Lit6aG26YOo5a+86Iiq5qCPXG5jb25zdCBzZWxlY3RlZENvbXBvbmVudCA9IHJlZjxEaXlDb21wb25lbnQ8YW55Pj4oKVxuLy8g6YCJ5Lit55qE57uE5Lu257Si5byVXG5jb25zdCBzZWxlY3RlZENvbXBvbmVudEluZGV4ID0gcmVmPG51bWJlcj4oLTEpXG4vLyDnu4Tku7bliJfooahcbmNvbnN0IHBhZ2VDb21wb25lbnRzID0gcmVmPERpeUNvbXBvbmVudDxhbnk+W10+KFtdKVxuLy8g5a6a5LmJ5bGe5oCnXG5jb25zdCBwcm9wcyA9IGRlZmluZVByb3BzKHtcbiAgLy8g6aG16Z2i6YWN572u77yM5pSv5oyBSnNvbuWtl+espuS4slxuICBtb2RlbFZhbHVlOiBvbmVPZlR5cGU8c3RyaW5nIHwgUGFnZUNvbmZpZz4oW1N0cmluZywgT2JqZWN0XSkuaXNSZXF1aXJlZCxcbiAgLy8g5qCH6aKYXG4gIHRpdGxlOiBwcm9wVHlwZXMuc3RyaW5nLmRlZignJyksXG4gIC8vIOe7hOS7tuW6k1xuICBsaWJzOiBhcnJheTxEaXlDb21wb25lbnRMaWJyYXJ5PigpLFxuICAvLyDmmK/lkKbmmL7npLrpobbpg6jlr7zoiKrmoI9cbiAgc2hvd05hdmlnYXRpb25CYXI6IHByb3BUeXBlcy5ib29sLmRlZih0cnVlKSxcbiAgLy8g5piv5ZCm5pi+56S65bqV6YOo5a+86Iiq6I+c5Y2VXG4gIHNob3dUYWJCYXI6IHByb3BUeXBlcy5ib29sLmRlZihmYWxzZSksXG4gIC8vIOaYr+WQpuaYvuekuumhtemdoumFjee9rlxuICBzaG93UGFnZUNvbmZpZzogcHJvcFR5cGVzLmJvb2wuZGVmKHRydWUpLFxuICAvLyDpooTop4jlnLDlnYDvvJrmj5DkvpvkuobpooTop4jlnLDlnYDvvIzmiY3kvJrmmL7npLrpooTop4jmjInpkq5cbiAgcHJldmlld1VybDogcHJvcFR5cGVzLnN0cmluZy5kZWYoJycpXG59KVxuXG4vLyDnm5HlkKzkvKDlhaXnmoTpobXpnaLphY3nva5cbi8vIOino+aekOWHuiBwYWdlQ29uZmlnQ29tcG9uZW50IOmhtemdouaVtOS9k+eahOmFjee9ru+8jG5hdmlnYXRpb25CYXJDb21wb25lbnTjgIFwYWdlQ29tcG9uZW50c+OAgXRhYkJhckNvbXBvbmVudCDpobXpnaLkuIrjgIHkuK3jgIHkuIvnmoTphY3nva5cbndhdGNoKFxuICAoKSA9PiBwcm9wcy5tb2RlbFZhbHVlLFxuICAoKSA9PiB7XG4gICAgY29uc3QgbW9kZWxWYWx1ZSA9IGlzU3RyaW5nKHByb3BzLm1vZGVsVmFsdWUpXG4gICAgICA/IChKU09OLnBhcnNlKHByb3BzLm1vZGVsVmFsdWUpIGFzIFBhZ2VDb25maWcpXG4gICAgICA6IHByb3BzLm1vZGVsVmFsdWVcbiAgICBwYWdlQ29uZmlnQ29tcG9uZW50LnZhbHVlLnByb3BlcnR5ID0gbW9kZWxWYWx1ZT8ucGFnZSB8fCBQQUdFX0NPTkZJR19DT01QT05FTlQucHJvcGVydHlcbiAgICBuYXZpZ2F0aW9uQmFyQ29tcG9uZW50LnZhbHVlLnByb3BlcnR5ID1cbiAgICAgIG1vZGVsVmFsdWU/Lm5hdmlnYXRpb25CYXIgfHwgTkFWSUdBVElPTl9CQVJfQ09NUE9ORU5ULnByb3BlcnR5XG4gICAgdGFiQmFyQ29tcG9uZW50LnZhbHVlLnByb3BlcnR5ID0gbW9kZWxWYWx1ZT8udGFiQmFyIHx8IFRBQl9CQVJfQ09NUE9ORU5ULnByb3BlcnR5XG4gICAgLy8g5p+l5om+5a+55bqU55qE6aG16Z2i57uE5Lu2XG4gICAgcGFnZUNvbXBvbmVudHMudmFsdWUgPSAobW9kZWxWYWx1ZT8uY29tcG9uZW50cyB8fCBbXSkubWFwKChpdGVtKSA9PiB7XG4gICAgICBjb25zdCBjb21wb25lbnQgPSBjb21wb25lbnRDb25maWdzW2l0ZW0uaWRdXG4gICAgICByZXR1cm4geyAuLi5jb21wb25lbnQsIHByb3BlcnR5OiBpdGVtLnByb3BlcnR5IH1cbiAgICB9KVxuICB9LFxuICB7XG4gICAgaW1tZWRpYXRlOiB0cnVlXG4gIH1cbilcblxuLy8g5L+d5a2YXG5jb25zdCBoYW5kbGVTYXZlID0gKCkgPT4ge1xuICBjb25zdCBwYWdlQ29uZmlnID0ge1xuICAgIHBhZ2U6IHBhZ2VDb25maWdDb21wb25lbnQudmFsdWUucHJvcGVydHksXG4gICAgbmF2aWdhdGlvbkJhcjogbmF2aWdhdGlvbkJhckNvbXBvbmVudC52YWx1ZS5wcm9wZXJ0eSxcbiAgICB0YWJCYXI6IHRhYkJhckNvbXBvbmVudC52YWx1ZS5wcm9wZXJ0eSxcbiAgICBjb21wb25lbnRzOiBwYWdlQ29tcG9uZW50cy52YWx1ZS5tYXAoKGNvbXBvbmVudCkgPT4ge1xuICAgICAgLy8g5Y+q5L+d55WZQVBQ5pyJ55So55qE5a2X5q61XG4gICAgICByZXR1cm4geyBpZDogY29tcG9uZW50LmlkLCBwcm9wZXJ0eTogY29tcG9uZW50LnByb3BlcnR5IH1cbiAgICB9KVxuICB9IGFzIFBhZ2VDb25maWdcbiAgaWYgKCFwcm9wcy5zaG93VGFiQmFyKSB7XG4gICAgZGVsZXRlIHBhZ2VDb25maWcudGFiQmFyXG4gIH1cbiAgLy8g5Y+R6YCB5pWw5o2u5pu05paw6YCa55+lXG4gIGNvbnN0IG1vZGVsVmFsdWUgPSBpc1N0cmluZyhwcm9wcy5tb2RlbFZhbHVlKSA/IEpTT04uc3RyaW5naWZ5KHBhZ2VDb25maWcpIDogcGFnZUNvbmZpZ1xuICBlbWl0cygndXBkYXRlOm1vZGVsVmFsdWUnLCBtb2RlbFZhbHVlKVxuICAvLyDlj5HpgIHkv53lrZjpgJrnn6VcbiAgZW1pdHMoJ3NhdmUnLCBwYWdlQ29uZmlnKVxufVxuXG4vLyDlpITnkIbpobXpnaLpgInkuK3vvJrmmL7npLrlsZ7mgKfooajljZVcbmNvbnN0IGhhbmRsZVBhZ2VTZWxlY3RlZCA9IChldmVudDogYW55KSA9PiB7XG4gIGlmICghcHJvcHMuc2hvd1BhZ2VDb25maWcpIHJldHVyblxuXG4gIC8vIOmFjee9ruS6huagt+W8jyBwYWdlLXByb3AtYXJlYSDnmoTlhYPntKDvvIzmiY3mmL7npLrpobXpnaLorr7nva5cbiAgaWYgKGluY2x1ZGVzKGV2ZW50Py50YXJnZXQ/LmNsYXNzTGlzdCwgJ3BhZ2UtcHJvcC1hcmVhJykpIHtcbiAgICBoYW5kbGVDb21wb25lbnRTZWxlY3RlZCh1bnJlZihwYWdlQ29uZmlnQ29tcG9uZW50KSlcbiAgfVxufVxuXG4vKipcbiAqIOmAieS4ree7hOS7tlxuICpcbiAqIEBwYXJhbSBjb21wb25lbnQg57uE5Lu2XG4gKiBAcGFyYW0gaW5kZXgg57uE5Lu255qE57Si5byVXG4gKi9cbmNvbnN0IGhhbmRsZUNvbXBvbmVudFNlbGVjdGVkID0gKGNvbXBvbmVudDogRGl5Q29tcG9uZW50PGFueT4sIGluZGV4OiBudW1iZXIgPSAtMSkgPT4ge1xuICBzZWxlY3RlZENvbXBvbmVudC52YWx1ZSA9IGNvbXBvbmVudFxuICBzZWxlY3RlZENvbXBvbmVudEluZGV4LnZhbHVlID0gaW5kZXhcbn1cblxuLy8g6YCJ5Lit6aG26YOo5a+86Iiq5qCPXG5jb25zdCBoYW5kbGVOYXZpZ2F0aW9uQmFyU2VsZWN0ZWQgPSAoKSA9PiB7XG4gIGhhbmRsZUNvbXBvbmVudFNlbGVjdGVkKHVucmVmKG5hdmlnYXRpb25CYXJDb21wb25lbnQpKVxufVxuXG4vLyDpgInkuK3lupXpg6jlr7zoiKroj5zljZVcbmNvbnN0IGhhbmRsZVRhYkJhclNlbGVjdGVkID0gKCkgPT4ge1xuICBoYW5kbGVDb21wb25lbnRTZWxlY3RlZCh1bnJlZih0YWJCYXJDb21wb25lbnQpKVxufVxuXG4vLyDnu4Tku7blj5jliqjvvIjmi5bmi73vvIlcbmNvbnN0IGhhbmRsZUNvbXBvbmVudENoYW5nZSA9IChkcmFnRXZlbnQ6IGFueSkgPT4ge1xuICAvLyDmlrDlop7vvIzljbPku47nu4Tku7blupPmi5bmi73mt7vliqDnu4Tku7ZcbiAgaWYgKGRyYWdFdmVudC5hZGRlZCkge1xuICAgIGNvbnN0IHsgZWxlbWVudCwgbmV3SW5kZXggfSA9IGRyYWdFdmVudC5hZGRlZFxuICAgIGhhbmRsZUNvbXBvbmVudFNlbGVjdGVkKGVsZW1lbnQsIG5ld0luZGV4KVxuICB9IGVsc2UgaWYgKGRyYWdFdmVudC5tb3ZlZCkge1xuICAgIC8vIOaLluaLveaOkuW6j1xuICAgIGNvbnN0IHsgbmV3SW5kZXggfSA9IGRyYWdFdmVudC5tb3ZlZFxuICAgIC8vIOS/neaMgemAieS4rVxuICAgIHNlbGVjdGVkQ29tcG9uZW50SW5kZXgudmFsdWUgPSBuZXdJbmRleFxuICB9XG59XG5cbi8vIOS6pOaNoue7hOS7tlxuY29uc3Qgc3dhcENvbXBvbmVudCA9IChvbGRJbmRleDogbnVtYmVyLCBuZXdJbmRleDogbnVtYmVyKSA9PiB7XG4gIDtbcGFnZUNvbXBvbmVudHMudmFsdWVbb2xkSW5kZXhdLCBwYWdlQ29tcG9uZW50cy52YWx1ZVtuZXdJbmRleF1dID0gW1xuICAgIHBhZ2VDb21wb25lbnRzLnZhbHVlW25ld0luZGV4XSxcbiAgICBwYWdlQ29tcG9uZW50cy52YWx1ZVtvbGRJbmRleF1cbiAgXVxuICAvLyDkv53mjIHpgInkuK1cbiAgc2VsZWN0ZWRDb21wb25lbnRJbmRleC52YWx1ZSA9IG5ld0luZGV4XG59XG5cbi8qKiDnp7vliqjnu4Tku7bvvIjkuIrnp7vjgIHkuIvnp7vvvIkgKi9cbmNvbnN0IGhhbmRsZU1vdmVDb21wb25lbnQgPSAoaW5kZXg6IG51bWJlciwgZGlyZWN0aW9uOiBudW1iZXIpID0+IHtcbiAgY29uc3QgbmV3SW5kZXggPSBpbmRleCArIGRpcmVjdGlvblxuICBpZiAobmV3SW5kZXggPCAwIHx8IG5ld0luZGV4ID49IHBhZ2VDb21wb25lbnRzLnZhbHVlLmxlbmd0aCkgcmV0dXJuXG5cbiAgc3dhcENvbXBvbmVudChpbmRleCwgbmV3SW5kZXgpXG59XG5cbi8qKiDlpI3liLbnu4Tku7YgKi9cbmNvbnN0IGhhbmRsZUNvcHlDb21wb25lbnQgPSAoaW5kZXg6IG51bWJlcikgPT4ge1xuICBjb25zdCBjb21wb25lbnQgPSBjbG9uZURlZXAocGFnZUNvbXBvbmVudHMudmFsdWVbaW5kZXhdKVxuICBjb21wb25lbnQudWlkID0gbmV3IERhdGUoKS5nZXRUaW1lKClcbiAgcGFnZUNvbXBvbmVudHMudmFsdWUuc3BsaWNlKGluZGV4ICsgMSwgMCwgY29tcG9uZW50KVxufVxuXG4vKipcbiAqIOWIoOmZpOe7hOS7tlxuICogQHBhcmFtIGluZGV4IOW9k+WJjee7hOS7tmluZGV4XG4gKi9cbmNvbnN0IGhhbmRsZURlbGV0ZUNvbXBvbmVudCA9IChpbmRleDogbnVtYmVyKSA9PiB7XG4gIC8vIOWIoOmZpOe7hOS7tlxuICBwYWdlQ29tcG9uZW50cy52YWx1ZS5zcGxpY2UoaW5kZXgsIDEpXG4gIGlmIChpbmRleCA8IHBhZ2VDb21wb25lbnRzLnZhbHVlLmxlbmd0aCkge1xuICAgIC8vIDEuIOS4jeaYr+acgOWQjuS4gOS4que7hOS7tuaXtu+8jOWIoOmZpOWQjumAieS4reS4i+mdoueahOe7hOS7tlxuICAgIGxldCBib3R0b21JbmRleCA9IGluZGV4XG4gICAgaGFuZGxlQ29tcG9uZW50U2VsZWN0ZWQocGFnZUNvbXBvbmVudHMudmFsdWVbYm90dG9tSW5kZXhdLCBib3R0b21JbmRleClcbiAgfSBlbHNlIGlmIChwYWdlQ29tcG9uZW50cy52YWx1ZS5sZW5ndGggPiAwKSB7XG4gICAgLy8gMi4g5LiN5piv56ys5LiA5Liq57uE5Lu25pe277yM5Yig6Zmk5ZCO6YCJ5Lit5LiK6Z2i55qE57uE5Lu2XG4gICAgbGV0IHRvcEluZGV4ID0gaW5kZXggLSAxXG4gICAgaGFuZGxlQ29tcG9uZW50U2VsZWN0ZWQocGFnZUNvbXBvbmVudHMudmFsdWVbdG9wSW5kZXhdLCB0b3BJbmRleClcbiAgfSBlbHNlIHtcbiAgICAvLyAzLiDnu4Tku7blhajpg6jliKDpmaTkuYvlkI7vvIzmmL7npLrpobXpnaLorr7nva5cbiAgICBoYW5kbGVDb21wb25lbnRTZWxlY3RlZCh1bnJlZihwYWdlQ29uZmlnQ29tcG9uZW50KSlcbiAgfVxufVxuXG4vLyDlt6XlhbfmoI/mk43kvZxcbmNvbnN0IGVtaXRzID0gZGVmaW5lRW1pdHMoWydyZXNldCcsICdwcmV2aWV3JywgJ3NhdmUnLCAndXBkYXRlOm1vZGVsVmFsdWUnXSlcblxuLy8g5rOo5YWl5peg5oSf5Yi35paw6aG16Z2i5Ye95pWwXG5jb25zdCByZWxvYWQgPSBpbmplY3Q8KCkgPT4gdm9pZD4oJ3JlbG9hZCcpXG4vLyDph43nva5cbmNvbnN0IGhhbmRsZVJlc2V0ID0gKCkgPT4ge1xuICBpZiAocmVsb2FkKSByZWxvYWQoKVxuICBlbWl0cygncmVzZXQnKVxufVxuXG4vLyDpooTop4hcbmNvbnN0IHByZXZpZXdEaWFsb2dWaXNpYmxlID0gcmVmKGZhbHNlKVxuY29uc3QgaGFuZGxlUHJldmlldyA9ICgpID0+IHtcbiAgcHJldmlld0RpYWxvZ1Zpc2libGUudmFsdWUgPSB0cnVlXG4gIGVtaXRzKCdwcmV2aWV3Jylcbn1cblxuLy8g6K6+572u6buY6K6k6YCJ5Lit55qE57uE5Lu2XG5jb25zdCBzZXREZWZhdWx0U2VsZWN0ZWRDb21wb25lbnQgPSAoKSA9PiB7XG4gIGlmIChwcm9wcy5zaG93UGFnZUNvbmZpZykge1xuICAgIHNlbGVjdGVkQ29tcG9uZW50LnZhbHVlID0gdW5yZWYocGFnZUNvbmZpZ0NvbXBvbmVudClcbiAgfSBlbHNlIGlmIChwcm9wcy5zaG93TmF2aWdhdGlvbkJhcikge1xuICAgIHNlbGVjdGVkQ29tcG9uZW50LnZhbHVlID0gdW5yZWYobmF2aWdhdGlvbkJhckNvbXBvbmVudClcbiAgfSBlbHNlIGlmIChwcm9wcy5zaG93VGFiQmFyKSB7XG4gICAgc2VsZWN0ZWRDb21wb25lbnQudmFsdWUgPSB1bnJlZih0YWJCYXJDb21wb25lbnQpXG4gIH1cbn1cblxud2F0Y2goXG4gICgpID0+IFtwcm9wcy5zaG93UGFnZUNvbmZpZywgcHJvcHMuc2hvd05hdmlnYXRpb25CYXIsIHByb3BzLnNob3dUYWJCYXJdLFxuICAoKSA9PiBzZXREZWZhdWx0U2VsZWN0ZWRDb21wb25lbnQoKVxuKVxuXG5vbk1vdW50ZWQoKCkgPT4gc2V0RGVmYXVsdFNlbGVjdGVkQ29tcG9uZW50KCkpXG48L3NjcmlwdD5cbjxzdHlsZSBsYW5nPVwic2Nzc1wiIHNjb3BlZD5cbi8qIOaJi+acuuWuveW6piAqL1xuJHBob25lLXdpZHRoOiAzNzVweDtcbiR0b29sYmFyLWhlaWdodDogNDJweDtcblxuLyog5qC56IqC54K55qC35byPICovXG4uZWRpdG9yIHtcbiAgZGlzcGxheTogZmxleDtcbiAgaGVpZ2h0OiAxMDAlO1xuICBtYXJnaW46IGNhbGMoMHB4IC0gdmFyKC0tYXBwLWNvbnRlbnQtcGFkZGluZykpO1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuXG4gIC8qIOmhtumDqO+8muW3peWFt+agjyAqL1xuICAuZWRpdG9yLWhlYWRlciB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBoZWlnaHQ6ICR0b29sYmFyLWhlaWdodDtcbiAgICBwYWRkaW5nOiAwO1xuICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWVsLWJnLWNvbG9yKTtcbiAgICBib3JkZXItYm90dG9tOiBzb2xpZCAxcHggdmFyKC0tZWwtYm9yZGVyLWNvbG9yKTtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcblxuICAgIC8qIOW3peWFt+agj++8muWPs+S+p+aMiemSriAqL1xuICAgIC5oZWFkZXItcmlnaHQge1xuICAgICAgaGVpZ2h0OiAxMDAlO1xuXG4gICAgICAuZWwtYnV0dG9uIHtcbiAgICAgICAgaGVpZ2h0OiAxMDAlO1xuICAgICAgfVxuICAgIH1cblxuICAgIC8qIOmakOiXj+W3peWFt+agj+aMiemSrueahOi+ueahhiAqL1xuICAgIDpkZWVwKC5lbC1yYWRpby1idXR0b25fX2lubmVyKSxcbiAgICA6ZGVlcCguZWwtYnV0dG9uKSB7XG4gICAgICBib3JkZXItdG9wOiBub25lICFpbXBvcnRhbnQ7XG4gICAgICBib3JkZXItYm90dG9tOiBub25lICFpbXBvcnRhbnQ7XG4gICAgICBib3JkZXItcmFkaXVzOiAwICFpbXBvcnRhbnQ7XG4gICAgfVxuICB9XG5cbiAgLyog5Lit5b+D5pON5L2c5Yy6ICovXG4gIC5lZGl0b3ItY29udGFpbmVyIHtcbiAgICBoZWlnaHQ6IGNhbGMoXG4gICAgICAxMDB2aCAtIHZhcigtLXRvcC10b29sLWhlaWdodCkgLSB2YXIoLS10YWdzLXZpZXctaGVpZ2h0KSAtIHZhcigtLWFwcC1mb290ZXItaGVpZ2h0KSAtXG4gICAgICAgICR0b29sYmFyLWhlaWdodFxuICAgICk7XG5cbiAgICAvKiDlj7PkvqflsZ7mgKfpnaLmnb8gKi9cbiAgICAuZWRpdG9yLXJpZ2h0IHtcbiAgICAgIG92ZXJmbG93OiBoaWRkZW47XG4gICAgICBib3gtc2hhZG93OiAtOHB4IDAgOHB4IC04cHggcmdiKDAgMCAwIC8gMTIlKTtcbiAgICAgIGZsZXgtc2hyaW5rOiAwO1xuXG4gICAgICAvKiDlsZ7mgKfpnaLmnb/pobbpg6jvvJrlh4/lsJHlhoXovrnot50gKi9cbiAgICAgIDpkZWVwKC5lbC1jYXJkX19oZWFkZXIpIHtcbiAgICAgICAgcGFkZGluZzogOHB4IDE2cHg7XG4gICAgICB9XG5cbiAgICAgIC8qIOWxnuaAp+mdouadv+WIhue7hCAqL1xuICAgICAgOmRlZXAoLnByb3BlcnR5LWdyb3VwKSB7XG4gICAgICAgIG1hcmdpbjogMCAtMjBweDtcblxuICAgICAgICAmLmVsLWNhcmQge1xuICAgICAgICAgIGJvcmRlcjogbm9uZTtcbiAgICAgICAgfVxuXG4gICAgICAgIC8qIOWxnuaAp+WIhue7hOWQjeensCAqL1xuICAgICAgICAuZWwtY2FyZF9faGVhZGVyIHtcbiAgICAgICAgICBwYWRkaW5nOiA4cHggMzJweDtcbiAgICAgICAgICBiYWNrZ3JvdW5kOiB2YXIoLS1lbC1iZy1jb2xvci1wYWdlKTtcbiAgICAgICAgICBib3JkZXI6IG5vbmU7XG4gICAgICAgIH1cblxuICAgICAgICAuZWwtY2FyZF9fYm9keSB7XG4gICAgICAgICAgYm9yZGVyOiBub25lO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgLyog5Lit5b+D5Yy65Z+fICovXG4gICAgLmVkaXRvci1jZW50ZXIge1xuICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIHdpZHRoOiAxMDAlO1xuICAgICAgbWFyZ2luOiAxNnB4IDAgMDtcbiAgICAgIG92ZXJmbG93OiBoaWRkZW47XG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1hcHAtY29udGVudC1iZy1jb2xvcik7XG4gICAgICBmbGV4OiAxIDEgMDtcbiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcblxuICAgICAgLyog5omL5py66aG26YOoICovXG4gICAgICAuZWRpdG9yLWRlc2lnbi10b3Age1xuICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICB3aWR0aDogJHBob25lLXdpZHRoO1xuICAgICAgICBtYXJnaW46IDAgYXV0bztcbiAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcblxuICAgICAgICAvKiDmiYvmnLrpobbpg6jnirbmgIHmoI8gKi9cbiAgICAgICAgLnN0YXR1cy1iYXIge1xuICAgICAgICAgIHdpZHRoOiAkcGhvbmUtd2lkdGg7XG4gICAgICAgICAgaGVpZ2h0OiAyMHB4O1xuICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmY7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLyog5omL5py65bqV6YOo5a+86IiqICovXG4gICAgICAuZWRpdG9yLWRlc2lnbi1ib3R0b20ge1xuICAgICAgICB3aWR0aDogJHBob25lLXdpZHRoO1xuICAgICAgICBtYXJnaW46IDAgYXV0bztcbiAgICAgIH1cblxuICAgICAgLyog5omL5py66aG16Z2i57yW6L6R5Yy65Z+fICovXG4gICAgICA6ZGVlcCguZWRpdG9yLWRlc2lnbi1jZW50ZXIpIHtcbiAgICAgICAgd2lkdGg6IDEwMCU7XG5cbiAgICAgICAgLyog5Li75L2T5YaF5a65ICovXG4gICAgICAgIC5waG9uZS1jb250YWluZXIge1xuICAgICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICAgICAgICB3aWR0aDogJHBob25lLXdpZHRoO1xuICAgICAgICAgIGhlaWdodDogMTAwJTtcbiAgICAgICAgICBtYXJnaW46IDAgYXV0bztcbiAgICAgICAgICBiYWNrZ3JvdW5kLXJlcGVhdDogbm8tcmVwZWF0O1xuICAgICAgICAgIGJhY2tncm91bmQtc2l6ZTogMTAwJSAxMDAlO1xuXG4gICAgICAgICAgLmRyYWctYXJlYSB7XG4gICAgICAgICAgICB3aWR0aDogMTAwJTtcbiAgICAgICAgICAgIGhlaWdodDogMTAwJTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLyog5Zu65a6a5biD5bGA55qE57uE5Lu2IOaTjeS9nOaMiemSruWMuiAqL1xuICAgICAgLmZpeGVkLWNvbXBvbmVudC1hY3Rpb24tZ3JvdXAge1xuICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gICAgICAgIHRvcDogMDtcbiAgICAgICAgcmlnaHQ6IDE2cHg7XG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICAgIGdhcDogOHB4O1xuXG4gICAgICAgIDpkZWVwKC5lbC10YWcpIHtcbiAgICAgICAgICBib3gtc2hhZG93OiAwIDJweCA4cHggMCByZ2JhKDAsIDAsIDAsIDAuMSk7XG4gICAgICAgICAgYm9yZGVyOiBub25lO1xuICAgICAgICAgIC5lbC10YWdfX2NvbnRlbnQge1xuICAgICAgICAgICAgd2lkdGg6IDEwMCU7XG4gICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgICAgIGp1c3RpZnktY29udGVudDogZmxleC1zdGFydDtcblxuICAgICAgICAgICAgLmVsLWljb24ge1xuICAgICAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDRweDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cbjwvc3R5bGU+XG4iLCJpbXBvcnQgeyByZWYsIFJlZiB9IGZyb20gJ3Z1ZSdcbmltcG9ydCB7IFBhZ2VDb25maWdQcm9wZXJ0eSB9IGZyb20gJ0AvY29tcG9uZW50cy9EaXlFZGl0b3IvY29tcG9uZW50cy9tb2JpbGUvUGFnZUNvbmZpZy9jb25maWcnXG5pbXBvcnQgeyBOYXZpZ2F0aW9uQmFyUHJvcGVydHkgfSBmcm9tICdAL2NvbXBvbmVudHMvRGl5RWRpdG9yL2NvbXBvbmVudHMvbW9iaWxlL05hdmlnYXRpb25CYXIvY29uZmlnJ1xuaW1wb3J0IHsgVGFiQmFyUHJvcGVydHkgfSBmcm9tICdAL2NvbXBvbmVudHMvRGl5RWRpdG9yL2NvbXBvbmVudHMvbW9iaWxlL1RhYkJhci9jb25maWcnXG5cbi8vIOmhtemdouijheS/rue7hOS7tlxuZXhwb3J0IGludGVyZmFjZSBEaXlDb21wb25lbnQ8VD4ge1xuICAvLyDnlKjkuo7ljLrliIblkIzkuIDnp43nu4Tku7bnmoTkuI3lkIzlrp7kvotcbiAgdWlkPzogbnVtYmVyXG4gIC8vIOe7hOS7tuWUr+S4gOagh+ivhlxuICBpZDogc3RyaW5nXG4gIC8vIOe7hOS7tuWQjeensFxuICBuYW1lOiBzdHJpbmdcbiAgLy8g57uE5Lu25Zu+5qCHXG4gIGljb246IHN0cmluZ1xuICAvKlxuICAg57uE5Lu25L2N572u77yaXG4gICB0b3A6IOWbuuWumuS6juaJi+acuumhtumDqO+8jOS+i+WmgiDpobbpg6jnmoTlr7zoiKrmoI9cbiAgIGJvdHRvbTog5Zu65a6a5LqO5omL5py65bqV6YOo77yM5L6L5aaCIOW6lemDqOeahOiPnOWNleWvvOiIquagj1xuICAgY2VudGVyOiDkvY3kuo7miYvmnLrkuK3lv4PvvIzmr4/kuKrnu4Tku7bljaDkuIDooYzvvIzpobrluo/lkJHkuIvmjpLliJdcbiAgIOepuu+8muWQjGNlbnRlclxuICAgZml4ZWQ6IOeUsee7hOS7tuiHquW3seWGs+WumuS9jee9ru+8jOWmguW8ueeql+S9jeS6juaJi+acuuS4reW/g+OAgea1ruWKqOaMiemSruS4gOiIrOS9jeS6juaJi+acuuWPs+S4i+inklxuICAqL1xuICBwb3NpdGlvbj86ICd0b3AnIHwgJ2JvdHRvbScgfCAnY2VudGVyJyB8ICcnIHwgJ2ZpeGVkJ1xuICAvLyDnu4Tku7blsZ7mgKdcbiAgcHJvcGVydHk6IFRcbn1cblxuLy8g6aG16Z2i6KOF5L+u57uE5Lu25bqTXG5leHBvcnQgaW50ZXJmYWNlIERpeUNvbXBvbmVudExpYnJhcnkge1xuICAvLyDnu4Tku7blupPlkI3np7BcbiAgbmFtZTogc3RyaW5nXG4gIC8vIOaYr+WQpuWxleW8gFxuICBleHRlbmRlZDogYm9vbGVhblxuICAvLyDnu4Tku7bliJfooahcbiAgY29tcG9uZW50czogc3RyaW5nW11cbn1cblxuLy8g57uE5Lu25qC35byPXG5leHBvcnQgaW50ZXJmYWNlIENvbXBvbmVudFN0eWxlIHtcbiAgLy8g6IOM5pmv57G75Z6LXG4gIGJnVHlwZTogJ2NvbG9yJyB8ICdpbWcnXG4gIC8vIOiDjOaZr+minOiJslxuICBiZ0NvbG9yOiBzdHJpbmdcbiAgLy8g6IOM5pmv5Zu+54mHXG4gIGJnSW1nOiBzdHJpbmdcbiAgLy8g5aSW6L656LedXG4gIG1hcmdpbjogbnVtYmVyXG4gIG1hcmdpblRvcDogbnVtYmVyXG4gIG1hcmdpblJpZ2h0OiBudW1iZXJcbiAgbWFyZ2luQm90dG9tOiBudW1iZXJcbiAgbWFyZ2luTGVmdDogbnVtYmVyXG4gIC8vIOWGhei+uei3nVxuICBwYWRkaW5nOiBudW1iZXJcbiAgcGFkZGluZ1RvcDogbnVtYmVyXG4gIHBhZGRpbmdSaWdodDogbnVtYmVyXG4gIHBhZGRpbmdCb3R0b206IG51bWJlclxuICBwYWRkaW5nTGVmdDogbnVtYmVyXG4gIC8vIOi+ueahhuWchuinklxuICBib3JkZXJSYWRpdXM6IG51bWJlclxuICBib3JkZXJUb3BMZWZ0UmFkaXVzOiBudW1iZXJcbiAgYm9yZGVyVG9wUmlnaHRSYWRpdXM6IG51bWJlclxuICBib3JkZXJCb3R0b21SaWdodFJhZGl1czogbnVtYmVyXG4gIGJvcmRlckJvdHRvbUxlZnRSYWRpdXM6IG51bWJlclxufVxuXG4vLyDpobXpnaLphY3nva5cbmV4cG9ydCBpbnRlcmZhY2UgUGFnZUNvbmZpZyB7XG4gIC8vIOmhtemdouWxnuaAp1xuICBwYWdlOiBQYWdlQ29uZmlnUHJvcGVydHlcbiAgLy8g6aG26YOo5a+86Iiq5qCP5bGe5oCnXG4gIG5hdmlnYXRpb25CYXI6IE5hdmlnYXRpb25CYXJQcm9wZXJ0eVxuICAvLyDlupXpg6jlr7zoiKroj5zljZXlsZ7mgKdcbiAgdGFiQmFyPzogVGFiQmFyUHJvcGVydHlcbiAgLy8g6aG16Z2i57uE5Lu25YiX6KGoXG4gIGNvbXBvbmVudHM6IFBhZ2VDb21wb25lbnRbXVxufVxuLy8g6aG16Z2i57uE5Lu277yM5Y+q5L+d55WZ57uE5Lu2SUTvvIznu4Tku7blsZ7mgKdcbmV4cG9ydCBpbnRlcmZhY2UgUGFnZUNvbXBvbmVudCBleHRlbmRzIFBpY2s8RGl5Q29tcG9uZW50PGFueT4sICdpZCcgfCAncHJvcGVydHknPiB7fVxuXG4vLyDlsZ7mgKfooajljZXnm5HlkKxcbmV4cG9ydCBmdW5jdGlvbiB1c2VQcm9wZXJ0eUZvcm08VD4obW9kZWxWYWx1ZTogVCwgZW1pdDogRnVuY3Rpb24pOiB7IGZvcm1EYXRhOiBSZWY8VD4gfSB7XG4gIGNvbnN0IGZvcm1EYXRhID0gcmVmPFQ+KClcbiAgLy8g55uR5ZCs5bGe5oCn5pWw5o2u5Y+Y5YqoXG4gIHdhdGNoKFxuICAgICgpID0+IG1vZGVsVmFsdWUsXG4gICAgKCkgPT4ge1xuICAgICAgZm9ybURhdGEudmFsdWUgPSBtb2RlbFZhbHVlXG4gICAgfSxcbiAgICB7XG4gICAgICBkZWVwOiB0cnVlLFxuICAgICAgaW1tZWRpYXRlOiB0cnVlXG4gICAgfVxuICApXG4gIC8vIOebkeWQrOihqOWNleaVsOaNruWPmOWKqFxuICB3YXRjaChcbiAgICAoKSA9PiBmb3JtRGF0YS52YWx1ZSxcbiAgICAoKSA9PiB7XG4gICAgICBlbWl0KCd1cGRhdGU6bW9kZWxWYWx1ZScsIGZvcm1EYXRhLnZhbHVlKVxuICAgIH0sXG4gICAge1xuICAgICAgZGVlcDogdHJ1ZVxuICAgIH1cbiAgKVxuXG4gIHJldHVybiB7IGZvcm1EYXRhIH0gYXMgeyBmb3JtRGF0YTogUmVmPFQ+IH1cbn1cblxuLy8g6aG16Z2i57uE5Lu25bqTXG5leHBvcnQgY29uc3QgUEFHRV9MSUJTID0gW1xuICB7XG4gICAgbmFtZTogJ+WfuuehgOe7hOS7ticsXG4gICAgZXh0ZW5kZWQ6IHRydWUsXG4gICAgY29tcG9uZW50czogW1xuICAgICAgJ1NlYXJjaEJhcicsXG4gICAgICAnTm90aWNlQmFyJyxcbiAgICAgICdNZW51U3dpcGVyJyxcbiAgICAgICdNZW51R3JpZCcsXG4gICAgICAnTWVudUxpc3QnLFxuICAgICAgJ1BvcG92ZXInLFxuICAgICAgJ0Zsb2F0aW5nQWN0aW9uQnV0dG9uJ1xuICAgIF1cbiAgfSxcbiAge1xuICAgIG5hbWU6ICflm77mlofnu4Tku7YnLFxuICAgIGV4dGVuZGVkOiB0cnVlLFxuICAgIGNvbXBvbmVudHM6IFtcbiAgICAgICdJbWFnZUJhcicsXG4gICAgICAnQ2Fyb3VzZWwnLFxuICAgICAgJ1RpdGxlQmFyJyxcbiAgICAgICdWaWRlb1BsYXllcicsXG4gICAgICAnRGl2aWRlcicsXG4gICAgICAnTWFnaWNDdWJlJyxcbiAgICAgICdIb3Rab25lJ1xuICAgIF1cbiAgfSxcbiAgeyBuYW1lOiAn5ZWG5ZOB57uE5Lu2JywgZXh0ZW5kZWQ6IHRydWUsIGNvbXBvbmVudHM6IFsnUHJvZHVjdENhcmQnLCAnUHJvZHVjdExpc3QnXSB9LFxuICB7XG4gICAgbmFtZTogJ+eUqOaIt+e7hOS7ticsXG4gICAgZXh0ZW5kZWQ6IHRydWUsXG4gICAgY29tcG9uZW50czogWydVc2VyQ2FyZCcsICdVc2VyT3JkZXInLCAnVXNlcldhbGxldCcsICdVc2VyQ291cG9uJ11cbiAgfSxcbiAge1xuICAgIG5hbWU6ICfokKXplIDnu4Tku7YnLFxuICAgIGV4dGVuZGVkOiB0cnVlLFxuICAgIGNvbXBvbmVudHM6IFtcbiAgICAgICdQcm9tb3Rpb25Db21iaW5hdGlvbicsXG4gICAgICAnUHJvbW90aW9uU2Vja2lsbCcsXG4gICAgICAnUHJvbW90aW9uUG9pbnQnLFxuICAgICAgJ0NvdXBvbkNhcmQnLFxuICAgICAgJ1Byb21vdGlvbkFydGljbGUnXG4gICAgXVxuICB9XG5dIGFzIERpeUNvbXBvbmVudExpYnJhcnlbXVxuIl0sImZpbGUiOiJhc3NldHMvdXRpbC1EeXA4Nkd2Mi5qcyJ9