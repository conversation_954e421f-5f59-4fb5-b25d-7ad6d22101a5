import{d as h,I,b6 as v,r as U,f as w,o as x,l as j,w as r,g as B,aV as E,i as F,j as k,a as t,N as z,bw as H,B as N,__tla as P}from"./index-BUSn51wb.js";import{b as S,a as V,U as $,H as q,__tla as A}from"./upload-DyVf7G_u.js";import{U as i,__tla as C}from"./useUpload-gjof4KYU.js";let d,D=Promise.all([(()=>{try{return P}catch{}})(),(()=>{try{return A}catch{}})(),(()=>{try{return C}catch{}})()]).then(async()=>{let l;l={class:"el-upload__tip",style:{"margin-left":"5px"}},d=N(h({__name:"UploadFile",props:{type:{}},emits:["uploaded"],setup(n,{emit:p}){const e=I(),_=n,c=v("accountId"),o=U([]),u=p,s=w({type:i.Image,title:"",introduction:"",accountId:c}),m=_.type===i.Image?S:V,f=a=>{if(a.code!==0)return e.alertError("\u4E0A\u4F20\u51FA\u9519\uFF1A"+a.msg),!1;o.value=[],s.title="",s.introduction="",e.notifySuccess("\u4E0A\u4F20\u6210\u529F"),u("uploaded")},y=a=>e.error("\u4E0A\u4F20\u5931\u8D25: "+a.message);return(a,G)=>{const b=z,g=H;return x(),j(g,{action:t($),headers:t(q),multiple:"",limit:1,"file-list":t(o),data:t(s),"on-error":y,"before-upload":t(m),"on-success":f},{tip:r(()=>[B("span",l,[E(a.$slots,"default",{},void 0,!0)])]),default:r(()=>[F(b,{type:"primary",plain:""},{default:r(()=>[k(" \u70B9\u51FB\u4E0A\u4F20 ")]),_:1})]),_:3},8,["action","headers","file-list","data","before-upload"])}}}),[["__scopeId","data-v-bd722126"]])});export{D as __tla,d as default};
