import{_ as t,__tla as _}from"./EvaluationForm.vue_vue_type_script_setup_true_lang-BT_r-VVc.js";import{__tla as a}from"./index-BUSn51wb.js";import{__tla as r}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as l}from"./index-BGdxPkF_.js";let e=Promise.all([(()=>{try{return _}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return r}catch{}})(),(()=>{try{return l}catch{}})()]).then(async()=>{});export{e as __tla,t as default};
