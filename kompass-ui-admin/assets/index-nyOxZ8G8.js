import{d as y,o as r,c as s,i as a,w as l,F as d,k as m,l as h,g as f,t as g,av as v,q as k,_ as C,__tla as b}from"./index-BUSn51wb.js";import{E as w,a as E,__tla as j}from"./el-carousel-item-D3JjuyEq.js";import{E as q,__tla as B}from"./el-image-BjHZRFih.js";let o,F=Promise.all([(()=>{try{return b}catch{}})(),(()=>{try{return j}catch{}})(),(()=>{try{return B}catch{}})()]).then(async()=>{let e;e={class:"h-24px truncate leading-24px"},o=y({name:"NoticeBar",__name:"index",props:{property:{}},setup:N=>(t,P)=>{const c=q,p=k,n=w,_=E,i=C;return r(),s("div",{class:"flex items-center p-y-4px text-12px",style:v({backgroundColor:t.property.backgroundColor,color:t.property.textColor})},[a(c,{src:t.property.iconUrl,class:"h-18px"},null,8,["src"]),a(p,{direction:"vertical"}),a(_,{height:"24px",direction:"vertical",autoplay:!0,class:"flex-1 p-r-8px"},{default:l(()=>[(r(!0),s(d,null,m(t.property.contents,(u,x)=>(r(),h(n,{key:x},{default:l(()=>[f("div",e,g(u.text),1)]),_:2},1024))),128))]),_:1}),a(i,{icon:"ep:arrow-right"})],4)}})});export{F as __tla,o as default};
