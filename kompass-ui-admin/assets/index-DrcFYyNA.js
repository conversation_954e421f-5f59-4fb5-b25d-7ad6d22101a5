import{by as t,__tla as l}from"./index-BUSn51wb.js";let e,d=Promise.all([(()=>{try{return l}catch{}})()]).then(async()=>{e={getChatModelPage:async a=>await t.get({url:"/ai/chat-model/page",params:a}),getChatModelSimpleList:async a=>await t.get({url:"/ai/chat-model/simple-list",params:{status:a}}),getChatModel:async a=>await t.get({url:"/ai/chat-model/get?id="+a}),createChatModel:async a=>await t.post({url:"/ai/chat-model/create",data:a}),updateChatModel:async a=>await t.put({url:"/ai/chat-model/update",data:a}),deleteChatModel:async a=>await t.delete({url:"/ai/chat-model/delete?id="+a})}});export{e as C,d as __tla};
