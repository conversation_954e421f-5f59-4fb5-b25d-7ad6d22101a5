import{_ as E,__tla as M}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{d as S,r as o,o as i,l as p,w as t,i as l,j as s,t as u,a,G as d,a9 as g,y as x,__tla as A}from"./index-BUSn51wb.js";import{E as D,a as I,__tla as O}from"./el-descriptions-item-dD3qa0ub.js";import{_ as P,__tla as V}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{f,__tla as Y}from"./formatTime-DWdBpgsM.js";let T,j=Promise.all([(()=>{try{return M}catch{}})(),(()=>{try{return A}catch{}})(),(()=>{try{return O}catch{}})(),(()=>{try{return V}catch{}})(),(()=>{try{return Y}catch{}})()]).then(async()=>{T=S({name:"MyNotifyMessageDetailDetail",__name:"MyNotifyMessageDetail",setup(k,{expose:v}){const r=o(!1),m=o(!1),e=o({});return v({open:async n=>{r.value=!0,m.value=!0;try{e.value=n}finally{m.value=!1}}}),(n,y)=>{const _=D,c=P,b=I,h=E;return i(),p(h,{modelValue:a(r),"onUpdate:modelValue":y[0]||(y[0]=N=>x(r)?r.value=N:null),"max-height":500,scroll:!0,title:"\u6D88\u606F\u8BE6\u60C5"},{default:t(()=>[l(b,{column:1,border:""},{default:t(()=>[l(_,{label:"\u53D1\u9001\u4EBA"},{default:t(()=>[s(u(a(e).templateNickname),1)]),_:1}),l(_,{label:"\u53D1\u9001\u65F6\u95F4"},{default:t(()=>[s(u(a(f)(a(e).createTime)),1)]),_:1}),l(_,{label:"\u6D88\u606F\u7C7B\u578B"},{default:t(()=>[l(c,{type:a(d).SYSTEM_NOTIFY_TEMPLATE_TYPE,value:a(e).templateType},null,8,["type","value"])]),_:1}),l(_,{label:"\u662F\u5426\u5DF2\u8BFB"},{default:t(()=>[l(c,{type:a(d).INFRA_BOOLEAN_STRING,value:a(e).readStatus},null,8,["type","value"])]),_:1}),a(e).readStatus?(i(),p(_,{key:0,label:"\u9605\u8BFB\u65F6\u95F4"},{default:t(()=>[s(u(a(f)(a(e).readTime)),1)]),_:1})):g("",!0),l(_,{label:"\u5185\u5BB9"},{default:t(()=>[s(u(a(e).templateContent),1)]),_:1})]),_:1})]),_:1},8,["modelValue"])}}})});export{T as _,j as __tla};
