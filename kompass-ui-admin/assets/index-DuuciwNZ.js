import{d as T,b5 as U,p as V,r as k,at as J,o as v,c as d,g as K,F as M,k as $,a0 as W,av as L,i as N,a as A,a9 as X,j as Y,t as Z,_ as ee,B as te,__tla as ae}from"./index-BUSn51wb.js";let O,le=Promise.all([(()=>{try{return ae}catch{}})()]).then(async()=>{let w,j,q,B,E,F;w=(i,x)=>{const[t,a]=[i.x,x.x].sort(),[l,r]=[i.y,x.y].sort(),p=a+1,f=r+1;return{left:t,right:p,top:l,bottom:f,height:f-l,width:p-t}},j={class:"relative"},q={class:"cube-table"},B=["onClick","onMouseenter"],E=["onClick"],F=["onClick"],O=te(T({name:"MagicCubeEditor",__name:"index",props:{modelValue:U().isRequired,rows:V.number.def(4),cols:V.number.def(4),cubeSize:V.number.def(75)},emits:["update:modelValue","hotAreaSelected"],setup(i,{emit:x}){const t=i,a=k([]);J(()=>[t.rows,t.cols],()=>{if(a.value=[],t.rows&&t.cols)for(let o=0;o<t.rows;o++){a.value[o]=[];for(let e=0;e<t.cols;e++)a.value[o].push({x:e,y:o,active:!1})}},{immediate:!0});const l=k([]);J(()=>t.modelValue,()=>l.value=t.modelValue||[],{immediate:!0});const r=k(),p=()=>!!r.value,f=x,I=()=>f("update:modelValue",l),P=k(0),R=(o,e)=>{P.value=e,f("hotAreaSelected",o,e)};function y(){D((o,e,s)=>{s.active&&(s.active=!1)}),r.value=void 0}const D=o=>{for(let e=0;e<a.value.length;e++)for(let s=0;s<a.value[e].length;s++)o(e,s,a.value[e][s])};return(o,e)=>{const s=ee;return v(),d("div",j,[K("table",q,[K("tbody",null,[(v(!0),d(M,null,$(A(a),(u,n)=>(v(),d("tr",{key:n},[(v(!0),d(M,null,$(u,(S,m)=>(v(),d("td",{key:m,class:W(["cube",{active:S.active}]),style:L({width:`${i.cubeSize}px`,height:`${i.cubeSize}px`}),onClick:Q=>((C,z)=>{const b=a.value[C][z];if(!p())return r.value=b,void(r.value.active=!0);l.value.push(w(r.value,b)),y();let c=l.value.length-1;R(l.value[c],c),I()})(n,m),onMouseenter:Q=>((C,z)=>{if(!p())return;const b=w(r.value,a.value[C][z]);for(const G of l.value)if(h=b,(c=G).left<h.left+h.width&&c.left+c.width>h.left&&c.top<h.top+h.height&&c.height+c.top>h.top)return void y();var c,h;D((G,oe,H)=>{var g,_;H.active=(g=b,(_=H).x>=g.left&&_.x<g.right&&_.y>=g.top&&_.y<g.bottom)})})(n,m)},[N(s,{icon:"ep-plus"})],46,B))),128))]))),128))]),(v(!0),d(M,null,$(A(l),(u,n)=>(v(),d("div",{key:n,class:"hot-area",style:L({top:i.cubeSize*u.top+"px",left:i.cubeSize*u.left+"px",height:i.cubeSize*u.height+"px",width:i.cubeSize*u.width+"px"}),onClick:S=>R(u,n),onMouseover:y},[A(P)===n?(v(),d("div",{key:0,class:"btn-delete",onClick:S=>(m=>{l.value.splice(m,1),y(),I()})(n)},[N(s,{icon:"ep:circle-close-filled"})],8,F)):X("",!0),Y(" "+Z(`${u.width}\xD7${u.height}`),1)],44,E))),128))])])}}}),[["__scopeId","data-v-1a498d3a"]])});export{O as _,le as __tla};
