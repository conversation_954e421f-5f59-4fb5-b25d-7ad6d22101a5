import{d as H,r as u,f as Q,at as V,T as q,o as s,c as A,i as a,w as i,j as _,H as f,a as l,l as g,dV as W,F as B,I as E,n as G,_ as J,N as K,s as M,P as O,Q as X,R as Y,__tla as Z}from"./index-BUSn51wb.js";import{_ as $,__tla as aa}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as ea,__tla as ta}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{g as ra,d as la,__tla as oa}from"./index-Uo5NQqNb.js";import{_ as ca,__tla as ia}from"./ReceivablePlanForm.vue_vue_type_script_setup_true_lang-BzUD040F.js";import{b as x,__tla as na}from"./formatTime-DWdBpgsM.js";let N,sa=Promise.all([(()=>{try{return Z}catch{}})(),(()=>{try{return aa}catch{}})(),(()=>{try{return ta}catch{}})(),(()=>{try{return oa}catch{}})(),(()=>{try{return ia}catch{}})(),(()=>{try{return na}catch{}})()]).then(async()=>{N=H({name:"CrmReceivablePlanList",__name:"ReceivablePlanList",props:{customerId:{},contractId:{}},emits:["createReceivable"],setup(P,{emit:R}){const e=P,b=E(),{t:S}=G(),v=u(!0),w=u(0),y=u([]),t=Q({pageNo:1,pageSize:10,customerId:void 0,contractId:void 0}),d=async()=>{v.value=!0;try{e.customerId&&!e.contractId?t.customerId=e.customerId:e.customerId&&e.contractId&&(t.customerId=e.customerId,t.contractId=e.contractId);const n=await ra(t);y.value=n.list,w.value=n.total}finally{v.value=!1}},h=u(),k=(n,c)=>{h.value.open(n,c,e.customerId,e.contractId)},z=R;return V(()=>[e.customerId,e.contractId],n=>{n[0]&&(t.pageNo=1,t.customerId=void 0,t.contractId=void 0,d())},{immediate:!0,deep:!0}),(n,c)=>{const T=J,p=K,U=M,r=O,j=X,L=ea,D=$,I=q("hasPermi"),F=Y;return s(),A(B,null,[a(U,{justify:"end"},{default:i(()=>[a(p,{onClick:c[0]||(c[0]=o=>k("create",void 0))},{default:i(()=>[a(T,{class:"mr-5px",icon:"icon-park:income"}),_(" \u521B\u5EFA\u56DE\u6B3E\u8BA1\u5212 ")]),_:1})]),_:1}),a(D,{class:"mt-10px"},{default:i(()=>[f((s(),g(j,{data:l(y),"show-overflow-tooltip":!0,stripe:!0},{default:i(()=>[a(r,{align:"center",label:"\u5BA2\u6237\u540D\u79F0",prop:"customerName",width:"150px"}),a(r,{align:"center",label:"\u5408\u540C\u7F16\u53F7",prop:"contractNo",width:"200px"}),a(r,{align:"center",label:"\u671F\u6570",prop:"period"}),a(r,{align:"center",label:"\u8BA1\u5212\u56DE\u6B3E(\u5143)",prop:"price",width:"120",formatter:l(W)},null,8,["formatter"]),a(r,{formatter:l(x),align:"center",label:"\u8BA1\u5212\u56DE\u6B3E\u65E5\u671F",prop:"returnTime",width:"180px"},null,8,["formatter"]),a(r,{align:"center",label:"\u63D0\u524D\u51E0\u5929\u63D0\u9192",prop:"remindDays",width:"150"}),a(r,{formatter:l(x),align:"center",label:"\u63D0\u9192\u65E5\u671F",prop:"remindTime",width:"180px"},null,8,["formatter"]),a(r,{label:"\u8D1F\u8D23\u4EBA",prop:"ownerUserName",width:"120"}),a(r,{align:"center",label:"\u5907\u6CE8",prop:"remark"}),a(r,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"200px"},{default:i(o=>[f((s(),g(p,{link:"",type:"primary",onClick:C=>{return m=o.row,void z("createReceivable",m);var m},disabled:o.row.receivableId},{default:i(()=>[_(" \u521B\u5EFA\u56DE\u6B3E ")]),_:2},1032,["onClick","disabled"])),[[I,["crm:receivable:create"]]]),f((s(),g(p,{link:"",type:"primary",onClick:C=>k("update",o.row.id)},{default:i(()=>[_(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[I,["crm:receivable-plan:update"]]]),f((s(),g(p,{link:"",type:"danger",onClick:C=>(async m=>{try{await b.delConfirm(),await la(m),b.success(S("common.delSuccess")),await d()}catch{}})(o.row.id)},{default:i(()=>[_(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[I,["crm:receivable-plan:delete"]]])]),_:1})]),_:1},8,["data"])),[[F,l(v)]]),a(L,{limit:l(t).pageSize,"onUpdate:limit":c[1]||(c[1]=o=>l(t).pageSize=o),page:l(t).pageNo,"onUpdate:page":c[2]||(c[2]=o=>l(t).pageNo=o),total:l(w),onPagination:d},null,8,["limit","page","total"])]),_:1}),a(ca,{ref_key:"formRef",ref:h,onSuccess:d},null,512)],64)}}})});export{N as _,sa as __tla};
