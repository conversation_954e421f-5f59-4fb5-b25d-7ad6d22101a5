import{_ as H,__tla as K}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{d as $,n as G,I as Q,r as c,C as P,au as q,T as W,o as E,c as S,i as a,w as l,g as f,j as d,a as s,t as X,H as Y,a9 as Z,y as aa,F as ta,ep as ea,e5 as la,b8 as sa,N as ra,E as oa,s as na,b2 as ua,__tla as ia}from"./index-BUSn51wb.js";import{_ as ca,__tla as _a}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{u as ma,__tla as pa}from"./useFormCreateDesigner-C9MJ3oSM.js";import{H as y,__tla as fa}from"./index-DCh5hX9K.js";import{j as da}from"./java-CqGtcfkc.js";import{__tla as ga}from"./el-card-CJbXGyyg.js";import{__tla as ya}from"./dict.type-7eDXjvul.js";let M,va=Promise.all([(()=>{try{return K}catch{}})(),(()=>{try{return ia}catch{}})(),(()=>{try{return _a}catch{}})(),(()=>{try{return pa}catch{}})(),(()=>{try{return fa}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return ya}catch{}})()]).then(async()=>{function b(n){const r=["true","false","null"],_={scope:"literal",beginKeywords:r.join(" ")};return{name:"JSON",keywords:{literal:r},contains:[{className:"attr",begin:/"(\\.|[^\\"\r\n])*"(?=\s*:)/,relevance:1.01},{match:/[{}[\],:]/,className:"punctuation",relevance:0},n.QUOTE_STRING_MODE,_,n.C_NUMBER_MODE,n.C_LINE_COMMENT_MODE,n.C_BLOCK_COMMENT_MODE],illegal:"\\S"}}let v,h,O;v={class:"float-right mb-2"},h={key:0,ref:"editor"},O={class:"hljs"},M=$({name:"InfraBuild",__name:"index",setup(n){const{t:r}=G(),_=Q(),o=c(),u=c(!1),C=c(""),m=c(-1),i=c("");ma(o);const g=t=>{u.value=!0,C.value=t},J=()=>{g("\u751F\u6210 JSON"),m.value=0,i.value=o.value.getRule()},k=()=>{g("\u751F\u6210 Options"),m.value=1,i.value=o.value.getOption()},D=()=>{g("\u751F\u6210\u7EC4\u4EF6"),m.value=2,i.value=j()},j=()=>{const t=o.value.getRule(),e=o.value.getOption();return`<template>
    <form-create
      v-model:api="fApi"
      :rule="rule"
      :option="option"
      @submit="onSubmit"
    ></form-create>
  </template>
  <script setup lang=ts>
    const faps = ref(null)
    const rule = ref('')
    const option = ref('')
    const init = () => {
      rule.value = formCreate.parseJson('${ea.toJson(t).replaceAll("\\","\\\\")}')
      option.value = formCreate.parseJson('${JSON.stringify(e)}')
    }
    const onSubmit = (formData) => {
      //todo \u63D0\u4EA4\u8868\u5355
    }
    init()
  <\/script>`},x=t=>{let e="json";return m.value===2&&(e="xml"),sa(t)||(t=JSON.stringify(t)),y.highlight(e,t,!0).value||"&nbsp;"};return P(async()=>{y.registerLanguage("xml",da),y.registerLanguage("json",b)}),(t,e)=>{const p=ra,L=oa,T=na,w=q("FcDesigner"),I=ca,R=ua,z=H,B=W("dompurify-html");return E(),S(ta,null,[a(I,null,{default:l(()=>[a(T,null,{default:l(()=>[a(L,null,{default:l(()=>[f("div",v,[a(p,{size:"small",type:"primary",onClick:J},{default:l(()=>[d("\u751F\u6210 JSON")]),_:1}),a(p,{size:"small",type:"success",onClick:k},{default:l(()=>[d("\u751F\u6210 Options")]),_:1}),a(p,{size:"small",type:"danger",onClick:D},{default:l(()=>[d("\u751F\u6210\u7EC4\u4EF6")]),_:1})])]),_:1})]),_:1}),a(w,{ref_key:"designer",ref:o,height:"780px"},null,512)]),_:1}),a(z,{modelValue:s(u),"onUpdate:modelValue":e[1]||(e[1]=N=>aa(u)?u.value=N:null),title:s(C),"max-height":"600"},{default:l(()=>[s(u)?(E(),S("div",h,[a(p,{style:{float:"right"},onClick:e[0]||(e[0]=N=>(async U=>{const{copy:V,copied:A,isSupported:F}=la({source:U});F?(await V(),s(A)&&_.success(r("common.copySuccess"))):_.error(r("common.copyError"))})(s(i)))},{default:l(()=>[d(X(s(r)("common.copy")),1)]),_:1}),a(R,{height:"580"},{default:l(()=>[f("div",null,[f("pre",null,[Y(f("code",O,null,512),[[B,x(s(i))]])])])]),_:1})],512)):Z("",!0)]),_:1},8,["modelValue","title"])],64)}}})});export{va as __tla,M as default};
