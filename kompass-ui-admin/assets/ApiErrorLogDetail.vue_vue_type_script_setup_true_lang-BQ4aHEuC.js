import{_ as U,__tla as A}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{d as E,r as p,o as d,l as c,w as l,i as t,j as _,t as s,a as e,G as b,a9 as i,y as R,Z as k,__tla as P}from"./index-BUSn51wb.js";import{E as V,a as g,__tla as w}from"./el-descriptions-item-dD3qa0ub.js";import{_ as q,__tla as L}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{f as T,__tla as N}from"./formatTime-DWdBpgsM.js";let h,O=Promise.all([(()=>{try{return A}catch{}})(),(()=>{try{return P}catch{}})(),(()=>{try{return w}catch{}})(),(()=>{try{return L}catch{}})(),(()=>{try{return N}catch{}})()]).then(async()=>{h=E({name:"ApiErrorLogDetail",__name:"ApiErrorLogDetail",setup(D,{expose:v}){const u=p(!1),m=p(!1),a=p({});return v({open:async f=>{u.value=!0,m.value=!0;try{a.value=f}finally{m.value=!1}}}),(f,o)=>{const r=V,y=q,x=k,S=g,I=U;return d(),c(I,{modelValue:e(u),"onUpdate:modelValue":o[1]||(o[1]=n=>R(u)?u.value=n:null),"max-height":500,scroll:!0,title:"\u8BE6\u60C5",width:"800"},{default:l(()=>[t(S,{column:1,border:""},{default:l(()=>[t(r,{label:"\u65E5\u5FD7\u4E3B\u952E","min-width":"120"},{default:l(()=>[_(s(e(a).id),1)]),_:1}),t(r,{label:"\u94FE\u8DEF\u8FFD\u8E2A"},{default:l(()=>[_(s(e(a).traceId),1)]),_:1}),t(r,{label:"\u5E94\u7528\u540D"},{default:l(()=>[_(s(e(a).applicationName),1)]),_:1}),t(r,{label:"\u7528\u6237\u7F16\u53F7"},{default:l(()=>[_(s(e(a).userId)+" ",1),t(y,{type:e(b).USER_TYPE,value:e(a).userType},null,8,["type","value"])]),_:1}),t(r,{label:"\u7528\u6237 IP"},{default:l(()=>[_(s(e(a).userIp),1)]),_:1}),t(r,{label:"\u7528\u6237 UA"},{default:l(()=>[_(s(e(a).userAgent),1)]),_:1}),t(r,{label:"\u8BF7\u6C42\u4FE1\u606F"},{default:l(()=>[_(s(e(a).requestMethod)+" "+s(e(a).requestUrl),1)]),_:1}),t(r,{label:"\u8BF7\u6C42\u53C2\u6570"},{default:l(()=>[_(s(e(a).requestParams),1)]),_:1}),t(r,{label:"\u5F02\u5E38\u65F6\u95F4"},{default:l(()=>[_(s(e(T)(e(a).exceptionTime)),1)]),_:1}),t(r,{label:"\u5F02\u5E38\u540D"},{default:l(()=>[_(s(e(a).exceptionName),1)]),_:1}),e(a).exceptionStackTrace?(d(),c(r,{key:0,label:"\u5F02\u5E38\u5806\u6808"},{default:l(()=>[t(x,{modelValue:e(a).exceptionStackTrace,"onUpdate:modelValue":o[0]||(o[0]=n=>e(a).exceptionStackTrace=n),autosize:{maxRows:20},readonly:!0,type:"textarea"},null,8,["modelValue"])]),_:1})):i("",!0),t(r,{label:"\u5904\u7406\u72B6\u6001"},{default:l(()=>[t(y,{type:e(b).INFRA_API_ERROR_LOG_PROCESS_STATUS,value:e(a).processStatus},null,8,["type","value"])]),_:1}),e(a).processUserId?(d(),c(r,{key:1,label:"\u5904\u7406\u4EBA"},{default:l(()=>[_(s(e(a).processUserId),1)]),_:1})):i("",!0),e(a).processTime?(d(),c(r,{key:2,label:"\u5904\u7406\u65F6\u95F4"},{default:l(()=>[_(s(e(T)(e(a).processTime)),1)]),_:1})):i("",!0)]),_:1})]),_:1},8,["modelValue"])}}})});export{h as _,O as __tla};
