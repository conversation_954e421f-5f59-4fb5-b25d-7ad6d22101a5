import{d as H,r as f,f as J,u as K,bc as M,C as O,T as Q,o as d,c as T,i as e,w as l,a as t,F as P,k as X,l as h,H as D,j as s,t as c,dV as b,dX as E,G as W,g as Y,J as Z,K as $,L as ee,O as ae,P as te,v as re,N as le,Q as ie,R as ne,__tla as oe}from"./index-BUSn51wb.js";import{_ as pe,__tla as se}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as me,__tla as de}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as ce,__tla as ue}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{b as y,d as v,__tla as _e}from"./formatTime-DWdBpgsM.js";import{e as fe,__tla as we}from"./index-DrB1WZUR.js";import{C as ge}from"./common-BQQO87UM.js";let U,he=Promise.all([(()=>{try{return oe}catch{}})(),(()=>{try{return se}catch{}})(),(()=>{try{return de}catch{}})(),(()=>{try{return ue}catch{}})(),(()=>{try{return _e}catch{}})(),(()=>{try{return we}catch{}})()]).then(async()=>{let C;C=Y("div",{class:"pb-5 text-xl"}," \u5373\u5C06\u5230\u671F\u7684\u5408\u540C ",-1),U=H({__name:"ContractRemindList",setup(be){const w=f(!0),x=f(0),k=f([]),n=J({pageNo:1,pageSize:10,sceneType:"1",expiryType:1}),I=f(),u=async()=>{w.value=!0;try{const g=await fe(n);k.value=g.list,x.value=g.total}finally{w.value=!1}},R=()=>{n.pageNo=1,u()},{push:m}=K();return M(async()=>{await u()}),O(()=>{u()}),(g,o)=>{const S=Z,L=$,V=ee,z=ae,N=ce,r=te,_=re,B=me,q=le,A=ie,F=pe,j=Q("hasPermi"),G=ne;return d(),T(P,null,[e(N,null,{default:l(()=>[C,e(z,{class:"-mb-15px",model:t(n),ref_key:"queryFormRef",ref:I,inline:!0,"label-width":"68px"},{default:l(()=>[e(V,{label:"\u5230\u671F\u72B6\u6001",prop:"expiryType"},{default:l(()=>[e(L,{modelValue:t(n).expiryType,"onUpdate:modelValue":o[0]||(o[0]=a=>t(n).expiryType=a),class:"!w-240px",placeholder:"\u72B6\u6001",onChange:R},{default:l(()=>[(d(!0),T(P,null,X(t(ge),(a,p)=>(d(),h(S,{label:a.label,value:a.value,key:p},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),e(N,null,{default:l(()=>[D((d(),h(A,{data:t(k),"show-overflow-tooltip":!0,stripe:!0},{default:l(()=>[e(r,{align:"center",fixed:"left",label:"\u5408\u540C\u7F16\u53F7",prop:"no",width:"180"}),e(r,{align:"center",fixed:"left",label:"\u5408\u540C\u540D\u79F0",prop:"name",width:"160"},{default:l(a=>[e(_,{underline:!1,type:"primary",onClick:p=>{return i=a.row.id,void m({name:"CrmContractDetail",params:{id:i}});var i}},{default:l(()=>[s(c(a.row.name),1)]),_:2},1032,["onClick"])]),_:1}),e(r,{align:"center",label:"\u5BA2\u6237\u540D\u79F0",prop:"customerName",width:"120"},{default:l(a=>[e(_,{underline:!1,type:"primary",onClick:p=>{return i=a.row.customerId,void m({name:"CrmCustomerDetail",params:{id:i}});var i}},{default:l(()=>[s(c(a.row.customerName),1)]),_:2},1032,["onClick"])]),_:1}),e(r,{align:"center",label:"\u5546\u673A\u540D\u79F0",prop:"businessName",width:"130"},{default:l(a=>[e(_,{underline:!1,type:"primary",onClick:p=>{return i=a.row.businessId,void m({name:"CrmBusinessDetail",params:{id:i}});var i}},{default:l(()=>[s(c(a.row.businessName),1)]),_:2},1032,["onClick"])]),_:1}),e(r,{align:"center",label:"\u5408\u540C\u91D1\u989D\uFF08\u5143\uFF09",prop:"totalPrice",width:"140",formatter:t(b)},null,8,["formatter"]),e(r,{align:"center",label:"\u4E0B\u5355\u65F6\u95F4",prop:"orderDate",width:"120",formatter:t(y)},null,8,["formatter"]),e(r,{align:"center",label:"\u5408\u540C\u5F00\u59CB\u65F6\u95F4",prop:"startTime",width:"120",formatter:t(y)},null,8,["formatter"]),e(r,{align:"center",label:"\u5408\u540C\u7ED3\u675F\u65F6\u95F4",prop:"endTime",width:"120",formatter:t(y)},null,8,["formatter"]),e(r,{align:"center",label:"\u5BA2\u6237\u7B7E\u7EA6\u4EBA",prop:"contactName",width:"130"},{default:l(a=>[e(_,{underline:!1,type:"primary",onClick:p=>{return i=a.row.signContactId,void m({name:"CrmContactDetail",params:{id:i}});var i}},{default:l(()=>[s(c(a.row.signContactName),1)]),_:2},1032,["onClick"])]),_:1}),e(r,{align:"center",label:"\u516C\u53F8\u7B7E\u7EA6\u4EBA",prop:"signUserName",width:"130"}),e(r,{align:"center",label:"\u5907\u6CE8",prop:"remark",width:"200"}),e(r,{align:"center",label:"\u5DF2\u56DE\u6B3E\u91D1\u989D\uFF08\u5143\uFF09",prop:"totalReceivablePrice",width:"140",formatter:t(b)},null,8,["formatter"]),e(r,{align:"center",label:"\u672A\u56DE\u6B3E\u91D1\u989D\uFF08\u5143\uFF09",prop:"totalReceivablePrice",width:"140",formatter:t(b)},{default:l(a=>[s(c(t(E)(a.row.totalPrice-a.row.totalReceivablePrice)),1)]),_:1},8,["formatter"]),e(r,{formatter:t(v),align:"center",label:"\u6700\u540E\u8DDF\u8FDB\u65F6\u95F4",prop:"contactLastTime",width:"180px"},null,8,["formatter"]),e(r,{align:"center",label:"\u8D1F\u8D23\u4EBA",prop:"ownerUserName",width:"120"}),e(r,{align:"center",label:"\u6240\u5C5E\u90E8\u95E8",prop:"ownerUserDeptName",width:"100px"}),e(r,{formatter:t(v),align:"center",label:"\u66F4\u65B0\u65F6\u95F4",prop:"updateTime",width:"180px"},null,8,["formatter"]),e(r,{formatter:t(v),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"]),e(r,{align:"center",label:"\u521B\u5EFA\u4EBA",prop:"creatorName",width:"120"}),e(r,{align:"center",fixed:"right",label:"\u5408\u540C\u72B6\u6001",prop:"auditStatus",width:"120"},{default:l(a=>[e(B,{type:t(W).CRM_AUDIT_STATUS,value:a.row.auditStatus},null,8,["type","value"])]),_:1}),e(r,{fixed:"right",label:"\u64CD\u4F5C",width:"90"},{default:l(a=>[D((d(),h(q,{link:"",type:"primary",onClick:p=>{return i=a.row,void m({name:"BpmProcessInstanceDetail",query:{id:i.processInstanceId}});var i}},{default:l(()=>[s(" \u67E5\u770B\u5BA1\u6279 ")]),_:2},1032,["onClick"])),[[j,["crm:contract:update"]]])]),_:1})]),_:1},8,["data"])),[[G,t(w)]]),e(F,{limit:t(n).pageSize,"onUpdate:limit":o[1]||(o[1]=a=>t(n).pageSize=a),page:t(n).pageNo,"onUpdate:page":o[2]||(o[2]=a=>t(n).pageNo=a),total:t(x),onPagination:u},null,8,["limit","page","total"])]),_:1})],64)}}})});export{U as _,he as __tla};
