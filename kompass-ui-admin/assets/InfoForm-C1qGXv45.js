import{_ as t,__tla as r}from"./InfoForm.vue_vue_type_script_setup_true_lang-DK__qetF.js";import{__tla as _}from"./index-BUSn51wb.js";import"./tree-BMa075Oj.js";import{__tla as a}from"./category-WzWM3ODe.js";import{__tla as l}from"./brand-DwnGZI23.js";import{__tla as o}from"./formRules-CA9eXdcX.js";let m=Promise.all([(()=>{try{return r}catch{}})(),(()=>{try{return _}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})()]).then(async()=>{});export{m as __tla,t as default};
