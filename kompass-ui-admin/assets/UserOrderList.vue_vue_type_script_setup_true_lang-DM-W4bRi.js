import{d as Q,u as X,r as _,f as Z,C as B,o as d,c as i,i as t,w as r,a,F as n,k as v,l as p,V as b,G as y,dR as W,a9 as g,U as $,H as q,j as x,J as ee,K as ae,L as le,M as te,Z as ue,_ as re,N as de,O as oe,Q as pe,R as se,a8 as ie,__tla as ne}from"./index-BUSn51wb.js";import{_ as ce,__tla as me}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as _e,__tla as ve}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{e as ye,__tla as fe}from"./index-BQq32Shw.js";import{a as be,__tla as Ve}from"./index-BmYfnmm4.js";import{g as he,__tla as ke}from"./index-H6D82e8c.js";import Ue,{__tla as we}from"./OrderTableColumn-AjIStixC.js";import{D as I}from"./constants-A8BI3pz7.js";let O,Ce=Promise.all([(()=>{try{return ne}catch{}})(),(()=>{try{return me}catch{}})(),(()=>{try{return ve}catch{}})(),(()=>{try{return fe}catch{}})(),(()=>{try{return Ve}catch{}})(),(()=>{try{return ke}catch{}})(),(()=>{try{return we}catch{}})()]).then(async()=>{O=Q({__name:"UserOrderList",props:{userId:{}},setup(L){const{push:M}=X(),{userId:P}=L,V=_(!0),E=_(0),h=_([]),k=_([]),D=_([]),R=_(),l=_({pageNo:1,pageSize:10,userId:P,status:void 0,payChannelCode:void 0,createTime:void 0,terminal:void 0,type:void 0,deliveryType:void 0,logisticsId:void 0,pickUpStoreId:void 0,pickUpVerifyCode:void 0}),f=Z({queryParam:""}),S=_([{value:"no",label:"\u8BA2\u5355\u53F7"},{value:"userNickname",label:"\u7528\u6237\u6635\u79F0"},{value:"userMobile",label:"\u7528\u6237\u7535\u8BDD"}]),H=c=>{var u;(u=S.value.filter(o=>o.value!==c))==null||u.forEach(o=>{l.value.hasOwnProperty(o.value)&&delete l.value[o.value]})},U=async()=>{l.value.pageNo=1,await w()},K=()=>{var c;(c=R.value)==null||c.resetFields(),l.value.userId=P,U()},w=async()=>{V.value=!0;try{const c=await ye(l.value);h.value=c.list,E.value=c.total}finally{V.value=!1}};return B(async()=>{await w(),k.value=await be(),D.value=await he()}),(c,u)=>{const o=ee,m=ae,s=le,z=te,N=ue,C=re,T=de,F=oe,A=_e,j=pe,G=ce,J=se;return d(),i(n,null,[t(A,null,{default:r(()=>[t(F,{ref_key:"queryFormRef",ref:R,inline:!0,model:a(l),class:"-mb-15px","label-width":"68px"},{default:r(()=>[t(s,{label:"\u8BA2\u5355\u72B6\u6001",prop:"status"},{default:r(()=>[t(m,{modelValue:a(l).status,"onUpdate:modelValue":u[0]||(u[0]=e=>a(l).status=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:r(()=>[(d(!0),i(n,null,v(a(b)(a(y).TRADE_ORDER_STATUS),e=>(d(),p(o,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(s,{label:"\u652F\u4ED8\u65B9\u5F0F",prop:"payChannelCode"},{default:r(()=>[t(m,{modelValue:a(l).payChannelCode,"onUpdate:modelValue":u[1]||(u[1]=e=>a(l).payChannelCode=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:r(()=>[(d(!0),i(n,null,v(a(W)(a(y).PAY_CHANNEL_CODE),e=>(d(),p(o,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(s,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:r(()=>[t(z,{modelValue:a(l).createTime,"onUpdate:modelValue":u[2]||(u[2]=e=>a(l).createTime=e),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-280px","end-placeholder":"\u81EA\u5B9A\u4E49\u65F6\u95F4","start-placeholder":"\u81EA\u5B9A\u4E49\u65F6\u95F4",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),t(s,{label:"\u8BA2\u5355\u6765\u6E90",prop:"terminal"},{default:r(()=>[t(m,{modelValue:a(l).terminal,"onUpdate:modelValue":u[3]||(u[3]=e=>a(l).terminal=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:r(()=>[(d(!0),i(n,null,v(a(b)(a(y).TERMINAL),e=>(d(),p(o,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(s,{label:"\u8BA2\u5355\u7C7B\u578B",prop:"type"},{default:r(()=>[t(m,{modelValue:a(l).type,"onUpdate:modelValue":u[4]||(u[4]=e=>a(l).type=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:r(()=>[(d(!0),i(n,null,v(a(b)(a(y).TRADE_ORDER_TYPE),e=>(d(),p(o,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(s,{label:"\u914D\u9001\u65B9\u5F0F",prop:"deliveryType"},{default:r(()=>[t(m,{modelValue:a(l).deliveryType,"onUpdate:modelValue":u[5]||(u[5]=e=>a(l).deliveryType=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:r(()=>[(d(!0),i(n,null,v(a(b)(a(y).TRADE_DELIVERY_TYPE),e=>(d(),p(o,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(l).deliveryType===a(I).EXPRESS.type?(d(),p(s,{key:0,label:"\u5FEB\u9012\u516C\u53F8",prop:"logisticsId"},{default:r(()=>[t(m,{modelValue:a(l).logisticsId,"onUpdate:modelValue":u[6]||(u[6]=e=>a(l).logisticsId=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:r(()=>[(d(!0),i(n,null,v(a(D),e=>(d(),p(o,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):g("",!0),a(l).deliveryType===a(I).PICK_UP.type?(d(),p(s,{key:1,label:"\u81EA\u63D0\u95E8\u5E97",prop:"pickUpStoreId"},{default:r(()=>[t(m,{modelValue:a(l).pickUpStoreId,"onUpdate:modelValue":u[7]||(u[7]=e=>a(l).pickUpStoreId=e),class:"!w-280px",clearable:"",multiple:"",placeholder:"\u5168\u90E8"},{default:r(()=>[(d(!0),i(n,null,v(a(k),e=>(d(),p(o,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):g("",!0),a(l).deliveryType===a(I).PICK_UP.type?(d(),p(s,{key:2,label:"\u6838\u9500\u7801",prop:"pickUpVerifyCode"},{default:r(()=>[t(N,{modelValue:a(l).pickUpVerifyCode,"onUpdate:modelValue":u[8]||(u[8]=e=>a(l).pickUpVerifyCode=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u81EA\u63D0\u6838\u9500\u7801",onKeyup:$(U,["enter"])},null,8,["modelValue"])]),_:1})):g("",!0),t(s,{label:"\u805A\u5408\u641C\u7D22"},{default:r(()=>[q(t(N,{modelValue:a(l)[a(f).queryParam],"onUpdate:modelValue":u[10]||(u[10]=e=>a(l)[a(f).queryParam]=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165"},{prepend:r(()=>[t(m,{modelValue:a(f).queryParam,"onUpdate:modelValue":u[9]||(u[9]=e=>a(f).queryParam=e),class:"!w-110px",clearable:"",placeholder:"\u5168\u90E8",onChange:H},{default:r(()=>[(d(!0),i(n,null,v(a(S),e=>(d(),p(o,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["modelValue"]),[[ie,!0]])]),_:1}),t(s,null,{default:r(()=>[t(T,{onClick:U},{default:r(()=>[t(C,{class:"mr-5px",icon:"ep:search"}),x(" \u641C\u7D22 ")]),_:1}),t(T,{onClick:K},{default:r(()=>[t(C,{class:"mr-5px",icon:"ep:refresh"}),x(" \u91CD\u7F6E ")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),t(A,null,{default:r(()=>[q((d(),p(j,{data:a(h),"row-key":"id"},{default:r(()=>[t(a(Ue),{list:a(h),"pick-up-store-list":a(k)},{default:r(({row:e})=>[t(T,{link:"",type:"primary",onClick:Te=>{return Y=e.id,void M({name:"TradeOrderDetail",params:{id:Y}});var Y}},{default:r(()=>[t(C,{icon:"ep:notification"}),x(" \u8BE6\u60C5 ")]),_:2},1032,["onClick"])]),_:1},8,["list","pick-up-store-list"])]),_:1},8,["data"])),[[J,a(V)]]),t(G,{limit:a(l).pageSize,"onUpdate:limit":u[11]||(u[11]=e=>a(l).pageSize=e),page:a(l).pageNo,"onUpdate:page":u[12]||(u[12]=e=>a(l).pageNo=e),total:a(E),onPagination:w},null,8,["limit","page","total"])]),_:1})],64)}}})});export{O as _,Ce as __tla};
