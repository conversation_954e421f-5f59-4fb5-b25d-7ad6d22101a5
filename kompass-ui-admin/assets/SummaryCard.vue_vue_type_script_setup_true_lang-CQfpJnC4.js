import{_ as u,__tla as m}from"./CountTo.vue_vue_type_script_setup_true_lang-BUNK7cmG.js";import{d as o,p as r,o as _,c,g as e,t as d,i as p,__tla as f}from"./index-BUSn51wb.js";let i,x=Promise.all([(()=>{try{return m}catch{}})(),(()=>{try{return f}catch{}})()]).then(async()=>{let a,t,l;a={class:"flex flex-col gap-2 bg-[var(--el-bg-color-overlay)] p-6"},t={class:"flex items-center justify-between text-gray-500"},l={class:"flex flex-row items-baseline justify-between"},i=o({name:"ErpSummaryCard",__name:"SummaryCard",props:{title:r.string.def("").isRequired,value:r.number.def(0).isRequired},setup:s=>(v,y)=>{const n=u;return _(),c("div",a,[e("div",t,[e("span",null,d(s.title),1)]),e("div",l,[p(n,{prefix:"\uFFE5","end-val":s.value,decimals:2,duration:500,class:"text-3xl"},null,8,["end-val"])])])}})});export{i as _,x as __tla};
