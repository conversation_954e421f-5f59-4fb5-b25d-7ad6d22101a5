import{d as T,r as p,f as x,C as z,o as m,l as d,w as n,H as P,a,i as l,j as g,t as f,aF as w,P as S,Q as L,R as U,__tla as W}from"./index-BUSn51wb.js";import{_ as q,__tla as C}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as F,__tla as H}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{d as Q,__tla as R}from"./formatTime-DWdBpgsM.js";import{g as k,__tla as A}from"./index-CPcKnf_r.js";let y,B=Promise.all([(()=>{try{return W}catch{}})(),(()=>{try{return C}catch{}})(),(()=>{try{return H}catch{}})(),(()=>{try{return R}catch{}})(),(()=>{try{return A}catch{}})()]).then(async()=>{y=T({name:"WalletTransactionList",__name:"WalletTransactionList",props:{walletId:{type:Number,required:!1}},setup(b){const{walletId:h}=b,s=p(!0),_=p(0),e=x({pageNo:1,pageSize:10,walletId:null}),c=p([]),u=async()=>{s.value=!0;try{e.walletId=h;const i=await k(e);c.value=i.list,_.value=i.total}finally{s.value=!1}};return z(()=>{u()}),(i,o)=>{const r=S,v=L,I=F,N=q,j=U;return m(),d(N,null,{default:n(()=>[P((m(),d(v,{data:a(c),stripe:!0,"show-overflow-tooltip":!0},{default:n(()=>[l(r,{label:"\u7F16\u53F7",align:"center",prop:"id"}),l(r,{label:"\u94B1\u5305\u7F16\u53F7",align:"center",prop:"walletId"}),l(r,{label:"\u5173\u8054\u4E1A\u52A1\u6807\u9898",align:"center",prop:"title"}),l(r,{label:"\u4EA4\u6613\u91D1\u989D",align:"center",prop:"price"},{default:n(({row:t})=>[g(f(a(w)(t.price))+" \u5143",1)]),_:1}),l(r,{label:"\u94B1\u5305\u4F59\u989D",align:"center",prop:"balance"},{default:n(({row:t})=>[g(f(a(w)(t.balance))+" \u5143",1)]),_:1}),l(r,{label:"\u4EA4\u6613\u65F6\u95F4",align:"center",prop:"createTime",formatter:a(Q),width:"180px"},null,8,["formatter"])]),_:1},8,["data"])),[[j,a(s)]]),l(I,{total:a(_),page:a(e).pageNo,"onUpdate:page":o[0]||(o[0]=t=>a(e).pageNo=t),limit:a(e).pageSize,"onUpdate:limit":o[1]||(o[1]=t=>a(e).pageSize=t),onPagination:u},null,8,["total","page","limit"])]),_:1})}}})});export{y as _,B as __tla};
