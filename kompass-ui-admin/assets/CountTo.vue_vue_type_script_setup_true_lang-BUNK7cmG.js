import{d as q,p as r,f as k,bj as E,C as P,b as $,at as j,a as o,o as v,c as w,t as M,a0 as N,a4 as R,bl as z,__tla as B}from"./index-BUSn51wb.js";let D,G=Promise.all([(()=>{try{return B}catch{}})()]).then(async()=>{D=q({name:"CountTo",__name:"CountTo",props:{startVal:r.number.def(0),endVal:r.number.def(2021),duration:r.number.def(3e3),autoplay:r.bool.def(!0),decimals:r.number.validate(i=>i>=0).def(0),decimal:r.string.def("."),separator:r.string.def(","),prefix:r.string.def(""),suffix:r.string.def(""),useEasing:r.bool.def(!0),easingFn:{type:Function,default:(i,p,m,V)=>m*(1-Math.pow(2,-10*i/V))*1024/1023+p}},emits:["mounted","callback"],setup(i,{expose:p,emit:m}){const{getPrefixCls:V}=R(),T=V("count-to"),t=i,S=m,d=e=>{const{decimals:s,decimal:u,separator:l,suffix:n,prefix:C}=t;e=Number(e).toFixed(s);const b=(e+="").split(".");let c=b[0];const h=b.length>1?u+b[1]:"",A=/(\d+)(\d{3})/;if(l&&!z(l))for(;A.test(c);)c=c.replace(A,"$1"+l+"$2");return C+c+h+n},a=k({localStartVal:t.startVal,displayValue:d(t.startVal),printVal:null,paused:!1,localDuration:t.duration,startTime:null,timestamp:null,remaining:null,rAF:null}),_=E(a,"displayValue");P(()=>{t.autoplay&&F(),S("mounted")});const f=$(()=>t.startVal>t.endVal);j([()=>t.startVal,()=>t.endVal],()=>{t.autoplay&&F()});const F=()=>{const{startVal:e,duration:s}=t;a.localStartVal=e,a.startTime=null,a.localDuration=s,a.paused=!1,a.rAF=requestAnimationFrame(g)},y=()=>{cancelAnimationFrame(a.rAF)},x=()=>{a.startTime=null,a.localDuration=+a.remaining,a.localStartVal=+a.printVal,requestAnimationFrame(g)},g=e=>{const{useEasing:s,easingFn:u,endVal:l}=t;a.startTime||(a.startTime=e),a.timestamp=e;const n=e-a.startTime;a.remaining=a.localDuration-n,s?o(f)?a.printVal=a.localStartVal-u(n,0,a.localStartVal-l,a.localDuration):a.printVal=u(n,a.localStartVal,l-a.localStartVal,a.localDuration):o(f)?a.printVal=a.localStartVal-(a.localStartVal-l)*(n/a.localDuration):a.printVal=a.localStartVal+(l-a.localStartVal)*(n/a.localDuration),o(f)?a.printVal=a.printVal<l?l:a.printVal:a.printVal=a.printVal>l?l:a.printVal,a.displayValue=d(a.printVal),n<a.localDuration?a.rAF=requestAnimationFrame(g):S("callback")};return p({pauseResume:()=>{a.paused?(x(),a.paused=!1):(y(),a.paused=!0)},reset:()=>{a.startTime=null,cancelAnimationFrame(a.rAF),a.displayValue=d(t.startVal)},start:F,pause:y}),(e,s)=>(v(),w("span",{class:N(o(T))},M(o(_)),3))}})});export{D as _,G as __tla};
