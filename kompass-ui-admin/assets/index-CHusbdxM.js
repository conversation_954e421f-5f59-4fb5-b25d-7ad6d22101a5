import{d as L,I as Q,n as Z,u as D,r as i,f as J,at as W,C as X,au as Y,T as $,o as u,c as aa,i as a,w as e,a as t,U as ea,j as m,H as c,l as d,G as ta,y as la,F as ra,Z as oa,L as na,_ as sa,N as _a,O as ia,P as ua,Q as ma,R as pa,__tla as ca}from"./index-BUSn51wb.js";import{_ as da,__tla as fa}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{_ as ya,__tla as ha}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as ba,__tla as ga}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as ka,__tla as wa}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as Ca,__tla as va}from"./index-COobLwz-.js";import{d as xa,__tla as Na}from"./formatTime-DWdBpgsM.js";import{b as Sa,d as Ua,g as Va,__tla as Fa}from"./index-COJ8hy-t.js";import{b as Pa,__tla as Ta}from"./formCreate-DDLxm5B5.js";import{__tla as qa}from"./index-Cch5e1V0.js";import"./color-BN7ZL7BD.js";import{__tla as za}from"./el-card-CJbXGyyg.js";let T,Oa=Promise.all([(()=>{try{return ca}catch{}})(),(()=>{try{return fa}catch{}})(),(()=>{try{return ha}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return wa}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return Na}catch{}})(),(()=>{try{return Fa}catch{}})(),(()=>{try{return Ta}catch{}})(),(()=>{try{return qa}catch{}})(),(()=>{try{return za}catch{}})()]).then(async()=>{T=L({name:"BpmForm",__name:"index",setup(Ra){const C=Q(),{t:q}=Z(),{currentRoute:z,push:O}=D(),h=i(!0),v=i(0),x=i([]),o=J({pageNo:1,pageSize:10,name:null}),N=i(),p=async()=>{h.value=!0;try{const n=await Sa(o);x.value=n.list,v.value=n.total}finally{h.value=!1}},b=()=>{o.pageNo=1,p()},R=()=>{N.value.resetFields(),b()},S=n=>{const r={name:"BpmFormEditor"};typeof n=="number"&&(r.query={id:n}),O(r)},f=i(!1),g=i({rule:[],option:{}});return W(()=>z.value,()=>{p()},{immediate:!0}),X(()=>{p()}),(n,r)=>{const A=Ca,B=oa,U=na,k=sa,s=_a,G=ia,V=ka,_=ua,H=ba,M=ma,j=ya,E=Y("form-create"),I=da,y=$("hasPermi"),K=pa;return u(),aa(ra,null,[a(A,{title:"\u5BA1\u6279\u63A5\u5165\uFF08\u6D41\u7A0B\u8868\u5355\uFF09",url:"https://doc.iocoder.cn/bpm/use-bpm-form/"}),a(V,null,{default:e(()=>[a(G,{ref_key:"queryFormRef",ref:N,inline:!0,model:t(o),class:"-mb-15px","label-width":"68px"},{default:e(()=>[a(U,{label:"\u8868\u5355\u540D",prop:"name"},{default:e(()=>[a(B,{modelValue:t(o).name,"onUpdate:modelValue":r[0]||(r[0]=l=>t(o).name=l),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u8868\u5355\u540D",onKeyup:ea(b,["enter"])},null,8,["modelValue"])]),_:1}),a(U,null,{default:e(()=>[a(s,{onClick:b},{default:e(()=>[a(k,{class:"mr-5px",icon:"ep:search"}),m(" \u641C\u7D22 ")]),_:1}),a(s,{onClick:R},{default:e(()=>[a(k,{class:"mr-5px",icon:"ep:refresh"}),m(" \u91CD\u7F6E ")]),_:1}),c((u(),d(s,{plain:"",type:"primary",onClick:S},{default:e(()=>[a(k,{class:"mr-5px",icon:"ep:plus"}),m(" \u65B0\u589E ")]),_:1})),[[y,["bpm:form:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(V,null,{default:e(()=>[c((u(),d(M,{data:t(x)},{default:e(()=>[a(_,{align:"center",label:"\u7F16\u53F7",prop:"id"}),a(_,{align:"center",label:"\u8868\u5355\u540D",prop:"name"}),a(_,{align:"center",label:"\u72B6\u6001",prop:"status"},{default:e(l=>[a(H,{type:t(ta).COMMON_STATUS,value:l.row.status},null,8,["type","value"])]),_:1}),a(_,{align:"center",label:"\u5907\u6CE8",prop:"remark"}),a(_,{formatter:t(xa),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},null,8,["formatter"]),a(_,{align:"center",label:"\u64CD\u4F5C"},{default:e(l=>[c((u(),d(s,{link:"",type:"primary",onClick:F=>S(l.row.id)},{default:e(()=>[m(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[y,["bpm:form:update"]]]),c((u(),d(s,{link:"",onClick:F=>(async w=>{const P=await Va(w);Pa(g,P.conf,P.fields),f.value=!0})(l.row.id)},{default:e(()=>[m(" \u8BE6\u60C5 ")]),_:2},1032,["onClick"])),[[y,["bpm:form:query"]]]),c((u(),d(s,{link:"",type:"danger",onClick:F=>(async w=>{try{await C.delConfirm(),await Ua(w),C.success(q("common.delSuccess")),await p()}catch{}})(l.row.id)},{default:e(()=>[m(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[y,["bpm:form:delete"]]])]),_:1})]),_:1},8,["data"])),[[K,t(h)]]),a(j,{limit:t(o).pageSize,"onUpdate:limit":r[1]||(r[1]=l=>t(o).pageSize=l),page:t(o).pageNo,"onUpdate:page":r[2]||(r[2]=l=>t(o).pageNo=l),total:t(v),onPagination:p},null,8,["limit","page","total"])]),_:1}),a(I,{modelValue:t(f),"onUpdate:modelValue":r[3]||(r[3]=l=>la(f)?f.value=l:null),title:"\u8868\u5355\u8BE6\u60C5",width:"800"},{default:e(()=>[a(E,{option:t(g).option,rule:t(g).rule},null,8,["option","rule"])]),_:1},8,["modelValue"])],64)}}})});export{Oa as __tla,T as default};
