import{d as le,I as re,n as oe,r as m,f as ne,u as ie,C as ce,T as se,o as s,c as K,i as e,w as t,a as l,U as L,F as M,k as pe,l as d,j as c,H as _,y as ue,t as U,dV as q,G as B,Z as me,L as de,J as _e,K as fe,_ as ye,N as we,O as he,z as be,A as ve,v as ge,P as ke,Q as Ce,R as xe,__tla as Te}from"./index-BUSn51wb.js";import{_ as Ne,__tla as Ie}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as Ue,__tla as Ve}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as Re,__tla as Se}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as Pe,__tla as De}from"./index-COobLwz-.js";import{b as Ee,d as J,__tla as ze}from"./formatTime-DWdBpgsM.js";import{d as Ae}from"./download-e0EdwhTv.js";import{b as Fe,d as Ke,s as Le,f as Me,__tla as qe}from"./index-D3Ji6shA.js";import{_ as Be,__tla as Je}from"./ReceivableForm.vue_vue_type_script_setup_true_lang-37IhTDRa.js";import{a as Qe,__tla as je}from"./index-CD52sTBY.js";import{__tla as Ge}from"./index-Cch5e1V0.js";import"./color-BN7ZL7BD.js";import{__tla as He}from"./el-card-CJbXGyyg.js";import{__tla as Oe}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as We}from"./index-Uo5NQqNb.js";import{__tla as Ye}from"./index-BYXzDB8j.js";import{__tla as Ze}from"./index-DrB1WZUR.js";let Q,$e=Promise.all([(()=>{try{return Te}catch{}})(),(()=>{try{return Ie}catch{}})(),(()=>{try{return Ve}catch{}})(),(()=>{try{return Se}catch{}})(),(()=>{try{return De}catch{}})(),(()=>{try{return ze}catch{}})(),(()=>{try{return qe}catch{}})(),(()=>{try{return Je}catch{}})(),(()=>{try{return je}catch{}})(),(()=>{try{return Ge}catch{}})(),(()=>{try{return He}catch{}})(),(()=>{try{return Oe}catch{}})(),(()=>{try{return We}catch{}})(),(()=>{try{return Ye}catch{}})(),(()=>{try{return Ze}catch{}})()]).then(async()=>{Q=le({name:"Receivable",__name:"index",setup(Xe){const h=re(),{t:j}=oe(),k=m(!0),V=m(0),R=m([]),i=ne({pageNo:1,pageSize:10,sceneType:"1",no:void 0,customerId:void 0}),S=m(),C=m(!1),x=m("1"),P=m([]),G=p=>{i.sceneType=p.paneName,b()},f=async()=>{k.value=!0;try{const p=await Fe(i);R.value=p.list,V.value=p.total}finally{k.value=!1}},b=()=>{i.pageNo=1,f()},H=()=>{S.value.resetFields(),b()},D=m(),E=(p,n)=>{D.value.open(p,n)},{push:v}=ie(),O=async()=>{try{await h.exportConfirm(),C.value=!0;const p=await Me(i);Ae.excel(p,"\u56DE\u6B3E.xls")}catch{}finally{C.value=!1}};return ce(async()=>{await f(),P.value=await Qe()}),(p,n)=>{const z=Pe,W=me,T=de,Y=_e,Z=fe,g=ye,u=we,$=he,A=Re,N=be,X=ve,I=ge,o=ke,F=Ue,ee=Ce,ae=Ne,y=se("hasPermi"),te=xe;return s(),K(M,null,[e(z,{title:"\u3010\u56DE\u6B3E\u3011\u56DE\u6B3E\u7BA1\u7406\u3001\u56DE\u6B3E\u8BA1\u5212",url:"https://doc.iocoder.cn/crm/receivable/"}),e(z,{title:"\u3010\u901A\u7528\u3011\u6570\u636E\u6743\u9650",url:"https://doc.iocoder.cn/crm/permission/"}),e(A,null,{default:t(()=>[e($,{ref_key:"queryFormRef",ref:S,inline:!0,model:l(i),class:"-mb-15px","label-width":"68px"},{default:t(()=>[e(T,{label:"\u56DE\u6B3E\u7F16\u53F7",prop:"no"},{default:t(()=>[e(W,{modelValue:l(i).no,"onUpdate:modelValue":n[0]||(n[0]=a=>l(i).no=a),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u56DE\u6B3E\u7F16\u53F7",onKeyup:L(b,["enter"])},null,8,["modelValue"])]),_:1}),e(T,{label:"\u5BA2\u6237\u540D\u79F0",prop:"customerId"},{default:t(()=>[e(Z,{modelValue:l(i).customerId,"onUpdate:modelValue":n[1]||(n[1]=a=>l(i).customerId=a),class:"!w-240px",placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237",onKeyup:L(b,["enter"])},{default:t(()=>[(s(!0),K(M,null,pe(l(P),a=>(s(),d(Y,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(T,null,{default:t(()=>[e(u,{onClick:b},{default:t(()=>[e(g,{class:"mr-5px",icon:"ep:search"}),c(" \u641C\u7D22 ")]),_:1}),e(u,{onClick:H},{default:t(()=>[e(g,{class:"mr-5px",icon:"ep:refresh"}),c(" \u91CD\u7F6E ")]),_:1}),_((s(),d(u,{plain:"",type:"primary",onClick:n[2]||(n[2]=a=>E("create"))},{default:t(()=>[e(g,{class:"mr-5px",icon:"ep:plus"}),c(" \u65B0\u589E ")]),_:1})),[[y,["crm:receivable:create"]]]),_((s(),d(u,{loading:l(C),plain:"",type:"success",onClick:O},{default:t(()=>[e(g,{class:"mr-5px",icon:"ep:download"}),c(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[y,["crm:receivable:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(A,null,{default:t(()=>[e(X,{modelValue:l(x),"onUpdate:modelValue":n[3]||(n[3]=a=>ue(x)?x.value=a:null),onTabClick:G},{default:t(()=>[e(N,{label:"\u6211\u8D1F\u8D23\u7684",name:"1"}),e(N,{label:"\u6211\u53C2\u4E0E\u7684",name:"2"}),e(N,{label:"\u4E0B\u5C5E\u8D1F\u8D23\u7684",name:"3"})]),_:1},8,["modelValue"]),_((s(),d(ee,{data:l(R),"show-overflow-tooltip":!0,stripe:!0},{default:t(()=>[e(o,{align:"center",fixed:"left",label:"\u56DE\u6B3E\u7F16\u53F7",prop:"no",width:"180"},{default:t(a=>[e(I,{underline:!1,type:"primary",onClick:w=>{return r=a.row.id,void v({name:"CrmReceivableDetail",params:{id:r}});var r}},{default:t(()=>[c(U(a.row.no),1)]),_:2},1032,["onClick"])]),_:1}),e(o,{align:"center",label:"\u5BA2\u6237\u540D\u79F0",prop:"customerName",width:"120"},{default:t(a=>[e(I,{underline:!1,type:"primary",onClick:w=>{return r=a.row.customerId,void v({name:"CrmCustomerDetail",params:{id:r}});var r}},{default:t(()=>[c(U(a.row.customerName),1)]),_:2},1032,["onClick"])]),_:1}),e(o,{align:"center",label:"\u5408\u540C\u7F16\u53F7",prop:"contractNo",width:"180"},{default:t(a=>[e(I,{underline:!1,type:"primary",onClick:w=>{return r=a.row.contractId,void v({name:"CrmContractDetail",params:{id:r}});var r}},{default:t(()=>[c(U(a.row.contract.no),1)]),_:2},1032,["onClick"])]),_:1}),e(o,{formatter:l(Ee),align:"center",label:"\u56DE\u6B3E\u65E5\u671F",prop:"returnTime",width:"150px"},null,8,["formatter"]),e(o,{align:"center",label:"\u56DE\u6B3E\u91D1\u989D(\u5143)",prop:"price",width:"140",formatter:l(q)},null,8,["formatter"]),e(o,{align:"center",label:"\u56DE\u6B3E\u65B9\u5F0F",prop:"returnType",width:"130px"},{default:t(a=>[e(F,{type:l(B).CRM_RECEIVABLE_RETURN_TYPE,value:a.row.returnType},null,8,["type","value"])]),_:1}),e(o,{align:"center",label:"\u5907\u6CE8",prop:"remark",width:"200"}),e(o,{align:"center",label:"\u5408\u540C\u91D1\u989D\uFF08\u5143\uFF09",prop:"contract.totalPrice",width:"140",formatter:l(q)},null,8,["formatter"]),e(o,{align:"center",label:"\u8D1F\u8D23\u4EBA",prop:"ownerUserName",width:"120"}),e(o,{align:"center",label:"\u6240\u5C5E\u90E8\u95E8",prop:"ownerUserDeptName",width:"100px"}),e(o,{formatter:l(J),align:"center",label:"\u66F4\u65B0\u65F6\u95F4",prop:"updateTime",width:"180px"},null,8,["formatter"]),e(o,{formatter:l(J),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"]),e(o,{align:"center",label:"\u521B\u5EFA\u4EBA",prop:"creatorName",width:"120"}),e(o,{align:"center",fixed:"right",label:"\u56DE\u6B3E\u72B6\u6001",prop:"auditStatus",width:"120"},{default:t(a=>[e(F,{type:l(B).CRM_AUDIT_STATUS,value:a.row.auditStatus},null,8,["type","value"])]),_:1}),e(o,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"180px"},{default:t(a=>[_((s(),d(u,{link:"",type:"primary",onClick:w=>E("update",a.row.id)},{default:t(()=>[c(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[y,["crm:receivable:update"]]]),a.row.auditStatus===0?_((s(),d(u,{key:0,link:"",type:"primary",onClick:w=>(async r=>{await h.confirm(`\u60A8\u786E\u5B9A\u63D0\u4EA4\u7F16\u53F7\u4E3A\u3010${r.no}\u3011\u7684\u56DE\u6B3E\u5BA1\u6838\u5417\uFF1F`),await Le(r.id),h.success("\u63D0\u4EA4\u5BA1\u6838\u6210\u529F\uFF01"),await f()})(a.row)},{default:t(()=>[c(" \u63D0\u4EA4\u5BA1\u6838 ")]),_:2},1032,["onClick"])),[[y,["crm:receivable:update"]]]):_((s(),d(u,{key:1,link:"",type:"primary",onClick:w=>{return r=a.row,void v({name:"BpmProcessInstanceDetail",query:{id:r.processInstanceId}});var r}},{default:t(()=>[c(" \u67E5\u770B\u5BA1\u6279 ")]),_:2},1032,["onClick"])),[[y,["crm:receivable:update"]]]),_((s(),d(u,{link:"",type:"danger",onClick:w=>(async r=>{try{await h.delConfirm(),await Ke(r),h.success(j("common.delSuccess")),await f()}catch{}})(a.row.id)},{default:t(()=>[c(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[y,["crm:receivable:delete"]]])]),_:1})]),_:1},8,["data"])),[[te,l(k)]]),e(ae,{limit:l(i).pageSize,"onUpdate:limit":n[4]||(n[4]=a=>l(i).pageSize=a),page:l(i).pageNo,"onUpdate:page":n[5]||(n[5]=a=>l(i).pageNo=a),total:l(V),onPagination:f},null,8,["limit","page","total"])]),_:1}),e(Be,{ref_key:"formRef",ref:D,onSuccess:f},null,512)],64)}}})});export{$e as __tla,Q as default};
