import{bd as c,bm as m,d as s,bY as y,bf as b,b as o,bM as d,o as f,l as g,w as _,aV as v,a0 as x,a as S,av as h,b0 as w,bg as C,bh as E,__tla as k}from"./index-BUSn51wb.js";let l,z=Promise.all([(()=>{try{return k}catch{}})()]).then(async()=>{const n=c({type:{type:String,values:["primary","success","info","warning","danger",""],default:""},size:{type:String,values:m,default:""},truncated:{type:Boolean},lineClamp:{type:[String,Number]},tag:{type:String,default:"span"}}),r=s({name:"ElText"});l=E(C(s({...r,props:n,setup(i){const t=i,p=y(),a=b("text"),u=o(()=>[a.b(),a.m(t.type),a.m(p.value),a.is("truncated",t.truncated),a.is("line-clamp",!d(t.lineClamp))]);return(e,B)=>(f(),g(w(e.tag),{class:x(S(u)),style:h({"-webkit-line-clamp":e.lineClamp})},{default:_(()=>[v(e.$slots,"default")]),_:3},8,["class","style"]))}}),[["__file","text.vue"]]))});export{l as E,z as __tla};
