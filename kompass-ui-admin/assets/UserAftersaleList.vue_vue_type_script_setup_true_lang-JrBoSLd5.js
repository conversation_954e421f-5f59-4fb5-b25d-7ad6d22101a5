import{d as ee,u as ae,r as _,C as le,cJ as g,G as c,o,c as m,i as a,w as t,a as l,U as k,F as f,k as w,l as y,j as h,H as te,t as b,g as E,aF as re,aD as ue,aJ as se,Z as oe,L as ne,J as pe,K as de,M as ie,_ as _e,N as ce,O as me,z as fe,A as ye,P as be,ax as ve,Q as we,R as he,__tla as Te}from"./index-BUSn51wb.js";import{_ as Ae,__tla as Ve}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as ge,__tla as Ee}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{E as Ne,__tla as xe}from"./el-image-BjHZRFih.js";import{_ as Ue,__tla as ke}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{e as Se,__tla as Re}from"./index-3xRtOTae.js";import{f as De,__tla as Ce}from"./formatTime-DWdBpgsM.js";let M,Fe=Promise.all([(()=>{try{return Te}catch{}})(),(()=>{try{return Ve}catch{}})(),(()=>{try{return Ee}catch{}})(),(()=>{try{return xe}catch{}})(),(()=>{try{return ke}catch{}})(),(()=>{try{return Re}catch{}})(),(()=>{try{return Ce}catch{}})()]).then(async()=>{let S,R;S={class:"flex items-center"},R={class:"mr-10px"},M=ee({name:"UserAfterSaleList",__name:"UserAftersaleList",props:{userId:{}},setup(H){const{push:D}=ae(),C=H,N=_(!0),F=_(0),L=_([]),I=_([{label:"\u5168\u90E8",value:"0"}]),P=_(),r=_({pageNo:1,pageSize:10,no:null,status:"0",orderNo:null,spuName:null,createTime:[],way:null,type:null,userId:null}),T=async()=>{N.value=!0;try{const s=ue(r.value);s.status==="0"&&delete s.status,C.userId&&(s.userId=C.userId);const u=await Se(s);L.value=u.list,F.value=u.total}finally{N.value=!1}},v=async()=>{r.value.pageNo=1,await T()},J=()=>{var s;(s=P.value)==null||s.resetFields(),v()},W=async s=>{r.value.status=s.paneName,await T()};return le(async()=>{await T();for(const s of g(c.TRADE_AFTER_SALE_STATUS))I.value.push({label:s.label,value:s.value})}),(s,u)=>{const x=oe,n=ne,A=pe,U=de,O=ie,Y=_e,V=ce,j=me,z=Ue,q=fe,G=ye,p=be,Q=Ne,Z=ve,K=ge,B=we,X=Ae,$=he;return o(),m(f,null,[a(z,null,{default:t(()=>[a(j,{ref_key:"queryFormRef",ref:P,inline:!0,model:l(r),"label-width":"68px"},{default:t(()=>[a(n,{label:"\u5546\u54C1\u540D\u79F0",prop:"spuName"},{default:t(()=>[a(x,{modelValue:l(r).spuName,"onUpdate:modelValue":u[0]||(u[0]=e=>l(r).spuName=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1 SPU \u540D\u79F0",onKeyup:k(v,["enter"])},null,8,["modelValue"])]),_:1}),a(n,{label:"\u9000\u6B3E\u7F16\u53F7",prop:"no"},{default:t(()=>[a(x,{modelValue:l(r).no,"onUpdate:modelValue":u[1]||(u[1]=e=>l(r).no=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u9000\u6B3E\u7F16\u53F7",onKeyup:k(v,["enter"])},null,8,["modelValue"])]),_:1}),a(n,{label:"\u8BA2\u5355\u7F16\u53F7",prop:"orderNo"},{default:t(()=>[a(x,{modelValue:l(r).orderNo,"onUpdate:modelValue":u[2]||(u[2]=e=>l(r).orderNo=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u8BA2\u5355\u7F16\u53F7",onKeyup:k(v,["enter"])},null,8,["modelValue"])]),_:1}),a(n,{label:"\u552E\u540E\u72B6\u6001",prop:"status"},{default:t(()=>[a(U,{modelValue:l(r).status,"onUpdate:modelValue":u[3]||(u[3]=e=>l(r).status=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u552E\u540E\u72B6\u6001"},{default:t(()=>[a(A,{label:"\u5168\u90E8",value:"0"}),(o(!0),m(f,null,w(l(g)(l(c).TRADE_AFTER_SALE_STATUS),e=>(o(),y(A,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(n,{label:"\u552E\u540E\u65B9\u5F0F",prop:"way"},{default:t(()=>[a(U,{modelValue:l(r).way,"onUpdate:modelValue":u[4]||(u[4]=e=>l(r).way=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u552E\u540E\u65B9\u5F0F"},{default:t(()=>[(o(!0),m(f,null,w(l(g)(l(c).TRADE_AFTER_SALE_WAY),e=>(o(),y(A,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(n,{label:"\u552E\u540E\u7C7B\u578B",prop:"type"},{default:t(()=>[a(U,{modelValue:l(r).type,"onUpdate:modelValue":u[5]||(u[5]=e=>l(r).type=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u552E\u540E\u7C7B\u578B"},{default:t(()=>[(o(!0),m(f,null,w(l(g)(l(c).TRADE_AFTER_SALE_TYPE),e=>(o(),y(A,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(n,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:t(()=>[a(O,{modelValue:l(r).createTime,"onUpdate:modelValue":u[6]||(u[6]=e=>l(r).createTime=e),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-280px","end-placeholder":"\u81EA\u5B9A\u4E49\u65F6\u95F4","start-placeholder":"\u81EA\u5B9A\u4E49\u65F6\u95F4",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),a(n,null,{default:t(()=>[a(V,{onClick:v},{default:t(()=>[a(Y,{class:"mr-5px",icon:"ep:search"}),h(" \u641C\u7D22 ")]),_:1}),a(V,{onClick:J},{default:t(()=>[a(Y,{class:"mr-5px",icon:"ep:refresh"}),h(" \u91CD\u7F6E ")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),a(z,null,{default:t(()=>[a(G,{modelValue:l(r).status,"onUpdate:modelValue":u[7]||(u[7]=e=>l(r).status=e),onTabClick:W},{default:t(()=>[(o(!0),m(f,null,w(l(I),e=>(o(),y(q,{key:e.label,label:e.label,name:e.value},null,8,["label","name"]))),128))]),_:1},8,["modelValue"]),te((o(),y(B,{data:l(L)},{default:t(()=>[a(p,{align:"center",label:"\u9000\u6B3E\u7F16\u53F7","min-width":"200",prop:"no"}),a(p,{align:"center",label:"\u8BA2\u5355\u7F16\u53F7","min-width":"200",prop:"orderNo"},{default:t(({row:e})=>[a(V,{link:"",type:"primary",onClick:i=>{return d=e.orderId,void D({name:"TradeOrderDetail",params:{id:d}});var d}},{default:t(()=>[h(b(e.orderNo),1)]),_:2},1032,["onClick"])]),_:1}),a(p,{label:"\u5546\u54C1\u4FE1\u606F","min-width":"600",prop:"spuName"},{default:t(({row:e})=>[E("div",S,[a(Q,{src:e.picUrl,class:"mr-10px h-30px w-30px",onClick:i=>{return d=e.picUrl,void se({urlList:[d]});var d}},null,8,["src","onClick"]),E("span",R,b(e.spuName),1),(o(!0),m(f,null,w(e.properties,i=>(o(),y(Z,{key:i.propertyId,class:"mr-10px"},{default:t(()=>[h(b(i.propertyName)+": "+b(i.valueName),1)]),_:2},1024))),128))])]),_:1}),a(p,{align:"center",label:"\u8BA2\u5355\u91D1\u989D","min-width":"120",prop:"refundPrice"},{default:t(e=>[E("span",null,b(l(re)(e.row.refundPrice))+" \u5143",1)]),_:1}),a(p,{align:"center",label:"\u7533\u8BF7\u65F6\u95F4",prop:"createTime",width:"180"},{default:t(e=>[E("span",null,b(l(De)(e.row.createTime)),1)]),_:1}),a(p,{align:"center",label:"\u552E\u540E\u72B6\u6001",width:"100"},{default:t(e=>[a(K,{type:l(c).TRADE_AFTER_SALE_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),a(p,{align:"center",label:"\u552E\u540E\u65B9\u5F0F"},{default:t(e=>[a(K,{type:l(c).TRADE_AFTER_SALE_WAY,value:e.row.way},null,8,["type","value"])]),_:1}),a(p,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"120"},{default:t(({row:e})=>[a(V,{link:"",type:"primary",onClick:i=>{return d=e.id,void D({name:"TradeAfterSaleDetail",params:{id:d}});var d}},{default:t(()=>[h("\u5904\u7406\u9000\u6B3E")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[$,l(N)]]),a(X,{limit:l(r).pageSize,"onUpdate:limit":u[8]||(u[8]=e=>l(r).pageSize=e),page:l(r).pageNo,"onUpdate:page":u[9]||(u[9]=e=>l(r).pageNo=e),total:l(F),onPagination:T},null,8,["limit","page","total"])]),_:1})],64)}}})});export{M as _,Fe as __tla};
