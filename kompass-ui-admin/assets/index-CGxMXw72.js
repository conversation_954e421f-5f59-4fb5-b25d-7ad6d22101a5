import{d as M,I as O,n as Q,r as h,f as T,C as Z,T as B,o as c,c as N,i as a,w as t,a as r,U as D,j as o,H as m,l as d,t as E,a9 as G,F as J,Z as W,L as X,_ as Y,N as $,O as aa,P as ea,Q as ta,R as la,__tla as ra}from"./index-BUSn51wb.js";import{_ as na,__tla as ca}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as oa,__tla as sa}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as ia,__tla as pa}from"./index-COobLwz-.js";import{a as _a,d as ua,b as ma,e as da,__tla as fa}from"./index-C-Ee_eqi.js";import{_ as ya,__tla as ha}from"./AccountForm.vue_vue_type_script_setup_true_lang-WMvREy1I.js";import{__tla as ga}from"./index-Cch5e1V0.js";import{__tla as wa}from"./el-card-CJbXGyyg.js";import{__tla as ka}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";let S,Ca=Promise.all([(()=>{try{return ra}catch{}})(),(()=>{try{return ca}catch{}})(),(()=>{try{return sa}catch{}})(),(()=>{try{return pa}catch{}})(),(()=>{try{return fa}catch{}})(),(()=>{try{return ha}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return wa}catch{}})(),(()=>{try{return ka}catch{}})()]).then(async()=>{let b;b=["src"],S=M({name:"MpAccount",__name:"index",setup(ba){const _=O(),{t:R}=Q(),g=h(!0),x=h(0),I=h([]),n=T({pageNo:1,pageSize:10,name:null,account:null,appId:null}),U=h(),u=async()=>{g.value=!0;try{const f=await _a(n);I.value=f.list,x.value=f.total}finally{g.value=!1}},w=()=>{n.pageNo=1,u()},V=()=>{U.value.resetFields(),w()},P=h(),q=(f,l)=>{P.value.open(f,l)};return Z(()=>{u()}),(f,l)=>{const z=ia,F=W,v=X,k=Y,s=$,L=aa,A=oa,i=ea,j=ta,H=na,y=B("hasPermi"),K=la;return c(),N(J,null,[a(z,{title:"\u516C\u4F17\u53F7\u63A5\u5165",url:"https://doc.iocoder.cn/mp/account/"}),a(A,null,{default:t(()=>[a(L,{class:"-mb-15px",model:r(n),ref_key:"queryFormRef",ref:U,inline:!0,"label-width":"68px"},{default:t(()=>[a(v,{label:"\u540D\u79F0",prop:"name"},{default:t(()=>[a(F,{modelValue:r(n).name,"onUpdate:modelValue":l[0]||(l[0]=e=>r(n).name=e),placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0",clearable:"",onKeyup:D(w,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(v,null,{default:t(()=>[a(s,{onClick:w},{default:t(()=>[a(k,{icon:"ep:search",class:"mr-5px"}),o("\u641C\u7D22")]),_:1}),a(s,{onClick:V},{default:t(()=>[a(k,{icon:"ep:refresh",class:"mr-5px"}),o("\u91CD\u7F6E")]),_:1}),m((c(),d(s,{type:"primary",onClick:l[1]||(l[1]=e=>q("create"))},{default:t(()=>[a(k,{icon:"ep:plus",class:"mr-5px"}),o(" \u65B0\u589E ")]),_:1})),[[y,["mp:account:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(A,null,{default:t(()=>[m((c(),d(j,{data:r(I)},{default:t(()=>[a(i,{label:"\u540D\u79F0",align:"center",prop:"name"}),a(i,{label:"\u5FAE\u4FE1\u53F7",align:"center",prop:"account",width:"180"}),a(i,{label:"appId",align:"center",prop:"appId",width:"180"}),a(i,{label:"\u670D\u52A1\u5668\u5730\u5740(URL)",align:"center",prop:"appId",width:"360"},{default:t(e=>[o(E("http://\u670D\u52A1\u7AEF\u5730\u5740/admin-api/mp/open/"+e.row.appId),1)]),_:1}),a(i,{label:"\u4E8C\u7EF4\u7801",align:"center",prop:"qrCodeUrl"},{default:t(e=>[e.row.qrCodeUrl?(c(),N("img",{key:0,src:e.row.qrCodeUrl,alt:"\u4E8C\u7EF4\u7801",style:{display:"inline-block",height:"100px"}},null,8,b)):G("",!0),m((c(),d(s,{link:"",type:"primary",onClick:C=>(async p=>{try{await _.confirm('\u662F\u5426\u786E\u8BA4\u751F\u6210\u516C\u4F17\u53F7\u8D26\u53F7\u7F16\u53F7\u4E3A"'+p.name+'"\u7684\u4E8C\u7EF4\u7801?'),await ma(p.id),_.success("\u751F\u6210\u4E8C\u7EF4\u7801\u6210\u529F"),await u()}catch{}})(e.row)},{default:t(()=>[o(" \u751F\u6210\u4E8C\u7EF4\u7801 ")]),_:2},1032,["onClick"])),[[y,["mp:account:qr-code"]]])]),_:1}),a(i,{label:"\u5907\u6CE8",align:"center",prop:"remark"}),a(i,{label:"\u64CD\u4F5C",align:"center"},{default:t(e=>[m((c(),d(s,{link:"",type:"primary",onClick:C=>q("update",e.row.id)},{default:t(()=>[o(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[y,["mp:account:update"]]]),m((c(),d(s,{link:"",type:"danger",onClick:C=>(async p=>{try{await _.delConfirm(),await ua(p),_.success(R("common.delSuccess")),await u()}catch{}})(e.row.id)},{default:t(()=>[o(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[y,["mp:account:delete"]]]),m((c(),d(s,{link:"",type:"danger",onClick:C=>(async p=>{try{await _.confirm('\u662F\u5426\u786E\u8BA4\u6E05\u7A7A\u751F\u6210\u516C\u4F17\u53F7\u8D26\u53F7\u7F16\u53F7\u4E3A"'+p.name+'"\u7684 API \u914D\u989D?'),await da(p.id),_.success("\u6E05\u7A7A API \u914D\u989D\u6210\u529F")}catch{}})(e.row)},{default:t(()=>[o(" \u6E05\u7A7A API \u914D\u989D ")]),_:2},1032,["onClick"])),[[y,["mp:account:clear-quota"]]])]),_:1})]),_:1},8,["data"])),[[K,r(g)]]),a(H,{total:r(x),page:r(n).pageNo,"onUpdate:page":l[2]||(l[2]=e=>r(n).pageNo=e),limit:r(n).pageSize,"onUpdate:limit":l[3]||(l[3]=e=>r(n).pageSize=e),onPagination:u},null,8,["total","page","limit"])]),_:1}),a(ya,{ref_key:"formRef",ref:P,onSuccess:u},null,512)],64)}}})});export{Ca as __tla,S as default};
