import{_ as t,__tla as _}from"./UploadVideo.vue_vue_type_script_setup_true_lang-CsT_aq48.js";import{__tla as a}from"./index-BUSn51wb.js";import{__tla as r}from"./upload-DyVf7G_u.js";import{__tla as l}from"./useUpload-gjof4KYU.js";let o=Promise.all([(()=>{try{return _}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return r}catch{}})(),(()=>{try{return l}catch{}})()]).then(async()=>{});export{o as __tla,t as default};
