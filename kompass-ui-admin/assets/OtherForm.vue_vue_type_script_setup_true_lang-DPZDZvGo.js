import{d as w,p as y,r as c,f as I,at as C,o as D,l as F,w as s,a as e,O,i as r,I as S,dK as x,cc as U,L as j,__tla as N}from"./index-BUSn51wb.js";import{r as m,__tla as P}from"./formRules-CA9eXdcX.js";let _,k=Promise.all([(()=>{try{return N}catch{}})(),(()=>{try{return P}catch{}})()]).then(async()=>{_=w({name:"ProductOtherForm",__name:"OtherForm",props:{propFormData:{type:Object,default:()=>{}},isDetail:y.bool.def(!1)},emits:["update:activeName"],setup(n,{expose:v,emit:f}){const h=S(),p=n,u=c(),a=c({sort:0,giveIntegral:0,virtualSalesCount:0}),g=I({sort:[m],giveIntegral:[m],virtualSalesCount:[m]});C(()=>p.propFormData,t=>{t&&x(a.value,t)},{immediate:!0});const b=f;return v({validate:async()=>{var t;if(u)try{await((t=e(u))==null?void 0:t.validate()),Object.assign(p.propFormData,a.value)}catch(l){throw h.error("\u3010\u5176\u5B83\u8BBE\u7F6E\u3011\u4E0D\u5B8C\u5584\uFF0C\u8BF7\u586B\u5199\u76F8\u5173\u4FE1\u606F"),b("update:activeName","other"),l}}}),(t,l)=>{const d=U,i=j,V=O;return D(),F(V,{ref_key:"formRef",ref:u,model:e(a),rules:e(g),"label-width":"120px",disabled:n.isDetail},{default:s(()=>[r(i,{label:"\u5546\u54C1\u6392\u5E8F",prop:"sort"},{default:s(()=>[r(d,{modelValue:e(a).sort,"onUpdate:modelValue":l[0]||(l[0]=o=>e(a).sort=o),min:0,placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1\u6392\u5E8F",class:"w-80!"},null,8,["modelValue"])]),_:1}),r(i,{label:"\u8D60\u9001\u79EF\u5206",prop:"giveIntegral"},{default:s(()=>[r(d,{modelValue:e(a).giveIntegral,"onUpdate:modelValue":l[1]||(l[1]=o=>e(a).giveIntegral=o),min:0,placeholder:"\u8BF7\u8F93\u5165\u8D60\u9001\u79EF\u5206",class:"w-80!"},null,8,["modelValue"])]),_:1}),r(i,{label:"\u865A\u62DF\u9500\u91CF",prop:"virtualSalesCount"},{default:s(()=>[r(d,{modelValue:e(a).virtualSalesCount,"onUpdate:modelValue":l[2]||(l[2]=o=>e(a).virtualSalesCount=o),min:0,placeholder:"\u8BF7\u8F93\u5165\u865A\u62DF\u9500\u91CF",class:"w-80!"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules","disabled"])}}})});export{_,k as __tla};
