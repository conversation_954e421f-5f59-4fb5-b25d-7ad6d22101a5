import{bd as Q,be as T,bI as U,c2 as w,bp as B,bn as X,c5 as $,d as x,bf as W,d0 as Z,bY as ee,cD as ae,bR as se,bS as le,r as te,d$ as ie,f as de,b as u,c0 as ne,at as C,b$ as oe,o as h,c as y,g as m,av as re,a as l,a0 as n,F as be,k as ce,aV as ue,j as me,t as fe,bg as ve,bN as _,bh as pe,__tla as ge}from"./index-BUSn51wb.js";let E,he=Promise.all([(()=>{try{return ge}catch{}})()]).then(async()=>{const L=Q({options:{type:T(Array),default:()=>[]},modelValue:{type:[String,Number,Boolean],default:void 0},block:Boolean,size:U,disabled:Boolean,validateEvent:{type:Boolean,default:!0},id:String,name:String,ariaLabel:String}),R={[w]:d=>B(d)||X(d),[$]:d=>B(d)||X(d)},q=["id","aria-label","aria-labelledby"],F=["name","disabled","checked","onChange"],j=x({name:"ElSegmented"});E=pe(ve(x({...j,props:L,emits:R,setup(d,{emit:k}){const t=d,s=W("segmented"),A=Z(),D=ee(),N=ae(),{formItem:o}=se(),{inputId:z,isLabeledByFormItem:I}=le(t,{formItemContext:o}),r=te(null),P=ie(),a=de({isInit:!1,width:0,translateX:0,disabled:!1,focusVisible:!1}),f=e=>_(e)?e.value:e,Y=e=>_(e)?e.label:e,v=e=>!!(N.value||_(e)&&e.disabled),V=e=>t.modelValue===f(e),G=e=>[s.e("item"),s.is("selected",V(e)),s.is("disabled",v(e))],p=()=>{if(!r.value)return;const e=r.value.querySelector(".is-selected"),b=r.value.querySelector(".is-selected input");if(!e||!b)return a.width=0,a.translateX=0,a.disabled=!1,void(a.focusVisible=!1);const i=e.getBoundingClientRect();var c;a.isInit=!0,a.width=i.width,a.translateX=e.offsetLeft,a.disabled=v((c=t.modelValue,t.options.find(g=>f(g)===c)));try{a.focusVisible=b.matches(":focus-visible")}catch{}},H=u(()=>[s.b(),s.m(D.value),s.is("block",t.block)]),J=u(()=>({width:`${a.width}px`,transform:`translateX(${a.translateX}px)`,display:a.isInit?"block":"none"})),K=u(()=>[s.e("item-selected"),s.is("disabled",a.disabled),s.is("focus-visible",a.focusVisible)]),M=u(()=>t.name||A.value);return ne(r,p),C(P,p),C(()=>t.modelValue,()=>{var e;p(),t.validateEvent&&((e=o==null?void 0:o.validate)==null||e.call(o,"change").catch(b=>oe()))},{flush:"post"}),(e,b)=>(h(),y("div",{id:l(z),ref_key:"segmentedRef",ref:r,class:n(l(H)),role:"radiogroup","aria-label":l(I)?void 0:e.ariaLabel||"segmented","aria-labelledby":l(I)?l(o).labelId:void 0},[m("div",{class:n(l(s).e("group"))},[m("div",{style:re(l(J)),class:n(l(K))},null,6),(h(!0),y(be,null,ce(e.options,(i,c)=>(h(),y("label",{key:c,class:n(G(i))},[m("input",{class:n(l(s).e("item-input")),type:"radio",name:l(M),disabled:v(i),checked:V(i),onChange:g=>(O=>{const S=f(O);k(w,S),k($,S)})(i)},null,42,F),m("div",{class:n(l(s).e("item-label"))},[ue(e.$slots,"default",{item:i},()=>[me(fe(Y(i)),1)])],2)],2))),128))],2)],10,q))}}),[["__file","segmented.vue"]]))});export{E,he as __tla};
