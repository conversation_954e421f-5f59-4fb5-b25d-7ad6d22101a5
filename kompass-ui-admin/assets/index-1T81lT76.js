import{d as Ma,S as za,I as Da,r as l,b as La,C as Sa,o as m,l as h,w as _,i as o,a as e,g as y,j as H,t as U,c as na,a9 as f,H as Ra,y as sa,aw as Ba,ay as j,_ as Ea,N as Na,b9 as Va,e3 as Ha,ce as Ua,eb as ja,b7 as qa,a5 as Aa,a6 as Ga,B as Ja,__tla as Ka}from"./index-BUSn51wb.js";import{C as q,__tla as Pa}from"./index-CBZlCJOz.js";import{C as $a,__tla as Fa}from"./index-UejJy_db.js";import Qa,{__tla as Wa}from"./ConversationList-iZNHbzAS.js";import{_ as Xa,__tla as Ya}from"./ConversationUpdateForm.vue_vue_type_script_setup_true_lang-BqY_fRul.js";import Za,{__tla as ae}from"./MessageList-CzQ9k6Bq.js";import ee,{__tla as te}from"./MessageListEmpty-DTP2nGWl.js";import le,{__tla as re}from"./MessageLoading-CFgVl-Rs.js";import ne,{__tla as se}from"./MessageNewConversation-CweTJabh.js";import"./fetch-D5K_4anA.js";import{__tla as oe}from"./el-drawer-DMK0hKF6.js";import{__tla as ue}from"./el-text-CIwNlU-U.js";import{__tla as ie}from"./el-empty-DomufbmG.js";import{__tla as ce}from"./RoleRepository-C2mkS3L0.js";import{__tla as _e}from"./RoleHeader-BBBy8NqQ.js";import{__tla as ve}from"./RoleList-DrJHeS8f.js";import{__tla as me}from"./el-card-CJbXGyyg.js";import{__tla as ye}from"./el-dropdown-item-CIJXMVYa.js";import{__tla as pe}from"./ChatRoleForm.vue_vue_type_script_setup_true_lang-DAKVMBtZ.js";import{__tla as fe}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import"./constants-A8BI3pz7.js";import{__tla as de}from"./index-DrcFYyNA.js";import{__tla as he}from"./RoleCategoryList-Dzs_rZGP.js";import"./gpt-WhTktY3S.js";import{__tla as Ce}from"./el-avatar-Da2TGjmj.js";import{__tla as we}from"./formatTime-DWdBpgsM.js";import{__tla as ge}from"./index-DCh5hX9K.js";import"./avatar-BG6NdH5s.js";import{__tla as ke}from"./el-skeleton-item-tDN8U6BH.js";let oa,Te=Promise.all([(()=>{try{return Ka}catch{}})(),(()=>{try{return Pa}catch{}})(),(()=>{try{return Fa}catch{}})(),(()=>{try{return Wa}catch{}})(),(()=>{try{return Ya}catch{}})(),(()=>{try{return ae}catch{}})(),(()=>{try{return te}catch{}})(),(()=>{try{return re}catch{}})(),(()=>{try{return se}catch{}})(),(()=>{try{return oe}catch{}})(),(()=>{try{return ue}catch{}})(),(()=>{try{return ie}catch{}})(),(()=>{try{return ce}catch{}})(),(()=>{try{return _e}catch{}})(),(()=>{try{return ve}catch{}})(),(()=>{try{return me}catch{}})(),(()=>{try{return ye}catch{}})(),(()=>{try{return pe}catch{}})(),(()=>{try{return fe}catch{}})(),(()=>{try{return de}catch{}})(),(()=>{try{return he}catch{}})(),(()=>{try{return Ce}catch{}})(),(()=>{try{return we}catch{}})(),(()=>{try{return ge}catch{}})(),(()=>{try{return ke}catch{}})()]).then(async()=>{let A,G,J,K,P,$,F,Q,W;A={class:"title"},G={key:0},J={key:0,class:"btns"},K=["innerHTML"],P={class:"message-container"},$={class:"prompt-from"},F={class:"prompt-btns"},Q=(I=>(Aa("data-v-d2070a63"),I=I(),Ga(),I))(()=>y("span",{class:"ml-5px text-14px text-#8f8f8f"},"\u4E0A\u4E0B\u6587",-1)),W=Ma({name:"AiChat",__name:"index",setup(I){const X=za(),p=Da(),Y=l(),r=l(null),u=l(null),i=l(!1),x=l(),n=l([]),C=l(!1),L=l(),T=l(50),S=l(!1),w=l(!1),O=l(),R=l(),v=l(),M=l(!0),g=l(""),z=l(""),Z=async a=>{if(!a)return;const t=await $a.getChatConversationMy(a);t&&(u.value=t,r.value=t.id)},ua=async a=>i.value?(p.alert("\u5BF9\u8BDD\u4E2D\uFF0C\u4E0D\u5141\u8BB8\u5207\u6362!"),!1):(r.value=a.id,u.value=a,await B(),b(!0),v.value="",!0),ia=async a=>{r.value===a.id&&await aa()},aa=async()=>{if(i.value)return p.alert("\u5BF9\u8BDD\u4E2D\uFF0C\u4E0D\u5141\u8BB8\u5207\u6362!"),!1;r.value=null,u.value=null,n.value=[]},ea=l(),ca=async()=>{ea.value.open(r.value)},_a=async()=>{await Z(r.value)},va=async()=>{await Y.value.createConversation()},ma=async()=>{v.value=""},B=async()=>{try{if(r.value===null)return;L.value=setTimeout(()=>{C.value=!0},60),n.value=await q.getChatMessageListByConversationId(r.value),await j(),await b()}finally{L.value&&clearTimeout(L.value),C.value=!1}},E=La(()=>{var a;return n.value.length>0?n.value:(a=u.value)!=null&&a.systemMessage?[{id:0,type:"system",content:u.value.systemMessage}]:[]}),ya=()=>{i.value?p.alert("\u56DE\u7B54\u4E2D\uFF0C\u4E0D\u80FD\u5220\u9664!"):B()},pa=async()=>{if(r.value)try{await p.delConfirm("\u786E\u8BA4\u6E05\u7A7A\u5BF9\u8BDD\u6D88\u606F\uFF1F"),await q.deleteByConversationId(r.value),n.value=[]}catch{}},fa=()=>{x.value.handlerGoTop()},da=async a=>{var s;if(w.value||i.value)return;const t=(s=v.value)==null?void 0:s.trim();a.key==="Enter"&&(a.shiftKey?(v.value+=`\r
`,a.preventDefault()):(await D(t),a.preventDefault()))},ha=()=>{var a;D((a=v.value)==null?void 0:a.trim())},Ca=a=>{if(!w.value){if(a.data==null)return;w.value=!0}R.value&&clearTimeout(R.value),R.value=setTimeout(()=>{w.value=!1},400)},wa=()=>{w.value=!0},ga=()=>{setTimeout(()=>{w.value=!1},200)},D=async a=>{a.length<1?p.error("\u53D1\u9001\u5931\u8D25\uFF0C\u539F\u56E0\uFF1A\u5185\u5BB9\u4E3A\u7A7A\uFF01"):r.value!=null?(v.value="",await ka({conversationId:r.value,content:a})):p.error("\u8FD8\u6CA1\u521B\u5EFA\u5BF9\u8BDD\uFF0C\u4E0D\u80FD\u53D1\u9001!")},ka=async a=>{O.value=new AbortController,i.value=!0,g.value="";try{n.value.push({id:-1,conversationId:r.value,type:"user",content:a.content,createTime:new Date}),n.value.push({id:-2,conversationId:r.value,type:"assistant",content:"\u601D\u8003\u4E2D...",createTime:new Date}),await j(),await b(),Ia();let t=!0;await q.sendChatMessageStream(a.conversationId,a.content,O.value,M.value,async s=>{const{code:c,data:k,msg:V}=JSON.parse(s.data);c===0?k.receive.content!==""&&(t&&(t=!1,n.value.pop(),n.value.pop(),n.value.push(k.send),n.value.push(k.receive)),g.value=g.value+k.receive.content,await b()):p.alert(`\u5BF9\u8BDD\u5F02\u5E38! ${V}`)},s=>{p.alert(`\u5BF9\u8BDD\u5F02\u5E38! ${s}`),N()},()=>{N()})}catch{}},N=async()=>{O.value&&O.value.abort(),i.value=!1},Ta=a=>{v.value=a.content},ba=a=>{D(a.content)},b=async a=>{await j(),x.value&&x.value.scrollToBottom(a)},Ia=async()=>{let a=0;try{if(S.value)return;S.value=!0,z.value="";const t=async()=>{const c=(g.value.length-z.value.length)/10;T.value=c>5?10:c>2?30:c>1.5?50:100,i.value||(T.value=10),a<g.value.length?(z.value+=g.value[a],a++,n.value[n.value.length-1].content=z.value,await b(),s=setTimeout(t,T.value)):i.value?s=setTimeout(t,T.value):(S.value=!1,clearTimeout(s))};let s=setTimeout(t,T.value)}catch{}};return Sa(async()=>{if(X.query.conversationId){const a=X.query.conversationId;r.value=a,await Z(a)}C.value=!0,await B()}),(a,t)=>{const s=Ea,c=Na,k=Va,V=Ha,xa=Ua,Oa=ja,ta=qa;return m(),h(ta,{class:"ai-layout"},{default:_(()=>[o(Qa,{"active-id":e(r),ref_key:"conversationListRef",ref:Y,onOnConversationCreate:ma,onOnConversationClick:ua,onOnConversationClear:aa,onOnConversationDelete:ia},null,8,["active-id"]),o(ta,{class:"detail-container"},{default:_(()=>[o(k,{class:"header"},{default:_(()=>{var d,la;return[y("div",A,[H(U((d=e(u))!=null&&d.title?(la=e(u))==null?void 0:la.title:"\u5BF9\u8BDD")+" ",1),e(n).length?(m(),na("span",G,"("+U(e(n).length)+")",1)):f("",!0)]),e(u)?(m(),na("div",J,[o(c,{type:"primary",bg:"",plain:"",size:"small",onClick:ca},{default:_(()=>{var ra;return[y("span",{innerHTML:(ra=e(u))==null?void 0:ra.modelName},null,8,K),o(s,{icon:"ep:setting",class:"ml-10px"})]}),_:1}),o(c,{size:"small",class:"btn",onClick:pa},{default:_(()=>[o(s,{icon:"heroicons-outline:archive-box-x-mark",color:"#787878"})]),_:1}),o(c,{size:"small",class:"btn"},{default:_(()=>[o(s,{icon:"ep:download",color:"#787878"})]),_:1}),o(c,{size:"small",class:"btn",onClick:fa},{default:_(()=>[o(s,{icon:"ep:top",color:"#787878"})]),_:1})])):f("",!0)]}),_:1}),o(V,{class:"main-container"},{default:_(()=>[y("div",null,[y("div",P,[e(C)?(m(),h(le,{key:0})):f("",!0),e(u)?f("",!0):(m(),h(ne,{key:1,onOnNewConversation:va})),!e(C)&&e(E).length===0&&e(u)?(m(),h(ee,{key:2,onOnPrompt:D})):f("",!0),!e(C)&&e(E).length>0?(m(),h(Za,{key:3,ref_key:"messageRef",ref:x,conversation:e(u),list:e(E),onOnDeleteSuccess:ya,onOnEdit:Ta,onOnRefresh:ba},null,8,["conversation","list"])):f("",!0)])])]),_:1}),o(Oa,{class:"footer-container"},{default:_(()=>[y("form",$,[Ra(y("textarea",{class:"prompt-input","onUpdate:modelValue":t[0]||(t[0]=d=>sa(v)?v.value=d:null),onKeydown:da,onInput:Ca,onCompositionstart:wa,onCompositionend:ga,placeholder:"\u95EE\u6211\u4EFB\u4F55\u95EE\u9898...\uFF08Shift+Enter \u6362\u884C\uFF0C\u6309\u4E0B Enter \u53D1\u9001\uFF09"},null,544),[[Ba,e(v)]]),y("div",F,[y("div",null,[o(xa,{modelValue:e(M),"onUpdate:modelValue":t[1]||(t[1]=d=>sa(M)?M.value=d:null)},null,8,["modelValue"]),Q]),e(i)==0?(m(),h(c,{key:0,type:"primary",size:"default",onClick:ha,loading:e(i)},{default:_(()=>[H(U(e(i)?"\u8FDB\u884C\u4E2D":"\u53D1\u9001"),1)]),_:1},8,["loading"])):f("",!0),e(i)==1?(m(),h(c,{key:1,type:"danger",size:"default",onClick:t[2]||(t[2]=d=>N())},{default:_(()=>[H(" \u505C\u6B62 ")]),_:1})):f("",!0)])])]),_:1})]),_:1}),o(Xa,{ref_key:"conversationUpdateFormRef",ref:ea,onSuccess:_a},null,512)]),_:1})}}}),oa=Ja(W,[["__scopeId","data-v-d2070a63"]])});export{Te as __tla,oa as default};
