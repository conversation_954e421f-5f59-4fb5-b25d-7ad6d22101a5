import{d as M,n as P,I as R,r as i,f as T,o as c,l as _,w as s,i as r,a as e,j as f,H as j,c as G,F as H,k as I,V as Z,G as z,t as D,y as J,Z as K,L as Q,cl as W,cc as X,am as Y,an as $,O as ee,N as ae,R as le,__tla as te}from"./index-BUSn51wb.js";import{_ as se,__tla as re}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{C as g}from"./constants-A8BI3pz7.js";import{a as ue,c as oe,u as de,__tla as ie}from"./brand-DwnGZI23.js";let h,me=Promise.all([(()=>{try{return te}catch{}})(),(()=>{try{return re}catch{}})(),(()=>{try{return ie}catch{}})()]).then(async()=>{h=M({name:"ProductBrandForm",__name:"BrandForm",emits:["success"],setup(ne,{expose:w,emit:k}){const{t:p}=P(),v=R(),o=i(!1),V=i(""),d=i(!1),y=i(""),t=i({id:void 0,name:"",picUrl:"",status:g.ENABLE,description:""}),E=T({name:[{required:!0,message:"\u54C1\u724C\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],picUrl:[{required:!0,message:"\u54C1\u724C\u56FE\u7247\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],sort:[{required:!0,message:"\u54C1\u724C\u6392\u5E8F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),n=i();w({open:async(u,a)=>{if(o.value=!0,V.value=p("action."+u),y.value=u,S(),a){d.value=!0;try{t.value=await ue(a)}finally{d.value=!1}}}});const F=k,N=async()=>{if(n&&await n.value.validate()){d.value=!0;try{const u=t.value;y.value==="create"?(await oe(u),v.success(p("common.createSuccess"))):(await de(u),v.success(p("common.updateSuccess"))),o.value=!1,F("success")}finally{d.value=!1}}},S=()=>{var u;t.value={id:void 0,name:"",picUrl:"",status:g.ENABLE,description:""},(u=n.value)==null||u.resetFields()};return(u,a)=>{const b=K,m=Q,q=W,x=X,B=Y,C=$,L=ee,U=ae,A=se,O=le;return c(),_(A,{title:e(V),modelValue:e(o),"onUpdate:modelValue":a[6]||(a[6]=l=>J(o)?o.value=l:null)},{footer:s(()=>[r(U,{onClick:N,type:"primary",disabled:e(d)},{default:s(()=>[f("\u786E \u5B9A")]),_:1},8,["disabled"]),r(U,{onClick:a[5]||(a[5]=l=>o.value=!1)},{default:s(()=>[f("\u53D6 \u6D88")]),_:1})]),default:s(()=>[j((c(),_(L,{ref_key:"formRef",ref:n,model:e(t),rules:e(E),"label-width":"80px"},{default:s(()=>[r(m,{label:"\u54C1\u724C\u540D\u79F0",prop:"name"},{default:s(()=>[r(b,{modelValue:e(t).name,"onUpdate:modelValue":a[0]||(a[0]=l=>e(t).name=l),placeholder:"\u8BF7\u8F93\u5165\u54C1\u724C\u540D\u79F0"},null,8,["modelValue"])]),_:1}),r(m,{label:"\u54C1\u724C\u56FE\u7247",prop:"picUrl"},{default:s(()=>[r(q,{modelValue:e(t).picUrl,"onUpdate:modelValue":a[1]||(a[1]=l=>e(t).picUrl=l),limit:1,"is-show-tip":!1},null,8,["modelValue"])]),_:1}),r(m,{label:"\u54C1\u724C\u6392\u5E8F",prop:"sort"},{default:s(()=>[r(x,{modelValue:e(t).sort,"onUpdate:modelValue":a[2]||(a[2]=l=>e(t).sort=l),"controls-position":"right",min:0},null,8,["modelValue"])]),_:1}),r(m,{label:"\u54C1\u724C\u72B6\u6001",prop:"status"},{default:s(()=>[r(C,{modelValue:e(t).status,"onUpdate:modelValue":a[3]||(a[3]=l=>e(t).status=l)},{default:s(()=>[(c(!0),G(H,null,I(e(Z)(e(z).COMMON_STATUS),l=>(c(),_(B,{key:l.value,label:l.value},{default:s(()=>[f(D(l.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),r(m,{label:"\u54C1\u724C\u63CF\u8FF0"},{default:s(()=>[r(b,{modelValue:e(t).description,"onUpdate:modelValue":a[4]||(a[4]=l=>e(t).description=l),type:"textarea",placeholder:"\u8BF7\u8F93\u5165\u54C1\u724C\u63CF\u8FF0"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[O,e(d)]])]),_:1},8,["title","modelValue"])}}})});export{h as _,me as __tla};
