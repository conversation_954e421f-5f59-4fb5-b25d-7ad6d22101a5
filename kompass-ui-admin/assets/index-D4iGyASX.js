import{d as L,I as Q,r as y,o as n,c as o,g as e,i as l,w as t,j as d,a as r,y as R,F as _,k as h,l as T,t as p,a0 as b,av as W,Z as X,N as Y,B as aa,__tla as ea}from"./index-BUSn51wb.js";import{E as la,__tla as ta}from"./el-image-BjHZRFih.js";import{E as sa,__tla as ia}from"./el-space-Dxj8A-LJ.js";import{E as ra,__tla as na}from"./el-text-CIwNlU-U.js";import{I as oa,__tla as da}from"./index-Cjd1fP7g.js";import{I as ca,d as ua,D as ma,e as z,a as E}from"./constants-C0I8ujwj.js";let q,ya=Promise.all([(()=>{try{return ea}catch{}})(),(()=>{try{return ta}catch{}})(),(()=>{try{return ia}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return da}catch{}})()]).then(async()=>{let D,N,P,S,V,A,O,M,$,j;D={class:"prompt"},N={class:"hot-words"},P={class:"model"},S={class:"model-font"},V={class:"image-style"},A={class:"style-font"},O={class:"image-size"},M=["onClick"],$={class:"size-font"},j={class:"btns"},q=aa(L({__name:"index",emits:["onDrawStart","onDrawComplete"],setup(_a,{expose:G,emit:H}){const J=Q(),u=y(""),v=y(!1),f=y(""),g=y("dall-e-3"),C=y("1024x1024"),k=y("vivid"),B=H,F=async s=>{C.value=s.key},K=async()=>{await J.confirm("\u786E\u8BA4\u751F\u6210\u5185\u5BB9?");try{v.value=!0,B("onDrawStart",E.OPENAI);const s=z.find(i=>i.key===C.value),m={platform:E.OPENAI,prompt:u.value,model:g.value,width:s.width,height:s.height,options:{style:k.value}};await oa.drawImage(m)}finally{B("onDrawComplete",E.OPENAI),v.value=!1}};return G({settingValues:async s=>{var i;u.value=s.prompt,g.value=s.model,k.value=(i=s.options)==null?void 0:i.style;const m=z.find(x=>x.key===`${s.width}x${s.height}`);await F(m)}}),(s,m)=>{const i=ra,x=X,U=Y,w=sa,Z=la;return n(),o(_,null,[e("div",D,[l(i,{tag:"b"},{default:t(()=>[d("\u753B\u9762\u63CF\u8FF0")]),_:1}),l(i,{tag:"p"},{default:t(()=>[d("\u5EFA\u8BAE\u4F7F\u7528\u201C\u5F62\u5BB9\u8BCD+\u52A8\u8BCD+\u98CE\u683C\u201D\u7684\u683C\u5F0F\uFF0C\u4F7F\u7528\u201C\uFF0C\u201D\u9694\u5F00")]),_:1}),l(x,{modelValue:r(u),"onUpdate:modelValue":m[0]||(m[0]=a=>R(u)?u.value=a:null),maxlength:"1024",rows:"5",class:"w-100% mt-15px","input-style":"border-radius: 7px;",placeholder:"\u4F8B\u5982\uFF1A\u7AE5\u8BDD\u91CC\u7684\u5C0F\u5C4B\u5E94\u8BE5\u662F\u4EC0\u4E48\u6837\u5B50\uFF1F","show-word-limit":"",type:"textarea"},null,8,["modelValue"])]),e("div",N,[e("div",null,[l(i,{tag:"b"},{default:t(()=>[d("\u968F\u673A\u70ED\u8BCD")]),_:1})]),l(w,{wrap:"",class:"word-list"},{default:t(()=>[(n(!0),o(_,null,h(r(ca),a=>(n(),T(U,{round:"",class:"btn",type:r(f)===a?"primary":"default",key:a,onClick:I=>(async c=>{f.value!=c?(f.value=c,u.value=c):f.value=""})(a)},{default:t(()=>[d(p(a),1)]),_:2},1032,["type","onClick"]))),128))]),_:1})]),e("div",P,[e("div",null,[l(i,{tag:"b"},{default:t(()=>[d("\u6A21\u578B\u9009\u62E9")]),_:1})]),l(w,{wrap:"",class:"model-list"},{default:t(()=>[(n(!0),o(_,null,h(r(ua),a=>(n(),o("div",{class:b(r(g)===a.key?"modal-item selectModel":"modal-item"),key:a.key},[l(Z,{src:a.image,fit:"contain",onClick:I=>(async c=>{g.value=c.key})(a)},null,8,["src","onClick"]),e("div",S,p(a.name),1)],2))),128))]),_:1})]),e("div",V,[e("div",null,[l(i,{tag:"b"},{default:t(()=>[d("\u98CE\u683C\u9009\u62E9")]),_:1})]),l(w,{wrap:"",class:"image-style-list"},{default:t(()=>[(n(!0),o(_,null,h(r(ma),a=>(n(),o("div",{class:b(r(k)===a.key?"image-style-item selectImageStyle":"image-style-item"),key:a.key},[l(Z,{src:a.image,fit:"contain",onClick:I=>(async c=>{k.value=c.key})(a)},null,8,["src","onClick"]),e("div",A,p(a.name),1)],2))),128))]),_:1})]),e("div",O,[e("div",null,[l(i,{tag:"b"},{default:t(()=>[d("\u753B\u9762\u6BD4\u4F8B")]),_:1})]),l(w,{wrap:"",class:"size-list"},{default:t(()=>[(n(!0),o(_,null,h(r(z),a=>(n(),o("div",{class:"size-item",key:a.key,onClick:I=>F(a)},[e("div",{class:b(r(C)===a.key?"size-wrapper selectImageSize":"size-wrapper")},[e("div",{style:W(a.style)},null,4)],2),e("div",$,p(a.name),1)],8,M))),128))]),_:1})]),e("div",j,[l(U,{type:"primary",size:"large",round:"",loading:r(v),onClick:K},{default:t(()=>[d(p(r(v)?"\u751F\u6210\u4E2D":"\u751F\u6210\u5185\u5BB9"),1)]),_:1},8,["loading"])])],64)}}}),[["__scopeId","data-v-e4d41aaf"]])});export{ya as __tla,q as default};
