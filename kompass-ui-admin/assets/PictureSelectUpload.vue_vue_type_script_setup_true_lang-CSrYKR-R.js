import P from"./picture-CTjip5lJ.js";import{d as x,I as C,o as b,c as k,g as E,a as T,ey as U,__tla as j}from"./index-BUSn51wb.js";let d,A=Promise.all([(()=>{try{return j}catch{}})()]).then(async()=>{let r;r=["src"],d=x({name:"PictureSelectUpload",__name:"PictureSelectUpload",emits:["send-picture"],setup(D,{emit:u}){const f=C(),y=u,g=async()=>{const c=await async function(h={}){const{multiple:_,accept:s,limit:w,fileSize:v}={multiple:!0,accept:"image/jpeg, image/png, image/gif",limit:1,fileSize:500,...h},e=document.createElement("input");e.type="file",e.style.display="none",_&&(e.multiple=!0),s&&(e.accept=s),document.body.appendChild(e),e.click();try{return await new Promise((t,o)=>{e.addEventListener("change",a=>{var p;const i=Array.from(((p=a==null?void 0:a.target)==null?void 0:p.files)||[]);if(document.body.removeChild(e),i.length>w)return void o({errorType:"limit",files:i});const m=i.filter(l=>l.size/1048576>v);if(m.length>0)return void o({errorType:"fileSize",files:m});const S=i.map((l,z)=>({file:l,uid:Date.now()+z}));t(S)})})}catch(t){throw console.error("\u9009\u62E9\u6587\u4EF6\u51FA\u9519:",t),t}}();f.success("\u56FE\u7247\u53D1\u9001\u4E2D\u8BF7\u7A0D\u7B49\u3002\u3002\u3002");const n=await U({file:c[0].file});y("send-picture",n.data)};return(c,n)=>(b(),k("div",null,[E("img",{src:T(P),class:"w-35px h-35px",onClick:g},null,8,r)]))}})});export{d as _,A as __tla};
