import{by as t,__tla as u}from"./index-BUSn51wb.js";let r,c,e,s,o,l=Promise.all([(()=>{try{return u}catch{}})()]).then(async()=>{r=async a=>await t.get({url:"/product/comment/page",params:a}),e=async a=>await t.get({url:"/product/comment/get?id="+a}),c=async a=>await t.post({url:"/product/comment/create",data:a}),o=async a=>await t.put({url:"/product/comment/update-visible",data:a}),s=async a=>await t.put({url:"/product/comment/reply",data:a})});export{l as __tla,r as a,c,e as g,s as r,o as u};
