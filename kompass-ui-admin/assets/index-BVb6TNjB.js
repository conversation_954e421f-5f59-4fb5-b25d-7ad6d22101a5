import{by as a,__tla as g}from"./index-BUSn51wb.js";let e,s,l,n,p,r,c=Promise.all([(()=>{try{return g}catch{}})()]).then(async()=>{s=t=>a.get({url:"/system/tenant-package/page",params:t}),e=t=>a.get({url:"/system/tenant-package/get?id="+t}),l=t=>a.post({url:"/system/tenant-package/create",data:t}),r=t=>a.put({url:"/system/tenant-package/update",data:t}),n=t=>a.delete({url:"/system/tenant-package/delete?id="+t}),p=()=>a.get({url:"/system/tenant-package/simple-list"})});export{c as __tla,e as a,s as b,l as c,n as d,p as g,r as u};
