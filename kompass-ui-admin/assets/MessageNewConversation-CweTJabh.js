import{d as _,o as v,c as m,g as s,i as p,w as u,j as f,N as w,a5 as b,a6 as y,B as C,__tla as N}from"./index-BUSn51wb.js";let r,h=Promise.all([(()=>{try{return N}catch{}})()]).then(async()=>{let t,e,n,o;t={class:"new-chat"},e={class:"box-center"},n=(a=>(b("data-v-7348f8cb"),a=a(),y(),a))(()=>s("div",{class:"tip"},"\u70B9\u51FB\u4E0B\u65B9\u6309\u94AE\uFF0C\u5F00\u59CB\u4F60\u7684\u5BF9\u8BDD\u5427",-1)),o={class:"btns"},r=C(_({__name:"MessageNewConversation",emits:["onNewConversation"],setup(a,{emit:c}){const i=c,l=()=>{i("onNewConversation")};return(g,x)=>{const d=w;return v(),m("div",t,[s("div",e,[n,s("div",o,[p(d,{type:"primary",round:"",onClick:l},{default:u(()=>[f("\u65B0\u5EFA\u5BF9\u8BDD")]),_:1})])])])}}}),[["__scopeId","data-v-7348f8cb"]])});export{h as __tla,r as default};
