import{d as u,o as m,c as y,i as a,w as l,g as q,j as _,t as d,s as b,_ as f,E as h,v as g,__tla as k}from"./index-BUSn51wb.js";let n,K=Promise.all([(()=>{try{return k}catch{}})()]).then(async()=>{let o;o=["src"],n=u({name:"WxLocation",__name:"main",props:{locationX:{required:!0,type:Number},locationY:{required:!0,type:Number},label:{required:!0,type:String},qqMapKey:{required:!1,type:String,default:"TVDBZ-TDILD-4ON4B-PFDZA-RNLKH-VVF6E"}},setup(e,{expose:i}){const t=e;return i({locationX:t.locationX,locationY:t.locationY,label:t.label,qqMapKey:t.qqMapKey}),(X,Y)=>{const r=b,s=f,p=h,c=g;return m(),y("div",null,[a(c,{type:"primary",target:"_blank",href:"https://map.qq.com/?type=marker&isopeninfowin=1&markertype=1&pointx="+e.locationY+"&pointy="+e.locationX+"&name="+e.label+"&ref=yudao"},{default:l(()=>[a(p,null,{default:l(()=>[a(r,null,{default:l(()=>[q("img",{src:"https://apis.map.qq.com/ws/staticmap/v2/?zoom=10&markers=color:blue|label:A|"+e.locationX+","+e.locationY+"&key="+e.qqMapKey+"&size=250*180"},null,8,o)]),_:1}),a(r,null,{default:l(()=>[a(s,{icon:"ep:location"}),_(" "+d(e.label),1)]),_:1})]),_:1})]),_:1},8,["href"])])}}})});export{n as _,K as __tla};
