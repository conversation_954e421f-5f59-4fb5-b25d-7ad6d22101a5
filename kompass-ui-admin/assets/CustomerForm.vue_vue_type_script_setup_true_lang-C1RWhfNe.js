import{d as Z,n as B,I as D,r as m,f as Q,o as t,l as d,w as s,i as u,a,j as T,H as W,c,F as n,k as p,V as h,G as V,t as A,y as Y,Z as $,L as ee,am as ae,an as le,J as re,K as se,ai as ue,ca as te,M as oe,O as de,N as ie,R as me,__tla as ce}from"./index-BUSn51wb.js";import{_ as ne,__tla as pe}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{C as y,__tla as ve}from"./index-D8hnRknQ.js";import{g as _e,__tla as ge}from"./index-BYXzDB8j.js";import{g as be,__tla as he}from"./index-CyP7ZSdX.js";let R,Ve=Promise.all([(()=>{try{return ce}catch{}})(),(()=>{try{return pe}catch{}})(),(()=>{try{return ve}catch{}})(),(()=>{try{return ge}catch{}})(),(()=>{try{return he}catch{}})()]).then(async()=>{R=Z({name:"CustomerForm",__name:"CustomerForm",emits:["success"],setup(fe,{expose:q,emit:L}){const{t:S}=B(),w=D(),U=m([]),E=m([]),v=m(!1),x=m(""),_=m(!1),I=m(""),r=m({customerId:void 0,customerName:void 0,customerSex:void 0,relationship:void 0,customerPhone:void 0,openId:void 0,serviceStatus:void 0,sourceChannel:void 0,serviceTags:[],operationTags:[],levelTags:[],registerTime:void 0,lastLoginTime:void 0,headOperateUserId:void 0,headMarketUserId:void 0,customerRemark:void 0}),N=Q({customerName:[{required:!0,message:"\u5BB6\u957F\u59D3\u540D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],customerSex:[{required:!0,message:"\u5BB6\u957F\u6027\u522B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],relationship:[{required:!0,message:"\u5B69\u5B50\u5173\u7CFB\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],customerPhone:[{required:!0,message:"\u624B\u673A\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],serviceStatus:[{required:!0,message:"\u670D\u52A1\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],sourceChannel:[{required:!0,message:"\u83B7\u5BA2\u6E20\u9053\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],serviceTags:[{required:!0,message:"\u670D\u52A1\u6807\u7B7E\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],operationTags:[{required:!0,message:"\u8FD0\u8425\u6807\u7B7E\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],levelTags:[{required:!0,message:"\u5206\u7EA7\u6807\u7B7E\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],headOperateUserId:[{required:!0,message:"\u8FD0\u8425\u8D1F\u8D23\u4EBA\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],headMarketUserId:[{required:!0,message:"\u5E02\u573A\u8D1F\u8D23\u4EBA\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],customerRemark:[{required:!0,message:"\u5BB6\u957F\u5907\u6CE8\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),k=m();q({open:async(i,l)=>{if(v.value=!0,x.value=S("action."+i),I.value=i,M(),l){_.value=!0;try{r.value=await y.getCustomer(l)}finally{_.value=!1}}E.value=await be(),U.value=await _e()}});const O=L,P=async()=>{await k.value.validate(),_.value=!0;try{const i=r.value;I.value==="create"?(await y.createCustomer(i),w.success(S("common.createSuccess"))):(await y.updateCustomer(i),w.success(S("common.updateSuccess"))),v.value=!1,O("success")}finally{_.value=!1}},M=()=>{var i;r.value={customerId:void 0,customerName:void 0,customerSex:void 0,relationship:void 0,customerPhone:void 0,openId:void 0,serviceStatus:void 0,sourceChannel:void 0,serviceTags:[],operationTags:[],levelTags:[],registerTime:void 0,lastLoginTime:void 0,headOperateUserId:void 0,headMarketUserId:void 0,customerRemark:void 0},(i=k.value)==null||i.resetFields()};return(i,l)=>{const f=$,o=ee,F=ae,G=le,g=re,b=se,H=ue,J=te,j=oe,z=de,C=ie,K=ne,X=me;return t(),d(K,{title:a(x),modelValue:a(v),"onUpdate:modelValue":l[14]||(l[14]=e=>Y(v)?v.value=e:null),width:"1000px"},{footer:s(()=>[u(C,{onClick:P,type:"primary",disabled:a(_)},{default:s(()=>[T("\u786E \u5B9A")]),_:1},8,["disabled"]),u(C,{onClick:l[13]||(l[13]=e=>v.value=!1)},{default:s(()=>[T("\u53D6 \u6D88")]),_:1})]),default:s(()=>[W((t(),d(z,{ref_key:"formRef",ref:k,model:a(r),rules:a(N),"label-width":"100px",inline:""},{default:s(()=>[u(o,{label:"\u5BB6\u957F\u59D3\u540D",prop:"customerName",class:"!w-250px"},{default:s(()=>[u(f,{modelValue:a(r).customerName,"onUpdate:modelValue":l[0]||(l[0]=e=>a(r).customerName=e),placeholder:"\u8BF7\u8F93\u5165\u5BB6\u957F\u59D3\u540D"},null,8,["modelValue"])]),_:1}),u(o,{label:"\u5BB6\u957F\u6027\u522B",prop:"customerSex",class:"!w-300px"},{default:s(()=>[u(G,{modelValue:a(r).customerSex,"onUpdate:modelValue":l[1]||(l[1]=e=>a(r).customerSex=e)},{default:s(()=>[(t(!0),c(n,null,p(a(h)(a(V).ALS_SEX),e=>(t(),d(F,{key:e.value,label:e.value},{default:s(()=>[T(A(e.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(o,{label:"\u5B69\u5B50\u5173\u7CFB",prop:"relationship",class:"!w-250px"},{default:s(()=>[u(f,{modelValue:a(r).relationship,"onUpdate:modelValue":l[2]||(l[2]=e=>a(r).relationship=e),placeholder:"\u8BF7\u8F93\u5165\u5B69\u5B50\u5173\u7CFB"},null,8,["modelValue"])]),_:1}),u(o,{label:"\u624B\u673A\u53F7",prop:"customerPhone",class:"!w-250px"},{default:s(()=>[u(f,{modelValue:a(r).customerPhone,"onUpdate:modelValue":l[3]||(l[3]=e=>a(r).customerPhone=e),placeholder:"\u8BF7\u8F93\u5165\u624B\u673A\u53F7"},null,8,["modelValue"])]),_:1}),u(o,{label:"\u670D\u52A1\u72B6\u6001",prop:"serviceStatus",class:"!w-250px"},{default:s(()=>[u(b,{modelValue:a(r).serviceStatus,"onUpdate:modelValue":l[4]||(l[4]=e=>a(r).serviceStatus=e),placeholder:"\u8BF7\u9009\u62E9\u670D\u52A1\u72B6\u6001"},{default:s(()=>[(t(!0),c(n,null,p(a(h)(a(V).ALS_SERVICE_STATUS),e=>(t(),d(g,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(o,{label:"\u83B7\u5BA2\u6E20\u9053",prop:"sourceChannel",class:"!w-250px"},{default:s(()=>[u(b,{modelValue:a(r).sourceChannel,"onUpdate:modelValue":l[5]||(l[5]=e=>a(r).sourceChannel=e),placeholder:"\u8BF7\u9009\u62E9\u83B7\u5BA2\u6E20\u9053"},{default:s(()=>[(t(!0),c(n,null,p(a(h)(a(V).ALS_SOURCE_CHANNEL),e=>(t(),d(g,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(o,{label:"\u670D\u52A1\u6807\u7B7E",prop:"serviceTags",class:"!w-621px"},{default:s(()=>[u(b,{modelValue:a(r).serviceTags,"onUpdate:modelValue":l[6]||(l[6]=e=>a(r).serviceTags=e),multiple:"",clearable:""},{default:s(()=>[(t(!0),c(n,null,p(a(h)(a(V).ALS_SERVICE_TAGS),e=>(t(),d(g,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(o,{label:"\u8FD0\u8425\u6807\u7B7E",prop:"operationTags",class:"!w-100%"},{default:s(()=>[u(b,{modelValue:a(r).operationTags,"onUpdate:modelValue":l[7]||(l[7]=e=>a(r).operationTags=e),multiple:"",clearable:""},{default:s(()=>[(t(!0),c(n,null,p(a(h)(a(V).ALS_OPERATION_TAGS),e=>(t(),d(g,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(o,{label:"\u5206\u7EA7\u6807\u7B7E",prop:"levelTags",class:"!w-100%"},{default:s(()=>[u(J,{modelValue:a(r).levelTags,"onUpdate:modelValue":l[8]||(l[8]=e=>a(r).levelTags=e)},{default:s(()=>[(t(!0),c(n,null,p(a(h)(a(V).ALS_LEVEL_TAGS),e=>(t(),d(H,{key:e.value,label:e.value},{default:s(()=>[T(A(e.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(o,{label:"\u6CE8\u518C\u65F6\u95F4",prop:"registerTime"},{default:s(()=>[u(j,{modelValue:a(r).registerTime,"onUpdate:modelValue":l[9]||(l[9]=e=>a(r).registerTime=e),type:"datetime","value-format":"x",class:"!w-220px"},null,8,["modelValue"])]),_:1}),u(o,{label:"\u8FD0\u8425\u8D1F\u8D23\u4EBA",prop:"headOperateUserId"},{default:s(()=>[u(b,{modelValue:a(r).headOperateUserId,"onUpdate:modelValue":l[10]||(l[10]=e=>a(r).headOperateUserId=e),clearable:"",filterable:"",class:"!w-200px",placeholder:"\u8BF7\u8F93\u5165\u5F53\u524D\u8D1F\u8D23\u4EBA"},{default:s(()=>[(t(!0),c(n,null,p(a(U),e=>(t(),d(g,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(o,{label:"\u5E02\u573A\u8D1F\u8D23\u4EBA",prop:"headMarketUserId"},{default:s(()=>[u(b,{modelValue:a(r).headMarketUserId,"onUpdate:modelValue":l[11]||(l[11]=e=>a(r).headMarketUserId=e),clearable:"",filterable:"",class:"!w-200px",placeholder:"\u8BF7\u8F93\u5165\u5F53\u524D\u8D1F\u8D23\u4EBA"},{default:s(()=>[(t(!0),c(n,null,p(a(U),e=>(t(),d(g,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(o,{label:"\u5BB6\u957F\u5907\u6CE8",prop:"customerRemark",class:"!w-100%"},{default:s(()=>[u(f,{modelValue:a(r).customerRemark,"onUpdate:modelValue":l[12]||(l[12]=e=>a(r).customerRemark=e),type:"textarea",rows:4,maxlength:"500","show-word-limit":"",placeholder:"\u8BF7\u8F93\u5165\u5BB6\u957F\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[X,a(_)]])]),_:1},8,["title","modelValue"])}}})});export{R as _,Ve as __tla};
