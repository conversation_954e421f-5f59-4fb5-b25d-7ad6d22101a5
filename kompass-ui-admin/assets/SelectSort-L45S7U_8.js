import{d as _,r as m,at as S,o as c,c as r,g as h,F as R,k as y,i as f,w as b,j as x,t as C,N as g,B as j,__tla as w}from"./index-BUSn51wb.js";let o,B=Promise.all([(()=>{try{return w}catch{}})()]).then(async()=>{o=j(_({name:"SelectSort",__name:"SelectSort",props:{teachScopeRank:{}},emits:"[updateRank]",setup(p,{emit:v}){const l=p,a=m([...l.teachScopeRank]);S(()=>l.teachScopeRank,t=>{a.value=[...t]});const d=v,i=[{label:"\u8BED\u6587",value:"\u8BED\u6587"},{label:"\u6570\u5B66",value:"\u6570\u5B66"},{label:"\u82F1\u8BED",value:"\u82F1\u8BED"}];return(t,F)=>{const k=g;return c(),r("div",null,[h("div",null,[(c(),r(R,null,y(i,e=>{return f(k,{key:e.value,onClick:I=>(u=>{const n=a.value.indexOf(u);n===-1?a.value.push(u):a.value.splice(n,1),d("updateRank",a.value)})(e.value),type:(s=e.value,l.teachScopeRank.includes(s)?"primary":"default")},{default:b(()=>[x(C(e.label),1)]),_:2},1032,["onClick","type"]);var s}),64))])])}}}),[["__scopeId","data-v-97a21c5d"]])});export{B as __tla,o as default};
