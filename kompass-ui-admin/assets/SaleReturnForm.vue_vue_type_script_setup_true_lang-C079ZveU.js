import{d as te,n as re,I as oe,r as i,f as ue,b as de,at as se,dW as ie,o as c,c as f,i as e,w as r,a as t,l as p,j as g,a9 as ne,H as ce,F as v,k as S,y as q,dX as R,Z as me,L as _e,E as pe,M as fe,_ as ve,N as be,J as Ve,K as he,cn as Pe,s as ye,z as Ue,A as Ie,cc as ke,O as we,R as ge,__tla as Se}from"./index-BUSn51wb.js";import{_ as Re,__tla as xe}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{_ as Ce,__tla as Ne}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{S as x,__tla as Te}from"./index-DIBvuZmk.js";import{_ as <PERSON>,__tla as Le}from"./SaleReturnItemForm.vue_vue_type_script_setup_true_lang-v5moq-AX.js";import{C as Ae,__tla as Ee}from"./index-DYwp4_G0.js";import{A as Oe,__tla as qe}from"./index-LbO7ASKC.js";import{_ as He,__tla as Je}from"./SaleOrderReturnEnableList.vue_vue_type_script_setup_true_lang-CPUsuOZa.js";import{g as Me,__tla as We}from"./index-BYXzDB8j.js";let H,Xe=Promise.all([(()=>{try{return Se}catch{}})(),(()=>{try{return xe}catch{}})(),(()=>{try{return Ne}catch{}})(),(()=>{try{return Te}catch{}})(),(()=>{try{return Le}catch{}})(),(()=>{try{return Ee}catch{}})(),(()=>{try{return qe}catch{}})(),(()=>{try{return Je}catch{}})(),(()=>{try{return We}catch{}})()]).then(async()=>{H=te({name:"SaleReturnForm",__name:"SaleReturnForm",emits:["success"],setup(je,{expose:J,emit:M}){const{t:b}=re(),C=oe(),m=i(!1),N=i(""),_=i(!1),V=i(""),l=i({id:void 0,customerId:void 0,accountId:void 0,saleUserId:void 0,returnTime:void 0,remark:void 0,fileUrl:"",discountPercent:0,discountPrice:0,totalPrice:0,otherPrice:0,orderNo:void 0,items:[],no:void 0}),W=ue({customerId:[{required:!0,message:"\u5BA2\u6237\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],returnTime:[{required:!0,message:"\u9000\u8D27\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),h=de(()=>V.value==="detail"),P=i(),T=i([]),y=i([]),F=i([]),U=i("item"),L=i();se(()=>l.value,u=>{if(!u)return;const a=u.items.reduce((d,s)=>d+s.totalPrice,0),n=u.discountPercent!=null?ie(a,u.discountPercent/100):0;l.value.totalPrice=a-n+u.otherPrice},{deep:!0}),J({open:async(u,a)=>{if(m.value=!0,N.value=b("action."+u),V.value=u,K(),a){_.value=!0;try{l.value=await x.getSaleReturn(a)}finally{_.value=!1}}T.value=await Ae.getCustomerSimpleList(),F.value=await Me(),y.value=await Oe.getAccountSimpleList();const n=y.value.find(d=>d.defaultStatus);n&&(l.value.accountId=n.id)}});const A=i(),X=()=>{A.value.open()},j=u=>{l.value.orderId=u.id,l.value.orderNo=u.no,l.value.customerId=u.customerId,l.value.accountId=u.accountId,l.value.saleUserId=u.saleUserId,l.value.discountPercent=u.discountPercent,l.value.remark=u.remark,l.value.fileUrl=u.fileUrl,u.items.forEach(a=>{a.count=a.outCount-a.returnCount,a.orderItemId=a.id,a.id=void 0}),l.value.items=u.items.filter(a=>a.count>0)},z=M,D=async()=>{await P.value.validate(),await L.value.validate(),_.value=!0;try{const u=l.value;V.value==="create"?(await x.createSaleReturn(u),C.success(b("common.createSuccess"))):(await x.updateSaleReturn(u),C.success(b("common.updateSuccess"))),m.value=!1,z("success")}finally{_.value=!1}},K=()=>{var u;l.value={id:void 0,customerId:void 0,accountId:void 0,saleUserId:void 0,returnTime:void 0,remark:void 0,fileUrl:void 0,discountPercent:0,discountPrice:0,totalPrice:0,otherPrice:0,items:[]},(u=P.value)==null||u.resetFields()};return(u,a)=>{const n=me,d=_e,s=pe,Q=fe,Z=ve,I=be,k=Ve,w=he,B=Pe,E=ye,G=Ue,Y=Ie,$=Ce,O=ke,ee=we,ae=Re,le=ge;return c(),f(v,null,[e(ae,{title:t(N),modelValue:t(m),"onUpdate:modelValue":a[14]||(a[14]=o=>q(m)?m.value=o:null),width:"1440"},{footer:r(()=>[t(h)?ne("",!0):(c(),p(I,{key:0,onClick:D,type:"primary",disabled:t(_)},{default:r(()=>[g(" \u786E \u5B9A ")]),_:1},8,["disabled"])),e(I,{onClick:a[13]||(a[13]=o=>m.value=!1)},{default:r(()=>[g("\u53D6 \u6D88")]),_:1})]),default:r(()=>[ce((c(),p(ee,{ref_key:"formRef",ref:P,model:t(l),rules:t(W),"label-width":"100px",disabled:t(h)},{default:r(()=>[e(E,{gutter:20},{default:r(()=>[e(s,{span:8},{default:r(()=>[e(d,{label:"\u9000\u8D27\u5355\u53F7",prop:"no"},{default:r(()=>[e(n,{disabled:"",modelValue:t(l).no,"onUpdate:modelValue":a[0]||(a[0]=o=>t(l).no=o),placeholder:"\u4FDD\u5B58\u65F6\u81EA\u52A8\u751F\u6210"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:r(()=>[e(d,{label:"\u9000\u8D27\u65F6\u95F4",prop:"returnTime"},{default:r(()=>[e(Q,{modelValue:t(l).returnTime,"onUpdate:modelValue":a[1]||(a[1]=o=>t(l).returnTime=o),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u9000\u8D27\u65F6\u95F4",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:r(()=>[e(d,{label:"\u5173\u8054\u8BA2\u5355",prop:"orderNo"},{default:r(()=>[e(n,{modelValue:t(l).orderNo,"onUpdate:modelValue":a[2]||(a[2]=o=>t(l).orderNo=o),readonly:""},{append:r(()=>[e(I,{onClick:X},{default:r(()=>[e(Z,{icon:"ep:search"}),g(" \u9009\u62E9 ")]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:r(()=>[e(d,{label:"\u5BA2\u6237",prop:"customerId"},{default:r(()=>[e(w,{modelValue:t(l).customerId,"onUpdate:modelValue":a[3]||(a[3]=o=>t(l).customerId=o),clearable:"",filterable:"",disabled:"",placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237",class:"!w-1/1"},{default:r(()=>[(c(!0),f(v,null,S(t(T),o=>(c(),p(k,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:r(()=>[e(d,{label:"\u9500\u552E\u4EBA\u5458",prop:"saleUserId"},{default:r(()=>[e(w,{modelValue:t(l).saleUserId,"onUpdate:modelValue":a[4]||(a[4]=o=>t(l).saleUserId=o),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u9500\u552E\u4EBA\u5458",class:"!w-1/1"},{default:r(()=>[(c(!0),f(v,null,S(t(F),o=>(c(),p(k,{key:o.id,label:o.nickname,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{span:16},{default:r(()=>[e(d,{label:"\u5907\u6CE8",prop:"remark"},{default:r(()=>[e(n,{type:"textarea",modelValue:t(l).remark,"onUpdate:modelValue":a[5]||(a[5]=o=>t(l).remark=o),rows:1,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:r(()=>[e(d,{label:"\u9644\u4EF6",prop:"fileUrl"},{default:r(()=>[e(B,{"is-show-tip":!1,modelValue:t(l).fileUrl,"onUpdate:modelValue":a[6]||(a[6]=o=>t(l).fileUrl=o),limit:1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e($,null,{default:r(()=>[e(Y,{modelValue:t(U),"onUpdate:modelValue":a[7]||(a[7]=o=>q(U)?U.value=o:null),class:"-mt-15px -mb-10px"},{default:r(()=>[e(G,{label:"\u9000\u8D27\u4EA7\u54C1\u6E05\u5355",name:"item"},{default:r(()=>[e(Fe,{ref_key:"itemFormRef",ref:L,items:t(l).items,disabled:t(h)},null,8,["items","disabled"])]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(E,{gutter:20},{default:r(()=>[e(s,{span:8},{default:r(()=>[e(d,{label:"\u4F18\u60E0\u7387\uFF08%\uFF09",prop:"discountPercent"},{default:r(()=>[e(O,{modelValue:t(l).discountPercent,"onUpdate:modelValue":a[8]||(a[8]=o=>t(l).discountPercent=o),"controls-position":"right",min:0,precision:2,placeholder:"\u8BF7\u8F93\u5165\u4F18\u60E0\u7387",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:r(()=>[e(d,{label:"\u9000\u6B3E\u4F18\u60E0",prop:"discountPrice"},{default:r(()=>[e(n,{disabled:"",modelValue:t(l).discountPrice,"onUpdate:modelValue":a[9]||(a[9]=o=>t(l).discountPrice=o),formatter:t(R)},null,8,["modelValue","formatter"])]),_:1})]),_:1}),e(s,{span:8},{default:r(()=>[e(d,{label:"\u4F18\u60E0\u540E\u91D1\u989D"},{default:r(()=>[e(n,{disabled:"","model-value":t(l).totalPrice-t(l).otherPrice,formatter:t(R)},null,8,["model-value","formatter"])]),_:1})]),_:1}),e(s,{span:8},{default:r(()=>[e(d,{label:"\u5176\u5B83\u8D39\u7528",prop:"otherPrice"},{default:r(()=>[e(O,{modelValue:t(l).otherPrice,"onUpdate:modelValue":a[10]||(a[10]=o=>t(l).otherPrice=o),"controls-position":"right",min:0,precision:2,placeholder:"\u8BF7\u8F93\u5165\u5176\u5B83\u8D39\u7528",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:r(()=>[e(d,{label:"\u7ED3\u7B97\u8D26\u6237",prop:"accountId"},{default:r(()=>[e(w,{modelValue:t(l).accountId,"onUpdate:modelValue":a[11]||(a[11]=o=>t(l).accountId=o),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u7ED3\u7B97\u8D26\u6237",class:"!w-1/1"},{default:r(()=>[(c(!0),f(v,null,S(t(y),o=>(c(),p(k,{key:o.id,label:o.name,value:o.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:r(()=>[e(d,{label:"\u5E94\u9000\u91D1\u989D",prop:"totalPrice"},{default:r(()=>[e(n,{disabled:"",modelValue:t(l).totalPrice,"onUpdate:modelValue":a[12]||(a[12]=o=>t(l).totalPrice=o),formatter:t(R)},null,8,["modelValue","formatter"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules","disabled"])),[[le,t(_)]])]),_:1},8,["title","modelValue"]),e(He,{ref_key:"saleOrderReturnEnableListRef",ref:A,onSuccess:j},null,512)],64)}}})});export{H as _,Xe as __tla};
