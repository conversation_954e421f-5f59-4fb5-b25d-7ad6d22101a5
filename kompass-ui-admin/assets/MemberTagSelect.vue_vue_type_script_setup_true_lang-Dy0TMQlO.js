import{d as T,b as C,r as c,C as j,T as M,o as l,c as _,i as p,w as i,F as y,k as P,l as f,a as h,y as B,H as F,j as H,a9 as J,J as K,K as N,N as R,__tla as U}from"./index-BUSn51wb.js";import{_ as q,g as z,__tla as D}from"./TagForm.vue_vue_type_script_setup_true_lang-D_qYz-X1.js";let v,E=Promise.all([(()=>{try{return U}catch{}})(),(()=>{try{return D}catch{}})()]).then(async()=>{v=T({name:"MemberTagSelect",__name:"MemberTagSelect",props:{modelValue:{type:Array,default:void 0},showAdd:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(t,{expose:V,emit:b}){const r=t,w=b;V({showAdd:r.showAdd});const s=C({get:()=>r.modelValue,set(m){w("update:modelValue",m)}}),o=c([]),u=async()=>{o.value=await z()},d=c();return j(()=>{u()}),(m,e)=>{const k=K,g=N,A=R,x=M("hasPermi");return l(),_(y,null,[p(g,{modelValue:h(s),"onUpdate:modelValue":e[0]||(e[0]=a=>B(s)?s.value=a:null),placeholder:"\u8BF7\u9009\u62E9\u7528\u6237\u6807\u7B7E",clearable:"",multiple:"",class:"!w-240px"},{default:i(()=>[(l(!0),_(y,null,P(h(o),a=>(l(),f(k,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),t.showAdd?F((l(),f(A,{key:0,type:"primary",class:"ml-2",link:"",onClick:e[1]||(e[1]=a=>{return n="create",void d.value.open(n,S);var n,S})},{default:i(()=>[H(" \u65B0\u589E\u6807\u7B7E ")]),_:1})),[[x,["member:tag:create"]]]):J("",!0),p(q,{ref_key:"formRef",ref:d,onSuccess:u},null,512)],64)}}})});export{v as _,E as __tla};
