import{d as H,r as n,f as Q,u as V,at as q,T as G,o as _,c as J,i as a,w as o,j as p,a as e,H as h,l as d,a9 as v,t as K,dV as W,F as X,I as Y,_ as Z,N as $,s as aa,P as ta,v as ea,Q as sa,R as ra,__tla as la}from"./index-BUSn51wb.js";import{_ as oa,__tla as ca}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as ia,__tla as na}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{b as ua,g as _a,__tla as pa}from"./index-M52UJVMY.js";import{h as da,i as ma,__tla as fa}from"./index-9ux5MgCS.js";import{_ as ya,__tla as Ia}from"./BusinessForm.vue_vue_type_script_setup_true_lang-D9dBQLPY.js";import{B as S,__tla as ba}from"./index-pKzyIv29.js";import{_ as ga,__tla as ha}from"./BusinessListModal.vue_vue_type_script_setup_true_lang-BvJ2tBPi.js";let T,va=Promise.all([(()=>{try{return la}catch{}})(),(()=>{try{return ca}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return pa}catch{}})(),(()=>{try{return fa}catch{}})(),(()=>{try{return Ia}catch{}})(),(()=>{try{return ba}catch{}})(),(()=>{try{return ha}catch{}})()]).then(async()=>{T=H({name:"CrmBusinessList",__name:"BusinessList",props:{bizType:{},bizId:{},customerId:{},contactId:{}},setup(x){const m=Y(),s=x,f=n(!0),k=n(0),w=n([]),t=Q({pageNo:1,pageSize:10,customerId:void 0,contactId:void 0}),y=async()=>{f.value=!0;try{t.customerId=void 0,t.contactId=void 0;let r={list:[],total:0};switch(s.bizType){case S.CRM_CUSTOMER:t.customerId=s.bizId,r=await _a(t);break;case S.CRM_CONTACT:t.contactId=s.bizId,r=await ua(t);break;default:return}w.value=r.list,k.value=r.total}finally{f.value=!1}},I=()=>{t.pageNo=1,y()},z=n(),P=()=>{z.value.open("create",null,s.customerId,s.contactId)},{push:M}=V(),C=n(),B=()=>{C.value.open()},U=async r=>{const l={contactId:s.bizId,businessIds:r};b.value.getSelectionRows().forEach(u=>{l.businessIds.push(u.id)}),await da(l),m.success("\u5173\u8054\u5546\u673A\u6210\u529F"),I()},b=n(),j=async()=>{const r={contactId:s.bizId,businessIds:b.value.getSelectionRows().map(l=>l.id)};if(r.businessIds.length===0)return m.error("\u672A\u9009\u62E9\u5546\u673A");await ma(r),m.success("\u53D6\u5173\u5546\u673A\u6210\u529F"),I()};return q(()=>[s.bizId,s.bizType],()=>{I()},{immediate:!0,deep:!0}),(r,l)=>{const u=Z,g=$,E=aa,c=ta,L=ea,O=sa,A=ia,D=oa,N=G("hasPermi"),F=ra;return _(),J(X,null,[a(E,{justify:"end"},{default:o(()=>[a(g,{onClick:P},{default:o(()=>[a(u,{class:"mr-5px",icon:"ep:opportunity"}),p(" \u521B\u5EFA\u5546\u673A ")]),_:1}),e(t).contactId?h((_(),d(g,{key:0,onClick:B},{default:o(()=>[a(u,{class:"mr-5px",icon:"ep:circle-plus"}),p("\u5173\u8054 ")]),_:1})),[[N,["crm:contact:create-business"]]]):v("",!0),e(t).contactId?h((_(),d(g,{key:1,onClick:j},{default:o(()=>[a(u,{class:"mr-5px",icon:"ep:remove"}),p("\u89E3\u9664\u5173\u8054 ")]),_:1})),[[N,["crm:contact:delete-business"]]]):v("",!0)]),_:1}),a(D,{class:"mt-10px"},{default:o(()=>[h((_(),d(O,{ref_key:"businessRef",ref:b,data:e(w),stripe:!0,"show-overflow-tooltip":!0},{default:o(()=>[e(t).contactId?(_(),d(c,{key:0,type:"selection",width:"55"})):v("",!0),a(c,{label:"\u5546\u673A\u540D\u79F0",fixed:"left",align:"center",prop:"name"},{default:o(i=>[a(L,{type:"primary",underline:!1,onClick:ka=>{return R=i.row.id,void M({name:"CrmBusinessDetail",params:{id:R}});var R}},{default:o(()=>[p(K(i.row.name),1)]),_:2},1032,["onClick"])]),_:1}),a(c,{label:"\u5546\u673A\u91D1\u989D",align:"center",prop:"price",formatter:e(W)},null,8,["formatter"]),a(c,{label:"\u5BA2\u6237\u540D\u79F0",align:"center",prop:"customerName"}),a(c,{label:"\u5546\u673A\u7EC4",align:"center",prop:"statusTypeName"}),a(c,{label:"\u5546\u673A\u9636\u6BB5",align:"center",prop:"statusName"})]),_:1},8,["data"])),[[F,e(f)]]),a(A,{total:e(k),page:e(t).pageNo,"onUpdate:page":l[0]||(l[0]=i=>e(t).pageNo=i),limit:e(t).pageSize,"onUpdate:limit":l[1]||(l[1]=i=>e(t).pageSize=i),onPagination:y},null,8,["total","page","limit"])]),_:1}),a(ya,{ref_key:"formRef",ref:z,onSuccess:y},null,512),a(ga,{ref_key:"businessModalRef",ref:C,"customer-id":s.customerId,onSuccess:U},null,8,["customer-id"])],64)}}})});export{T as _,va as __tla};
