import{d as ue,I as de,n as fe,u as ye,r as _,f as he,C as we,au as ke,T as ge,o as i,c as N,i as a,w as t,a as l,U as X,F as Y,k as be,l as n,j as p,H as f,g as q,t as b,a9 as A,y as B,ao as ve,Z as Ce,L as Ve,J as xe,K as De,_ as Se,N as Ue,O as Ne,P as Pe,ax as Te,ce as qe,Q as Be,R as Fe,__tla as Ie}from"./index-BUSn51wb.js";import{_ as Re,__tla as je}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{_ as ze,__tla as Ee}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{E as Ke,__tla as Me}from"./el-image-BjHZRFih.js";import{_ as <PERSON>,__tla as Ze}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as Ge,__tla as He}from"./index-COobLwz-.js";import{d as Je,f as Oe,__tla as Qe}from"./formatTime-DWdBpgsM.js";import{j as We,__tla as Xe}from"./bpmn-embedded-D6vUWKn8.js";import{a as Ye,d as Ae,b as $e,e as ea,g as aa,__tla as ta}from"./index-DzsWMVUv.js";import{g as la,__tla as ra}from"./index-COJ8hy-t.js";import{_ as oa,__tla as ia}from"./ModelForm.vue_vue_type_script_setup_true_lang-C0I03pet.js";import{_ as na,__tla as sa}from"./ModelImportForm.vue_vue_type_script_setup_true_lang-BDHkfFY7.js";import{b as ca,__tla as _a}from"./formCreate-DDLxm5B5.js";import{C as pa,__tla as ma}from"./index-B5YaQXtD.js";import{__tla as ua}from"./index-Cch5e1V0.js";import{__tla as da}from"./el-card-CJbXGyyg.js";import{__tla as fa}from"./XTextButton-DMuYh5Ak.js";import{__tla as ya}from"./XButton-BjahQbul.js";import{__tla as ha}from"./el-collapse-item-B_QvnH_b.js";import{__tla as wa}from"./el-tree-select-CBuha0HW.js";import"./tree-BMa075Oj.js";import{__tla as ka}from"./index-CCFX7HyJ.js";import{__tla as ga}from"./index-Bqt292RI.js";import{__tla as ba}from"./index-D6tFY92u.js";import{__tla as va}from"./index-BYXzDB8j.js";import{__tla as Ca}from"./index-xiOMzVtR.js";import"./constants-A8BI3pz7.js";import{__tla as Va}from"./index-BEeS1wHc.js";import{__tla as xa}from"./el-drawer-DMK0hKF6.js";import{__tla as Da}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import"./color-BN7ZL7BD.js";import{__tla as Sa}from"./index-CRkUQbt2.js";let $,Ua=Promise.all([(()=>{try{return Ie}catch{}})(),(()=>{try{return je}catch{}})(),(()=>{try{return Ee}catch{}})(),(()=>{try{return Me}catch{}})(),(()=>{try{return Ze}catch{}})(),(()=>{try{return He}catch{}})(),(()=>{try{return Qe}catch{}})(),(()=>{try{return Xe}catch{}})(),(()=>{try{return ta}catch{}})(),(()=>{try{return ra}catch{}})(),(()=>{try{return ia}catch{}})(),(()=>{try{return sa}catch{}})(),(()=>{try{return _a}catch{}})(),(()=>{try{return ma}catch{}})(),(()=>{try{return ua}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return fa}catch{}})(),(()=>{try{return ya}catch{}})(),(()=>{try{return ha}catch{}})(),(()=>{try{return wa}catch{}})(),(()=>{try{return ka}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return ba}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return Ca}catch{}})(),(()=>{try{return Va}catch{}})(),(()=>{try{return xa}catch{}})(),(()=>{try{return Da}catch{}})(),(()=>{try{return Sa}catch{}})()]).then(async()=>{let F,I;F={key:2},I={key:0},$=ue({name:"BpmModel",__name:"index",setup(Na){const w=de(),{t:R}=fe(),{push:v}=ye(),P=_(!0),j=_(0),z=_([]),s=he({pageNo:1,pageSize:10,key:void 0,name:void 0,category:void 0}),E=_(),K=_([]),y=async()=>{P.value=!0;try{const d=await Ye(s);z.value=d.list,j.value=d.total}finally{P.value=!1}},C=()=>{s.pageNo=1,y()},ee=()=>{E.value.resetFields(),C()},M=_(),L=(d,r)=>{M.value.open(d,r)},Z=_(),ae=()=>{Z.value.open()},V=_(!1),T=_({rule:[],option:{}}),G=async d=>{if(d.formType==10){const r=await la(d.formId);ca(T,r.conf,r.fields),V.value=!0}else await v({path:d.formCustomCreatePath})},x=_(!1),k=_(null),H=_({prefix:"flowable"});return we(async()=>{await y(),K.value=await pa.getCategorySimpleList()}),(d,r)=>{const D=Ge,J=Ce,S=Ve,te=xe,le=De,U=Se,c=Ue,re=Ne,O=Le,m=Pe,oe=Ke,Q=Te,ie=qe,ne=Be,se=ze,ce=ke("form-create"),W=Re,h=ge("hasPermi"),_e=Fe;return i(),N(Y,null,[a(D,{title:"\u6D41\u7A0B\u8BBE\u8BA1\u5668\uFF08BPMN\uFF09",url:"https://doc.iocoder.cn/bpm/model-designer-dingding/"}),a(D,{title:"\u6D41\u7A0B\u8BBE\u8BA1\u5668\uFF08\u9489\u9489\u3001\u98DE\u4E66\uFF09",url:"https://doc.iocoder.cn/bpm/model-designer-bpmn/"}),a(D,{title:"\u9009\u62E9\u5BA1\u6279\u4EBA\u3001\u53D1\u8D77\u4EBA\u81EA\u9009",url:"https://doc.iocoder.cn/bpm/assignee/"}),a(D,{title:"\u4F1A\u7B7E\u3001\u6216\u7B7E\u3001\u4F9D\u6B21\u5BA1\u6279",url:"https://doc.iocoder.cn/bpm/multi-instance/"}),a(O,null,{default:t(()=>[a(re,{class:"-mb-15px",model:l(s),ref_key:"queryFormRef",ref:E,inline:!0,"label-width":"68px"},{default:t(()=>[a(S,{label:"\u6D41\u7A0B\u6807\u8BC6",prop:"key"},{default:t(()=>[a(J,{modelValue:l(s).key,"onUpdate:modelValue":r[0]||(r[0]=e=>l(s).key=e),placeholder:"\u8BF7\u8F93\u5165\u6D41\u7A0B\u6807\u8BC6",clearable:"",onKeyup:X(C,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(S,{label:"\u6D41\u7A0B\u540D\u79F0",prop:"name"},{default:t(()=>[a(J,{modelValue:l(s).name,"onUpdate:modelValue":r[1]||(r[1]=e=>l(s).name=e),placeholder:"\u8BF7\u8F93\u5165\u6D41\u7A0B\u540D\u79F0",clearable:"",onKeyup:X(C,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(S,{label:"\u6D41\u7A0B\u5206\u7C7B",prop:"category"},{default:t(()=>[a(le,{modelValue:l(s).category,"onUpdate:modelValue":r[2]||(r[2]=e=>l(s).category=e),placeholder:"\u8BF7\u9009\u62E9\u6D41\u7A0B\u5206\u7C7B",clearable:"",class:"!w-240px"},{default:t(()=>[(i(!0),N(Y,null,be(l(K),e=>(i(),n(te,{key:e.code,label:e.name,value:e.code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(S,null,{default:t(()=>[a(c,{onClick:C},{default:t(()=>[a(U,{icon:"ep:search",class:"mr-5px"}),p(" \u641C\u7D22")]),_:1}),a(c,{onClick:ee},{default:t(()=>[a(U,{icon:"ep:refresh",class:"mr-5px"}),p(" \u91CD\u7F6E")]),_:1}),f((i(),n(c,{type:"primary",plain:"",onClick:r[3]||(r[3]=e=>L("create"))},{default:t(()=>[a(U,{icon:"ep:plus",class:"mr-5px"}),p(" \u65B0\u5EFA\u6D41\u7A0B ")]),_:1})),[[h,["bpm:model:create"]]]),f((i(),n(c,{type:"success",plain:"",onClick:ae},{default:t(()=>[a(U,{icon:"ep:upload",class:"mr-5px"}),p(" \u5BFC\u5165\u6D41\u7A0B ")]),_:1})),[[h,["bpm:model:import"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(O,null,{default:t(()=>[f((i(),n(ne,{data:l(z)},{default:t(()=>[a(m,{label:"\u6D41\u7A0B\u6807\u8BC6",align:"center",prop:"key",width:"200"}),a(m,{label:"\u6D41\u7A0B\u540D\u79F0",align:"center",prop:"name",width:"200"},{default:t(e=>[a(c,{type:"primary",link:"",onClick:u=>(async o=>{const g=await aa(o.id);k.value=g.bpmnXml||"",x.value=!0})(e.row)},{default:t(()=>[q("span",null,b(e.row.name),1)]),_:2},1032,["onClick"])]),_:1}),a(m,{label:"\u6D41\u7A0B\u56FE\u6807",align:"center",prop:"icon",width:"100"},{default:t(e=>[a(oe,{src:e.row.icon,class:"w-32px h-32px"},null,8,["src"])]),_:1}),a(m,{label:"\u6D41\u7A0B\u5206\u7C7B",align:"center",prop:"categoryName",width:"100"}),a(m,{label:"\u8868\u5355\u4FE1\u606F",align:"center",prop:"formType",width:"200"},{default:t(e=>[e.row.formType===10?(i(),n(c,{key:0,type:"primary",link:"",onClick:u=>G(e.row)},{default:t(()=>[q("span",null,b(e.row.formName),1)]),_:2},1032,["onClick"])):e.row.formType===20?(i(),n(c,{key:1,type:"primary",link:"",onClick:u=>G(e.row)},{default:t(()=>[q("span",null,b(e.row.formCustomCreatePath),1)]),_:2},1032,["onClick"])):(i(),N("label",F,"\u6682\u65E0\u8868\u5355"))]),_:1}),a(m,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",width:"180",formatter:l(Je)},null,8,["formatter"]),a(m,{label:"\u6700\u65B0\u90E8\u7F72\u7684\u6D41\u7A0B\u5B9A\u4E49",align:"center"},{default:t(()=>[a(m,{label:"\u6D41\u7A0B\u7248\u672C",align:"center",prop:"processDefinition.version",width:"100"},{default:t(e=>[e.row.processDefinition?(i(),n(Q,{key:0},{default:t(()=>[p(" v"+b(e.row.processDefinition.version),1)]),_:2},1024)):(i(),n(Q,{key:1,type:"warning"},{default:t(()=>[p("\u672A\u90E8\u7F72")]),_:1}))]),_:1}),a(m,{label:"\u6FC0\u6D3B\u72B6\u6001",align:"center",prop:"processDefinition.version",width:"85"},{default:t(e=>[e.row.processDefinition?(i(),n(ie,{key:0,modelValue:e.row.processDefinition.suspensionState,"onUpdate:modelValue":u=>e.row.processDefinition.suspensionState=u,"active-value":1,"inactive-value":2,onChange:u=>(async o=>{const g=o.processDefinition.suspensionState;try{const pe=o.id,me="\u662F\u5426\u786E\u8BA4"+(g===1?"\u6FC0\u6D3B":"\u6302\u8D77")+'\u6D41\u7A0B\u540D\u5B57\u4E3A"'+o.name+'"\u7684\u6570\u636E\u9879?';await w.confirm(me),await $e(pe,g),await y()}catch{o.processDefinition.suspensionState=g===1?2:1}})(e.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])):A("",!0)]),_:1}),a(m,{label:"\u90E8\u7F72\u65F6\u95F4",align:"center",prop:"deploymentTime",width:"180"},{default:t(e=>[e.row.processDefinition?(i(),N("span",I,b(l(Oe)(e.row.processDefinition.deploymentTime)),1)):A("",!0)]),_:1})]),_:1}),a(m,{label:"\u64CD\u4F5C",align:"center",width:"240",fixed:"right"},{default:t(e=>[f((i(),n(c,{link:"",type:"primary",onClick:u=>L("update",e.row.id)},{default:t(()=>[p(" \u4FEE\u6539\u6D41\u7A0B ")]),_:2},1032,["onClick"])),[[h,["bpm:model:update"]]]),f((i(),n(c,{link:"",type:"primary",onClick:u=>{return o=e.row,void v({name:"BpmModelEditor",query:{modelId:o.id}});var o}},{default:t(()=>[p(" \u8BBE\u8BA1\u6D41\u7A0B ")]),_:2},1032,["onClick"])),[[h,["bpm:model:update"]]]),f((i(),n(c,{link:"",type:"primary",onClick:u=>{return o=e.row.id,void v({name:"SimpleWorkflowDesignEditor",query:{modelId:o.id}});var o}},{default:t(()=>[p(" \u4EFF\u9489\u9489\u8BBE\u8BA1\u6D41\u7A0B ")]),_:2},1032,["onClick"])),[[h,["bpm:model:update"]]]),f((i(),n(c,{link:"",type:"primary",onClick:u=>(async o=>{try{await w.confirm("\u662F\u5426\u90E8\u7F72\u8BE5\u6D41\u7A0B\uFF01\uFF01"),await ea(o.id),w.success(R("\u90E8\u7F72\u6210\u529F")),await y()}catch{}})(e.row)},{default:t(()=>[p(" \u53D1\u5E03\u6D41\u7A0B ")]),_:2},1032,["onClick"])),[[h,["bpm:model:deploy"]]]),f((i(),n(c,{link:"",type:"primary",onClick:u=>{return o=e.row,void v({name:"BpmProcessDefinition",query:{key:o.key}});var o}},{default:t(()=>[p(" \u6D41\u7A0B\u5B9A\u4E49 ")]),_:2},1032,["onClick"])),[[h,["bpm:process-definition:query"]]]),f((i(),n(c,{link:"",type:"danger",onClick:u=>(async o=>{try{await w.delConfirm(),await Ae(o),w.success(R("common.delSuccess")),await y()}catch{}})(e.row.id)},{default:t(()=>[p(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[h,["bpm:model:delete"]]])]),_:1})]),_:1},8,["data"])),[[_e,l(P)]]),a(se,{total:l(j),page:l(s).pageNo,"onUpdate:page":r[4]||(r[4]=e=>l(s).pageNo=e),limit:l(s).pageSize,"onUpdate:limit":r[5]||(r[5]=e=>l(s).pageSize=e),onPagination:y},null,8,["total","page","limit"])]),_:1}),a(oa,{ref_key:"formRef",ref:M,onSuccess:y},null,512),a(na,{ref_key:"importFormRef",ref:Z,onSuccess:y},null,512),a(W,{title:"\u8868\u5355\u8BE6\u60C5",modelValue:l(V),"onUpdate:modelValue":r[6]||(r[6]=e=>B(V)?V.value=e:null),width:"800"},{default:t(()=>[a(ce,{rule:l(T).rule,option:l(T).option},null,8,["rule","option"])]),_:1},8,["modelValue"]),a(W,{title:"\u6D41\u7A0B\u56FE",modelValue:l(x),"onUpdate:modelValue":r[8]||(r[8]=e=>B(x)?x.value=e:null),width:"800"},{default:t(()=>[a(l(We),ve({key:"designer",modelValue:l(k),"onUpdate:modelValue":r[7]||(r[7]=e=>B(k)?k.value=e:null),value:l(k)},l(H),{prefix:l(H).prefix}),null,16,["modelValue","value","prefix"])]),_:1},8,["modelValue"])],64)}}})});export{Ua as __tla,$ as default};
