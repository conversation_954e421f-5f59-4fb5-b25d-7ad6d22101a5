import{d as y,o as s,c as r,g as e,av as t,i as p,t as n,F as h,k as u,H as _,a8 as g,_ as v,B as b,__tla as m}from"./index-BUSn51wb.js";let c,x=Promise.all([(()=>{try{return m}catch{}})()]).then(async()=>{let o;o={class:"right"},c=b(y({name:"SearchBar",__name:"index",props:{property:{}},setup:k=>(a,f)=>{const l=v;return s(),r("div",{class:"search-bar",style:t({color:a.property.textColor})},[e("div",{class:"inner",style:t({height:`${a.property.height}px`,background:a.property.backgroundColor,borderRadius:`${a.property.borderRadius}px`})},[e("div",{class:"placeholder",style:t({justifyContent:a.property.placeholderPosition})},[p(l,{icon:"ep:search"}),e("span",null,n(a.property.placeholder||"\u641C\u7D22\u5546\u54C1"),1)],4),e("div",o,[(s(!0),r(h,null,u(a.property.hotKeywords,(d,i)=>(s(),r("span",{key:i},n(d),1))),128)),_(p(l,{icon:"ant-design:scan-outlined"},null,512),[[g,a.property.showScan]])])],4)],4)}}),[["__scopeId","data-v-d4436951"]])});export{x as __tla,c as default};
