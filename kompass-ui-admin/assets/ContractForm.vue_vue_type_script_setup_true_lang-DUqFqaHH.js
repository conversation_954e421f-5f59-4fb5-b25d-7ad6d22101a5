import{d as le,r as n,f as ae,at as te,b as S,o as i,l as m,w as a,i as e,j as z,a as t,H as de,c as V,k as I,F as y,y as A,dV as oe,n as ue,I as re,dW as se,e as ne,Z as ie,L as ce,E as me,J as pe,K as _e,s as fe,M as ve,z as be,A as Ve,cc as Ie,O as ye,N as ge,R as Ue,__tla as we}from"./index-BUSn51wb.js";import{_ as Pe,__tla as he}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{_ as ke,__tla as Ce}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{a as Te,__tla as xe}from"./index-CD52sTBY.js";import{b as De,c as Fe,u as qe,__tla as Re}from"./index-DrB1WZUR.js";import{g as Se,__tla as ze}from"./index-BYXzDB8j.js";import{b as Ae,__tla as Ee}from"./index-9ux5MgCS.js";import{d as He,a as Je,__tla as Ke}from"./index-M52UJVMY.js";import{_ as Le,__tla as We}from"./ContractProductForm.vue_vue_type_script_setup_true_lang-CcyDfNfh.js";let E,je=Promise.all([(()=>{try{return we}catch{}})(),(()=>{try{return he}catch{}})(),(()=>{try{return Ce}catch{}})(),(()=>{try{return xe}catch{}})(),(()=>{try{return Re}catch{}})(),(()=>{try{return ze}catch{}})(),(()=>{try{return Ee}catch{}})(),(()=>{try{return Ke}catch{}})(),(()=>{try{return We}catch{}})()]).then(async()=>{E=le({__name:"ContractForm",emits:["success"],setup(Me,{expose:H,emit:J}){const{t:w}=ue(),C=re(),p=n(!1),T=n(""),_=n(!1),g=n(""),d=n({id:void 0,no:void 0,name:void 0,customerId:void 0,businessId:void 0,orderDate:void 0,startTime:void 0,endTime:void 0,signUserId:void 0,signContactId:void 0,ownerUserId:void 0,discountPercent:0,totalProductPrice:void 0,remark:void 0,products:[]}),K=ae({name:[{required:!0,message:"\u5408\u540C\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],customerId:[{required:!0,message:"\u5BA2\u6237\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],orderDate:[{required:!0,message:"\u4E0B\u5355\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],ownerUserId:[{required:!0,message:"\u8D1F\u8D23\u4EBA\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),U=n(),P=n([]),x=n([]),D=n([]),F=n([]),h=n("product"),q=n();te(()=>d.value,u=>{if(!u)return;const o=u.products.reduce((r,s)=>r+s.totalPrice,0),c=o-(u.discountPercent!=null?se(o,u.discountPercent/100):0);d.value.totalProductPrice=o,d.value.totalPrice=c},{deep:!0}),H({open:async(u,o)=>{if(p.value=!0,T.value=w("action."+u),g.value=u,j(),o){_.value=!0;try{d.value=await De(o)}finally{_.value=!1}}x.value=await Te(),P.value=await Se(),g.value==="create"&&(d.value.ownerUserId=ne().getUser.id),F.value=await Ae(),D.value=await He()}});const L=J,W=async()=>{if(U&&await U.value.validate()){_.value=!0,q.value.validate();try{const u=t(d.value);g.value==="create"?(await Fe(u),C.success(w("common.createSuccess"))):(await qe(u),C.success(w("common.updateSuccess"))),p.value=!1,L("success")}finally{_.value=!1}}},j=()=>{var u;d.value={id:void 0,no:void 0,name:void 0,customerId:void 0,businessId:void 0,orderDate:void 0,startTime:void 0,endTime:void 0,signUserId:void 0,signContactId:void 0,ownerUserId:void 0,discountPercent:0,totalProductPrice:void 0,remark:void 0,products:[]},(u=U.value)==null||u.resetFields()},M=()=>{d.value.businessId=void 0,d.value.signContactId=void 0,d.value.products=[]},N=async u=>{const o=await Je(u);o.products.forEach(c=>{c.contractPrice=c.businessPrice}),d.value.products=o.products},Z=S(()=>F.value.filter(u=>u.customerId==d.value.customerId)),B=S(()=>D.value.filter(u=>u.customerId==d.value.customerId));return(u,o)=>{const c=ie,r=ce,s=me,f=pe,v=_e,b=fe,k=ve,G=be,O=Ve,Q=ke,X=Ie,Y=ye,R=ge,$=Pe,ee=Ue;return i(),m($,{modelValue:t(p),"onUpdate:modelValue":o[16]||(o[16]=l=>A(p)?p.value=l:null),title:t(T),width:"1280"},{footer:a(()=>[e(R,{disabled:t(_),type:"primary",onClick:W},{default:a(()=>[z("\u4FDD\u5B58")]),_:1},8,["disabled"]),e(R,{onClick:o[15]||(o[15]=l=>p.value=!1)},{default:a(()=>[z("\u53D6 \u6D88")]),_:1})]),default:a(()=>[de((i(),m(Y,{ref_key:"formRef",ref:U,model:t(d),rules:t(K),"label-width":"120px"},{default:a(()=>[e(b,null,{default:a(()=>[e(s,{span:8},{default:a(()=>[e(r,{label:"\u5408\u540C\u7F16\u53F7",prop:"no"},{default:a(()=>[e(c,{disabled:"",modelValue:t(d).no,"onUpdate:modelValue":o[0]||(o[0]=l=>t(d).no=l),placeholder:"\u4FDD\u5B58\u65F6\u81EA\u52A8\u751F\u6210"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:a(()=>[e(r,{label:"\u5408\u540C\u540D\u79F0",prop:"name"},{default:a(()=>[e(c,{modelValue:t(d).name,"onUpdate:modelValue":o[1]||(o[1]=l=>t(d).name=l),placeholder:"\u8BF7\u8F93\u5165\u5408\u540C\u540D\u79F0"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:a(()=>[e(r,{label:"\u8D1F\u8D23\u4EBA",prop:"ownerUserId"},{default:a(()=>[e(v,{modelValue:t(d).ownerUserId,"onUpdate:modelValue":o[2]||(o[2]=l=>t(d).ownerUserId=l),disabled:t(g)!=="create",class:"w-1/1"},{default:a(()=>[(i(!0),V(y,null,I(t(P),l=>(i(),m(f,{key:l.id,label:l.nickname,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),e(b,null,{default:a(()=>[e(s,{span:8},{default:a(()=>[e(r,{label:"\u5BA2\u6237\u540D\u79F0",prop:"customerId"},{default:a(()=>[e(v,{modelValue:t(d).customerId,"onUpdate:modelValue":o[3]||(o[3]=l=>t(d).customerId=l),placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237",class:"w-1/1",onChange:M},{default:a(()=>[(i(!0),V(y,null,I(t(x),l=>(i(),m(f,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:a(()=>[e(r,{label:"\u5546\u673A\u540D\u79F0",prop:"businessId"},{default:a(()=>[e(v,{onChange:N,disabled:!t(d).customerId,modelValue:t(d).businessId,"onUpdate:modelValue":o[4]||(o[4]=l=>t(d).businessId=l),class:"w-1/1"},{default:a(()=>[(i(!0),V(y,null,I(t(B),l=>(i(),m(f,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["disabled","modelValue"])]),_:1})]),_:1})]),_:1}),e(b,null,{default:a(()=>[e(s,{span:8},{default:a(()=>[e(r,{label:"\u4E0B\u5355\u65E5\u671F",prop:"orderDate"},{default:a(()=>[e(k,{modelValue:t(d).orderDate,"onUpdate:modelValue":o[5]||(o[5]=l=>t(d).orderDate=l),placeholder:"\u9009\u62E9\u4E0B\u5355\u65E5\u671F",type:"date","value-format":"x",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:a(()=>[e(r,{label:"\u5F00\u59CB\u65F6\u95F4",prop:"startTime"},{default:a(()=>[e(k,{modelValue:t(d).startTime,"onUpdate:modelValue":o[6]||(o[6]=l=>t(d).startTime=l),placeholder:"\u9009\u62E9\u5F00\u59CB\u65F6\u95F4",type:"date","value-format":"x",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:a(()=>[e(r,{label:"\u7ED3\u675F\u65F6\u95F4",prop:"endTime"},{default:a(()=>[e(k,{modelValue:t(d).endTime,"onUpdate:modelValue":o[7]||(o[7]=l=>t(d).endTime=l),placeholder:"\u9009\u62E9\u7ED3\u675F\u65F6\u95F4",type:"date","value-format":"x",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(b,null,{default:a(()=>[e(s,{span:8},{default:a(()=>[e(r,{label:"\u516C\u53F8\u7B7E\u7EA6\u4EBA",prop:"signUserId"},{default:a(()=>[e(v,{modelValue:t(d).signUserId,"onUpdate:modelValue":o[8]||(o[8]=l=>t(d).signUserId=l),class:"w-1/1"},{default:a(()=>[(i(!0),V(y,null,I(t(P),l=>(i(),m(f,{key:l.id,label:l.nickname,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:a(()=>[e(r,{label:"\u5BA2\u6237\u7B7E\u7EA6\u4EBA",prop:"signContactId"},{default:a(()=>[e(v,{modelValue:t(d).signContactId,"onUpdate:modelValue":o[9]||(o[9]=l=>t(d).signContactId=l),disabled:!t(d).customerId,class:"w-1/1"},{default:a(()=>[(i(!0),V(y,null,I(t(Z),l=>(i(),m(f,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(s,{span:8},{default:a(()=>[e(r,{label:"\u5907\u6CE8",prop:"remark"},{default:a(()=>[e(c,{modelValue:t(d).remark,"onUpdate:modelValue":o[10]||(o[10]=l=>t(d).remark=l),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(Q,null,{default:a(()=>[e(O,{modelValue:t(h),"onUpdate:modelValue":o[11]||(o[11]=l=>A(h)?h.value=l:null),class:"-mt-15px -mb-10px"},{default:a(()=>[e(G,{label:"\u4EA7\u54C1\u6E05\u5355",name:"product"},{default:a(()=>[e(Le,{ref_key:"productFormRef",ref:q,products:t(d).products,disabled:u.disabled},null,8,["products","disabled"])]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(b,null,{default:a(()=>[e(s,{span:8},{default:a(()=>[e(r,{label:"\u4EA7\u54C1\u603B\u91D1\u989D",prop:"totalProductPrice"},{default:a(()=>[e(c,{disabled:"",modelValue:t(d).totalProductPrice,"onUpdate:modelValue":o[12]||(o[12]=l=>t(d).totalProductPrice=l),formatter:t(oe)},null,8,["modelValue","formatter"])]),_:1})]),_:1}),e(s,{span:8},{default:a(()=>[e(r,{label:"\u6574\u5355\u6298\u6263\uFF08%\uFF09",prop:"discountPercent"},{default:a(()=>[e(X,{modelValue:t(d).discountPercent,"onUpdate:modelValue":o[13]||(o[13]=l=>t(d).discountPercent=l),placeholder:"\u8BF7\u8F93\u5165\u6574\u5355\u6298\u6263","controls-position":"right",min:0,precision:2,class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:a(()=>[e(r,{label:"\u6298\u6263\u540E\u91D1\u989D",prop:"totalPrice"},{default:a(()=>[e(c,{disabled:"",modelValue:t(d).totalPrice,"onUpdate:modelValue":o[14]||(o[14]=l=>t(d).totalPrice=l),placeholder:"\u8BF7\u8F93\u5165\u5546\u673A\u91D1\u989D",formatter:u.erpPriceTableColumnFormattere},null,8,["modelValue","formatter"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])),[[ee,t(_)]])]),_:1},8,["modelValue","title"])}}})});export{E as _,je as __tla};
