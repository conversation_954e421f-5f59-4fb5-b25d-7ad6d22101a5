import{by as a,__tla as r}from"./index-BUSn51wb.js";let t,i=Promise.all([(()=>{try{return r}catch{}})()]).then(async()=>{t={getTeacherInterviewPage:async e=>await a.get({url:"/als/teacher-interview/page",params:e}),getTeacherInterview:async e=>await a.get({url:"/als/teacher-interview/get?id="+e}),reservationInterview:async e=>await a.post({url:"/als/teacher-interview/reservation",data:e}),createTeacherInterview:async e=>await a.post({url:"/als/teacher-interview/create",data:e}),updateTeacherInterview:async e=>await a.put({url:"/als/teacher-interview/update",data:e}),deleteTeacherInterview:async e=>await a.delete({url:"/als/teacher-interview/delete?id="+e}),exportTeacherInterview:async e=>await a.download({url:"/als/teacher-interview/export-excel",params:e})}});export{t as T,i as __tla};
