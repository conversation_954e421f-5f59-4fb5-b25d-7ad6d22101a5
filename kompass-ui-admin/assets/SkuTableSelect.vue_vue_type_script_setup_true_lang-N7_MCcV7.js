import{d as T,p as P,I as B,r as d,C as E,at as F,o as h,l as f,w as e,H,a as l,Q as N,i as t,y as v,j as c,t as y,aF as Q,am as R,P as q,R as z,__tla as A}from"./index-BUSn51wb.js";import{_ as D,__tla as G}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{E as J,__tla as K}from"./el-image-BjHZRFih.js";import{g as L,__tla as M}from"./spu-CW3JGweV.js";let b,O=Promise.all([(()=>{try{return A}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return K}catch{}})(),(()=>{try{return M}catch{}})()]).then(async()=>{b=T({name:"SkuTableSelect",__name:"SkuTableSelect",props:{spuId:P.number.def(null)},emits:["change"],setup(g,{expose:V,emit:x}){const _=g;B();const m=d([]),p=d(!1),s=d(!1),o=d(),I=x;return V({open:()=>{s.value=!0}}),E(async()=>{}),F(()=>_.spuId,()=>{_.spuId&&(async()=>{p.value=!0;try{const w=await L(_.spuId);m.value=w.skus}finally{p.value=!1}})()}),(w,u)=>{const S=R,n=q,U=J,j=D,k=z;return h(),f(j,{modelValue:l(s),"onUpdate:modelValue":u[1]||(u[1]=a=>v(s)?s.value=a:null),appendToBody:!0,title:"\u9009\u62E9\u89C4\u683C",width:"700"},{default:e(()=>[H((h(),f(l(N),{data:l(m),"show-overflow-tooltip":""},{default:e(()=>[t(n,{label:"#",width:"55"},{default:e(({row:a})=>[t(S,{label:a.id,modelValue:l(o),"onUpdate:modelValue":u[0]||(u[0]=r=>v(o)?o.value=r:null),onChange:r=>(i=>{I("change",i),s.value=!1,o.value=void 0})(a)},{default:e(()=>[c("\xA0 ")]),_:2},1032,["label","modelValue","onChange"])]),_:1}),t(n,{label:"\u56FE\u7247","min-width":"80"},{default:e(({row:a})=>[t(U,{src:a.picUrl,class:"h-30px w-30px","preview-src-list":[a.picUrl],"preview-teleported":""},null,8,["src","preview-src-list"])]),_:1}),t(n,{label:"\u89C4\u683C",align:"center","min-width":"80"},{default:e(({row:a})=>{var r,i;return[c(y((i=(r=a.properties)==null?void 0:r.map(C=>C.valueName))==null?void 0:i.join(" ")),1)]}),_:1}),t(n,{align:"center",label:"\u9500\u552E\u4EF7(\u5143)","min-width":"80"},{default:e(({row:a})=>[c(y(l(Q)(a.price)),1)]),_:1})]),_:1},8,["data"])),[[k,l(p)]])]),_:1},8,["modelValue"])}}})});export{b as _,O as __tla};
