import{ct as u,__tla as H}from"./index-BUSn51wb.js";let i,c,l,m,D,s,Y,M,g,d,w,h,o,T,b,y,x=Promise.all([(()=>{try{return H}catch{}})()]).then(async()=>{M=[{text:"\u4ECA\u5929",value:()=>new Date},{text:"\u6628\u5929",value:()=>{const t=new Date;return t.setTime(t.getTime()-864e5),[t,t]}},{text:"\u6700\u8FD1\u4E03\u5929",value:()=>{const t=new Date;return t.setTime(t.getTime()-6048e5),[t,new Date]}},{text:"\u6700\u8FD1 30 \u5929",value:()=>{const t=new Date;return t.setTime(t.getTime()-2592e6),[t,new Date]}},{text:"\u672C\u6708",value:()=>{const t=new Date;return t.setDate(1),[t,new Date]}},{text:"\u4ECA\u5E74",value:()=>{const t=new Date;return[new Date(`${t.getFullYear()}-01-01`),t]}}],s=function(t,e){return t&&t?u(t).format(e??"YYYY-MM-DD HH:mm:ss"):""},l=function(t,e="YYYY-mm-dd HH:MM:SS"){let n,r,a=new Date().getTime();return n=new Date(t).getTime(),a=Number.parseInt(""+(a-n)),a<1e4?"\u521A\u521A":a<6e4&&a>=1e4?(r=Math.floor(a/1e3),`${r}\u79D2\u524D`):a<36e5&&a>=6e4?(r=Math.floor(a/6e4),`${r}\u5206\u949F\u524D`):a<864e5&&a>=36e5?(r=Math.floor(a/36e5),`${r}\u5C0F\u65F6\u524D`):a<2592e5&&a>=864e5?(r=Math.floor(a/864e5),`${r}\u5929\u524D`):s(new Date(t),e)},i=function(t){const e=Math.floor(t/864e5),n=Math.floor(t/36e5-24*e),r=Math.floor(t/6e4-24*e*60-60*n),a=Math.floor(t/1e3-24*e*60*60-60*n*60-60*r);return e>0?e+" \u5929"+n+" \u5C0F\u65F6 "+r+" \u5206\u949F":n>0?n+" \u5C0F\u65F6 "+r+" \u5206\u949F":r>0?r+" \u5206\u949F":a>0?a+" \u79D2":"0 \u79D2"},m=function(t,e,n){return n?s(n):""},c=function(t,e,n){return n?s(n,"YYYY-MM-DD"):""},D=function(t){return new Date(t.getFullYear(),t.getMonth(),t.getDate(),0,0,0)},Y=function(t){return new Date(t.getFullYear(),t.getMonth(),t.getDate(),23,59,59)},b=function(t,e){return t=f(t),e=f(e),Math.floor((e.getTime()-t.getTime())/864e5)},y=function(t,e){return t=f(t),new Date(t.getTime()+e)};function f(t){return typeof t=="string"?new Date(t):t}T=function(t,e){if(!t||!e)return!1;const n=u(t),r=u(e);return n.year()==r.year()&&n.month()==r.month()&&n.day()==r.day()},g=function(t,e){const n=u(t).add(e,"d");return o(n,n)},d=function(){return o(u().subtract(7,"d"),u().subtract(1,"d"))},w=function(){return o(u().subtract(30,"d"),u().subtract(1,"d"))},h=function(){return o(u().subtract(1,"y"),u().subtract(1,"d"))},o=function(t,e){return[u(t).startOf("d").format("YYYY-MM-DD HH:mm:ss"),u(e).endOf("d").format("YYYY-MM-DD HH:mm:ss")]}});export{x as __tla,i as a,c as b,l as c,m as d,D as e,s as f,Y as g,M as h,g as i,d as j,w as k,h as l,o as m,T as n,b as o,y as p};
