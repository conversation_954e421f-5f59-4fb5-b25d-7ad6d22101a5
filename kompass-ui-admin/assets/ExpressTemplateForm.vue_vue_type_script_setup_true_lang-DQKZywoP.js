import{by as C,d as K,n as W,I as Y,r as i,f as ee,C as ae,o as y,l as U,w as l,i as a,a as r,j as m,H as le,c as te,F as re,k as oe,V as se,G as ue,t as de,y as ne,aF as k,aD as ie,eF as T,Z as ce,L as pe,am as me,an as fe,cd as _e,P as ge,cc as Ve,N as he,Q as ve,_ as Ce,O as be,R as xe,__tla as ye}from"./index-BUSn51wb.js";import{_ as we,__tla as Pe}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{g as Ue,__tla as ke}from"./index-CyP7ZSdX.js";import{d as Te}from"./tree-BMa075Oj.js";let q,D,G,Ee=Promise.all([(()=>{try{return ye}catch{}})(),(()=>{try{return Pe}catch{}})(),(()=>{try{return ke}catch{}})()]).then(async()=>{G=async b=>await C.get({url:"/trade/delivery/express-template/page",params:b}),D=async b=>await C.delete({url:"/trade/delivery/express-template/delete?id="+b}),q=K({__name:"ExpressTemplateForm",emits:["success"],setup(b,{expose:H,emit:L}){const{t:w}=W(),E=Y(),M={...Te,multiple:!0},f=i(!1),I=i(""),_=i(!1),F=i(""),o=i({id:void 0,name:"",chargeMode:1,sort:0,charges:[],frees:[]}),g=new Map,V=i({startCountTitle:"\u9996\u4EF6",extraCountTitle:"\u7EED\u4EF6",freeCountTitle:"\u5305\u90AE\u4EF6\u6570"}),$=ee({name:[{required:!0,message:"\u6A21\u677F\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],chargeMode:[{required:!0,message:"\u914D\u9001\u8BA1\u8D39\u65B9\u5F0F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],sort:[{required:!0,message:"\u5206\u7C7B\u6392\u5E8F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),x=i();H({open:async(u,t)=>{f.value=!0,I.value=w("action."+u),F.value=u,N();try{t&&(_.value=!0,o.value=await(async d=>await C.get({url:"/trade/delivery/express-template/get?id="+d}))(t),V.value=g.get(o.value.chargeMode),o.value.charges.forEach(d=>{d.startPrice=k(d.startPrice),d.extraPrice=k(d.extraPrice)}),o.value.frees.forEach(d=>{d.freePrice=k(d.freePrice)}))}finally{_.value=!1}}});const j=L,A=async()=>{if(x&&await x.value.validate()){_.value=!0;try{const u=ie(o.value);u.charges.forEach(t=>{t.startPrice=T(t.startPrice),t.extraPrice=T(t.extraPrice)}),u.frees.forEach(t=>{t.freePrice=T(t.freePrice)}),F.value==="create"?(await(async t=>await C.post({url:"/trade/delivery/express-template/create",data:t}))(u),E.success(w("common.createSuccess"))):(await(async t=>await C.put({url:"/trade/delivery/express-template/update",data:t}))(u),E.success(w("common.updateSuccess"))),f.value=!1,j("success")}finally{_.value=!1}}},N=()=>{var u;o.value={id:void 0,name:"",chargeMode:1,charges:[{areaIds:[1],startCount:2,startPrice:5,extraCount:5,extraPrice:10}],frees:[],sort:0},V.value=g.get(1),(u=x.value)==null||u.resetFields()},Q=u=>{V.value=g.get(u)},P=i([]);return ae(()=>{(async()=>(g.set(1,{startCountTitle:"\u9996\u4EF6",extraCountTitle:"\u7EED\u4EF6",freeCountTitle:"\u5305\u90AE\u4EF6\u6570"}),g.set(2,{startCountTitle:"\u9996\u4EF6\u91CD\u91CF(kg)",extraCountTitle:"\u7EED\u4EF6\u91CD\u91CF(kg)",freeCountTitle:"\u5305\u90AE\u91CD\u91CF(kg)"}),g.set(3,{startCountTitle:"\u9996\u4EF6\u4F53\u79EF(m\xB3)",extraCountTitle:"\u7EED\u4EF6\u4F53\u79EF(m\xB3)",freeCountTitle:"\u5305\u90AE\u4F53\u79EF(m\xB3)"}),P.value=await Ue()))()}),(u,t)=>{const d=ce,c=pe,X=me,Z=fe,O=_e,n=ge,p=Ve,h=he,R=ve,S=Ce,z=be,B=we,J=xe;return y(),U(B,{title:r(I),modelValue:r(f),"onUpdate:modelValue":t[6]||(t[6]=e=>ne(f)?f.value=e:null),width:"1300px"},{footer:l(()=>[a(h,{onClick:A,type:"primary",disabled:r(_)},{default:l(()=>[m("\u786E \u5B9A")]),_:1},8,["disabled"]),a(h,{onClick:t[5]||(t[5]=e=>f.value=!1)},{default:l(()=>[m("\u53D6 \u6D88")]),_:1})]),default:l(()=>[le((y(),U(z,{ref_key:"formRef",ref:x,model:r(o),rules:r($),"label-width":"80px"},{default:l(()=>[a(c,{label:"\u6A21\u677F\u540D\u79F0",prop:"name"},{default:l(()=>[a(d,{modelValue:r(o).name,"onUpdate:modelValue":t[0]||(t[0]=e=>r(o).name=e),placeholder:"\u8BF7\u8F93\u5165\u6A21\u677F\u540D\u79F0"},null,8,["modelValue"])]),_:1}),a(c,{label:"\u8BA1\u8D39\u65B9\u5F0F",prop:"chargeMode"},{default:l(()=>[a(Z,{modelValue:r(o).chargeMode,"onUpdate:modelValue":t[1]||(t[1]=e=>r(o).chargeMode=e),onChange:Q},{default:l(()=>[(y(!0),te(re,null,oe(r(se)(r(ue).EXPRESS_CHARGE_MODE),e=>(y(),U(X,{key:e.value,label:e.value},{default:l(()=>[m(de(e.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(c,{label:"\u8FD0\u8D39",prop:"charges"},{default:l(()=>[a(R,{border:"",style:{width:"100%"},data:r(o).charges},{default:l(()=>[a(n,{align:"center",label:"\u533A\u57DF",width:"360"},{default:l(({row:e})=>[a(O,{modelValue:e.areaIds,"onUpdate:modelValue":s=>e.areaIds=s,options:r(P),props:M,class:"w-1/1",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u5730\u533A",filterable:"","collapse-tags":""},null,8,["modelValue","onUpdate:modelValue","options"])]),_:1}),a(n,{align:"center",label:r(V).startCountTitle,width:"180",prop:"startCount"},{default:l(({row:e})=>[a(p,{modelValue:e.startCount,"onUpdate:modelValue":s=>e.startCount=s,min:1},null,8,["modelValue","onUpdate:modelValue"])]),_:1},8,["label"]),a(n,{width:"180",align:"center",label:"\u8FD0\u8D39(\u5143)",prop:"startPrice"},{default:l(({row:e})=>[a(p,{modelValue:e.startPrice,"onUpdate:modelValue":s=>e.startPrice=s,min:1},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(n,{width:"180",align:"center",label:r(V).extraCountTitle,prop:"extraCount"},{default:l(({row:e})=>[a(p,{modelValue:e.extraCount,"onUpdate:modelValue":s=>e.extraCount=s,min:1},null,8,["modelValue","onUpdate:modelValue"])]),_:1},8,["label"]),a(n,{width:"180",align:"center",label:"\u7EED\u8D39(\u5143)",prop:"extraPrice"},{default:l(({row:e})=>[a(p,{modelValue:e.extraPrice,"onUpdate:modelValue":s=>e.extraPrice=s,min:1},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(n,{label:"\u64CD\u4F5C",align:"center"},{default:l(e=>[a(h,{link:"",type:"danger",onClick:s=>{return v=e.$index,void o.value.charges.splice(v,1);var v}},{default:l(()=>[m(" \u5220\u9664 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1}),a(c,null,{default:l(()=>[a(h,{type:"primary",plain:"",onClick:t[2]||(t[2]=e=>{o.value.charges.push({areaIds:[],startCount:1,startPrice:1,extraCount:1,extraPrice:1})})},{default:l(()=>[a(S,{icon:"ep:plus",class:"mr-5px"}),m(" \u6DFB\u52A0\u533A\u57DF ")]),_:1})]),_:1}),a(c,{label:"\u5305\u90AE\u533A\u57DF",prop:"frees"},{default:l(()=>[a(R,{border:"",style:{width:"100%"},data:r(o).frees},{default:l(()=>[a(n,{align:"center",label:"\u533A\u57DF",width:"360"},{default:l(({row:e})=>[a(O,{modelValue:e.areaIds,"onUpdate:modelValue":s=>e.areaIds=s,options:r(P),props:M,class:"w-1/1",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u5546\u54C1\u5206\u7C7B",filterable:"","collapse-tags":""},null,8,["modelValue","onUpdate:modelValue","options"])]),_:1}),a(n,{align:"center",label:r(V).freeCountTitle,prop:"freeCount"},{default:l(({row:e})=>[a(p,{modelValue:e.freeCount,"onUpdate:modelValue":s=>e.freeCount=s,min:1},null,8,["modelValue","onUpdate:modelValue"])]),_:1},8,["label"]),a(n,{align:"center",label:"\u5305\u90AE\u91D1\u989D\uFF08\u5143\uFF09",prop:"freePrice"},{default:l(({row:e})=>[a(p,{modelValue:e.freePrice,"onUpdate:modelValue":s=>e.freePrice=s,min:1},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),a(n,{label:"\u64CD\u4F5C",align:"center"},{default:l(e=>[a(h,{link:"",type:"danger",onClick:s=>{return v=e.$index,void o.value.frees.splice(v,1);var v}},{default:l(()=>[m(" \u5220\u9664 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1}),a(c,null,{default:l(()=>[a(h,{type:"primary",plain:"",onClick:t[3]||(t[3]=e=>{o.value.frees.push({areaIds:[],freeCount:1,freePrice:1})})},{default:l(()=>[a(S,{icon:"ep:plus",class:"mr-5px"}),m(" \u6DFB\u52A0\u533A\u57DF ")]),_:1})]),_:1}),a(c,{label:"\u6392\u5E8F",prop:"sort"},{default:l(()=>[a(p,{modelValue:r(o).sort,"onUpdate:modelValue":t[4]||(t[4]=e=>r(o).sort=e),"controls-position":"right",min:0},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[J,r(_)]])]),_:1},8,["title","modelValue"])}}})});export{q as _,Ee as __tla,D as d,G as g};
