import{_ as p,__tla as y}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{d as x,o as h,c as w,g as s,i as a,w as e,t,aV as j,j as _,a as c,dX as o,F as E,s as N,E as g,__tla as P}from"./index-BUSn51wb.js";import{E as T,a as D,__tla as F}from"./el-descriptions-item-dD3qa0ub.js";import{f as H,__tla as R}from"./formatTime-DWdBpgsM.js";let d,U=Promise.all([(()=>{try{return y}catch{}})(),(()=>{try{return P}catch{}})(),(()=>{try{return F}catch{}})(),(()=>{try{return R}catch{}})()]).then(async()=>{let i,u;i={class:"flex items-start justify-between"},u={class:"text-xl font-bold"},d=x({__name:"ReceivableDetailsHeader",props:{receivable:{}},setup:V=>(l,X)=>{const f=N,m=g,r=T,b=D,v=p;return h(),w(E,null,[s("div",null,[s("div",i,[s("div",null,[a(m,null,{default:e(()=>[a(f,null,{default:e(()=>[s("span",u,t(l.receivable.no),1)]),_:1})]),_:1})]),s("div",null,[j(l.$slots,"default")])])]),a(v,{class:"mt-10px"},{default:e(()=>[a(b,{column:5,direction:"vertical"},{default:e(()=>[a(r,{label:"\u5BA2\u6237\u540D\u79F0"},{default:e(()=>[_(t(l.receivable.customerName),1)]),_:1}),a(r,{label:"\u5408\u540C\u91D1\u989D"},{default:e(()=>{var n;return[_(t(c(o)((n=l.receivable.contract)==null?void 0:n.totalPrice)),1)]}),_:1}),a(r,{label:"\u56DE\u6B3E\u65E5\u671F"},{default:e(()=>[_(t(c(H)(l.receivable.returnTime)),1)]),_:1}),a(r,{label:"\u56DE\u6B3E\u91D1\u989D"},{default:e(()=>[_(t(c(o)(l.receivable.price)),1)]),_:1}),a(r,{label:"\u8D1F\u8D23\u4EBA"},{default:e(()=>[_(t(l.receivable.ownerUserName),1)]),_:1})]),_:1})]),_:1})],64)}})});export{d as _,U as __tla};
