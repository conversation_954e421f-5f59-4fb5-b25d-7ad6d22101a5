import{d as J,n as K,I as Q,r as _,f as W,o as i,l as r,w as d,i as u,a as l,j as s,H as X,c as C,F as k,k as E,V as I,G as h,t as x,a9 as n,y as $,aC as L,aE as U,ay as ee,Z as le,L as ae,am as te,an as de,cc as ue,M as ie,O as oe,N as re,R as se,__tla as ce}from"./index-BUSn51wb.js";import{_ as pe,__tla as ne}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{a as me,c as ve,u as ye,__tla as Te}from"./couponTemplate-CyEEfDVt.js";import{e as f,f as b,h as m}from"./constants-A8BI3pz7.js";import _e,{__tla as ge}from"./SpuShowcase-HyjHBJVE.js";import{_ as fe,__tla as Pe}from"./ProductCategorySelect.vue_vue_type_script_setup_true_lang-CJItHfu_.js";let A,Ve=Promise.all([(()=>{try{return ce}catch{}})(),(()=>{try{return ne}catch{}})(),(()=>{try{return Te}catch{}})(),(()=>{try{return ge}catch{}})(),(()=>{try{return Pe}catch{}})()]).then(async()=>{A=J({name:"CouponTemplateForm",__name:"CouponTemplateForm",emits:["success"],setup(be,{expose:D,emit:N}){const{t:S}=K(),w=Q(),y=_(!1),O=_(""),T=_(!1),q=_(""),e=_({id:void 0,name:void 0,discountType:f.PRICE.type,discountPrice:void 0,discountPercent:void 0,discountLimitPrice:void 0,usePrice:void 0,takeType:1,totalCount:void 0,takeLimitCount:void 0,validityType:b.DATE.type,validTimes:[],validStartTime:void 0,validEndTime:void 0,fixedStartTerm:void 0,fixedEndTerm:void 0,productScope:m.ALL.scope,productScopeValues:[],productCategoryIds:[],productSpuIds:[]}),Y=W({name:[{required:!0,message:"\u4F18\u60E0\u5238\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],discountType:[{required:!0,message:"\u4F18\u60E0\u5238\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],discountPrice:[{required:!0,message:"\u4F18\u60E0\u5238\u9762\u989D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],discountPercent:[{required:!0,message:"\u4F18\u60E0\u5238\u6298\u6263\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],discountLimitPrice:[{required:!0,message:"\u6700\u591A\u4F18\u60E0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],usePrice:[{required:!0,message:"\u6EE1\u591A\u5C11\u5143\u53EF\u4EE5\u4F7F\u7528\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],takeType:[{required:!0,message:"\u9886\u53D6\u65B9\u5F0F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],totalCount:[{required:!0,message:"\u53D1\u653E\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],takeLimitCount:[{required:!0,message:"\u6BCF\u4EBA\u9650\u9886\u4E2A\u6570\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],validityType:[{required:!0,message:"\u6709\u6548\u671F\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],validTimes:[{required:!0,message:"\u56FA\u5B9A\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],fixedStartTerm:[{required:!0,message:"\u5F00\u59CB\u9886\u53D6\u5929\u6570\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],fixedEndTerm:[{required:!0,message:"\u5F00\u59CB\u9886\u53D6\u5929\u6570\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],productScope:[{required:!0,message:"\u5546\u54C1\u8303\u56F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],productSpuIds:[{required:!0,message:"\u5546\u54C1\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],productCategoryIds:[{required:!0,message:"\u5206\u7C7B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),P=_();D({open:async(o,t)=>{if(y.value=!0,O.value=S("action."+o),q.value=o,F(),t){T.value=!0;try{const p=await me(t);e.value={...p,discountPrice:L(p.discountPrice),discountPercent:p.discountPercent!==void 0?p.discountPercent/10:void 0,discountLimitPrice:L(p.discountLimitPrice),usePrice:L(p.usePrice),validTimes:[p.validStartTime,p.validEndTime]},await j()}finally{T.value=!1}}}});const M=N,G=async()=>{if(P&&await P.value.validate()){T.value=!0;try{const o={...e.value,discountPrice:U(e.value.discountPrice),discountPercent:e.value.discountPercent!==void 0?10*e.value.discountPercent:void 0,discountLimitPrice:U(e.value.discountLimitPrice),usePrice:U(e.value.usePrice),validStartTime:e.value.validTimes&&e.value.validTimes.length===2?e.value.validTimes[0]:void 0,validEndTime:e.value.validTimes&&e.value.validTimes.length===2?e.value.validTimes[1]:void 0};(function(t){switch(e.value.productScope){case m.SPU.scope:t.productScopeValues=e.value.productSpuIds;break;case m.CATEGORY.scope:t.productScopeValues=Array.isArray(e.value.productCategoryIds)?e.value.productCategoryIds:[e.value.productCategoryIds]}})(o),q.value==="create"?(await ve(o),w.success(S("common.createSuccess"))):(await ye(o),w.success(S("common.updateSuccess"))),y.value=!1,M("success")}finally{T.value=!1}}},F=()=>{var o;e.value={id:void 0,name:void 0,discountType:f.PRICE.type,discountPrice:void 0,discountPercent:void 0,discountLimitPrice:void 0,usePrice:void 0,takeType:1,totalCount:void 0,takeLimitCount:void 0,validityType:b.DATE.type,validTimes:[],validStartTime:void 0,validEndTime:void 0,fixedStartTerm:void 0,fixedEndTerm:void 0,productScope:m.ALL.scope,productScopeValues:[],productSpuIds:[],productCategoryIds:[]},(o=P.value)==null||o.resetFields()},j=async()=>{switch(e.value.productScope){case m.SPU.scope:e.value.productSpuIds=e.value.productScopeValues;break;case m.CATEGORY.scope:await ee(()=>{let o=e.value.productScopeValues;Array.isArray(o)&&o.length>0&&(o=o[0]),e.value.productCategoryIds=o})}};return(o,t)=>{const p=le,c=ae,g=te,V=de,v=ue,H=ie,Z=oe,R=re,z=pe,B=se;return i(),r(z,{modelValue:l(y),"onUpdate:modelValue":t[17]||(t[17]=a=>$(y)?y.value=a:null),title:l(O)},{footer:d(()=>[u(R,{disabled:l(T),type:"primary",onClick:G},{default:d(()=>[s("\u786E \u5B9A")]),_:1},8,["disabled"]),u(R,{onClick:t[16]||(t[16]=a=>y.value=!1)},{default:d(()=>[s("\u53D6 \u6D88")]),_:1})]),default:d(()=>[X((i(),r(Z,{ref_key:"formRef",ref:P,model:l(e),rules:l(Y),"label-width":"140px"},{default:d(()=>[u(c,{label:"\u4F18\u60E0\u5238\u540D\u79F0",prop:"name"},{default:d(()=>[u(p,{modelValue:l(e).name,"onUpdate:modelValue":t[0]||(t[0]=a=>l(e).name=a),placeholder:"\u8BF7\u8F93\u5165\u4F18\u60E0\u5238\u540D\u79F0"},null,8,["modelValue"])]),_:1}),u(c,{label:"\u4F18\u60E0\u52B5\u7C7B\u578B",prop:"productScope"},{default:d(()=>[u(V,{modelValue:l(e).productScope,"onUpdate:modelValue":t[1]||(t[1]=a=>l(e).productScope=a)},{default:d(()=>[(i(!0),C(k,null,E(l(I)(l(h).PROMOTION_PRODUCT_SCOPE),a=>(i(),r(g,{key:a.value,label:a.value},{default:d(()=>[s(x(a.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(e).productScope===l(m).SPU.scope?(i(),r(c,{key:0,label:"\u5546\u54C1",prop:"productSpuIds"},{default:d(()=>[u(_e,{modelValue:l(e).productSpuIds,"onUpdate:modelValue":t[2]||(t[2]=a=>l(e).productSpuIds=a)},null,8,["modelValue"])]),_:1})):n("",!0),l(e).productScope===l(m).CATEGORY.scope?(i(),r(c,{key:1,label:"\u5206\u7C7B",prop:"productCategoryIds"},{default:d(()=>[u(fe,{modelValue:l(e).productCategoryIds,"onUpdate:modelValue":t[3]||(t[3]=a=>l(e).productCategoryIds=a)},null,8,["modelValue"])]),_:1})):n("",!0),u(c,{label:"\u4F18\u60E0\u7C7B\u578B",prop:"discountType"},{default:d(()=>[u(V,{modelValue:l(e).discountType,"onUpdate:modelValue":t[4]||(t[4]=a=>l(e).discountType=a)},{default:d(()=>[(i(!0),C(k,null,E(l(I)(l(h).PROMOTION_DISCOUNT_TYPE),a=>(i(),r(g,{key:a.value,label:a.value},{default:d(()=>[s(x(a.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(e).discountType===l(f).PRICE.type?(i(),r(c,{key:2,label:"\u4F18\u60E0\u5238\u9762\u989D",prop:"discountPrice"},{default:d(()=>[u(v,{modelValue:l(e).discountPrice,"onUpdate:modelValue":t[5]||(t[5]=a=>l(e).discountPrice=a),min:0,precision:2,class:"mr-2 !w-329px",placeholder:"\u8BF7\u8F93\u5165\u4F18\u60E0\u91D1\u989D\uFF0C\u5355\u4F4D\uFF1A\u5143"},null,8,["modelValue"]),s(" \u5143 ")]),_:1})):n("",!0),l(e).discountType===l(f).PERCENT.type?(i(),r(c,{key:3,label:"\u4F18\u60E0\u5238\u6298\u6263",prop:"discountPercent"},{default:d(()=>[u(p,{modelValue:l(e).discountPercent,"onUpdate:modelValue":t[6]||(t[6]=a=>l(e).discountPercent=a),max:9.9,min:1,precision:1,class:"mr-2 !w-329px",placeholder:"\u4F18\u60E0\u5238\u6298\u6263\u4E0D\u80FD\u5C0F\u4E8E 1 \u6298\uFF0C\u4E14\u4E0D\u53EF\u5927\u4E8E 9.9 \u6298",type:"number"},null,8,["modelValue"]),s(" \u6298 ")]),_:1})):n("",!0),l(e).discountType===l(f).PERCENT.type?(i(),r(c,{key:4,label:"\u6700\u591A\u4F18\u60E0",prop:"discountLimitPrice"},{default:d(()=>[u(v,{modelValue:l(e).discountLimitPrice,"onUpdate:modelValue":t[7]||(t[7]=a=>l(e).discountLimitPrice=a),min:0,precision:2,class:"mr-2 !w-329px",placeholder:"\u8BF7\u8F93\u5165\u6700\u591A\u4F18\u60E0"},null,8,["modelValue"]),s(" \u5143 ")]),_:1})):n("",!0),u(c,{label:"\u6EE1\u591A\u5C11\u5143\u53EF\u4EE5\u4F7F\u7528",prop:"usePrice"},{default:d(()=>[u(v,{modelValue:l(e).usePrice,"onUpdate:modelValue":t[8]||(t[8]=a=>l(e).usePrice=a),min:0,precision:2,class:"mr-2 !w-329px",placeholder:"\u65E0\u95E8\u69DB\u8BF7\u8BBE\u4E3A 0"},null,8,["modelValue"]),s(" \u5143 ")]),_:1}),u(c,{label:"\u9886\u53D6\u65B9\u5F0F",prop:"takeType"},{default:d(()=>[u(V,{modelValue:l(e).takeType,"onUpdate:modelValue":t[9]||(t[9]=a=>l(e).takeType=a)},{default:d(()=>[(i(),r(g,{key:1,label:1},{default:d(()=>[s("\u76F4\u63A5\u9886\u53D6")]),_:1})),(i(),r(g,{key:2,label:2},{default:d(()=>[s("\u6307\u5B9A\u53D1\u653E")]),_:1}))]),_:1},8,["modelValue"])]),_:1}),l(e).takeType===1?(i(),r(c,{key:5,label:"\u53D1\u653E\u6570\u91CF",prop:"totalCount"},{default:d(()=>[u(v,{modelValue:l(e).totalCount,"onUpdate:modelValue":t[10]||(t[10]=a=>l(e).totalCount=a),min:-1,precision:0,class:"mr-2 !w-329px",placeholder:"\u53D1\u653E\u6570\u91CF\uFF0C\u6CA1\u6709\u4E4B\u540E\u4E0D\u80FD\u9886\u53D6\u6216\u53D1\u653E\uFF0C-1 \u4E3A\u4E0D\u9650\u5236"},null,8,["modelValue"]),s(" \u5F20 ")]),_:1})):n("",!0),l(e).takeType===1?(i(),r(c,{key:6,label:"\u6BCF\u4EBA\u9650\u9886\u4E2A\u6570",prop:"takeLimitCount"},{default:d(()=>[u(v,{modelValue:l(e).takeLimitCount,"onUpdate:modelValue":t[11]||(t[11]=a=>l(e).takeLimitCount=a),min:-1,precision:0,class:"mr-2 !w-329px",placeholder:"\u8BBE\u7F6E\u4E3A -1 \u65F6\uFF0C\u53EF\u65E0\u9650\u9886\u53D6"},null,8,["modelValue"]),s(" \u5F20 ")]),_:1})):n("",!0),u(c,{label:"\u6709\u6548\u671F\u7C7B\u578B",prop:"validityType"},{default:d(()=>[u(V,{modelValue:l(e).validityType,"onUpdate:modelValue":t[12]||(t[12]=a=>l(e).validityType=a)},{default:d(()=>[(i(!0),C(k,null,E(l(I)(l(h).PROMOTION_COUPON_TEMPLATE_VALIDITY_TYPE),a=>(i(),r(g,{key:a.value,label:a.value},{default:d(()=>[s(x(a.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(e).validityType===l(b).DATE.type?(i(),r(c,{key:7,label:"\u56FA\u5B9A\u65E5\u671F",prop:"validTimes"},{default:d(()=>[u(H,{modelValue:l(e).validTimes,"onUpdate:modelValue":t[13]||(t[13]=a=>l(e).validTimes=a),"default-time":[new Date(2e3,1,1,0,0,0),new Date(2e3,2,1,23,59,59)],style:{width:"240px"},type:"datetimerange","value-format":"x"},null,8,["modelValue","default-time"])]),_:1})):n("",!0),l(e).validityType===l(b).TERM.type?(i(),r(c,{key:8,label:"\u9886\u53D6\u65E5\u671F",prop:"fixedStartTerm"},{default:d(()=>[s(" \u7B2C "),u(v,{modelValue:l(e).fixedStartTerm,"onUpdate:modelValue":t[14]||(t[14]=a=>l(e).fixedStartTerm=a),min:0,precision:0,class:"mx-2",placeholder:"0 \u4E3A\u4ECA\u5929\u751F\u6548"},null,8,["modelValue"]),s(" \u81F3 "),u(v,{modelValue:l(e).fixedEndTerm,"onUpdate:modelValue":t[15]||(t[15]=a=>l(e).fixedEndTerm=a),min:0,precision:0,class:"mx-2",placeholder:"\u8BF7\u8F93\u5165\u7ED3\u675F\u5929\u6570"},null,8,["modelValue"]),s(" \u5929\u6709\u6548 ")]),_:1})):n("",!0)]),_:1},8,["model","rules"])),[[B,l(T)]])]),_:1},8,["modelValue","title"])}}})});export{A as _,Ve as __tla};
