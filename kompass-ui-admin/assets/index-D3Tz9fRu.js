import{d as B,I as X,n as Y,r as _,f as $,C as aa,T as ea,o as u,c as O,i as a,w as l,a as t,U as ta,F as M,k as la,V as ra,G as z,l as d,j as m,H as f,dV as A,Z as oa,L as sa,J as na,K as ua,_ as pa,N as ca,O as ia,P as _a,ce as da,Q as ma,R as fa,__tla as ha}from"./index-BUSn51wb.js";import{_ as ya,__tla as ga}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as wa,__tla as va}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as ba,__tla as ka}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as Sa,__tla as xa}from"./index-COobLwz-.js";import{d as Ca,__tla as Va}from"./formatTime-DWdBpgsM.js";import{d as Ua}from"./download-e0EdwhTv.js";import{W as v,__tla as Na}from"./index-B5GxX3eg.js";import{_ as Pa,__tla as Ta}from"./WarehouseForm.vue_vue_type_script_setup_true_lang-Bw6t9w_Z.js";import{__tla as Wa}from"./index-Cch5e1V0.js";import"./color-BN7ZL7BD.js";import{__tla as Oa}from"./el-card-CJbXGyyg.js";import{__tla as Ma}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import"./constants-A8BI3pz7.js";let F,za=Promise.all([(()=>{try{return ha}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return ka}catch{}})(),(()=>{try{return xa}catch{}})(),(()=>{try{return Va}catch{}})(),(()=>{try{return Na}catch{}})(),(()=>{try{return Ta}catch{}})(),(()=>{try{return Wa}catch{}})(),(()=>{try{return Oa}catch{}})(),(()=>{try{return Ma}catch{}})()]).then(async()=>{F=B({name:"ErpWarehouse",__name:"index",setup(Aa){const h=X(),{t:R}=Y(),b=_(!0),C=_([]),V=_(0),r=$({pageNo:1,pageSize:10,name:void 0,status:void 0}),U=_(),k=_(!1),c=async()=>{b.value=!0;try{const p=await v.getWarehousePage(r);C.value=p.list,V.value=p.total}finally{b.value=!1}},S=()=>{r.pageNo=1,c()},D=()=>{U.value.resetFields(),S()},N=_(),P=(p,o)=>{N.value.open(p,o)},K=async()=>{try{await h.exportConfirm(),k.value=!0;const p=await v.exportWarehouse(r);Ua.excel(p,"\u4ED3\u5E93.xls")}catch{}finally{k.value=!1}};return aa(()=>{c()}),(p,o)=>{const j=Sa,q=oa,x=sa,E=na,G=ua,y=pa,i=ca,H=ia,T=ba,s=_a,I=wa,J=da,L=ma,Q=ya,g=ea("hasPermi"),Z=fa;return u(),O(M,null,[a(j,{title:"\u3010\u5E93\u5B58\u3011\u4EA7\u54C1\u5E93\u5B58\u3001\u5E93\u5B58\u660E\u7EC6",url:"https://doc.iocoder.cn/erp/stock/"}),a(T,null,{default:l(()=>[a(H,{class:"-mb-15px",model:t(r),ref_key:"queryFormRef",ref:U,inline:!0,"label-width":"68px"},{default:l(()=>[a(x,{label:"\u4ED3\u5E93\u540D\u79F0",prop:"name"},{default:l(()=>[a(q,{modelValue:t(r).name,"onUpdate:modelValue":o[0]||(o[0]=e=>t(r).name=e),placeholder:"\u8BF7\u8F93\u5165\u4ED3\u5E93\u540D\u79F0",clearable:"",onKeyup:ta(S,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(x,{label:"\u4ED3\u5E93\u72B6\u6001",prop:"status"},{default:l(()=>[a(G,{modelValue:t(r).status,"onUpdate:modelValue":o[1]||(o[1]=e=>t(r).status=e),placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93\u72B6\u6001",clearable:"",class:"!w-240px"},{default:l(()=>[(u(!0),O(M,null,la(t(ra)(t(z).COMMON_STATUS),e=>(u(),d(E,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(x,null,{default:l(()=>[a(i,{onClick:S},{default:l(()=>[a(y,{icon:"ep:search",class:"mr-5px"}),m(" \u641C\u7D22")]),_:1}),a(i,{onClick:D},{default:l(()=>[a(y,{icon:"ep:refresh",class:"mr-5px"}),m(" \u91CD\u7F6E")]),_:1}),f((u(),d(i,{type:"primary",plain:"",onClick:o[2]||(o[2]=e=>P("create"))},{default:l(()=>[a(y,{icon:"ep:plus",class:"mr-5px"}),m(" \u65B0\u589E ")]),_:1})),[[g,["erp:warehouse:create"]]]),f((u(),d(i,{type:"success",plain:"",onClick:K,loading:t(k)},{default:l(()=>[a(y,{icon:"ep:download",class:"mr-5px"}),m(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[g,["erp:warehouse:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(T,null,{default:l(()=>[f((u(),d(L,{data:t(C),stripe:!0,"show-overflow-tooltip":!0},{default:l(()=>[a(s,{label:"\u4ED3\u5E93\u540D\u79F0",align:"center",prop:"name"}),a(s,{label:"\u4ED3\u5E93\u5730\u5740",align:"center",prop:"address"}),a(s,{label:"\u4ED3\u50A8\u8D39",align:"center",prop:"warehousePrice",formatter:t(A)},null,8,["formatter"]),a(s,{label:"\u642C\u8FD0\u8D39",align:"center",prop:"truckagePrice",formatter:t(A)},null,8,["formatter"]),a(s,{label:"\u8D1F\u8D23\u4EBA",align:"center",prop:"principal"}),a(s,{label:"\u5907\u6CE8",align:"center",prop:"remark"}),a(s,{label:"\u6392\u5E8F",align:"center",prop:"sort"}),a(s,{label:"\u72B6\u6001",align:"center",prop:"status"},{default:l(e=>[a(I,{type:t(z).COMMON_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),a(s,{label:"\u662F\u5426\u9ED8\u8BA4",align:"center",prop:"defaultStatus"},{default:l(e=>[a(J,{modelValue:e.row.defaultStatus,"onUpdate:modelValue":w=>e.row.defaultStatus=w,"active-value":!0,"inactive-value":!1,onChange:w=>(async n=>{try{const W=n.defaultStatus?"\u8BBE\u7F6E":"\u53D6\u6D88";await h.confirm("\u786E\u8BA4\u8981"+W+'"'+n.name+'"\u9ED8\u8BA4\u5417?'),await v.updateWarehouseDefaultStatus(n.id,n.defaultStatus),await c()}catch{n.defaultStatus=!n.defaultStatus}})(e.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),a(s,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:t(Ca),width:"180px"},null,8,["formatter"]),a(s,{label:"\u64CD\u4F5C",align:"center"},{default:l(e=>[f((u(),d(i,{link:"",type:"primary",onClick:w=>P("update",e.row.id)},{default:l(()=>[m(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[g,["erp:warehouse:update"]]]),f((u(),d(i,{link:"",type:"danger",onClick:w=>(async n=>{try{await h.delConfirm(),await v.deleteWarehouse(n),h.success(R("common.delSuccess")),await c()}catch{}})(e.row.id)},{default:l(()=>[m(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[g,["erp:warehouse:delete"]]])]),_:1})]),_:1},8,["data"])),[[Z,t(b)]]),a(Q,{total:t(V),page:t(r).pageNo,"onUpdate:page":o[3]||(o[3]=e=>t(r).pageNo=e),limit:t(r).pageSize,"onUpdate:limit":o[4]||(o[4]=e=>t(r).pageSize=e),onPagination:c},null,8,["total","page","limit"])]),_:1}),a(Pa,{ref_key:"formRef",ref:N,onSuccess:c},null,512)],64)}}})});export{za as __tla,F as default};
