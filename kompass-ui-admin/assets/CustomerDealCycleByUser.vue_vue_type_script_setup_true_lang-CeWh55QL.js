import{d as b,r as p,f as D,C as v,o as x,c as A,i as e,w as l,a as i,H as P,l as I,F as B,P as q,Q as U,R as S,__tla as E}from"./index-BUSn51wb.js";import{E as L,__tla as F}from"./el-card-CJbXGyyg.js";import{E as H,__tla as N}from"./el-skeleton-item-tDN8U6BH.js";import{_ as Q,__tla as R}from"./Echart.vue_vue_type_script_setup_true_lang-DnLZLH3w.js";import{S as y,__tla as X}from"./customer-DXRFD9ec.js";let h,Z=Promise.all([(()=>{try{return E}catch{}})(),(()=>{try{return F}catch{}})(),(()=>{try{return N}catch{}})(),(()=>{try{return R}catch{}})(),(()=>{try{return X}catch{}})()]).then(async()=>{h=b({name:"CustomerDealCycleByUser",__name:"CustomerDealCycleByUser",props:{queryParams:{}},setup(g,{expose:C}){const n=g,s=p(!1),u=p([]),a=D({grid:{left:20,right:40,bottom:20,containLabel:!0},legend:{},series:[{name:"\u6210\u4EA4\u5468\u671F(\u5929)",type:"bar",data:[],yAxisIndex:0},{name:"\u6210\u4EA4\u5BA2\u6237\u6570",type:"bar",data:[],yAxisIndex:1}],toolbox:{feature:{dataZoom:{xAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u6210\u4EA4\u5468\u671F\u5206\u6790"}}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},yAxis:[{type:"value",name:"\u6210\u4EA4\u5468\u671F(\u5929)",min:0,minInterval:1},{type:"value",name:"\u6210\u4EA4\u5BA2\u6237\u6570",min:0,minInterval:1,splitLine:{lineStyle:{type:"dotted",opacity:.7}}}],xAxis:{type:"category",name:"\u65E5\u671F",data:[]}}),c=async()=>{s.value=!0;try{await(async()=>{const o=await y.getCustomerDealCycleByDate(n.queryParams),d=await y.getCustomerSummaryByDate(n.queryParams),m=await y.getCustomerDealCycleByUser(n.queryParams);a.xAxis&&a.xAxis.data&&(a.xAxis.data=o.map(t=>t.time)),a.series&&a.series[0]&&a.series[0].data&&(a.series[0].data=o.map(t=>t.customerDealCycle)),a.series&&a.series[1]&&a.series[1].data&&(a.series[1].data=d.map(t=>t.customerDealCount)),u.value=m})()}finally{s.value=!1}};return C({loadData:c}),v(()=>{c()}),(o,d)=>{const m=Q,t=H,_=L,r=q,f=U,w=S;return x(),A(B,null,[e(_,{shadow:"never"},{default:l(()=>[e(t,{loading:i(s),animated:""},{default:l(()=>[e(m,{height:500,options:i(a)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),e(_,{shadow:"never",class:"mt-16px"},{default:l(()=>[P((x(),I(f,{data:i(u)},{default:l(()=>[e(r,{label:"\u5E8F\u53F7",align:"center",type:"index",width:"80"}),e(r,{label:"\u65E5\u671F",align:"center",prop:"ownerUserName","min-width":"200"}),e(r,{label:"\u6210\u4EA4\u5468\u671F(\u5929)",align:"center",prop:"customerDealCycle","min-width":"200"}),e(r,{label:"\u6210\u4EA4\u5BA2\u6237\u6570",align:"center",prop:"customerDealCount","min-width":"200"})]),_:1},8,["data"])),[[w,i(s)]])]),_:1})],64)}}})});export{h as _,Z as __tla};
