import{d as T,n as j,I as H,r as o,f as J,o as m,l as _,w as t,i as u,a,j as v,H as K,c as U,F as k,k as w,V as Z,G as z,t as D,y as P,Z as $,L as Q,J as W,K as X,am as Y,an as ee,O as ae,N as le,R as se,__tla as te}from"./index-BUSn51wb.js";import{_ as ue,__tla as re}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{C as I}from"./constants-A8BI3pz7.js";import{a as oe,c as de,u as ie,__tla as me}from"./index-xiOMzVtR.js";import{g as ne,__tla as ce}from"./index-BYXzDB8j.js";let E,_e=Promise.all([(()=>{try{return te}catch{}})(),(()=>{try{return re}catch{}})(),(()=>{try{return me}catch{}})(),(()=>{try{return ce}catch{}})()]).then(async()=>{E=T({name:"UserGroupForm",__name:"UserGroupForm",emits:["success"],setup(pe,{expose:F,emit:N}){const{t:p}=j(),f=H(),d=o(!1),y=o(""),i=o(!1),b=o(""),s=o({id:void 0,name:void 0,description:void 0,userIds:void 0,status:I.ENABLE}),S=J({name:[{required:!0,message:"\u7EC4\u540D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],description:[{required:!0,message:"\u63CF\u8FF0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],userIds:[{required:!0,message:"\u6210\u5458\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),n=o(),V=o([]);F({open:async(r,l)=>{if(d.value=!0,y.value=p("action."+r),b.value=r,A(),l){i.value=!0;try{s.value=await oe(l)}finally{i.value=!1}}V.value=await ne()}});const q=N,x=async()=>{if(n&&await n.value.validate()){i.value=!0;try{const r=s.value;b.value==="create"?(await de(r),f.success(p("common.createSuccess"))):(await ie(r),f.success(p("common.updateSuccess"))),d.value=!1,q("success")}finally{i.value=!1}}},A=()=>{var r;s.value={id:void 0,name:void 0,description:void 0,userIds:void 0,status:I.ENABLE},(r=n.value)==null||r.resetFields()};return(r,l)=>{const g=$,c=Q,C=W,G=X,L=Y,O=ee,B=ae,h=le,M=ue,R=se;return m(),_(M,{modelValue:a(d),"onUpdate:modelValue":l[5]||(l[5]=e=>P(d)?d.value=e:null),title:a(y)},{footer:t(()=>[u(h,{disabled:a(i),type:"primary",onClick:x},{default:t(()=>[v("\u786E \u5B9A")]),_:1},8,["disabled"]),u(h,{onClick:l[4]||(l[4]=e=>d.value=!1)},{default:t(()=>[v("\u53D6 \u6D88")]),_:1})]),default:t(()=>[K((m(),_(B,{ref_key:"formRef",ref:n,model:a(s),rules:a(S),"label-width":"100px"},{default:t(()=>[u(c,{label:"\u7EC4\u540D",prop:"name"},{default:t(()=>[u(g,{modelValue:a(s).name,"onUpdate:modelValue":l[0]||(l[0]=e=>a(s).name=e),placeholder:"\u8BF7\u8F93\u5165\u7EC4\u540D"},null,8,["modelValue"])]),_:1}),u(c,{label:"\u63CF\u8FF0"},{default:t(()=>[u(g,{modelValue:a(s).description,"onUpdate:modelValue":l[1]||(l[1]=e=>a(s).description=e),placeholder:"\u8BF7\u8F93\u5165\u63CF\u8FF0",type:"textarea"},null,8,["modelValue"])]),_:1}),u(c,{label:"\u6210\u5458",prop:"userIds"},{default:t(()=>[u(G,{modelValue:a(s).userIds,"onUpdate:modelValue":l[2]||(l[2]=e=>a(s).userIds=e),multiple:"",placeholder:"\u8BF7\u9009\u62E9\u6210\u5458"},{default:t(()=>[(m(!0),U(k,null,w(a(V),e=>(m(),_(C,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(c,{label:"\u72B6\u6001",prop:"status"},{default:t(()=>[u(O,{modelValue:a(s).status,"onUpdate:modelValue":l[3]||(l[3]=e=>a(s).status=e)},{default:t(()=>[(m(!0),U(k,null,w(a(Z)(a(z).COMMON_STATUS),e=>(m(),_(L,{key:e.value,label:e.value},{default:t(()=>[v(D(e.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[R,a(i)]])]),_:1},8,["modelValue","title"])}}})});export{E as _,_e as __tla};
