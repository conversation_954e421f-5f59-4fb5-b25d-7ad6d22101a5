import{d as x,o,c as f,g as e,i as t,w as d,_,__tla as m}from"./index-BUSn51wb.js";import{E as u,__tla as y}from"./el-avatar-Da2TGjmj.js";let i,b=Promise.all([(()=>{try{return m}catch{}})(),(()=>{try{return y}catch{}})()]).then(async()=>{let s,a,l,p,r;s={class:"flex flex-col"},a={class:"flex items-center justify-between p-x-18px p-y-24px"},l={class:"flex flex-1 items-center gap-16px"},p=e("span",{class:"text-18px font-bold"},"\u828B\u9053\u6E90\u7801",-1),r=e("div",{class:"flex items-center justify-between justify-between bg-white p-x-20px p-y-8px text-12px"},[e("span",{class:"color-#ff690d"},"\u70B9\u51FB\u7ED1\u5B9A\u624B\u673A\u53F7"),e("span",{class:"rounded-26px bg-#ff6100 p-x-8px p-y-5px color-white"},"\u53BB\u7ED1\u5B9A")],-1),i=x({name:"UserCard",__name:"index",props:{property:{}},setup:w=>(g,h)=>{const c=_,n=u;return o(),f("div",s,[e("div",a,[e("div",l,[t(n,{size:60},{default:d(()=>[t(c,{icon:"ep:avatar",size:60})]),_:1}),p]),t(c,{icon:"tdesign:qrcode",size:20})]),r])}})});export{b as __tla,i as default};
