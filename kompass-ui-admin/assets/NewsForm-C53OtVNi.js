import{I as D,e9 as oe,dm as se,d as ie,b6 as re,b as G,r as ce,o as d,l as y,w as o,i as e,g as p,c as C,F as ue,k as de,a as l,a0 as H,t as J,a9 as g,y as me,ck as pe,_ as ge,N as he,s as ve,b3 as _e,Z as fe,E as ye,e3 as Ce,b7 as ke,a5 as we,a6 as Ve,B as be,__tla as Ue}from"./index-BUSn51wb.js";import Fe,{__tla as Ie}from"./CoverSelect-BLP4TeZn.js";let F,K,L,Ne=Promise.all([(()=>{try{return Ue}catch{}})(),(()=>{try{return Ie}catch{}})()]).then(async()=>{let V,I,N,S,x,z,E,O,M,P,A,B,j,T,R,W,X;V=D(),I=(h,b)=>({MENU_CONF:{uploadImage:{server:h,maxFileSize:5242880,maxNumberOfFiles:10,allowedFileTypes:["image/*"],meta:{accountId:b,type:"image"},metaWithUrl:!0,headers:{Accept:"*",Authorization:"Bearer "+oe(),"tenant-id":se()},withCredentials:!0,timeout:5e3,fieldName:"file",onBeforeUpload:n=>(console.log(n),n),onProgress(n){console.log("progress",n)},onSuccess(n,u){console.log("onSuccess",n,u)},onFailed(n,u){V.alertError(u.message),console.log("onFailed",n,u)},onError(n,u,U){V.alertError(u.message),console.error("onError",n,u,U)},customInsert(n,u){u(n.data.url,"image",n.data.url)}}}}),L=()=>({title:"",thumbMediaId:"",author:"",digest:"",showCoverPic:"",content:"",contentSourceUrl:"",needOpenComment:"",onlyFansCanComment:"",thumbUrl:""}),N={class:"select-item"},S=["onClick"],x={class:"news-content"},z=["src"],E={class:"news-content-title"},O={key:0,class:"child"},M=["onClick"],P={class:"news-content-item"},A={class:"news-content-item-title"},B={class:"news-content-item-img"},j=["src"],T={class:"child"},R={key:0},W=(h=>(we("data-v-c4606cac"),h=h(),Ve(),h))(()=>p("p",null,"\u6458\u8981:",-1)),X=ie({name:"NewsForm",__name:"NewsForm",props:{isCreating:{type:Boolean},modelValue:{}},emits:["update:modelValue"],setup(h,{emit:b}){const n=D(),u=h,U=re("accountId"),Q=I("http://118.195.130.96:48080/admin-api/mp/material/upload-permanent",U),$=b,a=G({get:()=>u.modelValue===null?[{title:"",thumbMediaId:"",author:"",digest:"",showCoverPic:"",content:"",contentSourceUrl:"",needOpenComment:"",onlyFansCanComment:"",thumbUrl:""}]:u.modelValue,set(s){$("update:modelValue",s)}}),m=ce(0),c=G(()=>a.value[m.value]),Y=s=>{const i=a.value[s];a.value[s]=a.value[s+1],a.value[s+1]=i,m.value=s+1},Z=async s=>{try{await n.confirm("\u786E\u5B9A\u5220\u9664\u8BE5\u56FE\u6587\u5417?"),a.value.splice(s,1),m.value===s&&(m.value=0)}catch{}},ee=()=>{a.value.push({title:"",thumbMediaId:"",author:"",digest:"",showCoverPic:"",content:"",contentSourceUrl:"",needOpenComment:"",onlyFansCanComment:"",thumbUrl:""}),m.value=a.value.length-1};return(s,i)=>{const _=ge,f=he,k=ve,le=_e,w=fe,q=ye,ae=Ce,te=ke;return d(),y(te,null,{default:o(()=>[e(le,{width:"40%"},{default:o(()=>[p("div",N,[(d(!0),C(ue,null,de(l(a),(t,r)=>(d(),C("div",{key:r},[r===0?(d(),C("div",{key:0,class:H(["news-main father",{activeAddNews:l(m)===r}]),onClick:v=>m.value=r},[p("div",x,[p("img",{class:"material-img",src:t.thumbUrl},null,8,z),p("div",E,J(t.title),1)]),l(a).length>1?(d(),C("div",O,[e(f,{type:"info",circle:"",size:"small",onClick:()=>Y(r)},{default:o(()=>[e(_,{icon:"ep:arrow-down-bold"})]),_:2},1032,["onClick"]),s.isCreating?(d(),y(f,{key:0,type:"danger",circle:"",size:"small",onClick:()=>Z(r)},{default:o(()=>[e(_,{icon:"ep:delete"})]),_:2},1032,["onClick"])):g("",!0)])):g("",!0)],10,S)):g("",!0),r>0?(d(),C("div",{key:1,class:H(["news-main-item father",{activeAddNews:l(m)===r}]),onClick:v=>m.value=r},[p("div",P,[p("div",A,J(t.title),1),p("div",B,[p("img",{class:"material-img",src:t.thumbUrl,width:"100%"},null,8,j)])]),p("div",T,[l(a).length>r+1?(d(),y(f,{key:0,circle:"",type:"info",size:"small",onClick:()=>Y(r)},{default:o(()=>[e(_,{icon:"ep:arrow-down-bold"})]),_:2},1032,["onClick"])):g("",!0),r>0?(d(),y(f,{key:1,type:"info",circle:"",size:"small",onClick:()=>(v=>{const ne=a.value[v];a.value[v]=a.value[v-1],a.value[v-1]=ne,m.value=v-1})(r)},{default:o(()=>[e(_,{icon:"ep:arrow-up-bold"})]),_:2},1032,["onClick"])):g("",!0),s.isCreating?(d(),y(f,{key:2,type:"danger",size:"small",circle:"",onClick:()=>Z(r)},{default:o(()=>[e(_,{icon:"ep:delete"})]),_:2},1032,["onClick"])):g("",!0)])],10,M)):g("",!0)]))),128)),e(k,{justify:"center",class:"ope-row"},{default:o(()=>[l(a).length<8&&s.isCreating?(d(),y(f,{key:0,type:"primary",circle:"",onClick:ee},{default:o(()=>[e(_,{icon:"ep:plus"})]),_:1})):g("",!0)]),_:1})])]),_:1}),e(ae,null,{default:o(()=>[l(a).length>0?(d(),C("div",R,[e(k,{gutter:20},{default:o(()=>[e(w,{modelValue:l(c).title,"onUpdate:modelValue":i[0]||(i[0]=t=>l(c).title=t),placeholder:"\u8BF7\u8F93\u5165\u6807\u9898\uFF08\u5FC5\u586B\uFF09"},null,8,["modelValue"]),e(w,{modelValue:l(c).author,"onUpdate:modelValue":i[1]||(i[1]=t=>l(c).author=t),placeholder:"\u8BF7\u8F93\u5165\u4F5C\u8005",style:{"margin-top":"5px"}},null,8,["modelValue"]),e(w,{modelValue:l(c).contentSourceUrl,"onUpdate:modelValue":i[2]||(i[2]=t=>l(c).contentSourceUrl=t),placeholder:"\u8BF7\u8F93\u5165\u539F\u6587\u5730\u5740",style:{"margin-top":"5px"}},null,8,["modelValue"])]),_:1}),e(k,{gutter:20},{default:o(()=>[e(q,{span:12},{default:o(()=>[e(Fe,{modelValue:l(c),"onUpdate:modelValue":i[3]||(i[3]=t=>me(c)?c.value=t:null),"is-first":l(m)===0},null,8,["modelValue","is-first"])]),_:1}),e(q,{span:12},{default:o(()=>[W,e(w,{rows:8,type:"textarea",modelValue:l(c).digest,"onUpdate:modelValue":i[4]||(i[4]=t=>l(c).digest=t),placeholder:"\u8BF7\u8F93\u5165\u6458\u8981",class:"digest",maxlength:"120"},null,8,["modelValue"])]),_:1})]),_:1}),e(k,null,{default:o(()=>[e(l(pe),{modelValue:l(c).content,"onUpdate:modelValue":i[5]||(i[5]=t=>l(c).content=t),"editor-config":l(Q)},null,8,["modelValue","editor-config"])]),_:1})])):g("",!0)]),_:1})]),_:1})}}}),F=be(X,[["__scopeId","data-v-c4606cac"]]),K=Object.freeze(Object.defineProperty({__proto__:null,default:F},Symbol.toStringTag,{value:"Module"}))});export{F as N,Ne as __tla,K as a,L as c};
