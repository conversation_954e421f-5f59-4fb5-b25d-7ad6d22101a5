import{d as E,o as m,c as n,H as h,g as s,i as a,w as t,t as r,aV as x,a as c,G as C,j as _,F as g,s as w,E as H,R,__tla as j}from"./index-BUSn51wb.js";import{_ as D,__tla as L}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{E as M,a as T,__tla as S}from"./el-descriptions-item-dD3qa0ub.js";import{_ as U,__tla as V}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{f as B,__tla as F}from"./formatTime-DWdBpgsM.js";let d,G=Promise.all([(()=>{try{return j}catch{}})(),(()=>{try{return L}catch{}})(),(()=>{try{return S}catch{}})(),(()=>{try{return V}catch{}})(),(()=>{try{return F}catch{}})()]).then(async()=>{let u,o;u={class:"flex items-start justify-between"},o={class:"text-xl font-bold"},d=E({name:"CrmCustomerDetailsHeader",__name:"CustomerDetailsHeader",props:{customer:{},loading:{type:Boolean}},setup:N=>(l,O)=>{const i=w,f=H,p=U,e=M,y=T,v=D,b=R;return m(),n(g,null,[h((m(),n("div",null,[s("div",u,[s("div",null,[a(f,null,{default:t(()=>[a(i,null,{default:t(()=>[s("span",o,r(l.customer.name),1)]),_:1})]),_:1})]),s("div",null,[x(l.$slots,"default")])])])),[[b,l.loading]]),a(v,{class:"mt-10px"},{default:t(()=>[a(y,{column:5,direction:"vertical"},{default:t(()=>[a(e,{label:"\u5BA2\u6237\u7EA7\u522B"},{default:t(()=>[a(p,{type:c(C).CRM_CUSTOMER_LEVEL,value:l.customer.level},null,8,["type","value"])]),_:1}),a(e,{label:"\u6210\u4EA4\u72B6\u6001"},{default:t(()=>[_(r(l.customer.dealStatus?"\u5DF2\u6210\u4EA4":"\u672A\u6210\u4EA4"),1)]),_:1}),a(e,{label:"\u8D1F\u8D23\u4EBA"},{default:t(()=>[_(r(l.customer.ownerUserName),1)]),_:1}),a(e,{label:"\u521B\u5EFA\u65F6\u95F4"},{default:t(()=>[_(r(c(B)(l.customer.createTime)),1)]),_:1})]),_:1})]),_:1})],64)}})});export{d as _,G as __tla};
