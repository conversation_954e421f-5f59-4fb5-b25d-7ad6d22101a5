import{_ as p,__tla as f}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{d as v,r as e,o as y,l as h,w as l,i as n,j as w,a as r,y as V,N as x,__tla as j}from"./index-BUSn51wb.js";import{_ as k,__tla as C}from"./WalletTransactionList.vue_vue_type_script_setup_true_lang-B0WACgPN.js";let c,F=Promise.all([(()=>{try{return f}catch{}})(),(()=>{try{return j}catch{}})(),(()=>{try{return C}catch{}})()]).then(async()=>{c=v({__name:"WalletForm",setup(N,{expose:m}){const a=e(!1),s=e("");e(!1);const _=e(0);return m({open:async o=>{a.value=!0,s.value="\u94B1\u5305\u4F59\u989D\u660E\u7EC6",_.value=o}}),(o,t)=>{const i=x,d=p;return y(),h(d,{title:r(s),modelValue:r(a),"onUpdate:modelValue":t[1]||(t[1]=u=>V(a)?a.value=u:null),width:"800"},{footer:l(()=>[n(i,{onClick:t[0]||(t[0]=u=>a.value=!1)},{default:l(()=>[w("\u53D6 \u6D88")]),_:1})]),default:l(()=>[n(k,{"wallet-id":r(_)},null,8,["wallet-id"])]),_:1},8,["title","modelValue"])}}})});export{c as _,F as __tla};
