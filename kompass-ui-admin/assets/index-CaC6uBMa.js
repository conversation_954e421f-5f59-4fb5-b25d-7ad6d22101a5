import{d as da,I as pa,n as ma,u as _a,r as y,bc as fa,C as ya,T as wa,o as u,c as D,i as a,w as e,a as l,U as ha,j as c,H as v,l as p,F as L,k as ba,g as w,t as k,aF as N,aJ as va,Z as ka,L as ga,cd as Ca,M as xa,_ as Va,N as Sa,O as Ua,z as Ea,A as Ta,E as Ia,s as Pa,P as Da,aN as La,ce as Na,ax as Aa,Q as Ya,R as Ba,B as Fa,__tla as za}from"./index-BUSn51wb.js";import{_ as Ra,__tla as $a}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{E as Ha,__tla as Ka}from"./el-image-BjHZRFih.js";import{_ as Ma,__tla as ja}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as Za,__tla as qa}from"./index-COobLwz-.js";import{d as Ja,__tla as Oa}from"./formatTime-DWdBpgsM.js";import{h as Qa,d as Wa,t as Xa}from"./tree-BMa075Oj.js";import{d as C}from"./constants-A8BI3pz7.js";import{d as Ga}from"./download-e0EdwhTv.js";import{d as ae,a as ee,e as q,f as te,h as le,__tla as ne}from"./spu-CW3JGweV.js";import{g as se,__tla as re}from"./category-WzWM3ODe.js";import{__tla as oe}from"./index-Cch5e1V0.js";import{__tla as ue}from"./el-card-CJbXGyyg.js";let J,ie=Promise.all([(()=>{try{return za}catch{}})(),(()=>{try{return $a}catch{}})(),(()=>{try{return Ka}catch{}})(),(()=>{try{return ja}catch{}})(),(()=>{try{return qa}catch{}})(),(()=>{try{return Oa}catch{}})(),(()=>{try{return ne}catch{}})(),(()=>{try{return re}catch{}})(),(()=>{try{return oe}catch{}})(),(()=>{try{return ue}catch{}})()]).then(async()=>{let A,Y;A={class:"flex"},Y={class:"ml-4 overflow-hidden"},J=Fa(da({name:"ProductSpu",__name:"index",setup(ce){const h=pa(),{t:O}=ma(),{push:B}=_a(),E=y(!1),T=y(!1),F=y(0),z=y([]),R=y([{name:"\u51FA\u552E\u4E2D",type:0,count:0},{name:"\u4ED3\u5E93\u4E2D",type:1,count:0},{name:"\u5DF2\u552E\u7F44",type:2,count:0},{name:"\u8B66\u6212\u5E93\u5B58",type:3,count:0},{name:"\u56DE\u6536\u7AD9",type:4,count:0}]),r=y({pageNo:1,pageSize:10,tabType:0,name:"",categoryId:void 0,createTime:void 0}),$=y(),f=async()=>{E.value=!0;try{const s=await ae(r.value);z.value=s.list,F.value=s.total}finally{E.value=!1}},Q=s=>{r.value.tabType=s.paneName,f()},x=async()=>{const s=await ee();for(let n in s)R.value[Number(n)].count=s[n]},H=async(s,n)=>{try{const V=n===C.RECYCLE.status?"\u52A0\u5165\u5230\u56DE\u6536\u7AD9":"\u6062\u590D\u5230\u4ED3\u5E93";await h.confirm(`\u786E\u8BA4\u8981"${s.name}"${V}\u5417\uFF1F`),await q({id:s.id,status:n}),h.success(V+"\u6210\u529F"),await x(),await f()}catch{}},I=()=>{f()},W=()=>{$.value.resetFields(),I()},K=s=>{B(typeof s!="number"?{name:"ProductSpuAdd"}:{name:"ProductSpuEdit",params:{id:s}})},X=async()=>{try{await h.exportConfirm(),T.value=!0;const s=await le(r);Ga.excel(s,"\u5546\u54C1\u5217\u8868.xls")}catch{}finally{T.value=!1}},P=y();return fa(()=>{f()}),ya(async()=>{await x(),await f();const s=await se({});P.value=Qa(s,"id","parentId")}),(s,n)=>{const V=Za,G=ka,m=ga,aa=Ca,ea=xa,S=Va,_=Sa,M=Ua,j=Ma,ta=Ea,la=Ta,b=Ia,U=Pa,d=Da,na=Ha,sa=La,ra=Na,oa=Aa,ua=Ya,ia=Ra,g=wa("hasPermi"),ca=Ba;return u(),D(L,null,[a(V,{title:"\u3010\u5546\u54C1\u3011\u5546\u54C1 SPU \u4E0E SKU",url:"https://doc.iocoder.cn/mall/product-spu-sku/"}),a(j,null,{default:e(()=>[a(M,{ref_key:"queryFormRef",ref:$,inline:!0,model:l(r),class:"-mb-15px","label-width":"68px"},{default:e(()=>[a(m,{label:"\u5546\u54C1\u540D\u79F0",prop:"name"},{default:e(()=>[a(G,{modelValue:l(r).name,"onUpdate:modelValue":n[0]||(n[0]=t=>l(r).name=t),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1\u540D\u79F0",onKeyup:ha(I,["enter"])},null,8,["modelValue"])]),_:1}),a(m,{label:"\u5546\u54C1\u5206\u7C7B",prop:"categoryId"},{default:e(()=>[a(aa,{modelValue:l(r).categoryId,"onUpdate:modelValue":n[1]||(n[1]=t=>l(r).categoryId=t),options:l(P),props:l(Wa),class:"w-1/1",clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u5546\u54C1\u5206\u7C7B"},null,8,["modelValue","options","props"])]),_:1}),a(m,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:e(()=>[a(ea,{modelValue:l(r).createTime,"onUpdate:modelValue":n[2]||(n[2]=t=>l(r).createTime=t),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),a(m,null,{default:e(()=>[a(_,{onClick:I},{default:e(()=>[a(S,{class:"mr-5px",icon:"ep:search"}),c(" \u641C\u7D22 ")]),_:1}),a(_,{onClick:W},{default:e(()=>[a(S,{class:"mr-5px",icon:"ep:refresh"}),c(" \u91CD\u7F6E ")]),_:1}),v((u(),p(_,{plain:"",type:"primary",onClick:n[3]||(n[3]=t=>K(void 0))},{default:e(()=>[a(S,{class:"mr-5px",icon:"ep:plus"}),c(" \u65B0\u589E ")]),_:1})),[[g,["product:spu:create"]]]),v((u(),p(_,{loading:l(T),plain:"",type:"success",onClick:X},{default:e(()=>[a(S,{class:"mr-5px",icon:"ep:download"}),c(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[g,["product:spu:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(j,null,{default:e(()=>[a(la,{modelValue:l(r).tabType,"onUpdate:modelValue":n[4]||(n[4]=t=>l(r).tabType=t),onTabClick:Q},{default:e(()=>[(u(!0),D(L,null,ba(l(R),t=>(u(),p(ta,{key:t.type,label:t.name+"("+t.count+")",name:t.type},null,8,["label","name"]))),128))]),_:1},8,["modelValue"]),v((u(),p(ua,{data:l(z)},{default:e(()=>[a(d,{type:"expand"},{default:e(({row:t})=>[a(M,{class:"spu-table-expand","label-position":"left"},{default:e(()=>[a(U,null,{default:e(()=>[a(b,{span:24},{default:e(()=>[a(U,null,{default:e(()=>[a(b,{span:8},{default:e(()=>[a(m,{label:"\u5546\u54C1\u5206\u7C7B:"},{default:e(()=>{return[w("span",null,k((i=t.categoryId,Xa(P.value,i))),1)];var i}),_:2},1024)]),_:2},1024),a(b,{span:8},{default:e(()=>[a(m,{label:"\u5E02\u573A\u4EF7:"},{default:e(()=>[w("span",null,k(l(N)(t.marketPrice)),1)]),_:2},1024)]),_:2},1024),a(b,{span:8},{default:e(()=>[a(m,{label:"\u6210\u672C\u4EF7:"},{default:e(()=>[w("span",null,k(l(N)(t.costPrice)),1)]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:2},1024),a(U,null,{default:e(()=>[a(b,{span:24},{default:e(()=>[a(U,null,{default:e(()=>[a(b,{span:8},{default:e(()=>[a(m,{label:"\u6D4F\u89C8\u91CF:"},{default:e(()=>[w("span",null,k(t.browseCount),1)]),_:2},1024)]),_:2},1024),a(b,{span:8},{default:e(()=>[a(m,{label:"\u865A\u62DF\u9500\u91CF:"},{default:e(()=>[w("span",null,k(t.virtualSalesCount),1)]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:2},1024)]),_:1}),a(d,{label:"\u5546\u54C1\u7F16\u53F7","min-width":"140",prop:"id"}),a(d,{label:"\u5546\u54C1\u4FE1\u606F","min-width":"300"},{default:e(({row:t})=>[w("div",A,[a(na,{fit:"cover",src:t.picUrl,class:"flex-none w-50px h-50px",onClick:i=>{return o=t.picUrl,void va({urlList:[o]});var o}},null,8,["src","onClick"]),w("div",Y,[a(sa,{effect:"dark",content:t.name,placement:"top"},{default:e(()=>[w("div",null,k(t.name),1)]),_:2},1032,["content"])])])]),_:1}),a(d,{align:"center",label:"\u4EF7\u683C","min-width":"160",prop:"price"},{default:e(({row:t})=>[c(" \xA5 "+k(l(N)(t.price)),1)]),_:1}),a(d,{align:"center",label:"\u9500\u91CF","min-width":"90",prop:"salesCount"}),a(d,{align:"center",label:"\u5E93\u5B58","min-width":"90",prop:"stock"}),a(d,{align:"center",label:"\u6392\u5E8F","min-width":"70",prop:"sort"}),a(d,{align:"center",label:"\u9500\u552E\u72B6\u6001","min-width":"80"},{default:e(({row:t})=>[t.status>=0?(u(),p(ra,{key:0,modelValue:t.status,"onUpdate:modelValue":i=>t.status=i,"active-value":1,"inactive-value":0,"active-text":"\u4E0A\u67B6","inactive-text":"\u4E0B\u67B6","inline-prompt":"",onChange:i=>(async o=>{try{const Z=o.status?"\u4E0A\u67B6":"\u4E0B\u67B6";await h.confirm(`\u786E\u8BA4\u8981${Z}"${o.name}"\u5417\uFF1F`),await q({id:o.id,status:o.status}),h.success(Z+"\u6210\u529F"),await x(),await f()}catch{o.status=o.status===C.DISABLE.status?C.ENABLE.status:C.DISABLE.status}})(t)},null,8,["modelValue","onUpdate:modelValue","onChange"])):(u(),p(oa,{key:1,type:"info"},{default:e(()=>[c("\u56DE\u6536\u7AD9")]),_:1}))]),_:1}),a(d,{formatter:l(Ja),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180"},null,8,["formatter"]),a(d,{align:"center",fixed:"right",label:"\u64CD\u4F5C","min-width":"200"},{default:e(({row:t})=>[a(_,{link:"",type:"primary",onClick:i=>{return o=t.id,void B({name:"ProductSpuDetail",params:{id:o}});var o}},{default:e(()=>[c(" \u8BE6\u60C5 ")]),_:2},1032,["onClick"]),v((u(),p(_,{link:"",type:"primary",onClick:i=>K(t.id)},{default:e(()=>[c(" \u4FEE\u6539 ")]),_:2},1032,["onClick"])),[[g,["product:spu:update"]]]),l(r).tabType===4?(u(),D(L,{key:0},[v((u(),p(_,{link:"",type:"danger",onClick:i=>(async o=>{try{await h.delConfirm(),await te(o),h.success(O("common.delSuccess")),await x(),await f()}catch{}})(t.id)},{default:e(()=>[c(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[g,["product:spu:delete"]]]),v((u(),p(_,{link:"",type:"primary",onClick:i=>H(t,l(C).DISABLE.status)},{default:e(()=>[c(" \u6062\u590D ")]),_:2},1032,["onClick"])),[[g,["product:spu:update"]]])],64)):v((u(),p(_,{key:1,link:"",type:"danger",onClick:i=>H(t,l(C).RECYCLE.status)},{default:e(()=>[c(" \u56DE\u6536 ")]),_:2},1032,["onClick"])),[[g,["product:spu:update"]]])]),_:1})]),_:1},8,["data"])),[[ca,l(E)]]),a(ia,{limit:l(r).pageSize,"onUpdate:limit":n[5]||(n[5]=t=>l(r).pageSize=t),page:l(r).pageNo,"onUpdate:page":n[6]||(n[6]=t=>l(r).pageNo=t),total:l(F),onPagination:f},null,8,["limit","page","total"])]),_:1})],64)}}}),[["__scopeId","data-v-eb9784e4"]])});export{ie as __tla,J as default};
