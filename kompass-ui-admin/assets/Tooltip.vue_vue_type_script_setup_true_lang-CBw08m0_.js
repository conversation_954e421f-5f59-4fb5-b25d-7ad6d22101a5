import{d as o,p as e,o as i,c as p,g as r,t as c,i as a,w as _,F as m,_ as u,aN as d,__tla as f}from"./index-BUSn51wb.js";let s,g=Promise.all([(()=>{try{return f}catch{}})()]).then(async()=>{s=o({name:"Tooltip",__name:"Tooltip",props:{title:e.string.def(""),message:e.string.def(""),icon:e.string.def("ep:question-filled")},setup:t=>(x,h)=>{const l=u,n=d;return i(),p(m,null,[r("span",null,c(t.title),1),a(n,{content:t.message,placement:"top"},{default:_(()=>[a(l,{icon:t.icon,class:"relative top-1px ml-1px"},null,8,["icon"])]),_:1},8,["content"])],64)}})});export{s as _,g as __tla};
