import{_ as t,__tla as _}from"./ResetPwd.vue_vue_type_script_setup_true_lang-C3-4d7-M.js";import{__tla as r}from"./index-BUSn51wb.js";import{__tla as a}from"./XButton-BjahQbul.js";import{__tla as l}from"./InputPassword-RefetKoR.js";import{__tla as o}from"./profile-BQCm_-PE.js";let c=Promise.all([(()=>{try{return _}catch{}})(),(()=>{try{return r}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})()]).then(async()=>{});export{c as __tla,t as default};
