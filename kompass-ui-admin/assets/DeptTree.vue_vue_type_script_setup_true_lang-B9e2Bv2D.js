import{d as v,r,at as x,C as g,o as w,c as V,g as i,i as n,w as b,a as t,y as C,eh as D,F as T,_ as U,Z as j,__tla as F}from"./index-BUSn51wb.js";import{g as N,__tla as P}from"./index-Bqt292RI.js";import{d as R,h as S}from"./tree-BMa075Oj.js";let p,Z=Promise.all([(()=>{try{return F}catch{}})(),(()=>{try{return P}catch{}})()]).then(async()=>{let o,c;o={class:"head-container"},c={class:"head-container"},p=v({name:"SystemUserDeptTree",__name:"DeptTree",emits:["node-click"],setup(q,{emit:u}){const a=r(""),s=r([]),d=r(),m=(e,l)=>!e||l.name.includes(e),_=async e=>{h("node-click",e)},h=u;return x(a,e=>{d.value.filter(e)}),g(async()=>{await(async()=>{const e=await N();s.value=[],s.value.push(...S(e))})()}),(e,l)=>{const f=U,y=j;return w(),V(T,null,[i("div",o,[n(y,{modelValue:t(a),"onUpdate:modelValue":l[0]||(l[0]=k=>C(a)?a.value=k:null),class:"mb-20px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u90E8\u95E8\u540D\u79F0"},{prefix:b(()=>[n(f,{icon:"ep:search"})]),_:1},8,["modelValue"])]),i("div",c,[n(t(D),{ref_key:"treeRef",ref:d,data:t(s),"expand-on-click-node":!1,"filter-node-method":m,props:t(R),"default-expand-all":"","highlight-current":"","node-key":"id",onNodeClick:_},null,8,["data","props"])])],64)}}})});export{p as _,Z as __tla};
