import{d as U,n as J,I as j,r as d,o as m,c as b,i as r,w as u,a as s,j as f,H as A,l as g,F as E,k as L,cJ as T,G as q,t as B,y as D,am as G,an as H,L as P,Z,O as z,N as K,R as Q,__tla as W}from"./index-BUSn51wb.js";import{_ as X,__tla as Y}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{C as $}from"./constants-A8BI3pz7.js";import{g as aa,c as ea,u as la,__tla as sa}from"./index-BUKPQywH.js";let h,ta=Promise.all([(()=>{try{return W}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return sa}catch{}})()]).then(async()=>{h=U({name:"MockChannelForm",__name:"MockChannelForm",emits:["success"],setup(oa,{expose:w,emit:V}){const{t:_}=J(),p=j(),n=d(!1),v=d(""),c=d(!1),e=d({appId:"",code:"",status:void 0,feeRate:0,remark:"",config:{name:"mock-conf"}}),C={status:[{required:!0,message:"\u6E20\u9053\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]},i=d();w({open:async(l,a)=>{n.value=!0,c.value=!0,I(l,a);try{const o=await aa(l,a);o&&o.id&&(e.value=o,e.value.config=JSON.parse(o.config)),v.value=e.value.id?"\u7F16\u8F91\u652F\u4ED8\u6E20\u9053":"\u521B\u5EFA\u652F\u4ED8\u6E20\u9053"}finally{c.value=!1}}});const S=V,x=async()=>{if(i&&await i.value.validate()){c.value=!0;try{const l={...e.value};l.config=JSON.stringify(e.value.config),l.id?(await la(l),p.success(_("common.updateSuccess"))):(await ea(l),p.success(_("common.createSuccess"))),n.value=!1,S("success")}finally{c.value=!1}}},I=(l,a)=>{var o;e.value={appId:l,code:a,status:$.ENABLE,remark:"",feeRate:0,config:{name:"mock-conf"}},(o=i.value)==null||o.resetFields()};return(l,a)=>{const o=G,N=H,y=P,O=Z,F=z,k=K,M=X,R=Q;return m(),b("div",null,[r(M,{modelValue:s(n),"onUpdate:modelValue":a[3]||(a[3]=t=>D(n)?n.value=t:null),title:s(v),onClosed:l.close,width:"800px"},{footer:u(()=>[r(k,{disabled:s(c),type:"primary",onClick:x},{default:u(()=>[f("\u786E \u5B9A")]),_:1},8,["disabled"]),r(k,{onClick:a[2]||(a[2]=t=>n.value=!1)},{default:u(()=>[f("\u53D6 \u6D88")]),_:1})]),default:u(()=>[A((m(),g(F,{ref_key:"formRef",ref:i,model:s(e),rules:C,"label-width":"100px"},{default:u(()=>[r(y,{"label-width":"180px",label:"\u6E20\u9053\u72B6\u6001",prop:"status"},{default:u(()=>[r(N,{modelValue:s(e).status,"onUpdate:modelValue":a[0]||(a[0]=t=>s(e).status=t)},{default:u(()=>[(m(!0),b(E,null,L(s(T)(s(q).COMMON_STATUS),t=>(m(),g(o,{key:parseInt(t.value),label:parseInt(t.value)},{default:u(()=>[f(B(t.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),r(y,{"label-width":"180px",label:"\u5907\u6CE8",prop:"remark"},{default:u(()=>[r(O,{modelValue:s(e).remark,"onUpdate:modelValue":a[1]||(a[1]=t=>s(e).remark=t),style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])),[[R,s(c)]])]),_:1},8,["modelValue","title","onClosed"])])}}})});export{h as _,ta as __tla};
