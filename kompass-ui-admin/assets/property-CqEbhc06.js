import{_ as v,__tla as T}from"./ComponentContainerProperty-U-5gd_f0.js";import{d as j,r as C,o as s,l as P,w as o,i as t,a as l,j as f,c as d,F as i,k as $,a9 as A,cl as D,L as E,cf as F,O as H,__tla as L}from"./index-BUSn51wb.js";import{_ as M,__tla as O}from"./index.vue_vue_type_script_setup_true_lang-D01sdz8R.js";import{_ as S,__tla as q}from"./index-DuuciwNZ.js";import{E as G,__tla as I}from"./el-text-CIwNlU-U.js";import{u as J,__tla as K}from"./util-Dyp86Gv2.js";import{__tla as N}from"./el-card-CJbXGyyg.js";import{__tla as Q}from"./index-11u3nuTi.js";import"./color-BN7ZL7BD.js";import{__tla as W}from"./AppLinkSelectDialog.vue_vue_type_script_setup_true_lang-CHi5jpXX.js";import{__tla as X}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as Y}from"./ProductCategorySelect.vue_vue_type_script_setup_true_lang-CJItHfu_.js";import{__tla as Z}from"./el-tree-select-CBuha0HW.js";import"./tree-BMa075Oj.js";import{__tla as aa}from"./category-WzWM3ODe.js";import{__tla as ta}from"./Qrcode-CP7wmJi0.js";import{__tla as la}from"./IFrame.vue_vue_type_script_setup_true_lang-silYuU_p.js";import{__tla as ea}from"./vuedraggable.umd-BTL7hPHv.js";import{__tla as ra}from"./el-collapse-item-B_QvnH_b.js";let h,oa=Promise.all([(()=>{try{return T}catch{}})(),(()=>{try{return L}catch{}})(),(()=>{try{return O}catch{}})(),(()=>{try{return q}catch{}})(),(()=>{try{return I}catch{}})(),(()=>{try{return K}catch{}})(),(()=>{try{return N}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return W}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return Z}catch{}})(),(()=>{try{return aa}catch{}})(),(()=>{try{return ta}catch{}})(),(()=>{try{return la}catch{}})(),(()=>{try{return ea}catch{}})(),(()=>{try{return ra}catch{}})()]).then(async()=>{h=j({name:"MagicCubeProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(y,{emit:V}){const b=y,U=V,{formData:e}=J(b.modelValue,U),n=C(-1),w=(x,r)=>{n.value=r};return(x,r)=>{const c=G,g=S,R=D,_=E,z=M,u=F,k=H,B=v;return s(),P(B,{modelValue:l(e).style,"onUpdate:modelValue":r[4]||(r[4]=a=>l(e).style=a)},{default:o(()=>[t(k,{"label-width":"80px",model:l(e),class:"m-t-8px"},{default:o(()=>[t(c,{tag:"p"},{default:o(()=>[f(" \u9B54\u65B9\u8BBE\u7F6E ")]),_:1}),t(c,{type:"info",size:"small"},{default:o(()=>[f(" \u6BCF\u683C\u5C3A\u5BF8187 * 187 ")]),_:1}),t(g,{class:"m-y-16px",modelValue:l(e).list,"onUpdate:modelValue":r[0]||(r[0]=a=>l(e).list=a),rows:4,cols:4,onHotAreaSelected:w},null,8,["modelValue"]),(s(!0),d(i,null,$(l(e).list,(a,m)=>(s(),d(i,{key:m},[l(n)===m?(s(),d(i,{key:0},[t(_,{label:"\u4E0A\u4F20\u56FE\u7247",prop:`list[${m}].imgUrl`},{default:o(()=>[t(R,{modelValue:a.imgUrl,"onUpdate:modelValue":p=>a.imgUrl=p,height:"80px",width:"80px"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"]),t(_,{label:"\u94FE\u63A5",prop:`list[${m}].url`},{default:o(()=>[t(z,{modelValue:a.url,"onUpdate:modelValue":p=>a.url=p},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])],64)):A("",!0)],64))),128)),t(_,{label:"\u4E0A\u5706\u89D2",prop:"borderRadiusTop"},{default:o(()=>[t(u,{modelValue:l(e).borderRadiusTop,"onUpdate:modelValue":r[1]||(r[1]=a=>l(e).borderRadiusTop=a),max:100,min:0,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"])]),_:1}),t(_,{label:"\u4E0B\u5706\u89D2",prop:"borderRadiusBottom"},{default:o(()=>[t(u,{modelValue:l(e).borderRadiusBottom,"onUpdate:modelValue":r[2]||(r[2]=a=>l(e).borderRadiusBottom=a),max:100,min:0,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"])]),_:1}),t(_,{label:"\u95F4\u9694",prop:"space"},{default:o(()=>[t(u,{modelValue:l(e).space,"onUpdate:modelValue":r[3]||(r[3]=a=>l(e).space=a),max:100,min:0,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}})});export{oa as __tla,h as default};
