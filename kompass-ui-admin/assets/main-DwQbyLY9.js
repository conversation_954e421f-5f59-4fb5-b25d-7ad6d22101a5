import{d as x,o as a,c as s,F as b,k as U,g as e,i as B,t as v,B as E,__tla as F}from"./index-BUSn51wb.js";import{E as I,__tla as N}from"./el-image-BjHZRFih.js";let w,P=Promise.all([(()=>{try{return F}catch{}})(),(()=>{try{return N}catch{}})()]).then(async()=>{let l,i,n,r,c,m,_,o,d,h,u;l={class:"news-home"},i=["href"],n={class:"news-main"},r={class:"news-content"},c={class:"news-content-title"},m=["href"],_={class:"news-main-item"},o={class:"news-content-item"},d={class:"news-content-item-title"},h={class:"news-content-item-img"},u=["src"],w=E(x({name:"WxNews",__name:"main",props:{articles:{default:null}},setup:(f,{expose:g})=>(g({articles:f.articles}),(k,W)=>{const y=I;return a(),s("div",l,[(a(!0),s(b,null,U(k.articles,(t,p)=>(a(),s("div",{key:p,class:"news-div"},[p===0?(a(),s("a",{key:0,href:t.url,target:"_blank"},[e("div",n,[e("div",r,[B(y,{src:t.picUrl,class:"material-img",style:{width:"100%",height:"120px"}},null,8,["src"]),e("div",c,[e("span",null,v(t.title),1)])])])],8,i)):(a(),s("a",{key:1,href:t.url,target:"_blank"},[e("div",_,[e("div",o,[e("div",d,v(t.title),1),e("div",h,[e("img",{src:t.picUrl,class:"material-img",height:"100%"},null,8,u)])])])],8,m))]))),128))])})}),[["__scopeId","data-v-f0cf6e3b"]])});export{P as __tla,w as default};
