import{_ as p,__tla as u}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{d as y,b6 as v,o as f,l as g,w as o,i as _,a,g as e,t as s,j as h,N as b,__tla as j}from"./index-BUSn51wb.js";import{E as w,__tla as H}from"./el-image-BjHZRFih.js";let i,L=Promise.all([(()=>{try{return u}catch{}})(),(()=>{try{return j}catch{}})(),(()=>{try{return H}catch{}})()]).then(async()=>{let r,l,n,c;r={class:""},l={class:"text-[var(--el-text-color-secondary)] text-12px line-clamp-1"},n={class:"text-[var(--el-text-color-secondary)] text-12px"},c=["innerHTML"],i=y({name:"Index",__name:"index",setup(M){const t=v("currentSong",{});return(T,z)=>{const x=w,d=b,m=p;return f(),g(m,{class:"w-300px mb-[0!important] line-height-24px"},{default:o(()=>[_(x,{src:a(t).imageUrl},null,8,["src"]),e("div",r,s(a(t).title),1),e("div",l,s(a(t).desc),1),e("div",n,s(a(t).date),1),_(d,{size:"small",round:"",class:"my-6px"},{default:o(()=>[h("\u4FE1\u606F\u590D\u7528")]),_:1}),e("div",{class:"text-[var(--el-text-color-secondary)] text-12px",innerHTML:a(t).lyric},null,8,c)]),_:1})}}})});export{i as _,L as __tla};
