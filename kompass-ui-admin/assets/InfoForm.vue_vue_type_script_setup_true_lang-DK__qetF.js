import{d as j,p as O,r as p,f as w,at as z,C as K,o as c,l as y,w as r,a,O as N,i as t,c as C,k as B,F as J,I as L,dK as S,Z,L as q,cd as A,J as E,K as G,cl as H,cm as M,__tla as Q}from"./index-BUSn51wb.js";import{h as T,d as W}from"./tree-BMa075Oj.js";import{g as X,__tla as Y}from"./category-WzWM3ODe.js";import{g as $,__tla as aa}from"./brand-DwnGZI23.js";import{r as i,__tla as ea}from"./formRules-CA9eXdcX.js";let V,la=Promise.all([(()=>{try{return Q}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return aa}catch{}})(),(()=>{try{return ea}catch{}})()]).then(async()=>{V=j({name:"ProductSpuInfoForm",__name:"InfoForm",props:{propFormData:{type:Object,default:()=>{}},isDetail:O.bool.def(!1)},emits:["update:activeName"],setup(m,{expose:h,emit:U}){const _=m,I=L(),n=p(),e=w({name:"",categoryId:void 0,keyword:"",picUrl:"",sliderPicUrls:[],introduction:"",brandId:void 0}),g=w({name:[i],categoryId:[i],keyword:[i],introduction:[i],picUrl:[i],sliderPicUrls:[i],brandId:[i]});z(()=>_.propFormData,d=>{d&&S(e,d)},{immediate:!0});const x=U;h({validate:async()=>{var d;if(n)try{await((d=a(n))==null?void 0:d.validate()),Object.assign(_.propFormData,e)}catch(o){throw I.error("\u3010\u57FA\u7840\u8BBE\u7F6E\u3011\u4E0D\u5B8C\u5584\uFF0C\u8BF7\u586B\u5199\u76F8\u5173\u4FE1\u606F"),x("update:activeName","info"),o}}});const b=p([]),f=p([]);return K(async()=>{const d=await X({});f.value=T(d,"id"),b.value=await $()}),(d,o)=>{const u=Z,s=q,v=A,k=E,D=G,P=H,F=M,R=N;return c(),y(R,{ref_key:"formRef",ref:n,disabled:m.isDetail,model:a(e),rules:a(g),"label-width":"120px"},{default:r(()=>[t(s,{label:"\u5546\u54C1\u540D\u79F0",prop:"name"},{default:r(()=>[t(u,{modelValue:a(e).name,"onUpdate:modelValue":o[0]||(o[0]=l=>a(e).name=l),autosize:{minRows:2,maxRows:2},clearable:!0,"show-word-limit":!0,class:"w-80!",maxlength:"64",placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1\u540D\u79F0",type:"textarea"},null,8,["modelValue"])]),_:1}),t(s,{label:"\u5546\u54C1\u5206\u7C7B",prop:"categoryId"},{default:r(()=>[t(v,{modelValue:a(e).categoryId,"onUpdate:modelValue":o[1]||(o[1]=l=>a(e).categoryId=l),options:a(f),props:a(W),class:"w-80",clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u5546\u54C1\u5206\u7C7B"},null,8,["modelValue","options","props"])]),_:1}),t(s,{label:"\u5546\u54C1\u54C1\u724C",prop:"brandId"},{default:r(()=>[t(D,{modelValue:a(e).brandId,"onUpdate:modelValue":o[2]||(o[2]=l=>a(e).brandId=l),class:"w-80",placeholder:"\u8BF7\u9009\u62E9\u5546\u54C1\u54C1\u724C"},{default:r(()=>[(c(!0),C(J,null,B(a(b),l=>(c(),y(k,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(s,{label:"\u5546\u54C1\u5173\u952E\u5B57",prop:"keyword"},{default:r(()=>[t(u,{modelValue:a(e).keyword,"onUpdate:modelValue":o[3]||(o[3]=l=>a(e).keyword=l),class:"w-80!",placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1\u5173\u952E\u5B57"},null,8,["modelValue"])]),_:1}),t(s,{label:"\u5546\u54C1\u7B80\u4ECB",prop:"introduction"},{default:r(()=>[t(u,{modelValue:a(e).introduction,"onUpdate:modelValue":o[4]||(o[4]=l=>a(e).introduction=l),autosize:{minRows:2,maxRows:2},clearable:!0,"show-word-limit":!0,class:"w-80!",maxlength:"128",placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1\u540D\u79F0",type:"textarea"},null,8,["modelValue"])]),_:1}),t(s,{label:"\u5546\u54C1\u5C01\u9762\u56FE",prop:"picUrl"},{default:r(()=>[t(P,{modelValue:a(e).picUrl,"onUpdate:modelValue":o[5]||(o[5]=l=>a(e).picUrl=l),disabled:m.isDetail,height:"80px"},null,8,["modelValue","disabled"])]),_:1}),t(s,{label:"\u5546\u54C1\u8F6E\u64AD\u56FE",prop:"sliderPicUrls"},{default:r(()=>[t(F,{modelValue:a(e).sliderPicUrls,"onUpdate:modelValue":o[6]||(o[6]=l=>a(e).sliderPicUrls=l),disabled:m.isDetail},null,8,["modelValue","disabled"])]),_:1})]),_:1},8,["disabled","model","rules"])}}})});export{V as _,la as __tla};
