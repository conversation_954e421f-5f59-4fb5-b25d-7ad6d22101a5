import{d as S,n as j,I as A,r as i,f as G,o as p,l as v,w as t,i as u,a as e,j as f,H as L,c as B,F as D,k as E,D as H,G as T,t as Z,y as P,Z as z,L as J,am as K,an as M,O as Q,N as W,R as X,__tla as Y}from"./index-BUSn51wb.js";import{_ as $,__tla as ee}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{g as le,c as ae,u as re,__tla as ue}from"./index-BXfU_lLO.js";let k,te=Promise.all([(()=>{try{return Y}catch{}})(),(()=>{try{return ee}catch{}})(),(()=>{try{return ue}catch{}})()]).then(async()=>{k=S({name:"InfraConfigForm",__name:"ConfigForm",emits:["success"],setup(se,{expose:h,emit:U}){const{t:_}=j(),y=A(),o=i(!1),g=i(""),d=i(!1),b=i(""),r=i({id:void 0,category:"",name:"",key:"",value:"",visible:!0,remark:""}),w=G({category:[{required:!0,message:"\u53C2\u6570\u5206\u7C7B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],name:[{required:!0,message:"\u53C2\u6570\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],key:[{required:!0,message:"\u53C2\u6570\u952E\u540D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],value:[{required:!0,message:"\u53C2\u6570\u952E\u503C\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],visible:[{required:!0,message:"\u662F\u5426\u53EF\u89C1\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),c=i();h({open:async(s,l)=>{if(o.value=!0,g.value=_("action."+s),b.value=s,C(),l){d.value=!0;try{r.value=await le(l)}finally{d.value=!1}}}});const F=U,q=async()=>{if(c&&await c.value.validate()){d.value=!0;try{const s=r.value;b.value==="create"?(await ae(s),y.success(_("common.createSuccess"))):(await re(s),y.success(_("common.updateSuccess"))),o.value=!1,F("success")}finally{d.value=!1}}},C=()=>{var s;r.value={id:void 0,category:"",name:"",key:"",value:"",visible:!0,remark:""},(s=c.value)==null||s.resetFields()};return(s,l)=>{const n=z,m=J,I=K,x=M,N=Q,V=W,R=$,O=X;return p(),v(R,{modelValue:e(o),"onUpdate:modelValue":l[7]||(l[7]=a=>P(o)?o.value=a:null),title:e(g)},{footer:t(()=>[u(V,{disabled:e(d),type:"primary",onClick:q},{default:t(()=>[f("\u786E \u5B9A")]),_:1},8,["disabled"]),u(V,{onClick:l[6]||(l[6]=a=>o.value=!1)},{default:t(()=>[f("\u53D6 \u6D88")]),_:1})]),default:t(()=>[L((p(),v(N,{ref_key:"formRef",ref:c,model:e(r),rules:e(w),"label-width":"80px"},{default:t(()=>[u(m,{label:"\u53C2\u6570\u5206\u7C7B",prop:"category"},{default:t(()=>[u(n,{modelValue:e(r).category,"onUpdate:modelValue":l[0]||(l[0]=a=>e(r).category=a),placeholder:"\u8BF7\u8F93\u5165\u53C2\u6570\u5206\u7C7B"},null,8,["modelValue"])]),_:1}),u(m,{label:"\u53C2\u6570\u540D\u79F0",prop:"name"},{default:t(()=>[u(n,{modelValue:e(r).name,"onUpdate:modelValue":l[1]||(l[1]=a=>e(r).name=a),placeholder:"\u8BF7\u8F93\u5165\u53C2\u6570\u540D\u79F0"},null,8,["modelValue"])]),_:1}),u(m,{label:"\u53C2\u6570\u952E\u540D",prop:"key"},{default:t(()=>[u(n,{modelValue:e(r).key,"onUpdate:modelValue":l[2]||(l[2]=a=>e(r).key=a),placeholder:"\u8BF7\u8F93\u5165\u53C2\u6570\u952E\u540D"},null,8,["modelValue"])]),_:1}),u(m,{label:"\u53C2\u6570\u952E\u503C",prop:"value"},{default:t(()=>[u(n,{modelValue:e(r).value,"onUpdate:modelValue":l[3]||(l[3]=a=>e(r).value=a),placeholder:"\u8BF7\u8F93\u5165\u53C2\u6570\u952E\u503C"},null,8,["modelValue"])]),_:1}),u(m,{label:"\u662F\u5426\u53EF\u89C1",prop:"visible"},{default:t(()=>[u(x,{modelValue:e(r).visible,"onUpdate:modelValue":l[4]||(l[4]=a=>e(r).visible=a)},{default:t(()=>[(p(!0),B(D,null,E(e(H)(e(T).INFRA_BOOLEAN_STRING),a=>(p(),v(I,{key:a.value,label:a.value},{default:t(()=>[f(Z(a.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(m,{label:"\u5907\u6CE8",prop:"remark"},{default:t(()=>[u(n,{modelValue:e(r).remark,"onUpdate:modelValue":l[5]||(l[5]=a=>e(r).remark=a),placeholder:"\u8BF7\u8F93\u5165\u5185\u5BB9",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[O,e(d)]])]),_:1},8,["modelValue","title"])}}})});export{k as _,te as __tla};
