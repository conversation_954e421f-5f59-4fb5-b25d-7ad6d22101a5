import{d as x,r as m,f as v,C as w,o as _,c as P,i as a,w as n,a as e,H as R,l as k,dV as A,F as q,em as E,P as I,Q as j,R as C,__tla as D}from"./index-BUSn51wb.js";import{E as F,__tla as G}from"./el-card-CJbXGyyg.js";import{E as H,__tla as L}from"./el-skeleton-item-tDN8U6BH.js";import{_ as N,__tla as Q}from"./Echart.vue_vue_type_script_setup_true_lang-DnLZLH3w.js";import{S,__tla as V}from"./rank-CaJ4xEN0.js";let d,X=Promise.all([(()=>{try{return D}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return L}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return V}catch{}})()]).then(async()=>{d=x({name:"ReceivablePriceRank",__name:"ReceivablePriceRank",props:{queryParams:{}},setup(p,{expose:u}){const y=p,t=m(!1),o=m([]),r=v({dataset:{dimensions:["nickname","count"],source:[]},grid:{left:20,right:20,bottom:20,containLabel:!0},legend:{top:50},series:[{name:"\u56DE\u6B3E\u91D1\u989D\u6392\u884C",type:"bar"}],toolbox:{feature:{dataZoom:{yAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u56DE\u6B3E\u91D1\u989D\u6392\u884C"}}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},xAxis:{type:"value",name:"\u56DE\u6B3E\u91D1\u989D\uFF08\u5143\uFF09"},yAxis:{type:"category",name:"\u7B7E\u8BA2\u4EBA",nameGap:30}}),i=async()=>{t.value=!0;const l=await S.getReceivablePriceRank(y.queryParams);r.dataset&&r.dataset.source&&(r.dataset.source=E(l).reverse()),o.value=l,t.value=!1};return u({loadData:i}),w(()=>{i()}),(l,Z)=>{const h=N,g=H,c=F,s=I,f=j,b=C;return _(),P(q,null,[a(c,{shadow:"never"},{default:n(()=>[a(g,{loading:e(t),animated:""},{default:n(()=>[a(h,{height:500,options:e(r)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),a(c,{shadow:"never",class:"mt-16px"},{default:n(()=>[R((_(),k(f,{data:e(o)},{default:n(()=>[a(s,{label:"\u516C\u53F8\u6392\u540D",align:"center",type:"index",width:"80"}),a(s,{label:"\u7B7E\u8BA2\u4EBA",align:"center",prop:"nickname","min-width":"200"}),a(s,{label:"\u90E8\u95E8",align:"center",prop:"deptName","min-width":"200"}),a(s,{label:"\u56DE\u6B3E\u91D1\u989D\uFF08\u5143\uFF09",align:"center",prop:"count","min-width":"200",formatter:e(A)},null,8,["formatter"])]),_:1},8,["data"])),[[b,e(t)]])]),_:1})],64)}}})});export{d as _,X as __tla};
