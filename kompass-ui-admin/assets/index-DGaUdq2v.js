import{dv as B,d as z,I as D,r as U,f as $,u as j,C as J,bt as W,o as V,c as N,i as P,a as _,y as T,U as F,F as K,Z as R,a5 as q,a6 as H,g as O,B as Z,__tla as G}from"./index-BUSn51wb.js";import{O as Q,__tla as X}from"./index-T-3poKZQ.js";import{f as Y,__tla as aa}from"./formatTime-DWdBpgsM.js";import ea,{__tla as na}from"./PosterVue-Dubi2DC1.js";import{__tla as oa}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";let E,ia=Promise.all([(()=>{try{return G}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return aa}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return oa}catch{}})()]).then(async()=>{var S={exports:{}};let L,y,b,k;L=B(S.exports=function(){function f(e){var p=[];return e.AMapUI&&p.push(g(e.AMapUI)),e.Loca&&p.push(d(e.Loca)),Promise.all(p)}function g(e){return new Promise(function(p,r){var n=[];if(e.plugins)for(var a=0;a<e.plugins.length;a+=1)i.AMapUI.plugins.indexOf(e.plugins[a])==-1&&n.push(e.plugins[a]);if(s.AMapUI===o.failed)r("\u524D\u6B21\u8BF7\u6C42 AMapUI \u5931\u8D25");else if(s.AMapUI===o.notload){s.AMapUI=o.loading,i.AMapUI.version=e.version||i.AMapUI.version,a=i.AMapUI.version;var c=document.body||document.head,t=document.createElement("script");t.type="text/javascript",t.src="https://webapi.amap.com/ui/"+a+"/main.js",t.onerror=function(l){s.AMapUI=o.failed,r("\u8BF7\u6C42 AMapUI \u5931\u8D25")},t.onload=function(){if(s.AMapUI=o.loaded,n.length)window.AMapUI.loadUI(n,function(){for(var l=0,v=n.length;l<v;l++){var M=n[l].split("/").slice(-1)[0];window.AMapUI[M]=arguments[l]}for(p();u.AMapUI.length;)u.AMapUI.splice(0,1)[0]()});else for(p();u.AMapUI.length;)u.AMapUI.splice(0,1)[0]()},c.appendChild(t)}else s.AMapUI===o.loaded?e.version&&e.version!==i.AMapUI.version?r("\u4E0D\u5141\u8BB8\u591A\u4E2A\u7248\u672C AMapUI \u6DF7\u7528"):n.length?window.AMapUI.loadUI(n,function(){for(var l=0,v=n.length;l<v;l++){var M=n[l].split("/").slice(-1)[0];window.AMapUI[M]=arguments[l]}p()}):p():e.version&&e.version!==i.AMapUI.version?r("\u4E0D\u5141\u8BB8\u591A\u4E2A\u7248\u672C AMapUI \u6DF7\u7528"):u.AMapUI.push(function(l){l?r(l):n.length?window.AMapUI.loadUI(n,function(){for(var v=0,M=n.length;v<M;v++){var I=n[v].split("/").slice(-1)[0];window.AMapUI[I]=arguments[v]}p()}):p()})})}function d(e){return new Promise(function(p,r){if(s.Loca===o.failed)r("\u524D\u6B21\u8BF7\u6C42 Loca \u5931\u8D25");else if(s.Loca===o.notload){s.Loca=o.loading,i.Loca.version=e.version||i.Loca.version;var n=i.Loca.version,a=i.AMap.version.startsWith("2"),c=n.startsWith("2");if(a&&!c||!a&&c)r("JSAPI \u4E0E Loca \u7248\u672C\u4E0D\u5BF9\u5E94\uFF01\uFF01");else{a=i.key,c=document.body||document.head;var t=document.createElement("script");t.type="text/javascript",t.src="https://webapi.amap.com/loca?v="+n+"&key="+a,t.onerror=function(l){s.Loca=o.failed,r("\u8BF7\u6C42 AMapUI \u5931\u8D25")},t.onload=function(){for(s.Loca=o.loaded,p();u.Loca.length;)u.Loca.splice(0,1)[0]()},c.appendChild(t)}}else s.Loca===o.loaded?e.version&&e.version!==i.Loca.version?r("\u4E0D\u5141\u8BB8\u591A\u4E2A\u7248\u672C Loca \u6DF7\u7528"):p():e.version&&e.version!==i.Loca.version?r("\u4E0D\u5141\u8BB8\u591A\u4E2A\u7248\u672C Loca \u6DF7\u7528"):u.Loca.push(function(l){l?r(l):r()})})}if(!window)throw Error("AMap JSAPI can only be used in Browser.");var o,h;(h=o||(o={})).notload="notload",h.loading="loading",h.loaded="loaded",h.failed="failed";var i={key:"",AMap:{version:"1.4.15",plugins:[]},AMapUI:{version:"1.1",plugins:[]},Loca:{version:"1.3.2"}},s={AMap:o.notload,AMapUI:o.notload,Loca:o.notload},u={AMap:[],AMapUI:[],Loca:[]},A=[],w=function(e){typeof e=="function"&&(s.AMap===o.loaded?e(window.AMap):A.push(e))};return{load:function(e){return new Promise(function(p,r){if(s.AMap==o.failed)r("");else if(s.AMap==o.notload){var n=e.key,a=e.version,c=e.plugins;n?(window.AMap&&location.host!=="lbs.amap.com"&&r("\u7981\u6B62\u591A\u79CDAPI\u52A0\u8F7D\u65B9\u5F0F\u6DF7\u7528"),i.key=n,i.AMap.version=a||i.AMap.version,i.AMap.plugins=c||i.AMap.plugins,s.AMap=o.loading,a=document.body||document.head,window.___onAPILoaded=function(l){if(delete window.___onAPILoaded,l)s.AMap=o.failed,r(l);else for(s.AMap=o.loaded,f(e).then(function(){p(window.AMap)}).catch(r);A.length;)A.splice(0,1)[0]()},(c=document.createElement("script")).type="text/javascript",c.src="https://webapi.amap.com/maps?callback=___onAPILoaded&v="+i.AMap.version+"&key="+n+"&plugin="+i.AMap.plugins.join(","),c.onerror=function(l){s.AMap=o.failed,r(l)},a.appendChild(c)):r("\u8BF7\u586B\u5199key")}else if(s.AMap==o.loaded)if(e.key&&e.key!==i.key)r("\u591A\u4E2A\u4E0D\u4E00\u81F4\u7684 key");else if(e.version&&e.version!==i.AMap.version)r("\u4E0D\u5141\u8BB8\u591A\u4E2A\u7248\u672C JSAPI \u6DF7\u7528");else{if(n=[],e.plugins)for(a=0;a<e.plugins.length;a+=1)i.AMap.plugins.indexOf(e.plugins[a])==-1&&n.push(e.plugins[a]);n.length?window.AMap.plugin(n,function(){f(e).then(function(){p(window.AMap)}).catch(r)}):f(e).then(function(){p(window.AMap)}).catch(r)}else if(e.key&&e.key!==i.key)r("\u591A\u4E2A\u4E0D\u4E00\u81F4\u7684 key");else if(e.version&&e.version!==i.AMap.version)r("\u4E0D\u5141\u8BB8\u591A\u4E2A\u7248\u672C JSAPI \u6DF7\u7528");else{var t=[];if(e.plugins)for(a=0;a<e.plugins.length;a+=1)i.AMap.plugins.indexOf(e.plugins[a])==-1&&t.push(e.plugins[a]);w(function(){t.length?window.AMap.plugin(t,function(){f(e).then(function(){p(window.AMap)}).catch(r)}):f(e).then(function(){p(window.AMap)}).catch(r)})}})},reset:function(){delete window.AMap,delete window.AMapUI,delete window.Loca,i={key:"",AMap:{version:"1.4.15",plugins:[]},AMapUI:{version:"1.1",plugins:[]},Loca:{version:"1.3.2"}},s={AMap:o.notload,AMapUI:o.notload,Loca:o.notload},u={AMap:[],AMapUI:[],Loca:[]}}}}()),y=f=>(q("data-v-1b6df282"),f=f(),H(),f),b=y(()=>O("div",{id:"container"},null,-1)),k=y(()=>O("div",{id:"panel"},null,-1)),E=Z(z({name:"MapContainer",__name:"index",setup(f){const g=D();let d=null,o=U(""),h=null,i=[],s=null;const u=$({pageNo:1,pageSize:10}),A=U([]),w=n=>{if(d){if(i.length>0&&(d.remove(i),i=[]),n==null||n==="")return document.getElementById("panel").innerHTML="",void d.remove(i);s=new AMap.PlaceSearch({pageSize:2,pageIndex:1,city:"025",citylimit:!0,panel:"panel",autoFitView:!0}),s.search(n,(a,c)=>{a==="complete"&&c.info==="OK"?c.poiList.pois.forEach(t=>{h=new AMap.Marker({map:d,position:t.location,title:t.name,offset:new AMap.Pixel(-13,-30)}),i.push(h)}):(console.error("Search failed:",a,c),g.error("\u5730\u56FE\u67E5\u8BE2\u9519\u8BEF\uFF1A"+c))})}},e=async()=>{try{const n=await Q.getOrderMap(u);if(n===null)return;A.value=n,console.log(111,A.value.length)}finally{}},{push:p}=j(),r=U();return J(async()=>{await e(),window._AMapSecurityConfig={securityJsCode:"b3423bc381c356358ce06f25ce697e53"},L.load({key:"fcdb5fda7abca2df4985c82a20c30bf0",version:"2.0",plugins:["AMap.CircleMarker","AMap.LngLat","AMap.InfoWindow","AMap.PlaceSearch"]}).then(n=>{d=new n.Map("container",{viewMode:"3D",zoom:12,center:[118.796624,32.059344],resizeEnable:!0}),console.log("locations",A.value.length),A.value.forEach(a=>{const c=new n.LngLat(a.longitude,a.latitude),t=new n.CircleMarker({center:c,radius:10,strokeColor:"white",strokeWeight:2,strokeOpacity:.5,fillColor:"rgba(0,0,255,1)",fillOpacity:.5,zIndex:11,cursor:"pointer"}),l=`
          <div class="font-size-12px !w-240px">
            <div>
                <a href="#" id="btnOrder-${a.orderId}">`+a.orderId+`</a>
                <span class="ml-5px">`+Y(a.releaseTime)+`</span>
            </div>
            <div>
                <span>`+a.orderAddress+`</span>
            </div>
            <div>
                <span>`+a.customerName+`</span>
                <span>`+a.customerPhone+`</span>
            </div>
            <div>
                <span>\u5269\u4F59\u8BFE\u65F6\uFF1A</span>
                <span>`+a.lessonPeriodRemain+`</span>
                <span>\u8D1F\u8D23\u4EBA\uFF1A</span>
                <span>`+a.headOperateName+`</span>
            </div>
            <div>
                <span><a href="#" id="btnConfirm-${a.orderId}">`+a.confirmCount+`\u4EBA\u63A5\u5355\u5F85\u786E\u8BA4</a></span>
                <span class="ml-5px"><a href="#" id="btnCopyOrder-${a.orderId}">\u590D\u5236\u8BA2\u5355</a></span>
            </div>
            <div>
                <span>\u83B7\u5BA2\u6E20\u9053:</span>
                <span>`+a.sourceChannelDesc+`</span>
                <span class="ml-5px">\u671F\u671B\u6027\u522B:</span>
                <span>`+a.requireSexDesc+`</span>

            </div>
            <div>
                <div style="height: 1px; background-color: #bbbaba;"></div>
                <span class="color-warmgray">`+a.demandContent+`</span>
            </div>

          </div>
        `,v=new n.InfoWindow({content:l,anchor:"top-left"});t.on("click",()=>{v.open(d,c),setTimeout(()=>{const M=document.getElementById(`btnOrder-${a.orderId}`),I=document.getElementById(`btnConfirm-${a.orderId}`),x=document.getElementById(`btnCopyOrder-${a.orderId}`);M&&(M.onclick=()=>{(async m=>{console.log("openDetail::",m),await p({name:"ExpOrder2",params:{id:m}})})(a.orderId)}),I&&(I.onclick=()=>{return m=a.orderId,console.log("openDetail2"),void p({name:"CustomerDetail",params:{id:m}});var m}),x&&(x.onclick=()=>{return m="\u8BA2\u5355\u6D77\u62A5",C=a.orderId,void r.value.open(m,C);var m,C})},0)}),t.setMap(d)})}).catch(n=>{g.error(n)})}),W(()=>{d==null||d.destroy()}),(n,a)=>{const c=R;return V(),N(K,null,[b,k,P(c,{modelValue:_(o),"onUpdate:modelValue":a[0]||(a[0]=t=>T(o)?o.value=t:o=t),placeholder:"\u641C\u7D22\u5730\u70B9",onKeyup:a[1]||(a[1]=F(t=>w(_(o)),["enter"])),onBlur:a[2]||(a[2]=t=>w(_(o))),class:"searchInput",clearable:""},null,8,["modelValue"]),P(ea,{ref_key:"posterRef",ref:r,onSuccess:e},null,512)],64)}}}),[["__scopeId","data-v-1b6df282"]])});export{ia as __tla,E as default};
