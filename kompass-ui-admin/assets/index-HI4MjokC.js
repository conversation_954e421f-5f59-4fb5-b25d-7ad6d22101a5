import{d as j,f as G,e as K,r as u,b as O,at as X,C as Q,o as k,c as q,i as a,w as r,a as e,F as V,k as P,V as W,G as Z,l as Y,j as F,y as $,M as aa,L as ea,J as la,K as ra,_ as ta,N as sa,O as ua,z as _a,A as oa,E as ma,__tla as da}from"./index-BUSn51wb.js";import{_ as ca,__tla as na}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{E as ya,__tla as pa}from"./el-tree-select-CBuha0HW.js";import{g as fa,__tla as ia}from"./index-Bqt292RI.js";import{g as ha,__tla as va}from"./index-BYXzDB8j.js";import{f as M,e as ba,g as ka,h as wa,__tla as Ca}from"./formatTime-DWdBpgsM.js";import{h as H,d as Da}from"./tree-BMa075Oj.js";import{_ as qa,__tla as Va}from"./CustomerConversionStat.vue_vue_type_script_setup_true_lang-q0dKdvuh.js";import{_ as Ia,__tla as Ua}from"./CustomerDealCycleByUser.vue_vue_type_script_setup_true_lang-CeWh55QL.js";import{_ as Sa,__tla as ga}from"./CustomerDealCycleByArea.vue_vue_type_script_setup_true_lang-BzmmcJ2W.js";import{_ as Ra,__tla as xa}from"./CustomerDealCycleByProduct.vue_vue_type_script_setup_true_lang-B7ZlZxhC.js";import{_ as za,__tla as Ba}from"./CustomerFollowUpSummary.vue_vue_type_script_setup_true_lang-B0vPl9Y4.js";import{_ as Ta,__tla as Aa}from"./CustomerFollowUpType.vue_vue_type_script_setup_true_lang-DJhto4PA.js";import{_ as Ea,__tla as Pa}from"./CustomerSummary.vue_vue_type_script_setup_true_lang-Q1jVoGxO.js";import{_ as Ya,__tla as Fa}from"./CustomerPoolSummary.vue_vue_type_script_setup_true_lang-BNzKJ7Xt.js";import{__tla as Ma}from"./el-card-CJbXGyyg.js";import{__tla as Ha}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import"./color-BN7ZL7BD.js";import{__tla as Ja}from"./el-skeleton-item-tDN8U6BH.js";import{__tla as La}from"./Echart.vue_vue_type_script_setup_true_lang-DnLZLH3w.js";import{__tla as Na}from"./customer-DXRFD9ec.js";let J,ja=Promise.all([(()=>{try{return da}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return pa}catch{}})(),(()=>{try{return ia}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return Ca}catch{}})(),(()=>{try{return Va}catch{}})(),(()=>{try{return Ua}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return xa}catch{}})(),(()=>{try{return Ba}catch{}})(),(()=>{try{return Aa}catch{}})(),(()=>{try{return Pa}catch{}})(),(()=>{try{return Fa}catch{}})(),(()=>{try{return Ma}catch{}})(),(()=>{try{return Ha}catch{}})(),(()=>{try{return Ja}catch{}})(),(()=>{try{return La}catch{}})(),(()=>{try{return Na}catch{}})()]).then(async()=>{J=j({name:"CrmStatisticsCustomer",__name:"index",setup(Ga){const t=G({interval:2,deptId:K().getUser.deptId,userId:void 0,times:[M(ba(new Date(new Date().getTime()-6048e5))),M(ka(new Date(new Date().getTime()-864e5)))]}),I=u(),U=u([]),S=u([]),L=O(()=>t.deptId?S.value.filter(d=>d.deptId===t.deptId):[]),h=u("customerSummary"),g=u(),R=u(),x=u(),z=u(),B=u(),T=u(),A=u(),E=u(),m=()=>{var d,s,y,o,c,v,p,b,n,w,f,_,i,C,l,D;switch(h.value){case"customerSummary":(s=(d=g.value)==null?void 0:d.loadData)==null||s.call(d);break;case"followUpSummary":(o=(y=R.value)==null?void 0:y.loadData)==null||o.call(y);break;case"followUpType":(v=(c=x.value)==null?void 0:c.loadData)==null||v.call(c);break;case"conversionStat":(b=(p=z.value)==null?void 0:p.loadData)==null||b.call(p);break;case"poolSummary":(w=(n=B.value)==null?void 0:n.loadData)==null||w.call(n);break;case"dealCycleByUser":(_=(f=T.value)==null?void 0:f.loadData)==null||_.call(f);break;case"dealCycleByArea":(C=(i=A.value)==null?void 0:i.loadData)==null||C.call(i);break;case"dealCycleByProduct":(D=(l=E.value)==null?void 0:l.loadData)==null||D.call(l)}};X(h,()=>{m()});const N=()=>{I.value.resetFields(),m()};return Q(async()=>{U.value=H(await fa()),S.value=H(await ha())}),(d,s)=>{const y=aa,o=ea,c=la,v=ra,p=ya,b=ta,n=sa,w=ua,f=ca,_=_a,i=oa,C=ma;return k(),q(V,null,[a(f,null,{default:r(()=>[a(w,{ref_key:"queryFormRef",ref:I,inline:!0,model:e(t),class:"-mb-15px","label-width":"68px"},{default:r(()=>[a(o,{label:"\u65F6\u95F4\u8303\u56F4",prop:"orderDate"},{default:r(()=>[a(y,{modelValue:e(t).times,"onUpdate:modelValue":s[0]||(s[0]=l=>e(t).times=l),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],shortcuts:e(wa),class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss",onChange:m},null,8,["modelValue","default-time","shortcuts"])]),_:1}),a(o,{label:"\u65F6\u95F4\u95F4\u9694",prop:"interval"},{default:r(()=>[a(v,{modelValue:e(t).interval,"onUpdate:modelValue":s[1]||(s[1]=l=>e(t).interval=l),class:"!w-240px",placeholder:"\u95F4\u9694\u7C7B\u578B",onChange:m},{default:r(()=>[(k(!0),q(V,null,P(e(W)(e(Z).DATE_INTERVAL),l=>(k(),Y(c,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(o,{label:"\u5F52\u5C5E\u90E8\u95E8",prop:"deptId"},{default:r(()=>[a(p,{modelValue:e(t).deptId,"onUpdate:modelValue":s[2]||(s[2]=l=>e(t).deptId=l),data:e(U),props:e(Da),"check-strictly":"",class:"!w-240px","node-key":"id",placeholder:"\u8BF7\u9009\u62E9\u5F52\u5C5E\u90E8\u95E8",onChange:s[3]||(s[3]=l=>(e(t).userId=void 0,m()))},null,8,["modelValue","data","props"])]),_:1}),a(o,{label:"\u5458\u5DE5",prop:"userId"},{default:r(()=>[a(v,{modelValue:e(t).userId,"onUpdate:modelValue":s[4]||(s[4]=l=>e(t).userId=l),class:"!w-240px",clearable:"",placeholder:"\u5458\u5DE5",onChange:m},{default:r(()=>[(k(!0),q(V,null,P(e(L),(l,D)=>(k(),Y(c,{key:D,label:l.nickname,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(o,null,{default:r(()=>[a(n,{onClick:m},{default:r(()=>[a(b,{class:"mr-5px",icon:"ep:search"}),F(" \u67E5\u8BE2 ")]),_:1}),a(n,{onClick:N},{default:r(()=>[a(b,{class:"mr-5px",icon:"ep:refresh"}),F(" \u91CD\u7F6E ")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),a(C,null,{default:r(()=>[a(i,{modelValue:e(h),"onUpdate:modelValue":s[5]||(s[5]=l=>$(h)?h.value=l:null)},{default:r(()=>[a(_,{label:"\u5BA2\u6237\u603B\u91CF\u5206\u6790",lazy:"",name:"customerSummary"},{default:r(()=>[a(Ea,{ref_key:"customerSummaryRef",ref:g,"query-params":e(t)},null,8,["query-params"])]),_:1}),a(_,{label:"\u5BA2\u6237\u8DDF\u8FDB\u6B21\u6570\u5206\u6790",lazy:"",name:"followUpSummary"},{default:r(()=>[a(za,{ref_key:"followUpSummaryRef",ref:R,"query-params":e(t)},null,8,["query-params"])]),_:1}),a(_,{label:"\u5BA2\u6237\u8DDF\u8FDB\u65B9\u5F0F\u5206\u6790",lazy:"",name:"followUpType"},{default:r(()=>[a(Ta,{ref_key:"followUpTypeRef",ref:x,"query-params":e(t)},null,8,["query-params"])]),_:1}),a(_,{label:"\u5BA2\u6237\u8F6C\u5316\u7387\u5206\u6790",lazy:"",name:"conversionStat"},{default:r(()=>[a(qa,{ref_key:"conversionStatRef",ref:z,"query-params":e(t)},null,8,["query-params"])]),_:1}),a(_,{label:"\u516C\u6D77\u5BA2\u6237\u5206\u6790",lazy:"",name:"poolSummary"},{default:r(()=>[a(Ya,{ref_key:"customerPoolSummaryRef",ref:B,"query-params":e(t)},null,8,["query-params"])]),_:1}),a(_,{label:"\u5458\u5DE5\u5BA2\u6237\u6210\u4EA4\u5468\u671F\u5206\u6790",lazy:"",name:"dealCycleByUser"},{default:r(()=>[a(Ia,{ref_key:"dealCycleByUserRef",ref:T,"query-params":e(t)},null,8,["query-params"])]),_:1}),a(_,{label:"\u5730\u533A\u5BA2\u6237\u6210\u4EA4\u5468\u671F\u5206\u6790",lazy:"",name:"dealCycleByArea"},{default:r(()=>[a(Sa,{ref_key:"dealCycleByAreaRef",ref:A,"query-params":e(t)},null,8,["query-params"])]),_:1}),a(_,{label:"\u4EA7\u54C1\u5BA2\u6237\u6210\u4EA4\u5468\u671F\u5206\u6790",lazy:"",name:"dealCycleByProduct"},{default:r(()=>[a(Ra,{ref_key:"dealCycleByProductRef",ref:E,"query-params":e(t)},null,8,["query-params"])]),_:1})]),_:1},8,["modelValue"])]),_:1})],64)}}})});export{ja as __tla,J as default};
