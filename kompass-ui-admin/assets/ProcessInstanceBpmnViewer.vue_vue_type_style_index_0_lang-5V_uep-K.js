import{by as y,d,p as t,r as o,at as f,H as g,o as b,l as I,w as p,R as v,i as w,ao as x,a as s,g as D,__tla as h}from"./index-BUSn51wb.js";import{E as k,__tla as B}from"./el-card-CJbXGyyg.js";import{j as P,__tla as V}from"./bpmn-embedded-D6vUWKn8.js";let _,X=Promise.all([(()=>{try{return h}catch{}})(),(()=>{try{return B}catch{}})(),(()=>{try{return V}catch{}})()]).then(async()=>{let r;r=D("span",{class:"el-icon-picture-outline"},"\u6D41\u7A0B\u56FE",-1),_=d({name:"BpmProcessInstanceBpmnViewer",__name:"ProcessInstanceBpmnViewer",props:{loading:t.bool,id:t.string,processInstance:t.any,tasks:t.array,bpmnXml:t.string},setup(a){const e=a,n=o({prefix:"flowable"}),c=o([]);return f(()=>e.loading,async l=>{l&&e.id&&(c.value=await(async i=>await y.get({url:"/bpm/activity/list",params:i}))({processInstanceId:e.id}))}),(l,i)=>{const m=k,u=v;return g((b(),I(m,{class:"box-card"},{header:p(()=>[r]),default:p(()=>[w(s(P),x({key:"designer",activityData:s(c),prefix:s(n).prefix,processInstanceData:a.processInstance,taskData:a.tasks,value:a.bpmnXml},s(n)),null,16,["activityData","prefix","processInstanceData","taskData","value"])]),_:1})),[[u,a.loading]])}}})});export{_,X as __tla};
