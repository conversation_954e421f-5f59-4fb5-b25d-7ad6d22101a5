import{d as D,r as u,f as b,C as w,o as d,c as v,i as t,w as o,a as n,H as A,l as P,F as I,P as N,Q as B,R as q,__tla as E}from"./index-BUSn51wb.js";import{E as L,__tla as S}from"./el-card-CJbXGyyg.js";import{E as F,__tla as H}from"./el-skeleton-item-tDN8U6BH.js";import{_ as Q,__tla as R}from"./Echart.vue_vue_type_script_setup_true_lang-DnLZLH3w.js";import{S as X,__tla as Z}from"./customer-DXRFD9ec.js";let p,j=Promise.all([(()=>{try{return E}catch{}})(),(()=>{try{return S}catch{}})(),(()=>{try{return H}catch{}})(),(()=>{try{return R}catch{}})(),(()=>{try{return Z}catch{}})()]).then(async()=>{p=D({name:"CustomerDealCycleByProduct",__name:"CustomerDealCycleByProduct",props:{queryParams:{}},setup(y,{expose:_}){const x=y,r=u(!1),i=u([]),a=b({grid:{left:20,right:40,bottom:20,containLabel:!0},legend:{},series:[{name:"\u6210\u4EA4\u5468\u671F(\u5929)",type:"bar",data:[],yAxisIndex:0},{name:"\u6210\u4EA4\u5BA2\u6237\u6570",type:"bar",data:[],yAxisIndex:1}],toolbox:{feature:{dataZoom:{xAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u6210\u4EA4\u5468\u671F\u5206\u6790"}}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},yAxis:[{type:"value",name:"\u6210\u4EA4\u5468\u671F(\u5929)",min:0,minInterval:1},{type:"value",name:"\u6210\u4EA4\u5BA2\u6237\u6570",min:0,minInterval:1,splitLine:{lineStyle:{type:"dotted",opacity:.7}}}],xAxis:{type:"category",name:"\u4EA7\u54C1\u540D\u79F0",data:[]}}),m=async()=>{r.value=!0;try{await(async()=>{const s=(await X.getCustomerDealCycleByProduct(x.queryParams)).map(e=>({productName:e.productName??"\u672A\u77E5",customerDealCycle:e.customerDealCount,customerDealCount:e.customerDealCount}));a.xAxis&&a.xAxis.data&&(a.xAxis.data=s.map(e=>e.productName)),a.series&&a.series[0]&&a.series[0].data&&(a.series[0].data=s.map(e=>e.customerDealCycle)),a.series&&a.series[1]&&a.series[1].data&&(a.series[1].data=s.map(e=>e.customerDealCount)),i.value=s})()}finally{r.value=!1}};return _({loadData:m}),w(()=>{m()}),(s,e)=>{const h=Q,g=F,c=L,l=N,C=B,f=q;return d(),v(I,null,[t(c,{shadow:"never"},{default:o(()=>[t(g,{loading:n(r),animated:""},{default:o(()=>[t(h,{height:500,options:n(a)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),t(c,{shadow:"never",class:"mt-16px"},{default:o(()=>[A((d(),P(C,{data:n(i)},{default:o(()=>[t(l,{label:"\u5E8F\u53F7",align:"center",type:"index",width:"80"}),t(l,{label:"\u4EA7\u54C1\u540D\u79F0",align:"center",prop:"productName","min-width":"200"}),t(l,{label:"\u6210\u4EA4\u5468\u671F(\u5929)",align:"center",prop:"customerDealCycle","min-width":"200"}),t(l,{label:"\u6210\u4EA4\u5BA2\u6237\u6570",align:"center",prop:"customerDealCount","min-width":"200"})]),_:1},8,["data"])),[[f,n(r)]])]),_:1})],64)}}})});export{p as _,j as __tla};
