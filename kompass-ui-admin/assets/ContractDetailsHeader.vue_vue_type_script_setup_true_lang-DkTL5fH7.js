import{_ as b,__tla as v}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{d as y,o as x,c as h,g as s,i as a,w as t,t as e,aV as w,j as c,a as _,dX as u,F as D,s as j,E as C,__tla as E}from"./index-BUSn51wb.js";import{E as P,a as g,__tla as H}from"./el-descriptions-item-dD3qa0ub.js";import{f as N,__tla as F}from"./formatTime-DWdBpgsM.js";let d,R=Promise.all([(()=>{try{return v}catch{}})(),(()=>{try{return E}catch{}})(),(()=>{try{return H}catch{}})(),(()=>{try{return F}catch{}})()]).then(async()=>{let n,o;n={class:"flex items-start justify-between"},o={class:"text-xl font-bold"},d=y({name:"ContractDetailsHeader",__name:"ContractDetailsHeader",props:{contract:{}},setup:U=>(l,V)=>{const i=j,f=C,r=P,m=g,p=b;return x(),h(D,null,[s("div",null,[s("div",n,[s("div",null,[a(f,null,{default:t(()=>[a(i,null,{default:t(()=>[s("span",o,e(l.contract.name),1)]),_:1})]),_:1})]),s("div",null,[w(l.$slots,"default")])])]),a(p,{class:"mt-10px"},{default:t(()=>[a(m,{column:5,direction:"vertical"},{default:t(()=>[a(r,{label:"\u5BA2\u6237\u540D\u79F0"},{default:t(()=>[c(e(l.contract.customerName),1)]),_:1}),a(r,{label:"\u5408\u540C\u91D1\u989D\uFF08\u5143\uFF09"},{default:t(()=>[c(e(_(u)(l.contract.totalPrice)),1)]),_:1}),a(r,{label:"\u4E0B\u5355\u65F6\u95F4"},{default:t(()=>[c(e(_(N)(l.contract.orderDate)),1)]),_:1}),a(r,{label:"\u56DE\u6B3E\u91D1\u989D\uFF08\u5143\uFF09"},{default:t(()=>[c(e(_(u)(l.contract.totalReceivablePrice)),1)]),_:1}),a(r,{label:"\u8D1F\u8D23\u4EBA"},{default:t(()=>[c(e(l.contract.ownerUserName),1)]),_:1})]),_:1})]),_:1})],64)}})});export{d as _,R as __tla};
