import{d as Q,I as Z,n as B,r as c,f as D,C as E,T as W,o as s,c as k,i as e,w as t,a as l,U as X,F as w,k as T,dR as Y,G as f,l as u,V as $,j as m,H as y,Z as aa,L as ea,J as la,K as ta,_ as ra,N as oa,O as sa,P as pa,Q as ua,R as na,__tla as _a}from"./index-BUSn51wb.js";import{_ as ia,__tla as ca}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as ma,__tla as da}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as fa,__tla as ya}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{A as F,__tla as va}from"./index-BRuDnVkN.js";import{_ as ba,__tla as ga}from"./ApiKeyForm.vue_vue_type_script_setup_true_lang-CotBh4nx.js";import{__tla as ha}from"./index-Cch5e1V0.js";import"./color-BN7ZL7BD.js";import{__tla as ka}from"./el-card-CJbXGyyg.js";import{__tla as wa}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import"./constants-A8BI3pz7.js";let K,Aa=Promise.all([(()=>{try{return _a}catch{}})(),(()=>{try{return ca}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return ya}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return ha}catch{}})(),(()=>{try{return ka}catch{}})(),(()=>{try{return wa}catch{}})()]).then(async()=>{K=Q({name:"AiApiKey",__name:"index",setup(Ca){const A=Z(),{t:M}=B(),v=c(!0),C=c([]),x=c(0),o=D({pageNo:1,pageSize:10,name:void 0,platform:void 0,status:void 0}),V=c(),n=async()=>{v.value=!0;try{const _=await F.getApiKeyPage(o);C.value=_.list,x.value=_.total}finally{v.value=!1}},b=()=>{o.pageNo=1,n()},I=()=>{V.value.resetFields(),b()},S=c(),U=(_,r)=>{S.value.open(_,r)};return E(()=>{n()}),(_,r)=>{const L=aa,d=ea,P=la,N=ta,g=ra,i=oa,z=sa,O=fa,R=ma,p=pa,j=ua,q=ia,h=W("hasPermi"),G=na;return s(),k(w,null,[e(O,null,{default:t(()=>[e(z,{class:"-mb-15px",model:l(o),ref_key:"queryFormRef",ref:V,inline:!0,"label-width":"68px"},{default:t(()=>[e(d,{label:"\u540D\u79F0",prop:"name"},{default:t(()=>[e(L,{modelValue:l(o).name,"onUpdate:modelValue":r[0]||(r[0]=a=>l(o).name=a),placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0",clearable:"",onKeyup:X(b,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(d,{label:"\u5E73\u53F0",prop:"platform"},{default:t(()=>[e(N,{modelValue:l(o).platform,"onUpdate:modelValue":r[1]||(r[1]=a=>l(o).platform=a),placeholder:"\u8BF7\u8F93\u5165\u5E73\u53F0",clearable:"",class:"!w-240px"},{default:t(()=>[(s(!0),k(w,null,T(l(Y)(l(f).AI_PLATFORM),a=>(s(),u(P,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"\u72B6\u6001",prop:"status"},{default:t(()=>[e(N,{modelValue:l(o).status,"onUpdate:modelValue":r[2]||(r[2]=a=>l(o).status=a),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",clearable:"",class:"!w-240px"},{default:t(()=>[(s(!0),k(w,null,T(l($)(l(f).COMMON_STATUS),a=>(s(),u(P,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(d,null,{default:t(()=>[e(i,{onClick:b},{default:t(()=>[e(g,{icon:"ep:search",class:"mr-5px"}),m(" \u641C\u7D22")]),_:1}),e(i,{onClick:I},{default:t(()=>[e(g,{icon:"ep:refresh",class:"mr-5px"}),m(" \u91CD\u7F6E")]),_:1}),y((s(),u(i,{type:"primary",plain:"",onClick:r[3]||(r[3]=a=>U("create"))},{default:t(()=>[e(g,{icon:"ep:plus",class:"mr-5px"}),m(" \u65B0\u589E ")]),_:1})),[[h,["ai:api-key:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(O,null,{default:t(()=>[y((s(),u(j,{data:l(C),stripe:!0,"show-overflow-tooltip":!0},{default:t(()=>[e(p,{label:"\u6240\u5C5E\u5E73\u53F0",align:"center",prop:"platform"},{default:t(a=>[e(R,{type:l(f).AI_PLATFORM,value:a.row.platform},null,8,["type","value"])]),_:1}),e(p,{label:"\u540D\u79F0",align:"center",prop:"name"}),e(p,{label:"\u5BC6\u94A5",align:"center",prop:"apiKey"}),e(p,{label:"\u81EA\u5B9A\u4E49 API URL",align:"center",prop:"url"}),e(p,{label:"\u72B6\u6001",align:"center",prop:"status"},{default:t(a=>[e(R,{type:l(f).COMMON_STATUS,value:a.row.status},null,8,["type","value"])]),_:1}),e(p,{label:"\u64CD\u4F5C",align:"center"},{default:t(a=>[y((s(),u(i,{link:"",type:"primary",onClick:H=>U("update",a.row.id)},{default:t(()=>[m(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[h,["ai:api-key:update"]]]),y((s(),u(i,{link:"",type:"danger",onClick:H=>(async J=>{try{await A.delConfirm(),await F.deleteApiKey(J),A.success(M("common.delSuccess")),await n()}catch{}})(a.row.id)},{default:t(()=>[m(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[h,["ai:api-key:delete"]]])]),_:1})]),_:1},8,["data"])),[[G,l(v)]]),e(q,{total:l(x),page:l(o).pageNo,"onUpdate:page":r[4]||(r[4]=a=>l(o).pageNo=a),limit:l(o).pageSize,"onUpdate:limit":r[5]||(r[5]=a=>l(o).pageSize=a),onPagination:n},null,8,["total","page","limit"])]),_:1}),e(ba,{ref_key:"formRef",ref:S,onSuccess:n},null,512)],64)}}})});export{Aa as __tla,K as default};
