import{d as E,r as _,bc as G,C as H,o as a,c as s,i as n,w as m,g as I,F as R,k as q,a as e,a0 as z,j as D,t as J,l,a9 as c,eg as K,E as L,s as M,B as N,__tla as O}from"./index-BUSn51wb.js";import{_ as Q,__tla as S}from"./index-COobLwz-.js";import{_ as U,__tla as W}from"./CustomerFollowList.vue_vue_type_script_setup_true_lang-BCp5UO-w.js";import{_ as X,__tla as V}from"./CustomerTodayContactList.vue_vue_type_script_setup_true_lang-LfKAKokj.js";import{_ as Y,__tla as Z}from"./CustomerPutPoolRemindList.vue_vue_type_script_setup_true_lang-B_EbJYcN.js";import{_ as $,__tla as tt}from"./ClueFollowList.vue_vue_type_script_setup_true_lang-_Ezke3Xw.js";import{_ as at,__tla as rt}from"./ContractAuditList.vue_vue_type_script_setup_true_name_CheckContract_lang-C_utIzks.js";import{_ as _t,__tla as et}from"./ContractRemindList.vue_vue_type_script_setup_true_name_EndContract_lang-DmMcKAFg.js";import{_ as lt,__tla as ct}from"./ReceivablePlanRemindList.vue_vue_type_script_setup_true_lang-BYajaI0f.js";import{_ as ot,__tla as nt}from"./ReceivableAuditList.vue_vue_type_script_setup_true_lang-CP9trVSU.js";import{h as st,i as mt,j as ut,__tla as it}from"./index-CD52sTBY.js";import{d as yt,__tla as ht}from"./index-CRiW4Z5g.js";import{f as pt,h as ft,__tla as dt}from"./index-DrB1WZUR.js";import{e as vt,__tla as kt}from"./index-D3Ji6shA.js";import{f as bt,__tla as wt}from"./index-Uo5NQqNb.js";import{__tla as Ct}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{__tla as Pt}from"./index-Cch5e1V0.js";import{__tla as Rt}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import"./color-BN7ZL7BD.js";import{__tla as xt}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{__tla as At}from"./el-card-CJbXGyyg.js";import{__tla as Ft}from"./formatTime-DWdBpgsM.js";import"./common-BQQO87UM.js";import{__tla as gt}from"./el-text-CIwNlU-U.js";import{__tla as Tt}from"./ReceivableForm.vue_vue_type_script_setup_true_lang-37IhTDRa.js";import{__tla as jt}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as Bt}from"./index-BYXzDB8j.js";let x,Et=Promise.all([(()=>{try{return O}catch{}})(),(()=>{try{return S}catch{}})(),(()=>{try{return W}catch{}})(),(()=>{try{return V}catch{}})(),(()=>{try{return Z}catch{}})(),(()=>{try{return tt}catch{}})(),(()=>{try{return rt}catch{}})(),(()=>{try{return et}catch{}})(),(()=>{try{return ct}catch{}})(),(()=>{try{return nt}catch{}})(),(()=>{try{return it}catch{}})(),(()=>{try{return ht}catch{}})(),(()=>{try{return dt}catch{}})(),(()=>{try{return kt}catch{}})(),(()=>{try{return wt}catch{}})(),(()=>{try{return Ct}catch{}})(),(()=>{try{return Pt}catch{}})(),(()=>{try{return Rt}catch{}})(),(()=>{try{return xt}catch{}})(),(()=>{try{return At}catch{}})(),(()=>{try{return Ft}catch{}})(),(()=>{try{return gt}catch{}})(),(()=>{try{return Tt}catch{}})(),(()=>{try{return jt}catch{}})(),(()=>{try{return Bt}catch{}})()]).then(async()=>{let u,i,y;u={class:"side-item-list"},i=["onClick"],y=E({name:"CrmBacklog",__name:"index",setup(Gt){const r=_("customerTodayContact"),h=_(0),p=_(0),f=_(0),d=_(0),v=_(0),k=_(0),b=_(0),w=_(0),A=_([{name:"\u4ECA\u65E5\u9700\u8054\u7CFB\u5BA2\u6237",menu:"customerTodayContact",count:d},{name:"\u5206\u914D\u7ED9\u6211\u7684\u7EBF\u7D22",menu:"clueFollow",count:h},{name:"\u5206\u914D\u7ED9\u6211\u7684\u5BA2\u6237",menu:"customerFollow",count:p},{name:"\u5F85\u8FDB\u5165\u516C\u6D77\u7684\u5BA2\u6237",menu:"customerPutPoolRemind",count:f},{name:"\u5F85\u5BA1\u6838\u5408\u540C",menu:"contractAudit",count:v},{name:"\u5F85\u5BA1\u6838\u56DE\u6B3E",menu:"receivableAudit",count:b},{name:"\u5F85\u56DE\u6B3E\u63D0\u9192",menu:"receivablePlanRemind",count:w},{name:"\u5373\u5C06\u5230\u671F\u7684\u5408\u540C",menu:"contractRemind",count:k}]),C=()=>{st().then(t=>d.value=t),mt().then(t=>f.value=t),ut().then(t=>p.value=t),yt().then(t=>h.value=t),pt().then(t=>v.value=t),ft().then(t=>k.value=t),vt().then(t=>b.value=t),bt().then(t=>w.value=t)};return G(async()=>{C()}),H(async()=>{C()}),(t,Ht)=>{const F=Q,g=K,P=L,T=M;return a(),s(R,null,[n(F,{title:"\u3010\u901A\u7528\u3011\u8DDF\u8FDB\u8BB0\u5F55\u3001\u5F85\u529E\u4E8B\u9879",url:"https://doc.iocoder.cn/crm/follow-up/"}),n(T,{gutter:20},{default:m(()=>[n(P,{span:4,class:"min-w-[200px]"},{default:m(()=>[I("div",u,[(a(!0),s(R,null,q(e(A),(o,j)=>(a(),s("div",{key:j,class:z([e(r)==o.menu?"side-item-select":"side-item-default","side-item"]),onClick:It=>(B=>{r.value=B.menu})(o)},[D(J(o.name)+" ",1),o.count>0?(a(),l(g,{key:0,max:99,value:o.count},null,8,["value"])):c("",!0)],10,i))),128))])]),_:1}),n(P,{span:20,xs:24},{default:m(()=>[e(r)==="customerTodayContact"?(a(),l(X,{key:0})):c("",!0),e(r)==="clueFollow"?(a(),l($,{key:1})):c("",!0),e(r)==="contractAudit"?(a(),l(at,{key:2})):c("",!0),e(r)==="receivableAudit"?(a(),l(ot,{key:3})):c("",!0),e(r)==="contractRemind"?(a(),l(_t,{key:4})):c("",!0),e(r)==="customerFollow"?(a(),l(U,{key:5})):c("",!0),e(r)==="customerPutPoolRemind"?(a(),l(Y,{key:6})):c("",!0),e(r)==="receivablePlanRemind"?(a(),l(lt,{key:7})):c("",!0)]),_:1})]),_:1})],64)}}}),x=N(y,[["__scopeId","data-v-86959cbe"]])});export{Et as __tla,x as default};
