import{d as v,b as V,r as x,C as h,o as l,l as m,w as o,c as g,F as w,k,a as _,g as L,i as j,j as M,t as S,y as z,J as C,K as E,__tla as F}from"./index-BUSn51wb.js";import{E as J,__tla as K}from"./el-avatar-Da2TGjmj.js";import{a as N,__tla as P}from"./index-DmYUs3M3.js";let c,U=Promise.all([(()=>{try{return F}catch{}})(),(()=>{try{return K}catch{}})(),(()=>{try{return P}catch{}})()]).then(async()=>{let t;t={class:"flex items-center gap-x-8px"},c=v({name:"MemberLevelSelect",__name:"MemberLevelSelect",props:{modelValue:{type:Number,default:void 0}},emits:["update:modelValue"],setup(n,{emit:d}){const p=n,i=d,e=V({get:()=>p.modelValue,set(r){i("update:modelValue",r)}}),s=x([]);return h(()=>{(async()=>s.value=await N())()}),(r,u)=>{const y=J,b=C,f=E;return l(),m(f,{modelValue:_(e),"onUpdate:modelValue":u[0]||(u[0]=a=>z(e)?e.value=a:null),placeholder:"\u8BF7\u9009\u62E9\u7528\u6237\u7B49\u7EA7",clearable:"",class:"!w-240px"},{default:o(()=>[(l(!0),g(w,null,k(_(s),a=>(l(),m(b,{key:a.id,label:a.name,value:a.id},{default:o(()=>[L("span",t,[j(y,{src:a.icon,size:"small"},null,8,["src"]),M(" "+S(a.name),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])}}})});export{c as _,U as __tla};
