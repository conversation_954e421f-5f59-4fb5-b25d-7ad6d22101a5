import{d as j,r as u,o as c,l as i,w as s,i as m,j as p,a as l,H as C,y as g,n as H,I,Z as L,L as N,O as P,N as S,R as Z,__tla as q}from"./index-BUSn51wb.js";import{_ as z,__tla as A}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{u as B,__tla as D}from"./index-BQq32Shw.js";let f,E=Promise.all([(()=>{try{return q}catch{}})(),(()=>{try{return A}catch{}})(),(()=>{try{return D}catch{}})()]).then(async()=>{f=j({name:"OrderUpdateRemarkForm",__name:"OrderUpdateRemarkForm",emits:["success"],setup(G,{expose:y,emit:v}){const{t:k}=H(),h=I(),t=u(!1),o=u(!1),a=u({id:void 0,remark:""}),_=u();y({open:async e=>{w(),a.value.id=e.id,a.value.remark=e.remark,t.value=!0}});const V=v,b=async()=>{o.value=!0;try{const e=l(a);await B(e),h.success(k("common.updateSuccess")),t.value=!1,V("success",!0)}finally{o.value=!1}},w=()=>{var e;a.value={id:void 0,remark:""},(e=_.value)==null||e.resetFields()};return(e,r)=>{const x=L,R=N,U=P,n=S,F=z,O=Z;return c(),i(F,{modelValue:l(t),"onUpdate:modelValue":r[2]||(r[2]=d=>g(t)?t.value=d:null),title:"\u5546\u5BB6\u5907\u6CE8",width:"45%"},{footer:s(()=>[m(n,{disabled:l(o),type:"primary",onClick:b},{default:s(()=>[p("\u786E \u5B9A")]),_:1},8,["disabled"]),m(n,{onClick:r[1]||(r[1]=d=>t.value=!1)},{default:s(()=>[p("\u53D6 \u6D88")]),_:1})]),default:s(()=>[C((c(),i(U,{ref_key:"formRef",ref:_,model:l(a),"label-width":"80px"},{default:s(()=>[m(R,{label:"\u5907\u6CE8"},{default:s(()=>[m(x,{modelValue:l(a).remark,"onUpdate:modelValue":r[0]||(r[0]=d=>l(a).remark=d),rows:3,placeholder:"\u8BF7\u8F93\u5165\u8BA2\u5355\u5907\u6CE8",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])),[[O,l(o)]])]),_:1},8,["modelValue"])}}})});export{f as _,E as __tla};
