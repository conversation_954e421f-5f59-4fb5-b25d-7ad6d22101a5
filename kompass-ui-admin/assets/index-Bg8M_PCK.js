import{d as a,o as s,l as e,__tla as o}from"./index-BUSn51wb.js";import{E as p,__tla as _}from"./el-image-BjHZRFih.js";let t,n=Promise.all([(()=>{try{return o}catch{}})(),(()=>{try{return _}catch{}})()]).then(async()=>{t=a({name:"UserCoupon",__name:"index",props:{property:{}},setup:l=>(c,m)=>{const r=p;return s(),e(r,{src:"https://shopro.sheepjs.com/admin/static/images/shop/decorate/couponCardStyle.png"})}})});export{n as __tla,t as default};
