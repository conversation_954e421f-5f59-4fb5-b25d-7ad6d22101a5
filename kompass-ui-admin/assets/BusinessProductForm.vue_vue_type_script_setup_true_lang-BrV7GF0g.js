import{d as J,r as _,f as K,at as w,C as L,o as p,c as x,H as M,a as o,l as V,w as l,i as e,F as U,k as Q,G as W,dX as y,j as $,a9 as X,dW as Z,P as z,J as A,K as S,L as Y,Z as ee,cc as ae,N as le,Q as te,O as de,s as oe,R as re,__tla as se}from"./index-BUSn51wb.js";import{_ as ue,__tla as ie}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{g as ne,__tla as ce}from"./index-CaE_tgzr.js";let k,pe=Promise.all([(()=>{try{return se}catch{}})(),(()=>{try{return ie}catch{}})(),(()=>{try{return ce}catch{}})()]).then(async()=>{k=J({__name:"BusinessProductForm",props:{products:{},disabled:{type:Boolean}},setup(C,{expose:I}){const N=C,R=_(!1),i=_([]),f=K({productId:[{required:!0,message:"\u4EA7\u54C1\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],businessPrice:[{required:!0,message:"\u5408\u540C\u4EF7\u683C\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],count:[{required:!0,message:"\u4EA7\u54C1\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),v=_([]),b=_([]);w(()=>N.products,async s=>{i.value=s},{immediate:!0}),w(()=>i.value,s=>{s&&s.length!==0&&s.forEach(u=>{u.businessPrice!=null&&u.count!=null?u.totalPrice=Z(u.businessPrice,u.count):u.totalPrice=void 0})},{deep:!0});const q=()=>{i.value.push({id:void 0,productId:void 0,productUnit:void 0,productNo:void 0,productPrice:void 0,businessPrice:void 0,count:1})};return I({validate:()=>v.value.validate()}),L(async()=>{b.value=await ne()}),(s,u)=>{const r=z,j=A,B=S,n=Y,h=ee,F=ue,g=ae,P=le,O=te,T=de,D=oe,E=re;return p(),x(U,null,[M((p(),V(T,{ref_key:"formRef",ref:v,model:o(i),rules:o(f),"label-width":"0px","inline-message":!0,disabled:s.disabled},{default:l(()=>[e(O,{data:o(i),class:"-mt-10px"},{default:l(()=>[e(r,{label:"\u5E8F\u53F7",type:"index",align:"center",width:"60"}),e(r,{label:"\u4EA7\u54C1\u540D\u79F0","min-width":"180"},{default:l(({row:a,$index:d})=>[e(n,{prop:`${d}.productId`,rules:o(f).productId,class:"mb-0px!"},{default:l(()=>[e(B,{modelValue:a.productId,"onUpdate:modelValue":t=>a.productId=t,clearable:"",filterable:"",onChange:t=>((G,m)=>{const c=b.value.find(H=>H.id===G);c&&(m.productUnit=c.unit,m.productNo=c.no,m.productPrice=c.price,m.businessPrice=c.price)})(t,a),placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1"},{default:l(()=>[(p(!0),x(U,null,Q(o(b),t=>(p(),V(j,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),e(r,{label:"\u6761\u7801","min-width":"150"},{default:l(({row:a})=>[e(n,{class:"mb-0px!"},{default:l(()=>[e(h,{disabled:"",modelValue:a.productNo,"onUpdate:modelValue":d=>a.productNo=d},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),e(r,{label:"\u5355\u4F4D","min-width":"80"},{default:l(({row:a})=>[e(F,{type:o(W).CRM_PRODUCT_UNIT,value:a.productUnit},null,8,["type","value"])]),_:1}),e(r,{label:"\u4EF7\u683C\uFF08\u5143\uFF09","min-width":"120"},{default:l(({row:a})=>[e(n,{class:"mb-0px!"},{default:l(()=>[e(h,{disabled:"",modelValue:a.productPrice,"onUpdate:modelValue":d=>a.productPrice=d,formatter:o(y)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1024)]),_:1}),e(r,{label:"\u552E\u4EF7\uFF08\u5143\uFF09",fixed:"right","min-width":"140"},{default:l(({row:a,$index:d})=>[e(n,{prop:`${d}.businessPrice`,class:"mb-0px!"},{default:l(()=>[e(g,{modelValue:a.businessPrice,"onUpdate:modelValue":t=>a.businessPrice=t,"controls-position":"right",min:.001,precision:2,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(r,{label:"\u6570\u91CF",prop:"count",fixed:"right","min-width":"120"},{default:l(({row:a,$index:d})=>[e(n,{prop:`${d}.count`,rules:o(f).count,class:"mb-0px!"},{default:l(()=>[e(g,{modelValue:a.count,"onUpdate:modelValue":t=>a.count=t,"controls-position":"right",min:.001,precision:3,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(r,{label:"\u5408\u8BA1",prop:"totalPrice",fixed:"right","min-width":"140"},{default:l(({row:a,$index:d})=>[e(n,{prop:`${d}.totalPrice`,class:"mb-0px!"},{default:l(()=>[e(h,{disabled:"",modelValue:a.totalPrice,"onUpdate:modelValue":t=>a.totalPrice=t,formatter:o(y)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1032,["prop"])]),_:1}),e(r,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"60"},{default:l(({$index:a})=>[e(P,{onClick:d=>{return t=a,void i.value.splice(t,1);var t},link:""},{default:l(()=>[$("\u2014")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["model","rules","disabled"])),[[E,o(R)]]),s.disabled?X("",!0):(p(),V(D,{key:0,justify:"center",class:"mt-3"},{default:l(()=>[e(P,{onClick:q,round:""},{default:l(()=>[$("+ \u6DFB\u52A0\u4EA7\u54C1")]),_:1})]),_:1}))],64)}}})});export{k as _,pe as __tla};
