import{by as t,__tla as g}from"./index-BUSn51wb.js";let c,s,e,r,l,n,i,o,u,m,d,y,p,w,b=Promise.all([(()=>{try{return g}catch{}})()]).then(async()=>{m=async a=>await t.get({url:"/crm/contact/page",params:a}),i=async a=>await t.get({url:"/crm/contact/page-by-customer",params:a}),r=async a=>await t.get({url:"/crm/contact/page-by-business",params:a}),c=async a=>await t.get({url:"/crm/contact/get?id="+a}),e=async a=>await t.post({url:"/crm/contact/create",data:a}),w=async a=>await t.put({url:"/crm/contact/update",data:a}),d=async a=>await t.delete({url:"/crm/contact/delete?id="+a}),y=async a=>await t.download({url:"/crm/contact/export-excel",params:a}),s=async()=>await t.get({url:"/crm/contact/simple-all-list"}),o=async a=>await t.post({url:"/crm/contact/create-business-list",data:a}),l=async a=>await t.post({url:"/crm/contact/create-business-list2",data:a}),u=async a=>await t.delete({url:"/crm/contact/delete-business-list",data:a}),n=async a=>await t.delete({url:"/crm/contact/delete-business-list2",data:a}),p=async a=>await t.put({url:"/crm/contact/transfer",data:a})});export{b as __tla,c as a,s as b,e as c,r as d,l as e,n as f,i as g,o as h,u as i,m as j,d as k,y as l,p as t,w as u};
