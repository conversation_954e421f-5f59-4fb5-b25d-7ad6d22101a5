import{d as S,b6 as q,cR as al,bq as B,bf as F,H as W,h as N,cS as rl,bd as j,be as nl,a as s,o as a,c as y,g as v,F as g,k as D,l as A,i as H,bg as O,bI as il,bY as ol,cE as cl,c8 as dl,b as pl,a0 as w,aV as T,j as V,t as Y,a9 as ul,cT as bl,bh as yl,bk as hl,__tla as vl}from"./index-BUSn51wb.js";let P,G,gl=Promise.all([(()=>{try{return vl}catch{}})()]).then(async()=>{const I=Symbol("elDescriptions");var k=S({name:"ElDescriptionsCell",props:{cell:{type:Object},tag:{type:String,default:"td"},type:{type:String}},setup:()=>({descriptions:q(I,{})}),render(){var _,o,t,$,r,c,E;const i=al(this.cell),l=(((_=this.cell)==null?void 0:_.dirs)||[]).map(Z=>{const{dir:ll,arg:tl,modifiers:el,value:sl}=Z;return[ll,sl,tl,el]}),{border:p,direction:n}=this.descriptions,e=n==="vertical",m=(($=(t=(o=this.cell)==null?void 0:o.children)==null?void 0:t.label)==null?void 0:$.call(t))||i.label,u=(E=(c=(r=this.cell)==null?void 0:r.children)==null?void 0:c.default)==null?void 0:E.call(c),b=i.span,f=i.align?`is-${i.align}`:"",h=i.labelAlign?`is-${i.labelAlign}`:f,x=i.className,R=i.labelClassName,C={width:B(i.width),minWidth:B(i.minWidth)},d=F("descriptions");switch(this.type){case"label":return W(N(this.tag,{style:C,class:[d.e("cell"),d.e("label"),d.is("bordered-label",p),d.is("vertical-label",e),h,R],colSpan:e?b:1},m),l);case"content":return W(N(this.tag,{style:C,class:[d.e("cell"),d.e("content"),d.is("bordered-content",p),d.is("vertical-content",e),f,x],colSpan:e?b:2*b-1},u),l);default:return W(N("td",{style:C,class:[d.e("cell"),f],colSpan:b},[rl(m)?void 0:N("span",{class:[d.e("label"),R]},m),N("span",{class:[d.e("content"),x]},u)]),l)}}});const J=j({row:{type:nl(Array),default:()=>[]}}),K={key:1},L=S({name:"ElDescriptionsRow"});var M=O(S({...L,props:J,setup(_){const o=q(I,{});return(t,$)=>s(o).direction==="vertical"?(a(),y(g,{key:0},[v("tr",null,[(a(!0),y(g,null,D(t.row,(r,c)=>(a(),A(s(k),{key:`tr1-${c}`,cell:r,tag:"th",type:"label"},null,8,["cell"]))),128))]),v("tr",null,[(a(!0),y(g,null,D(t.row,(r,c)=>(a(),A(s(k),{key:`tr2-${c}`,cell:r,tag:"td",type:"content"},null,8,["cell"]))),128))])],64)):(a(),y("tr",K,[(a(!0),y(g,null,D(t.row,(r,c)=>(a(),y(g,{key:`tr3-${c}`},[s(o).border?(a(),y(g,{key:0},[H(s(k),{cell:r,tag:"td",type:"label"},null,8,["cell"]),H(s(k),{cell:r,tag:"td",type:"content"},null,8,["cell"])],64)):(a(),A(s(k),{key:1,cell:r,tag:"td",type:"both"},null,8,["cell"]))],64))),128))]))}}),[["__file","descriptions-row.vue"]]);const Q=j({border:{type:Boolean,default:!1},column:{type:Number,default:3},direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},size:il,title:{type:String,default:""},extra:{type:String,default:""}}),U=S({name:"ElDescriptions"});var X=O(S({...U,props:Q,setup(_){const o=_,t=F("descriptions"),$=ol(),r=cl();dl(I,o);const c=pl(()=>[t.b(),t.m($.value)]),E=(l,p,n,e=!1)=>(l.props||(l.props={}),p>n&&(l.props.span=n),e&&(l.props.span=p),l),i=()=>{if(!r.default)return[];const l=bl(r.default()).filter(u=>{var b;return((b=u==null?void 0:u.type)==null?void 0:b.name)==="ElDescriptionsItem"}),p=[];let n=[],e=o.column,m=0;return l.forEach((u,b)=>{var f;const h=((f=u.props)==null?void 0:f.span)||1;if(b<l.length-1&&(m+=h>e?e:h),b===l.length-1){const x=o.column-m%o.column;return n.push(E(u,x,e,!0)),void p.push(n)}h<e?(e-=h,n.push(u)):(n.push(E(u,h,e)),p.push(n),e=o.column,n=[])}),p};return(l,p)=>(a(),y("div",{class:w(s(c))},[l.title||l.extra||l.$slots.title||l.$slots.extra?(a(),y("div",{key:0,class:w(s(t).e("header"))},[v("div",{class:w(s(t).e("title"))},[T(l.$slots,"title",{},()=>[V(Y(l.title),1)])],2),v("div",{class:w(s(t).e("extra"))},[T(l.$slots,"extra",{},()=>[V(Y(l.extra),1)])],2)],2)):ul("v-if",!0),v("div",{class:w(s(t).e("body"))},[v("table",{class:w([s(t).e("table"),s(t).is("bordered",l.border)])},[v("tbody",null,[(a(!0),y(g,null,D(i(),(n,e)=>(a(),A(M,{key:e,row:n},null,8,["row"]))),128))])],2)],2)],2))}}),[["__file","description.vue"]]);let z;z=S({name:"ElDescriptionsItem",props:j({label:{type:String,default:""},span:{type:Number,default:1},width:{type:[String,Number],default:""},minWidth:{type:[String,Number],default:""},align:{type:String,default:"left"},labelAlign:{type:String,default:""},className:{type:String,default:""},labelClassName:{type:String,default:""}})}),G=yl(X,{DescriptionsItem:z}),P=hl(z)});export{P as E,gl as __tla,G as a};
