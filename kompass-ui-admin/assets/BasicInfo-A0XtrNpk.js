import{_ as t,__tla as _}from"./BasicInfo.vue_vue_type_script_setup_true_lang-DL00JDB5.js";import{__tla as r}from"./XButton-BjahQbul.js";import{__tla as a}from"./index-BUSn51wb.js";import{__tla as l}from"./Form-DJa9ov9B.js";import{__tla as o}from"./el-virtual-list-4L-8WDNg.js";import{__tla as c}from"./el-tree-select-CBuha0HW.js";import{__tla as m}from"./el-time-select-C-_NEIfl.js";import{__tla as e}from"./InputPassword-RefetKoR.js";import{__tla as s}from"./profile-BQCm_-PE.js";let n=Promise.all([(()=>{try{return _}catch{}})(),(()=>{try{return r}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return c}catch{}})(),(()=>{try{return m}catch{}})(),(()=>{try{return e}catch{}})(),(()=>{try{return s}catch{}})()]).then(async()=>{});export{n as __tla,t as default};
