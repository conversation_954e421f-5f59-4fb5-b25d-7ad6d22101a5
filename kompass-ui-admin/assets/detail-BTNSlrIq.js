import{_ as b,__tla as D}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{d as M,p as E,S as T,r as n,C as j,o as A,l as P,w as t,i as e,a,G as g,j as _,t as s,__tla as w}from"./index-BUSn51wb.js";import{E as x,a as B,__tla as L}from"./el-descriptions-item-dD3qa0ub.js";import{_ as O,__tla as q}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{f as m,__tla as C}from"./formatTime-DWdBpgsM.js";import{g as G,__tla as S}from"./index-BIIjyUuV.js";import{__tla as V}from"./el-card-CJbXGyyg.js";import"./color-BN7ZL7BD.js";let i,k=Promise.all([(()=>{try{return D}catch{}})(),(()=>{try{return w}catch{}})(),(()=>{try{return L}catch{}})(),(()=>{try{return q}catch{}})(),(()=>{try{return C}catch{}})(),(()=>{try{return S}catch{}})(),(()=>{try{return V}catch{}})()]).then(async()=>{i=M({name:"BpmOALeaveDetail",__name:"detail",props:{id:E.number.def(void 0)},setup(c,{expose:p}){const{query:f}=T(),d=c,u=n(!1),r=n({}),y=f.id,o=async()=>{u.value=!0;try{r.value=await G(d.id||y)}finally{u.value=!1}};return p({open:o}),j(()=>{o()}),(z,F)=>{const Y=O,l=x,h=B,v=b;return A(),P(v,null,{default:t(()=>[e(h,{column:1,border:""},{default:t(()=>[e(l,{label:"\u8BF7\u5047\u7C7B\u578B"},{default:t(()=>[e(Y,{type:a(g).BPM_OA_LEAVE_TYPE,value:a(r).type},null,8,["type","value"])]),_:1}),e(l,{label:"\u5F00\u59CB\u65F6\u95F4"},{default:t(()=>[_(s(a(m)(a(r).startTime,"YYYY-MM-DD")),1)]),_:1}),e(l,{label:"\u7ED3\u675F\u65F6\u95F4"},{default:t(()=>[_(s(a(m)(a(r).endTime,"YYYY-MM-DD")),1)]),_:1}),e(l,{label:"\u539F\u56E0"},{default:t(()=>[_(s(a(r).reason),1)]),_:1})]),_:1})]),_:1})}}})});export{k as __tla,i as default};
