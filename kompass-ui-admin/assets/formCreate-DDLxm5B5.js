import{y as p,__tla as i}from"./index-BUSn51wb.js";let s,u,n,o,c=Promise.all([(()=>{try{return i}catch{}})()]).then(async()=>{let l;n=e=>JSON.stringify(e.value.getOption()),s=e=>{const t=e.value.getRule(),a=[];return t.forEach(r=>{a.push(JSON.stringify(r))}),a},l=e=>{const t=[];return e.forEach(a=>{t.push(JSON.parse(a))}),t},o=(e,t,a)=>{e.value.setOption(JSON.parse(t)),e.value.setRule(l(a))},u=(e,t,a,r)=>{p(e)&&(e=e.value),e.option=JSON.parse(t),e.rule=l(a),r&&(e.value=r)}});export{c as __tla,s as a,u as b,n as e,o as s};
