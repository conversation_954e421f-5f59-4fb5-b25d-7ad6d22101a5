import{_ as I,__tla as P}from"./ComponentContainerProperty-U-5gd_f0.js";import{d as j,o as f,l as k,w as r,i as e,a as t,j as u,c as B,F as D,H as E,a8 as F,a9 as H,cl as L,L as M,_ as N,aM as O,aN as T,an as Q,cf as Z,ai as q,am as G,Z as J,O as K,__tla as R}from"./index-BUSn51wb.js";import{_ as X,__tla as Y}from"./index.vue_vue_type_script_setup_true_lang-D01sdz8R.js";import{_ as $,__tla as C}from"./index-wRBY3mxf.js";import{E as ee,__tla as le}from"./el-card-CJbXGyyg.js";import{u as te,__tla as ae}from"./util-Dyp86Gv2.js";import{__tla as oe}from"./index-11u3nuTi.js";import"./color-BN7ZL7BD.js";import{__tla as re}from"./AppLinkSelectDialog.vue_vue_type_script_setup_true_lang-CHi5jpXX.js";import{__tla as de}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as me}from"./ProductCategorySelect.vue_vue_type_script_setup_true_lang-CJItHfu_.js";import{__tla as _e}from"./el-tree-select-CBuha0HW.js";import"./tree-BMa075Oj.js";import{__tla as ue}from"./category-WzWM3ODe.js";import{__tla as pe}from"./Qrcode-CP7wmJi0.js";import{__tla as ie}from"./el-text-CIwNlU-U.js";import{__tla as ne}from"./IFrame.vue_vue_type_script_setup_true_lang-silYuU_p.js";import{__tla as se}from"./vuedraggable.umd-BTL7hPHv.js";import{__tla as ce}from"./el-collapse-item-B_QvnH_b.js";let V,he=Promise.all([(()=>{try{return P}catch{}})(),(()=>{try{return R}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return C}catch{}})(),(()=>{try{return le}catch{}})(),(()=>{try{return ae}catch{}})(),(()=>{try{return oe}catch{}})(),(()=>{try{return re}catch{}})(),(()=>{try{return de}catch{}})(),(()=>{try{return me}catch{}})(),(()=>{try{return _e}catch{}})(),(()=>{try{return ue}catch{}})(),(()=>{try{return pe}catch{}})(),(()=>{try{return ie}catch{}})(),(()=>{try{return ne}catch{}})(),(()=>{try{return se}catch{}})(),(()=>{try{return ce}catch{}})()]).then(async()=>{V=j({name:"TitleBarProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(y,{emit:b}){const g=y,w=b,{formData:l}=te(g.modelValue,w),x={};return(fe,a)=>{const U=L,d=M,i=N,n=O,s=T,c=Q,m=ee,h=$,_=Z,z=q,p=G,S=J,W=X,v=K,A=I;return f(),k(A,{modelValue:t(l).style,"onUpdate:modelValue":a[14]||(a[14]=o=>t(l).style=o)},{default:r(()=>[e(v,{"label-width":"85px",model:t(l),rules:x},{default:r(()=>[e(m,{header:"\u98CE\u683C",class:"property-group",shadow:"never"},{default:r(()=>[e(d,{label:"\u80CC\u666F\u56FE\u7247",prop:"bgImgUrl"},{default:r(()=>[e(U,{modelValue:t(l).bgImgUrl,"onUpdate:modelValue":a[0]||(a[0]=o=>t(l).bgImgUrl=o),width:"100%",height:"40px"},{tip:r(()=>[u("\u5EFA\u8BAE\u5C3A\u5BF8 750*80")]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"\u6807\u9898\u4F4D\u7F6E",prop:"textAlign"},{default:r(()=>[e(c,{modelValue:t(l).textAlign,"onUpdate:modelValue":a[1]||(a[1]=o=>t(l).textAlign=o)},{default:r(()=>[e(s,{content:"\u5C45\u5DE6",placement:"top"},{default:r(()=>[e(n,{label:"left"},{default:r(()=>[e(i,{icon:"ant-design:align-left-outlined"})]),_:1})]),_:1}),e(s,{content:"\u5C45\u4E2D",placement:"top"},{default:r(()=>[e(n,{label:"center"},{default:r(()=>[e(i,{icon:"ant-design:align-center-outlined"})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(m,{header:"\u4E3B\u6807\u9898",class:"property-group",shadow:"never"},{default:r(()=>[e(d,{label:"\u6587\u5B57",prop:"title","label-width":"40px"},{default:r(()=>[e(h,{modelValue:t(l).title,"onUpdate:modelValue":a[2]||(a[2]=o=>t(l).title=o),color:t(l).titleColor,"onUpdate:color":a[3]||(a[3]=o=>t(l).titleColor=o),"show-word-limit":"",maxlength:"20"},null,8,["modelValue","color"])]),_:1}),e(d,{label:"\u5927\u5C0F",prop:"titleSize","label-width":"40px"},{default:r(()=>[e(_,{modelValue:t(l).titleSize,"onUpdate:modelValue":a[4]||(a[4]=o=>t(l).titleSize=o),max:60,min:10,"show-input":"","input-size":"small"},null,8,["modelValue"])]),_:1}),e(d,{label:"\u7C97\u7EC6",prop:"titleWeight","label-width":"40px"},{default:r(()=>[e(_,{modelValue:t(l).titleWeight,"onUpdate:modelValue":a[5]||(a[5]=o=>t(l).titleWeight=o),min:100,max:900,step:100,"show-input":"","input-size":"small"},null,8,["modelValue"])]),_:1})]),_:1}),e(m,{header:"\u526F\u6807\u9898",class:"property-group",shadow:"never"},{default:r(()=>[e(d,{label:"\u6587\u5B57",prop:"description","label-width":"40px"},{default:r(()=>[e(h,{modelValue:t(l).description,"onUpdate:modelValue":a[6]||(a[6]=o=>t(l).description=o),color:t(l).descriptionColor,"onUpdate:color":a[7]||(a[7]=o=>t(l).descriptionColor=o),"show-word-limit":"",maxlength:"50"},null,8,["modelValue","color"])]),_:1}),e(d,{label:"\u5927\u5C0F",prop:"descriptionSize","label-width":"40px"},{default:r(()=>[e(_,{modelValue:t(l).descriptionSize,"onUpdate:modelValue":a[8]||(a[8]=o=>t(l).descriptionSize=o),max:60,min:10,"show-input":"","input-size":"small"},null,8,["modelValue"])]),_:1}),e(d,{label:"\u7C97\u7EC6",prop:"descriptionWeight","label-width":"40px"},{default:r(()=>[e(_,{modelValue:t(l).descriptionWeight,"onUpdate:modelValue":a[9]||(a[9]=o=>t(l).descriptionWeight=o),min:100,max:900,step:100,"show-input":"","input-size":"small"},null,8,["modelValue"])]),_:1})]),_:1}),e(m,{header:"\u67E5\u770B\u66F4\u591A",class:"property-group",shadow:"never"},{default:r(()=>[e(d,{label:"\u662F\u5426\u663E\u793A",prop:"more.show"},{default:r(()=>[e(z,{modelValue:t(l).more.show,"onUpdate:modelValue":a[10]||(a[10]=o=>t(l).more.show=o)},null,8,["modelValue"])]),_:1}),t(l).more.show?(f(),B(D,{key:0},[e(d,{label:"\u6837\u5F0F",prop:"more.type"},{default:r(()=>[e(c,{modelValue:t(l).more.type,"onUpdate:modelValue":a[11]||(a[11]=o=>t(l).more.type=o)},{default:r(()=>[e(p,{label:"text"},{default:r(()=>[u("\u6587\u5B57")]),_:1}),e(p,{label:"icon"},{default:r(()=>[u("\u56FE\u6807")]),_:1}),e(p,{label:"all"},{default:r(()=>[u("\u6587\u5B57+\u56FE\u6807")]),_:1})]),_:1},8,["modelValue"])]),_:1}),E(e(d,{label:"\u66F4\u591A\u6587\u5B57",prop:"more.text"},{default:r(()=>[e(S,{modelValue:t(l).more.text,"onUpdate:modelValue":a[12]||(a[12]=o=>t(l).more.text=o)},null,8,["modelValue"])]),_:1},512),[[F,t(l).more.type!=="icon"]]),e(d,{label:"\u8DF3\u8F6C\u94FE\u63A5",prop:"more.url"},{default:r(()=>[e(W,{modelValue:t(l).more.url,"onUpdate:modelValue":a[13]||(a[13]=o=>t(l).more.url=o)},null,8,["modelValue"])]),_:1})],64)):H("",!0)]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}})});export{he as __tla,V as default};
