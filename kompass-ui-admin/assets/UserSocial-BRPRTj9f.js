import{_ as t,__tla as r}from"./UserSocial.vue_vue_type_script_setup_true_lang-BdHpRd4H.js";import{__tla as _}from"./index-BUSn51wb.js";import{__tla as a}from"./XTextButton-DMuYh5Ak.js";import"./constants-A8BI3pz7.js";import{__tla as l}from"./profile-BQCm_-PE.js";let o=Promise.all([(()=>{try{return r}catch{}})(),(()=>{try{return _}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})()]).then(async()=>{});export{o as __tla,t as default};
