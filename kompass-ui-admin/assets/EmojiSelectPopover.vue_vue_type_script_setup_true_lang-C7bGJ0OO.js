import{d as y,b,o as r,l as h,w as t,i as o,g as i,c,F as w,k,a as v,av as C,_ as E,b2 as P,ap as S,__tla as z}from"./index-BUSn51wb.js";import{u as F,__tla as L}from"./emoji-DGuwlnSb.js";let m,q=Promise.all([(()=>{try{return z}catch{}})(),(()=>{try{return L}catch{}})()]).then(async()=>{let l,a,s;l={class:"ml-2 flex flex-wrap px-2"},a=["title","onClick"],s=["src"],m=y({name:"EmojiSelectPopover",__name:"EmojiSelectPopover",emits:["select-emoji"],setup(A,{emit:n}){const{getEmojiList:p}=F(),_=b(()=>p()),u=n;return(B,D)=>{const f=E,x=P,d=S;return r(),h(d,{width:500,placement:"top",trigger:"click"},{reference:t(()=>[o(f,{size:30,class:"ml-10px cursor-pointer",icon:"twemoji:grinning-face"})]),default:t(()=>[o(x,{height:"300px"},{default:t(()=>[i("ul",l,[(r(!0),c(w,null,k(v(_),(e,g)=>(r(),c("li",{key:g,style:C({borderColor:"var(--el-color-primary)",color:"var(--el-color-primary)"}),title:e.name,class:"icon-item mr-2 mt-1 w-1/10 flex cursor-pointer items-center justify-center border border-solid p-2",onClick:G=>(j=>{u("select-emoji",j)})(e)},[i("img",{src:e.url,class:"w-24px h-24px"},null,8,s)],12,a))),128))])]),_:1})]),_:1})}}})});export{m as _,q as __tla};
