import{d as z,n as B,I as D,r as i,f as Q,o as u,l as d,w as s,i as o,a,j as T,H as W,c as n,F as p,k as c,V as h,G as f,t as L,y as Y,Z as $,L as ee,am as ae,an as le,J as re,K as oe,ai as se,ca as ue,M as te,O as de,N as me,R as ie,__tla as ne}from"./index-BUSn51wb.js";import{_ as pe,__tla as ce}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{C as w,__tla as ve}from"./index-D8hnRknQ.js";import{g as ge,__tla as _e}from"./index-BYXzDB8j.js";import{g as be,__tla as Ve}from"./index-CyP7ZSdX.js";let R,he=Promise.all([(()=>{try{return ne}catch{}})(),(()=>{try{return ce}catch{}})(),(()=>{try{return ve}catch{}})(),(()=>{try{return _e}catch{}})(),(()=>{try{return Ve}catch{}})()]).then(async()=>{R=z({name:"CustomerForm",__name:"CustomerRegisterForm",emits:["success"],setup(fe,{expose:q,emit:A}){const{t:I}=B(),x=D(),U=i([]),E=i([]),v=i(!1),N=i(""),g=i(!1),y=i(""),r=i({customerId:void 0,customerName:void 0,customerSex:void 0,relationship:void 0,customerPhone:void 0,areaId:void 0,openId:void 0,serviceStatus:void 0,sourceChannel:void 0,serviceTags:[],operationTags:[],levelTags:[],registerTime:void 0,lastLoginTime:void 0,headOperateUserId:void 0,headMarketUserId:void 0,customerRemark:void 0}),O=Q({customerName:[{required:!0,message:"\u5BB6\u957F\u59D3\u540D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],customerSex:[{required:!0,message:"\u5BB6\u957F\u6027\u522B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],relationship:[{required:!0,message:"\u5B69\u5B50\u5173\u7CFB\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],customerPhone:[{required:!0,message:"\u624B\u673A\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],openId:[{required:!0,message:"openId\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],serviceStatus:[{required:!0,message:"\u670D\u52A1\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],sourceChannel:[{required:!0,message:"\u83B7\u5BA2\u6E20\u9053\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],serviceTags:[{required:!0,message:"\u670D\u52A1\u6807\u7B7E\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],operationTags:[{required:!0,message:"\u8FD0\u8425\u6807\u7B7E\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],levelTags:[{required:!0,message:"\u5206\u7EA7\u6807\u7B7E\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],headOperateUserId:[{required:!0,message:"\u8FD0\u8425\u8D1F\u8D23\u4EBA\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],headMarketUserId:[{required:!0,message:"\u5E02\u573A\u8D1F\u8D23\u4EBA\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],customerRemark:[{required:!0,message:"\u5BB6\u957F\u5907\u6CE8\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),S=i();q({open:async(m,l)=>{if(v.value=!0,N.value=I("action."+m),y.value=m,F(),l){g.value=!0;try{r.value=await w.getCustomer(l)}finally{g.value=!1}}E.value=await be(),U.value=await ge()}});const P=A,M=async()=>{await S.value.validate(),g.value=!0;try{const m=r.value;y.value==="create"?(await w.createCustomer(m),x.success(I("common.createSuccess"))):(await w.updateCustomer(m),x.success(I("common.updateSuccess"))),v.value=!1,P("success")}finally{g.value=!1}},F=()=>{var m;r.value={customerId:void 0,customerName:void 0,customerSex:void 0,relationship:void 0,customerPhone:void 0,openId:void 0,serviceStatus:void 0,sourceChannel:void 0,serviceTags:[],operationTags:[],levelTags:[],registerTime:void 0,lastLoginTime:void 0,headOperateUserId:void 0,headMarketUserId:void 0,customerRemark:void 0},(m=S.value)==null||m.resetFields()};return(m,l)=>{const _=$,t=ee,G=ae,H=le,b=re,V=oe,J=se,j=ue,k=te,K=de,C=me,X=pe,Z=ie;return u(),d(X,{title:"\u6CE8\u518C",modelValue:a(v),"onUpdate:modelValue":l[17]||(l[17]=e=>Y(v)?v.value=e:null),width:"1000px"},{footer:s(()=>[o(C,{onClick:M,type:"primary",disabled:a(g)},{default:s(()=>[T("\u786E \u5B9A")]),_:1},8,["disabled"]),o(C,{onClick:l[16]||(l[16]=e=>v.value=!1)},{default:s(()=>[T("\u53D6 \u6D88")]),_:1})]),default:s(()=>[W((u(),d(K,{ref_key:"formRef",ref:S,model:a(r),rules:a(O),"label-width":"100px",inline:""},{default:s(()=>[o(t,{label:"\u5BB6\u957F\u59D3\u540D",prop:"customerName",class:"!w-250px"},{default:s(()=>[o(_,{modelValue:a(r).customerName,"onUpdate:modelValue":l[0]||(l[0]=e=>a(r).customerName=e),placeholder:"\u8BF7\u8F93\u5165\u5BB6\u957F\u59D3\u540D"},null,8,["modelValue"])]),_:1}),o(t,{label:"\u5BB6\u957F\u6027\u522B",prop:"customerSex",class:"!w-300px"},{default:s(()=>[o(H,{modelValue:a(r).customerSex,"onUpdate:modelValue":l[1]||(l[1]=e=>a(r).customerSex=e)},{default:s(()=>[(u(!0),n(p,null,c(a(h)(a(f).ALS_SEX),e=>(u(),d(G,{key:e.value,label:e.value},{default:s(()=>[T(L(e.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(t,{label:"\u5B69\u5B50\u5173\u7CFB",prop:"relationship",class:"!w-250px"},{default:s(()=>[o(_,{modelValue:a(r).relationship,"onUpdate:modelValue":l[2]||(l[2]=e=>a(r).relationship=e),placeholder:"\u8BF7\u8F93\u5165\u5B69\u5B50\u5173\u7CFB"},null,8,["modelValue"])]),_:1}),o(t,{label:"\u624B\u673A\u53F7",prop:"customerPhone",class:"!w-250px"},{default:s(()=>[o(_,{modelValue:a(r).customerPhone,"onUpdate:modelValue":l[3]||(l[3]=e=>a(r).customerPhone=e),placeholder:"\u8BF7\u8F93\u5165\u624B\u673A\u53F7"},null,8,["modelValue"])]),_:1}),o(t,{label:"areaId",prop:"areaId",class:"!w-300px"},{default:s(()=>[o(_,{modelValue:a(r).areaId,"onUpdate:modelValue":l[4]||(l[4]=e=>a(r).areaId=e),placeholder:"\u8BF7\u8F93\u5165openId"},null,8,["modelValue"])]),_:1}),o(t,{label:"openId",prop:"openId",class:"!w-300px"},{default:s(()=>[o(_,{modelValue:a(r).openId,"onUpdate:modelValue":l[5]||(l[5]=e=>a(r).openId=e),placeholder:"\u8BF7\u8F93\u5165openId"},null,8,["modelValue"])]),_:1}),o(t,{label:"\u670D\u52A1\u72B6\u6001",prop:"serviceStatus",class:"!w-250px"},{default:s(()=>[o(V,{modelValue:a(r).serviceStatus,"onUpdate:modelValue":l[6]||(l[6]=e=>a(r).serviceStatus=e),placeholder:"\u8BF7\u9009\u62E9\u670D\u52A1\u72B6\u6001"},{default:s(()=>[(u(!0),n(p,null,c(a(h)(a(f).ALS_SERVICE_STATUS),e=>(u(),d(b,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(t,{label:"\u83B7\u5BA2\u6E20\u9053",prop:"sourceChannel",class:"!w-250px"},{default:s(()=>[o(V,{modelValue:a(r).sourceChannel,"onUpdate:modelValue":l[7]||(l[7]=e=>a(r).sourceChannel=e),placeholder:"\u8BF7\u9009\u62E9\u83B7\u5BA2\u6E20\u9053"},{default:s(()=>[(u(!0),n(p,null,c(a(h)(a(f).ALS_SOURCE_CHANNEL),e=>(u(),d(b,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(t,{label:"\u670D\u52A1\u6807\u7B7E",prop:"serviceTags",class:"!w-621px"},{default:s(()=>[o(V,{modelValue:a(r).serviceTags,"onUpdate:modelValue":l[8]||(l[8]=e=>a(r).serviceTags=e),multiple:"",clearable:""},{default:s(()=>[(u(!0),n(p,null,c(a(h)(a(f).ALS_SERVICE_TAGS),e=>(u(),d(b,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(t,{label:"\u8FD0\u8425\u6807\u7B7E",prop:"operationTags",class:"!w-100%"},{default:s(()=>[o(V,{modelValue:a(r).operationTags,"onUpdate:modelValue":l[9]||(l[9]=e=>a(r).operationTags=e),multiple:"",clearable:""},{default:s(()=>[(u(!0),n(p,null,c(a(h)(a(f).ALS_OPERATION_TAGS),e=>(u(),d(b,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(t,{label:"\u5206\u7EA7\u6807\u7B7E",prop:"levelTags",class:"!w-100%"},{default:s(()=>[o(j,{modelValue:a(r).levelTags,"onUpdate:modelValue":l[10]||(l[10]=e=>a(r).levelTags=e)},{default:s(()=>[(u(!0),n(p,null,c(a(h)(a(f).ALS_LEVEL_TAGS),e=>(u(),d(J,{key:e.value,label:e.value},{default:s(()=>[T(L(e.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(t,{label:"\u6CE8\u518C\u65F6\u95F4",prop:"registerTime"},{default:s(()=>[o(k,{modelValue:a(r).registerTime,"onUpdate:modelValue":l[11]||(l[11]=e=>a(r).registerTime=e),type:"datetime","value-format":"x",class:"!w-200px"},null,8,["modelValue"])]),_:1}),o(t,{label:"\u6700\u8FD1\u767B\u5F55\u65F6\u95F4",prop:"lastLoginTime"},{default:s(()=>[o(k,{modelValue:a(r).lastLoginTime,"onUpdate:modelValue":l[12]||(l[12]=e=>a(r).lastLoginTime=e),type:"datetime","value-format":"x",class:"!w-200px"},null,8,["modelValue"])]),_:1}),o(t,{label:"\u8FD0\u8425\u8D1F\u8D23\u4EBA",prop:"headOperateUserId"},{default:s(()=>[o(V,{modelValue:a(r).headOperateUserId,"onUpdate:modelValue":l[13]||(l[13]=e=>a(r).headOperateUserId=e),clearable:"",filterable:"",class:"!w-200px",placeholder:"\u8BF7\u8F93\u5165\u5F53\u524D\u8D1F\u8D23\u4EBA"},{default:s(()=>[(u(!0),n(p,null,c(a(U),e=>(u(),d(b,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(t,{label:"\u5E02\u573A\u8D1F\u8D23\u4EBA",prop:"headMarketUserId"},{default:s(()=>[o(V,{modelValue:a(r).headMarketUserId,"onUpdate:modelValue":l[14]||(l[14]=e=>a(r).headMarketUserId=e),clearable:"",filterable:"",class:"!w-200px",placeholder:"\u8BF7\u8F93\u5165\u5F53\u524D\u8D1F\u8D23\u4EBA"},{default:s(()=>[(u(!0),n(p,null,c(a(U),e=>(u(),d(b,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(t,{label:"\u5BB6\u957F\u5907\u6CE8",prop:"customerRemark",class:"!w-100%"},{default:s(()=>[o(_,{modelValue:a(r).customerRemark,"onUpdate:modelValue":l[15]||(l[15]=e=>a(r).customerRemark=e),type:"textarea",rows:4,maxlength:"500","show-word-limit":"",placeholder:"\u8BF7\u8F93\u5165\u5BB6\u957F\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[Z,a(g)]])]),_:1},8,["modelValue"])}}})});export{R as _,he as __tla};
