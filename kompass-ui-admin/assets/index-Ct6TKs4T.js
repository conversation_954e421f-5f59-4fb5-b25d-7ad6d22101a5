import{d as H,I as J,n as Q,r as _,f as Z,C as W,T as X,o as n,c as U,i as a,w as l,a as t,U as A,F as P,k as Y,D as $,G as v,l as i,j as m,H as y,Z as aa,L as ea,J as la,K as ta,_ as ra,N as oa,O as sa,P as na,Q as pa,R as ua,__tla as ca}from"./index-BUSn51wb.js";import{_ as _a,__tla as ia}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as ma,__tla as da}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{E as fa,__tla as ya}from"./el-image-BjHZRFih.js";import{_ as ga,__tla as ba}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as ha,C as F,__tla as va}from"./ChatRoleForm.vue_vue_type_script_setup_true_lang-DAKVMBtZ.js";import{__tla as wa}from"./index-Cch5e1V0.js";import"./color-BN7ZL7BD.js";import{__tla as Sa}from"./el-card-CJbXGyyg.js";import{__tla as Ca}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import"./constants-A8BI3pz7.js";import{__tla as ka}from"./index-DrcFYyNA.js";let I,Na=Promise.all([(()=>{try{return ca}catch{}})(),(()=>{try{return ia}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return ya}catch{}})(),(()=>{try{return ba}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return wa}catch{}})(),(()=>{try{return Sa}catch{}})(),(()=>{try{return Ca}catch{}})(),(()=>{try{return ka}catch{}})()]).then(async()=>{I=H({name:"AiChatRole",__name:"index",setup(xa){const w=J(),{t:T}=Q(),g=_(!0),S=_([]),C=_(0),o=Z({pageNo:1,pageSize:10,name:void 0,category:void 0,publicStatus:!0}),k=_(),p=async()=>{g.value=!0;try{const u=await F.getChatRolePage(o);S.value=u.list,C.value=u.total}finally{g.value=!1}},d=()=>{o.pageNo=1,p()},z=()=>{k.value.resetFields(),d()},N=_(),x=(u,r)=>{N.value.open(u,r)};return W(()=>{p()}),(u,r)=>{const R=aa,f=ea,E=la,G=ta,b=ra,c=oa,K=sa,V=ga,s=na,L=fa,O=ma,M=pa,B=_a,h=X("hasPermi"),j=ua;return n(),U(P,null,[a(V,null,{default:l(()=>[a(K,{class:"-mb-15px",model:t(o),ref_key:"queryFormRef",ref:k,inline:!0,"label-width":"68px"},{default:l(()=>[a(f,{label:"\u89D2\u8272\u540D\u79F0",prop:"name"},{default:l(()=>[a(R,{modelValue:t(o).name,"onUpdate:modelValue":r[0]||(r[0]=e=>t(o).name=e),placeholder:"\u8BF7\u8F93\u5165\u89D2\u8272\u540D\u79F0",clearable:"",onKeyup:A(d,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(f,{label:"\u89D2\u8272\u7C7B\u522B",prop:"category"},{default:l(()=>[a(R,{modelValue:t(o).category,"onUpdate:modelValue":r[1]||(r[1]=e=>t(o).category=e),placeholder:"\u8BF7\u8F93\u5165\u89D2\u8272\u7C7B\u522B",clearable:"",onKeyup:A(d,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(f,{label:"\u662F\u5426\u516C\u5F00",prop:"publicStatus"},{default:l(()=>[a(G,{modelValue:t(o).publicStatus,"onUpdate:modelValue":r[2]||(r[2]=e=>t(o).publicStatus=e),placeholder:"\u8BF7\u9009\u62E9\u662F\u5426\u516C\u5F00",clearable:"",class:"!w-240px"},{default:l(()=>[(n(!0),U(P,null,Y(t($)(t(v).INFRA_BOOLEAN_STRING),e=>(n(),i(E,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(f,null,{default:l(()=>[a(c,{onClick:d},{default:l(()=>[a(b,{icon:"ep:search",class:"mr-5px"}),m(" \u641C\u7D22")]),_:1}),a(c,{onClick:z},{default:l(()=>[a(b,{icon:"ep:refresh",class:"mr-5px"}),m(" \u91CD\u7F6E")]),_:1}),y((n(),i(c,{type:"primary",plain:"",onClick:r[3]||(r[3]=e=>x("create"))},{default:l(()=>[a(b,{icon:"ep:plus",class:"mr-5px"}),m(" \u65B0\u589E ")]),_:1})),[[h,["ai:chat-role:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(V,null,{default:l(()=>[y((n(),i(M,{data:t(S),stripe:!0,"show-overflow-tooltip":!0},{default:l(()=>[a(s,{label:"\u89D2\u8272\u540D\u79F0",align:"center",prop:"name"}),a(s,{label:"\u7ED1\u5B9A\u6A21\u578B",align:"center",prop:"modelName"}),a(s,{label:"\u89D2\u8272\u5934\u50CF",align:"center",prop:"avatar"},{default:l(e=>[a(L,{src:e==null?void 0:e.row.avatar,class:"w-32px h-32px"},null,8,["src"])]),_:1}),a(s,{label:"\u89D2\u8272\u7C7B\u522B",align:"center",prop:"category"}),a(s,{label:"\u89D2\u8272\u63CF\u8FF0",align:"center",prop:"description"}),a(s,{label:"\u89D2\u8272\u8BBE\u5B9A",align:"center",prop:"systemMessage"}),a(s,{label:"\u662F\u5426\u516C\u5F00",align:"center",prop:"publicStatus"},{default:l(e=>[a(O,{type:t(v).INFRA_BOOLEAN_STRING,value:e.row.publicStatus},null,8,["type","value"])]),_:1}),a(s,{label:"\u72B6\u6001",align:"center",prop:"status"},{default:l(e=>[a(O,{type:t(v).COMMON_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),a(s,{label:"\u89D2\u8272\u6392\u5E8F",align:"center",prop:"sort"}),a(s,{label:"\u64CD\u4F5C",align:"center"},{default:l(e=>[y((n(),i(c,{link:"",type:"primary",onClick:q=>x("update",e.row.id)},{default:l(()=>[m(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[h,["ai:chat-role:update"]]]),y((n(),i(c,{link:"",type:"danger",onClick:q=>(async D=>{try{await w.delConfirm(),await F.deleteChatRole(D),w.success(T("common.delSuccess")),await p()}catch{}})(e.row.id)},{default:l(()=>[m(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[h,["ai:chat-role:delete"]]])]),_:1})]),_:1},8,["data"])),[[j,t(g)]]),a(B,{total:t(C),page:t(o).pageNo,"onUpdate:page":r[4]||(r[4]=e=>t(o).pageNo=e),limit:t(o).pageSize,"onUpdate:limit":r[5]||(r[5]=e=>t(o).pageSize=e),onPagination:p},null,8,["total","page","limit"])]),_:1}),a(ha,{ref_key:"formRef",ref:N,onSuccess:p},null,512)],64)}}})});export{Na as __tla,I as default};
