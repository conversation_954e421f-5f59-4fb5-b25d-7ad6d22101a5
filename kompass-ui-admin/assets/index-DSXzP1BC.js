import{d as X,r as s,u as Y,S as Z,C as tt,T as at,o as c,c as rt,i as r,w as _,a as t,H as _t,l as n,j as y,a9 as f,F as et,I as lt,N as it,z as st,A as ct,E as ot,__tla as ut}from"./index-BUSn51wb.js";import{g as mt,_ as nt,__tla as yt}from"./index-CpmUC5sy.js";import{u as ft,__tla as pt}from"./tagsView-BOOrxb3Q.js";import{g as dt,u as ht,l as g,r as vt,p as bt,__tla as zt}from"./index-CD52sTBY.js";import{_ as wt,__tla as Ct}from"./CustomerForm.vue_vue_type_script_setup_true_lang-BJmzYL0H.js";import{_ as kt,__tla as Rt}from"./CustomerDetailsInfo.vue_vue_type_script_setup_true_lang-FY-XzdvT.js";import{_ as Mt,__tla as St}from"./CustomerDetailsHeader.vue_vue_type_script_setup_true_lang-CIncwRaq.js";import{_ as Ut,__tla as Ot}from"./ContactList.vue_vue_type_script_setup_true_lang-DUTa5zBf.js";import{_ as Tt,__tla as $t}from"./ContractList.vue_vue_type_script_setup_true_lang-uxdeMq-H.js";import{_ as Et,__tla as gt}from"./BusinessList.vue_vue_type_script_setup_true_lang-BcLGEvTu.js";import{_ as It,__tla as Ft}from"./ReceivableList.vue_vue_type_script_setup_true_lang-pc_SsQyC.js";import{_ as Pt,__tla as xt}from"./ReceivablePlanList.vue_vue_type_script_setup_true_lang-Dlr5i6D_.js";import{_ as Ht,__tla as Lt}from"./PermissionList.vue_vue_type_script_setup_true_lang--VdCh_pH.js";import{_ as Wt,__tla as jt}from"./TransferForm.vue_vue_type_script_setup_true_lang-Dm8VVNkH.js";import{_ as At,__tla as Bt}from"./index.vue_vue_type_script_setup_true_lang-cILER8X7.js";import{B as d,__tla as Dt}from"./index-pKzyIv29.js";import{_ as Nt,__tla as Qt}from"./CustomerDistributeForm.vue_vue_type_script_setup_true_lang-BavbExY4.js";import{__tla as Vt}from"./el-timeline-item-D8aDRTsd.js";import{__tla as qt}from"./formatTime-DWdBpgsM.js";import{__tla as Gt}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as Jt}from"./index-CyP7ZSdX.js";import"./tree-BMa075Oj.js";import{__tla as Kt}from"./index-BYXzDB8j.js";import{__tla as Xt}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{__tla as Yt}from"./el-card-CJbXGyyg.js";import{__tla as Zt}from"./el-collapse-item-B_QvnH_b.js";import{__tla as ta}from"./el-descriptions-item-dD3qa0ub.js";import{__tla as aa}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import"./color-BN7ZL7BD.js";import{__tla as ra}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{__tla as _a}from"./index-Cch5e1V0.js";import{__tla as ea}from"./index-9ux5MgCS.js";import{__tla as la}from"./ContactForm.vue_vue_type_script_setup_true_lang-B3yX3y4l.js";import{__tla as ia}from"./ContactListModal.vue_vue_type_script_setup_true_lang-Ba5Cb5nV.js";import{__tla as sa}from"./index-DrB1WZUR.js";import{__tla as ca}from"./ContractForm.vue_vue_type_script_setup_true_lang-DUqFqaHH.js";import{__tla as oa}from"./index-M52UJVMY.js";import{__tla as ua}from"./ContractProductForm.vue_vue_type_script_setup_true_lang-CcyDfNfh.js";import{__tla as ma}from"./index-CaE_tgzr.js";import{__tla as na}from"./BusinessForm.vue_vue_type_script_setup_true_lang-D9dBQLPY.js";import{__tla as ya}from"./index-HLeyY-fc.js";import{__tla as fa}from"./BusinessProductForm.vue_vue_type_script_setup_true_lang-BrV7GF0g.js";import{__tla as pa}from"./BusinessListModal.vue_vue_type_script_setup_true_lang-BvJ2tBPi.js";import{__tla as da}from"./index-D3Ji6shA.js";import{__tla as ha}from"./ReceivableForm.vue_vue_type_script_setup_true_lang-37IhTDRa.js";import{__tla as va}from"./index-Uo5NQqNb.js";import{__tla as ba}from"./ReceivablePlanForm.vue_vue_type_script_setup_true_lang-BzUD040F.js";import{__tla as za}from"./PermissionForm.vue_vue_type_script_setup_true_lang-oI9oCvWg.js";import{__tla as wa}from"./index-CRiW4Z5g.js";import{__tla as Ca}from"./FollowUpRecordForm.vue_vue_type_script_setup_true_lang-CVy9lSzv.js";import{__tla as ka}from"./FollowUpRecordBusinessForm.vue_vue_type_script_setup_true_lang-CrOiARp4.js";import{__tla as Ra}from"./FollowUpRecordContactForm.vue_vue_type_script_setup_true_lang-DH5izzTW.js";let I,Ma=Promise.all([(()=>{try{return ut}catch{}})(),(()=>{try{return yt}catch{}})(),(()=>{try{return pt}catch{}})(),(()=>{try{return zt}catch{}})(),(()=>{try{return Ct}catch{}})(),(()=>{try{return Rt}catch{}})(),(()=>{try{return St}catch{}})(),(()=>{try{return Ot}catch{}})(),(()=>{try{return $t}catch{}})(),(()=>{try{return gt}catch{}})(),(()=>{try{return Ft}catch{}})(),(()=>{try{return xt}catch{}})(),(()=>{try{return Lt}catch{}})(),(()=>{try{return jt}catch{}})(),(()=>{try{return Bt}catch{}})(),(()=>{try{return Dt}catch{}})(),(()=>{try{return Qt}catch{}})(),(()=>{try{return Vt}catch{}})(),(()=>{try{return qt}catch{}})(),(()=>{try{return Gt}catch{}})(),(()=>{try{return Jt}catch{}})(),(()=>{try{return Kt}catch{}})(),(()=>{try{return Xt}catch{}})(),(()=>{try{return Yt}catch{}})(),(()=>{try{return Zt}catch{}})(),(()=>{try{return ta}catch{}})(),(()=>{try{return aa}catch{}})(),(()=>{try{return ra}catch{}})(),(()=>{try{return _a}catch{}})(),(()=>{try{return ea}catch{}})(),(()=>{try{return la}catch{}})(),(()=>{try{return ia}catch{}})(),(()=>{try{return sa}catch{}})(),(()=>{try{return ca}catch{}})(),(()=>{try{return oa}catch{}})(),(()=>{try{return ua}catch{}})(),(()=>{try{return ma}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return ya}catch{}})(),(()=>{try{return fa}catch{}})(),(()=>{try{return pa}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return ha}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return ba}catch{}})(),(()=>{try{return za}catch{}})(),(()=>{try{return wa}catch{}})(),(()=>{try{return Ca}catch{}})(),(()=>{try{return ka}catch{}})(),(()=>{try{return Ra}catch{}})()]).then(async()=>{I=X({name:"CrmCustomerDetail",__name:"index",setup(Sa){const l=s(0),b=s(!0),i=lt(),{delView:F}=ft(),{push:P,currentRoute:x}=Y(),o=s(),a=s({}),p=async()=>{b.value=!0;try{a.value=await dt(l.value),await Q()}finally{b.value=!1}},w=s(),H=()=>{var e;(e=w.value)==null||e.open("update",l.value)},L=async()=>{const e=!a.value.dealStatus;try{await i.confirm(`\u786E\u5B9A\u66F4\u65B0\u6210\u4EA4\u72B6\u6001\u4E3A\u3010${e?"\u5DF2\u6210\u4EA4":"\u672A\u6210\u4EA4"}\u3011\u5417\uFF1F`),await ht(l.value,e),i.success("\u66F4\u65B0\u6210\u4EA4\u72B6\u6001\u6210\u529F"),await p()}catch{}},C=s(),W=()=>{var e;(e=C.value)==null||e.open(l.value)},j=async()=>{await i.confirm(`\u786E\u5B9A\u9501\u5B9A\u5BA2\u6237\u3010${a.value.name}\u3011 \u5417\uFF1F`),await g(t(l.value),!0),i.success(`\u9501\u5B9A\u5BA2\u6237\u3010${a.value.name}\u3011\u6210\u529F`),await p()},A=async()=>{await i.confirm(`\u786E\u5B9A\u89E3\u9501\u5BA2\u6237\u3010${a.value.name}\u3011 \u5417\uFF1F`),await g(t(l.value),!1),i.success(`\u89E3\u9501\u5BA2\u6237\u3010${a.value.name}\u3011\u6210\u529F`),await p()},B=async()=>{await i.confirm(`\u786E\u5B9A\u9886\u53D6\u5BA2\u6237\u3010${a.value.name}\u3011 \u5417\uFF1F`),await vt([t(l.value)]),i.success(`\u9886\u53D6\u5BA2\u6237\u3010${a.value.name}\u3011\u6210\u529F`),await p()},k=s(),D=async()=>{var e;(e=k.value)==null||e.open(l.value)},N=async()=>{await i.confirm(`\u786E\u5B9A\u5C06\u5BA2\u6237\u3010${a.value.name}\u3011\u653E\u5165\u516C\u6D77\u5417\uFF1F`),await bt(t(l.value)),i.success(`\u5BA2\u6237\u3010${a.value.name}\u3011\u653E\u5165\u516C\u6D77\u6210\u529F`),v()},R=s([]),Q=async()=>{if(!l.value)return;const e=await mt({bizType:d.CRM_CUSTOMER,bizId:l.value});R.value=e.list},M=s(),V=e=>{var z;(z=M.value)==null||z.createReceivable(e)},v=()=>{F(t(x)),P({name:"CrmCustomer"})},{params:S}=Z();return tt(()=>{if(!S.id)return i.warning("\u53C2\u6570\u9519\u8BEF\uFF0C\u5BA2\u6237\u4E0D\u80FD\u4E3A\u7A7A\uFF01"),void v();l.value=S.id,p()}),(e,z)=>{const u=it,m=st,q=nt,G=ct,J=ot,K=at("hasPermi");return c(),rt(et,null,[r(Mt,{customer:t(a),loading:t(b)},{default:_(()=>{var h,U,O,T,$,E;return[(h=t(o))!=null&&h.validateWrite?_t((c(),n(u,{key:0,type:"primary",onClick:H},{default:_(()=>[y(" \u7F16\u8F91 ")]),_:1})),[[K,["crm:customer:update"]]]):f("",!0),(U=t(o))!=null&&U.validateOwnerUser?(c(),n(u,{key:1,type:"primary",onClick:W},{default:_(()=>[y(" \u8F6C\u79FB ")]),_:1})):f("",!0),(O=t(o))!=null&&O.validateWrite?(c(),n(u,{key:2,onClick:L},{default:_(()=>[y(" \u66F4\u6539\u6210\u4EA4\u72B6\u6001 ")]),_:1})):f("",!0),t(a).lockStatus&&((T=t(o))!=null&&T.validateOwnerUser)?(c(),n(u,{key:3,onClick:A},{default:_(()=>[y(" \u89E3\u9501 ")]),_:1})):f("",!0),!t(a).lockStatus&&(($=t(o))!=null&&$.validateOwnerUser)?(c(),n(u,{key:4,onClick:j},{default:_(()=>[y(" \u9501\u5B9A ")]),_:1})):f("",!0),t(a).ownerUserId?f("",!0):(c(),n(u,{key:5,type:"primary",onClick:B},{default:_(()=>[y(" \u9886\u53D6")]),_:1})),t(a).ownerUserId?f("",!0):(c(),n(u,{key:6,type:"primary",onClick:D},{default:_(()=>[y(" \u5206\u914D ")]),_:1})),t(a).ownerUserId&&((E=t(o))!=null&&E.validateOwnerUser)?(c(),n(u,{key:7,onClick:N},{default:_(()=>[y(" \u653E\u5165\u516C\u6D77 ")]),_:1})):f("",!0)]}),_:1},8,["customer","loading"]),r(J,null,{default:_(()=>[r(G,null,{default:_(()=>[r(m,{label:"\u8DDF\u8FDB\u8BB0\u5F55"},{default:_(()=>[r(At,{"biz-id":t(l),"biz-type":t(d).CRM_CUSTOMER},null,8,["biz-id","biz-type"])]),_:1}),r(m,{label:"\u57FA\u672C\u4FE1\u606F"},{default:_(()=>[r(kt,{customer:t(a)},null,8,["customer"])]),_:1}),r(m,{label:"\u8054\u7CFB\u4EBA",lazy:""},{default:_(()=>[r(Ut,{"biz-id":t(a).id,"biz-type":t(d).CRM_CUSTOMER},null,8,["biz-id","biz-type"])]),_:1}),r(m,{label:"\u56E2\u961F\u6210\u5458"},{default:_(()=>{var h;return[r(Ht,{ref_key:"permissionListRef",ref:o,"biz-id":t(a).id,"biz-type":t(d).CRM_CUSTOMER,"show-action":!((h=t(o))!=null&&h.isPool)||!1,onQuitTeam:v},null,8,["biz-id","biz-type","show-action"])]}),_:1}),r(m,{label:"\u5546\u673A",lazy:""},{default:_(()=>[r(Et,{"biz-id":t(a).id,"biz-type":t(d).CRM_CUSTOMER},null,8,["biz-id","biz-type"])]),_:1}),r(m,{label:"\u5408\u540C",lazy:""},{default:_(()=>[r(Tt,{"biz-id":t(a).id,"biz-type":t(d).CRM_CUSTOMER},null,8,["biz-id","biz-type"])]),_:1}),r(m,{label:"\u56DE\u6B3E",lazy:""},{default:_(()=>[r(Pt,{"customer-id":t(a).id,onCreateReceivable:V},null,8,["customer-id"]),r(It,{ref_key:"receivableListRef",ref:M,"customer-id":t(a).id},null,8,["customer-id"])]),_:1}),r(m,{label:"\u64CD\u4F5C\u65E5\u5FD7"},{default:_(()=>[r(q,{"log-list":t(R)},null,8,["log-list"])]),_:1})]),_:1})]),_:1}),r(wt,{ref_key:"formRef",ref:w,onSuccess:p},null,512),r(Nt,{ref_key:"distributeForm",ref:k,onSuccess:p},null,512),r(Wt,{ref_key:"transferFormRef",ref:C,"biz-type":t(d).CRM_CUSTOMER,onSuccess:v},null,8,["biz-type"])],64)}}})});export{Ma as __tla,I as default};
