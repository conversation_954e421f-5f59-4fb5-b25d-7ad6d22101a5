import{by as w,d as Y,n as D,I as G,r,f as H,o as c,l as v,w as d,i as t,a as e,j as _,H as y,c as O,F as Z,k as z,V as B,G as J,t as K,a8 as I,y as M,eF as Q,am as W,an as X,L as $,cc as ee,Z as ae,O as le,N as oe,R as de,__tla as te}from"./index-BUSn51wb.js";import{_ as se,__tla as ue}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";let L,U,re=Promise.all([(()=>{try{return te}catch{}})(),(()=>{try{return ue}catch{}})()]).then(async()=>{U=function(f){return w.get({url:"/pay/demo-transfer/page",params:f})},L=Y({__name:"DemoTransferForm",emits:["success"],setup(f,{expose:k,emit:x}){const{t:V}=D(),F=G(),u=r(!1),b=r(""),p=r(!1),g=r(""),o=r({id:void 0,price:void 0,type:void 0,userName:void 0,alipayLogonId:void 0,openid:void 0}),R=H({price:[{required:!0,message:"\u8F6C\u8D26\u91D1\u989D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],type:[{required:!0,message:"\u8F6C\u8D26\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}]}),n=r();k({open:async s=>{u.value=!0,b.value=V("action."+s),g.value=s,h()},close:async()=>{u.value=!1,h()}});const T=x,q=async()=>{if(n&&await n.value.validate()){p.value=!0;try{const s={...o.value};s.price=Q(s.price),g.value==="create"&&(await function(l){return w.post({url:"/pay/demo-transfer/create",data:l})}(s),F.success(V("common.createSuccess"))),u.value=!1,T("success")}finally{p.value=!1}}},h=()=>{var s;o.value={id:void 0,price:void 0,userName:void 0,alipayLogonId:void 0,openid:void 0},(s=n.value)==null||s.resetFields()};return(s,l)=>{const A=W,P=X,i=$,j=ee,m=ae,C=le,N=oe,E=se,S=de;return c(),v(E,{title:e(b),modelValue:e(u),"onUpdate:modelValue":l[6]||(l[6]=a=>M(u)?u.value=a:null),width:"800px"},{footer:d(()=>[t(N,{onClick:q,type:"primary",disabled:e(p)},{default:d(()=>[_("\u786E \u5B9A")]),_:1},8,["disabled"]),t(N,{onClick:l[5]||(l[5]=a=>u.value=!1)},{default:d(()=>[_("\u53D6 \u6D88")]),_:1})]),default:d(()=>[y((c(),v(C,{ref_key:"formRef",ref:n,model:e(o),rules:e(R),"label-width":"120px"},{default:d(()=>[t(i,{label:"\u8F6C\u8D26\u7C7B\u578B",prop:"type"},{default:d(()=>[t(P,{modelValue:e(o).type,"onUpdate:modelValue":l[0]||(l[0]=a=>e(o).type=a)},{default:d(()=>[(c(!0),O(Z,null,z(e(B)(e(J).PAY_TRANSFER_TYPE),a=>(c(),v(A,{key:a.value,label:a.value,disabled:a.value===2||a.value===3||a.value===4},{default:d(()=>[_(K(a.label),1)]),_:2},1032,["label","disabled"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(i,{label:"\u8F6C\u8D26\u91D1\u989D(\u5143)",prop:"price"},{default:d(()=>[t(j,{modelValue:e(o).price,"onUpdate:modelValue":l[1]||(l[1]=a=>e(o).price=a),min:0,precision:2,step:.01,placeholder:"\u8BF7\u8F93\u5165\u8F6C\u8D26\u91D1\u989D",style:{width:"200px"}},null,8,["modelValue"])]),_:1}),t(i,{label:"\u6536\u6B3E\u4EBA\u59D3\u540D",prop:"userName"},{default:d(()=>[t(m,{modelValue:e(o).userName,"onUpdate:modelValue":l[2]||(l[2]=a=>e(o).userName=a),placeholder:"\u8BF7\u8F93\u5165\u6536\u6B3E\u4EBA\u59D3\u540D"},null,8,["modelValue"])]),_:1}),y(t(i,{label:"\u652F\u4ED8\u5B9D\u767B\u5F55\u8D26\u53F7",prop:"alipayLogonId"},{default:d(()=>[t(m,{modelValue:e(o).alipayLogonId,"onUpdate:modelValue":l[3]||(l[3]=a=>e(o).alipayLogonId=a),placeholder:"\u8BF7\u8F93\u5165\u652F\u4ED8\u5B9D\u767B\u5F55\u8D26\u53F7"},null,8,["modelValue"])]),_:1},512),[[I,e(o).type===1]]),y(t(i,{label:"\u5FAE\u4FE1 openid",prop:"openid"},{default:d(()=>[t(m,{modelValue:e(o).openid,"onUpdate:modelValue":l[4]||(l[4]=a=>e(o).openid=a),placeholder:"\u8BF7\u8F93\u5165\u5FAE\u4FE1 openid"},null,8,["modelValue"])]),_:1},512),[[I,e(o).type===2]])]),_:1},8,["model","rules"])),[[S,e(p)]])]),_:1},8,["title","modelValue"])}}})});export{L as _,re as __tla,U as g};
