import{by as r,__tla as t}from"./index-BUSn51wb.js";let e,d=Promise.all([(()=>{try{return t}catch{}})()]).then(async()=>{e={getOrderPage:async a=>await r.get({url:"/als/order/page",params:a}),getOrder:async a=>await r.get({url:"/als/order/get?id="+a}),createOrder:async a=>await r.post({url:"/als/order/create",data:a}),updateOrder:async a=>await r.put({url:"/als/order/update",data:a}),deleteOrder:async a=>await r.delete({url:"/als/order/delete?id="+a}),updateLocal:async a=>await r.get({url:"/als/order/updateLocal?id="+a}),auditOrder:async a=>await r.get({url:"/als/order/audit?id="+a}),pauseOrder:async a=>await r.get({url:"/als/order/pause?id="+a}),startOrder:async a=>await r.get({url:"/als/order/start?id="+a}),releaseOrder:async a=>await r.get({url:"/als/order/release?id="+a}),exportOrder:async a=>await r.download({url:"/als/order/export-excel",params:a}),addTrackDate:async a=>await r.put({url:"/als/order/addTrackDate",data:a}),addTeacher:async a=>await r.put({url:"/als/order/addTeacher",data:a}),changeTeacher:async a=>await r.put({url:"/als/order/changeTeacher",data:a}),getOrderMap:async a=>await r.get({url:"/als/order/map",params:a})}});export{e as O,d as __tla};
