import{d as le,I as te,n as re,r as d,f as oe,C as se,T as ue,o as s,c as w,i as a,w as r,a as l,U as L,F as g,k as S,l as i,V as ie,G as q,j as n,H as m,eo as ce,dV as de,Z as ne,L as pe,J as me,K as _e,M as fe,_ as ve,N as ye,O as he,P as ke,Q as be,R as we,__tla as ge}from"./index-BUSn51wb.js";import{_ as xe,__tla as Ve}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as Ce,__tla as Se}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as Ue,__tla as Te}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as Ie,__tla as Pe}from"./index-COobLwz-.js";import{b as Me,__tla as Ne}from"./formatTime-DWdBpgsM.js";import{d as De}from"./download-e0EdwhTv.js";import{_ as We,S as U,__tla as Ke}from"./StockMoveForm.vue_vue_type_script_setup_true_lang-eZWHx2Sj.js";import{P as Re,__tla as Ye}from"./index-B00QUU3o.js";import{W as ze,__tla as Ae}from"./index-B5GxX3eg.js";import{g as Ee,__tla as He}from"./index-BYXzDB8j.js";import{__tla as Le}from"./index-Cch5e1V0.js";import"./color-BN7ZL7BD.js";import{__tla as qe}from"./el-card-CJbXGyyg.js";import{__tla as Fe}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as je}from"./StockMoveItemForm.vue_vue_type_script_setup_true_lang-BGF_OMy5.js";import{__tla as Ge}from"./index-BCEOZol9.js";let F,Je=Promise.all([(()=>{try{return ge}catch{}})(),(()=>{try{return Ve}catch{}})(),(()=>{try{return Se}catch{}})(),(()=>{try{return Te}catch{}})(),(()=>{try{return Pe}catch{}})(),(()=>{try{return Ne}catch{}})(),(()=>{try{return Ke}catch{}})(),(()=>{try{return Ye}catch{}})(),(()=>{try{return Ae}catch{}})(),(()=>{try{return He}catch{}})(),(()=>{try{return Le}catch{}})(),(()=>{try{return qe}catch{}})(),(()=>{try{return Fe}catch{}})(),(()=>{try{return je}catch{}})(),(()=>{try{return Ge}catch{}})()]).then(async()=>{F=le({name:"ErpStockMove",__name:"index",setup(Oe){const h=te(),{t:j}=re(),T=d(!0),M=d([]),N=d(0),o=oe({pageNo:1,pageSize:10,no:void 0,productId:void 0,fromWarehouseId:void 0,moveTime:[],status:void 0,remark:void 0,creator:void 0}),D=d(),I=d(!1),W=d([]),K=d([]),R=d([]),v=async()=>{T.value=!0;try{const u=await U.getStockMovePage(o);M.value=u.list,N.value=u.total}finally{T.value=!1}},x=()=>{o.pageNo=1,v()},G=()=>{D.value.resetFields(),x()},Y=d(),P=(u,t)=>{Y.value.open(u,t)},z=async u=>{try{await h.delConfirm(),await U.deleteStockMove(u),h.success(j("common.delSuccess")),await v(),k.value=k.value.filter(t=>!u.includes(t.id))}catch{}},A=async(u,t)=>{try{await h.confirm(`\u786E\u5B9A${t===20?"\u5BA1\u6279":"\u53CD\u5BA1\u6279"}\u8BE5\u8C03\u5EA6\u5355\u5417\uFF1F`),await U.updateStockMoveStatus(u,t),h.success((t===20?"\u5BA1\u6279":"\u53CD\u5BA1\u6279")+"\u6210\u529F"),await v()}catch{}},J=async()=>{try{await h.exportConfirm(),I.value=!0;const u=await U.exportStockMove(o);De.excel(u,"\u5E93\u5B58\u8C03\u5EA6\u5355.xls")}catch{}finally{I.value=!1}},k=d([]),O=u=>{k.value=u};return se(async()=>{await v(),W.value=await Re.getProductSimpleList(),K.value=await ze.getWarehouseSimpleList(),R.value=await Ee()}),(u,t)=>{const Q=Ie,E=ne,_=pe,V=me,C=_e,Z=fe,b=ve,c=ye,$=he,H=Ue,p=ke,B=Ce,X=be,ee=xe,f=ue("hasPermi"),ae=we;return s(),w(g,null,[a(Q,{title:"\u3010\u5E93\u5B58\u3011\u5E93\u5B58\u8C03\u62E8\u3001\u5E93\u5B58\u76D8\u70B9",url:"https://doc.iocoder.cn/erp/stock-move-check/"}),a(H,null,{default:r(()=>[a($,{class:"-mb-15px",model:l(o),ref_key:"queryFormRef",ref:D,inline:!0,"label-width":"68px"},{default:r(()=>[a(_,{label:"\u8C03\u5EA6\u5355\u53F7",prop:"no"},{default:r(()=>[a(E,{modelValue:l(o).no,"onUpdate:modelValue":t[0]||(t[0]=e=>l(o).no=e),placeholder:"\u8BF7\u8F93\u5165\u8C03\u5EA6\u5355\u53F7",clearable:"",onKeyup:L(x,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(_,{label:"\u4EA7\u54C1",prop:"productId"},{default:r(()=>[a(C,{modelValue:l(o).productId,"onUpdate:modelValue":t[1]||(t[1]=e=>l(o).productId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1",class:"!w-240px"},{default:r(()=>[(s(!0),w(g,null,S(l(W),e=>(s(),i(V,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(_,{label:"\u8C03\u5EA6\u65F6\u95F4",prop:"moveTime"},{default:r(()=>[a(Z,{modelValue:l(o).moveTime,"onUpdate:modelValue":t[2]||(t[2]=e=>l(o).moveTime=e),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),a(_,{label:"\u4ED3\u5E93",prop:"fromWarehouseId"},{default:r(()=>[a(C,{modelValue:l(o).fromWarehouseId,"onUpdate:modelValue":t[3]||(t[3]=e=>l(o).fromWarehouseId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93",class:"!w-240px"},{default:r(()=>[(s(!0),w(g,null,S(l(K),e=>(s(),i(V,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(_,{label:"\u521B\u5EFA\u4EBA",prop:"creator"},{default:r(()=>[a(C,{modelValue:l(o).creator,"onUpdate:modelValue":t[4]||(t[4]=e=>l(o).creator=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u521B\u5EFA\u4EBA",class:"!w-240px"},{default:r(()=>[(s(!0),w(g,null,S(l(R),e=>(s(),i(V,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(_,{label:"\u72B6\u6001",prop:"status"},{default:r(()=>[a(C,{modelValue:l(o).status,"onUpdate:modelValue":t[5]||(t[5]=e=>l(o).status=e),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",clearable:"",class:"!w-240px"},{default:r(()=>[(s(!0),w(g,null,S(l(ie)(l(q).ERP_AUDIT_STATUS),e=>(s(),i(V,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(_,{label:"\u5907\u6CE8",prop:"remark"},{default:r(()=>[a(E,{modelValue:l(o).remark,"onUpdate:modelValue":t[6]||(t[6]=e=>l(o).remark=e),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",clearable:"",onKeyup:L(x,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(_,null,{default:r(()=>[a(c,{onClick:x},{default:r(()=>[a(b,{icon:"ep:search",class:"mr-5px"}),n(" \u641C\u7D22")]),_:1}),a(c,{onClick:G},{default:r(()=>[a(b,{icon:"ep:refresh",class:"mr-5px"}),n(" \u91CD\u7F6E")]),_:1}),m((s(),i(c,{type:"primary",plain:"",onClick:t[7]||(t[7]=e=>P("create"))},{default:r(()=>[a(b,{icon:"ep:plus",class:"mr-5px"}),n(" \u65B0\u589E ")]),_:1})),[[f,["erp:stock-move:create"]]]),m((s(),i(c,{type:"success",plain:"",onClick:J,loading:l(I)},{default:r(()=>[a(b,{icon:"ep:download",class:"mr-5px"}),n(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[f,["erp:stock-move:export"]]]),m((s(),i(c,{type:"danger",plain:"",onClick:t[8]||(t[8]=e=>z(l(k).map(y=>y.id))),disabled:l(k).length===0},{default:r(()=>[a(b,{icon:"ep:delete",class:"mr-5px"}),n(" \u5220\u9664 ")]),_:1},8,["disabled"])),[[f,["erp:stock-move:delete"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(H,null,{default:r(()=>[m((s(),i(X,{data:l(M),stripe:!0,"show-overflow-tooltip":!0,onSelectionChange:O},{default:r(()=>[a(p,{width:"30",label:"\u9009\u62E9",type:"selection"}),a(p,{"min-width":"180",label:"\u8C03\u5EA6\u5355\u53F7",align:"center",prop:"no"}),a(p,{label:"\u4EA7\u54C1\u4FE1\u606F",align:"center",prop:"productNames","min-width":"200"}),a(p,{label:"\u8C03\u5EA6\u65F6\u95F4",align:"center",prop:"moveTime",formatter:l(Me),width:"120px"},null,8,["formatter"]),a(p,{label:"\u521B\u5EFA\u4EBA",align:"center",prop:"creatorName"}),a(p,{label:"\u6570\u91CF",align:"center",prop:"totalCount",formatter:l(ce)},null,8,["formatter"]),a(p,{label:"\u91D1\u989D",align:"center",prop:"totalPrice",formatter:l(de)},null,8,["formatter"]),a(p,{label:"\u72B6\u6001",align:"center",fixed:"right",width:"90",prop:"status"},{default:r(e=>[a(B,{type:l(q).ERP_AUDIT_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),a(p,{label:"\u64CD\u4F5C",align:"center",fixed:"right",width:"220"},{default:r(e=>[m((s(),i(c,{link:"",onClick:y=>P("detail",e.row.id)},{default:r(()=>[n(" \u8BE6\u60C5 ")]),_:2},1032,["onClick"])),[[f,["erp:stock-move:query"]]]),m((s(),i(c,{link:"",type:"primary",onClick:y=>P("update",e.row.id),disabled:e.row.status===20},{default:r(()=>[n(" \u7F16\u8F91 ")]),_:2},1032,["onClick","disabled"])),[[f,["erp:stock-move:update"]]]),e.row.status===10?m((s(),i(c,{key:0,link:"",type:"primary",onClick:y=>A(e.row.id,20)},{default:r(()=>[n(" \u5BA1\u6279 ")]),_:2},1032,["onClick"])),[[f,["erp:stock-move:update-status"]]]):m((s(),i(c,{key:1,link:"",type:"danger",onClick:y=>A(e.row.id,10)},{default:r(()=>[n(" \u53CD\u5BA1\u6279 ")]),_:2},1032,["onClick"])),[[f,["erp:stock-move:update-status"]]]),m((s(),i(c,{link:"",type:"danger",onClick:y=>z([e.row.id])},{default:r(()=>[n(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[f,["erp:stock-move:delete"]]])]),_:1})]),_:1},8,["data"])),[[ae,l(T)]]),a(ee,{total:l(N),page:l(o).pageNo,"onUpdate:page":t[9]||(t[9]=e=>l(o).pageNo=e),limit:l(o).pageSize,"onUpdate:limit":t[10]||(t[10]=e=>l(o).pageSize=e),onPagination:v},null,8,["total","page","limit"])]),_:1}),a(We,{ref_key:"formRef",ref:Y,onSuccess:v},null,512)],64)}}})});export{Je as __tla,F as default};
