import{d as c,o,c as p,g as t,t as n,aV as i,__tla as d}from"./index-BUSn51wb.js";let x,_=Promise.all([(()=>{try{return d}catch{}})()]).then(async()=>{let e,s,a;e={class:"mb-12px"},s={class:"flex text-[var(--el-text-color-primary)] justify-between items-center"},a={class:"text-[var(--el-text-color-secondary)] text-12px my-8px"},x=c({name:"Index",__name:"index",props:{title:{type:String},desc:{type:String}},setup:l=>(r,m)=>(o(),p("div",e,[t("div",s,[t("span",null,n(l.title),1),i(r.$slots,"extra")]),t("div",a,n(l.desc),1),i(r.$slots,"default")]))})});export{x as _,_ as __tla};
