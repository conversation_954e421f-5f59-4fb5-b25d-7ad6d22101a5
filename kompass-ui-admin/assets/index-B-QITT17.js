import{d as u,r as _,C as p,o as l,c as y,i as n,w as f,a as r,H as d,l as h,a9 as v,F as b,R as x,__tla as g}from"./index-BUSn51wb.js";import{_ as j,__tla as w}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as k,__tla as A}from"./IFrame.vue_vue_type_script_setup_true_lang-silYuU_p.js";import{_ as C,__tla as F}from"./index-COobLwz-.js";import{b as H,__tla as I}from"./index-BXfU_lLO.js";import{__tla as P}from"./el-card-CJbXGyyg.js";let e,R=Promise.all([(()=>{try{return g}catch{}})(),(()=>{try{return w}catch{}})(),(()=>{try{return A}catch{}})(),(()=>{try{return F}catch{}})(),(()=>{try{return I}catch{}})(),(()=>{try{return P}catch{}})()]).then(async()=>{e=u({name:"InfraAdminServer",__name:"index",setup(S){const t=_(!0),s=_("http://**************:48080/admin/applications");return p(async()=>{try{const a=await H("url.spring-boot-admin");a&&a.length>0&&(s.value=a)}finally{t.value=!1}}),(a,q)=>{const o=C,c=k,i=j,m=x;return l(),y(b,null,[n(o,{title:"\u670D\u52A1\u76D1\u63A7",url:"https://doc.iocoder.cn/server-monitor/"}),n(i,null,{default:f(()=>[r(t)?v("",!0):d((l(),h(c,{key:0,src:r(s)},null,8,["src"])),[[m,r(t)]])]),_:1})],64)}}})});export{R as __tla,e as default};
