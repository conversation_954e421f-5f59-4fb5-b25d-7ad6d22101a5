import{d as O,p as K,r as c,f as v,at as L,o as r,l as o,w as d,a as e,O as g,i as p,c as n,k as f,j as N,t as S,V as U,G as Y,F as b,a9 as A,I as G,dK as J,ai as X,ca as q,L as z,J as B,K as C,__tla as H}from"./index-BUSn51wb.js";import{D as M}from"./constants-A8BI3pz7.js";import{r as T,__tla as Q}from"./formRules-CA9eXdcX.js";let D,W=Promise.all([(()=>{try{return H}catch{}})(),(()=>{try{return Q}catch{}})()]).then(async()=>{D=O({name:"ProductDeliveryForm",__name:"DeliveryForm",props:{propFormData:{type:Object,default:()=>{}},isDetail:K.bool.def(!1)},emits:["update:activeName"],setup(m,{expose:V,emit:h}){const I=G(),u=m,i=c(),l=v({deliveryTypes:[],deliveryTemplateId:void 0}),k=v({deliveryTypes:[T],deliveryTemplateId:[T]});L(()=>u.propFormData,t=>{t&&J(l,t)},{immediate:!0});const w=h;V({validate:async()=>{var t;if(i)try{await((t=e(i))==null?void 0:t.validate()),Object.assign(u.propFormData,l)}catch(s){throw I.error("\u3010\u7269\u6D41\u8BBE\u7F6E\u3011\u4E0D\u5B8C\u5584\uFF0C\u8BF7\u586B\u5199\u76F8\u5173\u4FE1\u606F"),w("update:activeName","delivery"),s}}});const E=c([]);return(t,s)=>{const F=X,R=q,y=z,P=B,j=C,x=g;return r(),o(x,{ref_key:"formRef",ref:i,model:e(l),rules:e(k),"label-width":"120px",disabled:m.isDetail},{default:d(()=>{var _;return[p(y,{label:"\u914D\u9001\u65B9\u5F0F",prop:"deliveryTypes"},{default:d(()=>[p(R,{modelValue:e(l).deliveryTypes,"onUpdate:modelValue":s[0]||(s[0]=a=>e(l).deliveryTypes=a),class:"w-80"},{default:d(()=>[(r(!0),n(b,null,f(e(U)(e(Y).TRADE_DELIVERY_TYPE),a=>(r(),o(F,{key:a.value,label:a.value},{default:d(()=>[N(S(a.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),(_=e(l).deliveryTypes)!=null&&_.includes(e(M).EXPRESS.type)?(r(),o(y,{key:0,label:"\u8FD0\u8D39\u6A21\u677F",prop:"deliveryTemplateId"},{default:d(()=>[p(j,{placeholder:"\u8BF7\u9009\u62E9\u8FD0\u8D39\u6A21\u677F",modelValue:e(l).deliveryTemplateId,"onUpdate:modelValue":s[1]||(s[1]=a=>e(l).deliveryTemplateId=a),class:"w-80"},{default:d(()=>[(r(!0),n(b,null,f(e(E),a=>(r(),o(P,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):A("",!0)]}),_:1},8,["model","rules","disabled"])}}})});export{D as _,W as __tla};
