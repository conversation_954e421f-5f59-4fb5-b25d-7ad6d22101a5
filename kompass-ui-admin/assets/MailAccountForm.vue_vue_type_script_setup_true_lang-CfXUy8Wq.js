import{d as M,n as R,I as b,r as c,o as x,l as A,w as u,i as m,a as l,j as v,H as C,y as g,N as j,R as E,__tla as H}from"./index-BUSn51wb.js";import{_ as I,__tla as N}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{_ as P,__tla as U}from"./Form-DJa9ov9B.js";import{g as q,c as z,u as B,__tla as D}from"./index-Dtskw_Cu.js";import{r as G,a as J,__tla as K}from"./account.data-fya8ok4m.js";let p,L=Promise.all([(()=>{try{return H}catch{}})(),(()=>{try{return N}catch{}})(),(()=>{try{return U}catch{}})(),(()=>{try{return D}catch{}})(),(()=>{try{return K}catch{}})()]).then(async()=>{p=M({name:"SystemMailAccountForm",__name:"MailAccountForm",emits:["success"],setup(O,{expose:h,emit:w}){const{t:o}=R(),n=b(),e=c(!1),i=c(""),t=c(!1),f=c(""),r=c();h({open:async(s,a)=>{if(e.value=!0,i.value=o("action."+s),f.value=s,a){t.value=!0;try{const _=await q(a);r.value.setValues(_)}finally{t.value=!1}}}});const S=w,V=async()=>{if(r&&await r.value.getElFormRef().validate()){t.value=!0;try{const s=r.value.formModel;f.value==="create"?(await z(s),n.success(o("common.createSuccess"))):(await B(s),n.success(o("common.updateSuccess"))),e.value=!1,S("success")}finally{t.value=!1}}};return(s,a)=>{const _=P,y=j,k=I,F=E;return x(),A(k,{modelValue:l(e),"onUpdate:modelValue":a[1]||(a[1]=d=>g(e)?e.value=d:null),title:l(i)},{footer:u(()=>[m(y,{disabled:l(t),type:"primary",onClick:V},{default:u(()=>[v("\u786E \u5B9A")]),_:1},8,["disabled"]),m(y,{onClick:a[0]||(a[0]=d=>e.value=!1)},{default:u(()=>[v("\u53D6 \u6D88")]),_:1})]),default:u(()=>[C(m(_,{ref_key:"formRef",ref:r,rules:l(G),schema:l(J).formSchema},null,8,["rules","schema"]),[[F,l(t)]])]),_:1},8,["modelValue","title"])}}})});export{p as _,L as __tla};
