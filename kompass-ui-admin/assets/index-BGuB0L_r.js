import{d as w,r as l,C as x,o as s,c as t,i as h,a as r,y as I,e2 as U,U as V,g as d,F as P,k as b,Z as k,B as z,__tla as q}from"./index-BUSn51wb.js";import{I as B,__tla as C}from"./index-Cjd1fP7g.js";let p,F=Promise.all([(()=>{try{return q}catch{}})(),(()=>{try{return C}catch{}})()]).then(async()=>{let i,n,c;i={class:"square-container"},n={class:"gallery"},c=["src"],p=z(w({__name:"index",setup(K){const g=l(1),y=l(20),o=l([]),a=l(""),u=async()=>{const m=await B.getImagePagePublic({pageNo:g.value,pageSize:y.value,prompt:a.value});o.value=m.list},v=async()=>{await u()};return x(async()=>{await u()}),(m,_)=>{const f=k;return s(),t("div",i,[h(f,{modelValue:r(a),"onUpdate:modelValue":_[0]||(_[0]=e=>I(a)?a.value=e:null),style:{width:"100%","margin-bottom":"20px"},size:"large",placeholder:"\u8BF7\u8F93\u5165\u8981\u641C\u7D22\u7684\u5185\u5BB9","suffix-icon":r(U),onKeyup:V(v,["enter"])},null,8,["modelValue","suffix-icon"]),d("div",n,[(s(!0),t(P,null,b(r(o),e=>(s(),t("div",{key:e.id,class:"gallery-item"},[d("img",{src:e.picUrl,class:"img"},null,8,c)]))),128))])])}}}),[["__scopeId","data-v-5a54a84a"]])});export{F as __tla,p as default};
