import{d as w,r as u,f as A,C as D,o as y,c as v,i as t,w as n,a as i,H as b,l as I,F as P,P as N,Q as B,R as q,__tla as E}from"./index-BUSn51wb.js";import{E as L,__tla as S}from"./el-card-CJbXGyyg.js";import{E as F,__tla as H}from"./el-skeleton-item-tDN8U6BH.js";import{_ as Q,__tla as R}from"./Echart.vue_vue_type_script_setup_true_lang-DnLZLH3w.js";import{S as X,__tla as Z}from"./customer-DXRFD9ec.js";let d,j=Promise.all([(()=>{try{return E}catch{}})(),(()=>{try{return S}catch{}})(),(()=>{try{return H}catch{}})(),(()=>{try{return R}catch{}})(),(()=>{try{return Z}catch{}})()]).then(async()=>{d=w({name:"CustomerDealCycleByArea",__name:"CustomerDealCycleByArea",props:{queryParams:{}},setup(_,{expose:p}){const x=_,r=u(!1),o=u([]),a=A({grid:{left:20,right:40,bottom:20,containLabel:!0},legend:{},series:[{name:"\u6210\u4EA4\u5468\u671F(\u5929)",type:"bar",data:[],yAxisIndex:0},{name:"\u6210\u4EA4\u5BA2\u6237\u6570",type:"bar",data:[],yAxisIndex:1}],toolbox:{feature:{dataZoom:{xAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u6210\u4EA4\u5468\u671F\u5206\u6790"}}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},yAxis:[{type:"value",name:"\u6210\u4EA4\u5468\u671F(\u5929)",min:0,minInterval:1},{type:"value",name:"\u6210\u4EA4\u5BA2\u6237\u6570",min:0,minInterval:1,splitLine:{lineStyle:{type:"dotted",opacity:.7}}}],xAxis:{type:"category",name:"\u533A\u57DF",data:[]}}),m=async()=>{r.value=!0;try{await(async()=>{const s=(await X.getCustomerDealCycleByArea(x.queryParams)).map(e=>({areaName:e.areaName,customerDealCycle:e.customerDealCycle,customerDealCount:e.customerDealCount}));a.xAxis&&a.xAxis.data&&(a.xAxis.data=s.map(e=>e.areaName)),a.series&&a.series[0]&&a.series[0].data&&(a.series[0].data=s.map(e=>e.customerDealCycle)),a.series&&a.series[1]&&a.series[1].data&&(a.series[1].data=s.map(e=>e.customerDealCount)),o.value=s})()}finally{r.value=!1}};return p({loadData:m}),D(()=>{m()}),(s,e)=>{const h=Q,g=F,c=L,l=N,C=B,f=q;return y(),v(P,null,[t(c,{shadow:"never"},{default:n(()=>[t(g,{loading:i(r),animated:""},{default:n(()=>[t(h,{height:500,options:i(a)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),t(c,{shadow:"never",class:"mt-16px"},{default:n(()=>[b((y(),I(C,{data:i(o)},{default:n(()=>[t(l,{label:"\u5E8F\u53F7",align:"center",type:"index",width:"80"}),t(l,{label:"\u533A\u57DF",align:"center",prop:"areaName","min-width":"200"}),t(l,{label:"\u6210\u4EA4\u5468\u671F(\u5929)",align:"center",prop:"customerDealCycle","min-width":"200"}),t(l,{label:"\u6210\u4EA4\u5BA2\u6237\u6570",align:"center",prop:"customerDealCount","min-width":"200"})]),_:1},8,["data"])),[[f,i(r)]])]),_:1})],64)}}})});export{d as _,j as __tla};
