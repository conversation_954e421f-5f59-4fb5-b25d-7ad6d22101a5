import{d as e,o as a,l as s,__tla as o}from"./index-BUSn51wb.js";import{E as _,__tla as p}from"./el-image-BjHZRFih.js";let t,l=Promise.all([(()=>{try{return o}catch{}})(),(()=>{try{return p}catch{}})()]).then(async()=>{t=e({name:"UserOrder",__name:"index",props:{property:{}},setup:n=>(c,m)=>{const r=_;return a(),s(r,{src:"https://shopro.sheepjs.com/admin/static/images/shop/decorate/orderCardStyle.png"})}})});export{l as __tla,t as default};
