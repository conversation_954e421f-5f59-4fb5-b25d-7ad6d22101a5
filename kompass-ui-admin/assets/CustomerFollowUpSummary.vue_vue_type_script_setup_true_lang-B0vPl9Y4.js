import{d as b,r as _,f as v,C as A,o as y,c as C,i as e,w as o,a as i,H as U,l as I,F as P,P as S,Q as F,R as q,__tla as L}from"./index-BUSn51wb.js";import{E as R,__tla as B}from"./el-card-CJbXGyyg.js";import{E as D,__tla as E}from"./el-skeleton-item-tDN8U6BH.js";import{S as c,__tla as k}from"./customer-DXRFD9ec.js";import{_ as H,__tla as N}from"./Echart.vue_vue_type_script_setup_true_lang-DnLZLH3w.js";let x,Q=Promise.all([(()=>{try{return L}catch{}})(),(()=>{try{return B}catch{}})(),(()=>{try{return E}catch{}})(),(()=>{try{return k}catch{}})(),(()=>{try{return N}catch{}})()]).then(async()=>{x=b({name:"CustomerFollowupSummary",__name:"CustomerFollowUpSummary",props:{queryParams:{}},setup(h,{expose:w}){const n=h,r=_(!1),m=_([]),a=v({grid:{left:20,right:30,bottom:20,containLabel:!0},legend:{},series:[{name:"\u8DDF\u8FDB\u5BA2\u6237\u6570",type:"bar",yAxisIndex:0,data:[]},{name:"\u8DDF\u8FDB\u6B21\u6570",type:"bar",yAxisIndex:1,data:[]}],toolbox:{feature:{dataZoom:{xAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u5BA2\u6237\u8DDF\u8FDB\u6B21\u6570\u5206\u6790"}}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},yAxis:[{type:"value",name:"\u8DDF\u8FDB\u5BA2\u6237\u6570",min:0,minInterval:1},{type:"value",name:"\u8DDF\u8FDB\u6B21\u6570",min:0,minInterval:1,splitLine:{lineStyle:{type:"dotted",opacity:.7}}}],xAxis:{type:"category",name:"\u65E5\u671F",axisTick:{alignWithLabel:!0},data:[]}}),p=async()=>{r.value=!0;try{await(async()=>{r.value=!0;const s=await c.getFollowUpSummaryByDate(n.queryParams),d=await c.getFollowUpSummaryByUser(n.queryParams);a.xAxis&&a.xAxis.data&&(a.xAxis.data=s.map(t=>t.time)),a.series&&a.series[0]&&a.series[0].data&&(a.series[0].data=s.map(t=>t.followUpCustomerCount)),a.series&&a.series[1]&&a.series[1].data&&(a.series[1].data=s.map(t=>t.followUpRecordCount)),m.value=d})()}finally{r.value=!1}};return w({loadData:p}),A(()=>{p()}),(s,d)=>{const t=D,u=R,l=S,g=F,f=q;return y(),C(P,null,[e(u,{shadow:"never"},{default:o(()=>[e(t,{loading:i(r),animated:""},{default:o(()=>[e(H,{height:500,options:i(a)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),e(u,{shadow:"never",class:"mt-16px"},{default:o(()=>[U((y(),I(g,{data:i(m)},{default:o(()=>[e(l,{label:"\u5E8F\u53F7",align:"center",type:"index",width:"80"}),e(l,{label:"\u5458\u5DE5\u59D3\u540D",align:"center",prop:"ownerUserName","min-width":"200"}),e(l,{label:"\u8DDF\u8FDB\u6B21\u6570",align:"right",prop:"followUpRecordCount","min-width":"200"}),e(l,{label:"\u8DDF\u8FDB\u5BA2\u6237\u6570",align:"right",prop:"followUpCustomerCount","min-width":"200"})]),_:1},8,["data"])),[[f,i(r)]])]),_:1})],64)}}})});export{x as _,Q as __tla};
