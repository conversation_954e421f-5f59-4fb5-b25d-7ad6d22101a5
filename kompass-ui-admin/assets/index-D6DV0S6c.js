import{d as m,o as r,c as o,l as y,a9 as t,g as c,av as p,t as l,H as g,a8 as f,_ as x,B as h,__tla as u}from"./index-BUSn51wb.js";import{E as v,__tla as k}from"./el-image-BjHZRFih.js";let n,b=Promise.all([(()=>{try{return u}catch{}})(),(()=>{try{return k}catch{}})()]).then(async()=>{let s,i,a;s={class:"title-bar"},i={class:"absolute left-0 top-0 w-full"},a={key:0},n=h(m({name:"TitleBar",__name:"index",props:{property:{}},setup:w=>(e,z)=>{const d=v,_=x;return r(),o("div",s,[e.property.bgImgUrl?(r(),y(d,{key:0,src:e.property.bgImgUrl,fit:"cover",class:"w-full"},null,8,["src"])):t("",!0),c("div",i,[e.property.title?(r(),o("div",{key:0,style:p({fontSize:`${e.property.titleSize}px`,fontWeight:e.property.titleWeight,color:e.property.titleColor,textAlign:e.property.textAlign})},l(e.property.title),5)):t("",!0),e.property.description?(r(),o("div",{key:1,style:p({fontSize:`${e.property.descriptionSize}px`,fontWeight:e.property.descriptionWeight,color:e.property.descriptionColor,textAlign:e.property.textAlign}),class:"m-t-8px"},l(e.property.description),5)):t("",!0)]),g(c("div",{class:"more",style:p({color:e.property.descriptionColor})},[e.property.more.type!=="icon"?(r(),o("span",a,l(e.property.more.text),1)):t("",!0),e.property.more.type!=="text"?(r(),y(_,{key:1,icon:"ep:arrow-right"})):t("",!0)],4),[[f,e.property.more.show]])])}}),[["__scopeId","data-v-456e496d"]])});export{b as __tla,n as default};
