import{_ as c,__tla as o}from"./index.vue_vue_type_script_setup_true_lang-DUWkJHYe.js";import m,{__tla as n}from"./index-CkPmgDRP.js";import{d as u,r as f,o as i,c as h,i as l,a as p,__tla as y}from"./index-BUSn51wb.js";import{__tla as x}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{__tla as d}from"./el-card-CJbXGyyg.js";import{__tla as M}from"./desc.vue_vue_type_script_setup_true_lang-DTnf_h5g.js";import{__tla as g}from"./index.vue_vue_type_script_setup_true_lang-CIHu5un_.js";import{__tla as k}from"./lyric.vue_vue_type_script_setup_true_lang-DglaqPdL.js";import{__tla as v}from"./el-space-Dxj8A-LJ.js";import{__tla as D}from"./el-empty-DomufbmG.js";import{__tla as G}from"./index.vue_vue_type_script_setup_true_lang-CRUeOu0c.js";import{__tla as I}from"./el-image-BjHZRFih.js";import{__tla as P}from"./index.vue_vue_type_script_setup_true_lang-BnEN40Pi.js";import{__tla as R}from"./index.vue_vue_type_script_setup_true_lang-DpxUWmr7.js";import{__tla as b}from"./formatTime-DWdBpgsM.js";let e,j=Promise.all([(()=>{try{return o}catch{}})(),(()=>{try{return n}catch{}})(),(()=>{try{return y}catch{}})(),(()=>{try{return x}catch{}})(),(()=>{try{return d}catch{}})(),(()=>{try{return M}catch{}})(),(()=>{try{return g}catch{}})(),(()=>{try{return k}catch{}})(),(()=>{try{return v}catch{}})(),(()=>{try{return D}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return I}catch{}})(),(()=>{try{return P}catch{}})(),(()=>{try{return R}catch{}})(),(()=>{try{return b}catch{}})()]).then(async()=>{let r;r={class:"flex h-full items-stretch"},e=u({name:"Index",__name:"index",setup(q){const a=f(null);function s(_){var t;(t=p(a))==null||t.generateMusic(_.formData)}return(_,t)=>(i(),h("div",r,[l(c,{class:"flex-none",onGenerateMusic:s}),l(m,{ref_key:"listRef",ref:a,class:"flex-auto"},null,512)]))}})});export{j as __tla,e as default};
