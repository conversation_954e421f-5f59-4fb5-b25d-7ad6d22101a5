import{by as t,__tla as s}from"./index-BUSn51wb.js";let i,o,n,c,r,e,l=Promise.all([(()=>{try{return s}catch{}})()]).then(async()=>{i=async a=>await t.get({url:"/promotion/combination-activity/page",params:a}),r=async a=>await t.get({url:"/promotion/combination-activity/get?id="+a}),n=async a=>await t.post({url:"/promotion/combination-activity/create",data:a}),e=async a=>await t.put({url:"/promotion/combination-activity/update",data:a}),o=async a=>await t.put({url:"/promotion/combination-activity/close?id="+a}),c=async a=>await t.delete({url:"/promotion/combination-activity/delete?id="+a})});export{l as __tla,i as a,o as b,n as c,c as d,r as g,e as u};
