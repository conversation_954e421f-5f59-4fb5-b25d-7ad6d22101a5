import{_ as t,__tla as _}from"./lyric.vue_vue_type_script_setup_true_lang-DglaqPdL.js";import{__tla as a}from"./index-BUSn51wb.js";import{__tla as r}from"./el-space-Dxj8A-LJ.js";import{__tla as l}from"./index.vue_vue_type_script_setup_true_lang-CIHu5un_.js";let e=Promise.all([(()=>{try{return _}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return r}catch{}})(),(()=>{try{return l}catch{}})()]).then(async()=>{});export{e as __tla,t as default};
