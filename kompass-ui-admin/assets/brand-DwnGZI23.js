import{by as t,__tla as s}from"./index-BUSn51wb.js";let r,e,d,l,u,p,c=Promise.all([(()=>{try{return s}catch{}})()]).then(async()=>{d=a=>t.post({url:"/product/brand/create",data:a}),p=a=>t.put({url:"/product/brand/update",data:a}),l=a=>t.delete({url:`/product/brand/delete?id=${a}`}),r=a=>t.get({url:`/product/brand/get?id=${a}`}),e=a=>t.get({url:"/product/brand/page",params:a}),u=()=>t.get({url:"/product/brand/list-all-simple"})});export{c as __tla,r as a,e as b,d as c,l as d,u as g,p as u};
