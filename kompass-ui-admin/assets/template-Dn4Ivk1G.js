import{by as a,__tla as y}from"./index-BUSn51wb.js";let e,o,i,p,r,l,s,d,m=Promise.all([(()=>{try{return y}catch{}})()]).then(async()=>{p=async t=>await a.get({url:"/promotion/diy-template/page",params:t}),e=async t=>await a.get({url:"/promotion/diy-template/get?id="+t}),i=async t=>await a.post({url:"/promotion/diy-template/create",data:t}),o=async t=>await a.put({url:"/promotion/diy-template/update",data:t}),r=async t=>await a.delete({url:"/promotion/diy-template/delete?id="+t}),l=async t=>await a.put({url:"/promotion/diy-template/use?id="+t}),s=async t=>await a.get({url:"/promotion/diy-template/get-property?id="+t}),d=async t=>await a.put({url:"/promotion/diy-template/update-property",data:t})});export{m as __tla,e as a,o as b,i as c,p as d,r as e,l as f,s as g,d as u};
