import{d as T,u as Z,r as t,f as M,C as F,o as P,l as S,w as r,i as l,g as G,a as H,e2 as J,j as K,a9 as X,Z as Q,_ as W,N as Y,z as $,A as aa,e3 as ea,b7 as la,B as ta,__tla as sa}from"./index-BUSn51wb.js";import oa,{__tla as ra}from"./RoleHeader-BBBy8NqQ.js";import z,{__tla as na}from"./RoleList-DrJHeS8f.js";import{_ as ia,C as v,__tla as ua}from"./ChatRoleForm.vue_vue_type_script_setup_true_lang-DAKVMBtZ.js";import ca,{__tla as _a}from"./RoleCategoryList-Dzs_rZGP.js";import{C as ya,__tla as pa}from"./index-UejJy_db.js";import{__tla as ma}from"./el-card-CJbXGyyg.js";import{__tla as va}from"./el-dropdown-item-CIJXMVYa.js";import{__tla as da}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import"./constants-A8BI3pz7.js";import{__tla as ga}from"./index-DrcFYyNA.js";let R,fa=Promise.all([(()=>{try{return sa}catch{}})(),(()=>{try{return ra}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return ua}catch{}})(),(()=>{try{return _a}catch{}})(),(()=>{try{return pa}catch{}})(),(()=>{try{return ma}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return ga}catch{}})()]).then(async()=>{let h;h={class:"search-container"},R=ta(T({__name:"RoleRepository",setup(ha){const U=Z(),u=t(!1),n=t("my-role"),c=t(""),d=M({pageNo:1,pageSize:50}),_=t([]),g=M({pageNo:1,pageSize:50}),y=t([]),p=t("\u5168\u90E8"),w=t([]),I=async a=>{n.value=a.paneName+"",await o()},C=async a=>{const e={...d,name:c.value,publicStatus:!1},{list:s}=await v.getMyPage(e);a?_.value.push.apply(_.value,s):_.value=s},b=async a=>{const e={...g,category:p.value==="\u5168\u90E8"?"":p.value,name:c.value,publicStatus:!0},{total:s,list:m}=await v.getMyPage(e);a?y.value.push.apply(y.value,m):y.value=m},o=async()=>{n.value==="my-role"?(d.pageNo=1,await C()):(g.pageNo=1,await b())},A=async a=>{p.value=a,await o()},f=t(),D=async()=>{f.value.open("my-create",null,"\u6DFB\u52A0\u89D2\u8272")},O=async a=>{f.value.open("my-update",a.id,"\u7F16\u8F91\u89D2\u8272")},E=async a=>{await o()},N=async a=>{await v.deleteMy(a.id),await o()},x=async a=>{try{u.value=!0,a==="public"?(g.pageNo++,await b(!0)):(d.pageNo++,await C(!0))}finally{u.value=!1}},k=async a=>{const e={roleId:a.id},s=await ya.createChatConversationMy(e);await U.push({name:"AiChat",query:{conversationId:s}})};return F(async()=>{await(async()=>{w.value=["\u5168\u90E8",...await v.getCategoryList()]})(),await o()}),(a,e)=>{const s=Q,m=W,j=Y,V=$,q=aa,B=ea,L=la;return P(),S(L,{class:"role-container"},{default:r(()=>[l(ia,{ref_key:"formRef",ref:f,onSuccess:E},null,512),l(oa,{title:"\u89D2\u8272\u4ED3\u5E93",class:"relative"}),l(B,{class:"role-main"},{default:r(()=>[G("div",h,[l(s,{loading:u.value,modelValue:c.value,"onUpdate:modelValue":e[0]||(e[0]=i=>c.value=i),class:"search-input",size:"default",placeholder:"\u8BF7\u8F93\u5165\u641C\u7D22\u7684\u5185\u5BB9","suffix-icon":H(J),onChange:o},null,8,["loading","modelValue","suffix-icon"]),n.value=="my-role"?(P(),S(j,{key:0,type:"primary",onClick:D,class:"ml-20px"},{default:r(()=>[l(m,{icon:"ep:user",style:{"margin-right":"5px"}}),K(" \u6DFB\u52A0\u89D2\u8272 ")]),_:1})):X("",!0)]),l(q,{modelValue:n.value,"onUpdate:modelValue":e[3]||(e[3]=i=>n.value=i),class:"tabs",onTabClick:I},{default:r(()=>[l(V,{class:"role-pane",label:"\u6211\u7684\u89D2\u8272",name:"my-role"},{default:r(()=>[l(z,{loading:u.value,"role-list":_.value,"show-more":!0,onOnDelete:N,onOnEdit:O,onOnUse:k,onOnPage:e[1]||(e[1]=i=>x("my")),class:"mt-20px"},null,8,["loading","role-list"])]),_:1}),l(V,{label:"\u516C\u5171\u89D2\u8272",name:"public-role"},{default:r(()=>[l(ca,{class:"role-category-list","category-list":w.value,active:p.value,onOnCategoryClick:A},null,8,["category-list","active"]),l(z,{"role-list":y.value,onOnDelete:N,onOnEdit:O,onOnUse:k,onOnPage:e[2]||(e[2]=i=>x("public")),class:"mt-20px",loading:""},null,8,["role-list"])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})}}}),[["__scopeId","data-v-3a7f9148"]])});export{fa as __tla,R as default};
