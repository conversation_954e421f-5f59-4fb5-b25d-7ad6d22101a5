import{by as t,__tla as s}from"./index-BUSn51wb.js";let e,r=Promise.all([(()=>{try{return s}catch{}})()]).then(async()=>{e={getCustomerPage:async a=>await t.get({url:"/als/customer/page",params:a}),getCustomer:async a=>await t.get({url:"/als/customer/get?id="+a}),createCustomer:async a=>await t.post({url:"/als/customer/create",data:a}),updateCustomer:async a=>await t.put({url:"/als/customer/update",data:a}),deleteCustomer:async a=>await t.delete({url:"/als/customer/delete?id="+a}),exportCustomer:async a=>await t.download({url:"/als/customer/export-excel",params:a}),getCustomerDetail:async a=>await t.get({url:"/als/customer/detail?id="+a})}});export{e as C,r as __tla};
