import{d as J,I as L,n as Z,r as p,f as $,C as W,T as X,o as i,c as Y,i as t,w as r,a as e,U as tt,F as V,k as at,V as rt,G as O,l as u,j as s,H as d,t as C,aF as et,Z as lt,L as ot,J as _t,K as it,_ as st,N as nt,O as ct,P as mt,Q as pt,R as ut,__tla as dt}from"./index-BUSn51wb.js";import{_ as ft,__tla as yt}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as ht,__tla as wt}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{E as bt,__tla as vt}from"./el-image-BjHZRFih.js";import{_ as gt,__tla as kt}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as Ct,__tla as xt}from"./index-COobLwz-.js";import{f as z,d as St,__tla as Mt}from"./formatTime-DWdBpgsM.js";import{a as Nt,b as Ut,d as Pt,__tla as Tt}from"./combinationActivity-Jgh6nzIi.js";import{_ as Yt,__tla as Vt}from"./CombinationActivityForm.vue_vue_type_script_setup_true_lang-GfOvu1th.js";import{f as Ot,__tla as zt}from"./formatter-DVQ2wbhT.js";import{__tla as Dt}from"./index-Cch5e1V0.js";import"./color-BN7ZL7BD.js";import{__tla as Ft}from"./el-card-CJbXGyyg.js";import{__tla as At}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as Rt}from"./Form-DJa9ov9B.js";import{__tla as jt}from"./el-virtual-list-4L-8WDNg.js";import{__tla as Kt}from"./el-tree-select-CBuha0HW.js";import{__tla as Qt}from"./el-time-select-C-_NEIfl.js";import{__tla as qt}from"./InputPassword-RefetKoR.js";import{__tla as Bt}from"./formRules-CA9eXdcX.js";import{__tla as Et}from"./useCrudSchemas-hBakuBRx.js";import"./tree-BMa075Oj.js";import{__tla as Gt}from"./SpuSelect.vue_vue_type_script_setup_true_lang-Q9Ka9uY_.js";import{__tla as Ht}from"./index-CjyLHUq3.js";import{__tla as It}from"./SkuList-DG93D6KA.js";import{__tla as Jt}from"./category-WzWM3ODe.js";import{__tla as Lt}from"./spu-CW3JGweV.js";import{__tla as Zt}from"./SpuAndSkuList.vue_vue_type_script_setup_true_lang-BlbORlY6.js";let D,$t=Promise.all([(()=>{try{return dt}catch{}})(),(()=>{try{return yt}catch{}})(),(()=>{try{return wt}catch{}})(),(()=>{try{return vt}catch{}})(),(()=>{try{return kt}catch{}})(),(()=>{try{return xt}catch{}})(),(()=>{try{return Mt}catch{}})(),(()=>{try{return Tt}catch{}})(),(()=>{try{return Vt}catch{}})(),(()=>{try{return zt}catch{}})(),(()=>{try{return Dt}catch{}})(),(()=>{try{return Ft}catch{}})(),(()=>{try{return At}catch{}})(),(()=>{try{return Rt}catch{}})(),(()=>{try{return jt}catch{}})(),(()=>{try{return Kt}catch{}})(),(()=>{try{return Qt}catch{}})(),(()=>{try{return qt}catch{}})(),(()=>{try{return Bt}catch{}})(),(()=>{try{return Et}catch{}})(),(()=>{try{return Gt}catch{}})(),(()=>{try{return Ht}catch{}})(),(()=>{try{return It}catch{}})(),(()=>{try{return Jt}catch{}})(),(()=>{try{return Lt}catch{}})(),(()=>{try{return Zt}catch{}})()]).then(async()=>{D=J({name:"PromotionBargainActivity",__name:"index",setup(Wt){const f=L(),{t:F}=Z(),h=p(!0),x=p(0),S=p([]),_=$({pageNo:1,pageSize:10,name:null,status:null}),M=p();p(!1);const c=async()=>{h.value=!0;try{const n=await Nt(_);S.value=n.list,x.value=n.total}finally{h.value=!1}},w=()=>{_.pageNo=1,c()},A=()=>{M.value.resetFields(),w()},N=p(),U=(n,l)=>{N.value.open(n,l)},R=n=>{const l=Math.min(...n.map(b=>b.combinationPrice));return`\uFFE5${et(l)}`};return W(async()=>{await c()}),(n,l)=>{const b=Ct,j=lt,v=ot,K=_t,Q=it,g=st,m=nt,q=ct,P=gt,o=mt,B=bt,E=ht,G=pt,H=ft,y=X("hasPermi"),I=ut;return i(),Y(V,null,[t(b,{title:"\u3010\u8425\u9500\u3011\u62FC\u56E2\u6D3B\u52A8",url:"https://doc.iocoder.cn/mall/promotion-combination/"}),t(P,null,{default:r(()=>[t(q,{class:"-mb-15px",model:e(_),ref_key:"queryFormRef",ref:M,inline:!0,"label-width":"68px"},{default:r(()=>[t(v,{label:"\u6D3B\u52A8\u540D\u79F0",prop:"name"},{default:r(()=>[t(j,{modelValue:e(_).name,"onUpdate:modelValue":l[0]||(l[0]=a=>e(_).name=a),placeholder:"\u8BF7\u8F93\u5165\u6D3B\u52A8\u540D\u79F0",clearable:"",onKeyup:tt(w,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),t(v,{label:"\u6D3B\u52A8\u72B6\u6001",prop:"status"},{default:r(()=>[t(Q,{modelValue:e(_).status,"onUpdate:modelValue":l[1]||(l[1]=a=>e(_).status=a),placeholder:"\u8BF7\u9009\u62E9\u6D3B\u52A8\u72B6\u6001",clearable:"",class:"!w-240px"},{default:r(()=>[(i(!0),Y(V,null,at(e(rt)(e(O).COMMON_STATUS),a=>(i(),u(K,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(v,null,{default:r(()=>[t(m,{onClick:w},{default:r(()=>[t(g,{icon:"ep:search",class:"mr-5px"}),s(" \u641C\u7D22")]),_:1}),t(m,{onClick:A},{default:r(()=>[t(g,{icon:"ep:refresh",class:"mr-5px"}),s(" \u91CD\u7F6E")]),_:1}),d((i(),u(m,{type:"primary",plain:"",onClick:l[2]||(l[2]=a=>U("create"))},{default:r(()=>[t(g,{icon:"ep:plus",class:"mr-5px"}),s(" \u65B0\u589E ")]),_:1})),[[y,["promotion:combination-activity:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),t(P,null,{default:r(()=>[d((i(),u(G,{data:e(S),stripe:!0,"show-overflow-tooltip":!0},{default:r(()=>[t(o,{label:"\u6D3B\u52A8\u7F16\u53F7",prop:"id","min-width":"80"}),t(o,{label:"\u6D3B\u52A8\u540D\u79F0",prop:"name","min-width":"140"}),t(o,{label:"\u6D3B\u52A8\u65F6\u95F4","min-width":"210"},{default:r(a=>[s(C(e(z)(a.row.startTime,"YYYY-MM-DD"))+" ~ "+C(e(z)(a.row.endTime,"YYYY-MM-DD")),1)]),_:1}),t(o,{label:"\u5546\u54C1\u56FE\u7247",prop:"spuName","min-width":"80"},{default:r(a=>[t(B,{src:a.row.picUrl,class:"h-40px w-40px","preview-src-list":[a.row.picUrl],"preview-teleported":""},null,8,["src","preview-src-list"])]),_:1}),t(o,{label:"\u5546\u54C1\u6807\u9898",prop:"spuName","min-width":"300"}),t(o,{label:"\u539F\u4EF7",prop:"marketPrice","min-width":"100",formatter:e(Ot)},null,8,["formatter"]),t(o,{label:"\u62FC\u56E2\u4EF7",prop:"seckillPrice","min-width":"100"},{default:r(a=>[s(C(R(a.row.products)),1)]),_:1}),t(o,{label:"\u5F00\u56E2\u7EC4\u6570",prop:"groupCount","min-width":"100"}),t(o,{label:"\u6210\u56E2\u7EC4\u6570",prop:"groupSuccessCount","min-width":"100"}),t(o,{label:"\u8D2D\u4E70\u6B21\u6570",prop:"recordCount","min-width":"100"}),t(o,{label:"\u6D3B\u52A8\u72B6\u6001",align:"center",prop:"status","min-width":"100"},{default:r(a=>[t(E,{type:e(O).COMMON_STATUS,value:a.row.status},null,8,["type","value"])]),_:1}),t(o,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:e(St),width:"180px"},null,8,["formatter"]),t(o,{label:"\u64CD\u4F5C",align:"center",width:"150px",fixed:"right"},{default:r(a=>[d((i(),u(m,{link:"",type:"primary",onClick:T=>U("update",a.row.id)},{default:r(()=>[s(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[y,["promotion:combination-activity:update"]]]),a.row.status===0?d((i(),u(m,{key:0,link:"",type:"danger",onClick:T=>(async k=>{try{await f.confirm("\u786E\u8BA4\u5173\u95ED\u8BE5\u79D2\u6740\u6D3B\u52A8\u5417\uFF1F"),await Ut(k),f.success("\u5173\u95ED\u6210\u529F"),await c()}catch{}})(a.row.id)},{default:r(()=>[s(" \u5173\u95ED ")]),_:2},1032,["onClick"])),[[y,["promotion:combination-activity:close"]]]):d((i(),u(m,{key:1,link:"",type:"danger",onClick:T=>(async k=>{try{await f.delConfirm(),await Pt(k),f.success(F("common.delSuccess")),await c()}catch{}})(a.row.id)},{default:r(()=>[s(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[y,["promotion:combination-activity:delete"]]])]),_:1})]),_:1},8,["data"])),[[I,e(h)]]),t(H,{total:e(x),page:e(_).pageNo,"onUpdate:page":l[3]||(l[3]=a=>e(_).pageNo=a),limit:e(_).pageSize,"onUpdate:limit":l[4]||(l[4]=a=>e(_).pageSize=a),onPagination:c},null,8,["total","page","limit"])]),_:1}),t(Yt,{ref_key:"formRef",ref:N,onSuccess:c},null,512)],64)}}})});export{$t as __tla,D as default};
