import{_ as f,__tla as p}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{d as b,o as x,c as y,g as e,i as t,w as a,t as s,aV as v,j as c,a as h,F as j,s as w,E,__tla as g}from"./index-BUSn51wb.js";import{E as C,a as D,__tla as F}from"./el-descriptions-item-dD3qa0ub.js";import{f as H,__tla as N}from"./formatTime-DWdBpgsM.js";let o,P=Promise.all([(()=>{try{return p}catch{}})(),(()=>{try{return g}catch{}})(),(()=>{try{return F}catch{}})(),(()=>{try{return N}catch{}})()]).then(async()=>{let r,n;r={class:"flex items-start justify-between"},n={class:"text-xl font-bold"},o=b({__name:"ContactDetailsHeader",props:{contact:{}},setup:T=>(l,V)=>{const u=w,i=E,_=C,m=D,d=f;return x(),y(j,null,[e("div",null,[e("div",r,[e("div",null,[t(i,null,{default:a(()=>[t(u,null,{default:a(()=>[e("span",n,s(l.contact.name),1)]),_:1})]),_:1})]),e("div",null,[v(l.$slots,"default")])])]),t(d,{class:"mt-10px"},{default:a(()=>[t(m,{column:5,direction:"vertical"},{default:a(()=>[t(_,{label:"\u5BA2\u6237\u540D\u79F0"},{default:a(()=>[c(s(l.contact.customerName),1)]),_:1}),t(_,{label:"\u804C\u52A1"},{default:a(()=>[c(s(l.contact.post),1)]),_:1}),t(_,{label:"\u624B\u673A"},{default:a(()=>[c(s(l.contact.mobile),1)]),_:1}),t(_,{label:"\u521B\u5EFA\u65F6\u95F4"},{default:a(()=>[c(s(h(H)(l.contact.createTime)),1)]),_:1})]),_:1})]),_:1})],64)}})});export{o as _,P as __tla};
