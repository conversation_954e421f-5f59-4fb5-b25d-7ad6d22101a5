import{d as q,r as u,o as _,l as m,w as r,i as o,j as v,a,H as R,c as j,k as D,F as H,y as J,J as K,K as L,L as N,Z as O,O as P,N as Z,R as z,__tla as A}from"./index-BUSn51wb.js";import{_ as B,__tla as E}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{t as G,__tla as M}from"./index-OMcsJcjy.js";import{g as Q,__tla as S}from"./index-BYXzDB8j.js";let y,W=Promise.all([(()=>{try{return A}catch{}})(),(()=>{try{return E}catch{}})(),(()=>{try{return M}catch{}})(),(()=>{try{return S}catch{}})()]).then(async()=>{y=q({name:"TaskTransferForm",__name:"TaskTransferForm",emits:["success"],setup(X,{expose:g,emit:b}){const t=u(!1),n=u(!1),l=u({id:"",assigneeUserId:void 0,reason:""}),h=u({assigneeUserId:[{required:!0,message:"\u65B0\u5BA1\u6279\u4EBA\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],reason:[{required:!0,message:"\u8F6C\u6D3E\u7406\u7531\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),i=u(),c=u([]);g({open:async d=>{t.value=!0,U(),l.value.id=d,c.value=await Q()}});const V=b,k=async()=>{if(i&&await i.value.validate()){n.value=!0;try{await G(l.value),t.value=!1,V("success")}finally{n.value=!1}}},U=()=>{var d;l.value={id:"",assigneeUserId:void 0,reason:""},(d=i.value)==null||d.resetFields()};return(d,s)=>{const w=K,I=L,f=N,F=O,T=P,p=Z,x=B,C=z;return _(),m(x,{modelValue:a(t),"onUpdate:modelValue":s[3]||(s[3]=e=>J(t)?t.value=e:null),title:"\u8F6C\u6D3E\u4EFB\u52A1",width:"500"},{footer:r(()=>[o(p,{disabled:a(n),type:"primary",onClick:k},{default:r(()=>[v("\u786E \u5B9A")]),_:1},8,["disabled"]),o(p,{onClick:s[2]||(s[2]=e=>t.value=!1)},{default:r(()=>[v("\u53D6 \u6D88")]),_:1})]),default:r(()=>[R((_(),m(T,{ref_key:"formRef",ref:i,model:a(l),rules:a(h),"label-width":"110px"},{default:r(()=>[o(f,{label:"\u65B0\u5BA1\u6279\u4EBA",prop:"assigneeUserId"},{default:r(()=>[o(I,{modelValue:a(l).assigneeUserId,"onUpdate:modelValue":s[0]||(s[0]=e=>a(l).assigneeUserId=e),clearable:"",style:{width:"100%"}},{default:r(()=>[(_(!0),j(H,null,D(a(c),e=>(_(),m(w,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(f,{label:"\u8F6C\u6D3E\u7406\u7531",prop:"reason"},{default:r(()=>[o(F,{modelValue:a(l).reason,"onUpdate:modelValue":s[1]||(s[1]=e=>a(l).reason=e),clearable:"",placeholder:"\u8BF7\u8F93\u5165\u8F6C\u6D3E\u7406\u7531"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[C,a(n)]])]),_:1},8,["modelValue"])}}})});export{y as _,W as __tla};
