import{d as j,n as B,I as G,r as m,f as H,o as i,l as n,w as u,i as s,a,j as f,H as D,c as K,F as h,k as U,dR as J,G as k,V as Z,t as z,y as Q,J as W,K as X,L as Y,Z as $,am as ee,an as ae,O as le,N as te,R as ue,__tla as se}from"./index-BUSn51wb.js";import{_ as re,__tla as oe}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{A as y,__tla as de}from"./index-BRuDnVkN.js";import{C as w}from"./constants-A8BI3pz7.js";let F,me=Promise.all([(()=>{try{return se}catch{}})(),(()=>{try{return oe}catch{}})(),(()=>{try{return de}catch{}})()]).then(async()=>{F=j({name:"ApiKeyForm",__name:"ApiKeyForm",emits:["success"],setup(ie,{expose:L,emit:q}){const{t:c}=B(),b=G(),o=m(!1),V=m(""),d=m(!1),g=m(""),t=m({id:void 0,name:void 0,apiKey:void 0,platform:void 0,url:void 0,status:w.ENABLE}),E=H({name:[{required:!0,message:"\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],apiKey:[{required:!0,message:"\u5BC6\u94A5\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],platform:[{required:!0,message:"\u5E73\u53F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),_=m();L({open:async(r,l)=>{if(o.value=!0,V.value=c("action."+r),g.value=r,C(),l){d.value=!0;try{t.value=await y.getApiKey(l)}finally{d.value=!1}}}});const I=q,R=async()=>{await _.value.validate(),d.value=!0;try{const r=t.value;g.value==="create"?(await y.createApiKey(r),b.success(c("common.createSuccess"))):(await y.updateApiKey(r),b.success(c("common.updateSuccess"))),o.value=!1,I("success")}finally{d.value=!1}},C=()=>{var r;t.value={id:void 0,name:void 0,apiKey:void 0,platform:void 0,url:void 0,status:w.ENABLE},(r=_.value)==null||r.resetFields()};return(r,l)=>{const M=W,N=X,p=Y,v=$,O=ee,P=ae,S=le,A=te,x=re,T=ue;return i(),n(x,{title:a(V),modelValue:a(o),"onUpdate:modelValue":l[6]||(l[6]=e=>Q(o)?o.value=e:null)},{footer:u(()=>[s(A,{onClick:R,type:"primary",disabled:a(d)},{default:u(()=>[f("\u786E \u5B9A")]),_:1},8,["disabled"]),s(A,{onClick:l[5]||(l[5]=e=>o.value=!1)},{default:u(()=>[f("\u53D6 \u6D88")]),_:1})]),default:u(()=>[D((i(),n(S,{ref_key:"formRef",ref:_,model:a(t),rules:a(E),"label-width":"120px"},{default:u(()=>[s(p,{label:"\u6240\u5C5E\u5E73\u53F0",prop:"platform"},{default:u(()=>[s(N,{modelValue:a(t).platform,"onUpdate:modelValue":l[0]||(l[0]=e=>a(t).platform=e),placeholder:"\u8BF7\u8F93\u5165\u5E73\u53F0",clearable:""},{default:u(()=>[(i(!0),K(h,null,U(a(J)(a(k).AI_PLATFORM),e=>(i(),n(M,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(p,{label:"\u540D\u79F0",prop:"name"},{default:u(()=>[s(v,{modelValue:a(t).name,"onUpdate:modelValue":l[1]||(l[1]=e=>a(t).name=e),placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0"},null,8,["modelValue"])]),_:1}),s(p,{label:"\u5BC6\u94A5",prop:"apiKey"},{default:u(()=>[s(v,{modelValue:a(t).apiKey,"onUpdate:modelValue":l[2]||(l[2]=e=>a(t).apiKey=e),placeholder:"\u8BF7\u8F93\u5165\u5BC6\u94A5"},null,8,["modelValue"])]),_:1}),s(p,{label:"\u81EA\u5B9A\u4E49 API URL",prop:"url"},{default:u(()=>[s(v,{modelValue:a(t).url,"onUpdate:modelValue":l[3]||(l[3]=e=>a(t).url=e),placeholder:"\u8BF7\u8F93\u5165\u81EA\u5B9A\u4E49 API URL"},null,8,["modelValue"])]),_:1}),s(p,{label:"\u72B6\u6001",prop:"status"},{default:u(()=>[s(P,{modelValue:a(t).status,"onUpdate:modelValue":l[4]||(l[4]=e=>a(t).status=e)},{default:u(()=>[(i(!0),K(h,null,U(a(Z)(a(k).COMMON_STATUS),e=>(i(),n(O,{key:e.value,label:e.value},{default:u(()=>[f(z(e.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[T,a(d)]])]),_:1},8,["title","modelValue"])}}})});export{F as _,me as __tla};
