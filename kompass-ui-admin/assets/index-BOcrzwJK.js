import{g as o,__tla as i}from"./index-CvQPhDtJ.js";import{d as p,r as l,at as _,o as c,c as m,a as d,__tla as y}from"./index-BUSn51wb.js";let n,u=Promise.all([(()=>{try{return i}catch{}})(),(()=>{try{return y}catch{}})()]).then(async()=>{let a;a=["innerHTML"],n=p({name:"PromotionArticle",__name:"index",props:{property:{}},setup(s){const t=s,r=l();return _(()=>t.property.id,async()=>{t.property.id&&(r.value=await o(t.property.id))},{immediate:!0}),(h,f)=>{var e;return c(),m("div",{class:"min-h-30px",innerHTML:(e=d(r))==null?void 0:e.content},null,8,a)}}})});export{u as __tla,n as default};
