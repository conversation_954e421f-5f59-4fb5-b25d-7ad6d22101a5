import{by as x,d as J,I as L,n as Q,r as i,f as Z,C as B,T as D,o as u,c as I,i as e,w as r,a,U as V,F as S,k as M,V as W,G as U,l as f,j as y,H as C,Z as X,L as $,J as ee,K as ae,_ as le,N as te,O as re,P as se,Q as oe,R as ne,__tla as ue}from"./index-BUSn51wb.js";import{_ as ce,__tla as pe}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as _e,__tla as ie}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as de,__tla as me}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as fe,__tla as ye}from"./index-COobLwz-.js";import{d as P,__tla as he}from"./formatTime-DWdBpgsM.js";import{__tla as ge}from"./index-Cch5e1V0.js";import"./color-BN7ZL7BD.js";import{__tla as be}from"./el-card-CJbXGyyg.js";let F,ke=Promise.all([(()=>{try{return ue}catch{}})(),(()=>{try{return pe}catch{}})(),(()=>{try{return ie}catch{}})(),(()=>{try{return me}catch{}})(),(()=>{try{return ye}catch{}})(),(()=>{try{return he}catch{}})(),(()=>{try{return ge}catch{}})(),(()=>{try{return be}catch{}})()]).then(async()=>{F=J({name:"SystemTokenClient",__name:"index",setup(Te){const h=L(),{t:N}=Q(),d=i(!0),g=i(0),b=i([]),s=Z({pageNo:1,pageSize:10,userId:null,userType:void 0,clientId:null}),k=i(),c=async()=>{d.value=!0;try{const t=await(n=s,x.get({url:"/system/oauth2-token/page",params:n}));b.value=t.list,g.value=t.total}finally{d.value=!1}var n},p=()=>{s.pageNo=1,c()},E=()=>{k.value.resetFields(),p()},K=async n=>{try{await h.confirm("\u662F\u5426\u8981\u5F3A\u5236\u9000\u51FA\u7528\u6237"),await(t=>x.delete({url:"/system/oauth2-token/delete?accessToken="+t}))(n),h.success(N("common.success")),await c()}catch{}};return B(()=>{c()}),(n,t)=>{const R=fe,T=X,_=$,z=ee,O=ae,w=le,m=te,Y=re,v=de,o=se,j=_e,q=oe,A=ce,G=D("hasPermi"),H=ne;return u(),I(S,null,[e(R,{title:"OAuth 2.0\uFF08SSO \u5355\u70B9\u767B\u5F55)",url:"https://doc.iocoder.cn/oauth2/"}),e(v,null,{default:r(()=>[e(Y,{class:"-mb-15px",model:a(s),ref_key:"queryFormRef",ref:k,inline:!0,"label-width":"90px"},{default:r(()=>[e(_,{label:"\u7528\u6237\u7F16\u53F7",prop:"userId"},{default:r(()=>[e(T,{modelValue:a(s).userId,"onUpdate:modelValue":t[0]||(t[0]=l=>a(s).userId=l),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u7F16\u53F7",clearable:"",onKeyup:V(p,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(_,{label:"\u7528\u6237\u7C7B\u578B",prop:"userType"},{default:r(()=>[e(O,{modelValue:a(s).userType,"onUpdate:modelValue":t[1]||(t[1]=l=>a(s).userType=l),placeholder:"\u8BF7\u9009\u62E9\u7528\u6237\u7C7B\u578B",clearable:"",class:"!w-240px"},{default:r(()=>[(u(!0),I(S,null,M(a(W)(a(U).USER_TYPE),l=>(u(),f(z,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(_,{label:"\u5BA2\u6237\u7AEF\u7F16\u53F7",prop:"clientId"},{default:r(()=>[e(T,{modelValue:a(s).clientId,"onUpdate:modelValue":t[2]||(t[2]=l=>a(s).clientId=l),placeholder:"\u8BF7\u8F93\u5165\u5BA2\u6237\u7AEF\u7F16\u53F7",clearable:"",onKeyup:V(p,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(_,null,{default:r(()=>[e(m,{onClick:p},{default:r(()=>[e(w,{icon:"ep:search",class:"mr-5px"}),y(" \u641C\u7D22")]),_:1}),e(m,{onClick:E},{default:r(()=>[e(w,{icon:"ep:refresh",class:"mr-5px"}),y(" \u91CD\u7F6E")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(v,null,{default:r(()=>[C((u(),f(q,{data:a(b)},{default:r(()=>[e(o,{label:"\u8BBF\u95EE\u4EE4\u724C",align:"center",prop:"accessToken",width:"300"}),e(o,{label:"\u5237\u65B0\u4EE4\u724C",align:"center",prop:"refreshToken",width:"300"}),e(o,{label:"\u7528\u6237\u7F16\u53F7",align:"center",prop:"userId"}),e(o,{label:"\u7528\u6237\u7C7B\u578B",align:"center",prop:"userType"},{default:r(l=>[e(j,{type:a(U).USER_TYPE,value:l.row.userType},null,8,["type","value"])]),_:1}),e(o,{label:"\u8FC7\u671F\u65F6\u95F4",align:"center",prop:"expiresTime",formatter:a(P),width:"180"},null,8,["formatter"]),e(o,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:a(P),width:"180"},null,8,["formatter"]),e(o,{label:"\u64CD\u4F5C",align:"center"},{default:r(l=>[C((u(),f(m,{link:"",type:"danger",onClick:we=>K(l.row.accessToken)},{default:r(()=>[y(" \u5F3A\u9000 ")]),_:2},1032,["onClick"])),[[G,["system:oauth2-token:delete"]]])]),_:1})]),_:1},8,["data"])),[[H,a(d)]]),e(A,{total:a(g),page:a(s).pageNo,"onUpdate:page":t[3]||(t[3]=l=>a(s).pageNo=l),limit:a(s).pageSize,"onUpdate:limit":t[4]||(t[4]=l=>a(s).pageSize=l),onPagination:c},null,8,["total","page","limit"])]),_:1})],64)}}})});export{ke as __tla,F as default};
