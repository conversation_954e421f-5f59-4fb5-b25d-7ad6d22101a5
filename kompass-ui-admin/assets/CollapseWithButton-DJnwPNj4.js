import{d as x,r as p,at as B,C as S,bt as $,o as i,c as d,g as f,aV as r,av as P,ay as j,B as m,i as u,w as h,a as z,j as w,t as I,ao as E,_ as L,N as R,__tla as W}from"./index-BUSn51wb.js";let T,H=Promise.all([(()=>{try{return W}catch{}})()]).then(async()=>{let v,_,y,C,b,g;v={class:"collapse-container"},_=m(x({name:"CollapseTransition",__name:"CollapseTransition",props:{isCollapsed:{type:Boolean,default:!0}},setup(a){const c=a,s=p(null),e=p(null),o=p(0),t=()=>{e.value&&(o.value=e.value.offsetHeight)};return B(()=>c.isCollapsed,(n,l)=>{!n&&l&&j(()=>{t()})}),S(()=>{t(),window.addEventListener("resize",t)}),$(()=>{window.removeEventListener("resize",t)}),(n,l)=>(i(),d("div",v,[f("div",{ref_key:"contentRef",ref:s,class:"collapse-content",style:P({height:a.isCollapsed?"0px":o.value+"px",overflow:"hidden",transition:"height 0.3s ease"})},[f("div",{ref_key:"innerContentRef",ref:e},[r(n.$slots,"default",{},void 0,!0)],512)],4)]))}}),[["__scopeId","data-v-0b4cbd32"]]),y={class:"collapse-with-button"},C={class:"collapse-button-container"},b={key:0},g={key:1},T=m(x({name:"CollapseWithButton",__name:"CollapseWithButton",props:{defaultCollapsed:{type:Boolean,default:!0},expandText:{type:String,default:"\u5C55\u5F00"},collapseText:{type:String,default:"\u6536\u8D77"},expandIcon:{type:String,default:"ep:arrow-down"},collapseIcon:{type:String,default:"ep:arrow-up"},buttonProps:{type:Object,default:()=>({})},buttonContainerClass:{type:String,default:""}},emits:["update:collapsed","toggle"],setup(a,{emit:c}){const s=c,e=p(a.defaultCollapsed),o=()=>{e.value=!e.value,s("update:collapsed",e.value),s("toggle",e.value)};return(t,n)=>{const l=L,k=R;return i(),d("div",y,[u(z(_),{"is-collapsed":e.value},{default:h(()=>[r(t.$slots,"default",{},void 0,!0)]),_:3},8,["is-collapsed"]),f("div",C,[r(t.$slots,"before-button",{},void 0,!0),u(k,E({onClick:o,plain:!0},a.buttonProps),{default:h(()=>[e.value?(i(),d("span",b,[u(l,{icon:a.expandIcon,class:"mr-5px"},null,8,["icon"]),w(I(a.expandText),1)])):(i(),d("span",g,[u(l,{icon:a.collapseIcon,class:"mr-5px"},null,8,["icon"]),w(I(a.collapseText),1)]))]),_:1},16),r(t.$slots,"after-button",{},void 0,!0)])])}}}),[["__scopeId","data-v-ac80758b"]])});export{T as C,H as __tla};
