import{d as ae,I as re,r as _,f as te,C as le,T as oe,o as p,c as ne,i as e,w as r,a,U as de,j as c,H as O,l as m,a9 as w,F as ie,Z as se,L as _e,J as pe,K as ce,M as ue,_ as me,N as be,O as fe,P as ge,ce as he,Q as ke,R as we,__tla as ye}from"./index-BUSn51wb.js";import{_ as xe,__tla as Ue}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{E as ve,a as Ee,b as Ve,__tla as Ce}from"./el-dropdown-item-CIJXMVYa.js";import{E as Te,__tla as Be}from"./el-avatar-Da2TGjmj.js";import{_ as Ie,__tla as Pe}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as De,__tla as Fe}from"./index-COobLwz-.js";import{d as q,__tla as Oe}from"./formatTime-DWdBpgsM.js";import{g as qe,c as ze,b as Ne,__tla as Re}from"./index-DnKHynsa.js";import{c as b,__tla as Le}from"./permission-DQXm2BCV.js";import{f as y,__tla as Se}from"./formatter-DVQ2wbhT.js";import{_ as Ye,__tla as $e}from"./UpdateBindUserForm.vue_vue_type_script_setup_true_lang-wVxUeQsW.js";import{_ as He,__tla as Me}from"./BrokerageUserListDialog.vue_vue_type_script_setup_true_lang-S0qjivL4.js";import{_ as Ke,__tla as je}from"./BrokerageOrderListDialog.vue_vue_type_script_setup_true_lang-D3yJw0C4.js";import{__tla as Je}from"./index-Cch5e1V0.js";import{__tla as Qe}from"./el-card-CJbXGyyg.js";import{__tla as Ze}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as Ae}from"./el-descriptions-item-dD3qa0ub.js";import{__tla as Ge}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import"./color-BN7ZL7BD.js";import{__tla as We}from"./index-DjHkScYI.js";import"./constants-A8BI3pz7.js";let z,Xe=Promise.all([(()=>{try{return ye}catch{}})(),(()=>{try{return Ue}catch{}})(),(()=>{try{return Ce}catch{}})(),(()=>{try{return Be}catch{}})(),(()=>{try{return Pe}catch{}})(),(()=>{try{return Fe}catch{}})(),(()=>{try{return Oe}catch{}})(),(()=>{try{return Re}catch{}})(),(()=>{try{return Le}catch{}})(),(()=>{try{return Se}catch{}})(),(()=>{try{return $e}catch{}})(),(()=>{try{return Me}catch{}})(),(()=>{try{return je}catch{}})(),(()=>{try{return Je}catch{}})(),(()=>{try{return Qe}catch{}})(),(()=>{try{return Ze}catch{}})(),(()=>{try{return Ae}catch{}})(),(()=>{try{return Ge}catch{}})(),(()=>{try{return We}catch{}})()]).then(async()=>{z=ae({name:"TradeBrokerageUser",__name:"index",setup(ea){const f=re(),x=_(!0),V=_(0),C=_([]),o=te({pageNo:1,pageSize:10,bindUserId:null,brokerageEnabled:!0,createTime:[]}),T=_(),u=async()=>{x.value=!0;try{const n=await qe(o);C.value=n.list,V.value=n.total}finally{x.value=!1}},U=()=>{o.pageNo=1,u()},N=()=>{T.value.resetFields(),U()},B=_(),R=n=>{B.value.open(n)},I=_(),L=n=>{I.value.open(n)},P=_(),S=n=>{P.value.open(n)},Y=async n=>{try{await f.confirm(`\u786E\u8BA4\u8981\u6E05\u9664"${n.nickname}"\u7684\u4E0A\u7EA7\u63A8\u5E7F\u4EBA\u5417\uFF1F`),await ze({id:n.id}),f.success("\u6E05\u9664\u6210\u529F"),await u()}catch{}};return le(()=>{u()}),(n,d)=>{const $=De,H=se,g=_e,D=pe,M=ce,K=ue,v=me,E=be,j=fe,F=Ie,l=ge,J=Te,Q=he,h=ve,Z=Ee,A=Ve,G=ke,W=xe,X=oe("hasPermi"),ee=we;return p(),ne(ie,null,[e($,{title:"\u3010\u4EA4\u6613\u3011\u5206\u9500\u8FD4\u4F63",url:"https://doc.iocoder.cn/mall/trade-brokerage/"}),e(F,null,{default:r(()=>[e(j,{class:"-mb-15px",model:a(o),ref_key:"queryFormRef",ref:T,inline:!0,"label-width":"85px"},{default:r(()=>[e(g,{label:"\u63A8\u5E7F\u5458\u7F16\u53F7",prop:"bindUserId"},{default:r(()=>[e(H,{modelValue:a(o).bindUserId,"onUpdate:modelValue":d[0]||(d[0]=t=>a(o).bindUserId=t),placeholder:"\u8BF7\u8F93\u5165\u63A8\u5E7F\u5458\u7F16\u53F7",clearable:"",onKeyup:de(U,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(g,{label:"\u63A8\u5E7F\u8D44\u683C",prop:"brokerageEnabled"},{default:r(()=>[e(M,{modelValue:a(o).brokerageEnabled,"onUpdate:modelValue":d[1]||(d[1]=t=>a(o).brokerageEnabled=t),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u63A8\u5E7F\u8D44\u683C"},{default:r(()=>[e(D,{label:"\u6709",value:!0}),e(D,{label:"\u65E0",value:!1})]),_:1},8,["modelValue"])]),_:1}),e(g,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:r(()=>[e(K,{modelValue:a(o).createTime,"onUpdate:modelValue":d[2]||(d[2]=t=>a(o).createTime=t),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),e(g,null,{default:r(()=>[e(E,{onClick:U},{default:r(()=>[e(v,{icon:"ep:search",class:"mr-5px"}),c(" \u641C\u7D22")]),_:1}),e(E,{onClick:N},{default:r(()=>[e(v,{icon:"ep:refresh",class:"mr-5px"}),c(" \u91CD\u7F6E")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(F,null,{default:r(()=>[O((p(),m(G,{data:a(C),stripe:!0,"show-overflow-tooltip":!0},{default:r(()=>[e(l,{label:"\u7528\u6237\u7F16\u53F7",align:"center",prop:"id","min-width":"80px"}),e(l,{label:"\u5934\u50CF",align:"center",prop:"avatar",width:"70px"},{default:r(t=>[e(J,{src:t.row.avatar},null,8,["src"])]),_:1}),e(l,{label:"\u6635\u79F0",align:"center",prop:"nickname","min-width":"80px"}),e(l,{label:"\u63A8\u5E7F\u4EBA\u6570",align:"center",prop:"brokerageUserCount",width:"80px"}),e(l,{label:"\u63A8\u5E7F\u8BA2\u5355\u6570\u91CF",align:"center",prop:"brokerageOrderCount","min-width":"110px"}),e(l,{label:"\u63A8\u5E7F\u8BA2\u5355\u91D1\u989D",align:"center",prop:"brokerageOrderPrice","min-width":"110px",formatter:a(y)},null,8,["formatter"]),e(l,{label:"\u5DF2\u63D0\u73B0\u91D1\u989D",align:"center",prop:"withdrawPrice","min-width":"100px",formatter:a(y)},null,8,["formatter"]),e(l,{label:"\u5DF2\u63D0\u73B0\u6B21\u6570",align:"center",prop:"withdrawCount","min-width":"100px"}),e(l,{label:"\u672A\u63D0\u73B0\u91D1\u989D",align:"center",prop:"price","min-width":"100px",formatter:a(y)},null,8,["formatter"]),e(l,{label:"\u51BB\u7ED3\u4E2D\u4F63\u91D1",align:"center",prop:"frozenPrice","min-width":"100px",formatter:a(y)},null,8,["formatter"]),e(l,{label:"\u63A8\u5E7F\u8D44\u683C",align:"center",prop:"brokerageEnabled","min-width":"80px"},{default:r(t=>[e(Q,{modelValue:t.row.brokerageEnabled,"onUpdate:modelValue":k=>t.row.brokerageEnabled=k,"active-text":"\u6709","inactive-text":"\u65E0","inline-prompt":"",disabled:!a(b)(["trade:brokerage-user:update-bind-user"]),onChange:k=>(async i=>{try{const s=i.brokerageEnabled?"\u5F00\u901A":"\u5173\u95ED";await f.confirm(`\u786E\u8BA4\u8981${s}"${i.nickname}"\u7684\u63A8\u5E7F\u8D44\u683C\u5417\uFF1F`),await Ne({id:i.id,enabled:i.brokerageEnabled}),f.success(s+"\u6210\u529F"),await u()}catch{i.brokerageEnabled=!i.brokerageEnabled}})(t.row)},null,8,["modelValue","onUpdate:modelValue","disabled","onChange"])]),_:1}),e(l,{label:"\u6210\u4E3A\u63A8\u5E7F\u5458\u65F6\u95F4",align:"center",prop:"brokerageTime",formatter:a(q),width:"180px"},null,8,["formatter"]),e(l,{label:"\u4E0A\u7EA7\u63A8\u5E7F\u5458\u7F16\u53F7",align:"center",prop:"bindUserId",width:"150px"}),e(l,{label:"\u63A8\u5E7F\u5458\u7ED1\u5B9A\u65F6\u95F4",align:"center",prop:"bindUserTime",formatter:a(q),width:"180px"},null,8,["formatter"]),e(l,{label:"\u64CD\u4F5C",align:"center",width:"150px",fixed:"right"},{default:r(t=>[O((p(),m(A,{onCommand:k=>((i,s)=>{switch(i){case"openBrokerageUserTable":R(s.id);break;case"openBrokerageOrderTable":L(s.id);break;case"openUpdateBindUserForm":S(s);break;case"handleClearBindUser":Y(s)}})(k,t.row)},{dropdown:r(()=>[e(Z,null,{default:r(()=>[a(b)(["trade:brokerage-user:user-query"])?(p(),m(h,{key:0,command:"openBrokerageUserTable"},{default:r(()=>[c(" \u63A8\u5E7F\u4EBA ")]),_:1})):w("",!0),a(b)(["trade:brokerage-user:order-query"])?(p(),m(h,{key:1,command:"openBrokerageOrderTable"},{default:r(()=>[c(" \u63A8\u5E7F\u8BA2\u5355 ")]),_:1})):w("",!0),a(b)(["trade:brokerage-user:update-bind-user"])?(p(),m(h,{key:2,command:"openUpdateBindUserForm"},{default:r(()=>[c(" \u4FEE\u6539\u4E0A\u7EA7\u63A8\u5E7F\u4EBA ")]),_:1})):w("",!0),t.row.bindUserId&&a(b)(["trade:brokerage-user:clear-bind-user"])?(p(),m(h,{key:3,command:"handleClearBindUser"},{default:r(()=>[c(" \u6E05\u9664\u4E0A\u7EA7\u63A8\u5E7F\u4EBA ")]),_:1})):w("",!0)]),_:2},1024)]),default:r(()=>[e(E,{link:"",type:"primary"},{default:r(()=>[e(v,{icon:"ep:d-arrow-right"}),c(" \u66F4\u591A ")]),_:1})]),_:2},1032,["onCommand"])),[[X,["trade:brokerage-user:user-query","trade:brokerage-user:order-query","trade:brokerage-user:update-bind-user","trade:brokerage-user:clear-bind-user"]]])]),_:1})]),_:1},8,["data"])),[[ee,a(x)]]),e(W,{total:a(V),page:a(o).pageNo,"onUpdate:page":d[3]||(d[3]=t=>a(o).pageNo=t),limit:a(o).pageSize,"onUpdate:limit":d[4]||(d[4]=t=>a(o).pageSize=t),onPagination:u},null,8,["total","page","limit"])]),_:1}),e(Ye,{ref_key:"updateBindUserFormRef",ref:P,onSuccess:u},null,512),e(He,{ref_key:"brokerageUserListDialogRef",ref:B},null,512),e(Ke,{ref_key:"brokerageOrderListDialogRef",ref:I},null,512)],64)}}})});export{Xe as __tla,z as default};
