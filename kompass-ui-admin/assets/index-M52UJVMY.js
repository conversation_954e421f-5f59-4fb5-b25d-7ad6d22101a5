import{by as s,__tla as b}from"./index-BUSn51wb.js";let t,e,r,c,u,i,n,l,m,p,y,d=Promise.all([(()=>{try{return b}catch{}})()]).then(async()=>{i=async a=>await s.get({url:"/crm/business/page",params:a}),n=async a=>await s.get({url:"/crm/business/page-by-customer",params:a}),t=async a=>await s.get({url:"/crm/business/get?id="+a}),c=async()=>await s.get({url:"/crm/business/simple-all-list"}),r=async a=>await s.post({url:"/crm/business/create",data:a}),y=async a=>await s.put({url:"/crm/business/update",data:a}),u=async a=>await s.put({url:"/crm/business/update-status",data:a}),l=async a=>await s.delete({url:"/crm/business/delete?id="+a}),m=async a=>await s.download({url:"/crm/business/export-excel",params:a}),e=async a=>await s.get({url:"/crm/business/page-by-contact",params:a}),p=async a=>await s.put({url:"/crm/business/transfer",data:a})});export{d as __tla,t as a,e as b,r as c,c as d,u as e,i as f,n as g,l as h,m as i,p as t,y as u};
