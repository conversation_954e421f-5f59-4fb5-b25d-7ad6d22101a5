import{d as b,r as g,o as e,c as l,g as f,a as o,F as y,k as C,i as r,w as m,av as w,t as z,a9 as c,a0 as d,_ as A,N as B,B as F,__tla as j}from"./index-BUSn51wb.js";import{E,__tla as I}from"./el-image-BjHZRFih.js";let v,N=Promise.all([(()=>{try{return j}catch{}})(),(()=>{try{return I}catch{}})()]).then(async()=>{let i,n,p;i=["onClick"],n={class:"h-full w-full flex items-center justify-center"},p=b({name:"FloatingActionButton",__name:"index",props:{property:{}},setup(P){const t=g(!0),x=()=>{t.value=!t.value};return(a,T)=>{const _=A,h=E,k=B;return e(),l(y,null,[f("div",{class:d(["absolute bottom-32px right-[calc(50%-375px/2+32px)] flex z-12 gap-12px items-center",{"flex-row":a.property.direction==="horizontal","flex-col":a.property.direction==="vertical"}])},[o(t)?(e(!0),l(y,{key:0},C(a.property.list,(s,u)=>(e(),l("div",{key:u,class:"flex flex-col items-center",onClick:U=>a.handleActive(u)},[r(h,{src:s.imgUrl,fit:"contain",class:"h-27px w-27px"},{error:m(()=>[f("div",n,[r(_,{icon:"ep:picture",color:s.textColor},null,8,["color"])])]),_:2},1032,["src"]),a.property.showText?(e(),l("span",{key:0,class:"mt-4px text-12px",style:w({color:s.textColor})},z(s.text),5)):c("",!0)],8,i))),128)):c("",!0),r(k,{type:"primary",size:"large",circle:"",onClick:x},{default:m(()=>[r(_,{icon:"ep:plus",class:d(["fab-icon",{active:o(t)}])},null,8,["class"])]),_:1})],2),o(t)?(e(),l("div",{key:0,class:"modal-bg",onClick:x})):c("",!0)],64)}}}),v=F(p,[["__scopeId","data-v-ff4b3b24"]])});export{N as __tla,v as default};
