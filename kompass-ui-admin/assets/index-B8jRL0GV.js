import{by as t,__tla as n}from"./index-BUSn51wb.js";let d,e=Promise.all([(()=>{try{return n}catch{}})()]).then(async()=>{d={getBindPage:async a=>await t.get({url:"/als/bind/page",params:a}),getBind:async a=>await t.get({url:"/als/bind/get?id="+a}),createBind:async a=>await t.post({url:"/als/bind/create",data:a}),updateBind:async a=>await t.put({url:"/als/bind/update",data:a}),deleteBind:async a=>await t.delete({url:"/als/bind/delete?id="+a}),unbind:async a=>await t.get({url:"/als/bind/unbind?id="+a}),unbindAudit:async a=>await t.put({url:"/als/bind/unbindAudit",data:a}),exportBind:async a=>await t.download({url:"/als/bind/export-excel",params:a})}});export{d as B,e as __tla};
