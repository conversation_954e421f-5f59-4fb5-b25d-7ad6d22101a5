import{d as x,r as m,f as b,C as v,o as c,c as k,i as a,w as r,a as n,H as A,l as P,F as C,em as F,P as R,Q as q,R as E,__tla as I}from"./index-BUSn51wb.js";import{E as j,__tla as D}from"./el-card-CJbXGyyg.js";import{E as H,__tla as L}from"./el-skeleton-item-tDN8U6BH.js";import{_ as N,__tla as Q}from"./Echart.vue_vue_type_script_setup_true_lang-DnLZLH3w.js";import{S,__tla as X}from"./rank-CaJ4xEN0.js";let u,Z=Promise.all([(()=>{try{return I}catch{}})(),(()=>{try{return D}catch{}})(),(()=>{try{return L}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return X}catch{}})()]).then(async()=>{u=x({name:"FollowCountRank",__name:"FollowCountRank",props:{queryParams:{}},setup(d,{expose:p}){const y=d,t=m(!1),l=m([]),e=b({dataset:{dimensions:["nickname","count"],source:[]},grid:{left:20,right:20,bottom:20,containLabel:!0},legend:{top:50},series:[{name:"\u8DDF\u8FDB\u6B21\u6570\u6392\u884C",type:"bar"}],toolbox:{feature:{dataZoom:{yAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u8DDF\u8FDB\u6B21\u6570\u6392\u884C"}}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},xAxis:{type:"value",name:"\u8DDF\u8FDB\u6B21\u6570\uFF08\u6B21\uFF09"},yAxis:{type:"category",name:"\u5458\u5DE5"}}),i=async()=>{t.value=!0;const o=await S.getFollowCountRank(y.queryParams);e.dataset&&e.dataset.source&&(e.dataset.source=F(o).reverse()),l.value=o,t.value=!1};return p({loadData:i}),v(()=>{i()}),(o,z)=>{const h=N,g=H,_=j,s=R,f=q,w=E;return c(),k(C,null,[a(_,{shadow:"never"},{default:r(()=>[a(g,{loading:n(t),animated:""},{default:r(()=>[a(h,{height:500,options:n(e)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),a(_,{shadow:"never",class:"mt-16px"},{default:r(()=>[A((c(),P(f,{data:n(l)},{default:r(()=>[a(s,{label:"\u516C\u53F8\u6392\u540D",align:"center",type:"index",width:"80"}),a(s,{label:"\u5458\u5DE5",align:"center",prop:"nickname","min-width":"200"}),a(s,{label:"\u90E8\u95E8",align:"center",prop:"deptName","min-width":"200"}),a(s,{label:"\u8DDF\u8FDB\u6B21\u6570\uFF08\u6B21\uFF09",align:"center",prop:"count","min-width":"200"})]),_:1},8,["data"])),[[w,n(t)]])]),_:1})],64)}}})});export{u as _,Z as __tla};
