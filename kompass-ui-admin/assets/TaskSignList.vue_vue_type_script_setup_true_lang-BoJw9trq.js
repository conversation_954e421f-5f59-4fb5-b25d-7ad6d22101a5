import{d as z,r as p,o as u,l as f,w as l,g as D,t as c,a as s,i as a,j as d,a9 as v,G as L,y as B,I as E,aA as U,_ as F,N as G,P as I,Q as K,__tla as M}from"./index-BUSn51wb.js";import{E as Q,__tla as R}from"./el-drawer-DMK0hKF6.js";import{_ as q,__tla as H}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{d as b,__tla as J}from"./formatTime-DWdBpgsM.js";import{_ as O,__tla as W}from"./TaskSignDeleteForm.vue_vue_type_script_setup_true_lang-S0I1ToNC.js";let S,X=Promise.all([(()=>{try{return M}catch{}})(),(()=>{try{return R}catch{}})(),(()=>{try{return H}catch{}})(),(()=>{try{return J}catch{}})(),(()=>{try{return W}catch{}})()]).then(async()=>{S=z({name:"TaskSignList",__name:"TaskSignList",emits:["success"],setup(Y,{expose:T,emit:x}){const A=E(),_=p(!1),n=p({});T({open:async t=>{U(t.children)?A.warning("\u8BE5\u4EFB\u52A1\u6CA1\u6709\u5B50\u4EFB\u52A1"):(n.value=t,_.value=!0)}});const h=p(),N=x,w=t=>{h.value.open(t.id)},C=()=>{N("success"),_.value=!1},y=t=>t&&t.children&&!U(t.children);return(t,m)=>{const g=F,k=G,i=I,V=q,P=K,j=Q;return u(),f(j,{modelValue:s(_),"onUpdate:modelValue":m[1]||(m[1]=e=>B(_)?_.value=e:null),title:"\u5B50\u4EFB\u52A1",size:"880px"},{header:l(()=>{var e,r;return[D("h4",null,"\u3010"+c(s(n).name)+" \u3011\u5BA1\u6279\u4EBA\uFF1A"+c((r=(e=s(n))==null?void 0:e.assigneeUser)==null?void 0:r.nickname),1),y(s(n))?(u(),f(k,{key:0,style:{"margin-left":"5px"},type:"danger",plain:"",onClick:m[0]||(m[0]=o=>w(s(n)))},{default:l(()=>[a(g,{icon:"ep:remove"}),d(" \u51CF\u7B7E ")]),_:1})):v("",!0)]}),default:l(()=>[a(P,{data:s(n).children,style:{width:"100%"},"row-key":"id",border:""},{default:l(()=>[a(i,{prop:"assigneeUser.nickname",label:"\u5BA1\u6279\u4EBA","min-width":"100"},{default:l(e=>{var r,o;return[d(c(((r=e.row.assigneeUser)==null?void 0:r.nickname)||((o=e.row.ownerUser)==null?void 0:o.nickname)),1)]}),_:1}),a(i,{prop:"assigneeUser.deptName",label:"\u6240\u5728\u90E8\u95E8","min-width":"100"},{default:l(e=>{var r,o;return[d(c(((r=e.row.assigneeUser)==null?void 0:r.deptName)||((o=e.row.ownerUser)==null?void 0:o.deptName)),1)]}),_:1}),a(i,{label:"\u5BA1\u6279\u72B6\u6001",prop:"status",width:"120"},{default:l(e=>[a(V,{type:s(L).BPM_TASK_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),a(i,{label:"\u63D0\u4EA4\u65F6\u95F4",align:"center",prop:"createTime",width:"180",formatter:s(b)},null,8,["formatter"]),a(i,{label:"\u7ED3\u675F\u65F6\u95F4",align:"center",prop:"endTime",width:"180",formatter:s(b)},null,8,["formatter"]),a(i,{label:"\u64CD\u4F5C",prop:"operation",width:"90"},{default:l(e=>[y(e.row)?(u(),f(k,{key:0,type:"danger",plain:"",size:"small",onClick:r=>w(e.row)},{default:l(()=>[a(g,{icon:"ep:remove"}),d(" \u51CF\u7B7E ")]),_:2},1032,["onClick"])):v("",!0)]),_:1})]),_:1},8,["data"]),a(O,{ref_key:"taskSignDeleteFormRef",ref:h,onSuccess:C},null,512)]),_:1},8,["modelValue"])}}})});export{S as _,X as __tla};
