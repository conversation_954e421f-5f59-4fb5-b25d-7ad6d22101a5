import{by as a,__tla as m}from"./index-BUSn51wb.js";let t,e,u,r,n,c,i,l,p=Promise.all([(()=>{try{return m}catch{}})()]).then(async()=>{t=[{endStatus:1,key:"\u7ED3\u675F",name:"\u8D62\u5355",percent:100},{endStatus:2,key:"\u7ED3\u675F",name:"\u8F93\u5355",percent:0},{endStatus:3,key:"\u7ED3\u675F",name:"\u65E0\u6548",percent:0}],n=async s=>await a.get({url:"/crm/business-status/page",params:s}),r=async s=>await a.post({url:"/crm/business-status/create",data:s}),l=async s=>await a.put({url:"/crm/business-status/update",data:s}),u=async s=>await a.get({url:"/crm/business-status/get?id="+s}),c=async s=>await a.delete({url:"/crm/business-status/delete?id="+s}),i=async()=>await a.get({url:"/crm/business-status/type-simple-list"}),e=async s=>await a.get({url:"/crm/business-status/status-simple-list",params:{typeId:s}})});export{t as D,p as __tla,e as a,u as b,r as c,n as d,c as e,i as g,l as u};
