import{d as L,n as O,I as q,r as o,f as z,o as g,l as C,w as l,i as t,a as u,j as m,H as D,t as I,y as h,ax as P,L as X,ce as G,eh as J,O as Q,N as T,R as W,B as Y,__tla as Z}from"./index-BUSn51wb.js";import{_ as $,__tla as ee}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{E as ae,__tla as le}from"./el-card-CJbXGyyg.js";import{d as te,h as se}from"./tree-BMa075Oj.js";import{g as ue,__tla as de}from"./index-B77mwhR6.js";import{g as oe,a as ne,__tla as re}from"./index-CODXyRlK.js";let V,ce=Promise.all([(()=>{try{return Z}catch{}})(),(()=>{try{return ee}catch{}})(),(()=>{try{return le}catch{}})(),(()=>{try{return de}catch{}})(),(()=>{try{return re}catch{}})()]).then(async()=>{V=Y(L({name:"SystemRoleAssignMenuForm",__name:"RoleAssignMenuForm",emits:["success"],setup(ie,{expose:w,emit:b}){const{t:R}=O(),F=q(),n=o(!1),r=o(!1),s=z({id:void 0,name:"",code:"",menuIds:[]}),f=o(),p=o([]),c=o(!1),d=o(),_=o(!1);w({open:async a=>{n.value=!0,N(),p.value=se(await ue()),s.id=a.id,s.name=a.name,s.code=a.code,r.value=!0;try{s.value.menuIds=await oe(a.id),s.value.menuIds.forEach(e=>{d.value.setChecked(e,!0,!1)})}finally{r.value=!1}}});const H=b,M=async()=>{if(f&&await f.value.validate()){r.value=!0;try{const a={roleId:s.id,menuIds:[...d.value.getCheckedKeys(!1),...d.value.getHalfCheckedKeys()]};await ne(a),F.success(R("common.updateSuccess")),n.value=!1,H("success")}finally{r.value=!1}}},N=()=>{var a,e;_.value=!1,c.value=!1,s.value={id:void 0,name:"",code:"",menuIds:[]},(a=d.value)==null||a.setCheckedNodes([]),(e=f.value)==null||e.resetFields()},U=()=>{d.value.setCheckedNodes(_.value?p.value:[])},j=()=>{var e;const a=(e=d.value)==null?void 0:e.store.nodesMap;for(let v in a)a[v].expanded!==c.value&&(a[v].expanded=c.value)};return(a,e)=>{const v=P,y=X,k=G,A=J,E=ae,K=Q,x=T,S=$,B=W;return g(),C(S,{modelValue:u(n),"onUpdate:modelValue":e[3]||(e[3]=i=>h(n)?n.value=i:null),title:"\u83DC\u5355\u6743\u9650"},{footer:l(()=>[t(x,{disabled:u(r),type:"primary",onClick:M},{default:l(()=>[m("\u786E \u5B9A")]),_:1},8,["disabled"]),t(x,{onClick:e[2]||(e[2]=i=>n.value=!1)},{default:l(()=>[m("\u53D6 \u6D88")]),_:1})]),default:l(()=>[D((g(),C(K,{ref_key:"formRef",ref:f,model:u(s),"label-width":"80px"},{default:l(()=>[t(y,{label:"\u89D2\u8272\u540D\u79F0"},{default:l(()=>[t(v,null,{default:l(()=>[m(I(u(s).name),1)]),_:1})]),_:1}),t(y,{label:"\u89D2\u8272\u6807\u8BC6"},{default:l(()=>[t(v,null,{default:l(()=>[m(I(u(s).code),1)]),_:1})]),_:1}),t(y,{label:"\u83DC\u5355\u6743\u9650"},{default:l(()=>[t(E,{class:"cardHeight"},{header:l(()=>[m(" \u5168\u9009/\u5168\u4E0D\u9009: "),t(k,{modelValue:u(_),"onUpdate:modelValue":e[0]||(e[0]=i=>h(_)?_.value=i:null),"active-text":"\u662F","inactive-text":"\u5426","inline-prompt":"",onChange:U},null,8,["modelValue"]),m(" \u5168\u90E8\u5C55\u5F00/\u6298\u53E0: "),t(k,{modelValue:u(c),"onUpdate:modelValue":e[1]||(e[1]=i=>h(c)?c.value=i:null),"active-text":"\u5C55\u5F00","inactive-text":"\u6298\u53E0","inline-prompt":"",onChange:j},null,8,["modelValue"])]),default:l(()=>[t(A,{ref_key:"treeRef",ref:d,data:u(p),props:u(te),"empty-text":"\u52A0\u8F7D\u4E2D\uFF0C\u8BF7\u7A0D\u5019","node-key":"id","show-checkbox":""},null,8,["data","props"])]),_:1})]),_:1})]),_:1},8,["model"])),[[B,u(r)]])]),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-a5ae28aa"]])});export{ce as __tla,V as default};
