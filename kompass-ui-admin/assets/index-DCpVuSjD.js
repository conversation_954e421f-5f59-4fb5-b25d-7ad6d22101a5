import{by as h,d as N,I as O,n as S,r as y,f as A,C as K,T as W,o as d,c as _,i as e,w as t,H as x,l as D,a as l,g as X,j as r,a9 as w,F as B,N as G,am as J,an as M,L as Q,cc as Y,O as Z,R as $,__tla as aa}from"./index-BUSn51wb.js";import{_ as ea,__tla as la}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{E as ta,__tla as sa}from"./el-card-CJbXGyyg.js";import{_ as oa,__tla as ra}from"./index-COobLwz-.js";import{C as na,__tla as ua}from"./CardTitle-Dm4BG9kg.js";let C,da=Promise.all([(()=>{try{return aa}catch{}})(),(()=>{try{return la}catch{}})(),(()=>{try{return sa}catch{}})(),(()=>{try{return ra}catch{}})(),(()=>{try{return ua}catch{}})()]).then(async()=>{let p,v,b;p={class:"flex items-center justify-between"},v={key:0},b={key:0},C=N({name:"CrmCustomerPoolConfig",__name:"index",setup(ia){const U=O(),{t:k}=S(),n=y(!1),a=y({enabled:!1,contactExpireDays:void 0,dealExpireDays:void 0,notifyEnabled:!1,notifyDays:void 0}),z=A({enabled:[{required:!0,message:"\u662F\u5426\u542F\u7528\u5BA2\u6237\u516C\u6D77\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),m=y(),g=async()=>{try{n.value=!0;const u=await(async()=>await h.get({url:"/crm/customer-pool-config/get"}))();if(u===null)return;a.value=u}finally{n.value=!1}},P=async()=>{if(m&&await m.value.validate()){n.value=!0;try{const u=a.value;await(async s=>await h.put({url:"/crm/customer-pool-config/save",data:s}))(u),U.success(k("common.updateSuccess")),await g(),n.value=!1}finally{n.value=!1}}},T=()=>{a.value.enabled||(a.value.contactExpireDays=void 0,a.value.dealExpireDays=void 0,a.value.notifyEnabled=!1,a.value.notifyDays=void 0)},j=()=>{a.value.notifyEnabled||(a.value.notifyDays=void 0)};return K(()=>{g()}),(u,s)=>{const E=oa,q=G,i=J,V=M,c=Q,f=Y,L=ta,R=Z,F=ea,H=W("hasPermi"),I=$;return d(),_(B,null,[e(E,{title:"\u3010\u5BA2\u6237\u3011\u5BA2\u6237\u7BA1\u7406\u3001\u516C\u6D77\u5BA2\u6237",url:"https://doc.iocoder.cn/crm/customer/"}),e(E,{title:"\u3010\u901A\u7528\u3011\u6570\u636E\u6743\u9650",url:"https://doc.iocoder.cn/crm/permission/"}),e(F,null,{default:t(()=>[x((d(),D(R,{ref_key:"formRef",ref:m,model:l(a),rules:l(z),"label-width":"160px"},{default:t(()=>[e(L,{shadow:"never"},{header:t(()=>[X("div",p,[e(l(na),{title:"\u5BA2\u6237\u516C\u6D77\u89C4\u5219\u8BBE\u7F6E"}),x((d(),D(q,{type:"primary",onClick:P},{default:t(()=>[r(" \u4FDD\u5B58 ")]),_:1})),[[H,["crm:customer-pool-config:update"]]])])]),default:t(()=>[e(c,{label:"\u5BA2\u6237\u516C\u6D77\u89C4\u5219\u8BBE\u7F6E",prop:"enabled"},{default:t(()=>[e(V,{modelValue:l(a).enabled,"onUpdate:modelValue":s[0]||(s[0]=o=>l(a).enabled=o),onChange:T,class:"ml-4"},{default:t(()=>[e(i,{label:!1,size:"large"},{default:t(()=>[r("\u4E0D\u542F\u7528")]),_:1}),e(i,{label:!0,size:"large"},{default:t(()=>[r("\u542F\u7528")]),_:1})]),_:1},8,["modelValue"])]),_:1}),l(a).enabled?(d(),_("div",v,[e(c,null,{default:t(()=>[e(f,{class:"mr-2",modelValue:l(a).contactExpireDays,"onUpdate:modelValue":s[1]||(s[1]=o=>l(a).contactExpireDays=o)},null,8,["modelValue"]),r(" \u5929\u4E0D\u8DDF\u8FDB\u6216 "),e(f,{class:"mx-2",modelValue:l(a).dealExpireDays,"onUpdate:modelValue":s[2]||(s[2]=o=>l(a).dealExpireDays=o)},null,8,["modelValue"]),r(" \u5929\u672A\u6210\u4EA4 ")]),_:1}),e(c,{label:"\u63D0\u524D\u63D0\u9192\u8BBE\u7F6E",prop:"notifyEnabled"},{default:t(()=>[e(V,{modelValue:l(a).notifyEnabled,"onUpdate:modelValue":s[3]||(s[3]=o=>l(a).notifyEnabled=o),onChange:j,class:"ml-4"},{default:t(()=>[e(i,{label:!1,size:"large"},{default:t(()=>[r("\u4E0D\u63D0\u9192")]),_:1}),e(i,{label:!0,size:"large"},{default:t(()=>[r("\u63D0\u9192")]),_:1})]),_:1},8,["modelValue"])]),_:1}),l(a).notifyEnabled?(d(),_("div",b,[e(c,null,{default:t(()=>[r(" \u63D0\u524D "),e(f,{class:"mx-2",modelValue:l(a).notifyDays,"onUpdate:modelValue":s[4]||(s[4]=o=>l(a).notifyDays=o)},null,8,["modelValue"]),r(" \u5929\u63D0\u9192 ")]),_:1})])):w("",!0)])):w("",!0)]),_:1})]),_:1},8,["model","rules"])),[[I,l(n)]])]),_:1})],64)}}})});export{da as __tla,C as default};
