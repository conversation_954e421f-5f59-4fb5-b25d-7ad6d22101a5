import{d as U,r as l,o as _,c as w,g as o,i as O,a as e,y as L,l as s,a9 as n,ay as b,B as A,__tla as F}from"./index-BUSn51wb.js";import{E as V,__tla as J}from"./el-segmented-CsiHBSIw.js";import{_ as M,__tla as B}from"./ImageList.vue_vue_type_style_index_0_lang-g6VS_PX2.js";import{a}from"./constants-C0I8ujwj.js";import P,{__tla as Y}from"./index-D4iGyASX.js";import x,{__tla as C}from"./index-DjKsXbJP.js";import T,{__tla as j}from"./index-KvrNWKOx.js";import q,{__tla as z}from"./index-C2HrS2gy.js";import{__tla as G}from"./el-card-CJbXGyyg.js";import{__tla as H}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{__tla as K}from"./index-Cch5e1V0.js";import{__tla as Q}from"./index-Cjd1fP7g.js";import{__tla as W}from"./ImageDetail-DklFMFvy.js";import{__tla as X}from"./el-drawer-DMK0hKF6.js";import{__tla as Z}from"./el-image-BjHZRFih.js";import{__tla as $}from"./ImageCard-BvRL6nWd.js";import"./download-e0EdwhTv.js";import{__tla as tt}from"./el-space-Dxj8A-LJ.js";import{__tla as at}from"./el-text-CIwNlU-U.js";let D,rt=Promise.all([(()=>{try{return F}catch{}})(),(()=>{try{return J}catch{}})(),(()=>{try{return B}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return C}catch{}})(),(()=>{try{return j}catch{}})(),(()=>{try{return z}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return H}catch{}})(),(()=>{try{return K}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return W}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return Z}catch{}})(),(()=>{try{return $}catch{}})(),(()=>{try{return tt}catch{}})(),(()=>{try{return at}catch{}})()]).then(async()=>{let c,i,u,f,y;c={class:"ai-image"},i={class:"left"},u={class:"segmented"},f={class:"modal-switch-container"},y={class:"main"},D=A(U({__name:"index",setup(et){const p=l(),h=l(),d=l(),v=l(),E=l(),r=l(a.MIDJOURNEY),g=[{label:"DALL3 \u7ED8\u753B",value:a.OPENAI},{label:"MJ \u7ED8\u753B",value:a.MIDJOURNEY},{label:"Stable Diffusion",value:a.STABLE_DIFFUSION},{label:"\u5176\u5B83",value:"other"}],N=async t=>{},m=async t=>{await p.value.getImageList()},R=async t=>{r.value=t.platform,await b(),t.platform===a.MIDJOURNEY?d.value.settingValues(t):t.platform===a.OPENAI?h.value.settingValues(t):t.platform===a.STABLE_DIFFUSION&&v.value.settingValues(t)};return(t,I)=>{const k=V;return _(),w("div",c,[o("div",i,[o("div",u,[O(k,{modelValue:e(r),"onUpdate:modelValue":I[0]||(I[0]=S=>L(r)?r.value=S:null),options:g},null,8,["modelValue"])]),o("div",f,[e(r)===e(a).OPENAI?(_(),s(P,{key:0,ref_key:"dall3Ref",ref:h,onOnDrawStart:N,onOnDrawComplete:m},null,512)):n("",!0),e(r)===e(a).MIDJOURNEY?(_(),s(x,{key:1,ref_key:"midjourneyRef",ref:d},null,512)):n("",!0),e(r)===e(a).STABLE_DIFFUSION?(_(),s(T,{key:2,ref_key:"stableDiffusionRef",ref:v,onOnDrawComplete:m},null,512)):n("",!0),e(r)==="other"?(_(),s(q,{key:3,ref_key:"otherRef",ref:E,onOnDrawComplete:m},null,512)):n("",!0)])]),o("div",y,[O(M,{ref_key:"imageListRef",ref:p,onOnRegeneration:R},null,512)])])}}}),[["__scopeId","data-v-1a674ee8"]])});export{rt as __tla,D as default};
