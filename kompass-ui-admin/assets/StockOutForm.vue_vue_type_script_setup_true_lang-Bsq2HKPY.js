import{by as i,d as $,n as B,I as G,r as d,f as Q,b as W,o as _,l as v,w as t,a as e,j as T,a9 as Y,i as a,H as ee,c as ae,F as te,k as le,y as F,Z as ue,L as se,E as oe,M as re,J as de,K as ie,cn as me,s as ce,O as pe,z as ne,A as _e,N as fe,R as ve,__tla as ke}from"./index-BUSn51wb.js";import{_ as ye,__tla as be}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{_ as we,__tla as Ve}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as Se,__tla as ge}from"./StockOutItemForm.vue_vue_type_script_setup_true_lang-BYOcEnXG.js";import{<PERSON> as he,__tla as Oe}from"./index-DYwp4_G0.js";let f,C,Ue=Promise.all([(()=>{try{return ke}catch{}})(),(()=>{try{return be}catch{}})(),(()=>{try{return Ve}catch{}})(),(()=>{try{return ge}catch{}})(),(()=>{try{return Oe}catch{}})()]).then(async()=>{f={getStockOutPage:async o=>await i.get({url:"/erp/stock-out/page",params:o}),getStockOut:async o=>await i.get({url:"/erp/stock-out/get?id="+o}),createStockOut:async o=>await i.post({url:"/erp/stock-out/create",data:o}),updateStockOut:async o=>await i.put({url:"/erp/stock-out/update",data:o}),updateStockOutStatus:async(o,k)=>await i.put({url:"/erp/stock-out/update-status",params:{id:o,status:k}}),deleteStockOut:async o=>await i.delete({url:"/erp/stock-out/delete",params:{ids:o.join(",")}}),exportStockOut:async o=>await i.download({url:"/erp/stock-out/export-excel",params:o})},C=$({name:"StockOutForm",__name:"StockOutForm",emits:["success"],setup(o,{expose:k,emit:R}){const{t:y}=B(),g=G(),m=d(!1),h=d(""),c=d(!1),b=d(""),s=d({id:void 0,customerId:void 0,outTime:void 0,remark:void 0,fileUrl:"",items:[]}),j=Q({outTime:[{required:!0,message:"\u51FA\u5E93\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),w=W(()=>b.value==="detail"),V=d(),O=d([]),S=d("item"),U=d();k({open:async(r,l)=>{if(m.value=!0,h.value=y("action."+r),b.value=r,A(),l){c.value=!0;try{s.value=await f.getStockOut(l)}finally{c.value=!1}}O.value=await he.getCustomerSimpleList()}});const q=R,z=async()=>{await V.value.validate(),await U.value.validate(),c.value=!0;try{const r=s.value;b.value==="create"?(await f.createStockOut(r),g.success(y("common.createSuccess"))):(await f.updateStockOut(r),g.success(y("common.updateSuccess"))),m.value=!1,q("success")}finally{c.value=!1}},A=()=>{var r;s.value={id:void 0,customerId:void 0,outTime:void 0,remark:void 0,fileUrl:void 0,items:[]},(r=V.value)==null||r.resetFields()};return(r,l)=>{const x=ue,p=se,n=oe,E=re,L=de,M=ie,P=me,D=ce,H=pe,J=ne,K=_e,N=we,I=fe,X=ye,Z=ve;return _(),v(X,{title:e(h),modelValue:e(m),"onUpdate:modelValue":l[7]||(l[7]=u=>F(m)?m.value=u:null),width:"1080"},{footer:t(()=>[e(w)?Y("",!0):(_(),v(I,{key:0,onClick:z,type:"primary",disabled:e(c)},{default:t(()=>[T(" \u786E \u5B9A ")]),_:1},8,["disabled"])),a(I,{onClick:l[6]||(l[6]=u=>m.value=!1)},{default:t(()=>[T("\u53D6 \u6D88")]),_:1})]),default:t(()=>[ee((_(),v(H,{ref_key:"formRef",ref:V,model:e(s),rules:e(j),"label-width":"100px",disabled:e(w)},{default:t(()=>[a(D,{gutter:20},{default:t(()=>[a(n,{span:8},{default:t(()=>[a(p,{label:"\u51FA\u5E93\u5355\u53F7",prop:"no"},{default:t(()=>[a(x,{disabled:"",modelValue:e(s).no,"onUpdate:modelValue":l[0]||(l[0]=u=>e(s).no=u),placeholder:"\u4FDD\u5B58\u65F6\u81EA\u52A8\u751F\u6210"},null,8,["modelValue"])]),_:1})]),_:1}),a(n,{span:8},{default:t(()=>[a(p,{label:"\u51FA\u5E93\u65F6\u95F4",prop:"outTime"},{default:t(()=>[a(E,{modelValue:e(s).outTime,"onUpdate:modelValue":l[1]||(l[1]=u=>e(s).outTime=u),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u51FA\u5E93\u65F6\u95F4",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),a(n,{span:8},{default:t(()=>[a(p,{label:"\u5BA2\u6237",prop:"customerId"},{default:t(()=>[a(M,{modelValue:e(s).customerId,"onUpdate:modelValue":l[2]||(l[2]=u=>e(s).customerId=u),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237",class:"!w-1/1"},{default:t(()=>[(_(!0),ae(te,null,le(e(O),u=>(_(),v(L,{key:u.id,label:u.name,value:u.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(n,{span:16},{default:t(()=>[a(p,{label:"\u5907\u6CE8",prop:"remark"},{default:t(()=>[a(x,{type:"textarea",modelValue:e(s).remark,"onUpdate:modelValue":l[3]||(l[3]=u=>e(s).remark=u),rows:1,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1}),a(n,{span:8},{default:t(()=>[a(p,{label:"\u9644\u4EF6",prop:"fileUrl"},{default:t(()=>[a(P,{"is-show-tip":!1,modelValue:e(s).fileUrl,"onUpdate:modelValue":l[4]||(l[4]=u=>e(s).fileUrl=u),limit:1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules","disabled"])),[[Z,e(c)]]),a(N,null,{default:t(()=>[a(K,{modelValue:e(S),"onUpdate:modelValue":l[5]||(l[5]=u=>F(S)?S.value=u:null),class:"-mt-15px -mb-10px"},{default:t(()=>[a(J,{label:"\u51FA\u5E93\u4EA7\u54C1\u6E05\u5355",name:"item"},{default:t(()=>[a(Se,{ref_key:"itemFormRef",ref:U,items:e(s).items,disabled:e(w)},null,8,["items","disabled"])]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["title","modelValue"])}}})});export{f as S,C as _,Ue as __tla};
