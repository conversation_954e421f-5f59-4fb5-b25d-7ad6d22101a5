import{d as k,I as C,r as s,e9 as S,eu as b,bx as w,C as F,bb as M,o as x,l as R,w as e,i as a,a as c,E as A,s as N,__tla as K}from"./index-BUSn51wb.js";import{_ as L,__tla as B}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import D,{__tla as G}from"./KeFuConversationList-tV8WRbMy.js";import H,{__tla as I}from"./KeFuMessageList-7V0stWQK.js";import J,{__tla as O}from"./MemberBrowsingHistory-C27j3FXi.js";import{W as i}from"./constants-CD8mqZTg.js";import{__tla as P}from"./el-card-CJbXGyyg.js";import{__tla as U}from"./el-avatar-Da2TGjmj.js";import{__tla as j}from"./emoji-DGuwlnSb.js";import{__tla as T}from"./formatTime-DWdBpgsM.js";import{__tla as W}from"./el-empty-DomufbmG.js";import{__tla as Y}from"./el-image-BjHZRFih.js";import{__tla as q}from"./EmojiSelectPopover.vue_vue_type_script_setup_true_lang-C7bGJ0OO.js";import{__tla as z}from"./PictureSelectUpload.vue_vue_type_script_setup_true_lang-CSrYKR-R.js";import"./picture-CTjip5lJ.js";import{__tla as Q}from"./ProductItem-bFAWKK8H.js";import{__tla as V}from"./OrderItem-DUnNh_aP.js";import"./constants-A8BI3pz7.js";import{__tla as X}from"./ProductBrowsingHistory.vue_vue_type_script_setup_true_lang-2Em6KJrO.js";import{__tla as Z}from"./concat-MbtHYl7y.js";import{__tla as $}from"./OrderBrowsingHistory.vue_vue_type_script_setup_true_lang-v98FEZn0.js";import{__tla as tt}from"./index-BQq32Shw.js";let p,rt=Promise.all([(()=>{try{return K}catch{}})(),(()=>{try{return B}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return I}catch{}})(),(()=>{try{return O}catch{}})(),(()=>{try{return P}catch{}})(),(()=>{try{return U}catch{}})(),(()=>{try{return j}catch{}})(),(()=>{try{return T}catch{}})(),(()=>{try{return W}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return q}catch{}})(),(()=>{try{return z}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return V}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return Z}catch{}})(),(()=>{try{return $}catch{}})(),(()=>{try{return tt}catch{}})()]).then(async()=>{p=k({name:"KeFu",__name:"index",setup(at){const h=C(),y=s("http://**************:48080/infra/ws".replace("http","ws")+"?token="+S()),{data:l,close:d,open:v}=b(y.value,{autoReconnect:!0,heartbeat:!0});w(()=>{var r;if(l.value)try{if(l.value==="pong")return;const _=JSON.parse(l.value),t=_.type;if(!t)return void h.error("\u672A\u77E5\u7684\u6D88\u606F\u7C7B\u578B\uFF1A"+l.value);if(t===i.KEFU_MESSAGE_TYPE)return o(),void((r=n.value)==null?void 0:r.refreshMessageList(JSON.parse(_.content)));t===i.KEFU_MESSAGE_ADMIN_READ&&o()}catch(_){console.error(_)}});const m=s(),o=()=>{var r;(r=m.value)==null||r.getConversationList()},n=s(),f=s(),E=r=>{var _,t;(_=n.value)==null||_.getNewMessageList(r),(t=f.value)==null||t.initHistory(r)};return F(()=>{o(),v()}),M(()=>{d()}),(r,_)=>{const t=L,u=A,g=N;return x(),R(g,{gutter:10},{default:e(()=>[a(u,{span:6},{default:e(()=>[a(t,null,{default:e(()=>[a(c(D),{ref_key:"keFuConversationRef",ref:m,onChange:E},null,512)]),_:1})]),_:1}),a(u,{span:12},{default:e(()=>[a(t,null,{default:e(()=>[a(c(H),{ref_key:"keFuChatBoxRef",ref:n,onChange:o},null,512)]),_:1})]),_:1}),a(u,{span:6},{default:e(()=>[a(t,null,{default:e(()=>[a(c(J),{ref_key:"memberBrowsingHistoryRef",ref:f},null,512)]),_:1})]),_:1})]),_:1})}}})});export{rt as __tla,p as default};
