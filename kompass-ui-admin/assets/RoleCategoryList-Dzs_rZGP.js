import{d as n,o as a,c as e,F as p,k as d,i as u,w as _,j as f,t as g,N as m,B as C,__tla as k}from"./index-BUSn51wb.js";let l,v=Promise.all([(()=>{try{return k}catch{}})()]).then(async()=>{let r;r={class:"category-list"},l=C(n({__name:"RoleCategoryList",props:{categoryList:{type:Array,required:!0},active:{type:String,required:!1,default:"\u5168\u90E8"}},emits:["onCategoryClick"],setup(s,{emit:i}){const o=i;return(L,h)=>{const c=m;return a(),e("div",r,[(a(!0),e(p,null,d(s.categoryList,t=>(a(),e("div",{class:"category",key:t},[u(c,{plain:"",round:"",size:"small",type:t===s.active?"primary":"",onClick:q=>(async y=>{o("onCategoryClick",y)})(t)},{default:_(()=>[f(g(t),1)]),_:2},1032,["type","onClick"])]))),128))])}}}),[["__scopeId","data-v-f4cf3ff9"]])});export{v as __tla,l as default};
