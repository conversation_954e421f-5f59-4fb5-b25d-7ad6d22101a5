import{bd as F,bI as N,be as S,cB as P,bG as U,bL as w,d as _,ct as x,cC as A,bf as D,r as G,cD as K,bQ as L,b as i,o as f,l as b,w as h,a as c,br as Q,a0 as q,b0 as J,a9 as R,c as W,F as X,k as Y,K as I,bg as Z,__tla as ee}from"./index-BUSn51wb.js";let $,ae=Promise.all([(()=>{try{return ee}catch{}})()]).then(async()=>{const T=F({format:{type:String,default:"HH:mm"},modelValue:String,disabled:Boolean,editable:{type:Boolean,default:!0},effect:{type:String,default:"light"},clearable:{type:Boolean,default:!0},size:N,placeholder:String,start:{type:String,default:"09:00"},end:{type:String,default:"18:00"},step:{type:String,default:"00:30"},minTime:String,maxTime:String,name:String,prefixIcon:{type:S([String,Object]),default:()=>P},clearIcon:{type:S([String,Object]),default:()=>U},...w}),r=n=>{const o=(n||"").split(":");if(o.length>=2){let a=Number.parseInt(o[0],10);const u=Number.parseInt(o[1],10),s=n.toUpperCase();return s.includes("AM")&&a===12?a=0:s.includes("PM")&&a!==12&&(a+=12),{hours:a,minutes:u}}return null},v=(n,o)=>{const a=r(n);if(!a)return-1;const u=r(o);if(!u)return-1;const s=a.minutes+60*a.hours,m=u.minutes+60*u.hours;return s===m?0:s>m?1:-1},g=n=>`${n}`.padStart(2,"0"),d=n=>`${g(n.hours)}:${g(n.minutes)}`,V=(n,o)=>{const a=r(n);if(!a)return"";const u=r(o);if(!u)return"";const s={hours:a.hours,minutes:a.minutes};return s.minutes+=u.minutes,s.hours+=u.hours,s.hours+=Math.floor(s.minutes/60),s.minutes=s.minutes%60,d(s)},B=_({name:"ElTimeSelect"});var p=Z(_({...B,props:T,emits:["change","blur","focus","update:modelValue"],setup(n,{expose:o}){const a=n;x.extend(A);const{Option:u}=I,s=D("input"),m=G(),C=K(),{lang:k}=L(),z=i(()=>a.modelValue),H=i(()=>{const e=r(a.start);return e?d(e):null}),y=i(()=>{const e=r(a.end);return e?d(e):null}),M=i(()=>{const e=r(a.step);return e?d(e):null}),O=i(()=>{const e=r(a.minTime||"");return e?d(e):null}),j=i(()=>{const e=r(a.maxTime||"");return e?d(e):null}),E=i(()=>{const e=[];if(a.start&&a.end&&a.step){let l,t=H.value;for(;t&&y.value&&v(t,y.value)<=0;)l=x(t,"HH:mm").locale(k.value).format(a.format),e.push({value:l,disabled:v(t,O.value||"-1:-1")<=0||v(t,j.value||"100:100")>=0}),t=V(t,M.value)}return e});return o({blur:()=>{var e,l;(l=(e=m.value)==null?void 0:e.blur)==null||l.call(e)},focus:()=>{var e,l;(l=(e=m.value)==null?void 0:e.focus)==null||l.call(e)}}),(e,l)=>(f(),b(c(I),{ref_key:"select",ref:m,"model-value":c(z),disabled:c(C),clearable:e.clearable,"clear-icon":e.clearIcon,size:e.size,effect:e.effect,placeholder:e.placeholder,"default-first-option":"",filterable:e.editable,"empty-values":e.emptyValues,"value-on-clear":e.valueOnClear,"onUpdate:modelValue":l[0]||(l[0]=t=>e.$emit("update:modelValue",t)),onChange:l[1]||(l[1]=t=>e.$emit("change",t)),onBlur:l[2]||(l[2]=t=>e.$emit("blur",t)),onFocus:l[3]||(l[3]=t=>e.$emit("focus",t))},{prefix:h(()=>[e.prefixIcon?(f(),b(c(Q),{key:0,class:q(c(s).e("prefix-icon"))},{default:h(()=>[(f(),b(J(e.prefixIcon)))]),_:1},8,["class"])):R("v-if",!0)]),default:h(()=>[(f(!0),W(X,null,Y(c(E),t=>(f(),b(c(u),{key:t.value,label:t.value,value:t.value,disabled:t.disabled},null,8,["label","value","disabled"]))),128))]),_:1},8,["model-value","disabled","clearable","clear-icon","size","effect","placeholder","filterable","empty-values","value-on-clear"]))}}),[["__file","time-select.vue"]]);p.install=n=>{n.component(p.name,p)},$=p});export{$ as E,ae as __tla};
