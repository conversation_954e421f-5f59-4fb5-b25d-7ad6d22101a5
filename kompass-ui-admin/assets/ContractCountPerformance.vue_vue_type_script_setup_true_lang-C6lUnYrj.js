import{d as Y,r as x,f as d,C as S,o as p,c as y,i as u,w as r,a as n,H as F,l as C,F as f,k,j as I,t as N,P as U,Q as E,R as q,__tla as T}from"./index-BUSn51wb.js";import{E as j,__tla as H}from"./el-card-CJbXGyyg.js";import{E as Q,__tla as R}from"./el-skeleton-item-tDN8U6BH.js";import{_ as W,__tla as X}from"./Echart.vue_vue_type_script_setup_true_lang-DnLZLH3w.js";import{S as Z,__tla as z}from"./performance-BsLSERfz.js";let L,G=Promise.all([(()=>{try{return T}catch{}})(),(()=>{try{return H}catch{}})(),(()=>{try{return R}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return z}catch{}})()]).then(async()=>{L=Y({name:"ContractCountPerformance",__name:"ContractCountPerformance",props:{queryParams:{}},setup(M,{expose:g}){const b=M,l=x(!1),m=x([]),e=d({grid:{left:20,right:20,bottom:20,containLabel:!0},legend:{},series:[{name:"\u5F53\u6708\u5408\u540C\u6570\u91CF\uFF08\u4E2A\uFF09",type:"line",data:[]},{name:"\u4E0A\u6708\u5408\u540C\u6570\u91CF\uFF08\u4E2A\uFF09",type:"line",data:[]},{name:"\u53BB\u5E74\u540C\u6708\u5408\u540C\u6570\u91CF\uFF08\u4E2A\uFF09",type:"line",data:[]},{name:"\u73AF\u6BD4\u589E\u957F\u7387\uFF08%\uFF09",type:"line",yAxisIndex:1,data:[]},{name:"\u540C\u6BD4\u589E\u957F\u7387\uFF08%\uFF09",type:"line",yAxisIndex:1,data:[]}],toolbox:{feature:{dataZoom:{xAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u5BA2\u6237\u603B\u91CF\u5206\u6790"}}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},yAxis:[{type:"value",name:"\u6570\u91CF\uFF08\u4E2A\uFF09",axisTick:{show:!1},axisLabel:{color:"#BDBDBD",formatter:"{value}"},axisLine:{lineStyle:{color:"#BDBDBD"}},splitLine:{show:!0,lineStyle:{color:"#e6e6e6"}}},{type:"value",name:"",axisTick:{alignWithLabel:!0,lineStyle:{width:0}},axisLabel:{color:"#BDBDBD",formatter:"{value}%"},axisLine:{lineStyle:{color:"#BDBDBD"}},splitLine:{show:!0,lineStyle:{color:"#e6e6e6"}}}],xAxis:{type:"category",name:"\u65E5\u671F",data:[]}}),h=async()=>{l.value=!0;const a=await Z.getContractCountPerformance(b.queryParams);e.xAxis&&e.xAxis.data&&(e.xAxis.data=a.map(t=>t.time)),e.series&&e.series[0]&&e.series[0].data&&(e.series[0].data=a.map(t=>t.currentMonthCount)),e.series&&e.series[1]&&e.series[1].data&&(e.series[1].data=a.map(t=>t.lastMonthCount),e.series[3].data=a.map(t=>t.lastMonthCount!==0?((t.currentMonthCount-t.lastMonthCount)/t.lastMonthCount*100).toFixed(2):"NULL")),e.series&&e.series[2]&&e.series[2].data&&(e.series[2].data=a.map(t=>t.lastYearCount),e.series[4].data=a.map(t=>t.lastYearCount!==0?((t.currentMonthCount-t.lastYearCount)/t.lastYearCount*100).toFixed(2):"NULL")),m.value=a,D(),l.value=!1},s=d([]),o=d([{title:"\u5F53\u6708\u5408\u540C\u6570\u91CF\u7EDF\u8BA1\uFF08\u4E2A\uFF09"},{title:"\u4E0A\u6708\u5408\u540C\u6570\u91CF\u7EDF\u8BA1\uFF08\u4E2A\uFF09"},{title:"\u53BB\u5E74\u5F53\u6708\u5408\u540C\u6570\u91CF\u7EDF\u8BA1\uFF08\u4E2A\uFF09"},{title:"\u73AF\u6BD4\u589E\u957F\u7387\uFF08%\uFF09"},{title:"\u540C\u6BD4\u589E\u957F\u7387\uFF08%\uFF09"}]),D=()=>{s.splice(0,s.length),s.push({label:"\u65E5\u671F",prop:"title"}),m.value.forEach((a,t)=>{const c={label:a.time,prop:"prop"+t};s.push(c),o[0]["prop"+t]=a.currentMonthCount,o[1]["prop"+t]=a.lastMonthCount,o[2]["prop"+t]=a.lastYearCount,o[3]["prop"+t]=a.lastMonthCount!==0?((a.currentMonthCount-a.lastMonthCount)/a.lastMonthCount*100).toFixed(2):"NULL",o[4]["prop"+t]=a.lastYearCount!==0?((a.currentMonthCount-a.lastYearCount)/a.lastYearCount*100).toFixed(2):"NULL"})};return g({loadData:h}),S(async()=>{await h()}),(a,t)=>{const c=W,v=Q,_=j,w=U,B=E,A=q;return p(),y(f,null,[u(_,{shadow:"never"},{default:r(()=>[u(v,{loading:n(l),animated:""},{default:r(()=>[u(c,{height:500,options:n(e)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),u(_,{shadow:"never",class:"mt-16px"},{default:r(()=>[F((p(),C(B,{data:n(o)},{default:r(()=>[(p(!0),y(f,null,k(n(s),i=>(p(),C(w,{key:i.prop,label:i.label,prop:i.prop,align:"center"},{default:r(P=>[I(N(P.row[i.prop]),1)]),_:2},1032,["label","prop"]))),128))]),_:1},8,["data"])),[[A,n(l)]])]),_:1})],64)}}})});export{L as _,G as __tla};
