import{by as t,__tla as d}from"./index-BUSn51wb.js";let c,e,r,l,s,u,m,n,i,o=Promise.all([(()=>{try{return d}catch{}})()]).then(async()=>{e=async a=>await t.get({url:"/crm/clue/page",params:a}),m=async a=>await t.get({url:"/crm/clue/get?id="+a}),r=async a=>await t.post({url:"/crm/clue/create",data:a}),i=async a=>await t.put({url:"/crm/clue/update",data:a}),s=async a=>await t.delete({url:"/crm/clue/delete?id="+a}),u=async a=>await t.download({url:"/crm/clue/export-excel",params:a}),c=async a=>await t.put({url:"/crm/clue/transfer",data:a}),n=async a=>await t.put({url:"/crm/clue/transform",params:{id:a}}),l=async()=>await t.get({url:"/crm/clue/follow-count"})});export{o as __tla,c as a,e as b,r as c,l as d,s as e,u as f,m as g,n as t,i as u};
