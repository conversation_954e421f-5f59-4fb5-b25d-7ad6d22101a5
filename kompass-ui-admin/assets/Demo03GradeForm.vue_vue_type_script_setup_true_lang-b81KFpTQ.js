import{d as b,r as u,f as y,at as V,H as I,a as l,o as q,l as w,w as o,i as t,Z as x,L as D,O as R,R as U,__tla as k}from"./index-BUSn51wb.js";import{b as F,__tla as G}from"./index-DrnBZ6x8.js";let _,H=Promise.all([(()=>{try{return k}catch{}})(),(()=>{try{return G}catch{}})()]).then(async()=>{_=b({__name:"Demo03GradeForm",props:{studentId:{}},setup(c,{expose:p}){const f=c,r=u(!1),e=u([]),v=y({studentId:[{required:!0,message:"\u5B66\u751F\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],name:[{required:!0,message:"\u540D\u5B57\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],teacher:[{required:!0,message:"\u73ED\u4E3B\u4EFB\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),m=u();return V(()=>f.studentId,async s=>{if(e.value={id:void 0,studentId:void 0,name:void 0,teacher:void 0},s)try{r.value=!0;const a=await F(s);if(!a)return;e.value=a}finally{r.value=!1}},{immediate:!0}),p({validate:()=>m.value.validate(),getData:()=>e.value}),(s,a)=>{const n=x,i=D,h=R,g=U;return I((q(),w(h,{ref_key:"formRef",ref:m,model:l(e),rules:l(v),"label-width":"100px"},{default:o(()=>[t(i,{label:"\u540D\u5B57",prop:"name"},{default:o(()=>[t(n,{modelValue:l(e).name,"onUpdate:modelValue":a[0]||(a[0]=d=>l(e).name=d),placeholder:"\u8BF7\u8F93\u5165\u540D\u5B57"},null,8,["modelValue"])]),_:1}),t(i,{label:"\u73ED\u4E3B\u4EFB",prop:"teacher"},{default:o(()=>[t(n,{modelValue:l(e).teacher,"onUpdate:modelValue":a[1]||(a[1]=d=>l(e).teacher=d),placeholder:"\u8BF7\u8F93\u5165\u73ED\u4E3B\u4EFB"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[g,l(r)]])}}})});export{_,H as __tla};
