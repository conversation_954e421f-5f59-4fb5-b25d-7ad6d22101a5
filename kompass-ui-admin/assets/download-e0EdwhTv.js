const a=(t,e,o)=>{const c=new Blob([t],{type:o});window.URL=window.URL||window.webkitURL;const n=URL.createObjectURL(c),i=document.createElement("a");i.href=n,i.download=e,i.click(),window.URL.revokeObjectURL(n)},d={excel:(t,e)=>{a(t,e,"application/vnd.ms-excel")},word:(t,e)=>{a(t,e,"application/msword")},zip:(t,e)=>{a(t,e,"application/zip")},html:(t,e)=>{a(t,e,"text/html")},markdown:(t,e)=>{a(t,e,"text/markdown")},image:t=>{const e=new Image;e.setAttribute("crossOrigin","anonymous"),e.src=t,e.onload=()=>{const o=document.createElement("canvas");o.width=e.width,o.height=e.height,o.getContext("2d").drawImage(e,0,0,e.width,e.height);const c=o.toDataURL("image/png"),n=document.createElement("a");n.href=c,n.download="image.png",n.click()}}};export{d};
