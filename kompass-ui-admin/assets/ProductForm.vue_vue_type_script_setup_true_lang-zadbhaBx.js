import{d as H,n as J,I as Y,r as i,f as Z,o as p,l as f,w as t,i as e,a,j as y,H as z,c as I,F as C,k as x,V as Q,G as W,t as X,y as $,Z as ee,L as ae,E as le,J as te,K as de,am as re,an as oe,cc as ue,s as se,O as ie,N as ne,R as ce,__tla as me}from"./index-BUSn51wb.js";import{_ as pe,__tla as _e}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{E as fe,__tla as ve}from"./el-tree-select-CBuha0HW.js";import{P as g,__tla as Ve}from"./index-B00QUU3o.js";import{P as ye,__tla as ge}from"./index-_v3tH2a8.js";import{P as be,__tla as he}from"./index-Bsu_xqlj.js";import{C as Pe}from"./constants-A8BI3pz7.js";import{d as we,h as Ue}from"./tree-BMa075Oj.js";let S,ke=Promise.all([(()=>{try{return me}catch{}})(),(()=>{try{return _e}catch{}})(),(()=>{try{return ve}catch{}})(),(()=>{try{return Ve}catch{}})(),(()=>{try{return ge}catch{}})(),(()=>{try{return he}catch{}})()]).then(async()=>{S=H({name:"ProductForm",__name:"ProductForm",emits:["success"],setup(Ie,{expose:q,emit:D}){const{t:v}=J(),b=Y(),n=i(!1),h=i(""),c=i(!1),P=i(""),r=i({id:void 0,name:void 0,barCode:void 0,categoryId:void 0,unitId:void 0,status:void 0,standard:void 0,remark:void 0,expiryDay:void 0,weight:void 0,purchasePrice:void 0,salePrice:void 0,minPrice:void 0}),E=Z({name:[{required:!0,message:"\u4EA7\u54C1\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],barCode:[{required:!0,message:"\u4EA7\u54C1\u6761\u7801\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],categoryId:[{required:!0,message:"\u4EA7\u54C1\u5206\u7C7B\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],unitId:[{required:!0,message:"\u5355\u4F4D\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u4EA7\u54C1\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),V=i(),w=i([]),U=i([]);q({open:async(s,d)=>{if(n.value=!0,h.value=v("action."+s),P.value=s,N(),d){c.value=!0;try{r.value=await g.getProduct(d)}finally{c.value=!1}}const m=await ye.getProductCategorySimpleList();w.value=Ue(m,"id","parentId"),U.value=await be.getProductUnitSimpleList()}});const F=D,L=async()=>{await V.value.validate(),c.value=!0;try{const s=r.value;P.value==="create"?(await g.createProduct(s),b.success(v("common.createSuccess"))):(await g.updateProduct(s),b.success(v("common.updateSuccess"))),n.value=!1,F("success")}finally{c.value=!1}},N=()=>{var s;r.value={id:void 0,name:void 0,barCode:void 0,categoryId:void 0,unitId:void 0,status:Pe.ENABLE,standard:void 0,remark:void 0,expiryDay:void 0,weight:void 0,purchasePrice:void 0,salePrice:void 0,minPrice:void 0},(s=V.value)==null||s.resetFields()};return(s,d)=>{const m=ee,o=ae,u=le,A=fe,O=te,R=de,T=re,B=oe,_=ue,K=se,M=ie,k=ne,j=pe,G=ce;return p(),f(j,{title:a(h),modelValue:a(n),"onUpdate:modelValue":d[13]||(d[13]=l=>$(n)?n.value=l:null)},{footer:t(()=>[e(k,{onClick:L,type:"primary",disabled:a(c)},{default:t(()=>[y("\u786E \u5B9A")]),_:1},8,["disabled"]),e(k,{onClick:d[12]||(d[12]=l=>n.value=!1)},{default:t(()=>[y("\u53D6 \u6D88")]),_:1})]),default:t(()=>[z((p(),f(M,{ref_key:"formRef",ref:V,model:a(r),rules:a(E),"label-width":"100px"},{default:t(()=>[e(K,{gutter:20},{default:t(()=>[e(u,{span:12},{default:t(()=>[e(o,{label:"\u540D\u79F0",prop:"name"},{default:t(()=>[e(m,{modelValue:a(r).name,"onUpdate:modelValue":d[0]||(d[0]=l=>a(r).name=l),placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0"},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:12},{default:t(()=>[e(o,{label:"\u6761\u7801",prop:"barCode"},{default:t(()=>[e(m,{modelValue:a(r).barCode,"onUpdate:modelValue":d[1]||(d[1]=l=>a(r).barCode=l),placeholder:"\u8BF7\u8F93\u5165\u6761\u7801"},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:12},{default:t(()=>[e(o,{label:"\u5206\u7C7B",prop:"categoryId"},{default:t(()=>[e(A,{modelValue:a(r).categoryId,"onUpdate:modelValue":d[2]||(d[2]=l=>a(r).categoryId=l),data:a(w),props:a(we),"check-strictly":"","default-expand-all":"",placeholder:"\u8BF7\u9009\u62E9\u5206\u7C7B",class:"w-1/1"},null,8,["modelValue","data","props"])]),_:1})]),_:1}),e(u,{span:12},{default:t(()=>[e(o,{label:"\u5355\u4F4D",prop:"unitId"},{default:t(()=>[e(R,{modelValue:a(r).unitId,"onUpdate:modelValue":d[3]||(d[3]=l=>a(r).unitId=l),clearable:"",placeholder:"\u8BF7\u9009\u62E9\u5355\u4F4D",class:"w-1/1"},{default:t(()=>[(p(!0),I(C,null,x(a(U),l=>(p(),f(O,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(u,{span:12},{default:t(()=>[e(o,{label:"\u72B6\u6001",prop:"status"},{default:t(()=>[e(B,{modelValue:a(r).status,"onUpdate:modelValue":d[4]||(d[4]=l=>a(r).status=l)},{default:t(()=>[(p(!0),I(C,null,x(a(Q)(a(W).COMMON_STATUS),l=>(p(),f(T,{key:l.value,label:l.value},{default:t(()=>[y(X(l.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(u,{span:12},{default:t(()=>[e(o,{label:"\u89C4\u683C",prop:"standard"},{default:t(()=>[e(m,{modelValue:a(r).standard,"onUpdate:modelValue":d[5]||(d[5]=l=>a(r).standard=l),placeholder:"\u8BF7\u8F93\u5165\u89C4\u683C"},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:12},{default:t(()=>[e(o,{label:"\u4FDD\u8D28\u671F\u5929\u6570",prop:"expiryDay"},{default:t(()=>[e(_,{modelValue:a(r).expiryDay,"onUpdate:modelValue":d[6]||(d[6]=l=>a(r).expiryDay=l),placeholder:"\u8BF7\u8F93\u5165\u4FDD\u8D28\u671F\u5929\u6570",min:0,precision:0,class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:12},{default:t(()=>[e(o,{label:"\u91CD\u91CF\uFF08kg\uFF09",prop:"weight"},{default:t(()=>[e(_,{modelValue:a(r).weight,"onUpdate:modelValue":d[7]||(d[7]=l=>a(r).weight=l),placeholder:"\u8BF7\u8F93\u5165\u91CD\u91CF\uFF08kg\uFF09",min:0,class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:12},{default:t(()=>[e(o,{label:"\u91C7\u8D2D\u4EF7\u683C",prop:"purchasePrice"},{default:t(()=>[e(_,{modelValue:a(r).purchasePrice,"onUpdate:modelValue":d[8]||(d[8]=l=>a(r).purchasePrice=l),placeholder:"\u8BF7\u8F93\u5165\u91C7\u8D2D\u4EF7\u683C\uFF0C\u5355\u4F4D\uFF1A\u5143",min:0,precision:2,class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:12},{default:t(()=>[e(o,{label:"\u9500\u552E\u4EF7\u683C",prop:"salePrice"},{default:t(()=>[e(_,{modelValue:a(r).salePrice,"onUpdate:modelValue":d[9]||(d[9]=l=>a(r).salePrice=l),placeholder:"\u8BF7\u8F93\u5165\u9500\u552E\u4EF7\u683C\uFF0C\u5355\u4F4D\uFF1A\u5143",min:0,precision:2,class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:12},{default:t(()=>[e(o,{label:"\u6700\u4F4E\u4EF7\u683C",prop:"minPrice"},{default:t(()=>[e(_,{modelValue:a(r).minPrice,"onUpdate:modelValue":d[10]||(d[10]=l=>a(r).minPrice=l),placeholder:"\u8BF7\u8F93\u5165\u6700\u4F4E\u4EF7\u683C\uFF0C\u5355\u4F4D\uFF1A\u5143",min:0,precision:2,class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(u,{span:24},{default:t(()=>[e(o,{label:"\u5907\u6CE8",prop:"remark"},{default:t(()=>[e(m,{type:"textarea",modelValue:a(r).remark,"onUpdate:modelValue":d[11]||(d[11]=l=>a(r).remark=l),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])),[[G,a(c)]])]),_:1},8,["title","modelValue"])}}})});export{S as _,ke as __tla};
