import{d as Y,u as z,r as i,f as B,C as F,o as k,c as H,i as e,w as l,a,U as q,j as p,H as A,l as K,G as R,t as j,F as G,Z as L,L as O,M as Q,_ as Z,N as W,O as X,P as E,Q as J,R as $,__tla as ee}from"./index-BUSn51wb.js";import{_ as ae,__tla as te}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as le,__tla as re}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as oe,__tla as ne}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as se,__tla as ie}from"./index-COobLwz-.js";import{d as h,a as pe,__tla as ce}from"./formatTime-DWdBpgsM.js";import{h as de,__tla as me}from"./index-OMcsJcjy.js";import{__tla as _e}from"./index-Cch5e1V0.js";import"./color-BN7ZL7BD.js";import{__tla as ue}from"./el-card-CJbXGyyg.js";let x,fe=Promise.all([(()=>{try{return ee}catch{}})(),(()=>{try{return te}catch{}})(),(()=>{try{return re}catch{}})(),(()=>{try{return ne}catch{}})(),(()=>{try{return ie}catch{}})(),(()=>{try{return ce}catch{}})(),(()=>{try{return me}catch{}})(),(()=>{try{return _e}catch{}})(),(()=>{try{return ue}catch{}})()]).then(async()=>{x=Y({name:"BpmTodoTask",__name:"index",setup(he){const{push:U}=z(),c=i(!0),g=i(0),w=i([]),o=B({pageNo:1,pageSize:10,name:"",createTime:[]}),y=i(),d=async()=>{c.value=!0;try{const _=await de(o);w.value=_.list,g.value=_.total}finally{c.value=!1}},m=()=>{o.pageNo=1,d()},V=()=>{y.value.resetFields(),m()};return F(()=>{d()}),(_,n)=>{const s=se,I=L,u=O,M=Q,b=Z,f=W,N=X,v=oe,r=E,S=le,C=J,D=ae,P=$;return k(),H(G,null,[e(s,{title:"\u5BA1\u6279\u901A\u8FC7\u3001\u4E0D\u901A\u8FC7\u3001\u9A73\u56DE",url:"https://doc.iocoder.cn/bpm/task-todo-done/"}),e(s,{title:"\u5BA1\u6279\u52A0\u7B7E\u3001\u51CF\u7B7E",url:"https://doc.iocoder.cn/bpm/sign/"}),e(s,{title:"\u5BA1\u6279\u8F6C\u529E\u3001\u59D4\u6D3E\u3001\u6284\u9001",url:"https://doc.iocoder.cn/bpm/task-delegation-and-cc/"}),e(s,{title:"\u5BA1\u6279\u52A0\u7B7E\u3001\u51CF\u7B7E",url:"https://doc.iocoder.cn/bpm/sign/"}),e(v,null,{default:l(()=>[e(N,{ref_key:"queryFormRef",ref:y,inline:!0,model:a(o),class:"-mb-15px","label-width":"68px"},{default:l(()=>[e(u,{label:"\u4EFB\u52A1\u540D\u79F0",prop:"name"},{default:l(()=>[e(I,{modelValue:a(o).name,"onUpdate:modelValue":n[0]||(n[0]=t=>a(o).name=t),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u4EFB\u52A1\u540D\u79F0",onKeyup:q(m,["enter"])},null,8,["modelValue"])]),_:1}),e(u,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:l(()=>[e(M,{modelValue:a(o).createTime,"onUpdate:modelValue":n[1]||(n[1]=t=>a(o).createTime=t),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),e(u,null,{default:l(()=>[e(f,{onClick:m},{default:l(()=>[e(b,{class:"mr-5px",icon:"ep:search"}),p(" \u641C\u7D22 ")]),_:1}),e(f,{onClick:V},{default:l(()=>[e(b,{class:"mr-5px",icon:"ep:refresh"}),p(" \u91CD\u7F6E ")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(v,null,{default:l(()=>[A((k(),K(C,{data:a(w)},{default:l(()=>[e(r,{align:"center",label:"\u6D41\u7A0B",prop:"processInstance.name",width:"180"}),e(r,{align:"center",label:"\u53D1\u8D77\u4EBA",prop:"processInstance.startUser.nickname",width:"100"}),e(r,{formatter:a(h),align:"center",label:"\u53D1\u8D77\u65F6\u95F4",prop:"createTime",width:"180"},null,8,["formatter"]),e(r,{align:"center",label:"\u5F53\u524D\u4EFB\u52A1",prop:"name",width:"180"}),e(r,{formatter:a(h),align:"center",label:"\u4EFB\u52A1\u5F00\u59CB\u65F6\u95F4",prop:"createTime",width:"180"},null,8,["formatter"]),e(r,{formatter:a(h),align:"center",label:"\u4EFB\u52A1\u7ED3\u675F\u65F6\u95F4",prop:"endTime",width:"180"},null,8,["formatter"]),e(r,{align:"center",label:"\u5BA1\u6279\u72B6\u6001",prop:"status",width:"120"},{default:l(t=>[e(S,{type:a(R).BPM_TASK_STATUS,value:t.row.status},null,8,["type","value"])]),_:1}),e(r,{align:"center",label:"\u5BA1\u6279\u5EFA\u8BAE",prop:"reason","min-width":"180"}),e(r,{align:"center",label:"\u8017\u65F6",prop:"durationInMillis",width:"160"},{default:l(t=>[p(j(a(pe)(t.row.durationInMillis)),1)]),_:1}),e(r,{align:"center",label:"\u6D41\u7A0B\u7F16\u53F7",prop:"id","show-overflow-tooltip":!0}),e(r,{align:"center",label:"\u4EFB\u52A1\u7F16\u53F7",prop:"id","show-overflow-tooltip":!0}),e(r,{align:"center",label:"\u64CD\u4F5C",fixed:"right",width:"80"},{default:l(t=>[e(f,{link:"",type:"primary",onClick:ge=>{return T=t.row,void U({name:"BpmProcessInstanceDetail",query:{id:T.processInstance.id}});var T}},{default:l(()=>[p("\u5386\u53F2")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[P,a(c)]]),e(D,{limit:a(o).pageSize,"onUpdate:limit":n[2]||(n[2]=t=>a(o).pageSize=t),page:a(o).pageNo,"onUpdate:page":n[3]||(n[3]=t=>a(o).pageNo=t),total:a(g),onPagination:d},null,8,["limit","page","total"])]),_:1})],64)}}})});export{fe as __tla,x as default};
