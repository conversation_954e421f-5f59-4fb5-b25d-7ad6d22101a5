import{_ as E,__tla as I}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{d as R,r as T,o as U,l as M,w as e,i as a,j as u,t as _,a as s,G as o,y as x,g as p,__tla as N}from"./index-BUSn51wb.js";import{E as S,a as L,__tla as O}from"./el-collapse-item-B_QvnH_b.js";import{E as V,a as j,__tla as w}from"./el-descriptions-item-dD3qa0ub.js";import{_ as D,__tla as q}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{f as c,__tla as Q}from"./formatTime-DWdBpgsM.js";let y,g=Promise.all([(()=>{try{return I}catch{}})(),(()=>{try{return N}catch{}})(),(()=>{try{return O}catch{}})(),(()=>{try{return w}catch{}})(),(()=>{try{return q}catch{}})(),(()=>{try{return Q}catch{}})()]).then(async()=>{let f,d;f=p("span",{class:"text-base font-bold"},"\u57FA\u672C\u4FE1\u606F",-1),d=p("span",{class:"text-base font-bold"},"\u7CFB\u7EDF\u4FE1\u606F",-1),y=R({name:"CrmClueDetailsInfo",__name:"ClueDetailsInfo",props:{clue:{}},setup(k){const r=T(["basicInfo","systemInfo"]);return(l,m)=>{const t=V,n=D,b=j,i=S,C=L,h=E;return U(),M(h,null,{default:e(()=>[a(C,{modelValue:s(r),"onUpdate:modelValue":m[0]||(m[0]=v=>x(r)?r.value=v:null),class:""},{default:e(()=>[a(i,{name:"basicInfo"},{title:e(()=>[f]),default:e(()=>[a(b,{column:4},{default:e(()=>[a(t,{label:"\u7EBF\u7D22\u540D\u79F0"},{default:e(()=>[u(_(l.clue.name),1)]),_:1}),a(t,{label:"\u5BA2\u6237\u6765\u6E90"},{default:e(()=>[a(n,{type:s(o).CRM_CUSTOMER_SOURCE,value:l.clue.source},null,8,["type","value"])]),_:1}),a(t,{label:"\u624B\u673A"},{default:e(()=>[u(_(l.clue.mobile),1)]),_:1}),a(t,{label:"\u7535\u8BDD"},{default:e(()=>[u(_(l.clue.telephone),1)]),_:1}),a(t,{label:"\u90AE\u7BB1"},{default:e(()=>[u(_(l.clue.email),1)]),_:1}),a(t,{label:"\u5730\u5740"},{default:e(()=>[u(_(l.clue.areaName)+" "+_(l.clue.detailAddress),1)]),_:1}),a(t,{label:"QQ"},{default:e(()=>[u(_(l.clue.qq),1)]),_:1}),a(t,{label:"\u5FAE\u4FE1"},{default:e(()=>[u(_(l.clue.wechat),1)]),_:1}),a(t,{label:"\u5BA2\u6237\u884C\u4E1A"},{default:e(()=>[a(n,{type:s(o).CRM_CUSTOMER_INDUSTRY,value:l.clue.industryId},null,8,["type","value"])]),_:1}),a(t,{label:"\u5BA2\u6237\u7EA7\u522B"},{default:e(()=>[a(n,{type:s(o).CRM_CUSTOMER_LEVEL,value:l.clue.level},null,8,["type","value"])]),_:1}),a(t,{label:"\u4E0B\u6B21\u8054\u7CFB\u65F6\u95F4"},{default:e(()=>[u(_(s(c)(l.clue.contactNextTime)),1)]),_:1}),a(t,{label:"\u5907\u6CE8"},{default:e(()=>[u(_(l.clue.remark),1)]),_:1})]),_:1})]),_:1}),a(i,{name:"systemInfo"},{title:e(()=>[d]),default:e(()=>[a(b,{column:4},{default:e(()=>[a(t,{label:"\u8D1F\u8D23\u4EBA"},{default:e(()=>[u(_(l.clue.ownerUserName),1)]),_:1}),a(t,{label:"\u6700\u540E\u8DDF\u8FDB\u8BB0\u5F55"},{default:e(()=>[u(_(l.clue.contactLastContent),1)]),_:1}),a(t,{label:"\u6700\u540E\u8DDF\u8FDB\u65F6\u95F4"},{default:e(()=>[u(_(s(c)(l.clue.contactLastTime)),1)]),_:1}),a(t,{label:""},{default:e(()=>[u("\xA0")]),_:1}),a(t,{label:"\u521B\u5EFA\u4EBA"},{default:e(()=>[u(_(l.clue.creatorName),1)]),_:1}),a(t,{label:"\u521B\u5EFA\u65F6\u95F4"},{default:e(()=>[u(_(s(c)(l.clue.createTime)),1)]),_:1}),a(t,{label:"\u66F4\u65B0\u65F6\u95F4"},{default:e(()=>[u(_(s(c)(l.clue.updateTime)),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})}}})});export{y as _,g as __tla};
