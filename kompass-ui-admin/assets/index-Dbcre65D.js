import{d as u,o as e,c as l,F as _,k as y,av as a,t as r,a9 as x,l as m,g as c,__tla as b}from"./index-BUSn51wb.js";import{E as g,__tla as f}from"./el-image-BjHZRFih.js";let n,h=Promise.all([(()=>{try{return b}catch{}})(),(()=>{try{return f}catch{}})()]).then(async()=>{let o;o={class:"flex flex-row flex-wrap"},n=u({name:"MenuGrid",__name:"index",props:{property:{}},setup:k=>(p,w)=>{const i=g;return e(),l("div",o,[(e(!0),l(_,null,y(p.property.list,(t,d)=>{var s;return e(),l("div",{key:d,class:"relative flex flex-col items-center p-b-14px p-t-20px",style:a({width:1/p.property.column*100+"%"})},[(s=t.badge)!=null&&s.show?(e(),l("span",{key:0,class:"absolute left-50% top-10px z-1 h-20px rounded-50% p-x-6px text-center text-12px leading-20px",style:a({color:t.badge.textColor,backgroundColor:t.badge.bgColor})},r(t.badge.text),5)):x("",!0),t.iconUrl?(e(),m(i,{key:1,class:"h-28px w-28px",src:t.iconUrl},null,8,["src"])):x("",!0),c("span",{class:"m-t-8px h-16px text-12px leading-16px",style:a({color:t.titleColor})},r(t.title),5),c("span",{class:"m-t-6px h-12px text-10px leading-12px",style:a({color:t.subtitleColor})},r(t.subtitle),5)],4)}),128))])}})});export{h as __tla,n as default};
