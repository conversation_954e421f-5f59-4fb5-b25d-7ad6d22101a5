import{d as Y,u as z,r as o,f as F,C as H,o as N,c as M,i as a,w as t,a as e,j as d,H as q,l as B,F as R,Z as j,L,M as O,_ as Q,N as Z,O as A,P as E,Q as G,R as J,__tla as K}from"./index-BUSn51wb.js";import{_ as W,__tla as X}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as $,__tla as aa}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as ea,__tla as ta}from"./index-COobLwz-.js";import{d as I,__tla as la}from"./formatTime-DWdBpgsM.js";import{f as ra,__tla as sa}from"./index-BtD-8VxR.js";import{__tla as na}from"./index-Cch5e1V0.js";import{__tla as oa}from"./el-card-CJbXGyyg.js";let x,ca=Promise.all([(()=>{try{return K}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return aa}catch{}})(),(()=>{try{return ta}catch{}})(),(()=>{try{return la}catch{}})(),(()=>{try{return sa}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return oa}catch{}})()]).then(async()=>{x=Y({name:"BpmProcessInstanceCopy",__name:"index",setup(ia){const{push:k}=z(),c=o(!1),u=o(0),f=o([]),l=F({pageNo:1,pageSize:10,processInstanceId:"",processInstanceName:"",createTime:[]}),h=o(),i=async()=>{c.value=!0;try{const p=await ra(l);f.value=p.list,u.value=p.total}finally{c.value=!1}},g=()=>{l.pageNo=1,i()},v=()=>{h.value.resetFields(),g()};return H(()=>{i()}),(p,s)=>{const C=ea,T=j,m=L,V=O,y=Q,_=Z,D=A,w=$,n=E,P=G,U=W,S=J;return N(),M(R,null,[a(C,{title:"\u5BA1\u6279\u8F6C\u529E\u3001\u59D4\u6D3E\u3001\u6284\u9001",url:"https://doc.iocoder.cn/bpm/task-delegation-and-cc/"}),a(w,null,{default:t(()=>[a(D,{ref_key:"queryFormRef",ref:h,inline:!0,class:"-mb-15px","label-width":"68px"},{default:t(()=>[a(m,{label:"\u6D41\u7A0B\u540D\u79F0",prop:"name"},{default:t(()=>[a(T,{modelValue:e(l).processInstanceName,"onUpdate:modelValue":s[0]||(s[0]=r=>e(l).processInstanceName=r),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u6D41\u7A0B\u540D\u79F0"},null,8,["modelValue"])]),_:1}),a(m,{label:"\u6284\u9001\u65F6\u95F4",prop:"createTime"},{default:t(()=>[a(V,{modelValue:e(l).createTime,"onUpdate:modelValue":s[1]||(s[1]=r=>e(l).createTime=r),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),a(m,null,{default:t(()=>[a(_,{onClick:g},{default:t(()=>[a(y,{class:"mr-5px",icon:"ep:search"}),d(" \u641C\u7D22 ")]),_:1}),a(_,{onClick:v},{default:t(()=>[a(y,{class:"mr-5px",icon:"ep:refresh"}),d(" \u91CD\u7F6E ")]),_:1})]),_:1})]),_:1},512)]),_:1}),a(w,null,{default:t(()=>[q((N(),B(P,{data:e(f)},{default:t(()=>[a(n,{align:"center",label:"\u6D41\u7A0B\u540D",prop:"processInstanceName","min-width":"180"}),a(n,{align:"center",label:"\u6D41\u7A0B\u53D1\u8D77\u4EBA",prop:"startUserName","min-width":"100"}),a(n,{formatter:e(I),align:"center",label:"\u6D41\u7A0B\u53D1\u8D77\u65F6\u95F4",prop:"processInstanceStartTime",width:"180"},null,8,["formatter"]),a(n,{align:"center",label:"\u6284\u9001\u4EFB\u52A1",prop:"taskName","min-width":"180"}),a(n,{align:"center",label:"\u6284\u9001\u4EBA",prop:"creatorName","min-width":"100"}),a(n,{align:"center",label:"\u6284\u9001\u65F6\u95F4",prop:"createTime",width:"180",formatter:e(I)},null,8,["formatter"]),a(n,{align:"center",label:"\u64CD\u4F5C",fixed:"right",width:"80"},{default:t(r=>[a(_,{link:"",type:"primary",onClick:pa=>{return b=r.row,void k({name:"BpmProcessInstanceDetail",query:{id:b.processInstanceId}});var b}},{default:t(()=>[d("\u8BE6\u60C5")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[S,e(c)]]),a(U,{limit:e(l).pageSize,"onUpdate:limit":s[2]||(s[2]=r=>e(l).pageSize=r),page:e(l).pageNo,"onUpdate:page":s[3]||(s[3]=r=>e(l).pageNo=r),total:e(u),onPagination:i},null,8,["limit","page","total"])]),_:1})],64)}}})});export{ca as __tla,x as default};
