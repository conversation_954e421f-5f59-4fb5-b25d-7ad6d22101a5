import{by as s,__tla as a}from"./index-BUSn51wb.js";let t,r=Promise.all([(()=>{try{return a}catch{}})()]).then(async()=>{t={getProcessListenerPage:async e=>await s.get({url:"/bpm/process-listener/page",params:e}),getProcessListener:async e=>await s.get({url:"/bpm/process-listener/get?id="+e}),createProcessListener:async e=>await s.post({url:"/bpm/process-listener/create",data:e}),updateProcessListener:async e=>await s.put({url:"/bpm/process-listener/update",data:e}),deleteProcessListener:async e=>await s.delete({url:"/bpm/process-listener/delete?id="+e})}});export{t as P,r as __tla};
