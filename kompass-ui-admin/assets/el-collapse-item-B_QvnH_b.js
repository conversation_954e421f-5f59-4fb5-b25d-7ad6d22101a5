import{bd as j,be as O,cw as le,c2 as P,c5 as Q,bn as te,bp as ie,bV as oe,r as I,d4 as T,at as de,c8 as ne,bf as $,b as i,d as g,o as U,c as q,aV as w,a0 as b,a as e,bg as z,b6 as ce,dA as re,g as V,j as ue,t as ve,i as E,w as D,cQ as pe,br as be,U as me,b1 as fe,H as he,a8 as Ce,dB as ye,bh as Ke,bk as _e,__tla as ge}from"./index-BUSn51wb.js";let G,J,xe=Promise.all([(()=>{try{return ge}catch{}})()]).then(async()=>{const B=r=>te(r)||ie(r)||oe(r),L=j({accordion:Boolean,modelValue:{type:O([Array,String,Number]),default:()=>le([])}}),M={[P]:B,[Q]:B},F=Symbol("collapseContextKey"),R=g({name:"ElCollapse"});var X=z(g({...R,props:L,emits:M,setup(r,{expose:x,emit:f}){const h=r,{activeNames:A,setActiveNames:u}=((l,v)=>{const o=I(T(l.modelValue)),m=c=>{o.value=c;const n=l.accordion?o.value[0]:o.value;v(P,n),v(Q,n)};return de(()=>l.modelValue,()=>o.value=T(l.modelValue),{deep:!0}),ne(F,{activeNames:o,handleItemClick:c=>{if(l.accordion)m([o.value[0]===c?"":c]);else{const n=[...o.value],y=n.indexOf(c);y>-1?n.splice(y,1):n.push(c),m(n)}}}),{activeNames:o,setActiveNames:m}})(h,f),{rootKls:C}=(()=>{const l=$("collapse");return{rootKls:i(()=>l.b())}})();return x({activeNames:A,setActiveNames:u}),(l,v)=>(U(),q("div",{class:b(e(C))},[w(l.$slots,"default")],2))}}),[["__file","collapse.vue"]]);const Y=j({title:{type:String,default:""},name:{type:O([String,Number]),default:void 0},disabled:Boolean}),Z=["id","aria-expanded","aria-controls","aria-describedby","tabindex"],ee=["id","aria-hidden","aria-labelledby"],ae=g({name:"ElCollapseItem"});var H=z(g({...ae,props:Y,setup(r,{expose:x}){const f=r,{focusing:h,id:A,isActive:u,handleFocus:C,handleHeaderClick:l,handleEnterClick:v}=(d=>{const a=ce(F),{namespace:t}=$("collapse"),p=I(!1),s=I(!1),K=re(),_=i(()=>K.current++),k=i(()=>{var W;return(W=d.name)!=null?W:`${t.value}-id-${K.prefix}-${e(_)}`}),se=i(()=>a==null?void 0:a.activeNames.value.includes(e(k)));return{focusing:p,id:_,isActive:se,handleFocus:()=>{setTimeout(()=>{s.value?s.value=!1:p.value=!0},50)},handleHeaderClick:()=>{d.disabled||(a==null||a.handleItemClick(e(k)),p.value=!1,s.value=!0)},handleEnterClick:()=>{a==null||a.handleItemClick(e(k))}}})(f),{arrowKls:o,headKls:m,rootKls:c,itemWrapperKls:n,itemContentKls:y,scopedContentId:N,scopedHeadId:S}=((d,{focusing:a,isActive:t,id:p})=>{const s=$("collapse"),K=i(()=>[s.b("item"),s.is("active",e(t)),s.is("disabled",d.disabled)]),_=i(()=>[s.be("item","header"),s.is("active",e(t)),{focusing:e(a)&&!d.disabled}]);return{arrowKls:i(()=>[s.be("item","arrow"),s.is("active",e(t))]),headKls:_,rootKls:K,itemWrapperKls:i(()=>s.be("item","wrap")),itemContentKls:i(()=>s.be("item","content")),scopedContentId:i(()=>s.b(`content-${e(p)}`)),scopedHeadId:i(()=>s.b(`head-${e(p)}`))}})(f,{focusing:h,isActive:u,id:A});return x({isActive:u}),(d,a)=>(U(),q("div",{class:b(e(c))},[V("button",{id:e(S),class:b(e(m)),"aria-expanded":e(u),"aria-controls":e(N),"aria-describedby":e(N),tabindex:d.disabled?-1:0,type:"button",onClick:a[0]||(a[0]=(...t)=>e(l)&&e(l)(...t)),onKeydown:a[1]||(a[1]=me(fe((...t)=>e(v)&&e(v)(...t),["stop","prevent"]),["space","enter"])),onFocus:a[2]||(a[2]=(...t)=>e(C)&&e(C)(...t)),onBlur:a[3]||(a[3]=t=>h.value=!1)},[w(d.$slots,"title",{},()=>[ue(ve(d.title),1)]),E(e(be),{class:b(e(o))},{default:D(()=>[E(e(pe))]),_:1},8,["class"])],42,Z),E(e(ye),null,{default:D(()=>[he(V("div",{id:e(N),role:"region",class:b(e(n)),"aria-hidden":!e(u),"aria-labelledby":e(S)},[V("div",{class:b(e(y))},[w(d.$slots,"default")],2)],10,ee),[[Ce,e(u)]])]),_:3})],2))}}),[["__file","collapse-item.vue"]]);J=Ke(X,{CollapseItem:H}),G=_e(H)});export{G as E,xe as __tla,J as a};
