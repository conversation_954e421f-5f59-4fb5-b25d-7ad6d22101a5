import{d as e,o as r,l as s,__tla as o}from"./index-BUSn51wb.js";import{E as l,__tla as _}from"./el-image-BjHZRFih.js";let t,p=Promise.all([(()=>{try{return o}catch{}})(),(()=>{try{return _}catch{}})()]).then(async()=>{t=e({name:"UserWallet",__name:"index",props:{property:{}},setup:n=>(c,m)=>{const a=l;return r(),s(a,{src:"https://shopro.sheepjs.com/admin/static/images/shop/decorate/walletCardStyle.png"})}})});export{p as __tla,t as default};
