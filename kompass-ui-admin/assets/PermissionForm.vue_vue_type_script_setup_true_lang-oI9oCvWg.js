import{d as j,r as _,f as G,C as H,o as r,l as m,w as t,i as o,j as n,a as e,H as J,c as k,k as M,F as I,a9 as T,t as K,V as W,G as D,y as Q,n as X,I as Y,J as Z,K as $,L as ee,am as ae,an as le,ai as se,ca as te,O as ue,N as re,R as oe,__tla as de}from"./index-BUSn51wb.js";import{_ as ie,__tla as _e}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{g as ce,__tla as me}from"./index-BYXzDB8j.js";import{P as ne,B as C,c as ve,u as fe,__tla as pe}from"./index-pKzyIv29.js";let O,ye=Promise.all([(()=>{try{return de}catch{}})(),(()=>{try{return _e}catch{}})(),(()=>{try{return me}catch{}})(),(()=>{try{return pe}catch{}})()]).then(async()=>{O=j({name:"CrmPermissionForm",__name:"PermissionForm",emits:["success"],setup(be,{expose:z,emit:E}){const{t:p}=X(),h=Y(),i=_(!1),V=_(""),y=_(!1),v=_(""),S=_([]),s=_({}),N=G({userId:[{required:!0,message:"\u4EBA\u5458\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],level:[{required:!0,message:"\u6743\u9650\u7EA7\u522B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),b=_();z({open:async(u,l,d,c)=>{i.value=!0,V.value=p("action."+u)+"\u56E2\u961F\u6210\u5458",v.value=u,g(l,d),c&&(s.value.ids=c)},open0:async(u,l,d,c,f)=>{i.value=!0,V.value=p("action."+u)+"\u56E2\u961F\u6210\u5458",v.value=u,g(l,d),s.value.level=f,s.value.ids=[c]}});const U=E,B=async()=>{if(b&&await b.value.validate()){y.value=!0;try{const u=s.value;v.value==="create"?(await ve(e(u)),h.success(p("common.createSuccess"))):(await fe(e(u)),h.success(p("common.updateSuccess"))),i.value=!1,U("success")}finally{y.value=!1}}},g=(u,l)=>{var d;(d=b.value)==null||d.resetFields(),s.value={},s.value={...s.value,bizType:u,bizId:l}};return H(async()=>{S.value=await ce()}),(u,l)=>{const d=Z,c=$,f=ee,F=ae,P=le,R=se,L=te,x=ue,w=re,q=ie,A=oe;return r(),m(q,{modelValue:e(i),"onUpdate:modelValue":l[4]||(l[4]=a=>Q(i)?i.value=a:null),title:e(V),width:"30%"},{footer:t(()=>[o(w,{disabled:e(y),type:"primary",onClick:B},{default:t(()=>[n("\u786E \u5B9A")]),_:1},8,["disabled"]),o(w,{onClick:l[3]||(l[3]=a=>i.value=!1)},{default:t(()=>[n("\u53D6 \u6D88")]),_:1})]),default:t(()=>[J((r(),m(x,{ref_key:"formRef",ref:b,model:e(s),rules:e(N),"label-width":"100px"},{default:t(()=>[e(v)==="create"?(r(),m(f,{key:0,label:"\u9009\u62E9\u4EBA\u5458",prop:"userId"},{default:t(()=>[o(c,{modelValue:e(s).userId,"onUpdate:modelValue":l[0]||(l[0]=a=>e(s).userId=a)},{default:t(()=>[(r(!0),k(I,null,M(e(S),a=>(r(),m(d,{key:a.id,label:a.nickname,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):T("",!0),o(f,{label:"\u6743\u9650\u7EA7\u522B",prop:"level"},{default:t(()=>[o(P,{modelValue:e(s).level,"onUpdate:modelValue":l[1]||(l[1]=a=>e(s).level=a)},{default:t(()=>[(r(!0),k(I,null,M(e(W)(e(D).CRM_PERMISSION_LEVEL),a=>(r(),k(I,{key:a.value},[a.value!=e(ne).OWNER?(r(),m(F,{key:0,label:a.value},{default:t(()=>[n(K(a.label),1)]),_:2},1032,["label"])):T("",!0)],64))),128))]),_:1},8,["modelValue"])]),_:1}),e(v)==="create"&&e(s).bizType===e(C).CRM_CUSTOMER?(r(),m(f,{key:1,label:"\u540C\u65F6\u6DFB\u52A0\u81F3"},{default:t(()=>[o(L,{modelValue:e(s).toBizTypes,"onUpdate:modelValue":l[2]||(l[2]=a=>e(s).toBizTypes=a)},{default:t(()=>[o(R,{label:e(C).CRM_CONTACT},{default:t(()=>[n("\u8054\u7CFB\u4EBA")]),_:1},8,["label"]),o(R,{label:e(C).CRM_BUSINESS},{default:t(()=>[n("\u5546\u673A")]),_:1},8,["label"]),o(R,{label:e(C).CRM_CONTRACT},{default:t(()=>[n("\u5408\u540C")]),_:1},8,["label"])]),_:1},8,["modelValue"])]),_:1})):T("",!0)]),_:1},8,["model","rules"])),[[A,e(y)]])]),_:1},8,["modelValue","title"])}}})});export{O as _,ye as __tla};
