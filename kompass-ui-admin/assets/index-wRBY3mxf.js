import{d as v,p as n,at as d,a as t,r as m,o as h,l as y,w as g,i as q,y as p,ao as x,ci as I,Z as P,B as R,__tla as U}from"./index-BUSn51wb.js";import{P as b}from"./color-BN7ZL7BD.js";let i,w=Promise.all([(()=>{try{return U}catch{}})()]).then(async()=>{i=R(v({name:"InputWithColor",__name:"index",props:{modelValue:n.string.def("").isRequired,color:n.string.def("").isRequired},emits:["update:modelValue","update:color"],setup(c,{emit:_}){const s=c;d(()=>s.modelValue,e=>{e!==t(a)&&(a.value=e)});const r=_,a=m(s.modelValue);d(()=>a.value,e=>{r("update:modelValue",e)});const l=m(s.color);return d(()=>l.value,e=>{r("update:color",e)}),(e,o)=>{const V=I,f=P;return h(),y(f,x({modelValue:t(a),"onUpdate:modelValue":o[1]||(o[1]=u=>p(a)?a.value=u:null)},e.$attrs),{append:g(()=>[q(V,{modelValue:t(l),"onUpdate:modelValue":o[0]||(o[0]=u=>p(l)?l.value=u:null),predefine:t(b)},null,8,["modelValue","predefine"])]),_:1},16,["modelValue"])}}}),[["__scopeId","data-v-acc5b671"]])});export{i as _,w as __tla};
