import{d as W,r as m,f as q,C as z,o as f,c as N,i as a,w as s,j as b,a as l,H as j,l as k,G as D,F as A,N as I,aY as R,E as U,s as Y,P as G,Q as H,R as L,__tla as M}from"./index-BUSn51wb.js";import{_ as O,__tla as Q}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{E as V,__tla as J}from"./el-card-CJbXGyyg.js";import{E as K,__tla as X}from"./el-skeleton-item-tDN8U6BH.js";import{_ as Z,__tla as aa}from"./Echart.vue_vue_type_script_setup_true_lang-DnLZLH3w.js";import{S as v,__tla as ea}from"./funnel-B_PNiNbM.js";let g,ta=Promise.all([(()=>{try{return M}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return J}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return aa}catch{}})(),(()=>{try{return ea}catch{}})()]).then(async()=>{g=W({name:"FunnelBusiness",__name:"FunnelBusiness",props:{queryParams:{}},setup(S,{expose:w}){const _=S,d=m(!0),r=m(!1),p=m([]),n=q({title:{text:"\u9500\u552E\u6F0F\u6597"},tooltip:{trigger:"item",formatter:"{a} <br/>{b}"},toolbox:{feature:{dataView:{readOnly:!1},restore:{},saveAsImage:{}}},legend:{data:["\u5BA2\u6237","\u5546\u673A","\u8D62\u5355"]},series:[{name:"\u9500\u552E\u6F0F\u6597",type:"funnel",left:"10%",top:60,bottom:60,width:"80%",min:0,max:100,minSize:"0%",maxSize:"100%",sort:"descending",gap:2,label:{show:!0,position:"inside"},labelLine:{length:10,lineStyle:{width:1,type:"solid"}},itemStyle:{borderColor:"#fff",borderWidth:1},emphasis:{label:{fontSize:20}},data:[{value:60,name:"\u5BA2\u6237-0\u4E2A"},{value:40,name:"\u5546\u673A-0\u4E2A"},{value:20,name:"\u8D62\u5355-0\u4E2A"}]}]}),c=async e=>{d.value=e,await i()},i=async()=>{r.value=!0;const e=await v.getFunnelSummary(_.queryParams);if(e&&n.series&&n.series[0]&&n.series[0].data){const t=[];d.value?(t.push({value:60,name:`\u5BA2\u6237-${e.customerCount||0}\u4E2A`}),t.push({value:40,name:`\u5546\u673A-${e.businessCount||0}\u4E2A`}),t.push({value:20,name:`\u8D62\u5355-${e.businessWinCount||0}\u4E2A`})):(t.push({value:e.customerCount||0,name:`\u5BA2\u6237-${e.customerCount||0}\u4E2A`}),t.push({value:e.businessCount||0,name:`\u5546\u673A-${e.businessCount||0}\u4E2A`}),t.push({value:e.businessWinCount||0,name:`\u8D62\u5355-${e.businessWinCount||0}\u4E2A`})),n.series[0].data=t}p.value=await v.getBusinessSummaryByEndStatus(_.queryParams),r.value=!1};return w({loadData:i}),z(()=>{i()}),(e,t)=>{const h=I,C=R,x=Z,E=K,P=U,$=Y,y=V,u=G,B=O,F=H,T=L;return f(),N(A,null,[a(y,{shadow:"never"},{default:s(()=>[a($,null,{default:s(()=>[a(P,{span:24},{default:s(()=>[a(C,{class:"mb-10px"},{default:s(()=>[a(h,{type:"primary",onClick:t[0]||(t[0]=o=>c(!0))},{default:s(()=>[b("\u5BA2\u6237\u89C6\u89D2")]),_:1}),a(h,{type:"primary",onClick:t[1]||(t[1]=o=>c(!1))},{default:s(()=>[b("\u52A8\u6001\u89C6\u89D2")]),_:1})]),_:1}),a(E,{loading:l(r),animated:""},{default:s(()=>[a(x,{height:500,options:l(n)},null,8,["options"])]),_:1},8,["loading"])]),_:1})]),_:1})]),_:1}),a(y,{class:"mt-16px",shadow:"never"},{default:s(()=>[j((f(),k(F,{data:l(p)},{default:s(()=>[a(u,{align:"center",label:"\u5E8F\u53F7",type:"index",width:"80"}),a(u,{align:"center",label:"\u9636\u6BB5",prop:"endStatus",width:"200"},{default:s(o=>[a(B,{type:l(D).CRM_BUSINESS_END_STATUS_TYPE,value:o.row.endStatus},null,8,["type","value"])]),_:1}),a(u,{align:"center",label:"\u5546\u673A\u6570","min-width":"200",prop:"businessCount"}),a(u,{align:"center",label:"\u5546\u673A\u603B\u91D1\u989D(\u5143)","min-width":"200",prop:"totalPrice"})]),_:1},8,["data"])),[[T,l(r)]])]),_:1})],64)}}})});export{g as _,ta as __tla};
