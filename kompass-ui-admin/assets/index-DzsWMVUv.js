import{by as t,__tla as c}from"./index-BUSn51wb.js";let e,s,l,d,m,p,r,i=Promise.all([(()=>{try{return c}catch{}})()]).then(async()=>{e=async a=>await t.get({url:"/bpm/model/page",params:a}),p=async a=>await t.get({url:"/bpm/model/get?id="+a}),r=async a=>await t.put({url:"/bpm/model/update",data:a}),s=async(a,o)=>{const u={id:a,state:o};return await t.put({url:"/bpm/model/update-state",data:u})},l=async a=>await t.post({url:"/bpm/model/create",data:a}),d=async a=>await t.delete({url:"/bpm/model/delete?id="+a}),m=async a=>await t.post({url:"/bpm/model/deploy?id="+a})});export{i as __tla,e as a,s as b,l as c,d,m as e,p as g,r as u};
