import{d as X,I as Y,r as n,o as m,c as U,g as u,i as a,w as t,j as r,a as s,y as c,F as I,k as P,l as z,t as q,Z as $,N as ll,J as al,K as el,B as tl,__tla as ul}from"./index-BUSn51wb.js";import{E as sl,__tla as ol}from"./el-space-Dxj8A-LJ.js";import{E as dl,__tla as rl}from"./el-text-CIwNlU-U.js";import{I as pl,__tla as nl}from"./index-Cjd1fP7g.js";import{h as il,S as ml,b as cl,c as vl,a as E}from"./constants-C0I8ujwj.js";let H,_l=Promise.all([(()=>{try{return ul}catch{}})(),(()=>{try{return ol}catch{}})(),(()=>{try{return rl}catch{}})(),(()=>{try{return nl}catch{}})()]).then(async()=>{let N,O,A,B,L,T,G,j,J,K;N={class:"prompt"},O={class:"hot-words"},A={class:"group-item"},B={class:"group-item"},L={class:"group-item"},T={class:"group-item"},G={class:"group-item"},j={class:"group-item"},J={class:"group-item"},K={class:"btns"},H=tl(X({__name:"index",emits:["onDrawStart","onDrawComplete"],setup(yl,{expose:Q,emit:R}){const M=Y(),S=n(!1),D=n(""),v=n(""),y=n(512),g=n(512),f=n("DDIM"),w=n(20),b=n(42),V=n(7.5),h=n("NONE"),x=n("3d-model"),Z=R,W=async()=>{if(o=v.value,/[\u4e00-\u9fa5]/.test(o))M.alert("\u6682\u4E0D\u652F\u6301\u4E2D\u6587\uFF01");else{var o;await M.confirm("\u786E\u8BA4\u751F\u6210\u5185\u5BB9?");try{S.value=!0,Z("onDrawStart",E.STABLE_DIFFUSION);const e={platform:E.STABLE_DIFFUSION,model:"stable-diffusion-v1-6",prompt:v.value,width:y.value,height:g.value,options:{seed:b.value,steps:w.value,scale:V.value,sampler:f.value,clipGuidancePreset:h.value,stylePreset:x.value}};await pl.drawImage(e)}finally{Z("onDrawComplete",E.STABLE_DIFFUSION),S.value=!1}}};return Q({settingValues:async o=>{var e,d,i,k,p,_;v.value=o.prompt,y.value=o.width,g.value=o.height,b.value=(e=o.options)==null?void 0:e.seed,w.value=(d=o.options)==null?void 0:d.steps,V.value=(i=o.options)==null?void 0:i.scale,f.value=(k=o.options)==null?void 0:k.sampler,h.value=(p=o.options)==null?void 0:p.clipGuidancePreset,x.value=(_=o.options)==null?void 0:_.stylePreset}}),(o,e)=>{const d=dl,i=$,k=ll,p=sl,_=al,F=el;return m(),U(I,null,[u("div",N,[a(d,{tag:"b"},{default:t(()=>[r("\u753B\u9762\u63CF\u8FF0")]),_:1}),a(d,{tag:"p"},{default:t(()=>[r("\u5EFA\u8BAE\u4F7F\u7528\u201C\u5F62\u5BB9\u8BCD+\u52A8\u8BCD+\u98CE\u683C\u201D\u7684\u683C\u5F0F\uFF0C\u4F7F\u7528\u201C\uFF0C\u201D\u9694\u5F00")]),_:1}),a(i,{modelValue:s(v),"onUpdate:modelValue":e[0]||(e[0]=l=>c(v)?v.value=l:null),maxlength:"1024",rows:"5",class:"w-100% mt-15px","input-style":"border-radius: 7px;",placeholder:"\u4F8B\u5982\uFF1A\u7AE5\u8BDD\u91CC\u7684\u5C0F\u5C4B\u5E94\u8BE5\u662F\u4EC0\u4E48\u6837\u5B50\uFF1F","show-word-limit":"",type:"textarea"},null,8,["modelValue"])]),u("div",O,[u("div",null,[a(d,{tag:"b"},{default:t(()=>[r("\u968F\u673A\u70ED\u8BCD")]),_:1})]),a(p,{wrap:"",class:"word-list"},{default:t(()=>[(m(!0),U(I,null,P(s(il),l=>(m(),z(k,{round:"",class:"btn",type:s(D)===l?"primary":"default",key:l,onClick:gl=>(async C=>{D.value!=C?(D.value=C,v.value=C):D.value=""})(l)},{default:t(()=>[r(q(l),1)]),_:2},1032,["type","onClick"]))),128))]),_:1})]),u("div",A,[u("div",null,[a(d,{tag:"b"},{default:t(()=>[r("\u91C7\u6837\u65B9\u6CD5")]),_:1})]),a(p,{wrap:"",class:"group-item-body"},{default:t(()=>[a(F,{modelValue:s(f),"onUpdate:modelValue":e[1]||(e[1]=l=>c(f)?f.value=l:null),placeholder:"Select",size:"large",class:"!w-350px"},{default:t(()=>[(m(!0),U(I,null,P(s(ml),l=>(m(),z(_,{key:l.key,label:l.name,value:l.key},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),u("div",B,[u("div",null,[a(d,{tag:"b"},{default:t(()=>[r("CLIP")]),_:1})]),a(p,{wrap:"",class:"group-item-body"},{default:t(()=>[a(F,{modelValue:s(h),"onUpdate:modelValue":e[2]||(e[2]=l=>c(h)?h.value=l:null),placeholder:"Select",size:"large",class:"!w-350px"},{default:t(()=>[(m(!0),U(I,null,P(s(cl),l=>(m(),z(_,{key:l.key,label:l.name,value:l.key},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),u("div",L,[u("div",null,[a(d,{tag:"b"},{default:t(()=>[r("\u98CE\u683C")]),_:1})]),a(p,{wrap:"",class:"group-item-body"},{default:t(()=>[a(F,{modelValue:s(x),"onUpdate:modelValue":e[3]||(e[3]=l=>c(x)?x.value=l:null),placeholder:"Select",size:"large",class:"!w-350px"},{default:t(()=>[(m(!0),U(I,null,P(s(vl),l=>(m(),z(_,{key:l.key,label:l.name,value:l.key},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),u("div",T,[u("div",null,[a(d,{tag:"b"},{default:t(()=>[r("\u56FE\u7247\u5C3A\u5BF8")]),_:1})]),a(p,{wrap:"",class:"group-item-body"},{default:t(()=>[a(i,{modelValue:s(y),"onUpdate:modelValue":e[4]||(e[4]=l=>c(y)?y.value=l:null),class:"w-170px",placeholder:"\u56FE\u7247\u5BBD\u5EA6"},null,8,["modelValue"]),a(i,{modelValue:s(g),"onUpdate:modelValue":e[5]||(e[5]=l=>c(g)?g.value=l:null),class:"w-170px",placeholder:"\u56FE\u7247\u9AD8\u5EA6"},null,8,["modelValue"])]),_:1})]),u("div",G,[u("div",null,[a(d,{tag:"b"},{default:t(()=>[r("\u8FED\u4EE3\u6B65\u6570")]),_:1})]),a(p,{wrap:"",class:"group-item-body"},{default:t(()=>[a(i,{modelValue:s(w),"onUpdate:modelValue":e[6]||(e[6]=l=>c(w)?w.value=l:null),type:"number",size:"large",class:"!w-350px",placeholder:"Please input"},null,8,["modelValue"])]),_:1})]),u("div",j,[u("div",null,[a(d,{tag:"b"},{default:t(()=>[r("\u5F15\u5BFC\u7CFB\u6570")]),_:1})]),a(p,{wrap:"",class:"group-item-body"},{default:t(()=>[a(i,{modelValue:s(V),"onUpdate:modelValue":e[7]||(e[7]=l=>c(V)?V.value=l:null),type:"number",size:"large",class:"!w-350px",placeholder:"Please input"},null,8,["modelValue"])]),_:1})]),u("div",J,[u("div",null,[a(d,{tag:"b"},{default:t(()=>[r("\u968F\u673A\u56E0\u5B50")]),_:1})]),a(p,{wrap:"",class:"group-item-body"},{default:t(()=>[a(i,{modelValue:s(b),"onUpdate:modelValue":e[8]||(e[8]=l=>c(b)?b.value=l:null),type:"number",size:"large",class:"!w-350px",placeholder:"Please input"},null,8,["modelValue"])]),_:1})]),u("div",K,[a(k,{type:"primary",size:"large",round:"",loading:s(S),onClick:W},{default:t(()=>[r(q(s(S)?"\u751F\u6210\u4E2D":"\u751F\u6210\u5185\u5BB9"),1)]),_:1},8,["loading"])])],64)}}}),[["__scopeId","data-v-3fe20467"]])});export{_l as __tla,H as default};
