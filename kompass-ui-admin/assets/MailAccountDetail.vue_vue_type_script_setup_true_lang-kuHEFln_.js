import{_ as i,__tla as d}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{_ as p,__tla as y}from"./Descriptions-DnGjMn9o.js";import{g as h,__tla as f}from"./index-Dtskw_Cu.js";import{a as v,__tla as V}from"./account.data-fya8ok4m.js";import{d as w,r as t,o as x,l as A,w as D,i as M,a as l,y as S,__tla as g}from"./index-BUSn51wb.js";let o,P=Promise.all([(()=>{try{return d}catch{}})(),(()=>{try{return y}catch{}})(),(()=>{try{return f}catch{}})(),(()=>{try{return V}catch{}})(),(()=>{try{return g}catch{}})()]).then(async()=>{o=w({name:"SystemMailAccountDetail",__name:"MailAccountDetail",setup(U,{expose:c}){const a=t(!1),e=t(!1),r=t();return c({open:async _=>{a.value=!0,e.value=!0;try{r.value=await h(_)}finally{e.value=!1}}}),(_,s)=>{const n=p,u=i;return x(),A(u,{modelValue:l(a),"onUpdate:modelValue":s[0]||(s[0]=m=>S(a)?a.value=m:null),title:"\u8BE6\u60C5"},{default:D(()=>[M(n,{data:l(r),schema:l(v).detailSchema},null,8,["data","schema"])]),_:1},8,["modelValue"])}}})});export{o as _,P as __tla};
