import{d as C,r as m,f as U,at as q,o as g,c as j,H as D,a as d,l as F,w as a,i as e,j as h,F as P,P as R,Z as H,L,N,Q as O,O as Q,s as Z,R as z,__tla as A}from"./index-BUSn51wb.js";import{a as B,__tla as E}from"./index-DrnBZ6x8.js";let x,G=Promise.all([(()=>{try{return A}catch{}})(),(()=>{try{return E}catch{}})()]).then(async()=>{x=C({__name:"Demo03CourseForm",props:{studentId:{}},setup(b,{expose:w}){const c=b,n=m(!1),l=m([]),i=U({studentId:[{required:!0,message:"\u5B66\u751F\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],name:[{required:!0,message:"\u540D\u5B57\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],score:[{required:!0,message:"\u5206\u6570\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),p=m();q(()=>c.studentId,async s=>{if(l.value=[],s)try{n.value=!0,l.value=await B(s)}finally{n.value=!1}},{immediate:!0});const y=()=>{const s={id:void 0,studentId:void 0,name:void 0,score:void 0};s.studentId=c.studentId,l.value.push(s)};return w({validate:()=>p.value.validate(),getData:()=>l.value}),(s,J)=>{const u=R,_=H,f=L,v=N,V=O,I=Q,k=Z,$=z;return g(),j(P,null,[D((g(),F(I,{ref_key:"formRef",ref:p,model:d(l),rules:d(i),"label-width":"0px","inline-message":!0},{default:a(()=>[e(V,{data:d(l),class:"-mt-10px"},{default:a(()=>[e(u,{label:"\u5E8F\u53F7",type:"index",width:"100"}),e(u,{label:"\u540D\u5B57","min-width":"150"},{default:a(({row:t,$index:o})=>[e(f,{prop:`${o}.name`,rules:d(i).name,class:"mb-0px!"},{default:a(()=>[e(_,{modelValue:t.name,"onUpdate:modelValue":r=>t.name=r,placeholder:"\u8BF7\u8F93\u5165\u540D\u5B57"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(u,{label:"\u5206\u6570","min-width":"150"},{default:a(({row:t,$index:o})=>[e(f,{prop:`${o}.score`,rules:d(i).score,class:"mb-0px!"},{default:a(()=>[e(_,{modelValue:t.score,"onUpdate:modelValue":r=>t.score=r,placeholder:"\u8BF7\u8F93\u5165\u5206\u6570"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(u,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"60"},{default:a(({$index:t})=>[e(v,{onClick:o=>{return r=t,void l.value.splice(r,1);var r},link:""},{default:a(()=>[h("\u2014")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["model","rules"])),[[$,d(n)]]),e(k,{justify:"center",class:"mt-3"},{default:a(()=>[e(v,{onClick:y,round:""},{default:a(()=>[h("+ \u6DFB\u52A0\u5B66\u751F\u8BFE\u7A0B")]),_:1})]),_:1})],64)}}})});export{x as _,G as __tla};
