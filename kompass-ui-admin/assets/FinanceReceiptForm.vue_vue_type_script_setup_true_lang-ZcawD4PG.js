import{by as m,d as ae,n as le,I as te,r as s,f as ie,b as re,at as ue,o as p,l as v,w as t,a as e,j as E,a9 as de,i as a,H as ce,c as k,F as R,k as x,y as H,dX as J,Z as se,L as oe,E as ne,M as pe,J as me,K as fe,cn as _e,s as ve,z as be,A as Ve,cc as ye,O as we,N as Ue,R as ge,__tla as Pe}from"./index-BUSn51wb.js";import{_ as he,__tla as Ie}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{_ as Fe,__tla as ke}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as Re,__tla as xe}from"./FinanceReceiptItemForm.vue_vue_type_script_setup_true_lang-DjMAEWZQ.js";import{g as Se,__tla as Te}from"./index-BYXzDB8j.js";import{A as Ce,__tla as Ae}from"./index-LbO7ASKC.js";import{C as Le,__tla as je}from"./index-DYwp4_G0.js";let b,K,qe=Promise.all([(()=>{try{return Pe}catch{}})(),(()=>{try{return Ie}catch{}})(),(()=>{try{return ke}catch{}})(),(()=>{try{return xe}catch{}})(),(()=>{try{return Te}catch{}})(),(()=>{try{return Ae}catch{}})(),(()=>{try{return je}catch{}})()]).then(async()=>{b={getFinanceReceiptPage:async u=>await m.get({url:"/erp/finance-receipt/page",params:u}),getFinanceReceipt:async u=>await m.get({url:"/erp/finance-receipt/get?id="+u}),createFinanceReceipt:async u=>await m.post({url:"/erp/finance-receipt/create",data:u}),updateFinanceReceipt:async u=>await m.put({url:"/erp/finance-receipt/update",data:u}),updateFinanceReceiptStatus:async(u,V)=>await m.put({url:"/erp/finance-receipt/update-status",params:{id:u,status:V}}),deleteFinanceReceipt:async u=>await m.delete({url:"/erp/finance-receipt/delete",params:{ids:u.join(",")}}),exportFinanceReceipt:async u=>await m.download({url:"/erp/finance-receipt/export-excel",params:u})},K=ae({name:"FinanceReceiptForm",__name:"FinanceReceiptForm",emits:["success"],setup(u,{expose:V,emit:M}){const{t:y}=le(),S=te(),f=s(!1),T=s(""),_=s(!1),w=s(""),r=s({id:void 0,customerId:void 0,accountId:void 0,financeUserId:void 0,receiptTime:void 0,remark:void 0,fileUrl:"",totalPrice:0,discountPrice:0,receiptPrice:0,items:[],no:void 0}),N=ie({customerId:[{required:!0,message:"\u5BA2\u6237\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],receiptTime:[{required:!0,message:"\u8BA2\u5355\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),U=re(()=>w.value==="detail"),g=s(),C=s([]),P=s([]),A=s([]),h=s("item"),L=s();ue(()=>r.value,c=>{if(!c)return;const i=c.items.reduce((n,d)=>n+d.receiptPrice,0);r.value.totalPrice=i,r.value.receiptPrice=i-c.discountPrice},{deep:!0}),V({open:async(c,i)=>{if(f.value=!0,T.value=y("action."+c),w.value=c,D(),i){_.value=!0;try{r.value=await b.getFinanceReceipt(i)}finally{_.value=!1}}C.value=await Le.getCustomerSimpleList(),A.value=await Se(),P.value=await Ce.getAccountSimpleList();const n=P.value.find(d=>d.defaultStatus);n&&(r.value.accountId=n.id)}});const Z=M,z=async()=>{await g.value.validate(),await L.value.validate(),_.value=!0;try{const c=r.value;w.value==="create"?(await b.createFinanceReceipt(c),S.success(y("common.createSuccess"))):(await b.updateFinanceReceipt(c),S.success(y("common.updateSuccess"))),f.value=!1,Z("success")}finally{_.value=!1}},D=()=>{var c;r.value={id:void 0,customerId:void 0,accountId:void 0,financeUserId:void 0,receiptTime:void 0,remark:void 0,fileUrl:void 0,totalPrice:0,discountPrice:0,receiptPrice:0,items:[],no:void 0},(c=g.value)==null||c.resetFields()};return(c,i)=>{const n=se,d=oe,o=ne,O=pe,I=me,F=fe,X=_e,j=ve,B=be,G=Ve,Q=Fe,W=ye,Y=we,q=Ue,$=he,ee=ge;return p(),v($,{title:e(T),modelValue:e(f),"onUpdate:modelValue":i[12]||(i[12]=l=>H(f)?f.value=l:null),width:"1080"},{footer:t(()=>[e(U)?de("",!0):(p(),v(q,{key:0,onClick:z,type:"primary",disabled:e(_)},{default:t(()=>[E(" \u786E \u5B9A ")]),_:1},8,["disabled"])),a(q,{onClick:i[11]||(i[11]=l=>f.value=!1)},{default:t(()=>[E("\u53D6 \u6D88")]),_:1})]),default:t(()=>[ce((p(),v(Y,{ref_key:"formRef",ref:g,model:e(r),rules:e(N),"label-width":"100px",disabled:e(U)},{default:t(()=>[a(j,{gutter:20},{default:t(()=>[a(o,{span:8},{default:t(()=>[a(d,{label:"\u6536\u6B3E\u5355\u53F7",prop:"no"},{default:t(()=>[a(n,{disabled:"",modelValue:e(r).no,"onUpdate:modelValue":i[0]||(i[0]=l=>e(r).no=l),placeholder:"\u4FDD\u5B58\u65F6\u81EA\u52A8\u751F\u6210"},null,8,["modelValue"])]),_:1})]),_:1}),a(o,{span:8},{default:t(()=>[a(d,{label:"\u6536\u6B3E\u65F6\u95F4",prop:"receiptTime"},{default:t(()=>[a(O,{modelValue:e(r).receiptTime,"onUpdate:modelValue":i[1]||(i[1]=l=>e(r).receiptTime=l),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u6536\u6B3E\u65F6\u95F4",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),a(o,{span:8},{default:t(()=>[a(d,{label:"\u5BA2\u6237",prop:"customerId"},{default:t(()=>[a(F,{modelValue:e(r).customerId,"onUpdate:modelValue":i[2]||(i[2]=l=>e(r).customerId=l),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237",class:"!w-1/1"},{default:t(()=>[(p(!0),k(R,null,x(e(C),l=>(p(),v(I,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(o,{span:8},{default:t(()=>[a(d,{label:"\u8D22\u52A1\u4EBA\u5458",prop:"financeUserId"},{default:t(()=>[a(F,{modelValue:e(r).financeUserId,"onUpdate:modelValue":i[3]||(i[3]=l=>e(r).financeUserId=l),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u8D22\u52A1\u4EBA\u5458",class:"!w-1/1"},{default:t(()=>[(p(!0),k(R,null,x(e(A),l=>(p(),v(I,{key:l.id,label:l.nickname,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(o,{span:16},{default:t(()=>[a(d,{label:"\u5907\u6CE8",prop:"remark"},{default:t(()=>[a(n,{type:"textarea",modelValue:e(r).remark,"onUpdate:modelValue":i[4]||(i[4]=l=>e(r).remark=l),rows:1,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1}),a(o,{span:8},{default:t(()=>[a(d,{label:"\u9644\u4EF6",prop:"fileUrl"},{default:t(()=>[a(X,{"is-show-tip":!1,modelValue:e(r).fileUrl,"onUpdate:modelValue":i[5]||(i[5]=l=>e(r).fileUrl=l),limit:1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),a(Q,null,{default:t(()=>[a(G,{modelValue:e(h),"onUpdate:modelValue":i[6]||(i[6]=l=>H(h)?h.value=l:null),class:"-mt-15px -mb-10px"},{default:t(()=>[a(B,{label:"\u91C7\u8D2D\u5165\u5E93\u3001\u9000\u8D27\u5355",name:"item"},{default:t(()=>[a(Re,{ref_key:"itemFormRef",ref:L,"customer-id":e(r).customerId,items:e(r).items,disabled:e(U)},null,8,["customer-id","items","disabled"])]),_:1})]),_:1},8,["modelValue"])]),_:1}),a(j,{gutter:20},{default:t(()=>[a(o,{span:8},{default:t(()=>[a(d,{label:"\u6536\u6B3E\u8D26\u6237",prop:"accountId"},{default:t(()=>[a(F,{modelValue:e(r).accountId,"onUpdate:modelValue":i[7]||(i[7]=l=>e(r).accountId=l),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u7ED3\u7B97\u8D26\u6237",class:"!w-1/1"},{default:t(()=>[(p(!0),k(R,null,x(e(P),l=>(p(),v(I,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(o,{span:8},{default:t(()=>[a(d,{label:"\u5408\u8BA1\u6536\u6B3E",prop:"totalPrice"},{default:t(()=>[a(n,{disabled:"",modelValue:e(r).totalPrice,"onUpdate:modelValue":i[8]||(i[8]=l=>e(r).totalPrice=l),formatter:e(J)},null,8,["modelValue","formatter"])]),_:1})]),_:1}),a(o,{span:8},{default:t(()=>[a(d,{label:"\u4F18\u60E0\u91D1\u989D",prop:"discountPrice"},{default:t(()=>[a(W,{modelValue:e(r).discountPrice,"onUpdate:modelValue":i[9]||(i[9]=l=>e(r).discountPrice=l),"controls-position":"right",precision:2,placeholder:"\u8BF7\u8F93\u5165\u4F18\u60E0\u91D1\u989D",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),a(o,{span:8},{default:t(()=>[a(d,{label:"\u5B9E\u9645\u6536\u6B3E"},{default:t(()=>[a(n,{disabled:"",modelValue:e(r).receiptPrice,"onUpdate:modelValue":i[10]||(i[10]=l=>e(r).receiptPrice=l),formatter:e(J)},null,8,["modelValue","formatter"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules","disabled"])),[[ee,e(_)]])]),_:1},8,["title","modelValue"])}}})});export{b as F,K as _,qe as __tla};
