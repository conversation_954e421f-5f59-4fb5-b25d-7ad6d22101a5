import{_ as t,__tla as r}from"./PermissionList.vue_vue_type_script_setup_true_lang--VdCh_pH.js";import{__tla as _}from"./index-BUSn51wb.js";import{__tla as a}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import"./color-BN7ZL7BD.js";import{__tla as l}from"./formatTime-DWdBpgsM.js";import{__tla as o}from"./index-pKzyIv29.js";import{__tla as m}from"./PermissionForm.vue_vue_type_script_setup_true_lang-oI9oCvWg.js";import{__tla as c}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as e}from"./index-BYXzDB8j.js";let s=Promise.all([(()=>{try{return r}catch{}})(),(()=>{try{return _}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return m}catch{}})(),(()=>{try{return c}catch{}})(),(()=>{try{return e}catch{}})()]).then(async()=>{});export{s as __tla,t as default};
