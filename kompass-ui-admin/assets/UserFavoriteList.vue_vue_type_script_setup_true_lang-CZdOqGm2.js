import{by as C,d as N,r as n,f as S,C as I,o as m,l as d,w as i,H as T,a as t,i as e,j as k,t as x,aB as z,G as j,I as R,n as q,P as A,Q as B,R as D,__tla as E}from"./index-BUSn51wb.js";import{_ as F,__tla as G}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as H,__tla as L}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as O,__tla as Q}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{E as J,__tla as K}from"./el-image-BjHZRFih.js";import{d as M,__tla as V}from"./formatTime-DWdBpgsM.js";let g,W=Promise.all([(()=>{try{return E}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return L}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return K}catch{}})(),(()=>{try{return V}catch{}})()]).then(async()=>{g=N({__name:"UserFavoriteList",props:{userId:{type:Number,required:!0}},setup(f){R(),q();const _=n(!0),p=n(0),u=n([]),s=S({pageNo:1,pageSize:10,name:null,createTime:[],userId:NaN});n();const c=async()=>{_.value=!0;try{const r=await(o=s,C.get({url:"/product/favorite/page",params:o}));u.value=r.list,p.value=r.total}finally{_.value=!1}var o},{userId:h}=f;return I(()=>{s.userId=h,c()}),(o,r)=>{const l=A,w=J,y=O,v=B,b=H,U=F,P=D;return m(),d(U,null,{default:i(()=>[T((m(),d(v,{data:t(u)},{default:i(()=>[e(l,{key:"id",align:"center",label:"\u5546\u54C1\u7F16\u53F7",width:"180",prop:"id"}),e(l,{label:"\u5546\u54C1\u56FE","min-width":"80"},{default:i(({row:a})=>[e(w,{src:a.picUrl,class:"h-30px w-30px",onClick:X=>o.imagePreview(a.picUrl)},null,8,["src","onClick"])]),_:1}),e(l,{"show-overflow-tooltip":!0,label:"\u5546\u54C1\u540D\u79F0","min-width":"300",prop:"name"}),e(l,{align:"center",label:"\u5546\u54C1\u552E\u4EF7","min-width":"90",prop:"price"},{default:i(({row:a})=>[k(x(t(z)(a.price))+"\u5143",1)]),_:1}),e(l,{align:"center",label:"\u9500\u91CF","min-width":"90",prop:"salesCount"}),e(l,{formatter:t(M),align:"center",label:"\u6536\u85CF\u65F6\u95F4",prop:"createTime",width:"180"},null,8,["formatter"]),e(l,{align:"center",label:"\u72B6\u6001","min-width":"80"},{default:i(a=>[e(y,{type:t(j).PRODUCT_SPU_STATUS,value:a.row.status},null,8,["type","value"])]),_:1})]),_:1},8,["data"])),[[P,t(_)]]),e(b,{total:t(p),page:t(s).pageNo,"onUpdate:page":r[0]||(r[0]=a=>t(s).pageNo=a),limit:t(s).pageSize,"onUpdate:limit":r[1]||(r[1]=a=>t(s).pageSize=a),onPagination:c},null,8,["total","page","limit"])]),_:1})}}})});export{g as _,W as __tla};
