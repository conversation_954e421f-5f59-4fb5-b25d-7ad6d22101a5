import{d as j,n as q,I as H,r as m,f as I,o as V,l as b,w as s,i as d,a,j as h,H as D,y as L,Z as N,L as O,O as Z,N as z,R as A,__tla as B}from"./index-BUSn51wb.js";import{_ as E,__tla as G}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{g as J,b as K,f as M,__tla as Q}from"./property-BdOytbZT.js";let k,T=Promise.all([(()=>{try{return B}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return Q}catch{}})()]).then(async()=>{k=j({name:"ProductPropertyForm",__name:"PropertyForm",emits:["success"],setup(W,{expose:w,emit:g}){const{t:c}=q(),i=H(),r=m(!1),_=m(""),u=m(!1),p=m(""),t=m({id:void 0,name:""}),P=I({name:[{required:!0,message:"\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),n=m();w({open:async(l,e)=>{if(r.value=!0,_.value=c("action."+l),p.value=l,U(),e){u.value=!0;try{t.value=await J(e)}finally{u.value=!1}}}});const x=g,F=async()=>{if(n&&await n.value.validate()){u.value=!0;try{const l=t.value;p.value==="create"?(await K(l),i.success(c("common.createSuccess"))):(await M(l),i.success(c("common.updateSuccess"))),r.value=!1,x("success")}finally{u.value=!1}}},U=()=>{var l;t.value={id:void 0,name:""},(l=n.value)==null||l.resetFields()};return(l,e)=>{const f=N,v=O,C=Z,y=z,R=E,S=A;return V(),b(R,{modelValue:a(r),"onUpdate:modelValue":e[3]||(e[3]=o=>L(r)?r.value=o:null),title:a(_)},{footer:s(()=>[d(y,{disabled:a(u),type:"primary",onClick:F},{default:s(()=>[h("\u786E \u5B9A")]),_:1},8,["disabled"]),d(y,{onClick:e[2]||(e[2]=o=>r.value=!1)},{default:s(()=>[h("\u53D6 \u6D88")]),_:1})]),default:s(()=>[D((V(),b(C,{ref_key:"formRef",ref:n,model:a(t),rules:a(P),"label-width":"80px"},{default:s(()=>[d(v,{label:"\u540D\u79F0",prop:"name"},{default:s(()=>[d(f,{modelValue:a(t).name,"onUpdate:modelValue":e[0]||(e[0]=o=>a(t).name=o),placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0"},null,8,["modelValue"])]),_:1}),d(v,{label:"\u5907\u6CE8",prop:"remark"},{default:s(()=>[d(f,{modelValue:a(t).remark,"onUpdate:modelValue":e[1]||(e[1]=o=>a(t).remark=o),placeholder:"\u8BF7\u8F93\u5165\u5185\u5BB9",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[S,a(u)]])]),_:1},8,["modelValue","title"])}}})});export{k as _,T as __tla};
