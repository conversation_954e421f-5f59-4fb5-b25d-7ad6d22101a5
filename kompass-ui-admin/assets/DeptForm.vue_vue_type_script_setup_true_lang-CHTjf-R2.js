import{d as G,n as H,I as J,r as u,f as K,o as m,l as c,w as s,i as r,j as k,H as P,a as l,c as w,F as x,k as S,V as Z,G as $,y as z,L as Q,Z as W,cc as Y,J as X,K as ee,O as ae,N as le,R as te,__tla as re}from"./index-BUSn51wb.js";import{_ as se,__tla as oe}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{E as ue,__tla as de}from"./el-tree-select-CBuha0HW.js";import{d as ie,h as me}from"./tree-BMa075Oj.js";import{a as ne,c as pe,u as ce,g as _e,__tla as ve}from"./index-Bqt292RI.js";import{g as fe,__tla as he}from"./index-BYXzDB8j.js";import{C as q}from"./constants-A8BI3pz7.js";let C,Ve=Promise.all([(()=>{try{return re}catch{}})(),(()=>{try{return oe}catch{}})(),(()=>{try{return de}catch{}})(),(()=>{try{return ve}catch{}})(),(()=>{try{return he}catch{}})()]).then(async()=>{C=G({name:"SystemDeptForm",__name:"DeptForm",emits:["success"],setup(ge,{expose:E,emit:F}){const{t:_}=H(),h=J(),i=u(!1),V=u(""),n=u(!1),g=u(""),t=u({id:void 0,title:"",parentId:void 0,name:void 0,sort:void 0,leaderUserId:void 0,phone:void 0,email:void 0,status:q.ENABLE}),N=K({parentId:[{required:!0,message:"\u4E0A\u7EA7\u90E8\u95E8\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],name:[{required:!0,message:"\u90E8\u95E8\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],sort:[{required:!0,message:"\u663E\u793A\u6392\u5E8F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],email:[{type:"email",message:"\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u90AE\u7BB1\u5730\u5740",trigger:["blur","change"]}],phone:[{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u624B\u673A\u53F7\u7801",trigger:"blur"}],status:[{required:!0,message:"\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),p=u(),v=u(),y=u([]);E({open:async(o,e)=>{if(i.value=!0,V.value=_("action."+o),g.value=o,O(),e){n.value=!0;try{t.value=await ne(e)}finally{n.value=!1}}y.value=await fe(),await D()}});const A=F,B=async()=>{if(p&&await p.value.validate()){n.value=!0;try{const o=t.value;g.value==="create"?(await pe(o),h.success(_("common.createSuccess"))):(await ce(o),h.success(_("common.updateSuccess"))),i.value=!1,A("success")}finally{n.value=!1}}},O=()=>{var o;t.value={id:void 0,title:"",parentId:void 0,name:void 0,sort:void 0,leaderUserId:void 0,phone:void 0,email:void 0,status:q.ENABLE},(o=p.value)==null||o.resetFields()},D=async()=>{v.value=[];const o=await _e();let e={id:0,name:"\u9876\u7EA7\u90E8\u95E8",children:[]};e.children=me(o),v.value.push(e)};return(o,e)=>{const L=ue,d=Q,f=W,M=Y,b=X,U=ee,R=ae,I=le,T=se,j=te;return m(),c(T,{modelValue:l(i),"onUpdate:modelValue":e[8]||(e[8]=a=>z(i)?i.value=a:null),title:l(V)},{footer:s(()=>[r(I,{type:"primary",onClick:B},{default:s(()=>[k("\u786E \u5B9A")]),_:1}),r(I,{onClick:e[7]||(e[7]=a=>i.value=!1)},{default:s(()=>[k("\u53D6 \u6D88")]),_:1})]),default:s(()=>[P((m(),c(R,{ref_key:"formRef",ref:p,model:l(t),rules:l(N),"label-width":"80px"},{default:s(()=>[r(d,{label:"\u4E0A\u7EA7\u90E8\u95E8",prop:"parentId"},{default:s(()=>[r(L,{modelValue:l(t).parentId,"onUpdate:modelValue":e[0]||(e[0]=a=>l(t).parentId=a),data:l(v),props:l(ie),"check-strictly":"","default-expand-all":"",placeholder:"\u8BF7\u9009\u62E9\u4E0A\u7EA7\u90E8\u95E8","value-key":"deptId"},null,8,["modelValue","data","props"])]),_:1}),r(d,{label:"\u90E8\u95E8\u540D\u79F0",prop:"name"},{default:s(()=>[r(f,{modelValue:l(t).name,"onUpdate:modelValue":e[1]||(e[1]=a=>l(t).name=a),placeholder:"\u8BF7\u8F93\u5165\u90E8\u95E8\u540D\u79F0"},null,8,["modelValue"])]),_:1}),r(d,{label:"\u663E\u793A\u6392\u5E8F",prop:"sort"},{default:s(()=>[r(M,{modelValue:l(t).sort,"onUpdate:modelValue":e[2]||(e[2]=a=>l(t).sort=a),min:0,"controls-position":"right"},null,8,["modelValue"])]),_:1}),r(d,{label:"\u8D1F\u8D23\u4EBA",prop:"leaderUserId"},{default:s(()=>[r(U,{modelValue:l(t).leaderUserId,"onUpdate:modelValue":e[3]||(e[3]=a=>l(t).leaderUserId=a),clearable:"",placeholder:"\u8BF7\u8F93\u5165\u8D1F\u8D23\u4EBA"},{default:s(()=>[(m(!0),w(x,null,S(l(y),a=>(m(),c(b,{key:a.id,label:a.nickname,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),r(d,{label:"\u8054\u7CFB\u7535\u8BDD",prop:"phone"},{default:s(()=>[r(f,{modelValue:l(t).phone,"onUpdate:modelValue":e[4]||(e[4]=a=>l(t).phone=a),maxlength:"11",placeholder:"\u8BF7\u8F93\u5165\u8054\u7CFB\u7535\u8BDD"},null,8,["modelValue"])]),_:1}),r(d,{label:"\u90AE\u7BB1",prop:"email"},{default:s(()=>[r(f,{modelValue:l(t).email,"onUpdate:modelValue":e[5]||(e[5]=a=>l(t).email=a),maxlength:"50",placeholder:"\u8BF7\u8F93\u5165\u90AE\u7BB1"},null,8,["modelValue"])]),_:1}),r(d,{label:"\u72B6\u6001",prop:"status"},{default:s(()=>[r(U,{modelValue:l(t).status,"onUpdate:modelValue":e[6]||(e[6]=a=>l(t).status=a),clearable:"",placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001"},{default:s(()=>[(m(!0),w(x,null,S(l(Z)(l($).COMMON_STATUS),a=>(m(),c(b,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[j,l(n)]])]),_:1},8,["modelValue","title"])}}})});export{C as _,Ve as __tla};
