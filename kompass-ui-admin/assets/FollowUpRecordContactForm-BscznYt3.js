import{_ as t,__tla as a}from"./FollowUpRecordContactForm.vue_vue_type_script_setup_true_lang-DH5izzTW.js";import{__tla as r}from"./index-BUSn51wb.js";import{__tla as _}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import"./color-BN7ZL7BD.js";let l=Promise.all([(()=>{try{return a}catch{}})(),(()=>{try{return r}catch{}})(),(()=>{try{return _}catch{}})()]).then(async()=>{});export{l as __tla,t as default};
