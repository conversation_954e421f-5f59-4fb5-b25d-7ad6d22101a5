import{i as v}from"./color-BN7ZL7BD.js";import{d as y,r as f,i as m,ax as _,cJ as d,__tla as T}from"./index-BUSn51wb.js";let i,g=Promise.all([(()=>{try{return T}catch{}})()]).then(async()=>{i=y({name:"DictTag",props:{type:{type:String,required:!0},value:{type:[String,Number,Boolean],required:!0}},setup(l){const e=f(),n=()=>{var t,o,u,c,p;return l.type?l.value===void 0||l.value===null?null:(r=l.type,s=l.value.toString(),d(r).forEach(a=>{a.value===s&&(a.colorType+""!="primary"&&a.colorType+""!="default"||(a.colorType=""),e.value=a)}),m(_,{style:(t=e.value)!=null&&t.cssClass?"color: #fff":"",type:(o=e.value)==null?void 0:o.colorType,color:(u=e.value)!=null&&u.cssClass&&v((c=e.value)==null?void 0:c.cssClass)?(p=e.value)==null?void 0:p.cssClass:"",disableTransitions:!0},{default:()=>{var a;return[(a=e.value)==null?void 0:a.label]}})):null;var r,s};return()=>n()}})});export{i as _,g as __tla};
