import{_ as R,__tla as T}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{d as g,r as j,T as k,o as i,c as w,g as l,i as a,w as t,t as _,H as D,l as E,j as u,a as f,G as H,dX as U,F,s as G,E as I,N as M,__tla as O}from"./index-BUSn51wb.js";import{E as S,a as X,__tla as q}from"./el-descriptions-item-dD3qa0ub.js";import{_ as z,__tla as A}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as B,__tla as J}from"./ProductForm.vue_vue_type_script_setup_true_lang-G9Bcq_BM.js";let m,K=Promise.all([(()=>{try{return T}catch{}})(),(()=>{try{return O}catch{}})(),(()=>{try{return q}catch{}})(),(()=>{try{return A}catch{}})(),(()=>{try{return J}catch{}})()]).then(async()=>{let o,c;o={class:"flex items-start justify-between"},c={class:"text-xl font-bold"},m=g({__name:"ProductDetailsHeader",props:{product:{}},setup(L){const n=j();return(e,r)=>{const y=G,v=I,h=M,s=S,b=z,x=X,N=R,P=k("hasPermi");return i(),w(F,null,[l("div",null,[l("div",o,[l("div",null,[a(v,null,{default:t(()=>[a(y,null,{default:t(()=>[l("span",c,_(e.product.name),1)]),_:1})]),_:1})]),l("div",null,[D((i(),E(h,{onClick:r[0]||(r[0]=C=>{return d="update",p=e.product.id,void n.value.open(d,p);var d,p})},{default:t(()=>[u(" \u7F16\u8F91 ")]),_:1})),[[P,["crm:product:update"]]])])])]),a(N,{class:"mt-10px"},{default:t(()=>[a(x,{column:5,direction:"vertical"},{default:t(()=>[a(s,{label:"\u4EA7\u54C1\u7C7B\u522B"},{default:t(()=>[u(_(e.product.categoryName),1)]),_:1}),a(s,{label:"\u4EA7\u54C1\u5355\u4F4D"},{default:t(()=>[a(b,{type:f(H).CRM_PRODUCT_UNIT,value:e.product.unit},null,8,["type","value"])]),_:1}),a(s,{label:"\u4EA7\u54C1\u4EF7\u683C"},{default:t(()=>[u(_(f(U)(e.product.price))+" \u5143 ",1)]),_:1}),a(s,{label:"\u4EA7\u54C1\u7F16\u7801"},{default:t(()=>[u(_(e.product.no),1)]),_:1})]),_:1})]),_:1}),a(B,{ref_key:"formRef",ref:n,onSuccess:r[1]||(r[1]=C=>e.emit("refresh"))},null,512)],64)}}})});export{m as _,K as __tla};
