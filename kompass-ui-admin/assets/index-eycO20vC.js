import{d as G,I as K,r as V,f as j,C as H,o as n,c as f,i as e,w as a,H as U,l as p,a as r,a8 as L,F as m,k as _,j as o,V as w,G as h,t as P,aD as J,Z as Y,L as Z,J as Q,K as X,z as $,ce as ee,cc as le,am as ae,an as re,cm as se,ai as te,ca as oe,A as de,N as ue,O as ne,R as ie,__tla as ce}from"./index-BUSn51wb.js";import{_ as be,__tla as fe}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{E as pe,__tla as me}from"./el-text-CIwNlU-U.js";import{_ as _e,__tla as ge}from"./index-COobLwz-.js";import{s as ye,g as ke,__tla as Ve}from"./index-Brylag5m.js";import{__tla as we}from"./el-card-CJbXGyyg.js";let W,he=Promise.all([(()=>{try{return ce}catch{}})(),(()=>{try{return fe}catch{}})(),(()=>{try{return me}catch{}})(),(()=>{try{return ge}catch{}})(),(()=>{try{return Ve}catch{}})(),(()=>{try{return we}catch{}})()]).then(async()=>{W=G({name:"TradeConfig",__name:"index",setup(Pe){const z=K(),c=V(!1),g=V(),s=V({id:null,afterSaleRefundReasons:[],afterSaleReturnReasons:[],deliveryExpressFreeEnabled:!1,deliveryExpressFreePrice:0,deliveryPickUpEnabled:!1,brokerageEnabled:!1,brokerageEnabledCondition:void 0,brokerageBindMode:void 0,brokeragePosterUrls:[],brokerageFirstPercent:0,brokerageSecondPercent:0,brokerageWithdrawMinPrice:0,brokerageWithdrawFeePercent:0,brokerageFrozenDays:0,brokerageWithdrawTypes:[]}),M=j({deliveryExpressFreePrice:[{required:!0,message:"\u6EE1\u989D\u5305\u90AE\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],brokerageEnabledCondition:[{required:!0,message:"\u5206\u4F63\u6A21\u5F0F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],brokerageBindMode:[{required:!0,message:"\u5206\u9500\u5173\u7CFB\u7ED1\u5B9A\u6A21\u5F0F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],brokerageFirstPercent:[{required:!0,message:"\u4E00\u7EA7\u8FD4\u4F63\u6BD4\u4F8B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],brokerageSecondPercent:[{required:!0,message:"\u4E8C\u7EA7\u8FD4\u4F63\u6BD4\u4F8B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],brokerageWithdrawMinPrice:[{required:!0,message:"\u7528\u6237\u63D0\u73B0\u6700\u4F4E\u91D1\u989D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],brokerageWithdrawFeePercent:[{required:!0,message:"\u63D0\u73B0\u624B\u7EED\u8D39\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],brokerageFrozenDays:[{required:!0,message:"\u4F63\u91D1\u51BB\u7ED3\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],brokerageWithdrawTypes:[{required:!0,message:"\u63D0\u73B0\u65B9\u5F0F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}]}),S=async()=>{if(!c.value&&g&&await g.value.validate()){c.value=!0;try{const i=J(r(s.value));i.deliveryExpressFreePrice=100*i.deliveryExpressFreePrice,i.brokerageWithdrawMinPrice=100*i.brokerageWithdrawMinPrice,await ye(i),z.success("\u4FDD\u5B58\u6210\u529F")}finally{c.value=!1}}};return H(()=>{(async()=>{c.value=!0;try{const i=await ke();i!=null&&(s.value=i,s.value.deliveryExpressFreePrice=i.deliveryExpressFreePrice/100,s.value.brokerageWithdrawMinPrice=i.brokerageWithdrawMinPrice/100)}finally{c.value=!1}})()}),(i,t)=>{const E=_e,D=Y,d=Z,v=Q,F=X,y=$,k=ee,u=pe,b=le,R=ae,x=re,B=se,q=te,C=oe,T=de,A=ue,O=ne,I=be,N=ie;return n(),f(m,null,[e(E,{title:"\u3010\u4EA4\u6613\u3011\u4EA4\u6613\u8BA2\u5355",url:"https://doc.iocoder.cn/mall/trade-order/"}),e(E,{title:"\u3010\u4EA4\u6613\u3011\u8D2D\u7269\u8F66",url:"https://doc.iocoder.cn/mall/trade-cart/"}),e(I,null,{default:a(()=>[U((n(),p(O,{ref_key:"formRef",ref:g,model:r(s),rules:r(M),"label-width":"120px"},{default:a(()=>[U(e(d,{label:"hideId"},{default:a(()=>[e(D,{modelValue:r(s).id,"onUpdate:modelValue":t[0]||(t[0]=l=>r(s).id=l)},null,8,["modelValue"])]),_:1},512),[[L,!1]]),e(T,null,{default:a(()=>[e(y,{label:"\u552E\u540E"},{default:a(()=>[e(d,{label:"\u9000\u6B3E\u7406\u7531",prop:"afterSaleRefundReasons"},{default:a(()=>[e(F,{modelValue:r(s).afterSaleRefundReasons,"onUpdate:modelValue":t[1]||(t[1]=l=>r(s).afterSaleRefundReasons=l),"allow-create":"",filterable:"",multiple:"",placeholder:"\u8BF7\u76F4\u63A5\u8F93\u5165\u9000\u6B3E\u7406\u7531"},{default:a(()=>[(n(!0),f(m,null,_(r(s).afterSaleRefundReasons,l=>(n(),p(v,{key:l,label:l,value:l},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"\u9000\u8D27\u7406\u7531",prop:"afterSaleReturnReasons"},{default:a(()=>[e(F,{modelValue:r(s).afterSaleReturnReasons,"onUpdate:modelValue":t[2]||(t[2]=l=>r(s).afterSaleReturnReasons=l),"allow-create":"",filterable:"",multiple:"",placeholder:"\u8BF7\u76F4\u63A5\u8F93\u5165\u9000\u8D27\u7406\u7531"},{default:a(()=>[(n(!0),f(m,null,_(r(s).afterSaleReturnReasons,l=>(n(),p(v,{key:l,label:l,value:l},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(y,{label:"\u914D\u9001"},{default:a(()=>[e(d,{label:"\u542F\u7528\u5305\u90AE",prop:"deliveryExpressFreeEnabled"},{default:a(()=>[e(k,{modelValue:r(s).deliveryExpressFreeEnabled,"onUpdate:modelValue":t[3]||(t[3]=l=>r(s).deliveryExpressFreeEnabled=l),style:{"user-select":"none"}},null,8,["modelValue"]),e(u,{class:"w-full",size:"small",type:"info"},{default:a(()=>[o(" \u5546\u57CE\u662F\u5426\u542F\u7528\u5168\u573A\u5305\u90AE")]),_:1})]),_:1}),e(d,{label:"\u6EE1\u989D\u5305\u90AE",prop:"deliveryExpressFreePrice"},{default:a(()=>[e(b,{modelValue:r(s).deliveryExpressFreePrice,"onUpdate:modelValue":t[4]||(t[4]=l=>r(s).deliveryExpressFreePrice=l),min:0,precision:2,class:"!w-xs",placeholder:"\u8BF7\u8F93\u5165\u6EE1\u989D\u5305\u90AE"},null,8,["modelValue"]),e(u,{class:"w-full",size:"small",type:"info"},{default:a(()=>[o(" \u5546\u57CE\u5546\u54C1\u6EE1\u591A\u5C11\u91D1\u989D\u5373\u53EF\u5305\u90AE\uFF0C\u5355\u4F4D\uFF1A\u5143 ")]),_:1})]),_:1}),e(d,{label:"\u542F\u7528\u95E8\u5E97\u81EA\u63D0",prop:"deliveryPickUpEnabled"},{default:a(()=>[e(k,{modelValue:r(s).deliveryPickUpEnabled,"onUpdate:modelValue":t[5]||(t[5]=l=>r(s).deliveryPickUpEnabled=l),style:{"user-select":"none"}},null,8,["modelValue"])]),_:1})]),_:1}),e(y,{label:"\u5206\u9500"},{default:a(()=>[e(d,{label:"\u5206\u4F63\u542F\u7528",prop:"brokerageEnabled"},{default:a(()=>[e(k,{modelValue:r(s).brokerageEnabled,"onUpdate:modelValue":t[6]||(t[6]=l=>r(s).brokerageEnabled=l),style:{"user-select":"none"}},null,8,["modelValue"]),e(u,{class:"w-full",size:"small",type:"info"},{default:a(()=>[o(" \u5546\u57CE\u662F\u5426\u5F00\u542F\u5206\u9500\u6A21\u5F0F")]),_:1})]),_:1}),e(d,{label:"\u5206\u4F63\u6A21\u5F0F",prop:"brokerageEnabledCondition"},{default:a(()=>[e(x,{modelValue:r(s).brokerageEnabledCondition,"onUpdate:modelValue":t[7]||(t[7]=l=>r(s).brokerageEnabledCondition=l)},{default:a(()=>[(n(!0),f(m,null,_(r(w)(r(h).BROKERAGE_ENABLED_CONDITION),l=>(n(),p(R,{key:l.value,label:l.value},{default:a(()=>[o(P(l.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"]),e(u,{class:"w-full",size:"small",type:"info"},{default:a(()=>[o(" \u4EBA\u4EBA\u5206\u9500\uFF1A\u6BCF\u4E2A\u7528\u6237\u90FD\u53EF\u4EE5\u6210\u4E3A\u63A8\u5E7F\u5458 ")]),_:1}),e(u,{class:"w-full",size:"small",type:"info"},{default:a(()=>[o(" \u6307\u5B9A\u5206\u9500\uFF1A\u4EC5\u53EF\u5728\u540E\u53F0\u624B\u52A8\u8BBE\u7F6E\u63A8\u5E7F\u5458 ")]),_:1})]),_:1}),e(d,{label:"\u5206\u9500\u5173\u7CFB\u7ED1\u5B9A",prop:"brokerageBindMode"},{default:a(()=>[e(x,{modelValue:r(s).brokerageBindMode,"onUpdate:modelValue":t[8]||(t[8]=l=>r(s).brokerageBindMode=l)},{default:a(()=>[(n(!0),f(m,null,_(r(w)(r(h).BROKERAGE_BIND_MODE),l=>(n(),p(R,{key:l.value,label:l.value},{default:a(()=>[o(P(l.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"]),e(u,{class:"w-full",size:"small",type:"info"},{default:a(()=>[o(" \u9996\u6B21\u7ED1\u5B9A\uFF1A\u53EA\u8981\u7528\u6237\u6CA1\u6709\u63A8\u5E7F\u4EBA\uFF0C\u968F\u65F6\u90FD\u53EF\u4EE5\u7ED1\u5B9A\u63A8\u5E7F\u5173\u7CFB ")]),_:1}),e(u,{class:"w-full",size:"small",type:"info"},{default:a(()=>[o(" \u6CE8\u518C\u7ED1\u5B9A\uFF1A\u53EA\u6709\u65B0\u7528\u6237\u6CE8\u518C\u65F6\u6216\u9996\u6B21\u8FDB\u5165\u7CFB\u7EDF\u65F6\u624D\u53EF\u4EE5\u7ED1\u5B9A\u63A8\u5E7F\u5173\u7CFB ")]),_:1})]),_:1}),e(d,{label:"\u5206\u9500\u6D77\u62A5\u56FE"},{default:a(()=>[e(B,{modelValue:r(s).brokeragePosterUrls,"onUpdate:modelValue":t[9]||(t[9]=l=>r(s).brokeragePosterUrls=l),height:"125px",width:"75px"},null,8,["modelValue"]),e(u,{class:"w-full",size:"small",type:"info"},{default:a(()=>[o(" \u4E2A\u4EBA\u4E2D\u5FC3\u5206\u9500\u6D77\u62A5\u56FE\u7247\uFF0C\u5EFA\u8BAE\u5C3A\u5BF8 600x1000 ")]),_:1})]),_:1}),e(d,{label:"\u4E00\u7EA7\u8FD4\u4F63\u6BD4\u4F8B",prop:"brokerageFirstPercent"},{default:a(()=>[e(b,{modelValue:r(s).brokerageFirstPercent,"onUpdate:modelValue":t[10]||(t[10]=l=>r(s).brokerageFirstPercent=l),max:100,min:0,class:"!w-xs",placeholder:"\u8BF7\u8F93\u5165\u4E00\u7EA7\u8FD4\u4F63\u6BD4\u4F8B"},null,8,["modelValue"]),e(u,{class:"w-full",size:"small",type:"info"},{default:a(()=>[o(" \u8BA2\u5355\u4EA4\u6613\u6210\u529F\u540E\u7ED9\u63A8\u5E7F\u4EBA\u8FD4\u4F63\u7684\u767E\u5206\u6BD4 ")]),_:1})]),_:1}),e(d,{label:"\u4E8C\u7EA7\u8FD4\u4F63\u6BD4\u4F8B",prop:"brokerageSecondPercent"},{default:a(()=>[e(b,{modelValue:r(s).brokerageSecondPercent,"onUpdate:modelValue":t[11]||(t[11]=l=>r(s).brokerageSecondPercent=l),max:100,min:0,class:"!w-xs",placeholder:"\u8BF7\u8F93\u5165\u4E8C\u7EA7\u8FD4\u4F63\u6BD4\u4F8B"},null,8,["modelValue"]),e(u,{class:"w-full",size:"small",type:"info"},{default:a(()=>[o(" \u8BA2\u5355\u4EA4\u6613\u6210\u529F\u540E\u7ED9\u63A8\u5E7F\u4EBA\u7684\u63A8\u8350\u4EBA\u8FD4\u4F63\u7684\u767E\u5206\u6BD4 ")]),_:1})]),_:1}),e(d,{label:"\u4F63\u91D1\u51BB\u7ED3\u5929\u6570",prop:"brokerageFrozenDays"},{default:a(()=>[e(b,{modelValue:r(s).brokerageFrozenDays,"onUpdate:modelValue":t[12]||(t[12]=l=>r(s).brokerageFrozenDays=l),min:0,class:"!w-xs",placeholder:"\u8BF7\u8F93\u5165\u4F63\u91D1\u51BB\u7ED3\u5929\u6570"},null,8,["modelValue"]),e(u,{class:"w-full",size:"small",type:"info"},{default:a(()=>[o(" \u9632\u6B62\u7528\u6237\u9000\u6B3E\uFF0C\u4F63\u91D1\u88AB\u63D0\u73B0\u4E86\uFF0C\u6240\u4EE5\u9700\u8981\u8BBE\u7F6E\u4F63\u91D1\u51BB\u7ED3\u65F6\u95F4\uFF0C\u5355\u4F4D\uFF1A\u5929 ")]),_:1})]),_:1}),e(d,{label:"\u63D0\u73B0\u6700\u4F4E\u91D1\u989D",prop:"brokerageWithdrawMinPrice"},{default:a(()=>[e(b,{modelValue:r(s).brokerageWithdrawMinPrice,"onUpdate:modelValue":t[13]||(t[13]=l=>r(s).brokerageWithdrawMinPrice=l),min:0,precision:2,class:"!w-xs",placeholder:"\u8BF7\u8F93\u5165\u63D0\u73B0\u6700\u4F4E\u91D1\u989D"},null,8,["modelValue"]),e(u,{class:"w-full",size:"small",type:"info"},{default:a(()=>[o(" \u7528\u6237\u63D0\u73B0\u6700\u4F4E\u91D1\u989D\u9650\u5236\uFF0C\u5355\u4F4D\uFF1A\u5143 ")]),_:1})]),_:1}),e(d,{label:"\u63D0\u73B0\u624B\u7EED\u8D39",prop:"brokerageWithdrawFeePercent"},{default:a(()=>[e(b,{modelValue:r(s).brokerageWithdrawFeePercent,"onUpdate:modelValue":t[14]||(t[14]=l=>r(s).brokerageWithdrawFeePercent=l),max:100,min:0,class:"!w-xs",placeholder:"\u8BF7\u8F93\u5165\u63D0\u73B0\u624B\u7EED\u8D39"},null,8,["modelValue"]),e(u,{class:"w-full",size:"small",type:"info"},{default:a(()=>[o(" \u63D0\u73B0\u624B\u7EED\u8D39\u767E\u5206\u6BD4\uFF0C\u8303\u56F4 0-100\uFF0C0 \u4E3A\u65E0\u63D0\u73B0\u624B\u7EED\u8D39\u3002\u4F8B\uFF1A\u8BBE\u7F6E 10\uFF0C\u5373\u6536\u53D6 10% \u624B\u7EED\u8D39\uFF0C\u63D0\u73B0 10 \u5143\uFF0C\u5230\u8D26 9 \u5143\uFF0C1 \u5143\u624B\u7EED\u8D39 ")]),_:1})]),_:1}),e(d,{label:"\u63D0\u73B0\u65B9\u5F0F",prop:"brokerageWithdrawTypes"},{default:a(()=>[e(C,{modelValue:r(s).brokerageWithdrawTypes,"onUpdate:modelValue":t[15]||(t[15]=l=>r(s).brokerageWithdrawTypes=l)},{default:a(()=>[(n(!0),f(m,null,_(r(w)(r(h).BROKERAGE_WITHDRAW_TYPE),l=>(n(),p(q,{key:l.value,label:l.value},{default:a(()=>[o(P(l.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"]),e(u,{class:"w-full",size:"small",type:"info"},{default:a(()=>[o(" \u5546\u57CE\u5F00\u901A\u63D0\u73B0\u7684\u4ED8\u6B3E\u65B9\u5F0F")]),_:1})]),_:1})]),_:1})]),_:1}),e(d,null,{default:a(()=>[e(A,{loading:r(c),type:"primary",onClick:S},{default:a(()=>[o(" \u4FDD\u5B58")]),_:1},8,["loading"])]),_:1})]),_:1},8,["model","rules"])),[[N,r(c)]])]),_:1})],64)}}})});export{he as __tla,W as default};
