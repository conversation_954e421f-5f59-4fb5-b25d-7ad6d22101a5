import{d as Q,r as c,f as X,at as Y,o as n,l as f,w as t,i as e,j as R,a as l,H as $,c as w,k as h,F as U,y as S,dV as z,n as ee,I as ae,dW as le,e as te,Z as de,L as ue,E as re,J as oe,K as se,s as ce,M as ie,z as ne,A as me,cc as pe,O as _e,N as fe,R as ve,__tla as be}from"./index-BUSn51wb.js";import{_ as Ve,__tla as ye}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{_ as Pe,__tla as Ie}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{a as we,c as he,u as Ue,__tla as ge}from"./index-M52UJVMY.js";import{g as ke,__tla as Te}from"./index-HLeyY-fc.js";import{a as xe,__tla as qe}from"./index-CD52sTBY.js";import{g as De,__tla as Fe}from"./index-BYXzDB8j.js";import{_ as Re,__tla as Se}from"./BusinessProductForm.vue_vue_type_script_setup_true_lang-BrV7GF0g.js";let A,ze=Promise.all([(()=>{try{return be}catch{}})(),(()=>{try{return ye}catch{}})(),(()=>{try{return Ie}catch{}})(),(()=>{try{return ge}catch{}})(),(()=>{try{return Te}catch{}})(),(()=>{try{return qe}catch{}})(),(()=>{try{return Fe}catch{}})(),(()=>{try{return Se}catch{}})()]).then(async()=>{A=Q({__name:"BusinessForm",emits:["success"],setup(Ae,{expose:B,emit:C}){const{t:b}=ee(),g=ae(),m=c(!1),k=c(""),p=c(!1),_=c(""),d=c({id:void 0,name:void 0,customerId:void 0,ownerUserId:void 0,statusTypeId:void 0,dealTime:void 0,discountPercent:0,totalProductPrice:void 0,totalPrice:void 0,remark:void 0,products:[],contactId:void 0,customerDefault:!1}),E=X({name:[{required:!0,message:"\u5546\u673A\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],customerId:[{required:!0,message:"\u5BA2\u6237\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],ownerUserId:[{required:!0,message:"\u8D1F\u8D23\u4EBA\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],statusTypeId:[{required:!0,message:"\u5546\u673A\u72B6\u6001\u7EC4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),v=c(),T=c([]),x=c([]),q=c([]),V=c("product"),D=c();Y(()=>d.value,r=>{if(!r)return;const u=r.products.reduce((o,s)=>o+s.totalPrice,0),i=u-(r.discountPercent!=null?le(u,r.discountPercent/100):0);d.value.totalProductPrice=u,d.value.totalPrice=i},{deep:!0}),B({open:async(r,u,i,o)=>{if(m.value=!0,k.value=b("action."+r),_.value=r,W(),u){p.value=!0;try{d.value=await we(u)}finally{p.value=!1}}else i&&(d.value.customerId=i,d.value.customerDefault=!0),o&&(d.value.contactId=o);q.value=await xe(),x.value=await ke(),T.value=await De(),_.value==="create"&&(d.value.ownerUserId=te().getUser.id)}});const J=C,L=async()=>{if(v&&await v.value.validate()){await D.value.validate(),p.value=!0;try{const r=d.value;_.value==="create"?(await he(r),g.success(b("common.createSuccess"))):(await Ue(r),g.success(b("common.updateSuccess"))),m.value=!1,J("success")}finally{p.value=!1}}},W=()=>{var r;d.value={id:void 0,name:void 0,customerId:void 0,ownerUserId:void 0,statusTypeId:void 0,dealTime:void 0,discountPercent:0,totalProductPrice:void 0,totalPrice:void 0,remark:void 0,products:[],contactId:void 0,customerDefault:!1},(r=v.value)==null||r.resetFields()};return(r,u)=>{const i=de,o=ue,s=re,y=oe,P=se,I=ce,j=ie,H=ne,K=me,M=Pe,N=pe,O=_e,F=fe,Z=Ve,G=ve;return n(),f(Z,{title:l(k),modelValue:l(m),"onUpdate:modelValue":u[11]||(u[11]=a=>S(m)?m.value=a:null),width:"1280"},{footer:t(()=>[e(F,{onClick:L,type:"primary",disabled:l(p)},{default:t(()=>[R("\u786E \u5B9A")]),_:1},8,["disabled"]),e(F,{onClick:u[10]||(u[10]=a=>m.value=!1)},{default:t(()=>[R("\u53D6 \u6D88")]),_:1})]),default:t(()=>[$((n(),f(O,{ref_key:"formRef",ref:v,model:l(d),rules:l(E),"label-width":"120px"},{default:t(()=>[e(I,null,{default:t(()=>[e(s,{span:8},{default:t(()=>[e(o,{label:"\u5546\u673A\u540D\u79F0",prop:"name"},{default:t(()=>[e(i,{modelValue:l(d).name,"onUpdate:modelValue":u[0]||(u[0]=a=>l(d).name=a),placeholder:"\u8BF7\u8F93\u5165\u5546\u673A\u540D\u79F0"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(o,{label:"\u8D1F\u8D23\u4EBA",prop:"ownerUserId"},{default:t(()=>[e(P,{modelValue:l(d).ownerUserId,"onUpdate:modelValue":u[1]||(u[1]=a=>l(d).ownerUserId=a),disabled:l(_)!=="create",class:"w-1/1"},{default:t(()=>[(n(!0),w(U,null,h(l(T),a=>(n(),f(y,{key:a.id,label:a.nickname,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(o,{label:"\u5BA2\u6237\u540D\u79F0",prop:"customerId"},{default:t(()=>[e(P,{disabled:l(d).customerDefault,modelValue:l(d).customerId,"onUpdate:modelValue":u[2]||(u[2]=a=>l(d).customerId=a),placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237",class:"w-1/1"},{default:t(()=>[(n(!0),w(U,null,h(l(q),a=>(n(),f(y,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["disabled","modelValue"])]),_:1})]),_:1})]),_:1}),e(I,null,{default:t(()=>[e(s,{span:8},{default:t(()=>[e(o,{label:"\u5546\u673A\u72B6\u6001\u7EC4",prop:"statusTypeId"},{default:t(()=>[e(P,{modelValue:l(d).statusTypeId,"onUpdate:modelValue":u[3]||(u[3]=a=>l(d).statusTypeId=a),placeholder:"\u8BF7\u9009\u62E9\u5546\u673A\u72B6\u6001\u7EC4",clearable:"",class:"w-1/1",disabled:l(_)!=="create"},{default:t(()=>[(n(!0),w(U,null,h(l(x),a=>(n(),f(y,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(o,{label:"\u9884\u8BA1\u6210\u4EA4\u65E5\u671F",prop:"dealTime"},{default:t(()=>[e(j,{modelValue:l(d).dealTime,"onUpdate:modelValue":u[4]||(u[4]=a=>l(d).dealTime=a),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u9884\u8BA1\u6210\u4EA4\u65E5\u671F",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(o,{label:"\u5907\u6CE8",prop:"remark"},{default:t(()=>[e(i,{type:"textarea",modelValue:l(d).remark,"onUpdate:modelValue":u[5]||(u[5]=a=>l(d).remark=a),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(M,null,{default:t(()=>[e(K,{modelValue:l(V),"onUpdate:modelValue":u[6]||(u[6]=a=>S(V)?V.value=a:null),class:"-mt-15px -mb-10px"},{default:t(()=>[e(H,{label:"\u4EA7\u54C1\u6E05\u5355",name:"product"},{default:t(()=>[e(Re,{ref_key:"productFormRef",ref:D,products:l(d).products,disabled:r.disabled},null,8,["products","disabled"])]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(I,null,{default:t(()=>[e(s,{span:8},{default:t(()=>[e(o,{label:"\u4EA7\u54C1\u603B\u91D1\u989D",prop:"totalProductPrice"},{default:t(()=>[e(i,{disabled:"",modelValue:l(d).totalProductPrice,"onUpdate:modelValue":u[7]||(u[7]=a=>l(d).totalProductPrice=a),formatter:l(z)},null,8,["modelValue","formatter"])]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(o,{label:"\u6574\u5355\u6298\u6263\uFF08%\uFF09",prop:"discountPercent"},{default:t(()=>[e(N,{modelValue:l(d).discountPercent,"onUpdate:modelValue":u[8]||(u[8]=a=>l(d).discountPercent=a),placeholder:"\u8BF7\u8F93\u5165\u6574\u5355\u6298\u6263","controls-position":"right",min:0,precision:2,class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(o,{label:"\u6298\u6263\u540E\u91D1\u989D",prop:"price"},{default:t(()=>[e(i,{disabled:"",modelValue:l(d).totalPrice,"onUpdate:modelValue":u[9]||(u[9]=a=>l(d).totalPrice=a),placeholder:"\u8BF7\u8F93\u5165\u5546\u673A\u91D1\u989D",formatter:l(z)},null,8,["modelValue","formatter"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])),[[G,l(p)]])]),_:1},8,["title","modelValue"])}}})});export{A as _,ze as __tla};
