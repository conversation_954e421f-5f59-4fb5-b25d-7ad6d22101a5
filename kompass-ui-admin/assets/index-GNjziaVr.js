import{by as e,__tla as s}from"./index-BUSn51wb.js";let t,c=Promise.all([(()=>{try{return s}catch{}})()]).then(async()=>{t={getCoursePackagePage:async a=>await e.get({url:"/als/course-package/page",params:a}),getCoursePackage:async a=>await e.get({url:"/als/course-package/get?id="+a}),createCoursePackage:async a=>await e.post({url:"/als/course-package/create",data:a}),updateCoursePackage:async a=>await e.put({url:"/als/course-package/update",data:a}),deleteCoursePackage:async a=>await e.delete({url:"/als/course-package/delete?id="+a}),exportCoursePackage:async a=>await e.download({url:"/als/course-package/export-excel",params:a}),getAllCoursePackage:async()=>await e.get({url:"/als/course-package/all"})}});export{t as C,c as __tla};
