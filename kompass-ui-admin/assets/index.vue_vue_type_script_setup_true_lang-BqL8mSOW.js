import{d as b,$ as y,b as i,r as z,bx as f,H as h,a8 as x,o as C,l as N,a as P,__tla as S}from"./index-BUSn51wb.js";import{E as U,__tla as j}from"./index-Cch5e1V0.js";let g,k=Promise.all([(()=>{try{return S}catch{}})(),(()=>{try{return j}catch{}})()]).then(async()=>{g=b({name:"Pagination",__name:"index",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pagerCount:{type:Number,default:document.body.clientWidth<992?5:7}},emits:["update:page","update:limit","pagination"],setup(t,{emit:m}){const c=y(),s=i(()=>c.currentSize),o=z(s.value==="small");f(()=>{o.value=s.value==="small"});const u=t,l=m,e=i({get:()=>u.page,set(a){l("update:page",a)}}),n=i({get:()=>u.limit,set(a){l("update:limit",a)}}),d=a=>{e.value*a>u.total&&(e.value=1),l("pagination",{page:e.value,limit:a})},_=a=>{l("pagination",{page:a,limit:n.value})};return(a,r)=>{const v=U;return h((C(),N(v,{"current-page":e.value,"onUpdate:currentPage":r[0]||(r[0]=p=>e.value=p),"page-size":n.value,"onUpdate:pageSize":r[1]||(r[1]=p=>n.value=p),background:!0,"page-sizes":[10,20,30,50,100],"pager-count":t.pagerCount,total:t.total,small:P(o),class:"float-right mb-15px mt-15px",layout:"total, sizes, prev, pager, next, jumper",onSizeChange:d,onCurrentChange:_},null,8,["current-page","page-size","pager-count","total","small"])),[[x,t.total>0]])}}})});export{g as _,k as __tla};
