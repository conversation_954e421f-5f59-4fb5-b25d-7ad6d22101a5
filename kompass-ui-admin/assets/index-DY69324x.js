import{d as de,I as ne,n as se,r as c,f as ie,C as ce,T as pe,o as u,c as b,i as a,w as r,a as l,U as R,F as w,k,l as d,V as _e,G as J,j as p,H as f,eo as me,dV as O,t as fe,dX as ye,Z as be,L as he,J as we,K as ve,M as ge,_ as ke,N as Ve,O as xe,P as Se,ax as Ce,Q as Ie,R as Ue,__tla as Pe}from"./index-BUSn51wb.js";import{_ as Ne,__tla as Te}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as Re,__tla as Ae}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as De,__tla as Le}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as Ye,__tla as ze}from"./index-COobLwz-.js";import{b as Ke,__tla as Me}from"./formatTime-DWdBpgsM.js";import{d as Ee}from"./download-e0EdwhTv.js";import{S as I,__tla as Fe}from"./index-DIBvuZmk.js";import{_ as He,__tla as Qe}from"./SaleReturnForm.vue_vue_type_script_setup_true_lang-C079ZveU.js";import{P as We,__tla as qe}from"./index-B00QUU3o.js";import{g as Je,__tla as Oe}from"./index-BYXzDB8j.js";import{C as Xe,__tla as Ze}from"./index-DYwp4_G0.js";import{W as je,__tla as Ge}from"./index-B5GxX3eg.js";import{A as $e,__tla as Be}from"./index-LbO7ASKC.js";import{__tla as ea}from"./index-Cch5e1V0.js";import"./color-BN7ZL7BD.js";import{__tla as aa}from"./el-card-CJbXGyyg.js";import{__tla as la}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as ta}from"./SaleReturnItemForm.vue_vue_type_script_setup_true_lang-v5moq-AX.js";import{__tla as ra}from"./index-BCEOZol9.js";import{__tla as oa}from"./SaleOrderReturnEnableList.vue_vue_type_script_setup_true_lang-CPUsuOZa.js";import{__tla as ua}from"./index-DgsXVLii.js";let X,da=Promise.all([(()=>{try{return Pe}catch{}})(),(()=>{try{return Te}catch{}})(),(()=>{try{return Ae}catch{}})(),(()=>{try{return Le}catch{}})(),(()=>{try{return ze}catch{}})(),(()=>{try{return Me}catch{}})(),(()=>{try{return Fe}catch{}})(),(()=>{try{return Qe}catch{}})(),(()=>{try{return qe}catch{}})(),(()=>{try{return Oe}catch{}})(),(()=>{try{return Ze}catch{}})(),(()=>{try{return Ge}catch{}})(),(()=>{try{return Be}catch{}})(),(()=>{try{return ea}catch{}})(),(()=>{try{return aa}catch{}})(),(()=>{try{return la}catch{}})(),(()=>{try{return ta}catch{}})(),(()=>{try{return ra}catch{}})(),(()=>{try{return oa}catch{}})(),(()=>{try{return ua}catch{}})()]).then(async()=>{let A;A={key:0},X=de({name:"ErpSaleReturn",__name:"index",setup(na){const V=ne(),{t:Z}=se(),U=c(!0),D=c([]),L=c(0),o=ie({pageNo:1,pageSize:10,no:void 0,customerId:void 0,productId:void 0,warehouseId:void 0,returnTime:[],orderNo:void 0,accountId:void 0,status:void 0,remark:void 0,creator:void 0,refundStatus:void 0}),Y=c(),P=c(!1),z=c([]),K=c([]),M=c([]),E=c([]),F=c([]),v=async()=>{U.value=!0;try{const n=await I.getSaleReturnPage(o);D.value=n.list,L.value=n.total}finally{U.value=!1}},x=()=>{o.pageNo=1,v()},j=()=>{Y.value.resetFields(),x()},H=c(),N=(n,t)=>{H.value.open(n,t)},Q=async n=>{try{await V.delConfirm(),await I.deleteSaleReturn(n),V.success(Z("common.delSuccess")),await v(),S.value=S.value.filter(t=>!n.includes(t.id))}catch{}},W=async(n,t)=>{try{await V.confirm(`\u786E\u5B9A${t===20?"\u5BA1\u6279":"\u53CD\u5BA1\u6279"}\u8BE5\u9000\u8D27\u5417\uFF1F`),await I.updateSaleReturnStatus(n,t),V.success((t===20?"\u5BA1\u6279":"\u53CD\u5BA1\u6279")+"\u6210\u529F"),await v()}catch{}},G=async()=>{try{await V.exportConfirm(),P.value=!0;const n=await I.exportSaleReturn(o);Ee.excel(n,"\u9500\u552E\u9000\u8D27.xls")}catch{}finally{P.value=!1}},S=c([]),$=n=>{S.value=n};return ce(async()=>{await v(),z.value=await We.getProductSimpleList(),K.value=await Xe.getCustomerSimpleList(),M.value=await Je(),E.value=await je.getWarehouseSimpleList(),F.value=await $e.getAccountSimpleList()}),(n,t)=>{const B=Ye,T=be,s=he,m=we,h=ve,ee=ge,C=ke,_=Ve,ae=xe,q=De,i=Se,le=Ce,te=Re,re=Ie,oe=Ne,y=pe("hasPermi"),ue=Ue;return u(),b(w,null,[a(B,{title:"\u3010\u9500\u552E\u3011\u9500\u552E\u8BA2\u5355\u3001\u51FA\u5E93\u3001\u9000\u8D27",url:"https://doc.iocoder.cn/erp/sale/"}),a(q,null,{default:r(()=>[a(ae,{class:"-mb-15px",model:l(o),ref_key:"queryFormRef",ref:Y,inline:!0,"label-width":"68px"},{default:r(()=>[a(s,{label:"\u9000\u8D27\u5355\u53F7",prop:"no"},{default:r(()=>[a(T,{modelValue:l(o).no,"onUpdate:modelValue":t[0]||(t[0]=e=>l(o).no=e),placeholder:"\u8BF7\u8F93\u5165\u9000\u8D27\u5355\u53F7",clearable:"",onKeyup:R(x,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(s,{label:"\u4EA7\u54C1",prop:"productId"},{default:r(()=>[a(h,{modelValue:l(o).productId,"onUpdate:modelValue":t[1]||(t[1]=e=>l(o).productId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1",class:"!w-240px"},{default:r(()=>[(u(!0),b(w,null,k(l(z),e=>(u(),d(m,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(s,{label:"\u9000\u8D27\u65F6\u95F4",prop:"outTime"},{default:r(()=>[a(ee,{modelValue:l(o).outTime,"onUpdate:modelValue":t[2]||(t[2]=e=>l(o).outTime=e),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-220px"},null,8,["modelValue","default-time"])]),_:1}),a(s,{label:"\u5BA2\u6237",prop:"customerId"},{default:r(()=>[a(h,{modelValue:l(o).customerId,"onUpdate:modelValue":t[3]||(t[3]=e=>l(o).customerId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4F9B\u5BA2\u6237",class:"!w-240px"},{default:r(()=>[(u(!0),b(w,null,k(l(K),e=>(u(),d(m,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(s,{label:"\u4ED3\u5E93",prop:"warehouseId"},{default:r(()=>[a(h,{modelValue:l(o).warehouseId,"onUpdate:modelValue":t[4]||(t[4]=e=>l(o).warehouseId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93",class:"!w-240px"},{default:r(()=>[(u(!0),b(w,null,k(l(E),e=>(u(),d(m,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(s,{label:"\u521B\u5EFA\u4EBA",prop:"creator"},{default:r(()=>[a(h,{modelValue:l(o).creator,"onUpdate:modelValue":t[5]||(t[5]=e=>l(o).creator=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u521B\u5EFA\u4EBA",class:"!w-240px"},{default:r(()=>[(u(!0),b(w,null,k(l(M),e=>(u(),d(m,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(s,{label:"\u5173\u8054\u8BA2\u5355",prop:"orderNo"},{default:r(()=>[a(T,{modelValue:l(o).orderNo,"onUpdate:modelValue":t[6]||(t[6]=e=>l(o).orderNo=e),placeholder:"\u8BF7\u8F93\u5165\u5173\u8054\u8BA2\u5355",clearable:"",onKeyup:R(x,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(s,{label:"\u7ED3\u7B97\u8D26\u6237",prop:"accountId"},{default:r(()=>[a(h,{modelValue:l(o).accountId,"onUpdate:modelValue":t[7]||(t[7]=e=>l(o).accountId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u7ED3\u7B97\u8D26\u6237",class:"!w-240px"},{default:r(()=>[(u(!0),b(w,null,k(l(F),e=>(u(),d(m,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(s,{label:"\u9000\u6B3E\u72B6\u6001",prop:"refundStatus"},{default:r(()=>[a(h,{modelValue:l(o).refundStatus,"onUpdate:modelValue":t[8]||(t[8]=e=>l(o).refundStatus=e),placeholder:"\u8BF7\u9009\u62E9\u9000\u6B3E\u72B6\u6001",clearable:"",class:"!w-240px"},{default:r(()=>[a(m,{label:"\u672A\u9000\u6B3E",value:"0"}),a(m,{label:"\u90E8\u5206\u9000\u6B3E",value:"1"}),a(m,{label:"\u5168\u90E8\u9000\u6B3E",value:"2"})]),_:1},8,["modelValue"])]),_:1}),a(s,{label:"\u5BA1\u6838\u72B6\u6001",prop:"status"},{default:r(()=>[a(h,{modelValue:l(o).status,"onUpdate:modelValue":t[9]||(t[9]=e=>l(o).status=e),placeholder:"\u8BF7\u9009\u62E9\u5BA1\u6838\u72B6\u6001",clearable:"",class:"!w-240px"},{default:r(()=>[(u(!0),b(w,null,k(l(_e)(l(J).ERP_AUDIT_STATUS),e=>(u(),d(m,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(s,{label:"\u5907\u6CE8",prop:"remark"},{default:r(()=>[a(T,{modelValue:l(o).remark,"onUpdate:modelValue":t[10]||(t[10]=e=>l(o).remark=e),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",clearable:"",onKeyup:R(x,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(s,null,{default:r(()=>[a(_,{onClick:x},{default:r(()=>[a(C,{icon:"ep:search",class:"mr-5px"}),p(" \u641C\u7D22")]),_:1}),a(_,{onClick:j},{default:r(()=>[a(C,{icon:"ep:refresh",class:"mr-5px"}),p(" \u91CD\u7F6E")]),_:1}),f((u(),d(_,{type:"primary",plain:"",onClick:t[11]||(t[11]=e=>N("create"))},{default:r(()=>[a(C,{icon:"ep:plus",class:"mr-5px"}),p(" \u65B0\u589E ")]),_:1})),[[y,["erp:sale-return:create"]]]),f((u(),d(_,{type:"success",plain:"",onClick:G,loading:l(P)},{default:r(()=>[a(C,{icon:"ep:download",class:"mr-5px"}),p(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[y,["erp:sale-return:export"]]]),f((u(),d(_,{type:"danger",plain:"",onClick:t[12]||(t[12]=e=>Q(l(S).map(g=>g.id))),disabled:l(S).length===0},{default:r(()=>[a(C,{icon:"ep:delete",class:"mr-5px"}),p(" \u5220\u9664 ")]),_:1},8,["disabled"])),[[y,["erp:sale-return:delete"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(q,null,{default:r(()=>[f((u(),d(re,{data:l(D),stripe:!0,"show-overflow-tooltip":!0,onSelectionChange:$},{default:r(()=>[a(i,{width:"30",label:"\u9009\u62E9",type:"selection"}),a(i,{"min-width":"180",label:"\u9000\u8D27\u5355\u53F7",align:"center",prop:"no"}),a(i,{label:"\u4EA7\u54C1\u4FE1\u606F",align:"center",prop:"productNames","min-width":"200"}),a(i,{label:"\u5BA2\u6237",align:"center",prop:"customerName"}),a(i,{label:"\u9000\u8D27\u65F6\u95F4",align:"center",prop:"returnTime",formatter:l(Ke),width:"120px"},null,8,["formatter"]),a(i,{label:"\u521B\u5EFA\u4EBA",align:"center",prop:"creatorName"}),a(i,{label:"\u603B\u6570\u91CF",align:"center",prop:"totalCount",formatter:l(me)},null,8,["formatter"]),a(i,{label:"\u5E94\u9000\u91D1\u989D",align:"center",prop:"totalPrice",formatter:l(O)},null,8,["formatter"]),a(i,{label:"\u5DF2\u9000\u91D1\u989D",align:"center",prop:"refundPrice",formatter:l(O)},null,8,["formatter"]),a(i,{label:"\u672A\u9000\u91D1\u989D",align:"center"},{default:r(e=>[e.row.refundPrice===e.row.totalPrice?(u(),b("span",A,"0")):(u(),d(le,{key:1,type:"danger"},{default:r(()=>[p(fe(l(ye)(e.row.totalPrice-e.row.refundPrice)),1)]),_:2},1024))]),_:1}),a(i,{label:"\u5BA1\u6838\u72B6\u6001",align:"center",fixed:"right",width:"90",prop:"status"},{default:r(e=>[a(te,{type:l(J).ERP_AUDIT_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),a(i,{label:"\u64CD\u4F5C",align:"center",fixed:"right",width:"220"},{default:r(e=>[f((u(),d(_,{link:"",onClick:g=>N("detail",e.row.id)},{default:r(()=>[p(" \u8BE6\u60C5 ")]),_:2},1032,["onClick"])),[[y,["erp:sale-return:query"]]]),f((u(),d(_,{link:"",type:"primary",onClick:g=>N("update",e.row.id),disabled:e.row.status===20},{default:r(()=>[p(" \u7F16\u8F91 ")]),_:2},1032,["onClick","disabled"])),[[y,["erp:sale-return:update"]]]),e.row.status===10?f((u(),d(_,{key:0,link:"",type:"primary",onClick:g=>W(e.row.id,20)},{default:r(()=>[p(" \u5BA1\u6279 ")]),_:2},1032,["onClick"])),[[y,["erp:sale-return:update-status"]]]):f((u(),d(_,{key:1,link:"",type:"danger",onClick:g=>W(e.row.id,10)},{default:r(()=>[p(" \u53CD\u5BA1\u6279 ")]),_:2},1032,["onClick"])),[[y,["erp:sale-return:update-status"]]]),f((u(),d(_,{link:"",type:"danger",onClick:g=>Q([e.row.id])},{default:r(()=>[p(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[y,["erp:sale-return:delete"]]])]),_:1})]),_:1},8,["data"])),[[ue,l(U)]]),a(oe,{total:l(L),page:l(o).pageNo,"onUpdate:page":t[13]||(t[13]=e=>l(o).pageNo=e),limit:l(o).pageSize,"onUpdate:limit":t[14]||(t[14]=e=>l(o).pageSize=e),onPagination:v},null,8,["total","page","limit"])]),_:1}),a(He,{ref_key:"formRef",ref:H,onSuccess:v},null,512)],64)}}})});export{da as __tla,X as default};
