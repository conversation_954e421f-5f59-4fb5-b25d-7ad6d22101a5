import{d as te,I as re,n as oe,r as u,f as ie,C as ne,T as se,o as i,c as k,i as a,w as r,a as l,U as M,F as b,k as S,l as s,V as ue,G as q,j as c,H as m,eo as pe,dV as de,Z as ce,L as _e,J as me,K as fe,M as ye,_ as he,N as ke,O as be,P as we,Q as ve,R as ge,__tla as Ve}from"./index-BUSn51wb.js";import{_ as xe,__tla as Se}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as Ce,__tla as Ie}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as Ue,__tla as Te}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as Pe,__tla as Ne}from"./index-COobLwz-.js";import{b as De,__tla as Le}from"./formatTime-DWdBpgsM.js";import{d as Re}from"./download-e0EdwhTv.js";import{_ as Ye,S as I,__tla as ze}from"./StockInForm.vue_vue_type_script_setup_true_lang-DWBBQo5g.js";import{P as Ae,__tla as Ee}from"./index-B00QUU3o.js";import{W as He,__tla as Ke}from"./index-B5GxX3eg.js";import{S as We,__tla as Fe}from"./index-CncHngEK.js";import{g as Me,__tla as qe}from"./index-BYXzDB8j.js";import{__tla as Ge}from"./index-Cch5e1V0.js";import"./color-BN7ZL7BD.js";import{__tla as je}from"./el-card-CJbXGyyg.js";import{__tla as Je}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as Oe}from"./StockInItemForm.vue_vue_type_script_setup_true_lang-SRPJtC1S.js";import{__tla as Qe}from"./index-BCEOZol9.js";let G,Ze=Promise.all([(()=>{try{return Ve}catch{}})(),(()=>{try{return Se}catch{}})(),(()=>{try{return Ie}catch{}})(),(()=>{try{return Te}catch{}})(),(()=>{try{return Ne}catch{}})(),(()=>{try{return Le}catch{}})(),(()=>{try{return ze}catch{}})(),(()=>{try{return Ee}catch{}})(),(()=>{try{return Ke}catch{}})(),(()=>{try{return Fe}catch{}})(),(()=>{try{return qe}catch{}})(),(()=>{try{return Ge}catch{}})(),(()=>{try{return je}catch{}})(),(()=>{try{return Je}catch{}})(),(()=>{try{return Oe}catch{}})(),(()=>{try{return Qe}catch{}})()]).then(async()=>{G=te({name:"ErpStockIn",__name:"index",setup($e){const w=re(),{t:j}=oe(),U=u(!0),N=u([]),D=u(0),o=ie({pageNo:1,pageSize:10,no:void 0,productId:void 0,supplierId:void 0,inTime:[],status:void 0,remark:void 0,creator:void 0}),L=u(),T=u(!1),R=u([]),Y=u([]),z=u([]),A=u([]),y=async()=>{U.value=!0;try{const n=await I.getStockInPage(o);N.value=n.list,D.value=n.total}finally{U.value=!1}},C=()=>{o.pageNo=1,y()},J=()=>{L.value.resetFields(),C()},E=u(),P=(n,t)=>{E.value.open(n,t)},H=async n=>{try{await w.delConfirm(),await I.deleteStockIn(n),w.success(j("common.delSuccess")),await y(),v.value=v.value.filter(t=>!n.includes(t.id))}catch{}},K=async(n,t)=>{try{await w.confirm(`\u786E\u5B9A${t===20?"\u5BA1\u6279":"\u53CD\u5BA1\u6279"}\u8BE5\u5165\u5E93\u5355\u5417\uFF1F`),await I.updateStockInStatus(n,t),w.success((t===20?"\u5BA1\u6279":"\u53CD\u5BA1\u6279")+"\u6210\u529F"),await y()}catch{}},O=async()=>{try{await w.exportConfirm(),T.value=!0;const n=await I.exportStockIn(o);Re.excel(n,"\u5176\u5B83\u5165\u5E93\u5355.xls")}catch{}finally{T.value=!1}},v=u([]),Q=n=>{v.value=n};return ne(async()=>{await y(),R.value=await Ae.getProductSimpleList(),Y.value=await He.getWarehouseSimpleList(),z.value=await We.getSupplierSimpleList(),A.value=await Me()}),(n,t)=>{const Z=Pe,W=ce,_=_e,g=me,V=fe,$=ye,x=he,p=ke,B=be,F=Ue,d=we,X=Ce,ee=ve,ae=xe,f=se("hasPermi"),le=ge;return i(),k(b,null,[a(Z,{title:"\u3010\u5E93\u5B58\u3011\u5176\u5B83\u5165\u5E93\u3001\u5176\u5B83\u51FA\u5E93",url:"https://doc.iocoder.cn/erp/stock-in-out/"}),a(F,null,{default:r(()=>[a(B,{class:"-mb-15px",model:l(o),ref_key:"queryFormRef",ref:L,inline:!0,"label-width":"68px"},{default:r(()=>[a(_,{label:"\u5165\u5E93\u5355\u53F7",prop:"no"},{default:r(()=>[a(W,{modelValue:l(o).no,"onUpdate:modelValue":t[0]||(t[0]=e=>l(o).no=e),placeholder:"\u8BF7\u8F93\u5165\u5165\u5E93\u5355\u53F7",clearable:"",onKeyup:M(C,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(_,{label:"\u4EA7\u54C1",prop:"productId"},{default:r(()=>[a(V,{modelValue:l(o).productId,"onUpdate:modelValue":t[1]||(t[1]=e=>l(o).productId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1",class:"!w-240px"},{default:r(()=>[(i(!0),k(b,null,S(l(R),e=>(i(),s(g,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(_,{label:"\u5165\u5E93\u65F6\u95F4",prop:"inTime"},{default:r(()=>[a($,{modelValue:l(o).inTime,"onUpdate:modelValue":t[2]||(t[2]=e=>l(o).inTime=e),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-220px"},null,8,["modelValue","default-time"])]),_:1}),a(_,{label:"\u4F9B\u5E94\u5546",prop:"supplierId"},{default:r(()=>[a(V,{modelValue:l(o).supplierId,"onUpdate:modelValue":t[3]||(t[3]=e=>l(o).supplierId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4F9B\u5E94\u5546",class:"!w-240px"},{default:r(()=>[(i(!0),k(b,null,S(l(z),e=>(i(),s(g,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(_,{label:"\u4ED3\u5E93",prop:"warehouseId"},{default:r(()=>[a(V,{modelValue:l(o).warehouseId,"onUpdate:modelValue":t[4]||(t[4]=e=>l(o).warehouseId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93",class:"!w-240px"},{default:r(()=>[(i(!0),k(b,null,S(l(Y),e=>(i(),s(g,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(_,{label:"\u521B\u5EFA\u4EBA",prop:"creator"},{default:r(()=>[a(V,{modelValue:l(o).creator,"onUpdate:modelValue":t[5]||(t[5]=e=>l(o).creator=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u521B\u5EFA\u4EBA",class:"!w-240px"},{default:r(()=>[(i(!0),k(b,null,S(l(A),e=>(i(),s(g,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(_,{label:"\u72B6\u6001",prop:"status"},{default:r(()=>[a(V,{modelValue:l(o).status,"onUpdate:modelValue":t[6]||(t[6]=e=>l(o).status=e),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",clearable:"",class:"!w-240px"},{default:r(()=>[(i(!0),k(b,null,S(l(ue)(l(q).ERP_AUDIT_STATUS),e=>(i(),s(g,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(_,{label:"\u5907\u6CE8",prop:"remark"},{default:r(()=>[a(W,{modelValue:l(o).remark,"onUpdate:modelValue":t[7]||(t[7]=e=>l(o).remark=e),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",clearable:"",onKeyup:M(C,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(_,null,{default:r(()=>[a(p,{onClick:C},{default:r(()=>[a(x,{icon:"ep:search",class:"mr-5px"}),c(" \u641C\u7D22")]),_:1}),a(p,{onClick:J},{default:r(()=>[a(x,{icon:"ep:refresh",class:"mr-5px"}),c(" \u91CD\u7F6E")]),_:1}),m((i(),s(p,{type:"primary",plain:"",onClick:t[8]||(t[8]=e=>P("create"))},{default:r(()=>[a(x,{icon:"ep:plus",class:"mr-5px"}),c(" \u65B0\u589E ")]),_:1})),[[f,["erp:stock-in:create"]]]),m((i(),s(p,{type:"success",plain:"",onClick:O,loading:l(T)},{default:r(()=>[a(x,{icon:"ep:download",class:"mr-5px"}),c(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[f,["erp:stock-in:export"]]]),m((i(),s(p,{type:"danger",plain:"",onClick:t[9]||(t[9]=e=>H(l(v).map(h=>h.id))),disabled:l(v).length===0},{default:r(()=>[a(x,{icon:"ep:delete",class:"mr-5px"}),c(" \u5220\u9664 ")]),_:1},8,["disabled"])),[[f,["erp:stock-in:delete"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(F,null,{default:r(()=>[m((i(),s(ee,{data:l(N),stripe:!0,"show-overflow-tooltip":!0,onSelectionChange:Q},{default:r(()=>[a(d,{width:"30",label:"\u9009\u62E9",type:"selection"}),a(d,{"min-width":"180",label:"\u5165\u5E93\u5355\u53F7",align:"center",prop:"no"}),a(d,{label:"\u4EA7\u54C1\u4FE1\u606F",align:"center",prop:"productNames","min-width":"200"}),a(d,{label:"\u4F9B\u5E94\u5546",align:"center",prop:"supplierName"}),a(d,{label:"\u5165\u5E93\u65F6\u95F4",align:"center",prop:"inTime",formatter:l(De),width:"120px"},null,8,["formatter"]),a(d,{label:"\u521B\u5EFA\u4EBA",align:"center",prop:"creatorName"}),a(d,{label:"\u6570\u91CF",align:"center",prop:"totalCount",formatter:l(pe)},null,8,["formatter"]),a(d,{label:"\u91D1\u989D",align:"center",prop:"totalPrice",formatter:l(de)},null,8,["formatter"]),a(d,{label:"\u72B6\u6001",align:"center",fixed:"right",width:"90",prop:"status"},{default:r(e=>[a(X,{type:l(q).ERP_AUDIT_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),a(d,{label:"\u64CD\u4F5C",align:"center",fixed:"right",width:"220"},{default:r(e=>[m((i(),s(p,{link:"",onClick:h=>P("detail",e.row.id)},{default:r(()=>[c(" \u8BE6\u60C5 ")]),_:2},1032,["onClick"])),[[f,["erp:stock-in:query"]]]),m((i(),s(p,{link:"",type:"primary",onClick:h=>P("update",e.row.id),disabled:e.row.status===20},{default:r(()=>[c(" \u7F16\u8F91 ")]),_:2},1032,["onClick","disabled"])),[[f,["erp:stock-in:update"]]]),e.row.status===10?m((i(),s(p,{key:0,link:"",type:"primary",onClick:h=>K(e.row.id,20)},{default:r(()=>[c(" \u5BA1\u6279 ")]),_:2},1032,["onClick"])),[[f,["erp:stock-in:update-status"]]]):m((i(),s(p,{key:1,link:"",type:"danger",onClick:h=>K(e.row.id,10)},{default:r(()=>[c(" \u53CD\u5BA1\u6279 ")]),_:2},1032,["onClick"])),[[f,["erp:stock-in:update-status"]]]),m((i(),s(p,{link:"",type:"danger",onClick:h=>H([e.row.id])},{default:r(()=>[c(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[f,["erp:stock-in:delete"]]])]),_:1})]),_:1},8,["data"])),[[le,l(U)]]),a(ae,{total:l(D),page:l(o).pageNo,"onUpdate:page":t[10]||(t[10]=e=>l(o).pageNo=e),limit:l(o).pageSize,"onUpdate:limit":t[11]||(t[11]=e=>l(o).pageSize=e),onPagination:y},null,8,["total","page","limit"])]),_:1}),a(Ye,{ref_key:"formRef",ref:E,onSuccess:y},null,512)],64)}}})});export{Ze as __tla,G as default};
