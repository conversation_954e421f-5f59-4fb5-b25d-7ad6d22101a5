import{u as P,_ as b,a as z,__tla as k}from"./useTable-Cn_FDA9m.js";import{d as C,r as v,C as x,T as L,o as m,c as j,i as r,w as l,a as t,H as R,l as w,j as M,F as U,N as q,__tla as A}from"./index-BUSn51wb.js";import{_ as F,__tla as H}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as N,__tla as O}from"./index-COobLwz-.js";import{a as i,_ as T,g as B,__tla as D}from"./MailLogDetail.vue_vue_type_script_setup_true_lang-D1hD4CUj.js";import{__tla as E}from"./Form-DJa9ov9B.js";import{__tla as G}from"./el-virtual-list-4L-8WDNg.js";import{__tla as I}from"./el-tree-select-CBuha0HW.js";import{__tla as J}from"./el-time-select-C-_NEIfl.js";import{__tla as K}from"./InputPassword-RefetKoR.js";import{__tla as Q}from"./index-Cch5e1V0.js";import{__tla as V}from"./useForm-C3fyhjNV.js";import"./download-e0EdwhTv.js";import{__tla as W}from"./el-card-CJbXGyyg.js";import{__tla as X}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as Y}from"./Descriptions-DnGjMn9o.js";import{__tla as Z}from"./Descriptions.vue_vue_type_style_index_0_scoped_30b8da63_lang-DDC-j81O.js";import{__tla as $}from"./el-descriptions-item-dD3qa0ub.js";import{__tla as tt}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import"./color-BN7ZL7BD.js";import{__tla as at}from"./formatTime-DWdBpgsM.js";import{__tla as rt}from"./index-Dtskw_Cu.js";import{__tla as _t}from"./useCrudSchemas-hBakuBRx.js";import"./tree-BMa075Oj.js";let u,et=Promise.all([(()=>{try{return k}catch{}})(),(()=>{try{return A}catch{}})(),(()=>{try{return H}catch{}})(),(()=>{try{return O}catch{}})(),(()=>{try{return D}catch{}})(),(()=>{try{return E}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return I}catch{}})(),(()=>{try{return J}catch{}})(),(()=>{try{return K}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return V}catch{}})(),(()=>{try{return W}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return Z}catch{}})(),(()=>{try{return $}catch{}})(),(()=>{try{return tt}catch{}})(),(()=>{try{return at}catch{}})(),(()=>{try{return rt}catch{}})(),(()=>{try{return _t}catch{}})()]).then(async()=>{u=C({name:"SystemMailLog",__name:"index",setup(lt){const{tableObject:a,tableMethods:p}=P({getListApi:B}),{getList:h,setSearchParams:o}=p,c=v();return x(()=>{h()}),(ot,e)=>{const f=N,y=b,s=F,g=q,d=z,S=L("hasPermi");return m(),j(U,null,[r(f,{title:"\u90AE\u4EF6\u914D\u7F6E",url:"https://doc.iocoder.cn/mail"}),r(s,null,{default:l(()=>[r(y,{schema:t(i).searchSchema,onSearch:t(o),onReset:t(o)},null,8,["schema","onSearch","onReset"])]),_:1}),r(s,null,{default:l(()=>[r(d,{columns:t(i).tableColumns,data:t(a).tableList,loading:t(a).loading,pagination:{total:t(a).total},pageSize:t(a).pageSize,"onUpdate:pageSize":e[0]||(e[0]=_=>t(a).pageSize=_),currentPage:t(a).currentPage,"onUpdate:currentPage":e[1]||(e[1]=_=>t(a).currentPage=_)},{action:l(({row:_})=>[R((m(),w(g,{link:"",type:"primary",onClick:ct=>{return n=_.id,void c.value.open(n);var n}},{default:l(()=>[M(" \u8BE6\u60C5 ")]),_:2},1032,["onClick"])),[[S,["system:mail-log:query"]]])]),_:1},8,["columns","data","loading","pagination","pageSize","currentPage"])]),_:1}),r(T,{ref_key:"detailRef",ref:c},null,512)],64)}}})});export{et as __tla,u as default};
