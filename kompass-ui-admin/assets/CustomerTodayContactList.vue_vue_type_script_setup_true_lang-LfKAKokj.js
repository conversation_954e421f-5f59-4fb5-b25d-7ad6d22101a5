import{d as z,u as G,r as i,C as P,o as p,c as y,i as e,w as r,a as l,F as w,k as U,l as g,H as B,j as k,t as E,G as s,g as j,J as q,K as H,L as J,O as K,v as Q,P as Y,Q as W,R as X,__tla as Z}from"./index-BUSn51wb.js";import{_ as $,__tla as ee}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as ae,__tla as le}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as te,__tla as re}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{e as oe,__tla as ne}from"./index-CD52sTBY.js";import{d,__tla as pe}from"./formatTime-DWdBpgsM.js";import{a as ue,S as ie}from"./common-BQQO87UM.js";let L,se=Promise.all([(()=>{try{return Z}catch{}})(),(()=>{try{return ee}catch{}})(),(()=>{try{return le}catch{}})(),(()=>{try{return re}catch{}})(),(()=>{try{return ne}catch{}})(),(()=>{try{return pe}catch{}})()]).then(async()=>{let h;h=j("div",{class:"pb-5 text-xl"}," \u4ECA\u65E5\u9700\u8054\u7CFB\u5BA2\u6237 ",-1),L=z({name:"CrmCustomerTodayContactList",__name:"CustomerTodayContactList",setup(ce){const{push:O}=G(),_=i(!0),b=i(0),v=i([]),o=i({pageNo:1,pageSize:10,contactStatus:1,sceneType:1,pool:null}),I=i(),m=async()=>{_.value=!0;try{const f=await oe(o.value);v.value=f.list,b.value=f.total}finally{_.value=!1}},C=()=>{o.value.pageNo=1,m()};return P(()=>{m()}),(f,n)=>{const S=q,x=H,T=J,V=K,N=te,M=Q,t=Y,u=ae,A=W,D=$,F=X;return p(),y(w,null,[e(N,null,{default:r(()=>[h,e(V,{ref_key:"queryFormRef",ref:I,inline:!0,model:l(o),class:"-mb-15px","label-width":"68px"},{default:r(()=>[e(T,{label:"\u72B6\u6001",prop:"contactStatus"},{default:r(()=>[e(x,{modelValue:l(o).contactStatus,"onUpdate:modelValue":n[0]||(n[0]=a=>l(o).contactStatus=a),class:"!w-240px",placeholder:"\u72B6\u6001",onChange:C},{default:r(()=>[(p(!0),y(w,null,U(l(ue),(a,c)=>(p(),g(S,{label:a.label,value:a.value,key:c},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(T,{label:"\u5F52\u5C5E",prop:"sceneType"},{default:r(()=>[e(x,{modelValue:l(o).sceneType,"onUpdate:modelValue":n[1]||(n[1]=a=>l(o).sceneType=a),class:"!w-240px",placeholder:"\u5F52\u5C5E",onChange:C},{default:r(()=>[(p(!0),y(w,null,U(l(ie),(a,c)=>(p(),g(S,{label:a.label,value:a.value,key:c},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),e(N,null,{default:r(()=>[B((p(),g(A,{data:l(v),"show-overflow-tooltip":!0,stripe:!0},{default:r(()=>[e(t,{align:"center",label:"\u5BA2\u6237\u540D\u79F0",fixed:"left",prop:"name",width:"160"},{default:r(a=>[e(M,{underline:!1,type:"primary",onClick:c=>{return R=a.row.id,void O({name:"CrmCustomerDetail",params:{id:R}});var R}},{default:r(()=>[k(E(a.row.name),1)]),_:2},1032,["onClick"])]),_:1}),e(t,{align:"center",label:"\u5BA2\u6237\u6765\u6E90",prop:"source",width:"100"},{default:r(a=>[e(u,{type:l(s).CRM_CUSTOMER_SOURCE,value:a.row.source},null,8,["type","value"])]),_:1}),e(t,{label:"\u624B\u673A",align:"center",prop:"mobile",width:"120"}),e(t,{label:"\u7535\u8BDD",align:"center",prop:"telephone",width:"130"}),e(t,{label:"\u90AE\u7BB1",align:"center",prop:"email",width:"180"}),e(t,{align:"center",label:"\u5BA2\u6237\u7EA7\u522B",prop:"level",width:"135"},{default:r(a=>[e(u,{type:l(s).CRM_CUSTOMER_LEVEL,value:a.row.level},null,8,["type","value"])]),_:1}),e(t,{align:"center",label:"\u5BA2\u6237\u884C\u4E1A",prop:"industryId",width:"100"},{default:r(a=>[e(u,{type:l(s).CRM_CUSTOMER_INDUSTRY,value:a.row.industryId},null,8,["type","value"])]),_:1}),e(t,{formatter:l(d),align:"center",label:"\u4E0B\u6B21\u8054\u7CFB\u65F6\u95F4",prop:"contactNextTime",width:"180px"},null,8,["formatter"]),e(t,{align:"center",label:"\u5907\u6CE8",prop:"remark",width:"200"}),e(t,{align:"center",label:"\u9501\u5B9A\u72B6\u6001",prop:"lockStatus"},{default:r(a=>[e(u,{type:l(s).INFRA_BOOLEAN_STRING,value:a.row.lockStatus},null,8,["type","value"])]),_:1}),e(t,{align:"center",label:"\u6210\u4EA4\u72B6\u6001",prop:"dealStatus"},{default:r(a=>[e(u,{type:l(s).INFRA_BOOLEAN_STRING,value:a.row.dealStatus},null,8,["type","value"])]),_:1}),e(t,{formatter:l(d),align:"center",label:"\u6700\u540E\u8DDF\u8FDB\u65F6\u95F4",prop:"contactLastTime",width:"180px"},null,8,["formatter"]),e(t,{align:"center",label:"\u6700\u540E\u8DDF\u8FDB\u8BB0\u5F55",prop:"contactLastContent",width:"200"}),e(t,{label:"\u5730\u5740",align:"center",prop:"detailAddress",width:"180"}),e(t,{align:"center",label:"\u8DDD\u79BB\u8FDB\u5165\u516C\u6D77\u5929\u6570",prop:"poolDay",width:"140"},{default:r(a=>[k(E(a.row.poolDay)+" \u5929",1)]),_:1}),e(t,{align:"center",label:"\u8D1F\u8D23\u4EBA",prop:"ownerUserName",width:"100px"}),e(t,{align:"center",label:"\u6240\u5C5E\u90E8\u95E8",prop:"ownerUserDeptName",width:"100px"}),e(t,{formatter:l(d),align:"center",label:"\u66F4\u65B0\u65F6\u95F4",prop:"updateTime",width:"180px"},null,8,["formatter"]),e(t,{formatter:l(d),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"]),e(t,{align:"center",label:"\u521B\u5EFA\u4EBA",prop:"creatorName",width:"100px"})]),_:1},8,["data"])),[[F,l(_)]]),e(D,{limit:l(o).pageSize,"onUpdate:limit":n[2]||(n[2]=a=>l(o).pageSize=a),page:l(o).pageNo,"onUpdate:page":n[3]||(n[3]=a=>l(o).pageNo=a),total:l(b),onPagination:m},null,8,["limit","page","total"])]),_:1})],64)}}})});export{L as _,se as __tla};
