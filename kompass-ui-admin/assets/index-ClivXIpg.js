import{d as z,r as o,at as F,o as t,l as u,w as g,c as r,F as v,k as m,a as s,g as w,av as x,a0 as H,t as b,a9 as y,__tla as M}from"./index-BUSn51wb.js";import{E as P,a as S,__tla as q}from"./el-carousel-item-D3JjuyEq.js";import{E as A,__tla as B}from"./el-image-BjHZRFih.js";let k,D=Promise.all([(()=>{try{return M}catch{}})(),(()=>{try{return q}catch{}})(),(()=>{try{return B}catch{}})()]).then(async()=>{let h;h={class:"flex flex-row flex-wrap"},k=z({name:"MenuSwiper",__name:"index",props:{property:{}},setup(C){const a=C,p=o([]),d=o(0),n=o(0),_=o("");return F(()=>a.property,()=>{_.value=1/a.property.column*100+"%",n.value=32+(a.property.layout==="iconText"?62:42),d.value=a.property.row*n.value;const i=a.property.row*a.property.column;p.value=[];let l=[];for(const c of a.property.list)l.length===i&&(l=[]),l.length===0&&p.value.push(l),l.push(c)},{immediate:!0,deep:!0}),(i,l)=>{const c=A,j=P,E=S;return t(),u(E,{height:`${s(d)}px`,autoplay:!1,arrow:"hover","indicator-position":"outside"},{default:g(()=>[(t(!0),r(v,null,m(s(p),(T,U)=>(t(),u(j,{key:U},{default:g(()=>[w("div",h,[(t(!0),r(v,null,m(T,(e,$)=>{var f;return t(),r("div",{key:$,class:"relative flex flex-col items-center justify-center",style:x({width:s(_),height:`${s(n)}px`})},[w("div",{class:H(["relative","h-42px w-42px"])},[(f=e.badge)!=null&&f.show?(t(),r("span",{key:0,class:"absolute right--10px top--10px z-1 h-20px rounded-10px p-x-6px text-center text-12px leading-20px",style:x({color:e.badge.textColor,backgroundColor:e.badge.bgColor})},b(e.badge.text),5)):y("",!0),e.iconUrl?(t(),u(c,{key:1,src:e.iconUrl,class:"h-full w-full"},null,8,["src"])):y("",!0)],2),i.property.layout==="iconText"?(t(),r("span",{key:0,class:"text-12px",style:x({color:e.titleColor,height:"20px",lineHeight:"20px"})},b(e.title),5)):y("",!0)],4)}),128))])]),_:2},1024))),128))]),_:1},8,["height"])}}})});export{D as __tla,k as default};
