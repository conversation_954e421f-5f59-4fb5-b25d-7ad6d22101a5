import{d as x,p as y,o as _,c as f,g as t,t as u,i as T,w as E,n as b,N as k,j as C,__tla as h}from"./index-BUSn51wb.js";let c,w=Promise.all([(()=>{try{return h}catch{}})()]).then(async()=>{let a,o,l,n,i;a={class:"flex justify-center"},o={class:"text-center"},l=["src"],n={class:"text-14px text-[var(--el-color-info)]"},i={class:"mt-20px"},c=x({name:"<PERSON>rror",__name:"Error",props:{type:y.string.validate(e=>["404","500","403"].includes(e)).def("404")},emits:["errorClick"],setup(e,{emit:m}){const{t:r}=b(),s={404:{url:"/assets/404-B3JyPfEa.svg",message:r("error.pageError"),buttonText:r("error.returnToHome")},500:{url:"/assets/500-BGu8fdSB.svg",message:r("error.networkError"),buttonText:r("error.returnToHome")},403:{url:"/assets/403-RqeqO19C.svg",message:r("error.noPermission"),buttonText:r("error.returnToHome")}},p=e,d=m,g=()=>{d("errorClick",p.type)};return(B,H)=>{const v=k;return _(),f("div",a,[t("div",o,[t("img",{src:s[e.type].url,alt:"",width:"350"},null,8,l),t("div",n,u(s[e.type].message),1),t("div",i,[T(v,{type:"primary",onClick:g},{default:E(()=>[C(u(s[e.type].buttonText),1)]),_:1})])])])}}})});export{c as _,w as __tla};
