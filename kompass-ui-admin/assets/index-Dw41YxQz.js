import{d as de,I as se,n as ie,r as p,f as ce,C as pe,T as ne,o as u,c as b,i as a,w as r,a as l,U as A,F as w,k,l as d,V as _e,G as q,j as n,H as f,eo as me,dV as G,t as fe,dX as ye,Z as be,L as he,J as we,K as ve,M as ge,_ as ke,N as Ve,O as xe,P as Se,ax as Ce,Q as Ie,R as Ue,__tla as Pe}from"./index-BUSn51wb.js";import{_ as Ne,__tla as Te}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as Ae,__tla as De}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as Oe,__tla as Ke}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as <PERSON>,__tla as Re}from"./index-COobLwz-.js";import{b as ze,__tla as He}from"./formatTime-DWdBpgsM.js";import{d as Me}from"./download-e0EdwhTv.js";import{S as I,__tla as Ye}from"./index-Cj6fFS0g.js";import{_ as Ee,__tla as Fe}from"./SaleOutForm.vue_vue_type_script_setup_true_lang-BGJGIACB.js";import{P as Qe,__tla as We}from"./index-B00QUU3o.js";import{g as qe,__tla as Ge}from"./index-BYXzDB8j.js";import{C as Xe,__tla as Ze}from"./index-DYwp4_G0.js";import{W as je,__tla as Je}from"./index-B5GxX3eg.js";import{A as $e,__tla as Be}from"./index-LbO7ASKC.js";import{__tla as ea}from"./index-Cch5e1V0.js";import"./color-BN7ZL7BD.js";import{__tla as aa}from"./el-card-CJbXGyyg.js";import{__tla as la}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as ta}from"./SaleOutItemForm.vue_vue_type_script_setup_true_lang-DRXsm1S3.js";import{__tla as ra}from"./index-BCEOZol9.js";import{__tla as oa}from"./SaleOrderOutEnableList.vue_vue_type_script_setup_true_lang-C2kuHgAL.js";import{__tla as ua}from"./index-DgsXVLii.js";let X,da=Promise.all([(()=>{try{return Pe}catch{}})(),(()=>{try{return Te}catch{}})(),(()=>{try{return De}catch{}})(),(()=>{try{return Ke}catch{}})(),(()=>{try{return Re}catch{}})(),(()=>{try{return He}catch{}})(),(()=>{try{return Ye}catch{}})(),(()=>{try{return Fe}catch{}})(),(()=>{try{return We}catch{}})(),(()=>{try{return Ge}catch{}})(),(()=>{try{return Ze}catch{}})(),(()=>{try{return Je}catch{}})(),(()=>{try{return Be}catch{}})(),(()=>{try{return ea}catch{}})(),(()=>{try{return aa}catch{}})(),(()=>{try{return la}catch{}})(),(()=>{try{return ta}catch{}})(),(()=>{try{return ra}catch{}})(),(()=>{try{return oa}catch{}})(),(()=>{try{return ua}catch{}})()]).then(async()=>{let D;D={key:0},X=de({name:"ErpSaleOut",__name:"index",setup(sa){const V=se(),{t:Z}=ie(),U=p(!0),O=p([]),K=p(0),o=ce({pageNo:1,pageSize:10,no:void 0,customerId:void 0,productId:void 0,warehouseId:void 0,outTime:[],orderNo:void 0,receiptStatus:void 0,accountId:void 0,status:void 0,remark:void 0,creator:void 0}),L=p(),P=p(!1),R=p([]),z=p([]),H=p([]),M=p([]),Y=p([]),v=async()=>{U.value=!0;try{const s=await I.getSaleOutPage(o);O.value=s.list,K.value=s.total}finally{U.value=!1}},x=()=>{o.pageNo=1,v()},j=()=>{L.value.resetFields(),x()},E=p(),N=(s,t)=>{E.value.open(s,t)},F=async s=>{try{await V.delConfirm(),await I.deleteSaleOut(s),V.success(Z("common.delSuccess")),await v(),S.value=S.value.filter(t=>!s.includes(t.id))}catch{}},Q=async(s,t)=>{try{await V.confirm(`\u786E\u5B9A${t===20?"\u5BA1\u6279":"\u53CD\u5BA1\u6279"}\u8BE5\u51FA\u5E93\u5417\uFF1F`),await I.updateSaleOutStatus(s,t),V.success((t===20?"\u5BA1\u6279":"\u53CD\u5BA1\u6279")+"\u6210\u529F"),await v()}catch{}},J=async()=>{try{await V.exportConfirm(),P.value=!0;const s=await I.exportSaleOut(o);Me.excel(s,"\u9500\u552E\u51FA\u5E93.xls")}catch{}finally{P.value=!1}},S=p([]),$=s=>{S.value=s};return pe(async()=>{await v(),R.value=await Qe.getProductSimpleList(),z.value=await Xe.getCustomerSimpleList(),H.value=await qe(),M.value=await je.getWarehouseSimpleList(),Y.value=await $e.getAccountSimpleList()}),(s,t)=>{const B=Le,T=be,i=he,m=we,h=ve,ee=ge,C=ke,_=Ve,ae=xe,W=Oe,c=Se,le=Ce,te=Ae,re=Ie,oe=Ne,y=ne("hasPermi"),ue=Ue;return u(),b(w,null,[a(B,{title:"\u3010\u9500\u552E\u3011\u9500\u552E\u8BA2\u5355\u3001\u51FA\u5E93\u3001\u9000\u8D27",url:"https://doc.iocoder.cn/erp/sale/"}),a(W,null,{default:r(()=>[a(ae,{class:"-mb-15px",model:l(o),ref_key:"queryFormRef",ref:L,inline:!0,"label-width":"68px"},{default:r(()=>[a(i,{label:"\u51FA\u5E93\u5355\u53F7",prop:"no"},{default:r(()=>[a(T,{modelValue:l(o).no,"onUpdate:modelValue":t[0]||(t[0]=e=>l(o).no=e),placeholder:"\u8BF7\u8F93\u5165\u51FA\u5E93\u5355\u53F7",clearable:"",onKeyup:A(x,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(i,{label:"\u4EA7\u54C1",prop:"productId"},{default:r(()=>[a(h,{modelValue:l(o).productId,"onUpdate:modelValue":t[1]||(t[1]=e=>l(o).productId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1",class:"!w-240px"},{default:r(()=>[(u(!0),b(w,null,k(l(R),e=>(u(),d(m,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(i,{label:"\u51FA\u5E93\u65F6\u95F4",prop:"outTime"},{default:r(()=>[a(ee,{modelValue:l(o).outTime,"onUpdate:modelValue":t[2]||(t[2]=e=>l(o).outTime=e),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-220px"},null,8,["modelValue","default-time"])]),_:1}),a(i,{label:"\u5BA2\u6237",prop:"customerId"},{default:r(()=>[a(h,{modelValue:l(o).customerId,"onUpdate:modelValue":t[3]||(t[3]=e=>l(o).customerId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4F9B\u5BA2\u6237",class:"!w-240px"},{default:r(()=>[(u(!0),b(w,null,k(l(z),e=>(u(),d(m,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(i,{label:"\u4ED3\u5E93",prop:"warehouseId"},{default:r(()=>[a(h,{modelValue:l(o).warehouseId,"onUpdate:modelValue":t[4]||(t[4]=e=>l(o).warehouseId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93",class:"!w-240px"},{default:r(()=>[(u(!0),b(w,null,k(l(M),e=>(u(),d(m,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(i,{label:"\u521B\u5EFA\u4EBA",prop:"creator"},{default:r(()=>[a(h,{modelValue:l(o).creator,"onUpdate:modelValue":t[5]||(t[5]=e=>l(o).creator=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u521B\u5EFA\u4EBA",class:"!w-240px"},{default:r(()=>[(u(!0),b(w,null,k(l(H),e=>(u(),d(m,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(i,{label:"\u5173\u8054\u8BA2\u5355",prop:"orderNo"},{default:r(()=>[a(T,{modelValue:l(o).orderNo,"onUpdate:modelValue":t[6]||(t[6]=e=>l(o).orderNo=e),placeholder:"\u8BF7\u8F93\u5165\u5173\u8054\u8BA2\u5355",clearable:"",onKeyup:A(x,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(i,{label:"\u7ED3\u7B97\u8D26\u6237",prop:"accountId"},{default:r(()=>[a(h,{modelValue:l(o).accountId,"onUpdate:modelValue":t[7]||(t[7]=e=>l(o).accountId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u7ED3\u7B97\u8D26\u6237",class:"!w-240px"},{default:r(()=>[(u(!0),b(w,null,k(l(Y),e=>(u(),d(m,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(i,{label:"\u6536\u6B3E\u72B6\u6001",prop:"receiptStatus"},{default:r(()=>[a(h,{modelValue:l(o).receiptStatus,"onUpdate:modelValue":t[8]||(t[8]=e=>l(o).receiptStatus=e),placeholder:"\u8BF7\u9009\u62E9\u6709\u6B3E\u72B6\u6001",clearable:"",class:"!w-240px"},{default:r(()=>[a(m,{label:"\u672A\u6536\u6B3E",value:"0"}),a(m,{label:"\u90E8\u5206\u6536\u6B3E",value:"1"}),a(m,{label:"\u5168\u90E8\u6536\u6B3E",value:"2"})]),_:1},8,["modelValue"])]),_:1}),a(i,{label:"\u5BA1\u6838\u72B6\u6001",prop:"status"},{default:r(()=>[a(h,{modelValue:l(o).status,"onUpdate:modelValue":t[9]||(t[9]=e=>l(o).status=e),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",clearable:"",class:"!w-240px"},{default:r(()=>[(u(!0),b(w,null,k(l(_e)(l(q).ERP_AUDIT_STATUS),e=>(u(),d(m,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(i,{label:"\u5907\u6CE8",prop:"remark"},{default:r(()=>[a(T,{modelValue:l(o).remark,"onUpdate:modelValue":t[10]||(t[10]=e=>l(o).remark=e),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",clearable:"",onKeyup:A(x,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(i,null,{default:r(()=>[a(_,{onClick:x},{default:r(()=>[a(C,{icon:"ep:search",class:"mr-5px"}),n(" \u641C\u7D22")]),_:1}),a(_,{onClick:j},{default:r(()=>[a(C,{icon:"ep:refresh",class:"mr-5px"}),n(" \u91CD\u7F6E")]),_:1}),f((u(),d(_,{type:"primary",plain:"",onClick:t[11]||(t[11]=e=>N("create"))},{default:r(()=>[a(C,{icon:"ep:plus",class:"mr-5px"}),n(" \u65B0\u589E ")]),_:1})),[[y,["erp:sale-out:create"]]]),f((u(),d(_,{type:"success",plain:"",onClick:J,loading:l(P)},{default:r(()=>[a(C,{icon:"ep:download",class:"mr-5px"}),n(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[y,["erp:sale-out:export"]]]),f((u(),d(_,{type:"danger",plain:"",onClick:t[12]||(t[12]=e=>F(l(S).map(g=>g.id))),disabled:l(S).length===0},{default:r(()=>[a(C,{icon:"ep:delete",class:"mr-5px"}),n(" \u5220\u9664 ")]),_:1},8,["disabled"])),[[y,["erp:sale-out:delete"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(W,null,{default:r(()=>[f((u(),d(re,{data:l(O),stripe:!0,"show-overflow-tooltip":!0,onSelectionChange:$},{default:r(()=>[a(c,{width:"30",label:"\u9009\u62E9",type:"selection"}),a(c,{"min-width":"180",label:"\u51FA\u5E93\u5355\u53F7",align:"center",prop:"no"}),a(c,{label:"\u4EA7\u54C1\u4FE1\u606F",align:"center",prop:"productNames","min-width":"200"}),a(c,{label:"\u5BA2\u6237",align:"center",prop:"customerName"}),a(c,{label:"\u51FA\u5E93\u65F6\u95F4",align:"center",prop:"outTime",formatter:l(ze),width:"120px"},null,8,["formatter"]),a(c,{label:"\u521B\u5EFA\u4EBA",align:"center",prop:"creatorName"}),a(c,{label:"\u603B\u6570\u91CF",align:"center",prop:"totalCount",formatter:l(me)},null,8,["formatter"]),a(c,{label:"\u5E94\u6536\u91D1\u989D",align:"center",prop:"totalPrice",formatter:l(G)},null,8,["formatter"]),a(c,{label:"\u5DF2\u6536\u91D1\u989D",align:"center",prop:"receiptPrice",formatter:l(G)},null,8,["formatter"]),a(c,{label:"\u672A\u6536\u91D1\u989D",align:"center"},{default:r(e=>[e.row.receiptPrice===e.row.totalPrice?(u(),b("span",D,"0")):(u(),d(le,{key:1,type:"danger"},{default:r(()=>[n(fe(l(ye)(e.row.totalPrice-e.row.receiptPrice)),1)]),_:2},1024))]),_:1}),a(c,{label:"\u5BA1\u6838\u72B6\u6001",align:"center",fixed:"right",width:"90",prop:"status"},{default:r(e=>[a(te,{type:l(q).ERP_AUDIT_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),a(c,{label:"\u64CD\u4F5C",align:"center",fixed:"right",width:"220"},{default:r(e=>[f((u(),d(_,{link:"",onClick:g=>N("detail",e.row.id)},{default:r(()=>[n(" \u8BE6\u60C5 ")]),_:2},1032,["onClick"])),[[y,["erp:sale-out:query"]]]),f((u(),d(_,{link:"",type:"primary",onClick:g=>N("update",e.row.id),disabled:e.row.status===20},{default:r(()=>[n(" \u7F16\u8F91 ")]),_:2},1032,["onClick","disabled"])),[[y,["erp:sale-out:update"]]]),e.row.status===10?f((u(),d(_,{key:0,link:"",type:"primary",onClick:g=>Q(e.row.id,20)},{default:r(()=>[n(" \u5BA1\u6279 ")]),_:2},1032,["onClick"])),[[y,["erp:sale-out:update-status"]]]):f((u(),d(_,{key:1,link:"",type:"danger",onClick:g=>Q(e.row.id,10)},{default:r(()=>[n(" \u53CD\u5BA1\u6279 ")]),_:2},1032,["onClick"])),[[y,["erp:sale-out:update-status"]]]),f((u(),d(_,{link:"",type:"danger",onClick:g=>F([e.row.id])},{default:r(()=>[n(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[y,["erp:sale-out:delete"]]])]),_:1})]),_:1},8,["data"])),[[ue,l(U)]]),a(oe,{total:l(K),page:l(o).pageNo,"onUpdate:page":t[13]||(t[13]=e=>l(o).pageNo=e),limit:l(o).pageSize,"onUpdate:limit":t[14]||(t[14]=e=>l(o).pageSize=e),onPagination:v},null,8,["total","page","limit"])]),_:1}),a(Ee,{ref_key:"formRef",ref:E,onSuccess:v},null,512)],64)}}})});export{da as __tla,X as default};
