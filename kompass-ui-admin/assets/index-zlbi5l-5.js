import{d as $,u as ee,r as v,f as le,C as ae,cJ as g,G as c,o as s,c as i,i as l,w as t,a,U as S,F as m,k as w,l as f,j as h,H as te,t as y,g as x,aF as re,aD as ue,aJ as oe,Z as se,L as ne,J as pe,K as de,M as _e,_ as ce,N as ie,O as me,z as fe,A as ye,P as be,ax as ve,Q as we,R as he,__tla as Te}from"./index-BUSn51wb.js";import{_ as Ae,__tla as Ve}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as ge,__tla as xe}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{E as Ee,__tla as Ne}from"./el-image-BjHZRFih.js";import{_ as ke,__tla as Se}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as Ue,__tla as Re}from"./index-COobLwz-.js";import{e as De,__tla as Ce}from"./index-3xRtOTae.js";import{f as Fe,__tla as Le}from"./formatTime-DWdBpgsM.js";import{__tla as Pe}from"./index-Cch5e1V0.js";import"./color-BN7ZL7BD.js";import{__tla as Ye}from"./el-card-CJbXGyyg.js";let K,ze=Promise.all([(()=>{try{return Te}catch{}})(),(()=>{try{return Ve}catch{}})(),(()=>{try{return xe}catch{}})(),(()=>{try{return Ne}catch{}})(),(()=>{try{return Se}catch{}})(),(()=>{try{return Re}catch{}})(),(()=>{try{return Ce}catch{}})(),(()=>{try{return Le}catch{}})(),(()=>{try{return Pe}catch{}})(),(()=>{try{return Ye}catch{}})()]).then(async()=>{let U,R;U={class:"flex items-center"},R={class:"mr-10px"},K=$({name:"TradeAfterSale",__name:"index",setup(Je){const{push:D}=ee(),E=v(!0),C=v(0),F=v([]),L=v([{label:"\u5168\u90E8",value:"0"}]),P=v(),r=le({pageNo:1,pageSize:10,no:null,status:"0",orderNo:null,spuName:null,createTime:[],way:null,type:null}),T=async()=>{E.value=!0;try{const o=ue(r);o.status==="0"&&delete o.status;const u=await De(o);F.value=u.list,C.value=u.total}finally{E.value=!1}},b=async()=>{r.pageNo=1,await T()},H=()=>{var o;(o=P.value)==null||o.resetFields(),b()},M=async o=>{r.status=o.paneName,await T()};return ae(async()=>{await T();for(const o of g(c.TRADE_AFTER_SALE_STATUS))L.value.push({label:o.label,value:o.value})}),(o,u)=>{const O=Ue,N=se,p=ne,A=pe,k=de,I=_e,Y=ce,V=ie,W=me,z=ke,j=fe,q=ye,n=be,G=Ee,Q=ve,J=ge,Z=we,B=Ae,X=he;return s(),i(m,null,[l(O,{title:"\u3010\u4EA4\u6613\u3011\u552E\u540E\u9000\u6B3E",url:"https://doc.iocoder.cn/mall/trade-aftersale/"}),l(z,null,{default:t(()=>[l(W,{ref_key:"queryFormRef",ref:P,inline:!0,model:a(r),"label-width":"68px"},{default:t(()=>[l(p,{label:"\u5546\u54C1\u540D\u79F0",prop:"spuName"},{default:t(()=>[l(N,{modelValue:a(r).spuName,"onUpdate:modelValue":u[0]||(u[0]=e=>a(r).spuName=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u5546\u54C1 SPU \u540D\u79F0",onKeyup:S(b,["enter"])},null,8,["modelValue"])]),_:1}),l(p,{label:"\u9000\u6B3E\u7F16\u53F7",prop:"no"},{default:t(()=>[l(N,{modelValue:a(r).no,"onUpdate:modelValue":u[1]||(u[1]=e=>a(r).no=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u9000\u6B3E\u7F16\u53F7",onKeyup:S(b,["enter"])},null,8,["modelValue"])]),_:1}),l(p,{label:"\u8BA2\u5355\u7F16\u53F7",prop:"orderNo"},{default:t(()=>[l(N,{modelValue:a(r).orderNo,"onUpdate:modelValue":u[2]||(u[2]=e=>a(r).orderNo=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u8BA2\u5355\u7F16\u53F7",onKeyup:S(b,["enter"])},null,8,["modelValue"])]),_:1}),l(p,{label:"\u552E\u540E\u72B6\u6001",prop:"status"},{default:t(()=>[l(k,{modelValue:a(r).status,"onUpdate:modelValue":u[3]||(u[3]=e=>a(r).status=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u552E\u540E\u72B6\u6001"},{default:t(()=>[l(A,{label:"\u5168\u90E8",value:"0"}),(s(!0),i(m,null,w(a(g)(a(c).TRADE_AFTER_SALE_STATUS),e=>(s(),f(A,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"\u552E\u540E\u65B9\u5F0F",prop:"way"},{default:t(()=>[l(k,{modelValue:a(r).way,"onUpdate:modelValue":u[4]||(u[4]=e=>a(r).way=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u552E\u540E\u65B9\u5F0F"},{default:t(()=>[(s(!0),i(m,null,w(a(g)(a(c).TRADE_AFTER_SALE_WAY),e=>(s(),f(A,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"\u552E\u540E\u7C7B\u578B",prop:"type"},{default:t(()=>[l(k,{modelValue:a(r).type,"onUpdate:modelValue":u[5]||(u[5]=e=>a(r).type=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u552E\u540E\u7C7B\u578B"},{default:t(()=>[(s(!0),i(m,null,w(a(g)(a(c).TRADE_AFTER_SALE_TYPE),e=>(s(),f(A,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(p,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:t(()=>[l(I,{modelValue:a(r).createTime,"onUpdate:modelValue":u[6]||(u[6]=e=>a(r).createTime=e),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-260px","end-placeholder":"\u81EA\u5B9A\u4E49\u65F6\u95F4","start-placeholder":"\u81EA\u5B9A\u4E49\u65F6\u95F4",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),l(p,null,{default:t(()=>[l(V,{onClick:b},{default:t(()=>[l(Y,{class:"mr-5px",icon:"ep:search"}),h(" \u641C\u7D22 ")]),_:1}),l(V,{onClick:H},{default:t(()=>[l(Y,{class:"mr-5px",icon:"ep:refresh"}),h(" \u91CD\u7F6E ")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),l(z,null,{default:t(()=>[l(q,{modelValue:a(r).status,"onUpdate:modelValue":u[7]||(u[7]=e=>a(r).status=e),onTabClick:M},{default:t(()=>[(s(!0),i(m,null,w(a(L),e=>(s(),f(j,{key:e.label,label:e.label,name:e.value},null,8,["label","name"]))),128))]),_:1},8,["modelValue"]),te((s(),f(Z,{data:a(F)},{default:t(()=>[l(n,{align:"center",label:"\u9000\u6B3E\u7F16\u53F7","min-width":"200",prop:"no"}),l(n,{align:"center",label:"\u8BA2\u5355\u7F16\u53F7","min-width":"200",prop:"orderNo"},{default:t(({row:e})=>[l(V,{link:"",type:"primary",onClick:_=>{return d=e.orderId,void D({name:"TradeOrderDetail",params:{id:d}});var d}},{default:t(()=>[h(y(e.orderNo),1)]),_:2},1032,["onClick"])]),_:1}),l(n,{label:"\u5546\u54C1\u4FE1\u606F","min-width":"600",prop:"spuName"},{default:t(({row:e})=>[x("div",U,[l(G,{src:e.picUrl,class:"mr-10px h-30px w-30px",onClick:_=>{return d=e.picUrl,void oe({urlList:[d]});var d}},null,8,["src","onClick"]),x("span",R,y(e.spuName),1),(s(!0),i(m,null,w(e.properties,_=>(s(),f(Q,{key:_.propertyId,class:"mr-10px"},{default:t(()=>[h(y(_.propertyName)+": "+y(_.valueName),1)]),_:2},1024))),128))])]),_:1}),l(n,{align:"center",label:"\u8BA2\u5355\u91D1\u989D",prop:"refundPrice"},{default:t(e=>[x("span",null,y(a(re)(e.row.refundPrice))+" \u5143",1)]),_:1}),l(n,{align:"center",label:"\u4E70\u5BB6",prop:"user.nickname"}),l(n,{align:"center",label:"\u7533\u8BF7\u65F6\u95F4",prop:"createTime",width:"180"},{default:t(e=>[x("span",null,y(a(Fe)(e.row.createTime)),1)]),_:1}),l(n,{align:"center",label:"\u552E\u540E\u72B6\u6001",width:"100"},{default:t(e=>[l(J,{type:a(c).TRADE_AFTER_SALE_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),l(n,{align:"center",label:"\u552E\u540E\u65B9\u5F0F"},{default:t(e=>[l(J,{type:a(c).TRADE_AFTER_SALE_WAY,value:e.row.way},null,8,["type","value"])]),_:1}),l(n,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"160"},{default:t(({row:e})=>[l(V,{link:"",type:"primary",onClick:_=>{return d=e.id,void D({name:"TradeAfterSaleDetail",params:{id:d}});var d}},{default:t(()=>[h("\u5904\u7406\u9000\u6B3E")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[X,a(E)]]),l(B,{limit:a(r).pageSize,"onUpdate:limit":u[8]||(u[8]=e=>a(r).pageSize=e),page:a(r).pageNo,"onUpdate:page":u[9]||(u[9]=e=>a(r).pageNo=e),total:a(C),onPagination:T},null,8,["limit","page","total"])]),_:1})],64)}}})});export{ze as __tla,K as default};
