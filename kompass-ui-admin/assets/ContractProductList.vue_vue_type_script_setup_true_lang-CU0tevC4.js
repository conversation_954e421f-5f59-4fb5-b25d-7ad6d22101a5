import{_ as h,__tla as w}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{d as P,o as g,l as b,w as r,i as t,j as c,t as i,a as e,G as y,dV as l,dX as p,P as x,Q as v,E as N,s as U,__tla as j}from"./index-BUSn51wb.js";import{_ as C,__tla as R}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";let u,T=Promise.all([(()=>{try{return w}catch{}})(),(()=>{try{return j}catch{}})(),(()=>{try{return R}catch{}})()]).then(async()=>{u=P({__name:"ContractProductList",props:{contract:{}},setup:D=>(o,E)=>{const a=x,d=C,_=v,s=N,m=U,f=h;return g(),b(f,null,{default:r(()=>[t(_,{data:o.contract.products,stripe:!0,"show-overflow-tooltip":!0},{default:r(()=>[t(a,{align:"center",label:"\u4EA7\u54C1\u540D\u79F0",fixed:"left",prop:"productName","min-width":"160"},{default:r(n=>[c(i(n.row.productName),1)]),_:1}),t(a,{label:"\u4EA7\u54C1\u6761\u7801",align:"center",prop:"productNo","min-width":"120"}),t(a,{align:"center",label:"\u4EA7\u54C1\u5355\u4F4D",prop:"productUnit","min-width":"160"},{default:r(({row:n})=>[t(d,{type:e(y).CRM_PRODUCT_UNIT,value:n.productUnit},null,8,["type","value"])]),_:1}),t(a,{label:"\u4EA7\u54C1\u4EF7\u683C\uFF08\u5143\uFF09",align:"center",prop:"productPrice","min-width":"140",formatter:e(l)},null,8,["formatter"]),t(a,{label:"\u5408\u540C\u4EF7\u683C\uFF08\u5143\uFF09",align:"center",prop:"contractPrice","min-width":"140",formatter:e(l)},null,8,["formatter"]),t(a,{align:"center",label:"\u6570\u91CF",prop:"count","min-width":"100px",formatter:e(l)},null,8,["formatter"]),t(a,{label:"\u5408\u8BA1\u91D1\u989D\uFF08\u5143\uFF09",align:"center",prop:"totalPrice","min-width":"140",formatter:e(l)},null,8,["formatter"])]),_:1},8,["data"]),t(m,{class:"mt-10px",justify:"end"},{default:r(()=>[t(s,{span:3},{default:r(()=>[c(" \u6574\u5355\u6298\u6263\uFF1A"+i(e(p)(o.contract.discountPercent))+"% ",1)]),_:1}),t(s,{span:4},{default:r(()=>[c(" \u4EA7\u54C1\u603B\u91D1\u989D\uFF1A"+i(e(p)(o.contract.totalProductPrice))+" \u5143 ",1)]),_:1})]),_:1})]),_:1})}})});export{u as _,T as __tla};
