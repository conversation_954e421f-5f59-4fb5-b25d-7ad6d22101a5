import{f as T,G as H,d as V,n as Y,I as j,r as c,o as A,l as I,w as u,i as f,a as o,j as M,H as R,y as E,N,R as O,__tla as U}from"./index-BUSn51wb.js";import{_ as G,__tla as q}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{_ as z,__tla as B}from"./Form-DJa9ov9B.js";import{g as J,c as K,u as L,__tla as Q}from"./index-CcfUqabz.js";import{d as W,__tla as X}from"./formatTime-DWdBpgsM.js";import{b as Z,__tla as $}from"./index-Dtskw_Cu.js";import{r as e,__tla as aa}from"./formRules-CA9eXdcX.js";import{u as ea,__tla as ta}from"./useCrudSchemas-hBakuBRx.js";let g,p,la=Promise.all([(()=>{try{return U}catch{}})(),(()=>{try{return q}catch{}})(),(()=>{try{return B}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return $}catch{}})(),(()=>{try{return aa}catch{}})(),(()=>{try{return ta}catch{}})()]).then(async()=>{let m,h,y;m=await Z(),h=T({name:[e],code:[e],accountId:[e],label:[e],content:[e],params:[e],status:[e]}),y=T([{label:"\u6A21\u677F\u7F16\u7801",field:"code",isSearch:!0},{label:"\u6A21\u677F\u540D\u79F0",field:"name",isSearch:!0},{label:"\u6A21\u677F\u6807\u9898",field:"title"},{label:"\u6A21\u677F\u5185\u5BB9",field:"content",form:{component:"Editor",componentProps:{valueHtml:"",height:200}}},{label:"\u90AE\u7BB1\u8D26\u53F7",field:"accountId",width:"200px",formatter:(k,v,_)=>{var t;return(t=m.find(n=>n.id===_))==null?void 0:t.mail},search:{show:!0,component:"Select",api:()=>m,componentProps:{optionsAlias:{labelField:"mail",valueField:"id"}}},form:{component:"Select",api:()=>m,componentProps:{optionsAlias:{labelField:"mail",valueField:"id"}}}},{label:"\u53D1\u9001\u4EBA\u540D\u79F0",field:"nickname"},{label:"\u5F00\u542F\u72B6\u6001",field:"status",isSearch:!0,dictType:H.COMMON_STATUS,dictClass:"number"},{label:"\u5907\u6CE8",field:"remark",isTable:!1},{label:"\u521B\u5EFA\u65F6\u95F4",field:"createTime",isForm:!1,formatter:W,search:{show:!0,component:"DatePicker",componentProps:{valueFormat:"YYYY-MM-DD HH:mm:ss",type:"daterange",defaultTime:[new Date("1 00:00:00"),new Date("1 23:59:59")]}}},{label:"\u64CD\u4F5C",field:"action",isForm:!1}]),{allSchemas:p}=ea(y),g=V({name:"SystemMailTemplateForm",__name:"MailTemplateForm",emits:["success"],setup(k,{expose:v,emit:_}){const{t}=Y(),n=j(),l=c(!1),b=c(""),s=c(!1),w=c(""),i=c();v({open:async(r,a)=>{if(l.value=!0,b.value=t("action."+r),w.value=r,a){s.value=!0;try{const d=await J(a);i.value.setValues(d)}finally{s.value=!1}}}});const D=_,P=async()=>{if(i&&await i.value.getElFormRef().validate()){s.value=!0;try{const r=i.value.formModel;w.value==="create"?(await K(r),n.success(t("common.createSuccess"))):(await L(r),n.success(t("common.updateSuccess"))),l.value=!1,D("success")}finally{s.value=!1}}};return(r,a)=>{const d=z,S=N,x=G,C=O;return A(),I(x,{modelValue:o(l),"onUpdate:modelValue":a[1]||(a[1]=F=>E(l)?l.value=F:null),"max-height":500,scroll:!0,title:o(b),width:800},{footer:u(()=>[f(S,{disabled:o(s),type:"primary",onClick:P},{default:u(()=>[M("\u786E \u5B9A")]),_:1},8,["disabled"]),f(S,{onClick:a[0]||(a[0]=F=>l.value=!1)},{default:u(()=>[M("\u53D6 \u6D88")]),_:1})]),default:u(()=>[R(f(d,{ref_key:"formRef",ref:i,rules:o(h),schema:o(p).formSchema},null,8,["rules","schema"]),[[C,o(s)]])]),_:1},8,["modelValue","title"])}}})});export{g as _,la as __tla,p as a};
