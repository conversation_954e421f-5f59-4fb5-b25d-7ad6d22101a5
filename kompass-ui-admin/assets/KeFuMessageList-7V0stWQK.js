import{by as P,dv as be,d as Ee,ct as V,r as v,I as Ne,f as Ie,b as C,ex as Se,aA as se,ba as Re,a as t,au as Ae,T as Ue,o,l as N,w as f,i,g as y,t as X,c as A,F as je,k as Ce,a9 as _,a0 as re,H as le,a8 as De,y as He,j as Be,b9 as Fe,b2 as Le,_ as Ke,e3 as Oe,Z as Pe,N as Ve,eb as Xe,b7 as Ye,a5 as ze,a6 as Ge,ay as We,B as $e,__tla as Qe}from"./index-BUSn51wb.js";import{E as Ze,__tla as qe}from"./el-empty-DomufbmG.js";import{E as Je,__tla as ea}from"./el-image-BjHZRFih.js";import{E as aa,__tla as ta}from"./el-avatar-Da2TGjmj.js";import{_ as sa,__tla as ra}from"./EmojiSelectPopover.vue_vue_type_script_setup_true_lang-C7bGJ0OO.js";import{_ as la,__tla as na}from"./PictureSelectUpload.vue_vue_type_script_setup_true_lang-CSrYKR-R.js";import oa,{__tla as ia}from"./ProductItem-bFAWKK8H.js";import ca,{__tla as ua}from"./OrderItem-DUnNh_aP.js";import{u as da,__tla as pa}from"./emoji-DGuwlnSb.js";import{K as h}from"./constants-CD8mqZTg.js";import{U as L}from"./constants-A8BI3pz7.js";import{f as ma,__tla as fa}from"./formatTime-DWdBpgsM.js";import"./picture-CTjip5lJ.js";let ne,va=Promise.all([(()=>{try{return Qe}catch{}})(),(()=>{try{return qe}catch{}})(),(()=>{try{return ea}catch{}})(),(()=>{try{return ta}catch{}})(),(()=>{try{return ra}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return ia}catch{}})(),(()=>{try{return ua}catch{}})(),(()=>{try{return pa}catch{}})(),(()=>{try{return fa}catch{}})()]).then(async()=>{const oe=async n=>await P.post({url:"/promotion/kefu-message/send",data:n}),ie=async n=>await P.put({url:"/promotion/kefu-message/update-read-status?conversationId="+n}),ce=async n=>await P.get({url:"/promotion/kefu-message/page",params:n});var ue={exports:{}};let Y,z,G,W,$,Q,Z,q,J,ee;Y=be(ue.exports=function(n,K,c){n=n||{};var g=K.prototype,D={future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function d(r,l,p,k){return g.fromToBase(r,l,p,k)}c.en.relativeTime=D,g.fromToBase=function(r,l,p,k,M){for(var I,b,U,w=p.$locale().relativeTime||D,j=n.thresholds||[{l:"s",r:44,d:"second"},{l:"m",r:89},{l:"mm",r:44,d:"minute"},{l:"h",r:89},{l:"hh",r:21,d:"hour"},{l:"d",r:35},{l:"dd",r:25,d:"day"},{l:"M",r:45},{l:"MM",r:10,d:"month"},{l:"y",r:17},{l:"yy",d:"year"}],O=j.length,E=0;E<O;E+=1){var m=j[E];m.d&&(I=k?c(r).diff(p,m.d,!0):p.diff(r,m.d,!0));var T=(n.rounding||Math.round)(Math.abs(I));if(U=I>0,T<=m.r||!m.r){T<=1&&E>0&&(m=j[E-1]);var S=w[m.l];M&&(T=M(""+T)),b=typeof S=="string"?S.replace("%d",T):S(T,l,m.l,U);break}}if(l)return b;var x=U?w.future:w.past;return typeof x=="function"?x(b):x.replace("%s",b)},g.to=function(r,l){return d(r,l,this,!0)},g.from=function(r,l){return d(r,l,this)};var u=function(r){return r.$u?c.utc():c()};g.toNow=function(r){return this.to(u(this),r)},g.fromNow=function(r){return this.from(u(this),r)}}),z={class:"kefu-title"},G={class:"flex justify-center items-center mb-20px"},W={key:0,class:"date-message"},$={key:1,class:"system-message"},Q={key:0,class:"flex items-center"},Z=(n=>(ze("data-v-9177901b"),n=n(),Ge(),n))(()=>y("span",null,"\u6709\u65B0\u6D88\u606F",-1)),q={class:"h-[100%]"},J={class:"chat-tools flex items-center"},ee={class:"h-45px flex justify-end"},ne=$e(Ee({name:"KeFuMessageList",__name:"KeFuMessageList",setup(n,{expose:K}){V.extend(Y);const c=v(""),{replaceEmoji:g}=da(),D=Ne(),d=v([]),u=v({}),r=v(!1),l=Ie({pageNo:1,pageSize:10,conversationId:0}),p=v(0),k=v(!1),M=C(()=>e=>Se(e.content)),I=async()=>{const e=await ce(l);if(p.value=e.total,l.pageNo===1)d.value=e.list;else for(const s of e.list)b(s);k.value=!0},b=e=>{d.value.some(s=>s.id===e.id)||d.value.push(e)},U=C(()=>(d.value.sort((e,s)=>e.createTime-s.createTime),d.value)),w=async e=>{if(u.value){if(e!==void 0){if(e.conversationId!==u.value.id)return;b(e)}else l.pageNo=1,await I();R.value?r.value=!0:await ae()}};K({getNewMessageList:async e=>{l.pageNo=1,d.value=[],p.value=0,R.value=!1,k.value=!1,u.value=e,l.conversationId=e.id,await w()},refreshMessageList:w});const j=C(()=>!se(u.value)),O=C(()=>p.value>0&&Math.ceil(p.value/l.pageSize)===l.pageNo),E=e=>{c.value+=e.name},m=async e=>{const s={conversationId:u.value.id,contentType:h.IMAGE,content:e};await S(s)},T=async()=>{if(se(t(c.value)))return void D.notifyWarning("\u8BF7\u8F93\u5165\u6D88\u606F\u540E\u518D\u53D1\u9001\u54E6\uFF01");const e={conversationId:u.value.id,contentType:h.TEXT,content:c.value};await S(e)},S=async e=>{await oe(e),c.value="",await w()},x=v(),H=v(),ae=async()=>{R.value=!1,await(async()=>{R.value||(await We(),H.value.setScrollTop(x.value.clientHeight),r.value=!1,await ie(u.value.id))})()},R=v(!1),de=Re(({scrollTop:e})=>{var B;if(O.value)return;Math.floor(e)===0&&pe();const s=(B=H.value)==null?void 0:B.wrapRef;Math.abs(s.scrollHeight-s.clientHeight-s.scrollTop)<1&&(R.value=!1,w())},200),pe=async()=>{var s;const e=(s=x.value)==null?void 0:s.clientHeight;e&&(R.value=!0,l.pageNo+=1,await I(),H.value.setScrollTop(x.value.clientHeight-e))},me=C(()=>(e,s)=>t(d.value)[s+1]?V(t(d.value)[s+1].createTime).fromNow()!==V(t(e).createTime).fromNow():!1);return(e,s)=>{const B=Fe,te=aa,F=Ae("MessageItem"),fe=Je,ve=Le,ye=Ke,_e=Oe,he=Pe,ge=Ve,we=Xe,Te=Ye,xe=Ze,ke=Ue("dompurify-html");return t(j)?(o(),N(Te,{key:0,class:"kefu"},{default:f(()=>[i(B,null,{default:f(()=>[y("div",z,X(t(u).userNickname),1)]),_:1}),i(_e,{class:"kefu-content overflow-visible"},{default:f(()=>[i(ve,{ref_key:"scrollbarRef",ref:H,always:"",height:"calc(100vh - 495px)",onScroll:t(de)},{default:f(()=>[t(k)?(o(),A("div",{key:0,ref_key:"innerRef",ref:x,class:"w-[100%] pb-3px"},[(o(!0),A(je,null,Ce(t(U),(a,Me)=>(o(),A("div",{key:a.id,class:"w-[100%]"},[y("div",G,[a.contentType!==t(h).SYSTEM&&t(me)(a,Me)?(o(),A("div",W,X(t(ma)(a.createTime)),1)):_("",!0),a.contentType===t(h).SYSTEM?(o(),A("div",$,X(a.content),1)):_("",!0)]),y("div",{class:re([[a.senderType===t(L).MEMBER?"ss-row-left":a.senderType===t(L).ADMIN?"ss-row-right":""],"flex mb-20px w-[100%]"])},[a.senderType===t(L).MEMBER?(o(),N(te,{key:0,src:t(u).userAvatar,alt:"avatar",class:"w-60px h-60px"},null,8,["src"])):_("",!0),y("div",{class:re([{"kefu-message":t(h).TEXT===a.contentType},"p-10px"])},[i(F,{message:a},{default:f(()=>[t(h).TEXT===a.contentType?le((o(),A("div",Q,null,512)),[[ke,t(g)(a.content)]]):_("",!0)]),_:2},1032,["message"]),i(F,{message:a},{default:f(()=>[t(h).IMAGE===a.contentType?(o(),N(fe,{key:0,"initial-index":0,"preview-src-list":[a.content],src:a.content,class:"w-200px",fit:"contain","preview-teleported":""},null,8,["preview-src-list","src"])):_("",!0)]),_:2},1032,["message"]),i(F,{message:a},{default:f(()=>[t(h).PRODUCT===a.contentType?(o(),N(oa,{key:0,picUrl:t(M)(a).picUrl,price:t(M)(a).price,skuText:t(M)(a).introduction,title:t(M)(a).spuName,titleWidth:400,class:"max-w-70%",priceColor:"#FF3000"},null,8,["picUrl","price","skuText","title"])):_("",!0)]),_:2},1032,["message"]),i(F,{message:a},{default:f(()=>[t(h).ORDER===a.contentType?(o(),N(ca,{key:0,message:a,class:"max-w-70%"},null,8,["message"])):_("",!0)]),_:2},1032,["message"])],2),a.senderType===t(L).ADMIN?(o(),N(te,{key:1,src:a.senderAvatar,alt:"avatar"},null,8,["src"])):_("",!0)],2)]))),128))],512)):_("",!0)]),_:1},8,["onScroll"]),le(y("div",{class:"newMessageTip flex items-center cursor-pointer",onClick:ae},[Z,i(ye,{class:"ml-5px",icon:"ep:bottom"})],512),[[De,t(r)]])]),_:1}),i(we,{height:"230px"},{default:f(()=>[y("div",q,[y("div",J,[i(sa,{onSelectEmoji:E}),i(la,{class:"ml-15px mt-3px cursor-pointer",onSendPicture:m})]),i(he,{modelValue:t(c),"onUpdate:modelValue":s[0]||(s[0]=a=>He(c)?c.value=a:null),rows:6,style:{"border-style":"none"},type:"textarea"},null,8,["modelValue"]),y("div",ee,[i(ge,{class:"mt-10px",type:"primary",onClick:T},{default:f(()=>[Be("\u53D1\u9001")]),_:1})])])]),_:1})]),_:1})):(o(),N(xe,{key:1,description:"\u8BF7\u9009\u62E9\u5DE6\u4FA7\u7684\u4E00\u4E2A\u4F1A\u8BDD\u540E\u5F00\u59CB"}))}}}),[["__scopeId","data-v-9177901b"]])});export{va as __tla,ne as default};
