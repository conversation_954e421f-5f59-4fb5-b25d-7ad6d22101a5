import{d as b,b as y,r as V,C as v,o as l,l as r,w as f,c as h,k as w,a as o,F as x,y as g,J as k,K as G,__tla as M}from"./index-BUSn51wb.js";import{g as S,__tla as C}from"./index-D05VL_Mu.js";let m,F=Promise.all([(()=>{try{return M}catch{}})(),(()=>{try{return C}catch{}})()]).then(async()=>{m=b({name:"MemberGroupSelect",__name:"MemberGroupSelect",props:{modelValue:{type:Number,default:void 0}},emits:["update:modelValue"],setup(d,{emit:n}){const _=d,c=n,a=y({get:()=>_.modelValue,set(s){c("update:modelValue",s)}}),t=V([]);return v(()=>{(async()=>t.value=await S())()}),(s,u)=>{const p=k,i=G;return l(),r(i,{modelValue:o(a),"onUpdate:modelValue":u[0]||(u[0]=e=>g(a)?a.value=e:null),placeholder:"\u8BF7\u9009\u62E9\u7528\u6237\u5206\u7EC4",clearable:"",class:"!w-240px"},{default:f(()=>[(l(!0),h(x,null,w(o(t),e=>(l(),r(p,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])}}})});export{m as _,F as __tla};
