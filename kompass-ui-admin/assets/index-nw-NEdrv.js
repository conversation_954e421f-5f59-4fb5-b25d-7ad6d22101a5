import{by as e,__tla as r}from"./index-BUSn51wb.js";let t,l=Promise.all([(()=>{try{return r}catch{}})()]).then(async()=>{t={getTeacherPage:async a=>await e.get({url:"/als/teacher/page",params:a}),getBindTeacherPage:async a=>await e.get({url:"/als/teacher/bindPage",params:a}),queryByKeywords:async a=>await e.get({url:"/als/teacher/queryByKeywords",params:a}),getTeacher:async a=>await e.get({url:"/als/teacher/get?id="+a}),getDetail:async a=>await e.get({url:"/als/teacher/detail?id="+a}),createTeacher:async a=>await e.post({url:"/als/teacher/create",data:a}),updateTeacher:async a=>await e.put({url:"/als/teacher/update",data:a}),deleteTeacher:async a=>await e.delete({url:"/als/teacher/delete?id="+a}),exportTeacher:async a=>await e.download({url:"/als/teacher/export-excel",params:a}),getResume:async a=>await e.get({url:"/als/teacher/getResume?id="+a})}});export{t as T,l as __tla};
