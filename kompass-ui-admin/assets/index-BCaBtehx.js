import{_ as l,__tla as o}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as c,__tla as n}from"./IFrame.vue_vue_type_script_setup_true_lang-silYuU_p.js";import{_ as m,__tla as u}from"./index-COobLwz-.js";import{d as i,r as p,e9 as h,o as f,l as d,w as y,i as t,a as x,__tla as j}from"./index-BUSn51wb.js";import{__tla as k}from"./el-card-CJbXGyyg.js";let a,w=Promise.all([(()=>{try{return o}catch{}})(),(()=>{try{return n}catch{}})(),(()=>{try{return u}catch{}})(),(()=>{try{return j}catch{}})(),(()=>{try{return k}catch{}})()]).then(async()=>{a=i({name:"<PERSON><PERSON><PERSON>ep<PERSON>",__name:"index",setup(J){const r=p("http://**************:48080/jmreport/list?token="+h());return(P,R)=>{const _=m,s=c,e=l;return f(),d(e,null,{default:y(()=>[t(_,{title:"\u62A5\u8868\u8BBE\u8BA1\u5668",url:"https://doc.iocoder.cn/report/"}),t(s,{src:x(r)},null,8,["src"])]),_:1})}}})});export{w as __tla,a as default};
