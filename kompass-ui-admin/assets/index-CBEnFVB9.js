import{d as I,r,at as w,C as B,o as s,l as C,w as j,g as y,av as l,a,c as p,F as z,k as L,i as b,a9 as f,a0 as P,t as g,b2 as U,__tla as E}from"./index-BUSn51wb.js";import{E as F,__tla as G}from"./el-image-BjHZRFih.js";import{g as W,__tla as q}from"./spu-CW3JGweV.js";import{g as A,__tla as D}from"./combinationActivity-Jgh6nzIi.js";let R,H=Promise.all([(()=>{try{return E}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return q}catch{}})(),(()=>{try{return D}catch{}})()]).then(async()=>{let m;m={key:0,class:"absolute left-0 top-0 z-1 items-center justify-center"},R=I({name:"PromotionCombination",__name:"index",props:{property:{}},setup($){const o=$,i=r([]);w(()=>o.property.activityId,async()=>{if(!o.property.activityId)return;const t=await A(o.property.activityId);t!=null&&t.spuId&&(i.value=[await W(t.spuId)])},{immediate:!0,deep:!0});const c=r(375),v=r(),e=r(2),x=r("100%"),d=r("0"),_=r("");return w(()=>[o.property,c,i.value.length],()=>{e.value=o.property.layoutType==="oneCol"?1:3;const t=(c.value-o.property.space*(e.value-1))/e.value;d.value=e.value===2?"64px":`${t}px`,_.value=`repeat(${e.value}, auto)`,x.value="100%"},{immediate:!0,deep:!0}),B(()=>{var t,u;c.value=((u=(t=v.value)==null?void 0:t.wrapRef)==null?void 0:u.offsetWidth)||375}),(t,u)=>{const h=F,k=U;return s(),C(k,{class:"z-1 min-h-30px","wrap-class":"w-full",ref_key:"containerRef",ref:v},{default:j(()=>[y("div",{class:"grid overflow-x-auto",style:l({gridGap:`${t.property.space}px`,gridTemplateColumns:a(_),width:a(x)})},[(s(!0),p(z,null,L(a(i),(n,T)=>(s(),p("div",{class:"relative box-content flex flex-row flex-wrap overflow-hidden bg-white",style:l({borderTopLeftRadius:`${t.property.borderRadiusTop}px`,borderTopRightRadius:`${t.property.borderRadiusTop}px`,borderBottomLeftRadius:`${t.property.borderRadiusBottom}px`,borderBottomRightRadius:`${t.property.borderRadiusBottom}px`}),key:T},[t.property.badge.show?(s(),p("div",m,[b(h,{fit:"cover",src:t.property.badge.imgUrl,class:"h-26px w-38px"},null,8,["src"])])):f("",!0),b(h,{fit:"cover",src:n.picUrl,style:l({width:a(d),height:a(d)})},null,8,["src","style"]),y("div",{class:P(["flex flex-col gap-8px p-8px box-border",{"w-[calc(100%-64px)]":a(e)===2,"w-full":a(e)===3}])},[t.property.fields.name.show?(s(),p("div",{key:0,class:"truncate text-12px",style:l({color:t.property.fields.name.color})},g(n.name),5)):f("",!0),y("div",null,[t.property.fields.price.show?(s(),p("span",{key:0,class:"text-12px",style:l({color:t.property.fields.price.color})}," \uFFE5"+g(n.price),5)):f("",!0)])],2)],4))),128))],4)]),_:1},512)}}})});export{H as __tla,R as default};
