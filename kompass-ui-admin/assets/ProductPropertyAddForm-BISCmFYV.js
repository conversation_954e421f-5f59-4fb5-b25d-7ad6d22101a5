import{_ as t,__tla as _}from"./ProductPropertyAddForm.vue_vue_type_script_setup_true_lang-BR4HNBkA.js";import{__tla as a}from"./index-BUSn51wb.js";import{__tla as r}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as l}from"./property-BdOytbZT.js";let o=Promise.all([(()=>{try{return _}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return r}catch{}})(),(()=>{try{return l}catch{}})()]).then(async()=>{});export{o as __tla,t as default};
