import{d as z,dZ as B,n as W,I as X,r as i,f as Y,C as $,T as aa,o as n,c as g,i as a,w as e,a as t,U as ea,F as N,k as la,V as ta,G as Z,l as p,j as c,H as m,a9 as ra,ay as oa,d_ as E,Z as sa,L as na,J as ca,K as ua,_ as _a,N as ia,O as pa,P as ma,Q as da,R as fa,__tla as ya}from"./index-BUSn51wb.js";import{_ as ha,__tla as wa}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as va,__tla as ka}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as ba,__tla as Ca}from"./index-COobLwz-.js";import{h as xa}from"./tree-BMa075Oj.js";import{b as Sa,d as Oa,__tla as <PERSON>}from"./index-B77mwhR6.js";import{_ as Ua,__tla as Va}from"./MenuForm.vue_vue_type_script_setup_true_lang-CNPcyT6Z.js";import"./color-BN7ZL7BD.js";import{__tla as Ta}from"./el-card-CJbXGyyg.js";import{__tla as Ma}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as ga}from"./Tooltip.vue_vue_type_script_setup_true_lang-CBw08m0_.js";import{__tla as Na}from"./index-Cch5e1V0.js";import{__tla as Za}from"./el-tree-select-CBuha0HW.js";import"./constants-A8BI3pz7.js";let F,Ea=Promise.all([(()=>{try{return ya}catch{}})(),(()=>{try{return wa}catch{}})(),(()=>{try{return ka}catch{}})(),(()=>{try{return Ca}catch{}})(),(()=>{try{return Ra}catch{}})(),(()=>{try{return Va}catch{}})(),(()=>{try{return Ta}catch{}})(),(()=>{try{return Ma}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return Na}catch{}})(),(()=>{try{return Za}catch{}})()]).then(async()=>{F=z({name:"SystemMenu",__name:"index",setup(Fa){const{wsCache:O}=B(),{t:K}=W(),w=X(),v=i(!0),R=i([]),u=Y({name:void 0,status:void 0}),U=i(),k=i(!1),b=i(!0),d=async()=>{v.value=!0;try{const f=await Sa(u);R.value=xa(f)}finally{v.value=!1}},C=()=>{d()},P=()=>{U.value.resetFields(),C()},V=i(),x=(f,r,y)=>{V.value.open(f,r,y)},A=()=>{b.value=!1,k.value=!k.value,oa(()=>{b.value=!0})},L=async()=>{try{await w.confirm("\u5373\u5C06\u66F4\u65B0\u7F13\u5B58\u5237\u65B0\u6D4F\u89C8\u5668\uFF01","\u5237\u65B0\u83DC\u5355\u7F13\u5B58"),O.delete(E.USER),O.delete(E.ROLE_ROUTERS),location.reload()}catch{}};return $(()=>{d()}),(f,r)=>{const y=ba,j=sa,S=na,q=ca,D=ua,_=_a,o=ia,G=pa,T=va,s=ma,H=ha,I=da,h=aa("hasPermi"),J=fa;return n(),g(N,null,[a(y,{title:"\u529F\u80FD\u6743\u9650",url:"https://doc.iocoder.cn/resource-permission"}),a(y,{title:"\u83DC\u5355\u8DEF\u7531",url:"https://doc.iocoder.cn/vue3/route/"}),a(T,null,{default:e(()=>[a(G,{ref_key:"queryFormRef",ref:U,inline:!0,model:t(u),class:"-mb-15px","label-width":"68px"},{default:e(()=>[a(S,{label:"\u83DC\u5355\u540D\u79F0",prop:"name"},{default:e(()=>[a(j,{modelValue:t(u).name,"onUpdate:modelValue":r[0]||(r[0]=l=>t(u).name=l),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u83DC\u5355\u540D\u79F0",onKeyup:ea(C,["enter"])},null,8,["modelValue"])]),_:1}),a(S,{label:"\u72B6\u6001",prop:"status"},{default:e(()=>[a(D,{modelValue:t(u).status,"onUpdate:modelValue":r[1]||(r[1]=l=>t(u).status=l),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u83DC\u5355\u72B6\u6001"},{default:e(()=>[(n(!0),g(N,null,la(t(ta)(t(Z).COMMON_STATUS),l=>(n(),p(q,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(S,null,{default:e(()=>[a(o,{onClick:C},{default:e(()=>[a(_,{class:"mr-5px",icon:"ep:search"}),c(" \u641C\u7D22 ")]),_:1}),a(o,{onClick:P},{default:e(()=>[a(_,{class:"mr-5px",icon:"ep:refresh"}),c(" \u91CD\u7F6E ")]),_:1}),m((n(),p(o,{plain:"",type:"primary",onClick:r[2]||(r[2]=l=>x("create"))},{default:e(()=>[a(_,{class:"mr-5px",icon:"ep:plus"}),c(" \u65B0\u589E ")]),_:1})),[[h,["system:menu:create"]]]),a(o,{plain:"",type:"danger",onClick:A},{default:e(()=>[a(_,{class:"mr-5px",icon:"ep:sort"}),c(" \u5C55\u5F00/\u6298\u53E0 ")]),_:1}),a(o,{plain:"",onClick:L},{default:e(()=>[a(_,{class:"mr-5px",icon:"ep:refresh"}),c(" \u5237\u65B0\u83DC\u5355\u7F13\u5B58 ")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),a(T,null,{default:e(()=>[t(b)?m((n(),p(I,{key:0,data:t(R),"default-expand-all":t(k),"row-key":"id"},{default:e(()=>[a(s,{"show-overflow-tooltip":!0,label:"\u83DC\u5355\u540D\u79F0",prop:"name",width:"250"}),a(s,{align:"center",label:"\u56FE\u6807",prop:"icon",width:"100"},{default:e(l=>[a(_,{icon:l.row.icon},null,8,["icon"])]),_:1}),a(s,{label:"\u6392\u5E8F",prop:"sort",width:"60"}),a(s,{"show-overflow-tooltip":!0,label:"\u6743\u9650\u6807\u8BC6",prop:"permission"}),a(s,{"show-overflow-tooltip":!0,label:"\u7EC4\u4EF6\u8DEF\u5F84",prop:"component"}),a(s,{"show-overflow-tooltip":!0,label:"\u7EC4\u4EF6\u540D\u79F0",prop:"componentName"}),a(s,{label:"\u72B6\u6001",prop:"status",width:"80"},{default:e(l=>[a(H,{type:t(Z).COMMON_STATUS,value:l.row.status},null,8,["type","value"])]),_:1}),a(s,{align:"center",label:"\u64CD\u4F5C"},{default:e(l=>[m((n(),p(o,{link:"",type:"primary",onClick:M=>x("update",l.row.id)},{default:e(()=>[c(" \u4FEE\u6539 ")]),_:2},1032,["onClick"])),[[h,["system:menu:update"]]]),m((n(),p(o,{link:"",type:"primary",onClick:M=>x("create",void 0,l.row.id)},{default:e(()=>[c(" \u65B0\u589E ")]),_:2},1032,["onClick"])),[[h,["system:menu:create"]]]),m((n(),p(o,{link:"",type:"danger",onClick:M=>(async Q=>{try{await w.delConfirm(),await Oa(Q),w.success(K("common.delSuccess")),await d()}catch{}})(l.row.id)},{default:e(()=>[c(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[h,["system:menu:delete"]]])]),_:1})]),_:1},8,["data","default-expand-all"])),[[J,t(v)]]):ra("",!0)]),_:1}),a(Ua,{ref_key:"formRef",ref:V,onSuccess:d},null,512)],64)}}})});export{Ea as __tla,F as default};
