import{_ as D,__tla as I}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{d as k,r as M,o as n,l as s,w as e,i as a,j as t,t as r,a as c,dX as i,G as N,y as R,g as y,__tla as x}from"./index-BUSn51wb.js";import{E as V,a as U,__tla as j}from"./el-collapse-item-B_QvnH_b.js";import{E as w,a as C,__tla as g}from"./el-descriptions-item-dD3qa0ub.js";import{E as A,__tla as B}from"./el-text-CIwNlU-U.js";import{_ as G,__tla as L}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{f,__tla as X}from"./formatTime-DWdBpgsM.js";let E,q=Promise.all([(()=>{try{return I}catch{}})(),(()=>{try{return x}catch{}})(),(()=>{try{return j}catch{}})(),(()=>{try{return g}catch{}})(),(()=>{try{return B}catch{}})(),(()=>{try{return L}catch{}})(),(()=>{try{return X}catch{}})()]).then(async()=>{let d,m;d=y("span",{class:"text-base font-bold"},"\u57FA\u672C\u4FE1\u606F",-1),m=y("span",{class:"text-base font-bold"},"\u7CFB\u7EDF\u4FE1\u606F",-1),E=k({__name:"ReceivablePlanDetailsInfo",props:{receivablePlan:{}},setup(z){const o=M(["basicInfo","systemInfo"]);return(l,v)=>{const _=w,Y=G,u=A,p=C,P=V,h=U,T=D;return n(),s(T,null,{default:e(()=>[a(h,{modelValue:c(o),"onUpdate:modelValue":v[0]||(v[0]=b=>R(o)?o.value=b:null)},{default:e(()=>[a(P,{name:"basicInfo"},{title:e(()=>[d]),default:e(()=>[a(p,{column:4},{default:e(()=>[a(_,{label:"\u671F\u6570"},{default:e(()=>[t(r(l.receivablePlan.period),1)]),_:1}),a(_,{label:"\u5BA2\u6237\u540D\u79F0"},{default:e(()=>[t(r(l.receivablePlan.customerName),1)]),_:1}),a(_,{label:"\u5408\u540C\u7F16\u53F7"},{default:e(()=>[t(r(l.receivablePlan.contractNo),1)]),_:1}),a(_,{label:"\u8BA1\u5212\u56DE\u6B3E\u91D1\u989D"},{default:e(()=>[t(r(c(i)(l.receivablePlan.price)),1)]),_:1}),a(_,{label:"\u8BA1\u5212\u56DE\u6B3E\u65E5\u671F"},{default:e(()=>[t(r(c(f)(l.receivablePlan.returnTime,"YYYY-MM-DD")),1)]),_:1}),a(_,{label:"\u8BA1\u5212\u56DE\u6B3E\u65B9\u5F0F"},{default:e(()=>[a(Y,{type:c(N).CRM_RECEIVABLE_RETURN_TYPE,value:l.receivablePlan.returnType},null,8,["type","value"])]),_:1}),a(_,{label:"\u63D0\u524D\u51E0\u5929\u63D0\u9192"},{default:e(()=>[t(r(l.receivablePlan.remindDays),1)]),_:1}),a(_,{label:"\u5907\u6CE8"},{default:e(()=>[t(r(l.receivablePlan.remark),1)]),_:1}),a(_,{label:"\u5B9E\u9645\u56DE\u6B3E\u91D1\u989D"},{default:e(()=>[l.receivablePlan.receivable?(n(),s(u,{key:0},{default:e(()=>[t(r(c(i)(l.receivablePlan.receivable.price)),1)]),_:1})):(n(),s(u,{key:1},{default:e(()=>[t(r(c(i)(0)),1)]),_:1}))]),_:1}),a(_,{label:"\u672A\u56DE\u6B3E\u91D1\u989D"},{default:e(()=>[l.receivablePlan.receivable?(n(),s(u,{key:0},{default:e(()=>[t(r(c(i)(l.receivablePlan.price-l.receivablePlan.receivable.price)),1)]),_:1})):(n(),s(u,{key:1},{default:e(()=>[t(r(c(i)(l.receivablePlan.price)),1)]),_:1}))]),_:1}),a(_,{label:"\u5B9E\u9645\u56DE\u6B3E\u65E5\u671F"},{default:e(()=>{var b;return[t(r(c(f)((b=l.receivablePlan.receivable)==null?void 0:b.returnTime,"YYYY-MM-DD")),1)]}),_:1})]),_:1})]),_:1}),a(P,{name:"systemInfo"},{title:e(()=>[m]),default:e(()=>[a(p,{column:4},{default:e(()=>[a(_,{label:"\u8D1F\u8D23\u4EBA"},{default:e(()=>[t(r(l.receivablePlan.ownerUserName),1)]),_:1}),a(_,{label:"\u521B\u5EFA\u4EBA"},{default:e(()=>[t(r(l.receivablePlan.creatorName),1)]),_:1}),a(_,{label:"\u521B\u5EFA\u65F6\u95F4"},{default:e(()=>[t(r(c(f)(l.receivablePlan.createTime)),1)]),_:1}),a(_,{label:"\u66F4\u65B0\u65F6\u95F4"},{default:e(()=>[t(r(c(f)(l.receivablePlan.updateTime)),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})}}})});export{E as _,q as __tla};
