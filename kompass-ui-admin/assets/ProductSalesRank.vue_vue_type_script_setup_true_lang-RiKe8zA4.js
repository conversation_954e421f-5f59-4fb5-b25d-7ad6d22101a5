import{d as w,r as c,f as b,C as v,o as d,c as P,i as a,w as r,a as n,H as k,l as A,F as R,em as S,P as q,Q as E,R as I,__tla as j}from"./index-BUSn51wb.js";import{E as C,__tla as D}from"./el-card-CJbXGyyg.js";import{E as F,__tla as H}from"./el-skeleton-item-tDN8U6BH.js";import{_ as L,__tla as N}from"./Echart.vue_vue_type_script_setup_true_lang-DnLZLH3w.js";import{S as Q,__tla as X}from"./rank-CaJ4xEN0.js";let m,Z=Promise.all([(()=>{try{return j}catch{}})(),(()=>{try{return D}catch{}})(),(()=>{try{return H}catch{}})(),(()=>{try{return N}catch{}})(),(()=>{try{return X}catch{}})()]).then(async()=>{m=w({name:"ProductSalesRank",__name:"ProductSalesRank",props:{queryParams:{}},setup(u,{expose:p}){const h=u,t=c(!1),o=c([]),e=b({dataset:{dimensions:["nickname","count"],source:[]},grid:{left:20,right:20,bottom:20,containLabel:!0},legend:{top:50},series:[{name:"\u4EA7\u54C1\u9500\u91CF\u6392\u884C",type:"bar"}],toolbox:{feature:{dataZoom:{yAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u4EA7\u54C1\u9500\u91CF\u6392\u884C"}}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},xAxis:{type:"value",name:"\u4EA7\u54C1\u9500\u91CF"},yAxis:{type:"category",name:"\u5458\u5DE5"}}),i=async()=>{t.value=!0;const l=await Q.getProductSalesRank(h.queryParams);e.dataset&&e.dataset.source&&(e.dataset.source=S(l).reverse()),o.value=l,t.value=!1};return p({loadData:i}),v(()=>{i()}),(l,z)=>{const y=L,g=F,_=C,s=q,f=E,x=I;return d(),P(R,null,[a(_,{shadow:"never"},{default:r(()=>[a(g,{loading:n(t),animated:""},{default:r(()=>[a(y,{height:500,options:n(e)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),a(_,{shadow:"never",class:"mt-16px"},{default:r(()=>[k((d(),A(f,{data:n(o)},{default:r(()=>[a(s,{label:"\u516C\u53F8\u6392\u540D",align:"center",type:"index",width:"80"}),a(s,{label:"\u5458\u5DE5",align:"center",prop:"nickname","min-width":"200"}),a(s,{label:"\u90E8\u95E8",align:"center",prop:"deptName","min-width":"200"}),a(s,{label:"\u4EA7\u54C1\u9500\u91CF",align:"center",prop:"count","min-width":"200"})]),_:1},8,["data"])),[[x,n(t)]])]),_:1})],64)}}})});export{m as _,Z as __tla};
