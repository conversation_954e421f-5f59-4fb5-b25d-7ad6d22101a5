import{by as y,d as b,r as l,C as f,H as h,a as t,o as g,l as x,w as n,i as a,G as v,P as I,Q as S,R as A,__tla as N}from"./index-BUSn51wb.js";import{_ as T,__tla as C}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{d as M,__tla as O}from"./formatTime-DWdBpgsM.js";let i,P=Promise.all([(()=>{try{return N}catch{}})(),(()=>{try{return C}catch{}})(),(()=>{try{return O}catch{}})()]).then(async()=>{i=b({__name:"UserAddressList",props:{userId:{type:Number,required:!0}},setup(o){const{userId:d}=o,r=l(!0);l(0);const s=l([]),u=async()=>{r.value=!0;try{s.value=await(async p=>await y.get({url:"/member/address/list",params:p}))({userId:d})}finally{r.value=!1}};return f(()=>{u()}),(p,U)=>{const e=I,_=T,c=S,m=A;return h((g(),x(c,{data:t(s),stripe:!0,"show-overflow-tooltip":!0},{default:n(()=>[a(e,{label:"\u5730\u5740\u7F16\u53F7",align:"center",prop:"id",width:"150px"}),a(e,{label:"\u6536\u4EF6\u4EBA\u540D\u79F0",align:"center",prop:"name",width:"150px"}),a(e,{label:"\u624B\u673A\u53F7",align:"center",prop:"mobile",width:"150px"}),a(e,{label:"\u5730\u533A\u7F16\u7801",align:"center",prop:"areaId",width:"150px"}),a(e,{label:"\u6536\u4EF6\u8BE6\u7EC6\u5730\u5740",align:"center",prop:"detailAddress"}),a(e,{label:"\u662F\u5426\u9ED8\u8BA4",align:"center",prop:"defaultStatus",width:"150px"},{default:n(w=>[a(_,{type:t(v).COMMON_STATUS,value:Number(w.row.defaultStatus)},null,8,["type","value"])]),_:1}),a(e,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:t(M),width:"180px"},null,8,["formatter"])]),_:1},8,["data"])),[[m,t(r)]])}}})});export{i as _,P as __tla};
