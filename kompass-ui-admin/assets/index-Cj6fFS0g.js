import{by as t,__tla as u}from"./index-BUSn51wb.js";let e,s=Promise.all([(()=>{try{return u}catch{}})()]).then(async()=>{e={getSaleOutPage:async a=>await t.get({url:"/erp/sale-out/page",params:a}),getSaleOut:async a=>await t.get({url:"/erp/sale-out/get?id="+a}),createSaleOut:async a=>await t.post({url:"/erp/sale-out/create",data:a}),updateSaleOut:async a=>await t.put({url:"/erp/sale-out/update",data:a}),updateSaleOutStatus:async(a,l)=>await t.put({url:"/erp/sale-out/update-status",params:{id:a,status:l}}),deleteSaleOut:async a=>await t.delete({url:"/erp/sale-out/delete",params:{ids:a.join(",")}}),exportSaleOut:async a=>await t.download({url:"/erp/sale-out/export-excel",params:a})}});export{e as S,s as __tla};
