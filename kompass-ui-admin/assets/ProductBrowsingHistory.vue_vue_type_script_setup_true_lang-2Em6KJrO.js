import{by as d,d as m,r as c,f as y,b as g,o as u,c as v,k as h,l as x,a as f,F as w,__tla as b}from"./index-BUSn51wb.js";import k,{__tla as N}from"./ProductItem-bFAWKK8H.js";import{c as F,__tla as H}from"./concat-MbtHYl7y.js";let p,I=Promise.all([(()=>{try{return b}catch{}})(),(()=>{try{return N}catch{}})(),(()=>{try{return H}catch{}})()]).then(async()=>{let s;s=o=>d.get({url:"/product/browse-history/page",params:o}),p=m({name:"ProductBrowsingHistory",__name:"ProductBrowsingHistory",setup(o,{expose:n}){const l=c([]),r=c(0),t=y({pageNo:1,pageSize:10,userId:0,userDeleted:!1}),_=g(()=>r.value>0&&Math.ceil(r.value/t.pageSize)===t.pageNo);return n({getHistoryList:async a=>{t.userId=a.userId;const i=await s(t);r.value=i.total,l.value=i.list},loadMore:async()=>{if(_.value)return;t.pageNo+=1;const a=await s(t);r.value=a.total,F(l.value,a.list)}}),(a,i)=>(u(!0),v(w,null,h(f(l),e=>(u(),x(k,{key:e.id,picUrl:e.picUrl,price:e.price,skuText:e.introduction,title:e.spuName,titleWidth:400,class:"mb-10px",priceColor:"#FF3000"},null,8,["picUrl","price","skuText","title"]))),128))}})});export{p as _,I as __tla};
