import{by as a,__tla as l}from"./index-BUSn51wb.js";let t,e,r,s,y,c=Promise.all([(()=>{try{return l}catch{}})()]).then(async()=>{y=async()=>await a.get({url:"/system/area/tree"}),s=async()=>await a.get({url:"/system/area/treeCity"}),t=async()=>await a.get({url:"/system/area/orderCity"}),e=async()=>await a.get({url:"/system/area/orderCityArea"}),r=async i=>await a.get({url:"/system/area/get-by-ip?ip="+i})});export{c as __tla,t as a,e as b,r as c,s as d,y as g};
