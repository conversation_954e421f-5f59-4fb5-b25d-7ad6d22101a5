import{d as h,p as s,o as d,l as i,c9 as b,w as a,a0 as w,a as v,a4 as k,g as t,t as p,i as C,a9 as j,aV as _,_ as P,aN as S,__tla as W}from"./index-BUSn51wb.js";import{E as $,__tla as q}from"./el-card-CJbXGyyg.js";let f,z=Promise.all([(()=>{try{return W}catch{}})(),(()=>{try{return q}catch{}})()]).then(async()=>{let l,r,n,o;l={class:"flex items-center"},r={class:"text-16px font-700"},n={class:"max-w-200px"},o={class:"flex flex-grow pl-20px"},f=h({name:"ContentWrap",__name:"ContentWrap",props:{title:s.string.def(""),message:s.string.def(""),bodyStyle:s.object.def({padding:"20px"})},setup(e){const{getPrefixCls:m}=k(),x=m("content-wrap");return(c,E)=>{const y=P,g=S,u=$;return d(),i(u,{"body-style":e.bodyStyle,class:w([v(x),"mb-15px"]),shadow:"never"},b({default:a(()=>[_(c.$slots,"default")]),_:2},[e.title?{name:"header",fn:a(()=>[t("div",l,[t("span",r,p(e.title),1),e.message?(d(),i(g,{key:0,effect:"dark",placement:"right"},{content:a(()=>[t("div",n,p(e.message),1)]),default:a(()=>[C(y,{size:14,class:"ml-5px",icon:"ep:question-filled"})]),_:1})):j("",!0),t("div",o,[_(c.$slots,"header")])])]),key:"0"}:void 0]),1032,["body-style","class"])}}})});export{f as _,z as __tla};
