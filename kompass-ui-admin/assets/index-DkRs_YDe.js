import{d as I,r,at as w,C as B,o as l,l as C,w as j,g as n,av as s,a,c as p,F as z,k as L,i as b,a9 as f,a0 as P,t as g,b2 as U,__tla as E}from"./index-BUSn51wb.js";import{E as F,__tla as G}from"./el-image-BjHZRFih.js";import{g as S,__tla as W}from"./spu-CW3JGweV.js";import{g as q,__tla as A}from"./seckillActivity-BKWzpRsU.js";let R,D=Promise.all([(()=>{try{return E}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return W}catch{}})(),(()=>{try{return A}catch{}})()]).then(async()=>{let m;m={key:0,class:"absolute left-0 top-0 z-1 items-center justify-center"},R=I({name:"Promotion<PERSON><PERSON>ill",__name:"index",props:{property:{}},setup(k){const o=k,i=r([]);w(()=>o.property.activityId,async()=>{if(!o.property.activityId)return;const e=await q(o.property.activityId);e!=null&&e.spuId&&(i.value=[await S(e.spuId)])},{immediate:!0,deep:!0});const c=r(375),v=r(),t=r(2),x=r("100%"),d=r("0"),_=r("");return w(()=>[o.property,c,i.value.length],()=>{t.value=o.property.layoutType==="oneCol"?1:3;const e=(c.value-o.property.space*(t.value-1))/t.value;d.value=t.value===2?"64px":`${e}px`,_.value=`repeat(${t.value}, auto)`,x.value="100%"},{immediate:!0,deep:!0}),B(()=>{var e,u;c.value=((u=(e=v.value)==null?void 0:e.wrapRef)==null?void 0:u.offsetWidth)||375}),(e,u)=>{const h=F,$=U;return l(),C($,{class:"z-1 min-h-30px","wrap-class":"w-full",ref_key:"containerRef",ref:v},{default:j(()=>[n("div",{class:"grid overflow-x-auto",style:s({gridGap:`${e.property.space}px`,gridTemplateColumns:a(_),width:a(x)})},[(l(!0),p(z,null,L(a(i),(y,T)=>(l(),p("div",{class:"relative box-content flex flex-row flex-wrap overflow-hidden bg-white",style:s({borderTopLeftRadius:`${e.property.borderRadiusTop}px`,borderTopRightRadius:`${e.property.borderRadiusTop}px`,borderBottomLeftRadius:`${e.property.borderRadiusBottom}px`,borderBottomRightRadius:`${e.property.borderRadiusBottom}px`}),key:T},[e.property.badge.show?(l(),p("div",m,[b(h,{fit:"cover",src:e.property.badge.imgUrl,class:"h-26px w-38px"},null,8,["src"])])):f("",!0),b(h,{fit:"cover",src:y.picUrl,style:s({width:a(d),height:a(d)})},null,8,["src","style"]),n("div",{class:P(["flex flex-col gap-8px p-8px box-border",{"w-[calc(100%-64px)]":a(t)===2,"w-full":a(t)===3}])},[e.property.fields.name.show?(l(),p("div",{key:0,class:"truncate text-12px",style:s({color:e.property.fields.name.color})},g(y.name),5)):f("",!0),n("div",null,[e.property.fields.price.show?(l(),p("span",{key:0,class:"text-12px",style:s({color:e.property.fields.price.color})}," \uFFE5"+g(y.price),5)):f("",!0)])],2)],4))),128))],4)]),_:1},512)}}})});export{D as __tla,R as default};
