import{d as V,f as v,o as r,c as d,i as l,w as o,a as s,F as f,k as h,Z as x,ce as b,J as w,K as y,__tla as z}from"./index-BUSn51wb.js";import{_ as u,__tla as U}from"./index.vue_vue_type_script_setup_true_lang-CIHu5un_.js";let n,k=Promise.all([(()=>{try{return z}catch{}})(),(()=>{try{return U}catch{}})()]).then(async()=>{n=V({name:"Desc",__name:"desc",setup(D,{expose:m}){const a=v({desc:"",pure:!1,version:"3"});return m({formData:a}),(R,t)=>{const _=x,c=b,i=w,p=y;return r(),d("div",null,[l(u,{title:"\u97F3\u4E50/\u6B4C\u8BCD\u8BF4\u660E",desc:"\u63CF\u8FF0\u60A8\u60F3\u8981\u7684\u97F3\u4E50\u98CE\u683C\u548C\u4E3B\u9898\uFF0C\u4F7F\u7528\u6D41\u6D3E\u548C\u6C1B\u56F4\u800C\u4E0D\u662F\u7279\u5B9A\u7684\u827A\u672F\u5BB6\u548C\u6B4C\u66F2"},{default:o(()=>[l(_,{modelValue:s(a).desc,"onUpdate:modelValue":t[0]||(t[0]=e=>s(a).desc=e),autosize:{minRows:6,maxRows:6},resize:"none",type:"textarea",maxlength:"1200","show-word-limit":"",placeholder:"\u4E00\u9996\u5173\u4E8E\u7CDF\u7CD5\u5206\u624B\u7684\u6B22\u5FEB\u6B4C\u66F2"},null,8,["modelValue"])]),_:1}),l(u,{title:"\u7EAF\u97F3\u4E50",desc:"\u521B\u5EFA\u4E00\u9996\u6CA1\u6709\u6B4C\u8BCD\u7684\u6B4C\u66F2"},{extra:o(()=>[l(c,{modelValue:s(a).pure,"onUpdate:modelValue":t[1]||(t[1]=e=>s(a).pure=e),size:"small"},null,8,["modelValue"])]),_:1}),l(u,{title:"\u7248\u672C",desc:"\u63CF\u8FF0\u60A8\u60F3\u8981\u7684\u97F3\u4E50\u98CE\u683C\u548C\u4E3B\u9898\uFF0C\u4F7F\u7528\u6D41\u6D3E\u548C\u6C1B\u56F4\u800C\u4E0D\u662F\u7279\u5B9A\u7684\u827A\u672F\u5BB6\u548C\u6B4C\u66F2"},{default:o(()=>[l(p,{modelValue:s(a).version,"onUpdate:modelValue":t[2]||(t[2]=e=>s(a).version=e),placeholder:"\u8BF7\u9009\u62E9"},{default:o(()=>[(r(),d(f,null,h([{value:"3",label:"V3"},{value:"2",label:"V2"}],e=>l(i,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})])}}})});export{n as _,k as __tla};
