import{_ as t,__tla as a}from"./DictDataForm.vue_vue_type_script_setup_true_lang-C98247dM.js";import{__tla as r}from"./index-BUSn51wb.js";import{__tla as _}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import"./constants-A8BI3pz7.js";let l=Promise.all([(()=>{try{return a}catch{}})(),(()=>{try{return r}catch{}})(),(()=>{try{return _}catch{}})()]).then(async()=>{});export{l as __tla,t as default};
