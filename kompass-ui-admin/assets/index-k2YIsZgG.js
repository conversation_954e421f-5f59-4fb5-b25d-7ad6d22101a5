import{B as oo,r as O,o as s,c as r,g as i,i as P,w as Me,a as C,y as Ae,ap as no,a5 as io,a6 as to,aq as lo,ar as so,as as ao,b as L,C as ro,at as $,au as co,T as po,av as fo,t as w,F as U,H as Be,aw as Re,a9 as h,j as go,a0 as We,k as Co,l as Ee,d as uo,__tla as No}from"./index-BUSn51wb.js";let qe,ho=Promise.all([(()=>{try{return No}catch{}})()]).then(async()=>{let m,W,E,q,z,H,G,K,x,_,j,F,J,Q,X,Y,Z,ee,oe,ne,ie,de,te,le,se,ae,re,ce,pe,fe,ge,Ce,ue,Ne,he,ve,ye,me,ke,be,we,<PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,xe,_e,<PERSON>,<PERSON>,<PERSON>,<PERSON>e;m=e=>(io("data-v-28152ded"),e=e(),to(),e),W={class:"add-node-btn-box"},E={class:"add-node-btn"},q={class:"add-node-popover-body"},z=[m(()=>i("div",{class:"item-wrapper"},[i("span",{class:"iconfont"},"\uE8EF")],-1)),m(()=>i("p",null,"\u5BA1\u6279\u4EBA",-1))],H=[m(()=>i("div",{class:"item-wrapper"},[i("span",{class:"iconfont"},"\uE93B")],-1)),m(()=>i("p",null,"\u6284\u9001\u4EBA",-1))],G=[m(()=>i("div",{class:"item-wrapper"},[i("span",{class:"iconfont"},"\uE9BE")],-1)),m(()=>i("p",null,"\u6761\u4EF6\u5206\u652F",-1))],K=m(()=>i("button",{class:"btn",type:"button"},[i("span",{class:"iconfont"},"\uE95B")],-1)),x=oo({__name:"addNode",props:{childNodeP:{type:Object,default:()=>({})}},emits:["update:childNodeP"],setup(e,{emit:c}){let a=e,o=c,l=O(!1);const u=v=>{var p;l.value=!1,v!=4?(v==1?p={nodeName:"\u5BA1\u6838\u4EBA",error:!0,type:1,settype:1,selectMode:0,selectRange:0,directorLevel:1,examineMode:1,noHanderAction:1,examineEndDirectorLevel:0,childNode:a.childNodeP,nodeUserList:[]}:v==2&&(p={nodeName:"\u6284\u9001\u4EBA",type:2,ccSelfSelectFlag:1,childNode:a.childNodeP,nodeUserList:[]}),o("update:childNodeP",p)):o("update:childNodeP",{nodeName:"\u8DEF\u7531",type:4,childNode:null,conditionNodes:[{nodeName:"\u6761\u4EF61",error:!0,type:3,priorityLevel:1,conditionList:[],nodeUserList:[],childNode:a.childNodeP},{nodeName:"\u6761\u4EF62",type:3,priorityLevel:2,conditionList:[],nodeUserList:[],childNode:null}]})};return(v,p)=>{const k=no;return s(),r("div",W,[i("div",E,[P(k,{placement:"right-start",modelValue:C(l),"onUpdate:modelValue":p[3]||(p[3]=f=>Ae(l)?l.value=f:l=f),width:"auto"},{reference:Me(()=>[K]),default:Me(()=>[i("div",q,[i("a",{class:"add-node-popover-item approver",onClick:p[0]||(p[0]=f=>u(1))},z),i("a",{class:"add-node-popover-item notifier",onClick:p[1]||(p[1]=f=>u(2))},H),i("a",{class:"add-node-popover-item condition",onClick:p[2]||(p[2]=f=>u(4))},G)])]),_:1},8,["modelValue"])])])}}},[["__scopeId","data-v-28152ded"]]),_=e=>{if(e)return e.map(c=>c.name).toString()},j=e=>{if(e.settype==1){if(e.nodeUserList.length==1)return e.nodeUserList[0].name;if(e.nodeUserList.length>1){if(e.examineMode==1)return _(e.nodeUserList);if(e.examineMode==2)return e.nodeUserList.length+"\u4EBA\u4F1A\u7B7E"}}else if(e.settype==2){const c=e.directorLevel==1?"\u76F4\u63A5\u4E3B\u7BA1":"\u7B2C"+e.directorLevel+"\u7EA7\u4E3B\u7BA1";if(e.examineMode==1)return c;if(e.examineMode==2)return c+"\u4F1A\u7B7E"}else{if(e.settype==4)return e.selectRange==1?"\u53D1\u8D77\u4EBA\u81EA\u9009":e.nodeUserList.length>0?e.selectRange==2?"\u53D1\u8D77\u4EBA\u81EA\u9009":"\u53D1\u8D77\u4EBA\u4ECE"+e.nodeUserList[0].name+"\u4E2D\u81EA\u9009":"";if(e.settype==5)return"\u53D1\u8D77\u4EBA\u81EA\u5DF1";if(e.settype==7)return"\u4ECE\u76F4\u63A5\u4E3B\u7BA1\u5230\u901A\u8BAF\u5F55\u4E2D\u7EA7\u522B\u6700\u9AD8\u7684\u7B2C"+e.examineEndDirectorLevel+"\u4E2A\u5C42\u7EA7\u4E3B\u7BA1"}},F=e=>e.nodeUserList.length!=0?_(e.nodeUserList):e.ccSelfSelectFlag==1?"\u53D1\u8D77\u4EBA\u81EA\u9009":void 0,J=(e,c)=>{const{conditionList:a,nodeUserList:o}=e.conditionNodes[c];if(a.length==0)return c==e.conditionNodes.length-1&&e.conditionNodes[0].conditionList.length!=0?"\u5176\u4ED6\u6761\u4EF6\u8FDB\u5165\u6B64\u6D41\u7A0B":"\u8BF7\u8BBE\u7F6E\u6761\u4EF6";{let l="";for(let u=0;u<a.length;u++){const{columnId:v,columnType:p,showType:k,showName:f,optType:y,zdy1:b,opt1:V,zdy2:S,opt2:I,fixedDownBoxValue:M}=a[u];v==0&&o.length!=0&&(l+="\u53D1\u8D77\u4EBA\u5C5E\u4E8E\uFF1A",l+=o.map(A=>A.name).join("\u6216")+" \u5E76\u4E14 "),p=="String"&&k=="3"&&b&&(l+=f+"\u5C5E\u4E8E\uFF1A"+Q(b,JSON.parse(M))+" \u5E76\u4E14 "),p=="Double"&&(y!=6&&b?l+=`${f} ${["","<",">","\u2264","=","\u2265"][y]} ${b} \u5E76\u4E14 `:y==6&&b&&S&&(l+=`${b} ${V} ${f} ${I} ${S} \u5E76\u4E14 `))}return l?l.substring(0,l.length-4):"\u8BF7\u8BBE\u7F6E\u6761\u4EF6"}},Q=(e,c)=>{const a=[],o=e.split(",");for(const l in c)o.map(u=>{u==l&&a.push(c[l].value)});return a.join("\u6216")},X=["87, 106, 149","255, 148, 62","50, 150, 250"],Y=["\u53D1\u8D77\u4EBA","\u5BA1\u6838\u4EBA","\u6284\u9001\u4EBA"],Z=lo("simpleWorkflow",{state:()=>({tableId:"",isTried:!1,promoterDrawer:!1,flowPermission1:{},approverDrawer:!1,approverConfig1:{},copyerDrawer:!1,copyerConfig1:{},conditionDrawer:!1,conditionsConfig1:{conditionNodes:[]}}),actions:{setTableId(e){this.tableId=e},setIsTried(e){this.isTried=e},setPromoter(e){this.promoterDrawer=e},setFlowPermission(e){this.flowPermission1=e},setApprover(e){this.approverDrawer=e},setApproverConfig(e){this.approverConfig1=e},setCopyer(e){this.copyerDrawer=e},setCopyerConfig(e){this.copyerConfig1=e},setCondition(e){this.conditionDrawer=e},setConditionsConfig(e){this.conditionsConfig1=e}}}),ee=()=>Z(so),oe={key:0,class:"node-wrap"},ne={key:0},ie={class:"iconfont"},de=["placeholder"],te={class:"text"},le={key:0,class:"placeholder"},se=i("i",{class:"anticon anticon-right arrow"},null,-1),ae={key:0,class:"error_tip"},re=[i("i",{class:"anticon anticon-exclamation-circle"},null,-1)],ce={key:1,class:"branch-wrap"},pe={class:"branch-box-wrap"},fe={class:"branch-box"},ge={class:"condition-node"},Ce={class:"condition-node-box"},ue=["onClick"],Ne={class:"title-wrapper"},he=["onBlur","onUpdate:modelValue"],ve=["onClick"],ye=["onClick"],me=["onClick"],ke=["onClick"],be=["onClick"],we={key:2,class:"error_tip"},Le=[i("i",{class:"anticon anticon-exclamation-circle"},null,-1)],Pe=i("div",{class:"top-left-cover-line"},null,-1),Ue=i("div",{class:"bottom-left-cover-line"},null,-1),xe=i("div",{class:"top-right-cover-line"},null,-1),_e=i("div",{class:"bottom-right-cover-line"},null,-1),Se={__name:"nodeWrap",props:{nodeConfig:{type:Object,default:()=>({})},flowPermission:{type:Object,default:()=>[]}},emits:["update:flowPermission","update:nodeConfig"],setup(e,{emit:c}){let a=ao().uid,o=e,l=L(()=>Y[o.nodeConfig.type]),u=L(()=>o.nodeConfig.type==0?_(o.flowPermission)||"\u6240\u6709\u4EBA":o.nodeConfig.type==1?j(o.nodeConfig):F(o.nodeConfig)),v=O([]),p=O(!1);const k=()=>{for(var n=0;n<o.nodeConfig.conditionNodes.length;n++)o.nodeConfig.conditionNodes[n].error=J(o.nodeConfig,n)=="\u8BF7\u8BBE\u7F6E\u6761\u4EF6"&&n!=o.nodeConfig.conditionNodes.length-1};ro(()=>{o.nodeConfig.type==1?o.nodeConfig.error=!j(o.nodeConfig):o.nodeConfig.type==2?o.nodeConfig.error=!F(o.nodeConfig):o.nodeConfig.type==4&&k()});let f=c,y=ee(),{setPromoter:b,setApprover:V,setCopyer:S,setCondition:I,setFlowPermission:M,setApproverConfig:A,setCopyerConfig:ze,setConditionsConfig:He}=y,D=L(()=>y.isTried),Ge=L(()=>y.flowPermission1),Ke=L(()=>y.approverConfig1),Qe=L(()=>y.copyerConfig1),Xe=L(()=>y.conditionsConfig1);$(Ge,n=>{n.flag&&n.id===a&&f("update:flowPermission",n.value)}),$(Ke,n=>{n.flag&&n.id===a&&f("update:nodeConfig",n.value)}),$(Qe,n=>{n.flag&&n.id===a&&f("update:nodeConfig",n.value)}),$(Xe,n=>{n.flag&&n.id===a&&f("update:nodeConfig",n.value)});const $e=n=>{n||n===0?v.value[n]=!0:p.value=!0},je=n=>{n||n===0?(v.value[n]=!1,o.nodeConfig.conditionNodes[n].nodeName=o.nodeConfig.conditionNodes[n].nodeName||"\u6761\u4EF6"):(p.value=!1,o.nodeConfig.nodeName=o.nodeConfig.nodeName||l)},Ye=()=>{f("update:nodeConfig",o.nodeConfig.childNode)},Ze=()=>{let n=o.nodeConfig.conditionNodes.length+1;o.nodeConfig.conditionNodes.push({nodeName:"\u6761\u4EF6"+n,type:3,priorityLevel:n,conditionList:[],nodeUserList:[],childNode:null}),k(),f("update:nodeConfig",o.nodeConfig)},Fe=(n,d)=>{n.childNode?Fe(n.childNode,d):n.childNode=d},B=n=>{var{type:d}=o.nodeConfig;d==0?(b(!0),M({value:o.flowPermission,flag:!1,id:a})):d==1?(V(!0),A({value:{...JSON.parse(JSON.stringify(o.nodeConfig)),settype:o.nodeConfig.settype?o.nodeConfig.settype:1},flag:!1,id:a})):d==2?(S(!0),ze({value:JSON.parse(JSON.stringify(o.nodeConfig)),flag:!1,id:a})):(I(!0),He({value:JSON.parse(JSON.stringify(o.nodeConfig)),priorityLevel:n,flag:!1,id:a}))},Je=(n,d=1)=>{o.nodeConfig.conditionNodes[n]=o.nodeConfig.conditionNodes.splice(n+d,1,o.nodeConfig.conditionNodes[n])[0],o.nodeConfig.conditionNodes.map((T,R)=>{T.priorityLevel=R+1}),k(),f("update:nodeConfig",o.nodeConfig)};return(n,d)=>{const T=co("nodeWrap",!0),R=po("focus");return s(),r(U,null,[e.nodeConfig.type<3?(s(),r("div",oe,[i("div",{class:We(["node-wrap-box",(e.nodeConfig.type==0?"start-node ":"")+(C(D)&&e.nodeConfig.error?"active error":"")])},[i("div",{class:"title",style:fo(`background: rgb(${C(X)[e.nodeConfig.type]});`)},[e.nodeConfig.type==0?(s(),r("span",ne,w(e.nodeConfig.nodeName),1)):(s(),r(U,{key:1},[i("span",ie,w(e.nodeConfig.type==1?"\uE8EF":"\uE93B"),1),C(p)?Be((s(),r("input",{key:0,type:"text",class:"ant-input editable-title-input",onBlur:d[0]||(d[0]=t=>je()),onFocus:d[1]||(d[1]=t=>t.currentTarget.select()),"onUpdate:modelValue":d[2]||(d[2]=t=>e.nodeConfig.nodeName=t),placeholder:C(l)},null,40,de)),[[R],[Re,e.nodeConfig.nodeName]]):(s(),r("span",{key:1,class:"editable-title",onClick:d[3]||(d[3]=t=>$e())},w(e.nodeConfig.nodeName),1)),i("i",{class:"anticon anticon-close close",onClick:Ye})],64))],4),i("div",{class:"content",onClick:B},[i("div",te,[C(u)?h("",!0):(s(),r("span",le,"\u8BF7\u9009\u62E9"+w(C(l)),1)),go(" "+w(C(u)),1)]),se]),C(D)&&e.nodeConfig.error?(s(),r("div",ae,re)):h("",!0)],2),P(x,{childNodeP:e.nodeConfig.childNode,"onUpdate:childNodeP":d[4]||(d[4]=t=>e.nodeConfig.childNode=t)},null,8,["childNodeP"])])):h("",!0),e.nodeConfig.type==4?(s(),r("div",ce,[i("div",pe,[i("div",fe,[i("button",{class:"add-branch",onClick:Ze},"\u6DFB\u52A0\u6761\u4EF6"),(s(!0),r(U,null,Co(e.nodeConfig.conditionNodes,(t,N)=>(s(),r("div",{class:"col-box",key:N},[i("div",ge,[i("div",Ce,[i("div",{class:We(["auto-judge",C(D)&&t.error?"error active":""])},[N!=0?(s(),r("div",{key:0,class:"sort-left",onClick:g=>Je(N,-1)},"<",8,ue)):h("",!0),i("div",Ne,[C(v)[N]?Be((s(),r("input",{key:0,type:"text",class:"ant-input editable-title-input",onBlur:g=>je(N),onFocus:d[5]||(d[5]=g=>g.currentTarget.select()),"onUpdate:modelValue":g=>t.nodeName=g},null,40,he)),[[Re,t.nodeName]]):(s(),r("span",{key:1,class:"editable-title",onClick:g=>$e(N)},w(t.nodeName),9,ve)),i("span",{class:"priority-title",onClick:g=>B(t.priorityLevel)},"\u4F18\u5148\u7EA7"+w(t.priorityLevel),9,ye),i("i",{class:"anticon anticon-close close",onClick:g=>(eo=>{o.nodeConfig.conditionNodes.splice(eo,1),o.nodeConfig.conditionNodes.map((Ve,Ie)=>{Ve.priorityLevel=Ie+1,Ve.nodeName=`\u6761\u4EF6${Ie+1}`}),k(),f("update:nodeConfig",o.nodeConfig),o.nodeConfig.conditionNodes.length==1&&(o.nodeConfig.childNode&&(o.nodeConfig.conditionNodes[0].childNode?Fe(o.nodeConfig.conditionNodes[0].childNode,o.nodeConfig.childNode):o.nodeConfig.conditionNodes[0].childNode=o.nodeConfig.childNode),f("update:nodeConfig",o.nodeConfig.conditionNodes[0].childNode))})(N)},null,8,me)]),N!=e.nodeConfig.conditionNodes.length-1?(s(),r("div",{key:1,class:"sort-right",onClick:g=>Je(N)},">",8,ke)):h("",!0),i("div",{class:"content",onClick:g=>B(t.priorityLevel)},w(C(J)(e.nodeConfig,N)),9,be),C(D)&&t.error?(s(),r("div",we,Le)):h("",!0)],2),P(x,{childNodeP:t.childNode,"onUpdate:childNodeP":g=>t.childNode=g},null,8,["childNodeP","onUpdate:childNodeP"])])]),t.childNode?(s(),Ee(T,{key:0,nodeConfig:t.childNode,"onUpdate:nodeConfig":g=>t.childNode=g},null,8,["nodeConfig","onUpdate:nodeConfig"])):h("",!0),N==0?(s(),r(U,{key:1},[Pe,Ue],64)):h("",!0),N==e.nodeConfig.conditionNodes.length-1?(s(),r(U,{key:2},[xe,_e],64)):h("",!0)]))),128))]),P(x,{childNodeP:e.nodeConfig.childNode,"onUpdate:childNodeP":d[6]||(d[6]=t=>e.nodeConfig.childNode=t)},null,8,["childNodeP"])])])):h("",!0),e.nodeConfig.childNode?(s(),Ee(T,{key:2,nodeConfig:e.nodeConfig.childNode,"onUpdate:nodeConfig":d[7]||(d[7]=t=>e.nodeConfig.childNode=t)},null,8,["nodeConfig"])):h("",!0)],64)}}},De={class:"dingflow-design"},Te={class:"box-scale"},Oe=i("div",{class:"end-node"},[i("div",{class:"end-node-circle"}),i("div",{class:"end-node-text"},"\u6D41\u7A0B\u7ED3\u675F")],-1),qe=uo({name:"SimpleWorkflowDesignEditor",__name:"index",setup(e){let c=O({nodeName:"\u53D1\u8D77\u4EBA",type:0,id:"root",formPerms:{},nodeUserList:[],childNode:{}});return(a,o)=>(s(),r("div",null,[i("section",De,[i("div",Te,[P(Se,{nodeConfig:C(c),"onUpdate:nodeConfig":o[0]||(o[0]=l=>Ae(c)?c.value=l:c=l)},null,8,["nodeConfig"]),Oe])])]))}})});export{ho as __tla,qe as default};
