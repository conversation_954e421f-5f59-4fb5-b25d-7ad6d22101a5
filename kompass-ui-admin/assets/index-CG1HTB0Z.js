import{e9 as l,ea as m,by as t,__tla as p}from"./index-BUSn51wb.js";import{f as d}from"./fetch-D5K_4anA.js";let a,g=Promise.all([(()=>{try{return p}catch{}})()]).then(async()=>{a={writeStream:({data:e,onClose:r,onMessage:o,onError:s,ctrl:n})=>{const i=l();return d(`${m.base_url}/ai/write/generate-stream`,{method:"post",headers:{"Content-Type":"application/json",Authorization:`Bearer ${i}`},openWhenHidden:!0,body:JSON.stringify(e),onmessage:o,onerror:s,onclose:r,signal:n.signal})},getWritePage:e=>t.get({url:"/ai/write/page",params:e}),deleteWrite:e=>t.delete({url:"/ai/write/delete",params:{id:e}})}});export{a as W,g as __tla};
