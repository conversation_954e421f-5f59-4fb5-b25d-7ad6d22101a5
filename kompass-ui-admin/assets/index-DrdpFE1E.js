import{by as t,__tla as d}from"./index-BUSn51wb.js";let e,l=Promise.all([(()=>{try{return d}catch{}})()]).then(async()=>{e={getCustomerAddApplyPage:async a=>await t.get({url:"/als/customer-add-apply/page",params:a}),getCustomerAddApply:async a=>await t.get({url:"/als/customer-add-apply/get?id="+a}),createCustomerAddApply:async a=>await t.post({url:"/als/customer-add-apply/create",data:a}),updateCustomerAddApply:async a=>await t.put({url:"/als/customer-add-apply/update",data:a}),deleteCustomerAddApply:async a=>await t.delete({url:"/als/customer-add-apply/delete?id="+a}),exportCustomerAddApply:async a=>await t.download({url:"/als/customer-add-apply/export-excel",params:a}),auditSubmit:async a=>await t.put({url:"/als/customer-add-apply/audit",data:a})}});export{e as C,l as __tla};
