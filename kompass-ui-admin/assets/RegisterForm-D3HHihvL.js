import{_ as t,__tla as _}from"./RegisterForm.vue_vue_type_script_setup_true_lang-CAJMH4Ie.js";import{__tla as r}from"./Form-DJa9ov9B.js";import{__tla as a}from"./index-BUSn51wb.js";import{__tla as l}from"./el-virtual-list-4L-8WDNg.js";import{__tla as o}from"./el-tree-select-CBuha0HW.js";import{__tla as c}from"./el-time-select-C-_NEIfl.js";import{__tla as m}from"./InputPassword-RefetKoR.js";import{__tla as e}from"./XButton-BjahQbul.js";import{__tla as s}from"./useForm-C3fyhjNV.js";import{__tla as n}from"./useValidator-C1ftTumK.js";import{__tla as f}from"./LoginFormTitle.vue_vue_type_script_setup_true_lang-DUoWRVM3.js";let h=Promise.all([(()=>{try{return _}catch{}})(),(()=>{try{return r}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return c}catch{}})(),(()=>{try{return m}catch{}})(),(()=>{try{return e}catch{}})(),(()=>{try{return s}catch{}})(),(()=>{try{return n}catch{}})(),(()=>{try{return f}catch{}})()]).then(async()=>{});export{h as __tla,t as default};
