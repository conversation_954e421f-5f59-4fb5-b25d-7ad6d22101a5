import{d as te,I as re,n as oe,r as i,f as ue,C as se,T as de,o as u,c as k,i as a,w as r,a as l,U as F,F as b,k as C,l as d,V as ie,G as W,j as p,H as m,eo as ce,dV as ne,Z as pe,L as _e,J as me,K as fe,M as ye,_ as he,N as ke,O as be,P as we,Q as ve,R as ge,__tla as Ve}from"./index-BUSn51wb.js";import{_ as xe,__tla as Ce}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as Se,__tla as Ie}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as Ue,__tla as Te}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as Pe,__tla as Ne}from"./index-COobLwz-.js";import{b as De,__tla as Oe}from"./formatTime-DWdBpgsM.js";import{d as Ae}from"./download-e0EdwhTv.js";import{_ as Me,S as I,__tla as Re}from"./StockOutForm.vue_vue_type_script_setup_true_lang-Bsq2HKPY.js";import{P as Ye,__tla as ze}from"./index-B00QUU3o.js";import{W as Ee,__tla as He}from"./index-B5GxX3eg.js";import{g as Ke,__tla as Le}from"./index-BYXzDB8j.js";import{C as Fe,__tla as We}from"./index-DYwp4_G0.js";import{__tla as qe}from"./index-Cch5e1V0.js";import"./color-BN7ZL7BD.js";import{__tla as je}from"./el-card-CJbXGyyg.js";import{__tla as Ge}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as Je}from"./StockOutItemForm.vue_vue_type_script_setup_true_lang-BYOcEnXG.js";import{__tla as Qe}from"./index-BCEOZol9.js";let q,Ze=Promise.all([(()=>{try{return Ve}catch{}})(),(()=>{try{return Ce}catch{}})(),(()=>{try{return Ie}catch{}})(),(()=>{try{return Te}catch{}})(),(()=>{try{return Ne}catch{}})(),(()=>{try{return Oe}catch{}})(),(()=>{try{return Re}catch{}})(),(()=>{try{return ze}catch{}})(),(()=>{try{return He}catch{}})(),(()=>{try{return Le}catch{}})(),(()=>{try{return We}catch{}})(),(()=>{try{return qe}catch{}})(),(()=>{try{return je}catch{}})(),(()=>{try{return Ge}catch{}})(),(()=>{try{return Je}catch{}})(),(()=>{try{return Qe}catch{}})()]).then(async()=>{q=te({name:"ErpStockOut",__name:"index",setup($e){const w=re(),{t:j}=oe(),U=i(!0),N=i([]),D=i(0),o=ue({pageNo:1,pageSize:10,no:void 0,productId:void 0,customerId:void 0,warehouseId:void 0,outTime:[],status:void 0,remark:void 0,creator:void 0}),O=i(),T=i(!1),A=i([]),M=i([]),R=i([]),Y=i([]),y=async()=>{U.value=!0;try{const s=await I.getStockOutPage(o);N.value=s.list,D.value=s.total}finally{U.value=!1}},S=()=>{o.pageNo=1,y()},G=()=>{O.value.resetFields(),S()},z=i(),P=(s,t)=>{z.value.open(s,t)},E=async s=>{try{await w.delConfirm(),await I.deleteStockOut(s),w.success(j("common.delSuccess")),await y(),v.value=v.value.filter(t=>!s.includes(t.id))}catch{}},H=async(s,t)=>{try{await w.confirm(`\u786E\u5B9A${t===20?"\u5BA1\u6279":"\u53CD\u5BA1\u6279"}\u8BE5\u51FA\u5E93\u5355\u5417\uFF1F`),await I.updateStockOutStatus(s,t),w.success((t===20?"\u5BA1\u6279":"\u53CD\u5BA1\u6279")+"\u6210\u529F"),await y()}catch{}},J=async()=>{try{await w.exportConfirm(),T.value=!0;const s=await I.exportStockOut(o);Ae.excel(s,"\u5176\u5B83\u51FA\u5E93\u5355.xls")}catch{}finally{T.value=!1}},v=i([]),Q=s=>{v.value=s};return se(async()=>{await y(),A.value=await Ye.getProductSimpleList(),M.value=await Ee.getWarehouseSimpleList(),R.value=await Fe.getCustomerSimpleList(),Y.value=await Ke()}),(s,t)=>{const Z=Pe,K=pe,_=_e,g=me,V=fe,$=ye,x=he,c=ke,B=be,L=Ue,n=we,X=Se,ee=ve,ae=xe,f=de("hasPermi"),le=ge;return u(),k(b,null,[a(Z,{title:"\u3010\u5E93\u5B58\u3011\u5176\u5B83\u5165\u5E93\u3001\u5176\u5B83\u51FA\u5E93",url:"https://doc.iocoder.cn/erp/stock-in-out/"}),a(L,null,{default:r(()=>[a(B,{class:"-mb-15px",model:l(o),ref_key:"queryFormRef",ref:O,inline:!0,"label-width":"68px"},{default:r(()=>[a(_,{label:"\u51FA\u5E93\u5355\u53F7",prop:"no"},{default:r(()=>[a(K,{modelValue:l(o).no,"onUpdate:modelValue":t[0]||(t[0]=e=>l(o).no=e),placeholder:"\u8BF7\u8F93\u5165\u51FA\u5E93\u5355\u53F7",clearable:"",onKeyup:F(S,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(_,{label:"\u4EA7\u54C1",prop:"productId"},{default:r(()=>[a(V,{modelValue:l(o).productId,"onUpdate:modelValue":t[1]||(t[1]=e=>l(o).productId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1",class:"!w-240px"},{default:r(()=>[(u(!0),k(b,null,C(l(A),e=>(u(),d(g,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(_,{label:"\u51FA\u5E93\u65F6\u95F4",prop:"outTime"},{default:r(()=>[a($,{modelValue:l(o).outTime,"onUpdate:modelValue":t[2]||(t[2]=e=>l(o).outTime=e),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),a(_,{label:"\u5BA2\u6237",prop:"customerId"},{default:r(()=>[a(V,{modelValue:l(o).customerId,"onUpdate:modelValue":t[3]||(t[3]=e=>l(o).customerId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4F9B\u5BA2\u6237",class:"!w-240px"},{default:r(()=>[(u(!0),k(b,null,C(l(R),e=>(u(),d(g,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(_,{label:"\u4ED3\u5E93",prop:"warehouseId"},{default:r(()=>[a(V,{modelValue:l(o).warehouseId,"onUpdate:modelValue":t[4]||(t[4]=e=>l(o).warehouseId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93",class:"!w-240px"},{default:r(()=>[(u(!0),k(b,null,C(l(M),e=>(u(),d(g,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(_,{label:"\u521B\u5EFA\u4EBA",prop:"creator"},{default:r(()=>[a(V,{modelValue:l(o).creator,"onUpdate:modelValue":t[5]||(t[5]=e=>l(o).creator=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u521B\u5EFA\u4EBA",class:"!w-240px"},{default:r(()=>[(u(!0),k(b,null,C(l(Y),e=>(u(),d(g,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(_,{label:"\u72B6\u6001",prop:"status"},{default:r(()=>[a(V,{modelValue:l(o).status,"onUpdate:modelValue":t[6]||(t[6]=e=>l(o).status=e),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",clearable:"",class:"!w-240px"},{default:r(()=>[(u(!0),k(b,null,C(l(ie)(l(W).ERP_AUDIT_STATUS),e=>(u(),d(g,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(_,{label:"\u5907\u6CE8",prop:"remark"},{default:r(()=>[a(K,{modelValue:l(o).remark,"onUpdate:modelValue":t[7]||(t[7]=e=>l(o).remark=e),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",clearable:"",onKeyup:F(S,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(_,null,{default:r(()=>[a(c,{onClick:S},{default:r(()=>[a(x,{icon:"ep:search",class:"mr-5px"}),p(" \u641C\u7D22")]),_:1}),a(c,{onClick:G},{default:r(()=>[a(x,{icon:"ep:refresh",class:"mr-5px"}),p(" \u91CD\u7F6E")]),_:1}),m((u(),d(c,{type:"primary",plain:"",onClick:t[8]||(t[8]=e=>P("create"))},{default:r(()=>[a(x,{icon:"ep:plus",class:"mr-5px"}),p(" \u65B0\u589E ")]),_:1})),[[f,["erp:stock-out:create"]]]),m((u(),d(c,{type:"success",plain:"",onClick:J,loading:l(T)},{default:r(()=>[a(x,{icon:"ep:download",class:"mr-5px"}),p(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[f,["erp:stock-out:export"]]]),m((u(),d(c,{type:"danger",plain:"",onClick:t[9]||(t[9]=e=>E(l(v).map(h=>h.id))),disabled:l(v).length===0},{default:r(()=>[a(x,{icon:"ep:delete",class:"mr-5px"}),p(" \u5220\u9664 ")]),_:1},8,["disabled"])),[[f,["erp:stock-out:delete"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(L,null,{default:r(()=>[m((u(),d(ee,{data:l(N),stripe:!0,"show-overflow-tooltip":!0,onSelectionChange:Q},{default:r(()=>[a(n,{width:"30",label:"\u9009\u62E9",type:"selection"}),a(n,{"min-width":"180",label:"\u51FA\u5E93\u5355\u53F7",align:"center",prop:"no"}),a(n,{label:"\u4EA7\u54C1\u4FE1\u606F",align:"center",prop:"productNames","min-width":"200"}),a(n,{label:"\u5BA2\u6237",align:"center",prop:"customerName"}),a(n,{label:"\u51FA\u5E93\u65F6\u95F4",align:"center",prop:"outTime",formatter:l(De),width:"120px"},null,8,["formatter"]),a(n,{label:"\u521B\u5EFA\u4EBA",align:"center",prop:"creatorName"}),a(n,{label:"\u6570\u91CF",align:"center",prop:"totalCount",formatter:l(ce)},null,8,["formatter"]),a(n,{label:"\u91D1\u989D",align:"center",prop:"totalPrice",formatter:l(ne)},null,8,["formatter"]),a(n,{label:"\u72B6\u6001",align:"center",fixed:"right",width:"90",prop:"status"},{default:r(e=>[a(X,{type:l(W).ERP_AUDIT_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),a(n,{label:"\u64CD\u4F5C",align:"center",fixed:"right",width:"220"},{default:r(e=>[m((u(),d(c,{link:"",onClick:h=>P("detail",e.row.id)},{default:r(()=>[p(" \u8BE6\u60C5 ")]),_:2},1032,["onClick"])),[[f,["erp:stock-out:query"]]]),m((u(),d(c,{link:"",type:"primary",onClick:h=>P("update",e.row.id),disabled:e.row.status===20},{default:r(()=>[p(" \u7F16\u8F91 ")]),_:2},1032,["onClick","disabled"])),[[f,["erp:stock-out:update"]]]),e.row.status===10?m((u(),d(c,{key:0,link:"",type:"primary",onClick:h=>H(e.row.id,20)},{default:r(()=>[p(" \u5BA1\u6279 ")]),_:2},1032,["onClick"])),[[f,["erp:stock-out:update-status"]]]):m((u(),d(c,{key:1,link:"",type:"danger",onClick:h=>H(e.row.id,10)},{default:r(()=>[p(" \u53CD\u5BA1\u6279 ")]),_:2},1032,["onClick"])),[[f,["erp:stock-out:update-status"]]]),m((u(),d(c,{link:"",type:"danger",onClick:h=>E([e.row.id])},{default:r(()=>[p(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[f,["erp:stock-out:delete"]]])]),_:1})]),_:1},8,["data"])),[[le,l(U)]]),a(ae,{total:l(D),page:l(o).pageNo,"onUpdate:page":t[10]||(t[10]=e=>l(o).pageNo=e),limit:l(o).pageSize,"onUpdate:limit":t[11]||(t[11]=e=>l(o).pageSize=e),onPagination:y},null,8,["total","page","limit"])]),_:1}),a(Me,{ref_key:"formRef",ref:z,onSuccess:y},null,512)],64)}}})});export{Ze as __tla,q as default};
