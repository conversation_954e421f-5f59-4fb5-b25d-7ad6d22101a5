import{d as i,o as a,c as l,i as _,F as c,k as h,av as d,t as f,B as u,__tla as m}from"./index-BUSn51wb.js";import{E as y,__tla as x}from"./el-image-BjHZRFih.js";let o,v=Promise.all([(()=>{try{return m}catch{}})(),(()=>{try{return x}catch{}})()]).then(async()=>{let e,s;e={class:"relative h-full min-h-30px w-full"},s=i({name:"HotZone",__name:"index",props:{property:{}},setup:w=>(r,$)=>{const n=y;return a(),l("div",e,[_(n,{src:r.property.imgUrl,class:"pointer-events-none h-full w-full select-none"},null,8,["src"]),(a(!0),l(c,null,h(r.property.list,(t,p)=>(a(),l("div",{key:p,class:"hot-zone",style:d({width:`${t.width}px`,height:`${t.height}px`,top:`${t.top}px`,left:`${t.left}px`})},f(t.name),5))),128))])}}),o=u(s,[["__scopeId","data-v-2a35f8d1"]])});export{v as __tla,o as default};
