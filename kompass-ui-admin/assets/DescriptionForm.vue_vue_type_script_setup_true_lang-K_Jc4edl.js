import{d as F,p as l,r as d,f as w,at as p,o as j,l as x,w as c,a as t,O as N,i as m,ck as O,I as V,dK as g,L as k,__tla as P}from"./index-BUSn51wb.js";import{r as I,__tla as K}from"./formRules-CA9eXdcX.js";let n,L=Promise.all([(()=>{try{return P}catch{}})(),(()=>{try{return K}catch{}})()]).then(async()=>{n=F({name:"ProductDescriptionForm",__name:"DescriptionForm",props:{propFormData:{type:Object,default:()=>{}},activeName:l.string.def(""),isDetail:l.bool.def(!1)},emits:["update:activeName"],setup(i,{expose:u,emit:_}){const f=V(),o=i,s=d(),e=d({description:""}),v=w({description:[I]});p(()=>e.value.description,a=>{a==="<p><br></p>"&&(e.value.description="")},{deep:!0,immediate:!0}),p(()=>o.propFormData,a=>{a&&g(e.value,a)},{immediate:!0});const b=_;return u({validate:async()=>{var a;if(s)try{await((a=t(s))==null?void 0:a.validate()),Object.assign(o.propFormData,e.value)}catch(r){throw f.error("\u3010\u5546\u54C1\u8BE6\u60C5\u3011\u4E0D\u5B8C\u5584\uFF0C\u8BF7\u586B\u5199\u76F8\u5173\u4FE1\u606F"),b("update:activeName","description"),r}}}),(a,r)=>{const y=k,D=N;return j(),x(D,{ref_key:"formRef",ref:s,model:t(e),rules:t(v),"label-width":"120px",disabled:i.isDetail},{default:c(()=>[m(y,{label:"\u5546\u54C1\u8BE6\u60C5",prop:"description"},{default:c(()=>[m(t(O),{modelValue:t(e).description,"onUpdate:modelValue":r[0]||(r[0]=h=>t(e).description=h)},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules","disabled"])}}})});export{n as _,L as __tla};
