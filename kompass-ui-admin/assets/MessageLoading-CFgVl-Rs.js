import{B as e,o as r,c as _,i as n,__tla as o}from"./index-BUSn51wb.js";import{E as l,__tla as c}from"./el-skeleton-item-tDN8U6BH.js";let a,d=Promise.all([(()=>{try{return o}catch{}})(),(()=>{try{return c}catch{}})()]).then(async()=>{const t={class:"message-loading"};a=e({},[["render",function(i,m){const s=l;return r(),_("div",t,[n(s,{animated:""})])}],["__scopeId","data-v-f6396fed"]])});export{d as __tla,a as default};
