import{d as z,r as h,f as A,at as S,dW as D,C as G,o as m,c as U,H as M,a as i,l as g,w as l,i as e,F as y,k as B,en as q,dX as L,j,a9 as T,el as Y,P as ee,J as ae,K as le,L as te,Z as oe,cc as de,N as re,Q as ue,O as se,s as ie,R as ne,__tla as ce}from"./index-BUSn51wb.js";import{P as pe,__tla as me}from"./index-B00QUU3o.js";import{W as fe,__tla as _e}from"./index-B5GxX3eg.js";import{S as he,__tla as be}from"./index-BCEOZol9.js";let W,we=Promise.all([(()=>{try{return ce}catch{}})(),(()=>{try{return me}catch{}})(),(()=>{try{return _e}catch{}})(),(()=>{try{return be}catch{}})()]).then(async()=>{W=z({__name:"StockInItemForm",props:{items:{},disabled:{type:Boolean}},setup(E,{expose:F}){const K=E,R=h(!1),c=h([]),b=A({inId:[{required:!0,message:"\u5165\u5E93\u7F16\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],warehouseId:[{required:!0,message:"\u4ED3\u5E93\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],productId:[{required:!0,message:"\u4EA7\u54C1\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],count:[{required:!0,message:"\u4EA7\u54C1\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),k=h([]),v=h([]),x=h([]),I=h(void 0);S(()=>K.items,async o=>{c.value=o},{immediate:!0}),S(()=>c.value,o=>{o&&o.length!==0&&o.forEach(s=>{s.totalPrice=D(s.productPrice,s.count)})},{deep:!0});const H=o=>{const{columns:s,data:u}=o,p=[];return s.forEach((f,r)=>{if(r!==0)if(["count","totalPrice"].includes(f.property)){const n=Y(u.map(w=>Number(w[f.property])));p[r]=f.property==="count"?q(n):L(n)}else p[r]="";else p[r]="\u5408\u8BA1"}),p},P=()=>{var s;const o={id:void 0,warehouseId:(s=I.value)==null?void 0:s.id,productId:void 0,productUnitName:void 0,productBarCode:void 0,productPrice:void 0,stockCount:void 0,count:1,totalPrice:void 0,remark:void 0};c.value.push(o)},C=async o=>{if(!o.productId||!o.warehouseId)return;const s=await he.getStock2(o.productId,o.warehouseId);o.stockCount=s?s.count:0};return F({validate:()=>k.value.validate()}),G(async()=>{v.value=await pe.getProductSimpleList(),x.value=await fe.getWarehouseSimpleList(),I.value=x.value.find(o=>o.defaultStatus),c.value.length===0&&P()}),(o,s)=>{const u=ee,p=ae,f=le,r=te,n=oe,w=de,$=re,J=ue,O=se,Q=ie,X=ne;return m(),U(y,null,[M((m(),g(O,{ref_key:"formRef",ref:k,model:i(c),rules:i(b),"label-width":"0px","inline-message":!0,disabled:o.disabled},{default:l(()=>[e(J,{data:i(c),"show-summary":"","summary-method":H,class:"-mt-10px"},{default:l(()=>[e(u,{label:"\u5E8F\u53F7",type:"index",align:"center",width:"60"}),e(u,{label:"\u4ED3\u5E93\u540D\u79F0","min-width":"125"},{default:l(({row:a,$index:d})=>[e(r,{prop:`${d}.warehouseId`,rules:i(b).warehouseId,class:"mb-0px!"},{default:l(()=>[e(f,{modelValue:a.warehouseId,"onUpdate:modelValue":t=>a.warehouseId=t,clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93",onChange:t=>((N,_)=>{C(_)})(0,a)},{default:l(()=>[(m(!0),U(y,null,B(i(x),t=>(m(),g(p,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),e(u,{label:"\u4EA7\u54C1\u540D\u79F0","min-width":"180"},{default:l(({row:a,$index:d})=>[e(r,{prop:`${d}.productId`,rules:i(b).productId,class:"mb-0px!"},{default:l(()=>[e(f,{modelValue:a.productId,"onUpdate:modelValue":t=>a.productId=t,clearable:"",filterable:"",onChange:t=>((N,_)=>{const V=v.value.find(Z=>Z.id===N);V&&(_.productUnitName=V.unitName,_.productBarCode=V.barCode,_.productPrice=V.minPrice),C(_)})(t,a),placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1"},{default:l(()=>[(m(!0),U(y,null,B(i(v),t=>(m(),g(p,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),e(u,{label:"\u5E93\u5B58","min-width":"100"},{default:l(({row:a})=>[e(r,{class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.stockCount,"onUpdate:modelValue":d=>a.stockCount=d,formatter:i(q)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1024)]),_:1}),e(u,{label:"\u6761\u7801","min-width":"150"},{default:l(({row:a})=>[e(r,{class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.productBarCode,"onUpdate:modelValue":d=>a.productBarCode=d},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),e(u,{label:"\u5355\u4F4D","min-width":"80"},{default:l(({row:a})=>[e(r,{class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.productUnitName,"onUpdate:modelValue":d=>a.productUnitName=d},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),e(u,{label:"\u6570\u91CF",prop:"count",fixed:"right","min-width":"140"},{default:l(({row:a,$index:d})=>[e(r,{prop:`${d}.count`,rules:i(b).count,class:"mb-0px!"},{default:l(()=>[e(w,{modelValue:a.count,"onUpdate:modelValue":t=>a.count=t,"controls-position":"right",min:.001,precision:3,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(u,{label:"\u4EA7\u54C1\u5355\u4EF7",fixed:"right","min-width":"120"},{default:l(({row:a,$index:d})=>[e(r,{prop:`${d}.productPrice`,class:"mb-0px!"},{default:l(()=>[e(w,{modelValue:a.productPrice,"onUpdate:modelValue":t=>a.productPrice=t,"controls-position":"right",min:.01,precision:2,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(u,{label:"\u5408\u8BA1\u91D1\u989D",prop:"totalPrice",fixed:"right","min-width":"100"},{default:l(({row:a,$index:d})=>[e(r,{prop:`${d}.totalPrice`,class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.totalPrice,"onUpdate:modelValue":t=>a.totalPrice=t,formatter:i(L)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1032,["prop"])]),_:1}),e(u,{label:"\u5907\u6CE8","min-width":"150"},{default:l(({row:a,$index:d})=>[e(r,{prop:`${d}.remark`,class:"mb-0px!"},{default:l(()=>[e(n,{modelValue:a.remark,"onUpdate:modelValue":t=>a.remark=t,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(u,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"60"},{default:l(({$index:a})=>[e($,{onClick:d=>{return t=a,void c.value.splice(t,1);var t},link:""},{default:l(()=>[j("\u2014")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["model","rules","disabled"])),[[X,i(R)]]),o.disabled?T("",!0):(m(),g(Q,{key:0,justify:"center",class:"mt-3"},{default:l(()=>[e($,{onClick:P,round:""},{default:l(()=>[j("+ \u6DFB\u52A0\u5165\u5E93\u4EA7\u54C1")]),_:1})]),_:1}))],64)}}})});export{W as _,we as __tla};
