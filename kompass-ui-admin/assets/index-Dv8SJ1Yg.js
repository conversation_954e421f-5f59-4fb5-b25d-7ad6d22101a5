import{d as c,o as e,c as p,i as _,l as n,_ as o,B as i,__tla as m}from"./index-BUSn51wb.js";import{E as y,__tla as u}from"./el-image-BjHZRFih.js";let r,x=Promise.all([(()=>{try{return m}catch{}})(),(()=>{try{return u}catch{}})()]).then(async()=>{let t;t={key:0,class:"h-50px flex items-center justify-center bg-gray-3"},r=i(c({name:"ImageBar",__name:"index",props:{property:{}},setup:d=>(a,f)=>{const s=o,l=y;return a.property.imgUrl?(e(),n(l,{key:1,class:"min-h-30px",src:a.property.imgUrl},null,8,["src"])):(e(),p("div",t,[_(s,{icon:"ep:picture",class:"text-gray-8 text-30px!"})]))}}),[["__scopeId","data-v-26bf41d6"]])});export{x as __tla,r as default};
