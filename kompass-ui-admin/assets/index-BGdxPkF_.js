import{by as t,__tla as e}from"./index-BUSn51wb.js";let l,u=Promise.all([(()=>{try{return e}catch{}})()]).then(async()=>{l={getEvaluationPage:async a=>await t.get({url:"/als/evaluation/page",params:a}),getEvaluation:async a=>await t.get({url:"/als/evaluation/get?id="+a}),createEvaluation:async a=>await t.post({url:"/als/evaluation/create",data:a}),updateEvaluation:async a=>await t.put({url:"/als/evaluation/update",data:a}),dealEvaluation:async a=>await t.put({url:"/als/evaluation/deal",data:a}),deleteEvaluation:async a=>await t.delete({url:"/als/evaluation/delete?id="+a}),exportEvaluation:async a=>await t.download({url:"/als/evaluation/export-excel",params:a})}});export{l as E,u as __tla};
