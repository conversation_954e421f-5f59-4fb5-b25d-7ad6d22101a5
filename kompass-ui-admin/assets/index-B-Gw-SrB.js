import{d as p,o,c as l,g as a,av as r,__tla as s}from"./index-BUSn51wb.js";let t,i=Promise.all([(()=>{try{return s}catch{}})()]).then(async()=>{t=p({name:"Divider",__name:"index",props:{property:{}},setup:d=>(e,n)=>(o(),l("div",{class:"flex items-center",style:r({height:e.property.height+"px"})},[a("div",{class:"w-full",style:r({borderTopStyle:e.property.borderType,borderTopColor:e.property.lineColor,borderTopWidth:`${e.property.lineWidth}px`,margin:e.property.paddingType==="none"?"0":"0px 16px"})},null,4)],4))})});export{i as __tla,t as default};
