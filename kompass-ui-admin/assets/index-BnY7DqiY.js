import{by as p,__tla as y}from"./index-BUSn51wb.js";let t,e,l,r,s,u,d,_=Promise.all([(()=>{try{return y}catch{}})()]).then(async()=>{t=a=>p.get({url:"/pay/app/page",params:a}),u=a=>p.get({url:"/pay/app/get?id="+a}),l=a=>p.post({url:"/pay/app/create",data:a}),d=a=>p.put({url:"/pay/app/update",data:a}),e=a=>p.put({url:"/pay/app/update-status",data:a}),r=a=>p.delete({url:"/pay/app/delete?id="+a}),s=()=>p.get({url:"/pay/app/list"})});export{_ as __tla,t as a,e as b,l as c,r as d,s as e,u as g,d as u};
