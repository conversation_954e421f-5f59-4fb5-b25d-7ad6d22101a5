import{d as x,r as f,o as s,c as o,k as h,av as b,a as g,i,w as y,g as n,t as v,F as w,_ as k,__tla as C}from"./index-BUSn51wb.js";import{E as P,__tla as j}from"./el-image-BjHZRFih.js";let p,z=Promise.all([(()=>{try{return C}catch{}})(),(()=>{try{return j}catch{}})()]).then(async()=>{let r,a,e;r=["onClick"],a={class:"h-full w-full flex items-center justify-center"},e={class:"absolute right-1 top-1 text-12px"},p=x({name:"Popover",__name:"index",props:{property:{}},setup(B){const l=f(0);return(c,E)=>{const u=k,_=P;return s(!0),o(w,null,h(c.property.list,(d,t)=>(s(),o("div",{key:t,class:"absolute bottom-50% right-50% h-454px w-292px border-1px border-gray border-rounded-4px border-solid bg-white p-1px",style:b({zIndex:100+t+(g(l)===t?100:0),marginRight:-146-20*t+"px",marginBottom:-227-20*t+"px"}),onClick:F=>(m=>{l.value=m})(t)},[i(_,{src:d.imgUrl,fit:"contain",class:"h-full w-full"},{error:y(()=>[n("div",a,[i(u,{icon:"ep:picture"})])]),_:2},1032,["src"]),n("div",e,v(t+1),1)],12,r))),128)}}})});export{z as __tla,p as default};
