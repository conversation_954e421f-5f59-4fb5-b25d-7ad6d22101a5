import{d as H,f as L,e as N,r as m,b as j,at as J,C as K,o as f,c as y,i as a,w as t,a as e,F as h,k as g,V as O,G as P,l as x,j as q,y as W,M as B,L as Q,J as X,K as Z,_ as $,N as aa,O as ea,z as la,A as ta,E as ra,__tla as sa}from"./index-BUSn51wb.js";import{_ as ua,__tla as _a}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{E as na,__tla as oa}from"./el-tree-select-CBuha0HW.js";import{g as ma,__tla as da}from"./index-Bqt292RI.js";import{g as ca,__tla as pa}from"./index-BYXzDB8j.js";import{f as C,e as fa,g as ia,h as ya,__tla as ha}from"./formatTime-DWdBpgsM.js";import{h as S,d as va}from"./tree-BMa075Oj.js";import{_ as ba,__tla as Ia}from"./FunnelBusiness.vue_vue_type_script_setup_true_lang-DNtmt2_Y.js";import{_ as Va,__tla as wa}from"./BusinessSummary.vue_vue_type_script_setup_true_lang-BzqIiziA.js";import{_ as Ra,__tla as ka}from"./BusinessInversionRateSummary.vue_vue_type_script_setup_true_lang-Ce4eneKm.js";import{__tla as Da}from"./el-card-CJbXGyyg.js";import{__tla as ga}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import"./color-BN7ZL7BD.js";import{__tla as xa}from"./el-skeleton-item-tDN8U6BH.js";import{__tla as qa}from"./Echart.vue_vue_type_script_setup_true_lang-DnLZLH3w.js";import{__tla as Ca}from"./funnel-B_PNiNbM.js";import{__tla as Sa}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{__tla as Ua}from"./index-Cch5e1V0.js";let U,Ea=Promise.all([(()=>{try{return sa}catch{}})(),(()=>{try{return _a}catch{}})(),(()=>{try{return oa}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return pa}catch{}})(),(()=>{try{return ha}catch{}})(),(()=>{try{return Ia}catch{}})(),(()=>{try{return wa}catch{}})(),(()=>{try{return ka}catch{}})(),(()=>{try{return Da}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return xa}catch{}})(),(()=>{try{return qa}catch{}})(),(()=>{try{return Ca}catch{}})(),(()=>{try{return Sa}catch{}})(),(()=>{try{return Ua}catch{}})()]).then(async()=>{U=H({name:"CrmStatisticsFunnel",__name:"index",setup(Fa){const r=L({interval:2,deptId:N().getUser.deptId,userId:void 0,times:[C(fa(new Date(new Date().getTime()-6048e5))),C(ia(new Date(new Date().getTime()-864e5)))]}),v=m(),b=m([]),I=m([]),E=j(()=>r.deptId?I.value.filter(n=>n.deptId===r.deptId):[]),c=m("funnelRef"),V=m(),w=m(),R=m(),_=()=>{var n,s,d,u,o,p;switch(c.value){case"funnelRef":(s=(n=V.value)==null?void 0:n.loadData)==null||s.call(n);break;case"businessSummaryRef":(u=(d=w.value)==null?void 0:d.loadData)==null||u.call(d);break;case"businessInversionRateSummaryRef":(p=(o=R.value)==null?void 0:o.loadData)==null||p.call(o)}};J(c,()=>{_()});const F=()=>{v.value.resetFields(),_()};return K(async()=>{b.value=S(await ma()),I.value=S(await ca())}),(n,s)=>{const d=B,u=Q,o=X,p=Z,T=na,k=$,D=aa,Y=ea,z=ua,i=la,A=ta,M=ra;return f(),y(h,null,[a(z,null,{default:t(()=>[a(Y,{ref_key:"queryFormRef",ref:v,inline:!0,model:e(r),class:"-mb-15px","label-width":"68px"},{default:t(()=>[a(u,{label:"\u65F6\u95F4\u8303\u56F4",prop:"orderDate"},{default:t(()=>[a(d,{modelValue:e(r).times,"onUpdate:modelValue":s[0]||(s[0]=l=>e(r).times=l),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],shortcuts:e(ya),class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss",onChange:_},null,8,["modelValue","default-time","shortcuts"])]),_:1}),a(u,{label:"\u65F6\u95F4\u95F4\u9694",prop:"interval"},{default:t(()=>[a(p,{modelValue:e(r).interval,"onUpdate:modelValue":s[1]||(s[1]=l=>e(r).interval=l),class:"!w-240px",placeholder:"\u95F4\u9694\u7C7B\u578B",onChange:_},{default:t(()=>[(f(!0),y(h,null,g(e(O)(e(P).DATE_INTERVAL),l=>(f(),x(o,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(u,{label:"\u5F52\u5C5E\u90E8\u95E8",prop:"deptId"},{default:t(()=>[a(T,{modelValue:e(r).deptId,"onUpdate:modelValue":s[2]||(s[2]=l=>e(r).deptId=l),data:e(b),props:e(va),"check-strictly":"",class:"!w-240px","node-key":"id",placeholder:"\u8BF7\u9009\u62E9\u5F52\u5C5E\u90E8\u95E8",onChange:s[3]||(s[3]=l=>(e(r).userId=void 0,_()))},null,8,["modelValue","data","props"])]),_:1}),a(u,{label:"\u5458\u5DE5",prop:"userId"},{default:t(()=>[a(p,{modelValue:e(r).userId,"onUpdate:modelValue":s[4]||(s[4]=l=>e(r).userId=l),class:"!w-240px",clearable:"",placeholder:"\u5458\u5DE5",onChange:_},{default:t(()=>[(f(!0),y(h,null,g(e(E),(l,G)=>(f(),x(o,{key:G,label:l.nickname,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(u,null,{default:t(()=>[a(D,{onClick:_},{default:t(()=>[a(k,{class:"mr-5px",icon:"ep:search"}),q(" \u67E5\u8BE2 ")]),_:1}),a(D,{onClick:F},{default:t(()=>[a(k,{class:"mr-5px",icon:"ep:refresh"}),q(" \u91CD\u7F6E ")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),a(M,null,{default:t(()=>[a(A,{modelValue:e(c),"onUpdate:modelValue":s[5]||(s[5]=l=>W(c)?c.value=l:null)},{default:t(()=>[a(i,{label:"\u9500\u552E\u6F0F\u6597\u5206\u6790",lazy:"",name:"funnelRef"},{default:t(()=>[a(ba,{ref_key:"funnelRef",ref:V,"query-params":e(r)},null,8,["query-params"])]),_:1}),a(i,{label:"\u65B0\u589E\u5546\u673A\u5206\u6790",lazy:"",name:"businessSummaryRef"},{default:t(()=>[a(Va,{ref_key:"businessSummaryRef",ref:w,"query-params":e(r)},null,8,["query-params"])]),_:1}),a(i,{label:"\u5546\u673A\u8F6C\u5316\u7387\u5206\u6790",lazy:"",name:"businessInversionRateSummaryRef"},{default:t(()=>[a(Ra,{ref_key:"businessInversionRateSummaryRef",ref:R,"query-params":e(r)},null,8,["query-params"])]),_:1})]),_:1},8,["modelValue"])]),_:1})],64)}}})});export{Ea as __tla,U as default};
