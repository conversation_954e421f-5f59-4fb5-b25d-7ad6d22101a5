import{bd as ne,bn as De,f3 as ze,as as oe,cE as Ge,r as m,b as B,a as e,f4 as ie,bp as Oe,at as W,eD as Re,C as re,c0 as Qe,bb as je,c8 as We,cT as Fe,cs as Ke,d as J,bf as se,bQ as Xe,o as H,c as D,l as ue,w as U,H as F,g as A,a0 as L,b1 as K,i as X,br as ve,cP as Ye,a8 as Y,a3 as ce,a9 as Q,cQ as qe,aV as de,av as he,F as Je,k as Ue,t as Ze,bg as fe,b6 as et,f as tt,bt as at,bM as lt,bh as nt,bk as ot,__tla as it}from"./index-BUSn51wb.js";let pe,me,rt=Promise.all([(()=>{try{return it}catch{}})()]).then(async()=>{const ge=ne({initialIndex:{type:Number,default:0},height:{type:String,default:""},trigger:{type:String,values:["hover","click"],default:"hover"},autoplay:{type:Boolean,default:!0},interval:{type:Number,default:3e3},indicatorPosition:{type:String,values:["","none","outside"],default:""},arrow:{type:String,values:["always","hover","never"],default:"hover"},type:{type:String,values:["","card"],default:""},loop:{type:Boolean,default:!0},direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},pauseOnHover:{type:Boolean,default:!0},motionBlur:{type:Boolean,default:!1}}),ye={change:(l,V)=>[l,V].every(De)},Z=Symbol("carouselContextKey"),be=(l,V,r)=>{const{children:n,addChild:$,removeChild:C}=ze(oe(),"ElCarouselItem"),T=Ge(),o=m(-1),g=m(null),_=m(!1),y=m(),x=m(0),k=m(!0),f=m(!0),d=m(!1),z=B(()=>l.arrow!=="never"&&!e(S)),b=B(()=>n.value.some(t=>t.props.label.toString().length>0)),h=B(()=>l.type==="card"),S=B(()=>l.direction==="vertical"),G=B(()=>l.height!=="auto"?{height:l.height}:{height:`${x.value}px`,overflow:"hidden"}),O=ie(t=>{u(t)},300,{trailing:!0}),R=ie(t=>{(function(v){l.trigger==="hover"&&v!==o.value&&(o.value=v,f.value||(d.value=!0))})(t)},300);function E(){g.value&&(clearInterval(g.value),g.value=null)}function P(){l.interval<=0||!l.autoplay||g.value||(g.value=setInterval(()=>p(),l.interval))}const p=()=>{f.value||(d.value=!0),f.value=!1,o.value<n.value.length-1?o.value=o.value+1:l.loop&&(o.value=0)};function u(t){if(f.value||(d.value=!0),f.value=!1,Oe(t)){const s=n.value.filter(i=>i.props.name===t);s.length>0&&(t=n.value.indexOf(s[0]))}if(t=Number(t),Number.isNaN(t)||t!==Math.floor(t))return;const v=n.value.length,M=o.value;o.value=t<0?l.loop?v-1:0:t>=v?l.loop?0:v-1:t,M===o.value&&I(M),a()}function I(t){n.value.forEach((v,M)=>{v.translateItem(M,o.value,t)})}function a(){E(),l.pauseOnHover||P()}W(()=>o.value,(t,v)=>{I(v),k.value&&(t%=2,v%=2),v>-1&&V("change",t,v)}),W(()=>l.autoplay,t=>{t?P():E()}),W(()=>l.loop,()=>{u(o.value)}),W(()=>l.interval,()=>{a()});const w=Re();return re(()=>{W(()=>n.value,()=>{n.value.length>0&&u(l.initialIndex)},{immediate:!0}),w.value=Qe(y.value,()=>{I()}),P()}),je(()=>{E(),y.value&&w.value&&w.value.stop()}),We(Z,{root:y,isCardType:h,isVertical:S,items:n,loop:l.loop,addItem:$,removeItem:C,setActiveItem:u,setContainerHeight:function(t){l.height==="auto"&&(x.value=t)}}),{root:y,activeIndex:o,arrowDisplay:z,hasLabel:b,hover:_,isCardType:h,isTransitioning:d,items:n,isVertical:S,containerStyle:G,isItemsTwoLength:k,handleButtonEnter:function(t){e(S)||n.value.forEach((v,M)=>{t===function(s,i){var c,N,q,te;const j=e(n),ae=j.length;if(ae===0||!s.states.inStage)return!1;const Ne=i+1,Ae=i-1,le=ae-1,Le=j[le].states.active,Ve=j[0].states.active,$e=(N=(c=j[Ne])==null?void 0:c.states)==null?void 0:N.active,Pe=(te=(q=j[Ae])==null?void 0:q.states)==null?void 0:te.active;return i===le&&Ve||$e?"left":!!(i===0&&Le||Pe)&&"right"}(v,M)&&(v.states.hover=!0)})},handleTransitionEnd:function(){d.value=!1},handleButtonLeave:function(){e(S)||n.value.forEach(t=>{t.states.hover=!1})},handleIndicatorClick:function(t){t!==o.value&&(f.value||(d.value=!0)),o.value=t},handleMouseEnter:function(){_.value=!0,l.pauseOnHover&&E()},handleMouseLeave:function(){_.value=!1,P()},setActiveItem:u,prev:function(){u(o.value-1)},next:function(){u(o.value+1)},PlaceholderItem:function(){var t;const v=(t=T.default)==null?void 0:t.call(T);if(!v)return null;const M=Fe(v).filter(s=>Ke(s)&&s.type.name==="ElCarouselItem");return(M==null?void 0:M.length)===2&&l.loop&&!h.value?(k.value=!0,M):(k.value=!1,null)},isTwoLengthShow:t=>!k.value||(o.value<=1?t<=1:t>1),throttledArrowClick:O,throttledIndicatorHover:R}},Ie=["aria-label"],we=["aria-label"],Ce=["onMouseenter","onClick"],ke=["aria-label"],Se={key:0},_e={key:3,xmlns:"http://www.w3.org/2000/svg",version:"1.1",style:{display:"none"}},xe=[A("defs",null,[A("filter",{id:"elCarouselHorizontal"},[A("feGaussianBlur",{in:"SourceGraphic",stdDeviation:"12,0"})]),A("filter",{id:"elCarouselVertical"},[A("feGaussianBlur",{in:"SourceGraphic",stdDeviation:"0,10"})])],-1)],Me=J({name:"ElCarousel"});var Ee=fe(J({...Me,props:ge,emits:ye,setup(l,{expose:V,emit:r}){const n=l,{root:$,activeIndex:C,arrowDisplay:T,hasLabel:o,hover:g,isCardType:_,items:y,isVertical:x,containerStyle:k,handleButtonEnter:f,handleButtonLeave:d,isTransitioning:z,handleIndicatorClick:b,handleMouseEnter:h,handleMouseLeave:S,handleTransitionEnd:G,setActiveItem:O,prev:R,next:E,PlaceholderItem:P,isTwoLengthShow:p,throttledArrowClick:u,throttledIndicatorHover:I}=be(n,r),a=se("carousel"),{t:w}=Xe(),t=B(()=>{const s=[a.b(),a.m(n.direction)];return e(_)&&s.push(a.m("card")),s}),v=B(()=>{const s=[a.e("container")];return n.motionBlur&&e(z)&&s.push(e(x)?`${a.namespace.value}-transitioning-vertical`:`${a.namespace.value}-transitioning`),s}),M=B(()=>{const s=[a.e("indicators"),a.em("indicators",n.direction)];return e(o)&&s.push(a.em("indicators","labels")),n.indicatorPosition==="outside"&&s.push(a.em("indicators","outside")),e(x)&&s.push(a.em("indicators","right")),s});return V({setActiveItem:O,prev:R,next:E}),(s,i)=>(H(),D("div",{ref_key:"root",ref:$,class:L(e(t)),onMouseenter:i[7]||(i[7]=K((...c)=>e(h)&&e(h)(...c),["stop"])),onMouseleave:i[8]||(i[8]=K((...c)=>e(S)&&e(S)(...c),["stop"]))},[e(T)?(H(),ue(ce,{key:0,name:"carousel-arrow-left",persisted:""},{default:U(()=>[F(A("button",{type:"button",class:L([e(a).e("arrow"),e(a).em("arrow","left")]),"aria-label":e(w)("el.carousel.leftArrow"),onMouseenter:i[0]||(i[0]=c=>e(f)("left")),onMouseleave:i[1]||(i[1]=(...c)=>e(d)&&e(d)(...c)),onClick:i[2]||(i[2]=K(c=>e(u)(e(C)-1),["stop"]))},[X(e(ve),null,{default:U(()=>[X(e(Ye))]),_:1})],42,Ie),[[Y,(s.arrow==="always"||e(g))&&(n.loop||e(C)>0)]])]),_:1})):Q("v-if",!0),e(T)?(H(),ue(ce,{key:1,name:"carousel-arrow-right",persisted:""},{default:U(()=>[F(A("button",{type:"button",class:L([e(a).e("arrow"),e(a).em("arrow","right")]),"aria-label":e(w)("el.carousel.rightArrow"),onMouseenter:i[3]||(i[3]=c=>e(f)("right")),onMouseleave:i[4]||(i[4]=(...c)=>e(d)&&e(d)(...c)),onClick:i[5]||(i[5]=K(c=>e(u)(e(C)+1),["stop"]))},[X(e(ve),null,{default:U(()=>[X(e(qe))]),_:1})],42,we),[[Y,(s.arrow==="always"||e(g))&&(n.loop||e(C)<e(y).length-1)]])]),_:1})):Q("v-if",!0),A("div",{class:L(e(v)),style:he(e(k)),onTransitionend:i[6]||(i[6]=(...c)=>e(G)&&e(G)(...c))},[X(e(P)),de(s.$slots,"default")],38),s.indicatorPosition!=="none"?(H(),D("ul",{key:2,class:L(e(M))},[(H(!0),D(Je,null,Ue(e(y),(c,N)=>F((H(),D("li",{key:N,class:L([e(a).e("indicator"),e(a).em("indicator",s.direction),e(a).is("active",N===e(C))]),onMouseenter:q=>e(I)(N),onClick:K(q=>e(b)(N),["stop"])},[A("button",{class:L(e(a).e("button")),"aria-label":e(w)("el.carousel.indicator",{index:N+1})},[e(o)?(H(),D("span",Se,Ze(c.props.label),1)):Q("v-if",!0)],10,ke)],42,Ce)),[[Y,e(p)(N)]])),128))],2)):Q("v-if",!0),n.motionBlur?(H(),D("svg",_e,xe)):Q("v-if",!0)],34))}}),[["__file","carousel.vue"]]);const Te=ne({name:{type:String,default:""},label:{type:[String,Number],default:""}}),Be=(l,V)=>{const r=et(Z),n=oe(),$=.83,C=m(),T=m(!1),o=m(0),g=m(1),_=m(!1),y=m(!1),x=m(!1),k=m(!1),{isCardType:f,isVertical:d}=r,z=(b,h,S)=>{var G;const O=e(f),R=(G=r.items.value.length)!=null?G:Number.NaN,E=b===h;O||lt(S)||(k.value=E||b===S),!E&&R>2&&r.loop&&(b=function(p,u,I){const a=I-1,w=I/2;return u===0&&p===a?-1:u===a&&p===0?I:p<u-1&&u-p>=w?I+1:p>u+1&&p-u>=w?-2:p}(b,h,R));const P=e(d);_.value=E,O?(x.value=Math.round(Math.abs(b-h))<=1,o.value=function(p,u){var I,a;const w=e(d)?((I=r.root.value)==null?void 0:I.offsetHeight)||0:((a=r.root.value)==null?void 0:a.offsetWidth)||0;return x.value?w*(1.17*(p-u)+1)/4:p<u?-1.83*w/4:3.83*w/4}(b,h),g.value=e(_)?1:$):o.value=function(p,u,I){const a=r.root.value;return a?((I?a.offsetHeight:a.offsetWidth)||0)*(p-u):0}(b,h,P),y.value=!0,E&&C.value&&r.setContainerHeight(C.value.offsetHeight)};return re(()=>{r.addItem({props:l,states:tt({hover:T,translate:o,scale:g,active:_,ready:y,inStage:x,animating:k}),uid:n.uid,translateItem:z})}),at(()=>{r.removeItem(n.uid)}),{carouselItemRef:C,active:_,animating:k,hover:T,inStage:x,isVertical:d,translate:o,isCardType:f,scale:g,ready:y,handleItemClick:function(){if(r&&e(f)){const b=r.items.value.findIndex(({uid:h})=>h===n.uid);r.setActiveItem(b)}}}},He=J({name:"ElCarouselItem"});var ee=fe(J({...He,props:Te,setup(l){const V=l,r=se("carousel"),{carouselItemRef:n,active:$,animating:C,hover:T,inStage:o,isVertical:g,translate:_,isCardType:y,scale:x,ready:k,handleItemClick:f}=Be(V),d=B(()=>[r.e("item"),r.is("active",$.value),r.is("in-stage",o.value),r.is("hover",T.value),r.is("animating",C.value),{[r.em("item","card")]:y.value,[r.em("item","card-vertical")]:y.value&&g.value}]),z=B(()=>({transform:[`${"translate"+(e(g)?"Y":"X")}(${e(_)}px)`,`scale(${e(x)})`].join(" ")}));return(b,h)=>F((H(),D("div",{ref_key:"carouselItemRef",ref:n,class:L(e(d)),style:he(e(z)),onClick:h[0]||(h[0]=(...S)=>e(f)&&e(f)(...S))},[e(y)?F((H(),D("div",{key:0,class:L(e(r).e("mask"))},null,2)),[[Y,!e($)]]):Q("v-if",!0),de(b.$slots,"default")],6)),[[Y,e(k)]])}}),[["__file","carousel-item.vue"]]);me=nt(Ee,{CarouselItem:ee}),pe=ot(ee)});export{pe as E,rt as __tla,me as a};
