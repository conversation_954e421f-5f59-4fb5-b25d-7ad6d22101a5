import{bf as ae,at as q,ay as w,cy as H,c7 as F,K as B,c2 as L,b,d as I,J as G,as as se,b_ as oe,cz as J,c4 as ne,c1 as ce,b6 as re,cA as de,r as Q,f as P,C as ue,h as T,bg as ie,__tla as he}from"./index-BUSn51wb.js";let W,pe=Promise.all([(()=>{try{return he}catch{}})()]).then(async()=>{const X=I({extends:G,setup(t,n){const r=G.setup(t,n);delete r.selectOptionClick;const p=se().proxy;return w(()=>{r.select.states.cachedOptions.get(p.value)||r.select.onOptionCreate(p)}),r},methods:{selectOptionClick(){this.$el.parentElement.click()}}});function D(t){return t||t===0}function z(t){return Array.isArray(t)&&t.length}function K(t){return Array.isArray(t)?t:D(t)?[t]:[]}function O(t,n,r,p,c){for(let d=0;d<t.length;d++){const f=t[d];if(n(f,d,t,c))return p?p(f,d,t,c):f;{const E=r(f);if(z(E)){const V=O(E,n,r,p,f);if(V)return V}}}}function R(t,n,r,p){for(let c=0;c<t.length;c++){const d=t[c];n(d,c,t,p);const f=r(d);z(f)&&R(f,n,r,d)}}var Y=I({props:{data:{type:Array,default:()=>[]}},setup(t){const n=re(de);return q(()=>t.data,()=>{var r;t.data.forEach(c=>{n.states.cachedOptions.has(c.value)||n.states.cachedOptions.set(c.value,c)});const p=((r=n.selectRef)==null?void 0:r.querySelectorAll("input"))||[];Array.from(p).includes(document.activeElement)||n.setSelected()},{flush:"post",immediate:!0}),()=>{}}});const Z=I({name:"ElTreeSelect",inheritAttrs:!1,props:{...B.props,...J.props,cacheData:{type:Array,default:()=>[]}},setup(t,n){const{slots:r,expose:p}=n,c=Q(),d=Q(),f=b(()=>t.nodeKey||t.valueKey||"value"),E=((e,{attrs:C,emit:M},{select:g,tree:m,key:v})=>{const _=ae("tree-select");return q(()=>e.data,()=>{e.filterable&&w(()=>{var u,a;(a=m.value)==null||a.filter((u=g.value)==null?void 0:u.states.inputValue)})},{flush:"post"}),{...H(F(e),Object.keys(B.props)),...C,"onUpdate:modelValue":u=>M(L,u),valueKey:v,popperClass:b(()=>{const u=[_.e("popper")];return e.popperClass&&u.push(e.popperClass),u.join(" ")}),filterMethod:(u="")=>{var a;e.filterMethod?e.filterMethod(u):e.remoteMethod?e.remoteMethod(u):(a=m.value)==null||a.filter(u)}}})(t,n,{select:c,tree:d,key:f}),{cacheOptions:V,...ee}=((e,{attrs:C,slots:M,emit:g},{select:m,tree:v,key:_})=>{q(()=>e.modelValue,()=>{e.showCheckbox&&w(()=>{const l=v.value;l&&!oe(l.getCheckedKeys(),K(e.modelValue))&&l.setCheckedKeys(K(e.modelValue))})},{immediate:!0,deep:!0});const u=b(()=>({value:_.value,label:"label",children:"children",disabled:"disabled",isLeaf:"isLeaf",...e.props})),a=(l,s)=>{var o;const i=u.value[l];return ce(i)?i(s,(o=v.value)==null?void 0:o.getNode(a("value",s))):s[i]},$=K(e.modelValue).map(l=>O(e.data||[],s=>a("value",s)===l,s=>a("children",s),(s,o,i,y)=>y&&a("value",y))).filter(l=>D(l)),te=b(()=>{if(!e.renderAfterExpand&&!e.lazy)return[];const l=[];return R(e.data.concat(e.cacheData),s=>{const o=a("value",s);l.push({value:o,currentLabel:a("label",s),isDisabled:a("disabled",s)})},s=>a("children",s)),l});return{...H(F(e),Object.keys(J.props)),...C,nodeKey:_,expandOnClickNode:b(()=>!e.checkStrictly&&e.expandOnClickNode),defaultExpandedKeys:b(()=>e.defaultExpandedKeys?e.defaultExpandedKeys.concat($):$),renderContent:(l,{node:s,data:o,store:i})=>l(X,{value:a("value",o),label:a("label",o),disabled:a("disabled",o)},e.renderContent?()=>e.renderContent(l,{node:s,data:o,store:i}):M.default?()=>M.default({node:s,data:o,store:i}):void 0),filterNodeMethod:(l,s,o)=>e.filterNodeMethod?e.filterNodeMethod(l,s,o):!l||new RegExp(ne(l),"i").test(a("label",s)||""),onNodeClick:(l,s,o)=>{var i,y,N,x;if((i=C.onNodeClick)==null||i.call(C,l,s,o),!e.showCheckbox||!e.checkOnClickNode){if(e.showCheckbox||!e.checkStrictly&&!s.isLeaf)e.expandOnClickNode&&o.proxy.handleExpandIconClick();else if(!a("disabled",l)){const S=(y=m.value)==null?void 0:y.states.options.get(a("value",l));(N=m.value)==null||N.handleOptionSelect(S)}(x=m.value)==null||x.focus()}},onCheck:(l,s)=>{var o;if(!e.showCheckbox)return;const i=a("value",l),y={};R([v.value.store.root],h=>y[h.key]=h,h=>h.childNodes);const N=s.checkedKeys,x=e.multiple?K(e.modelValue).filter(h=>!(h in y)&&!N.includes(h)):[],S=x.concat(N);if(e.checkStrictly)g(L,e.multiple?S:S.includes(i)?i:void 0);else if(e.multiple)g(L,x.concat(v.value.getCheckedKeys(!0)));else{const h=O([l],k=>!z(a("children",k))&&!a("disabled",k),k=>a("children",k)),j=h?a("value",h):void 0,le=D(e.modelValue)&&!!O([l],k=>a("value",k)===e.modelValue,k=>a("children",k));g(L,j===e.modelValue||le?void 0:j)}w(()=>{var h;const j=K(e.modelValue);v.value.setCheckedKeys(j),(h=C.onCheck)==null||h.call(C,l,{checkedKeys:v.value.getCheckedKeys(),checkedNodes:v.value.getCheckedNodes(),halfCheckedKeys:v.value.getHalfCheckedKeys(),halfCheckedNodes:v.value.getHalfCheckedNodes()})}),(o=m.value)==null||o.focus()},cacheOptions:te}})(t,n,{select:c,tree:d,key:f}),U=P({});return p(U),ue(()=>{Object.assign(U,{...H(d.value,["filter","updateKeyChildren","getCheckedNodes","setCheckedNodes","getCheckedKeys","setCheckedKeys","setChecked","getHalfCheckedNodes","getHalfCheckedKeys","getCurrentKey","getCurrentNode","setCurrentKey","setCurrentNode","getNode","remove","append","insertBefore","insertAfter"]),...H(c.value,["focus","blur"])})}),()=>T(B,P({...E,ref:e=>c.value=e}),{...r,default:()=>[T(Y,{data:V.value}),T(J,P({...ee,ref:e=>d.value=e}))]})}});var A=ie(Z,[["__file","tree-select.vue"]]);A.install=t=>{t.component(A.name,A)},W=A});export{W as E,pe as __tla};
