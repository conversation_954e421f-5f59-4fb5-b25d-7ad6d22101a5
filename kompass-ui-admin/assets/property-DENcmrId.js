import{d as w,o as i,l as v,w as t,i as e,a as o,c as s,F as c,k as z,a9 as W,cf as k,L as C,_ as D,aM as P,aN as j,an as F,O as L,__tla as M}from"./index-BUSn51wb.js";import{_ as N,__tla as O}from"./index-11u3nuTi.js";import{u as q,__tla as A}from"./util-Dyp86Gv2.js";import"./color-BN7ZL7BD.js";import{__tla as B}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as E}from"./Qrcode-CP7wmJi0.js";import{__tla as G}from"./el-text-CIwNlU-U.js";import{__tla as H}from"./IFrame.vue_vue_type_script_setup_true_lang-silYuU_p.js";import{__tla as I}from"./el-card-CJbXGyyg.js";import{__tla as J}from"./vuedraggable.umd-BTL7hPHv.js";import{__tla as K}from"./el-collapse-item-B_QvnH_b.js";let y,Q=Promise.all([(()=>{try{return M}catch{}})(),(()=>{try{return O}catch{}})(),(()=>{try{return A}catch{}})(),(()=>{try{return B}catch{}})(),(()=>{try{return E}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return H}catch{}})(),(()=>{try{return I}catch{}})(),(()=>{try{return J}catch{}})(),(()=>{try{return K}catch{}})()]).then(async()=>{y=w({name:"DividerProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(h,{emit:f}){const b=h,x=f,{formData:l}=q(b.modelValue,x),g=[{icon:"vaadin:line-h",text:"\u5B9E\u7EBF",type:"solid"},{icon:"tabler:line-dashed",text:"\u865A\u7EBF",type:"dashed"},{icon:"tabler:line-dotted",text:"\u70B9\u7EBF",type:"dotted"},{icon:"entypo:progress-empty",text:"\u65E0",type:"none"}];return(R,r)=>{const m=k,n=C,d=D,_=P,p=j,u=F,V=N,T=L;return i(),v(T,{"label-width":"80px",model:o(l)},{default:t(()=>[e(n,{label:"\u9AD8\u5EA6",prop:"height"},{default:t(()=>[e(m,{modelValue:o(l).height,"onUpdate:modelValue":r[0]||(r[0]=a=>o(l).height=a),min:1,max:100,"show-input":"","input-size":"small"},null,8,["modelValue"])]),_:1}),e(n,{label:"\u9009\u62E9\u6837\u5F0F",prop:"borderType"},{default:t(()=>[e(u,{modelValue:o(l).borderType,"onUpdate:modelValue":r[1]||(r[1]=a=>o(l).borderType=a)},{default:t(()=>[(i(),s(c,null,z(g,(a,U)=>e(p,{placement:"top",key:U,content:a.text},{default:t(()=>[e(_,{label:a.type},{default:t(()=>[e(d,{icon:a.icon},null,8,["icon"])]),_:2},1032,["label"])]),_:2},1032,["content"])),64))]),_:1},8,["modelValue"])]),_:1}),o(l).borderType!=="none"?(i(),s(c,{key:0},[e(n,{label:"\u7EBF\u5BBD",prop:"lineWidth"},{default:t(()=>[e(m,{modelValue:o(l).lineWidth,"onUpdate:modelValue":r[2]||(r[2]=a=>o(l).lineWidth=a),min:1,max:30,"show-input":"","input-size":"small"},null,8,["modelValue"])]),_:1}),e(n,{label:"\u5DE6\u53F3\u8FB9\u8DDD",prop:"paddingType"},{default:t(()=>[e(u,{modelValue:o(l).paddingType,"onUpdate:modelValue":r[3]||(r[3]=a=>o(l).paddingType=a)},{default:t(()=>[e(p,{content:"\u65E0\u8FB9\u8DDD",placement:"top"},{default:t(()=>[e(_,{label:"none"},{default:t(()=>[e(d,{icon:"tabler:box-padding"})]),_:1})]),_:1}),e(p,{content:"\u5DE6\u53F3\u7559\u8FB9",placement:"top"},{default:t(()=>[e(_,{label:"horizontal"},{default:t(()=>[e(d,{icon:"vaadin:padding"})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"\u989C\u8272"},{default:t(()=>[e(V,{modelValue:o(l).lineColor,"onUpdate:modelValue":r[4]||(r[4]=a=>o(l).lineColor=a)},null,8,["modelValue"])]),_:1})],64)):W("",!0)]),_:1},8,["model"])}}})});export{Q as __tla,y as default};
