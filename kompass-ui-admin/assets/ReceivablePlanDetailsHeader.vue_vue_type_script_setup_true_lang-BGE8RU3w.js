import{_ as y,__tla as h}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{d as x,o as i,c as E,g as _,i as e,w as a,t,aV as j,j as r,a as c,dX as n,l as f,F as k,s as w,E as N,__tla as g}from"./index-BUSn51wb.js";import{E as T,a as C,__tla as D}from"./el-descriptions-item-dD3qa0ub.js";import{E as F,__tla as H}from"./el-text-CIwNlU-U.js";import{f as R,__tla as V}from"./formatTime-DWdBpgsM.js";let b,X=Promise.all([(()=>{try{return h}catch{}})(),(()=>{try{return g}catch{}})(),(()=>{try{return D}catch{}})(),(()=>{try{return H}catch{}})(),(()=>{try{return V}catch{}})()]).then(async()=>{let u,o;u={class:"flex items-start justify-between"},o={class:"text-xl font-bold"},b=x({__name:"ReceivablePlanDetailsHeader",props:{receivablePlan:{}},setup:$=>(l,q)=>{const m=w,v=N,s=T,d=F,p=C,P=y;return i(),E(k,null,[_("div",null,[_("div",u,[_("div",null,[e(v,null,{default:a(()=>[e(m,null,{default:a(()=>[_("span",o,"\u7B2C "+t(l.receivablePlan.period)+" \u671F",1)]),_:1})]),_:1})]),_("div",null,[j(l.$slots,"default")])])]),e(P,{class:"mt-10px"},{default:a(()=>[e(p,{column:5,direction:"vertical"},{default:a(()=>[e(s,{label:"\u5BA2\u6237\u540D\u79F0"},{default:a(()=>[r(t(l.receivablePlan.customerName),1)]),_:1}),e(s,{label:"\u5408\u540C\u7F16\u53F7"},{default:a(()=>[r(t(l.receivablePlan.contractNo),1)]),_:1}),e(s,{label:"\u8BA1\u5212\u56DE\u6B3E\u91D1\u989D"},{default:a(()=>[r(t(c(n)(l.receivablePlan.price)),1)]),_:1}),e(s,{label:"\u8BA1\u5212\u56DE\u6B3E\u65E5\u671F"},{default:a(()=>[r(t(c(R)(l.receivablePlan.returnTime)),1)]),_:1}),e(s,{label:"\u5B9E\u9645\u56DE\u6B3E\u91D1\u989D"},{default:a(()=>[l.receivablePlan.receivable?(i(),f(d,{key:0},{default:a(()=>[r(t(c(n)(l.receivablePlan.receivable.price)),1)]),_:1})):(i(),f(d,{key:1},{default:a(()=>[r(t(c(n)(0)),1)]),_:1}))]),_:1})]),_:1})]),_:1})],64)}})});export{b as _,X as __tla};
