import{d as ua,r as c,C as pa,T as da,o as _,c as H,i as a,w as t,g as r,a as l,F as q,k as _a,V as ma,G as B,l as k,j as f,H as J,t as K,aJ as fa,_ as ha,E as xa,s as va,M as ga,L as ya,J as ba,K as wa,N as Ca,O as ka,P as Ta,Q as Na,R as Oa,__tla as Ra}from"./index-BUSn51wb.js";import{_ as Ia,__tla as Sa}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as Ua,__tla as za}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{E as Ma,__tla as Pa}from"./el-image-BjHZRFih.js";import{E as Va,__tla as ja}from"./el-avatar-Da2TGjmj.js";import{_ as Aa,__tla as Ea}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as Da,__tla as La}from"./CountTo.vue_vue_type_script_setup_true_lang-BUNK7cmG.js";import{_ as Ya,__tla as Fa}from"./index-COobLwz-.js";import{_ as Ga,g as Ha,a as qa,__tla as Ba}from"./CombinationRecordListDialog.vue_vue_type_script_setup_true_lang-BAh6LRXF.js";import{h as Ja,d as T,__tla as Ka}from"./formatTime-DWdBpgsM.js";import{__tla as Qa}from"./index-Cch5e1V0.js";import"./color-BN7ZL7BD.js";import{__tla as Wa}from"./el-card-CJbXGyyg.js";import{__tla as Xa}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";let Q,Za=Promise.all([(()=>{try{return Ra}catch{}})(),(()=>{try{return Sa}catch{}})(),(()=>{try{return za}catch{}})(),(()=>{try{return Pa}catch{}})(),(()=>{try{return ja}catch{}})(),(()=>{try{return Ea}catch{}})(),(()=>{try{return La}catch{}})(),(()=>{try{return Fa}catch{}})(),(()=>{try{return Ba}catch{}})(),(()=>{try{return Ka}catch{}})(),(()=>{try{return Qa}catch{}})(),(()=>{try{return Wa}catch{}})(),(()=>{try{return Xa}catch{}})()]).then(async()=>{let N,O,R,I,S,U,z,M,P,V,j,A,E;N={class:"flex items-center"},O={class:"h-[50px] w-[50px] flex items-center justify-center",style:{color:"rgb(24 144 255)","background-color":"rgb(24 144 255 / 10%)"}},R={class:"ml-[20px]"},I=r("div",{class:"mb-8px text-14px text-gray-400"},"\u53C2\u4E0E\u4EBA\u6570(\u4E2A)",-1),S={class:"flex items-center"},U={class:"h-[50px] w-[50px] flex items-center justify-center",style:{color:"rgb(162 119 255)","background-color":"rgb(162 119 255 / 10%)"}},z={class:"ml-[20px]"},M=r("div",{class:"mb-8px text-14px text-gray-400"},"\u6210\u56E2\u6570\u91CF(\u4E2A)",-1),P={class:"flex items-center"},V={class:"h-[50px] w-[50px] flex items-center justify-center",style:{color:"rgb(162 119 255)","background-color":"rgb(162 119 255 / 10%)"}},j={class:"ml-[20px]"},A=r("div",{class:"mb-8px text-14px text-gray-400"},"\u865A\u62DF\u6210\u56E2(\u4E2A)",-1),E={class:"align-middle"},Q=ua({name:"PromotionCombinationRecord",__name:"index",setup($a){const n=c({status:void 0,createTime:void 0,pageSize:10,pageNo:1}),D=c(),L=c(),h=c(!0),Y=c(0),x=c([]),v=async()=>{h.value=!0;try{const g=await Ha(n.value);x.value=g.list,Y.value=g.total}finally{h.value=!1}},m=c({successCount:0,userCount:0,virtualGroupCount:0}),F=()=>{n.value.pageNo=1,v()},W=()=>{D.value.resetFields(),F()};return pa(async()=>{await(async()=>{m.value=await qa()})(),await v()}),(g,o)=>{const X=Ya,p=ha,y=Da,d=Aa,b=xa,Z=va,$=ga,w=ya,aa=ba,ea=wa,C=Ca,ta=ka,s=Ta,la=Va,ra=Ma,sa=Ua,na=Na,oa=Ia,ia=da("hasPermi"),ca=Oa;return _(),H(q,null,[a(X,{title:"\u3010\u8425\u9500\u3011\u62FC\u56E2\u6D3B\u52A8",url:"https://doc.iocoder.cn/mall/promotion-combination/"}),a(Z,{gutter:12},{default:t(()=>[a(b,{span:6},{default:t(()=>[a(d,{class:"h-[110px] pb-0!"},{default:t(()=>[r("div",N,[r("div",O,[a(p,{size:23,icon:"fa:user-times"})]),r("div",R,[I,a(y,{duration:2600,"end-val":l(m).userCount,"start-val":0,class:"text-20px"},null,8,["end-val"])])])]),_:1})]),_:1}),a(b,{span:6},{default:t(()=>[a(d,{class:"h-[110px]"},{default:t(()=>[r("div",S,[r("div",U,[a(p,{size:23,icon:"fa:user-plus"})]),r("div",z,[M,a(y,{duration:2600,"end-val":l(m).successCount,"start-val":0,class:"text-20px"},null,8,["end-val"])])])]),_:1})]),_:1}),a(b,{span:6},{default:t(()=>[a(d,{class:"h-[110px]"},{default:t(()=>[r("div",P,[r("div",V,[a(p,{size:23,icon:"fa:user-plus"})]),r("div",j,[A,a(y,{duration:2600,"end-val":l(m).virtualGroupCount,"start-val":0,class:"text-20px"},null,8,["end-val"])])])]),_:1})]),_:1})]),_:1}),a(d,null,{default:t(()=>[a(ta,{ref_key:"queryFormRef",ref:D,inline:!0,model:l(n),class:"-mb-15px","label-width":"68px"},{default:t(()=>[a(w,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:t(()=>[a($,{modelValue:l(n).createTime,"onUpdate:modelValue":o[0]||(o[0]=e=>l(n).createTime=e),shortcuts:l(Ja),class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","shortcuts"])]),_:1}),a(w,{label:"\u62FC\u56E2\u72B6\u6001",prop:"status"},{default:t(()=>[a(ea,{modelValue:l(n).status,"onUpdate:modelValue":o[1]||(o[1]=e=>l(n).status=e),class:"!w-240px",clearable:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(_(!0),H(q,null,_a(l(ma)(l(B).PROMOTION_COMBINATION_RECORD_STATUS),(e,u)=>(_(),k(aa,{key:u,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(w,null,{default:t(()=>[a(C,{onClick:F},{default:t(()=>[a(p,{class:"mr-5px",icon:"ep:search"}),f(" \u641C\u7D22 ")]),_:1}),a(C,{onClick:W},{default:t(()=>[a(p,{class:"mr-5px",icon:"ep:refresh"}),f(" \u91CD\u7F6E ")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),a(d,null,{default:t(()=>[J((_(),k(na,{data:l(x)},{default:t(()=>[a(s,{align:"center",label:"\u7F16\u53F7",prop:"id","min-width":"50"}),a(s,{align:"center",label:"\u5934\u50CF",prop:"avatar","min-width":"80"},{default:t(e=>[a(la,{src:e.row.avatar},null,8,["src"])]),_:1}),a(s,{align:"center",label:"\u6635\u79F0",prop:"nickname","min-width":"100"}),a(s,{align:"center",label:"\u5F00\u56E2\u56E2\u957F",prop:"headId","min-width":"100"},{default:t(({row:e})=>{var u;return[f(K(e.headId?(u=l(x).find(i=>i.id===e.headId))==null?void 0:u.nickname:e.nickname),1)]}),_:1}),a(s,{formatter:l(T),align:"center",label:"\u5F00\u56E2\u65F6\u95F4",prop:"startTime",width:"180"},null,8,["formatter"]),a(s,{align:"center",label:"\u62FC\u56E2\u5546\u54C1",prop:"type","show-overflow-tooltip":"","min-width":"300"},{defaul:t(({row:e})=>[a(ra,{src:e.picUrl,class:"mr-5px h-30px w-30px align-middle",onClick:u=>{return i=e.picUrl,void fa({urlList:[i]});var i}},null,8,["src","onClick"]),r("span",E,K(e.spuName),1)]),_:1}),a(s,{align:"center",label:"\u51E0\u4EBA\u56E2",prop:"userSize","min-width":"100"}),a(s,{align:"center",label:"\u53C2\u4E0E\u4EBA\u6570",prop:"userCount","min-width":"100"}),a(s,{formatter:l(T),align:"center",label:"\u53C2\u56E2\u65F6\u95F4",prop:"createTime",width:"180"},null,8,["formatter"]),a(s,{formatter:l(T),align:"center",label:"\u7ED3\u675F\u65F6\u95F4",prop:"endTime",width:"180"},null,8,["formatter"]),a(s,{align:"center",label:"\u62FC\u56E2\u72B6\u6001",prop:"status","min-width":"150"},{default:t(e=>[a(sa,{type:l(B).PROMOTION_COMBINATION_RECORD_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),a(s,{align:"center",fixed:"right",label:"\u64CD\u4F5C"},{default:t(e=>[J((_(),k(C,{link:"",type:"primary",onClick:u=>{var G;return i=e.row,void((G=L.value)==null?void 0:G.open(i.headId||i.id));var i}},{default:t(()=>[f(" \u67E5\u770B\u62FC\u56E2 ")]),_:2},1032,["onClick"])),[[ia,["promotion:combination-record:query"]]])]),_:1})]),_:1},8,["data"])),[[ca,l(h)]]),a(oa,{limit:l(n).pageSize,"onUpdate:limit":o[2]||(o[2]=e=>l(n).pageSize=e),page:l(n).pageNo,"onUpdate:page":o[3]||(o[3]=e=>l(n).pageNo=e),total:l(Y),onPagination:v},null,8,["limit","page","total"])]),_:1}),a(Ga,{ref_key:"combinationRecordListRef",ref:L},null,512)],64)}}})});export{Za as __tla,Q as default};
