import{d as U,r as n,o as _,l as f,w as s,i as o,j as v,a,H as q,c as I,k as j,F as H,y as J,I as L,J as N,K as O,L as P,Z,O as z,N as A,R as B,__tla as E}from"./index-BUSn51wb.js";import{_ as G,__tla as M}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{b as Q,c as S,__tla as W}from"./index-OMcsJcjy.js";let g,X=Promise.all([(()=>{try{return E}catch{}})(),(()=>{try{return M}catch{}})(),(()=>{try{return W}catch{}})()]).then(async()=>{g=U({__name:"TaskReturnForm",emits:["success"],setup(Y,{expose:k,emit:b}){const m=L(),r=n(!1),u=n(!1),l=n({id:"",targetTaskDefinitionKey:void 0,reason:""}),h=n({targetTaskDefinitionKey:[{required:!0,message:"\u5FC5\u987B\u9009\u62E9\u56DE\u9000\u8282\u70B9",trigger:"change"}],reason:[{required:!0,message:"\u56DE\u9000\u7406\u7531\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),d=n(),c=n([]);k({open:async i=>{if(c.value=await Q(i),c.value.length===0)return m.warning("\u5F53\u524D\u6CA1\u6709\u53EF\u56DE\u9000\u7684\u8282\u70B9"),!1;r.value=!0,w(),l.value.id=i}});const K=b,V=async()=>{if(d&&await d.value.validate()){u.value=!0;try{await S(l.value),m.success("\u56DE\u9000\u6210\u529F"),r.value=!1,K("success")}finally{u.value=!1}}},w=()=>{var i;l.value={id:"",targetTaskDefinitionKey:void 0,reason:""},(i=d.value)==null||i.resetFields()};return(i,t)=>{const D=N,T=O,y=P,x=Z,C=z,p=A,F=G,R=B;return _(),f(F,{modelValue:a(r),"onUpdate:modelValue":t[3]||(t[3]=e=>J(r)?r.value=e:null),title:"\u56DE\u9000\u4EFB\u52A1",width:"500"},{footer:s(()=>[o(p,{disabled:a(u),type:"primary",onClick:V},{default:s(()=>[v("\u786E \u5B9A")]),_:1},8,["disabled"]),o(p,{onClick:t[2]||(t[2]=e=>r.value=!1)},{default:s(()=>[v("\u53D6 \u6D88")]),_:1})]),default:s(()=>[q((_(),f(C,{ref_key:"formRef",ref:d,model:a(l),rules:a(h),"label-width":"110px"},{default:s(()=>[o(y,{label:"\u9000\u56DE\u8282\u70B9",prop:"targetTaskDefinitionKey"},{default:s(()=>[o(T,{modelValue:a(l).targetTaskDefinitionKey,"onUpdate:modelValue":t[0]||(t[0]=e=>a(l).targetTaskDefinitionKey=e),clearable:"",style:{width:"100%"}},{default:s(()=>[(_(!0),I(H,null,j(a(c),e=>(_(),f(D,{key:e.taskDefinitionKey,label:e.name,value:e.taskDefinitionKey},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(y,{label:"\u56DE\u9000\u7406\u7531",prop:"reason"},{default:s(()=>[o(x,{modelValue:a(l).reason,"onUpdate:modelValue":t[1]||(t[1]=e=>a(l).reason=e),clearable:"",placeholder:"\u8BF7\u8F93\u5165\u56DE\u9000\u7406\u7531"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[R,a(u)]])]),_:1},8,["modelValue"])}}})});export{g as _,X as __tla};
