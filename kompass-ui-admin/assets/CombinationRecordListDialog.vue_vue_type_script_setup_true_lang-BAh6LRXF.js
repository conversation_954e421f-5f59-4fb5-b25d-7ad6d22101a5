import{by as f,d as U,r as _,f as z,o as h,l as y,w as r,i as a,H as D,a as e,j as V,t as A,G as j,y as E,P as L,ax as M,Q as k,R as B,__tla as G}from"./index-BUSn51wb.js";import{_ as H,__tla as Q}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{_ as q,__tla as F}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as J,__tla as K}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as W,__tla as X}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{E as Y,__tla as Z}from"./el-avatar-Da2TGjmj.js";import{d as w,__tla as $}from"./formatTime-DWdBpgsM.js";let b,v,p,aa=Promise.all([(()=>{try{return G}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return F}catch{}})(),(()=>{try{return K}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return Z}catch{}})(),(()=>{try{return $}catch{}})()]).then(async()=>{p=async u=>await f.get({url:"/promotion/combination-record/page",params:u}),v=async()=>await f.get({url:"/promotion/combination-record/get-summary"}),b=U({name:"CombinationRecordListDialog",__name:"CombinationRecordListDialog",setup(u,{expose:I}){const m=_(!0),c=_(0),d=_([]),o=z({pageNo:1,pageSize:10,headId:void 0}),s=_(!1);I({open:async i=>{s.value=!0,o.headId=i,await g()}});const g=async()=>{m.value=!0;try{const i=await p(o);d.value=i.list,c.value=i.total}finally{m.value=!1}};return(i,n)=>{const l=L,N=Y,O=M,R=W,T=k,S=J,x=q,C=H,P=B;return h(),y(C,{modelValue:e(s),"onUpdate:modelValue":n[2]||(n[2]=t=>E(s)?s.value=t:null),title:"\u62FC\u56E2\u5217\u8868",width:"950"},{default:r(()=>[a(x,null,{default:r(()=>[D((h(),y(T,{data:e(d)},{default:r(()=>[a(l,{align:"center",label:"\u7F16\u53F7",prop:"id","min-width":"50"}),a(l,{align:"center",label:"\u5934\u50CF",prop:"avatar","min-width":"80"},{default:r(t=>[a(N,{src:t.row.avatar},null,8,["src"])]),_:1}),a(l,{align:"center",label:"\u6635\u79F0",prop:"nickname","min-width":"100"}),a(l,{align:"center",label:"\u5F00\u56E2\u56E2\u957F",prop:"headId","min-width":"100"},{default:r(({row:t})=>[a(O,null,{default:r(()=>[V(A(t.headId===0?"\u56E2\u957F":"\u56E2\u5458"),1)]),_:2},1024)]),_:1}),a(l,{formatter:e(w),align:"center",label:"\u53C2\u56E2\u65F6\u95F4",prop:"createTime",width:"180"},null,8,["formatter"]),a(l,{formatter:e(w),align:"center",label:"\u7ED3\u675F\u65F6\u95F4",prop:"endTime",width:"180"},null,8,["formatter"]),a(l,{align:"center",label:"\u62FC\u56E2\u72B6\u6001",prop:"status","min-width":"150"},{default:r(t=>[a(R,{type:e(j).PROMOTION_COMBINATION_RECORD_STATUS,value:t.row.status},null,8,["type","value"])]),_:1})]),_:1},8,["data"])),[[P,e(m)]]),a(S,{limit:e(o).pageSize,"onUpdate:limit":n[0]||(n[0]=t=>e(o).pageSize=t),page:e(o).pageNo,"onUpdate:page":n[1]||(n[1]=t=>e(o).pageNo=t),total:e(c),onPagination:g},null,8,["limit","page","total"])]),_:1})]),_:1},8,["modelValue"])}}})});export{b as _,aa as __tla,v as a,p as g};
