import{d as w,b as x,r as T,at as I,a as _,o as M,l as j,w as l,i as e,j as s,y as c,_ as N,s as P,z as R,A as k,B as z,__tla as A}from"./index-BUSn51wb.js";import{N as B,R as n,c as V,T as S,__tla as W}from"./TabNews-CeL3Heyg.js";import{_ as q,__tla as C}from"./TabText.vue_vue_type_script_setup_true_lang-60j5SrWe.js";import D,{__tla as E}from"./TabImage-BHh9EaXQ.js";import F,{__tla as G}from"./TabVoice-BSMaiVPu.js";import H,{__tla as J}from"./TabVideo-CqCZb4l-.js";import{_ as K,__tla as L}from"./TabMusic.vue_vue_type_script_setup_true_lang-D0UZjdHy.js";import{__tla as O}from"./main-DwQbyLY9.js";import{__tla as Q}from"./el-image-BjHZRFih.js";import{__tla as X}from"./main-DvybYriQ.js";import{__tla as Y}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{__tla as Z}from"./index-Cch5e1V0.js";import{__tla as $}from"./main-CG5euiEw.js";import{__tla as ee}from"./main.vue_vue_type_script_setup_true_lang-Bi4NZ0QE.js";import{__tla as ae}from"./index-C4ZN3JCQ.js";import{__tla as le}from"./index-Cqwyhbsb.js";import{__tla as te}from"./formatTime-DWdBpgsM.js";import{__tla as _e}from"./useUpload-gjof4KYU.js";let y,re=Promise.all([(()=>{try{return A}catch{}})(),(()=>{try{return W}catch{}})(),(()=>{try{return C}catch{}})(),(()=>{try{return E}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return J}catch{}})(),(()=>{try{return L}catch{}})(),(()=>{try{return O}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return Z}catch{}})(),(()=>{try{return $}catch{}})(),(()=>{try{return ee}catch{}})(),(()=>{try{return ae}catch{}})(),(()=>{try{return le}catch{}})(),(()=>{try{return te}catch{}})(),(()=>{try{return _e}catch{}})()]).then(async()=>{y=z(w({name:"WxReplySelect",__name:"main",props:{modelValue:{},newsType:{default:()=>B.Published}},emits:["update:modelValue"],setup(h,{expose:v,emit:g}){const p=h,b=g,a=x({get:()=>p.modelValue,set:u=>b("update:modelValue",u)}),f=new Map,i=T(p.modelValue.type||n.Text);return I(i,(u,t)=>{if(t===void 0||u===void 0)return;f.set(t,_(a));const o=f.get(u);if(o)a.value=o;else{let m=V(a);m.type=u,a.value=m}},{immediate:!0}),v({clear:()=>{a.value=V(a)}}),(u,t)=>{const o=N,m=P,d=R,U=k;return M(),j(U,{type:"border-card",modelValue:_(i),"onUpdate:modelValue":t[6]||(t[6]=r=>c(i)?i.value=r:null)},{default:l(()=>[e(d,{name:_(n).Text},{label:l(()=>[e(m,{align:"middle"},{default:l(()=>[e(o,{icon:"ep:document"}),s(" \u6587\u672C")]),_:1})]),default:l(()=>[e(q,{modelValue:_(a).content,"onUpdate:modelValue":t[0]||(t[0]=r=>_(a).content=r)},null,8,["modelValue"])]),_:1},8,["name"]),e(d,{name:_(n).Image},{label:l(()=>[e(m,{align:"middle"},{default:l(()=>[e(o,{icon:"ep:picture",class:"mr-5px"}),s(" \u56FE\u7247")]),_:1})]),default:l(()=>[e(D,{modelValue:_(a),"onUpdate:modelValue":t[1]||(t[1]=r=>c(a)?a.value=r:null)},null,8,["modelValue"])]),_:1},8,["name"]),e(d,{name:_(n).Voice},{label:l(()=>[e(m,{align:"middle"},{default:l(()=>[e(o,{icon:"ep:phone"}),s(" \u8BED\u97F3")]),_:1})]),default:l(()=>[e(F,{modelValue:_(a),"onUpdate:modelValue":t[2]||(t[2]=r=>c(a)?a.value=r:null)},null,8,["modelValue"])]),_:1},8,["name"]),e(d,{name:_(n).Video},{label:l(()=>[e(m,{align:"middle"},{default:l(()=>[e(o,{icon:"ep:share"}),s(" \u89C6\u9891")]),_:1})]),default:l(()=>[e(H,{modelValue:_(a),"onUpdate:modelValue":t[3]||(t[3]=r=>c(a)?a.value=r:null)},null,8,["modelValue"])]),_:1},8,["name"]),e(d,{name:_(n).News},{label:l(()=>[e(m,{align:"middle"},{default:l(()=>[e(o,{icon:"ep:reading"}),s(" \u56FE\u6587")]),_:1})]),default:l(()=>[e(S,{modelValue:_(a),"onUpdate:modelValue":t[4]||(t[4]=r=>c(a)?a.value=r:null),"news-type":u.newsType},null,8,["modelValue","news-type"])]),_:1},8,["name"]),e(d,{name:_(n).Music},{label:l(()=>[e(m,{align:"middle"},{default:l(()=>[e(o,{icon:"ep:service"}),s("\u97F3\u4E50")]),_:1})]),default:l(()=>[e(K,{modelValue:_(a),"onUpdate:modelValue":t[5]||(t[5]=r=>c(a)?a.value=r:null)},null,8,["modelValue"])]),_:1},8,["name"])]),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-1d5dc1ea"]])});export{re as __tla,y as default};
