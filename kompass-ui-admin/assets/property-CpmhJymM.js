import{_ as D,__tla as F}from"./ComponentContainerProperty-U-5gd_f0.js";import{d as L,o as _,l as h,w as d,i as l,a,g as m,j as i,a9 as M,c as N,F as O,_ as Z,aM as q,aN as A,an as Q,L as W,ai as X,ce as G,cl as H,Z as J,cf as K,O as Y,__tla as $}from"./index-BUSn51wb.js";import{_ as j,__tla as ll}from"./index-11u3nuTi.js";import{E as el,__tla as al}from"./el-card-CJbXGyyg.js";import{u as tl,__tla as ol}from"./util-Dyp86Gv2.js";import dl,{__tla as rl}from"./SpuShowcase-HyjHBJVE.js";import"./color-BN7ZL7BD.js";import{__tla as ul}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as sl}from"./Qrcode-CP7wmJi0.js";import{__tla as ml}from"./el-text-CIwNlU-U.js";import{__tla as nl}from"./IFrame.vue_vue_type_script_setup_true_lang-silYuU_p.js";import{__tla as pl}from"./vuedraggable.umd-BTL7hPHv.js";import{__tla as _l}from"./el-collapse-item-B_QvnH_b.js";import{__tla as il}from"./el-image-BjHZRFih.js";import{__tla as cl}from"./spu-CW3JGweV.js";import{__tla as fl}from"./SpuTableSelect.vue_vue_type_script_setup_true_lang-lBhZ06IN.js";import{__tla as Vl}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{__tla as hl}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{__tla as bl}from"./index-Cch5e1V0.js";import{__tla as yl}from"./el-tree-select-CBuha0HW.js";import"./tree-BMa075Oj.js";import{__tla as gl}from"./category-WzWM3ODe.js";let k,wl=Promise.all([(()=>{try{return F}catch{}})(),(()=>{try{return $}catch{}})(),(()=>{try{return ll}catch{}})(),(()=>{try{return al}catch{}})(),(()=>{try{return ol}catch{}})(),(()=>{try{return rl}catch{}})(),(()=>{try{return ul}catch{}})(),(()=>{try{return sl}catch{}})(),(()=>{try{return ml}catch{}})(),(()=>{try{return nl}catch{}})(),(()=>{try{return pl}catch{}})(),(()=>{try{return _l}catch{}})(),(()=>{try{return il}catch{}})(),(()=>{try{return cl}catch{}})(),(()=>{try{return fl}catch{}})(),(()=>{try{return Vl}catch{}})(),(()=>{try{return hl}catch{}})(),(()=>{try{return bl}catch{}})(),(()=>{try{return yl}catch{}})(),(()=>{try{return gl}catch{}})()]).then(async()=>{let b,y,g,w,U,x;b={class:"flex gap-8px"},y={class:"flex gap-8px"},g={class:"flex gap-8px"},w={class:"flex gap-8px"},U={class:"flex gap-8px"},x={class:"flex gap-8px"},k=L({name:"ProductCardProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(v,{emit:P}){const R=v,E=P,{formData:e}=tl(R.modelValue,E);return(Ul,t)=>{const n=el,c=Z,p=q,f=A,B=Q,r=W,u=j,s=X,T=G,C=H,z=J,V=K,I=Y,S=D;return _(),h(S,{modelValue:a(e).style,"onUpdate:modelValue":t[24]||(t[24]=o=>a(e).style=o)},{default:d(()=>[l(I,{"label-width":"80px",model:a(e)},{default:d(()=>[l(n,{header:"\u5546\u54C1\u5217\u8868",class:"property-group",shadow:"never"},{default:d(()=>[l(dl,{modelValue:a(e).spuIds,"onUpdate:modelValue":t[0]||(t[0]=o=>a(e).spuIds=o)},null,8,["modelValue"])]),_:1}),l(n,{header:"\u5546\u54C1\u6837\u5F0F",class:"property-group",shadow:"never"},{default:d(()=>[l(r,{label:"\u5E03\u5C40",prop:"type"},{default:d(()=>[l(B,{modelValue:a(e).layoutType,"onUpdate:modelValue":t[1]||(t[1]=o=>a(e).layoutType=o)},{default:d(()=>[l(f,{class:"item",content:"\u5355\u5217\u5927\u56FE",placement:"bottom"},{default:d(()=>[l(p,{label:"oneColBigImg"},{default:d(()=>[l(c,{icon:"fluent:text-column-one-24-filled"})]),_:1})]),_:1}),l(f,{class:"item",content:"\u5355\u5217\u5C0F\u56FE",placement:"bottom"},{default:d(()=>[l(p,{label:"oneColSmallImg"},{default:d(()=>[l(c,{icon:"fluent:text-column-two-left-24-filled"})]),_:1})]),_:1}),l(f,{class:"item",content:"\u53CC\u5217",placement:"bottom"},{default:d(()=>[l(p,{label:"twoCol"},{default:d(()=>[l(c,{icon:"fluent:text-column-two-24-filled"})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}),l(r,{label:"\u5546\u54C1\u540D\u79F0",prop:"fields.name.show"},{default:d(()=>[m("div",b,[l(u,{modelValue:a(e).fields.name.color,"onUpdate:modelValue":t[2]||(t[2]=o=>a(e).fields.name.color=o)},null,8,["modelValue"]),l(s,{modelValue:a(e).fields.name.show,"onUpdate:modelValue":t[3]||(t[3]=o=>a(e).fields.name.show=o)},null,8,["modelValue"])])]),_:1}),l(r,{label:"\u5546\u54C1\u7B80\u4ECB",prop:"fields.introduction.show"},{default:d(()=>[m("div",y,[l(u,{modelValue:a(e).fields.introduction.color,"onUpdate:modelValue":t[4]||(t[4]=o=>a(e).fields.introduction.color=o)},null,8,["modelValue"]),l(s,{modelValue:a(e).fields.introduction.show,"onUpdate:modelValue":t[5]||(t[5]=o=>a(e).fields.introduction.show=o)},null,8,["modelValue"])])]),_:1}),l(r,{label:"\u5546\u54C1\u4EF7\u683C",prop:"fields.price.show"},{default:d(()=>[m("div",g,[l(u,{modelValue:a(e).fields.price.color,"onUpdate:modelValue":t[6]||(t[6]=o=>a(e).fields.price.color=o)},null,8,["modelValue"]),l(s,{modelValue:a(e).fields.price.show,"onUpdate:modelValue":t[7]||(t[7]=o=>a(e).fields.price.show=o)},null,8,["modelValue"])])]),_:1}),l(r,{label:"\u5E02\u573A\u4EF7",prop:"fields.marketPrice.show"},{default:d(()=>[m("div",w,[l(u,{modelValue:a(e).fields.marketPrice.color,"onUpdate:modelValue":t[8]||(t[8]=o=>a(e).fields.marketPrice.color=o)},null,8,["modelValue"]),l(s,{modelValue:a(e).fields.marketPrice.show,"onUpdate:modelValue":t[9]||(t[9]=o=>a(e).fields.marketPrice.show=o)},null,8,["modelValue"])])]),_:1}),l(r,{label:"\u5546\u54C1\u9500\u91CF",prop:"fields.salesCount.show"},{default:d(()=>[m("div",U,[l(u,{modelValue:a(e).fields.salesCount.color,"onUpdate:modelValue":t[10]||(t[10]=o=>a(e).fields.salesCount.color=o)},null,8,["modelValue"]),l(s,{modelValue:a(e).fields.salesCount.show,"onUpdate:modelValue":t[11]||(t[11]=o=>a(e).fields.salesCount.show=o)},null,8,["modelValue"])])]),_:1}),l(r,{label:"\u5546\u54C1\u5E93\u5B58",prop:"fields.stock.show"},{default:d(()=>[m("div",x,[l(u,{modelValue:a(e).fields.stock.color,"onUpdate:modelValue":t[12]||(t[12]=o=>a(e).fields.stock.color=o)},null,8,["modelValue"]),l(s,{modelValue:a(e).fields.stock.show,"onUpdate:modelValue":t[13]||(t[13]=o=>a(e).fields.stock.show=o)},null,8,["modelValue"])])]),_:1})]),_:1}),l(n,{header:"\u89D2\u6807",class:"property-group",shadow:"never"},{default:d(()=>[l(r,{label:"\u89D2\u6807",prop:"badge.show"},{default:d(()=>[l(T,{modelValue:a(e).badge.show,"onUpdate:modelValue":t[14]||(t[14]=o=>a(e).badge.show=o)},null,8,["modelValue"])]),_:1}),a(e).badge.show?(_(),h(r,{key:0,label:"\u89D2\u6807",prop:"badge.imgUrl"},{default:d(()=>[l(C,{modelValue:a(e).badge.imgUrl,"onUpdate:modelValue":t[15]||(t[15]=o=>a(e).badge.imgUrl=o),height:"44px",width:"72px"},{tip:d(()=>[i(" \u5EFA\u8BAE\u5C3A\u5BF8\uFF1A36 * 22 ")]),_:1},8,["modelValue"])]),_:1})):M("",!0)]),_:1}),l(n,{header:"\u6309\u94AE",class:"property-group",shadow:"never"},{default:d(()=>[l(r,{label:"\u6309\u94AE\u7C7B\u578B",prop:"btnBuy.type"},{default:d(()=>[l(B,{modelValue:a(e).btnBuy.type,"onUpdate:modelValue":t[16]||(t[16]=o=>a(e).btnBuy.type=o)},{default:d(()=>[l(p,{label:"text"},{default:d(()=>[i("\u6587\u5B57")]),_:1}),l(p,{label:"img"},{default:d(()=>[i("\u56FE\u7247")]),_:1})]),_:1},8,["modelValue"])]),_:1}),a(e).btnBuy.type==="text"?(_(),N(O,{key:0},[l(r,{label:"\u6309\u94AE\u6587\u5B57",prop:"btnBuy.text"},{default:d(()=>[l(z,{modelValue:a(e).btnBuy.text,"onUpdate:modelValue":t[17]||(t[17]=o=>a(e).btnBuy.text=o)},null,8,["modelValue"])]),_:1}),l(r,{label:"\u5DE6\u4FA7\u80CC\u666F",prop:"btnBuy.bgBeginColor"},{default:d(()=>[l(u,{modelValue:a(e).btnBuy.bgBeginColor,"onUpdate:modelValue":t[18]||(t[18]=o=>a(e).btnBuy.bgBeginColor=o)},null,8,["modelValue"])]),_:1}),l(r,{label:"\u53F3\u4FA7\u80CC\u666F",prop:"btnBuy.bgEndColor"},{default:d(()=>[l(u,{modelValue:a(e).btnBuy.bgEndColor,"onUpdate:modelValue":t[19]||(t[19]=o=>a(e).btnBuy.bgEndColor=o)},null,8,["modelValue"])]),_:1})],64)):(_(),h(r,{key:1,label:"\u56FE\u7247",prop:"btnBuy.imgUrl"},{default:d(()=>[l(C,{modelValue:a(e).btnBuy.imgUrl,"onUpdate:modelValue":t[20]||(t[20]=o=>a(e).btnBuy.imgUrl=o),height:"56px",width:"56px"},{tip:d(()=>[i(" \u5EFA\u8BAE\u5C3A\u5BF8\uFF1A56 * 56 ")]),_:1},8,["modelValue"])]),_:1}))]),_:1}),l(n,{header:"\u5546\u54C1\u6837\u5F0F",class:"property-group",shadow:"never"},{default:d(()=>[l(r,{label:"\u4E0A\u5706\u89D2",prop:"borderRadiusTop"},{default:d(()=>[l(V,{modelValue:a(e).borderRadiusTop,"onUpdate:modelValue":t[21]||(t[21]=o=>a(e).borderRadiusTop=o),max:100,min:0,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"])]),_:1}),l(r,{label:"\u4E0B\u5706\u89D2",prop:"borderRadiusBottom"},{default:d(()=>[l(V,{modelValue:a(e).borderRadiusBottom,"onUpdate:modelValue":t[22]||(t[22]=o=>a(e).borderRadiusBottom=o),max:100,min:0,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"])]),_:1}),l(r,{label:"\u95F4\u9694",prop:"space"},{default:d(()=>[l(V,{modelValue:a(e).space,"onUpdate:modelValue":t[23]||(t[23]=o=>a(e).space=o),max:100,min:0,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}})});export{wl as __tla,k as default};
