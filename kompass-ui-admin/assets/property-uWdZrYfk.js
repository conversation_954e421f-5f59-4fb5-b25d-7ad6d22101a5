import{_ as U,__tla as V}from"./ComponentContainerProperty-U-5gd_f0.js";import{d as b,o as g,l as w,w as o,i as r,a,j as x,cf as v,L as P,cn as j,cl as z,ce as D,O as L,__tla as O}from"./index-BUSn51wb.js";import{u as k,__tla as q}from"./util-Dyp86Gv2.js";import{__tla as A}from"./el-card-CJbXGyyg.js";import{__tla as B}from"./index-11u3nuTi.js";import"./color-BN7ZL7BD.js";import{__tla as C}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as E}from"./Qrcode-CP7wmJi0.js";import{__tla as F}from"./el-text-CIwNlU-U.js";import{__tla as G}from"./IFrame.vue_vue_type_script_setup_true_lang-silYuU_p.js";import{__tla as H}from"./vuedraggable.umd-BTL7hPHv.js";import{__tla as I}from"./el-collapse-item-B_QvnH_b.js";let m,J=Promise.all([(()=>{try{return V}catch{}})(),(()=>{try{return O}catch{}})(),(()=>{try{return q}catch{}})(),(()=>{try{return A}catch{}})(),(()=>{try{return B}catch{}})(),(()=>{try{return C}catch{}})(),(()=>{try{return E}catch{}})(),(()=>{try{return F}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return H}catch{}})(),(()=>{try{return I}catch{}})()]).then(async()=>{m=b({name:"VideoPlayerProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(s,{emit:u}){const p=s,d=u,{formData:t}=k(p.modelValue,d);return(K,l)=>{const i=v,_=P,n=j,c=z,y=D,f=L,h=U;return g(),w(h,{modelValue:a(t).style,"onUpdate:modelValue":l[4]||(l[4]=e=>a(t).style=e)},{style:o(()=>[r(_,{label:"\u9AD8\u5EA6",prop:"height"},{default:o(()=>[r(i,{modelValue:a(t).style.height,"onUpdate:modelValue":l[0]||(l[0]=e=>a(t).style.height=e),max:500,min:100,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"])]),_:1})]),default:o(()=>[r(f,{"label-width":"80px",model:a(t)},{default:o(()=>[r(_,{label:"\u4E0A\u4F20\u89C6\u9891",prop:"videoUrl"},{default:o(()=>[r(n,{modelValue:a(t).videoUrl,"onUpdate:modelValue":l[1]||(l[1]=e=>a(t).videoUrl=e),"file-type":["mp4"],limit:1,"file-size":100,class:"min-w-80px"},null,8,["modelValue"])]),_:1}),r(_,{label:"\u4E0A\u4F20\u5C01\u9762",prop:"posterUrl"},{default:o(()=>[r(c,{modelValue:a(t).posterUrl,"onUpdate:modelValue":l[2]||(l[2]=e=>a(t).posterUrl=e),draggable:"false",height:"80px",width:"100%",class:"min-w-80px"},{tip:o(()=>[x(" \u5EFA\u8BAE\u5BBD\u5EA6750 ")]),_:1},8,["modelValue"])]),_:1}),r(_,{label:"\u81EA\u52A8\u64AD\u653E",prop:"autoplay"},{default:o(()=>[r(y,{modelValue:a(t).autoplay,"onUpdate:modelValue":l[3]||(l[3]=e=>a(t).autoplay=e)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}})});export{J as __tla,m as default};
