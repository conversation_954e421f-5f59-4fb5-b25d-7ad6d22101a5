import{_ as t,__tla as r}from"./ReceivableDetailsInfo.vue_vue_type_script_setup_true_lang-CuP3AIhK.js";import{__tla as _}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{__tla as a}from"./index-BUSn51wb.js";import{__tla as l}from"./el-card-CJbXGyyg.js";import{__tla as o}from"./el-collapse-item-B_QvnH_b.js";import{__tla as m}from"./el-descriptions-item-dD3qa0ub.js";import{__tla as c}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import"./color-BN7ZL7BD.js";import{__tla as e}from"./formatTime-DWdBpgsM.js";let s=Promise.all([(()=>{try{return r}catch{}})(),(()=>{try{return _}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return m}catch{}})(),(()=>{try{return c}catch{}})(),(()=>{try{return e}catch{}})()]).then(async()=>{});export{s as __tla,t as default};
