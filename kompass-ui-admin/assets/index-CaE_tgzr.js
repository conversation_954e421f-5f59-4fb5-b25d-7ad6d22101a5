import{by as t,__tla as d}from"./index-BUSn51wb.js";let r,c,e,s,l,p,u,o=Promise.all([(()=>{try{return d}catch{}})()]).then(async()=>{c=async a=>await t.get({url:"/crm/product/page",params:a}),p=async()=>await t.get({url:"/crm/product/simple-list"}),r=async a=>await t.get({url:"/crm/product/get?id="+a}),e=async a=>await t.post({url:"/crm/product/create",data:a}),u=async a=>await t.put({url:"/crm/product/update",data:a}),s=async a=>await t.delete({url:"/crm/product/delete?id="+a}),l=async a=>await t.download({url:"/crm/product/export-excel",params:a})});export{o as __tla,r as a,c as b,e as c,s as d,l as e,p as g,u};
