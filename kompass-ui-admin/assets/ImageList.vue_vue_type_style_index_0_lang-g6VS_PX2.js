import{d as z,I as B,f as U,r as i,C as A,bt as G,o as v,c as k,i as y,w as R,j as D,g as N,F as S,k as q,a as n,l as F,af as H,__tla as J}from"./index-BUSn51wb.js";import{E as K,__tla as Q}from"./el-card-CJbXGyyg.js";import{_ as T,__tla as V}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{I as _,__tla as W}from"./index-Cjd1fP7g.js";import X,{__tla as Y}from"./ImageDetail-DklFMFvy.js";import Z,{__tla as $}from"./ImageCard-BvRL6nWd.js";import{A as E}from"./constants-C0I8ujwj.js";import{d as aa}from"./download-e0EdwhTv.js";let O,ta=Promise.all([(()=>{try{return J}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return V}catch{}})(),(()=>{try{return W}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return $}catch{}})()]).then(async()=>{let p;p={class:"task-image-pagination"},O=z({__name:"ImageList",emits:["onRegeneration"],setup(ea,{expose:P,emit:b}){const f=B(),r=U({pageNo:1,pageSize:10}),w=i(0),o=i([]),u=i(),h=i(),d=i({}),m=i(),g=i(!1),I=i(0),j=async()=>{g.value=!1},c=async()=>{try{u.value=H.service({target:h.value,text:"\u52A0\u8F7D\u4E2D..."});const{list:t,total:a}=await _.getImagePageMy(r);o.value=t,w.value=a;const s={};o.value.forEach(e=>{e.status===E.IN_PROGRESS&&(s[e.id]=e)}),d.value=s}finally{u.value&&(u.value.close(),u.value=null)}},x=async(t,a)=>t==="more"?(I.value=a.id,void await(async()=>{g.value=!0})()):t==="delete"?(await f.confirm("\u662F\u5426\u5220\u9664\u7167\u7247?"),await _.deleteImageMy(a.id),await c(),void f.success("\u5220\u9664\u6210\u529F!")):void(t!=="download"?t!=="regeneration"||await L("onRegeneration",a):await aa.image(a.picUrl)),C=async(t,a)=>{const s={id:a.id,customId:t.customId};await _.midjourneyAction(s),await c()};P({getImageList:c});const L=b;return A(async()=>{await c(),m.value=setInterval(async()=>{await(async()=>{const t=Object.keys(d.value).map(Number);if(t.length==0)return;const a=await _.getImageListMyByIds(t),s={};a.forEach(e=>{if(e.status===E.IN_PROGRESS)s[e.id]=e;else{const l=o.value.findIndex(M=>e.id===M.id);l>=0&&(o.value[l]=e)}}),d.value=s})()},3e3)}),G(async()=>{m.value&&clearInterval(m.value)}),(t,a)=>{const s=T,e=K;return v(),k(S,null,[y(e,{class:"dr-task","body-class":"task-card",shadow:"never"},{header:R(()=>[D("\u7ED8\u753B\u4EFB\u52A1")]),default:R(()=>[N("div",{class:"task-image-list",ref_key:"imageListRef",ref:h},[(v(!0),k(S,null,q(n(o),l=>(v(),F(Z,{key:l.id,detail:l,onOnBtnClick:x,onOnMjBtnClick:C},null,8,["detail"]))),128))],512),N("div",p,[y(s,{total:n(w),page:n(r).pageNo,"onUpdate:page":a[0]||(a[0]=l=>n(r).pageNo=l),limit:n(r).pageSize,"onUpdate:limit":a[1]||(a[1]=l=>n(r).pageSize=l),onPagination:c},null,8,["total","page","limit"])])]),_:1}),y(X,{show:n(g),id:n(I),onHandleDrawerClose:j},null,8,["show","id"])],64)}}})});export{O as _,ta as __tla};
