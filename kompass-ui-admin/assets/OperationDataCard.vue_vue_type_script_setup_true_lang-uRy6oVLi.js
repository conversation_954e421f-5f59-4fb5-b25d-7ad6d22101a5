import{by as y,d as w,u as g,f as x,C as k,o as t,l as S,w as o,i as n,a as i,g as d,c as m,F as A,k as N,t as P,__tla as W}from"./index-BUSn51wb.js";import{E as C,__tla as U}from"./el-card-CJbXGyyg.js";import{_ as O,__tla as T}from"./CountTo.vue_vue_type_script_setup_true_lang-BUNK7cmG.js";import{a as F,__tla as D}from"./spu-CW3JGweV.js";import{g as I,__tla as b}from"./trade-Dv0eYeK8.js";import{C as j,__tla as B}from"./CardTitle-Dm4BG9kg.js";let p,E=Promise.all([(()=>{try{return W}catch{}})(),(()=>{try{return U}catch{}})(),(()=>{try{return T}catch{}})(),(()=>{try{return D}catch{}})(),(()=>{try{return b}catch{}})(),(()=>{try{return B}catch{}})()]).then(async()=>{let l,s,u;l={class:"flex flex-row flex-wrap items-center gap-8 p-4"},s=["onClick"],u={class:"text-center"},p=w({name:"OperationDataCard",__name:"OperationDataCard",setup(R){const _=g(),e=x({orderUndelivered:{name:"\u5F85\u53D1\u8D27\u8BA2\u5355",value:9,routerName:"TradeOrder"},orderAfterSaleApply:{name:"\u9000\u6B3E\u4E2D\u8BA2\u5355",value:4,routerName:"TradeAfterSale"},orderWaitePickUp:{name:"\u5F85\u6838\u9500\u8BA2\u5355",value:0,routerName:"TradeOrder"},productAlertStock:{name:"\u5E93\u5B58\u9884\u8B66",value:0,routerName:"ProductSpu"},productForSale:{name:"\u4E0A\u67B6\u5546\u54C1",value:0,routerName:"ProductSpu"},productInWarehouse:{name:"\u4ED3\u5E93\u5546\u54C1",value:0,routerName:"ProductSpu"},withdrawAuditing:{name:"\u63D0\u73B0\u5F85\u5BA1\u6838",value:0,routerName:"TradeBrokerageWithdraw"},rechargePrice:{name:"\u8D26\u6237\u5145\u503C",value:0,prefix:"\uFFE5",decimals:2,routerName:"PayWalletRecharge"}}),v=async()=>{const a=await(async()=>await y.get({url:"/statistics/pay/summary"}))();e.rechargePrice.value=a.rechargePrice};return k(()=>{(async()=>{const a=await I();e.orderUndelivered.value=a.undelivered,e.orderAfterSaleApply.value=a.afterSaleApply,e.orderWaitePickUp.value=a.pickUp,e.withdrawAuditing.value=a.auditingWithdraw})(),(async()=>{const a=await F();e.productForSale.value=a[0],e.productInWarehouse.value=a[1],e.productAlertStock.value=a[3]})(),v()}),(a,q)=>{const f=O,h=C;return t(),S(h,{shadow:"never"},{header:o(()=>[n(i(j),{title:"\u8FD0\u8425\u6570\u636E"})]),default:o(()=>[d("div",l,[(t(!0),m(A,null,N(i(e),r=>(t(),m("div",{key:r.name,class:"h-20 w-20% flex flex-col cursor-pointer items-center justify-center gap-2",onClick:z=>{return c=r.routerName,void _.push({name:c});var c}},[n(f,{prefix:r.prefix,"end-val":r.value,decimals:r.decimals,class:"text-3xl"},null,8,["prefix","end-val","decimals"]),d("span",u,P(r.name),1)],8,s))),128))])]),_:1})}}})});export{p as _,E as __tla};
