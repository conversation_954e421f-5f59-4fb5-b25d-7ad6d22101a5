import{_ as o,__tla as m}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as u,__tla as i}from"./IFrame.vue_vue_type_script_setup_true_lang-silYuU_p.js";import{_ as p,__tla as h}from"./index-COobLwz-.js";import{b as f,__tla as y}from"./index-BXfU_lLO.js";import{d,r as _,C as g,o as w,c as v,i as a,w as x,a as b,F as C,__tla as F}from"./index-BUSn51wb.js";import{__tla as I}from"./el-card-CJbXGyyg.js";let l,P=Promise.all([(()=>{try{return m}catch{}})(),(()=>{try{return i}catch{}})(),(()=>{try{return h}catch{}})(),(()=>{try{return y}catch{}})(),(()=>{try{return F}catch{}})(),(()=>{try{return I}catch{}})()]).then(async()=>{l=d({name:"InfraSwagger",__name:"index",setup(S){const s=_(!0),r=_("http://**************:48080/doc.html");return g(async()=>{try{const t=await f("url.swagger");t&&t.length>0&&(r.value=t)}finally{s.value=!1}}),(t,j)=>{const e=p,c=u,n=o;return w(),v(C,null,[a(e,{title:"\u63A5\u53E3\u6587\u6863",url:"https://doc.iocoder.cn/api-doc/"}),a(n,null,{default:x(()=>[a(c,{src:b(r)},null,8,["src"])]),_:1})],64)}}})});export{P as __tla,l as default};
