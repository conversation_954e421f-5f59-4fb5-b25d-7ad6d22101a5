import p,{__tla as m}from"./OrderItem-DUnNh_aP.js";import{e as o,__tla as y}from"./index-BQq32Shw.js";import{c as d,__tla as g}from"./concat-MbtHYl7y.js";import{d as v,r as _,f,b as h,o as i,c as w,k as x,l as H,a as I,F as N,__tla as b}from"./index-BUSn51wb.js";let n,k=Promise.all([(()=>{try{return m}catch{}})(),(()=>{try{return y}catch{}})(),(()=>{try{return g}catch{}})(),(()=>{try{return b}catch{}})()]).then(async()=>{n=v({name:"OrderBrowsingHistory",__name:"OrderBrowsingHistory",setup(z,{expose:u}){const e=_([]),r=_(0),a=f({pageNo:1,pageSize:10,userId:0}),c=h(()=>r.value>0&&Math.ceil(r.value/a.pageSize)===a.pageNo);return u({getHistoryList:async t=>{a.userId=t.userId;const s=await o(a);r.value=s.total,e.value=s.list},loadMore:async()=>{if(c.value)return;a.pageNo+=1;const t=await o(a);r.value=t.total,d(e.value,t.list)}}),(t,s)=>(i(!0),w(N,null,x(I(e),l=>(i(),H(p,{key:l.id,order:l,class:"mb-10px"},null,8,["order"]))),128))}})});export{n as _,k as __tla};
