import{by as a,__tla as u}from"./index-BUSn51wb.js";let r,s=Promise.all([(()=>{try{return u}catch{}})()]).then(async()=>{r={getPurchaseReturnPage:async e=>await a.get({url:"/erp/purchase-return/page",params:e}),getPurchaseReturn:async e=>await a.get({url:"/erp/purchase-return/get?id="+e}),createPurchaseReturn:async e=>await a.post({url:"/erp/purchase-return/create",data:e}),updatePurchaseReturn:async e=>await a.put({url:"/erp/purchase-return/update",data:e}),updatePurchaseReturnStatus:async(e,t)=>await a.put({url:"/erp/purchase-return/update-status",params:{id:e,status:t}}),deletePurchaseReturn:async e=>await a.delete({url:"/erp/purchase-return/delete",params:{ids:e.join(",")}}),exportPurchaseReturn:async e=>await a.download({url:"/erp/purchase-return/export-excel",params:e})}});export{r as P,s as __tla};
