import{_ as b,__tla as p}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{d as y,o as v,c as x,g as _,i as a,w as s,t as l,aV as h,j as r,a as i,dX as w,F as N,s as j,E,__tla as T}from"./index-BUSn51wb.js";import{E as g,a as P,__tla as B}from"./el-descriptions-item-dD3qa0ub.js";import{f as D,__tla as F}from"./formatTime-DWdBpgsM.js";let o,H=Promise.all([(()=>{try{return p}catch{}})(),(()=>{try{return T}catch{}})(),(()=>{try{return B}catch{}})(),(()=>{try{return F}catch{}})()]).then(async()=>{let u,n;u={class:"flex items-start justify-between"},n={class:"text-xl font-bold"},o=y({__name:"BusinessDetailsHeader",props:{business:{}},setup:U=>(t,V)=>{const c=j,d=E,e=g,m=P,f=b;return v(),x(N,null,[_("div",null,[_("div",u,[_("div",null,[a(d,null,{default:s(()=>[a(c,null,{default:s(()=>[_("span",n,l(t.business.name),1)]),_:1})]),_:1})]),_("div",null,[h(t.$slots,"default")])])]),a(f,{class:"mt-10px"},{default:s(()=>[a(m,{column:5,direction:"vertical"},{default:s(()=>[a(e,{label:"\u5BA2\u6237\u540D\u79F0"},{default:s(()=>[r(l(t.business.customerName),1)]),_:1}),a(e,{label:"\u5546\u673A\u91D1\u989D\uFF08\u5143\uFF09"},{default:s(()=>[r(l(i(w)(t.business.totalPrice)),1)]),_:1}),a(e,{label:"\u5546\u673A\u7EC4"},{default:s(()=>[r(l(t.business.statusTypeName),1)]),_:1}),a(e,{label:"\u8D1F\u8D23\u4EBA"},{default:s(()=>[r(l(t.business.ownerUserName),1)]),_:1}),a(e,{label:"\u521B\u5EFA\u65F6\u95F4"},{default:s(()=>[r(l(i(D)(t.business.createTime)),1)]),_:1})]),_:1})]),_:1})],64)}})});export{o as _,H as __tla};
