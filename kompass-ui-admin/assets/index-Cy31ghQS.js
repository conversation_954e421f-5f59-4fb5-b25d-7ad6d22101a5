import{by as a,__tla as r}from"./index-BUSn51wb.js";let t,c=Promise.all([(()=>{try{return r}catch{}})()]).then(async()=>{t={getTeacherCertificatePage:async e=>await a.get({url:"/als/teacher-certificate/page",params:e}),getTeacherCertificate:async e=>await a.get({url:"/als/teacher-certificate/get?id="+e}),getByTeacherId:async e=>await a.get({url:"/als/teacher-certificate/getByTeacherId?teacherId="+e}),createTeacherCertificate:async e=>await a.post({url:"/als/teacher-certificate/create",data:e}),createOrUpdateTeacherCertificate:async e=>await a.post({url:"/als/teacher-certificate/createOrUpdate",data:e}),updateTeacherCertificate:async e=>await a.put({url:"/als/teacher-certificate/update",data:e}),deleteTeacherCertificate:async e=>await a.delete({url:"/als/teacher-certificate/delete?id="+e}),exportTeacherCertificate:async e=>await a.download({url:"/als/teacher-certificate/export-excel",params:e})}});export{t as T,c as __tla};
