import{d as N,r as s,u as V,S as x,C as M,o as n,c as S,H as T,a as t,l as R,w as r,j,a9 as D,i as a,F,I as H,N as W,z as Q,A as q,E as G,R as J,__tla as K}from"./index-BUSn51wb.js";import{g as O,_ as U,__tla as X}from"./index-CpmUC5sy.js";import{u as Y,__tla as Z}from"./tagsView-BOOrxb3Q.js";import{b as $,__tla as tt}from"./index-Uo5NQqNb.js";import{_ as at,__tla as rt}from"./ReceivablePlanDetailsHeader.vue_vue_type_script_setup_true_lang-BGE8RU3w.js";import{_ as _t,__tla as lt}from"./ReceivablePlanDetailsInfo.vue_vue_type_script_setup_true_lang-DXJniEMs.js";import{_ as et,__tla as st}from"./PermissionList.vue_vue_type_script_setup_true_lang--VdCh_pH.js";import{B as w,__tla as it}from"./index-pKzyIv29.js";import{_ as ot,__tla as ct}from"./ReceivablePlanForm.vue_vue_type_script_setup_true_lang-BzUD040F.js";import{__tla as nt}from"./el-timeline-item-D8aDRTsd.js";import{__tla as ut}from"./formatTime-DWdBpgsM.js";import{__tla as mt}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{__tla as ft}from"./el-card-CJbXGyyg.js";import{__tla as pt}from"./el-descriptions-item-dD3qa0ub.js";import{__tla as yt}from"./el-text-CIwNlU-U.js";import{__tla as ht}from"./el-collapse-item-B_QvnH_b.js";import{__tla as dt}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import"./color-BN7ZL7BD.js";import{__tla as vt}from"./PermissionForm.vue_vue_type_script_setup_true_lang-oI9oCvWg.js";import{__tla as bt}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as Rt}from"./index-BYXzDB8j.js";import{__tla as wt}from"./index-CD52sTBY.js";import{__tla as zt}from"./index-DrB1WZUR.js";let z,Ct=Promise.all([(()=>{try{return K}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return Z}catch{}})(),(()=>{try{return tt}catch{}})(),(()=>{try{return rt}catch{}})(),(()=>{try{return lt}catch{}})(),(()=>{try{return st}catch{}})(),(()=>{try{return it}catch{}})(),(()=>{try{return ct}catch{}})(),(()=>{try{return nt}catch{}})(),(()=>{try{return ut}catch{}})(),(()=>{try{return mt}catch{}})(),(()=>{try{return ft}catch{}})(),(()=>{try{return pt}catch{}})(),(()=>{try{return yt}catch{}})(),(()=>{try{return ht}catch{}})(),(()=>{try{return dt}catch{}})(),(()=>{try{return vt}catch{}})(),(()=>{try{return bt}catch{}})(),(()=>{try{return Rt}catch{}})(),(()=>{try{return wt}catch{}})(),(()=>{try{return zt}catch{}})()]).then(async()=>{z=N({name:"CrmReceivablePlanDetail",__name:"index",setup(Et){const C=H(),u=s(0),i=s(!0),_=s({}),m=s(),f=async l=>{i.value=!0;try{_.value=await $(l),await E(l)}finally{i.value=!1}},p=s(),y=s([]),E=async l=>{if(!l)return;const e=await O({bizType:w.CRM_RECEIVABLE_PLAN,bizId:l});y.value=e.list},{delView:A}=Y(),{currentRoute:L}=V(),h=()=>{A(t(L))},{params:d}=x();return M(async()=>{if(!d.id)return C.warning("\u53C2\u6570\u9519\u8BEF\uFF0C\u56DE\u6B3E\u8BA1\u5212\u4E0D\u80FD\u4E3A\u7A7A\uFF01"),void h();u.value=d.id,await f(u.value)}),(l,e)=>{const g=W,o=Q,k=U,I=q,P=G,B=J;return n(),S(F,null,[T((n(),R(at,{"receivable-plan":t(_)},{default:r(()=>{var c;return[(c=t(m))!=null&&c.validateWrite?(n(),R(g,{key:0,onClick:e[0]||(e[0]=At=>{return v="update",b=t(_).id,void p.value.open(v,b);var v,b})},{default:r(()=>[j(" \u7F16\u8F91 ")]),_:1})):D("",!0)]}),_:1},8,["receivable-plan"])),[[B,t(i)]]),a(P,null,{default:r(()=>[a(I,null,{default:r(()=>[a(o,{label:"\u8BE6\u7EC6\u8D44\u6599"},{default:r(()=>[a(_t,{"receivable-plan":t(_)},null,8,["receivable-plan"])]),_:1}),a(o,{label:"\u64CD\u4F5C\u65E5\u5FD7"},{default:r(()=>[a(k,{"log-list":t(y)},null,8,["log-list"])]),_:1}),a(o,{label:"\u56E2\u961F\u6210\u5458"},{default:r(()=>[a(et,{ref_key:"permissionListRef",ref:m,"biz-id":t(_).id,"biz-type":t(w).CRM_RECEIVABLE_PLAN,"show-action":!0,onQuitTeam:h},null,8,["biz-id","biz-type"])]),_:1})]),_:1})]),_:1}),a(ot,{ref_key:"formRef",ref:p,onSuccess:e[1]||(e[1]=c=>f(t(_).id))},null,512)],64)}}})});export{Ct as __tla,z as default};
