import{d as G,I as H,n as Q,r as p,f as Z,C as B,T as D,o as c,c as E,i as a,w as l,a as t,U as C,j as u,H as y,l as h,G as F,g as J,t as W,F as X,Z as Y,L as $,_ as aa,N as ea,O as la,P as ta,Q as ra,R as oa,__tla as na}from"./index-BUSn51wb.js";import{_ as pa,__tla as sa}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as ia,__tla as _a}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as ma,__tla as ca}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{C as I,__tla as ua}from"./index-DrcFYyNA.js";import{_ as da,__tla as fa}from"./ChatModelForm.vue_vue_type_script_setup_true_lang-D4OYaGPS.js";import{A as ya,__tla as ha}from"./index-BRuDnVkN.js";import{__tla as ga}from"./index-Cch5e1V0.js";import"./color-BN7ZL7BD.js";import{__tla as wa}from"./el-card-CJbXGyyg.js";import{__tla as ba}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import"./constants-A8BI3pz7.js";let K,ka=Promise.all([(()=>{try{return na}catch{}})(),(()=>{try{return sa}catch{}})(),(()=>{try{return _a}catch{}})(),(()=>{try{return ca}catch{}})(),(()=>{try{return ua}catch{}})(),(()=>{try{return fa}catch{}})(),(()=>{try{return ha}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return wa}catch{}})(),(()=>{try{return ba}catch{}})()]).then(async()=>{K=G({name:"AiChatModel",__name:"index",setup(va){const x=H(),{t:O}=Q(),g=p(!0),V=p([]),P=p(0),o=Z({pageNo:1,pageSize:10,name:void 0,model:void 0,platform:void 0}),S=p(),A=p([]),s=async()=>{g.value=!0;try{const _=await I.getChatModelPage(o);V.value=_.list,P.value=_.total}finally{g.value=!1}},i=()=>{o.pageNo=1,s()},R=()=>{S.value.resetFields(),i()},U=p(),M=(_,r)=>{U.value.open(_,r)};return B(async()=>{s(),A.value=await ya.getApiKeySimpleList()}),(_,r)=>{const w=Y,d=$,b=aa,m=ea,z=la,N=ma,T=ia,n=ta,L=ra,j=pa,k=D("hasPermi"),q=oa;return c(),E(X,null,[a(N,null,{default:l(()=>[a(z,{class:"-mb-15px",model:t(o),ref_key:"queryFormRef",ref:S,inline:!0,"label-width":"68px"},{default:l(()=>[a(d,{label:"\u6A21\u578B\u540D\u5B57",prop:"name"},{default:l(()=>[a(w,{modelValue:t(o).name,"onUpdate:modelValue":r[0]||(r[0]=e=>t(o).name=e),placeholder:"\u8BF7\u8F93\u5165\u6A21\u578B\u540D\u5B57",clearable:"",onKeyup:C(i,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(d,{label:"\u6A21\u578B\u6807\u8BC6",prop:"model"},{default:l(()=>[a(w,{modelValue:t(o).model,"onUpdate:modelValue":r[1]||(r[1]=e=>t(o).model=e),placeholder:"\u8BF7\u8F93\u5165\u6A21\u578B\u6807\u8BC6",clearable:"",onKeyup:C(i,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(d,{label:"\u6A21\u578B\u5E73\u53F0",prop:"platform"},{default:l(()=>[a(w,{modelValue:t(o).platform,"onUpdate:modelValue":r[2]||(r[2]=e=>t(o).platform=e),placeholder:"\u8BF7\u8F93\u5165\u6A21\u578B\u5E73\u53F0",clearable:"",onKeyup:C(i,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(d,null,{default:l(()=>[a(m,{onClick:i},{default:l(()=>[a(b,{icon:"ep:search",class:"mr-5px"}),u(" \u641C\u7D22")]),_:1}),a(m,{onClick:R},{default:l(()=>[a(b,{icon:"ep:refresh",class:"mr-5px"}),u(" \u91CD\u7F6E")]),_:1}),y((c(),h(m,{type:"primary",plain:"",onClick:r[3]||(r[3]=e=>M("create"))},{default:l(()=>[a(b,{icon:"ep:plus",class:"mr-5px"}),u(" \u65B0\u589E ")]),_:1})),[[k,["ai:chat-model:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(N,null,{default:l(()=>[y((c(),h(L,{data:t(V),stripe:!0,"show-overflow-tooltip":!0},{default:l(()=>[a(n,{label:"\u6240\u5C5E\u5E73\u53F0",align:"center",prop:"platform"},{default:l(e=>[a(T,{type:t(F).AI_PLATFORM,value:e.row.platform},null,8,["type","value"])]),_:1}),a(n,{label:"\u6A21\u578B\u540D\u5B57",align:"center",prop:"name"}),a(n,{label:"\u6A21\u578B\u6807\u8BC6",align:"center",prop:"model"}),a(n,{label:"API \u79D8\u94A5",align:"center",prop:"keyId","min-width":"140"},{default:l(e=>{var f;return[J("span",null,W((f=t(A).find(v=>v.id===e.row.keyId))==null?void 0:f.name),1)]}),_:1}),a(n,{label:"\u6392\u5E8F",align:"center",prop:"sort"}),a(n,{label:"\u72B6\u6001",align:"center",prop:"status"},{default:l(e=>[a(T,{type:t(F).COMMON_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),a(n,{label:"\u6E29\u5EA6\u53C2\u6570",align:"center",prop:"temperature"}),a(n,{label:"\u56DE\u590D\u6570 Token \u6570",align:"center",prop:"maxTokens","min-width":"140"}),a(n,{label:"\u4E0A\u4E0B\u6587\u6570\u91CF",align:"center",prop:"maxContexts"}),a(n,{label:"\u64CD\u4F5C",align:"center"},{default:l(e=>[y((c(),h(m,{link:"",type:"primary",onClick:f=>M("update",e.row.id)},{default:l(()=>[u(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[k,["ai:chat-model:update"]]]),y((c(),h(m,{link:"",type:"danger",onClick:f=>(async v=>{try{await x.delConfirm(),await I.deleteChatModel(v),x.success(O("common.delSuccess")),await s()}catch{}})(e.row.id)},{default:l(()=>[u(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[k,["ai:chat-model:delete"]]])]),_:1})]),_:1},8,["data"])),[[q,t(g)]]),a(j,{total:t(P),page:t(o).pageNo,"onUpdate:page":r[4]||(r[4]=e=>t(o).pageNo=e),limit:t(o).pageSize,"onUpdate:limit":r[5]||(r[5]=e=>t(o).pageSize=e),onPagination:s},null,8,["total","page","limit"])]),_:1}),a(da,{ref_key:"formRef",ref:U,onSuccess:s},null,512)],64)}}})});export{ka as __tla,K as default};
