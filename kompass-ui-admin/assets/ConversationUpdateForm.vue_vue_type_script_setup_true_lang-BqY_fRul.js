import{d as L,I as N,r as n,f as O,o as p,l as v,w as o,i as s,a as l,j as f,H as E,c as R,F as A,k as P,y as B,Z as H,L as J,J as K,K as S,cc as Z,O as z,N as D,R as G,__tla as Q}from"./index-BUSn51wb.js";import{_ as W,__tla as X}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{C as Y}from"./constants-A8BI3pz7.js";import{C as $,__tla as ee}from"./index-DrcFYyNA.js";import{C as g,__tla as ae}from"./index-UejJy_db.js";let C,le=Promise.all([(()=>{try{return Q}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return ee}catch{}})(),(()=>{try{return ae}catch{}})()]).then(async()=>{C=L({name:"ChatConversationUpdateForm",__name:"ConversationUpdateForm",emits:["success"],setup(te,{expose:V,emit:h}){const b=N(),u=n(!1),m=n(!1),t=n({id:void 0,systemMessage:void 0,modelId:void 0,temperature:void 0,maxTokens:void 0,maxContexts:void 0}),k=O({modelId:[{required:!0,message:"\u6A21\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],temperature:[{required:!0,message:"\u6E29\u5EA6\u53C2\u6570\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],maxTokens:[{required:!0,message:"\u56DE\u590D\u6570 Token \u6570\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],maxContexts:[{required:!0,message:"\u4E0A\u4E0B\u6587\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),_=n(),y=n([]);V({open:async d=>{if(u.value=!0,M(),d){m.value=!0;try{const e=await g.getChatConversationMy(d);t.value=Object.keys(t.value).reduce((i,r)=>(e.hasOwnProperty(r)&&(i[r]=e[r]),i),{})}finally{m.value=!1}}y.value=await $.getChatModelSimpleList(Y.ENABLE)}});const T=h,w=async()=>{await _.value.validate(),m.value=!0;try{const d=t.value;await g.updateChatConversationMy(d),b.success("\u5BF9\u8BDD\u914D\u7F6E\u5DF2\u66F4\u65B0"),u.value=!1,T("success")}finally{m.value=!1}},M=()=>{var d;t.value={id:void 0,systemMessage:void 0,modelId:void 0,temperature:void 0,maxTokens:void 0,maxContexts:void 0},(d=_.value)==null||d.resetFields()};return(d,e)=>{const i=H,r=J,U=K,I=S,c=Z,q=z,x=D,F=W,j=G;return p(),v(F,{title:"\u8BBE\u5B9A",modelValue:l(u),"onUpdate:modelValue":e[6]||(e[6]=a=>B(u)?u.value=a:null)},{footer:o(()=>[s(x,{onClick:w,type:"primary",disabled:l(m)},{default:o(()=>[f("\u786E \u5B9A")]),_:1},8,["disabled"]),s(x,{onClick:e[5]||(e[5]=a=>u.value=!1)},{default:o(()=>[f("\u53D6 \u6D88")]),_:1})]),default:o(()=>[E((p(),v(q,{ref_key:"formRef",ref:_,model:l(t),rules:l(k),"label-width":"130px"},{default:o(()=>[s(r,{label:"\u89D2\u8272\u8BBE\u5B9A",prop:"systemMessage"},{default:o(()=>[s(i,{type:"textarea",modelValue:l(t).systemMessage,"onUpdate:modelValue":e[0]||(e[0]=a=>l(t).systemMessage=a),rows:"4",placeholder:"\u8BF7\u8F93\u5165\u89D2\u8272\u8BBE\u5B9A"},null,8,["modelValue"])]),_:1}),s(r,{label:"\u6A21\u578B",prop:"modelId"},{default:o(()=>[s(I,{modelValue:l(t).modelId,"onUpdate:modelValue":e[1]||(e[1]=a=>l(t).modelId=a),placeholder:"\u8BF7\u9009\u62E9\u6A21\u578B"},{default:o(()=>[(p(!0),R(A,null,P(l(y),a=>(p(),v(U,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),s(r,{label:"\u6E29\u5EA6\u53C2\u6570",prop:"temperature"},{default:o(()=>[s(c,{modelValue:l(t).temperature,"onUpdate:modelValue":e[2]||(e[2]=a=>l(t).temperature=a),placeholder:"\u8BF7\u8F93\u5165\u6E29\u5EA6\u53C2\u6570",min:0,max:2,precision:2},null,8,["modelValue"])]),_:1}),s(r,{label:"\u56DE\u590D\u6570 Token \u6570",prop:"maxTokens"},{default:o(()=>[s(c,{modelValue:l(t).maxTokens,"onUpdate:modelValue":e[3]||(e[3]=a=>l(t).maxTokens=a),placeholder:"\u8BF7\u8F93\u5165\u56DE\u590D\u6570 Token \u6570",min:0,max:4096},null,8,["modelValue"])]),_:1}),s(r,{label:"\u4E0A\u4E0B\u6587\u6570\u91CF",prop:"maxContexts"},{default:o(()=>[s(c,{modelValue:l(t).maxContexts,"onUpdate:modelValue":e[4]||(e[4]=a=>l(t).maxContexts=a),placeholder:"\u8BF7\u8F93\u5165\u4E0A\u4E0B\u6587\u6570\u91CF",min:0,max:20},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[j,l(m)]])]),_:1},8,["modelValue"])}}})});export{C as _,le as __tla};
