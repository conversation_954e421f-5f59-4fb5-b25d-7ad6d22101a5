import{_ as t,__tla as r}from"./ReplyForm.vue_vue_type_script_setup_true_lang-D9_LvX1d.js";import{__tla as _}from"./index-BUSn51wb.js";import{__tla as a}from"./main-DgL9CntZ.js";import{__tla as l}from"./TabNews-CeL3Heyg.js";import{__tla as o}from"./main-DwQbyLY9.js";import{__tla as m}from"./el-image-BjHZRFih.js";import{__tla as c}from"./main-DvybYriQ.js";import{__tla as e}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{__tla as s}from"./index-Cch5e1V0.js";import{__tla as i}from"./main-CG5euiEw.js";import{__tla as n}from"./main.vue_vue_type_script_setup_true_lang-Bi4NZ0QE.js";import{__tla as p}from"./index-C4ZN3JCQ.js";import{__tla as f}from"./index-Cqwyhbsb.js";import{__tla as h}from"./formatTime-DWdBpgsM.js";import{__tla as u}from"./TabText.vue_vue_type_script_setup_true_lang-60j5SrWe.js";import{__tla as y}from"./TabImage-BHh9EaXQ.js";import{__tla as d}from"./useUpload-gjof4KYU.js";import{__tla as x}from"./TabVoice-BSMaiVPu.js";import{__tla as P}from"./TabVideo-CqCZb4l-.js";import{__tla as b}from"./TabMusic.vue_vue_type_script_setup_true_lang-D0UZjdHy.js";import"./types-CAO1T7C7.js";let g=Promise.all([(()=>{try{return r}catch{}})(),(()=>{try{return _}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return m}catch{}})(),(()=>{try{return c}catch{}})(),(()=>{try{return e}catch{}})(),(()=>{try{return s}catch{}})(),(()=>{try{return i}catch{}})(),(()=>{try{return n}catch{}})(),(()=>{try{return p}catch{}})(),(()=>{try{return f}catch{}})(),(()=>{try{return h}catch{}})(),(()=>{try{return u}catch{}})(),(()=>{try{return y}catch{}})(),(()=>{try{return d}catch{}})(),(()=>{try{return x}catch{}})(),(()=>{try{return P}catch{}})(),(()=>{try{return b}catch{}})()]).then(async()=>{});export{g as __tla,t as default};
