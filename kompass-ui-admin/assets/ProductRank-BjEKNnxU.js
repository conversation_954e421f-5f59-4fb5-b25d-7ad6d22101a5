import{_ as t,__tla as _}from"./ProductRank.vue_vue_type_script_setup_true_lang-DiXfHDS_.js";import{__tla as r}from"./index-BUSn51wb.js";import{__tla as a}from"./el-card-CJbXGyyg.js";import{__tla as l}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{__tla as o}from"./index-Cch5e1V0.js";import{__tla as c}from"./el-image-BjHZRFih.js";import{__tla as m}from"./index.vue_vue_type_script_setup_true_lang-BeC3r7Xt.js";import{__tla as e}from"./formatTime-DWdBpgsM.js";import{__tla as s}from"./product-lJh9Q1vt.js";import{__tla as n}from"./CardTitle-Dm4BG9kg.js";let f=Promise.all([(()=>{try{return _}catch{}})(),(()=>{try{return r}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return c}catch{}})(),(()=>{try{return m}catch{}})(),(()=>{try{return e}catch{}})(),(()=>{try{return s}catch{}})(),(()=>{try{return n}catch{}})()]).then(async()=>{});export{f as __tla,t as default};
