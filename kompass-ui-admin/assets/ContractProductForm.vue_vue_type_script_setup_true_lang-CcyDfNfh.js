import{d as J,r as _,f as K,at as w,C as L,o as p,c as x,H as M,a as d,l as V,w as l,i as e,F as U,k as Q,G as W,dX as y,j as C,a9 as X,dW as Z,P as z,J as A,K as S,L as Y,Z as ee,cc as ae,N as le,Q as te,O as oe,s as de,R as re,__tla as ue}from"./index-BUSn51wb.js";import{_ as ie,__tla as ce}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{g as ne,__tla as se}from"./index-CaE_tgzr.js";let $,pe=Promise.all([(()=>{try{return ue}catch{}})(),(()=>{try{return ce}catch{}})(),(()=>{try{return se}catch{}})()]).then(async()=>{$=J({__name:"ContractProductForm",props:{products:{},disabled:{type:Boolean}},setup(k,{expose:I}){const N=k,R=_(!1),c=_([]),f=K({productId:[{required:!0,message:"\u4EA7\u54C1\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],contractPrice:[{required:!0,message:"\u5408\u540C\u4EF7\u683C\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],count:[{required:!0,message:"\u4EA7\u54C1\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),v=_([]),b=_([]);w(()=>N.products,async u=>{c.value=u},{immediate:!0}),w(()=>c.value,u=>{u&&u.length!==0&&u.forEach(i=>{i.contractPrice!=null&&i.count!=null?i.totalPrice=Z(i.contractPrice,i.count):i.totalPrice=void 0})},{deep:!0});const q=()=>{c.value.push({id:void 0,productId:void 0,productUnit:void 0,productNo:void 0,productPrice:void 0,contractPrice:void 0,count:1})};return I({validate:()=>v.value.validate()}),L(async()=>{b.value=await ne()}),(u,i)=>{const r=z,j=A,F=S,n=Y,h=ee,O=ie,g=ae,P=le,T=te,B=oe,D=de,E=re;return p(),x(U,null,[M((p(),V(B,{ref_key:"formRef",ref:v,model:d(c),rules:d(f),"label-width":"0px","inline-message":!0,disabled:u.disabled},{default:l(()=>[e(T,{data:d(c),class:"-mt-10px"},{default:l(()=>[e(r,{label:"\u5E8F\u53F7",type:"index",align:"center",width:"60"}),e(r,{label:"\u4EA7\u54C1\u540D\u79F0","min-width":"180"},{default:l(({row:a,$index:o})=>[e(n,{prop:`${o}.productId`,rules:d(f).productId,class:"mb-0px!"},{default:l(()=>[e(F,{modelValue:a.productId,"onUpdate:modelValue":t=>a.productId=t,clearable:"",filterable:"",onChange:t=>((G,m)=>{const s=b.value.find(H=>H.id===G);s&&(m.productUnit=s.unit,m.productNo=s.no,m.productPrice=s.price,m.contractPrice=s.price)})(t,a),placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1"},{default:l(()=>[(p(!0),x(U,null,Q(d(b),t=>(p(),V(j,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),e(r,{label:"\u6761\u7801","min-width":"150"},{default:l(({row:a})=>[e(n,{class:"mb-0px!"},{default:l(()=>[e(h,{disabled:"",modelValue:a.productNo,"onUpdate:modelValue":o=>a.productNo=o},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),e(r,{label:"\u5355\u4F4D","min-width":"80"},{default:l(({row:a})=>[e(O,{type:d(W).CRM_PRODUCT_UNIT,value:a.productUnit},null,8,["type","value"])]),_:1}),e(r,{label:"\u4EF7\u683C\uFF08\u5143\uFF09","min-width":"120"},{default:l(({row:a})=>[e(n,{class:"mb-0px!"},{default:l(()=>[e(h,{disabled:"",modelValue:a.productPrice,"onUpdate:modelValue":o=>a.productPrice=o,formatter:d(y)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1024)]),_:1}),e(r,{label:"\u552E\u4EF7\uFF08\u5143\uFF09",fixed:"right","min-width":"140"},{default:l(({row:a,$index:o})=>[e(n,{prop:`${o}.contractPrice`,class:"mb-0px!"},{default:l(()=>[e(g,{modelValue:a.contractPrice,"onUpdate:modelValue":t=>a.contractPrice=t,"controls-position":"right",min:.001,precision:2,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(r,{label:"\u6570\u91CF",prop:"count",fixed:"right","min-width":"120"},{default:l(({row:a,$index:o})=>[e(n,{prop:`${o}.count`,rules:d(f).count,class:"mb-0px!"},{default:l(()=>[e(g,{modelValue:a.count,"onUpdate:modelValue":t=>a.count=t,"controls-position":"right",min:.001,precision:3,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(r,{label:"\u5408\u8BA1",prop:"totalPrice",fixed:"right","min-width":"140"},{default:l(({row:a,$index:o})=>[e(n,{prop:`${o}.totalPrice`,class:"mb-0px!"},{default:l(()=>[e(h,{disabled:"",modelValue:a.totalPrice,"onUpdate:modelValue":t=>a.totalPrice=t,formatter:d(y)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1032,["prop"])]),_:1}),e(r,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"60"},{default:l(({$index:a})=>[e(P,{onClick:o=>{return t=a,void c.value.splice(t,1);var t},link:""},{default:l(()=>[C("\u2014")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["model","rules","disabled"])),[[E,d(R)]]),u.disabled?X("",!0):(p(),V(D,{key:0,justify:"center",class:"mt-3"},{default:l(()=>[e(P,{onClick:q,round:""},{default:l(()=>[C("+ \u6DFB\u52A0\u4EA7\u54C1")]),_:1})]),_:1}))],64)}}})});export{$ as _,pe as __tla};
