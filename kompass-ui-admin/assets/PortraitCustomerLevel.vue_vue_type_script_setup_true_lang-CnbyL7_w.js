import{d as A,r as h,f as v,C as T,o as b,c as U,i as e,w as l,a as o,H as V,l as j,G as m,F as q,aG as g,aA as z,el as C,ej as w,E as G,s as I,P as D,Q as F,R as H,__tla as Q}from"./index-BUSn51wb.js";import{_ as k,__tla as B}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{E as J,__tla as K}from"./el-card-CJbXGyyg.js";import{E as N,__tla as X}from"./el-skeleton-item-tDN8U6BH.js";import{_ as Y,__tla as Z}from"./Echart.vue_vue_type_script_setup_true_lang-DnLZLH3w.js";import{S as $,__tla as ee}from"./portrait-BcNwms8P.js";let y,ae=Promise.all([(()=>{try{return Q}catch{}})(),(()=>{try{return B}catch{}})(),(()=>{try{return K}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return Z}catch{}})(),(()=>{try{return ee}catch{}})()]).then(async()=>{y=A({name:"PortraitCustomerLevel",__name:"PortraitCustomerLevel",props:{queryParams:{}},setup(E,{expose:L}){const P=E,i=h(!1),p=h([]),n=v({title:{text:"\u5168\u90E8\u5BA2\u6237",left:"center"},tooltip:{trigger:"item"},legend:{orient:"vertical",left:"left"},toolbox:{feature:{saveAsImage:{show:!0,name:"\u5168\u90E8\u5BA2\u6237"}}},series:[{name:"\u5168\u90E8\u5BA2\u6237",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:40,fontWeight:"bold"}},labelLine:{show:!1},data:[]}]}),d=v({title:{text:"\u6210\u4EA4\u5BA2\u6237",left:"center"},tooltip:{trigger:"item"},legend:{orient:"vertical",left:"left"},toolbox:{feature:{saveAsImage:{show:!0,name:"\u6210\u4EA4\u5BA2\u6237"}}},series:[{name:"\u6210\u4EA4\u5BA2\u6237",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:40,fontWeight:"bold"}},labelLine:{show:!1},data:[]}]}),c=async()=>{i.value=!0;const r=await $.getCustomerLevel(P.queryParams);n.series&&n.series[0]&&n.series[0].data&&(n.series[0].data=r.map(t=>({name:g(m.CRM_CUSTOMER_LEVEL,t.level),value:t.customerCount}))),d.series&&d.series[0]&&d.series[0].data&&(d.series[0].data=r.map(t=>({name:g(m.CRM_CUSTOMER_LEVEL,t.level),value:t.dealCount}))),R(r),p.value=r,i.value=!1};L({loadData:c});const R=r=>{if(z(r))return;const t=r,u=C(t.map(a=>a.customerCount)),_=C(t.map(a=>a.dealCount));t.forEach(a=>{a.levelPortion=a.customerCount===0?0:w(a.customerCount,u),a.dealPortion=a.dealCount===0?0:w(a.dealCount,_)})};return T(()=>{c()}),(r,t)=>{const u=Y,_=N,a=G,x=I,f=J,s=D,S=k,M=F,O=H;return b(),U(q,null,[e(f,{shadow:"never"},{default:l(()=>[e(x,{gutter:20},{default:l(()=>[e(a,{span:12},{default:l(()=>[e(_,{loading:o(i),animated:""},{default:l(()=>[e(u,{height:500,options:o(n)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),e(a,{span:12},{default:l(()=>[e(_,{loading:o(i),animated:""},{default:l(()=>[e(u,{height:500,options:o(d)},null,8,["options"])]),_:1},8,["loading"])]),_:1})]),_:1})]),_:1}),e(f,{class:"mt-16px",shadow:"never"},{default:l(()=>[V((b(),j(M,{data:o(p)},{default:l(()=>[e(s,{align:"center",label:"\u5E8F\u53F7",type:"index",width:"80"}),e(s,{align:"center",label:"\u5BA2\u6237\u7EA7\u522B",prop:"level",width:"200"},{default:l(W=>[e(S,{type:o(m).CRM_CUSTOMER_LEVEL,value:W.row.level},null,8,["type","value"])]),_:1}),e(s,{align:"center",label:"\u5BA2\u6237\u4E2A\u6570","min-width":"200",prop:"customerCount"}),e(s,{align:"center",label:"\u6210\u4EA4\u4E2A\u6570","min-width":"200",prop:"dealCount"}),e(s,{align:"center",label:"\u7EA7\u522B\u5360\u6BD4(%)","min-width":"200",prop:"levelPortion"}),e(s,{align:"center",label:"\u6210\u4EA4\u5360\u6BD4(%)","min-width":"200",prop:"dealPortion"})]),_:1},8,["data"])),[[O,o(i)]])]),_:1})],64)}}})});export{y as _,ae as __tla};
