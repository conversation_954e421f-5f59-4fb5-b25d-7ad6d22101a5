import{d as I,I as L,n as Q,r as c,f as Z,C as B,T as D,o as i,c as J,i as e,w as a,a as l,U as v,j as u,H as d,l as f,G as W,F as X,Z as Y,L as $,_ as ee,N as ae,O as le,P as te,Q as re,R as oe,__tla as ne}from"./index-BUSn51wb.js";import{_ as se,__tla as pe}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as ce,__tla as ie}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as ue,__tla as _e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as me,__tla as de}from"./index-COobLwz-.js";import{d as fe}from"./download-e0EdwhTv.js";import{C as k,__tla as ye}from"./index-DYwp4_G0.js";import{_ as he,__tla as ge}from"./CustomerForm.vue_vue_type_script_setup_true_lang-JudvIAdF.js";import{__tla as be}from"./index-Cch5e1V0.js";import"./color-BN7ZL7BD.js";import{__tla as we}from"./el-card-CJbXGyyg.js";import{__tla as Ce}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import"./constants-A8BI3pz7.js";let F,xe=Promise.all([(()=>{try{return ne}catch{}})(),(()=>{try{return pe}catch{}})(),(()=>{try{return ie}catch{}})(),(()=>{try{return _e}catch{}})(),(()=>{try{return de}catch{}})(),(()=>{try{return ye}catch{}})(),(()=>{try{return ge}catch{}})(),(()=>{try{return be}catch{}})(),(()=>{try{return we}catch{}})(),(()=>{try{return Ce}catch{}})()]).then(async()=>{F=I({name:"ErpCustomer",__name:"index",setup(ve){const b=L(),{t:K}=Q(),w=c(!0),V=c([]),S=c(0),r=Z({pageNo:1,pageSize:10,name:void 0,mobile:void 0,telephone:void 0}),U=c(),C=c(!1),_=async()=>{w.value=!0;try{const s=await k.getCustomerPage(r);V.value=s.list,S.value=s.total}finally{w.value=!1}},m=()=>{r.pageNo=1,_()},O=()=>{U.value.resetFields(),m()},N=c(),P=(s,o)=>{N.value.open(s,o)},R=async()=>{try{await b.exportConfirm(),C.value=!0;const s=await k.exportCustomer(r);fe.excel(s,"\u5BA2\u6237.xls")}catch{}finally{C.value=!1}};return B(()=>{_()}),(s,o)=>{const T=me,x=Y,y=$,h=ee,p=ae,M=le,z=ue,n=te,j=ce,q=re,A=se,g=D("hasPermi"),E=oe;return i(),J(X,null,[e(T,{title:"\u3010\u9500\u552E\u3011\u9500\u552E\u8BA2\u5355\u3001\u51FA\u5E93\u3001\u9000\u8D27",url:"https://doc.iocoder.cn/erp/sale/"}),e(z,null,{default:a(()=>[e(M,{class:"-mb-15px",model:l(r),ref_key:"queryFormRef",ref:U,inline:!0,"label-width":"68px"},{default:a(()=>[e(y,{label:"\u540D\u79F0",prop:"name"},{default:a(()=>[e(x,{modelValue:l(r).name,"onUpdate:modelValue":o[0]||(o[0]=t=>l(r).name=t),placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0",clearable:"",onKeyup:v(m,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(y,{label:"\u624B\u673A\u53F7\u7801",prop:"mobile"},{default:a(()=>[e(x,{modelValue:l(r).mobile,"onUpdate:modelValue":o[1]||(o[1]=t=>l(r).mobile=t),placeholder:"\u8BF7\u8F93\u5165\u624B\u673A\u53F7\u7801",clearable:"",onKeyup:v(m,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(y,{label:"\u8054\u7CFB\u7535\u8BDD",prop:"telephone"},{default:a(()=>[e(x,{modelValue:l(r).telephone,"onUpdate:modelValue":o[2]||(o[2]=t=>l(r).telephone=t),placeholder:"\u8BF7\u8F93\u5165\u8054\u7CFB\u7535\u8BDD",clearable:"",onKeyup:v(m,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(y,null,{default:a(()=>[e(p,{onClick:m},{default:a(()=>[e(h,{icon:"ep:search",class:"mr-5px"}),u(" \u641C\u7D22")]),_:1}),e(p,{onClick:O},{default:a(()=>[e(h,{icon:"ep:refresh",class:"mr-5px"}),u(" \u91CD\u7F6E")]),_:1}),d((i(),f(p,{type:"primary",plain:"",onClick:o[3]||(o[3]=t=>P("create"))},{default:a(()=>[e(h,{icon:"ep:plus",class:"mr-5px"}),u(" \u65B0\u589E ")]),_:1})),[[g,["erp:customer:create"]]]),d((i(),f(p,{type:"success",plain:"",onClick:R,loading:l(C)},{default:a(()=>[e(h,{icon:"ep:download",class:"mr-5px"}),u(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[g,["erp:customer:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(z,null,{default:a(()=>[d((i(),f(q,{data:l(V),stripe:!0,"show-overflow-tooltip":!0},{default:a(()=>[e(n,{label:"\u540D\u79F0",align:"center",prop:"name"}),e(n,{label:"\u8054\u7CFB\u4EBA",align:"center",prop:"contact"}),e(n,{label:"\u624B\u673A\u53F7\u7801",align:"center",prop:"mobile"}),e(n,{label:"\u8054\u7CFB\u7535\u8BDD",align:"center",prop:"telephone"}),e(n,{label:"\u7535\u5B50\u90AE\u7BB1",align:"center",prop:"email"}),e(n,{label:"\u5907\u6CE8",align:"center",prop:"remark"}),e(n,{label:"\u6392\u5E8F",align:"center",prop:"sort"}),e(n,{label:"\u72B6\u6001",align:"center",prop:"status"},{default:a(t=>[e(j,{type:l(W).COMMON_STATUS,value:t.row.status},null,8,["type","value"])]),_:1}),e(n,{label:"\u64CD\u4F5C",align:"center"},{default:a(t=>[d((i(),f(p,{link:"",type:"primary",onClick:G=>P("update",t.row.id)},{default:a(()=>[u(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[g,["erp:customer:update"]]]),d((i(),f(p,{link:"",type:"danger",onClick:G=>(async H=>{try{await b.delConfirm(),await k.deleteCustomer(H),b.success(K("common.delSuccess")),await _()}catch{}})(t.row.id)},{default:a(()=>[u(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[g,["erp:customer:delete"]]])]),_:1})]),_:1},8,["data"])),[[E,l(w)]]),e(A,{total:l(S),page:l(r).pageNo,"onUpdate:page":o[4]||(o[4]=t=>l(r).pageNo=t),limit:l(r).pageSize,"onUpdate:limit":o[5]||(o[5]=t=>l(r).pageSize=t),onPagination:_},null,8,["total","page","limit"])]),_:1}),e(he,{ref_key:"formRef",ref:N,onSuccess:_},null,512)],64)}}})});export{xe as __tla,F as default};
