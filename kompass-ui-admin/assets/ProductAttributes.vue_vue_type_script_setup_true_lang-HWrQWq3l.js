import{d as G,p as J,r as v,b as M,at as O,o as y,c as A,k as D,a as m,F as P,l as V,w as n,g as j,i as r,j as d,t as B,H as E,a8 as I,y as Q,U as R,Z as T,n as W,I as X,ax as Y,N as w,q as aa,E as sa,__tla as ea}from"./index-BUSn51wb.js";import{E as la,__tla as ta}from"./el-text-CIwNlU-U.js";import{c as ua,__tla as oa}from"./property-BdOytbZT.js";let K,ca=Promise.all([(()=>{try{return ea}catch{}})(),(()=>{try{return ta}catch{}})(),(()=>{try{return oa}catch{}})()]).then(async()=>{K=G({name:"ProductAttributes",__name:"ProductAttributes",props:{propertyList:{type:Array,default:()=>{}},isDetail:J.bool.def(!1)},emits:["success"],setup(f,{emit:L}){const{t:U}=W(),x=X(),l=v(""),p=v(null),k=M(()=>a=>p.value!==null&&(p.value===a||void 0)),b=v([]),q=a=>{a!=null&&(b.value.some(o=>{var u,i;return((u=o.input)==null?void 0:u.attributes.id)===((i=a.input)==null?void 0:i.attributes.id)})||b.value.push(a))},t=v([]),F=f;O(()=>F.propertyList,a=>{a&&(t.value=a)},{deep:!0,immediate:!0});const C=L,g=async(a,o)=>{if(l.value)try{const u=await ua({propertyId:o,name:l.value});t.value[a].values.push({id:u,name:l.value}),x.success(U("common.createSuccess")),C("success",t.value)}catch{x.error("\u6DFB\u52A0\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5")}p.value=null,l.value=""};return(a,o)=>{const u=la,i=Y,H=w,N=aa,S=sa;return y(!0),A(P,null,D(m(t),(_,s)=>(y(),V(S,{key:s},{default:n(()=>[j("div",null,[r(u,{class:"mx-1"},{default:n(()=>[d("\u5C5E\u6027\u540D\uFF1A")]),_:1}),r(i,{closable:!f.isDetail,class:"mx-1",type:"success",onClose:e=>(c=>{var h;(h=t.value)==null||h.splice(c,1),C("success",t.value)})(s)},{default:n(()=>[d(B(_.name),1)]),_:2},1032,["closable","onClose"])]),j("div",null,[r(u,{class:"mx-1"},{default:n(()=>[d("\u5C5E\u6027\u503C\uFF1A")]),_:1}),(y(!0),A(P,null,D(_.values,(e,c)=>(y(),V(i,{key:e.id,closable:!f.isDetail,class:"mx-1",onClose:h=>((Z,$)=>{var z;(z=t.value[Z].values)==null||z.splice($,1)})(s,c)},{default:n(()=>[d(B(e.name),1)]),_:2},1032,["closable","onClose"]))),128)),E(r(m(T),{id:`input${s}`,ref_for:!0,ref:q,modelValue:m(l),"onUpdate:modelValue":o[0]||(o[0]=e=>Q(l)?l.value=e:null),class:"!w-20",size:"small",onBlur:e=>g(s,_.id),onKeyup:R(e=>g(s,_.id),["enter"])},null,8,["id","modelValue","onBlur","onKeyup"]),[[I,m(k)(s)]]),E(r(H,{class:"button-new-tag ml-1",size:"small",onClick:e=>(async c=>{p.value=c,b.value[c].focus()})(s)},{default:n(()=>[d(" + \u6DFB\u52A0 ")]),_:2},1032,["onClick"]),[[I,!m(k)(s)]])]),r(N,{class:"my-10px"})]),_:2},1024))),128)}}})});export{K as _,ca as __tla};
