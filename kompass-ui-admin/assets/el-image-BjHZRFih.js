import{bd as te,be as z,cw as se,bn as ie,d as P,bQ as oe,bf as ne,bs as re,dL as ce,r as c,b as u,bC as p,at as ue,C as de,o as n,c as d,aV as _,g as T,t as ve,a,a0 as g,F as $,ao as fe,a9 as y,l as pe,w as ye,dM as me,av as ge,bg as we,ay as be,dN as he,bp as Se,dO as ke,c_ as A,dP as xe,dQ as ze,bh as _e,__tla as Ce}from"./index-BUSn51wb.js";let M,Ee=Promise.all([(()=>{try{return Ce}catch{}})()]).then(async()=>{const j=te({hideOnClickModal:Boolean,src:{type:String,default:""},fit:{type:String,values:["","contain","cover","fill","none","scale-down"],default:""},loading:{type:String,values:["eager","lazy"]},lazy:Boolean,scrollContainer:{type:z([String,Object])},previewSrcList:{type:z(Array),default:()=>se([])},previewTeleported:Boolean,zIndex:{type:Number},initialIndex:{type:Number,default:0},infinite:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},zoomRate:{type:Number,default:1.2},minScale:{type:Number,default:.2},maxScale:{type:Number,default:7},crossorigin:{type:z(String)}}),R={load:i=>i instanceof Event,error:i=>i instanceof Event,switch:i=>ie(i),close:()=>!0,show:()=>!0},Y=["src","loading","crossorigin"],F={key:0},Q=P({name:"ElImage",inheritAttrs:!1});M=_e(we(P({...Q,props:j,emits:R,setup(i,{emit:v}){const l=i;let C="";const{t:q}=oe(),o=ne("image"),D=re(),H=ce(),w=c(),f=c(!1),r=c(!0),b=c(!1),m=c(),s=c(),K=p&&"loading"in HTMLImageElement.prototype;let h,S;const V=u(()=>[o.e("inner"),k.value&&o.e("preview"),r.value&&o.is("loading")]),G=u(()=>D.style),J=u(()=>{const{fit:e}=l;return p&&e?{objectFit:e}:{}}),k=u(()=>{const{previewSrcList:e}=l;return Array.isArray(e)&&e.length>0}),U=u(()=>{const{previewSrcList:e,initialIndex:t}=l;let B=t;return t>e.length-1&&(B=0),B}),E=u(()=>l.loading!=="eager"&&(!K&&l.loading==="lazy"||l.lazy)),x=()=>{p&&(r.value=!0,f.value=!1,w.value=l.src)};function W(e){r.value=!1,f.value=!1,v("load",e)}function X(e){r.value=!1,f.value=!0,v("error",e)}function I(){xe(m.value,s.value)&&(x(),O())}const L=ze(I,200,!0);async function N(){var e;if(!p)return;await be();const{scrollContainer:t}=l;he(t)?s.value=t:Se(t)&&t!==""?s.value=(e=document.querySelector(t))!=null?e:void 0:m.value&&(s.value=ke(m.value)),s.value&&(h=A(s,"scroll",L),setTimeout(()=>I(),100))}function O(){p&&s.value&&L&&(h==null||h(),s.value=void 0)}function Z(e){if(e.ctrlKey)return e.deltaY<0||e.deltaY>0?(e.preventDefault(),!1):void 0}function ee(){k.value&&(S=A("wheel",Z,{passive:!1}),C=document.body.style.overflow,document.body.style.overflow="hidden",b.value=!0,v("show"))}function ae(){S==null||S(),document.body.style.overflow=C,b.value=!1,v("close")}function le(e){v("switch",e)}return ue(()=>l.src,()=>{E.value?(r.value=!0,f.value=!1,O(),N()):x()}),de(()=>{E.value?N():x()}),(e,t)=>(n(),d("div",{ref_key:"container",ref:m,class:g([a(o).b(),e.$attrs.class]),style:ge(a(G))},[f.value?_(e.$slots,"error",{key:0},()=>[T("div",{class:g(a(o).e("error"))},ve(a(q)("el.image.error")),3)]):(n(),d($,{key:1},[w.value!==void 0?(n(),d("img",fe({key:0},a(H),{src:w.value,loading:e.loading,style:a(J),class:a(V),crossorigin:e.crossorigin,onClick:ee,onLoad:W,onError:X}),null,16,Y)):y("v-if",!0),r.value?(n(),d("div",{key:1,class:g(a(o).e("wrapper"))},[_(e.$slots,"placeholder",{},()=>[T("div",{class:g(a(o).e("placeholder"))},null,2)])],2)):y("v-if",!0)],64)),a(k)?(n(),d($,{key:2},[b.value?(n(),pe(a(me),{key:0,"z-index":e.zIndex,"initial-index":a(U),infinite:e.infinite,"zoom-rate":e.zoomRate,"min-scale":e.minScale,"max-scale":e.maxScale,"url-list":e.previewSrcList,"hide-on-click-modal":e.hideOnClickModal,teleported:e.previewTeleported,"close-on-press-escape":e.closeOnPressEscape,onClose:ae,onSwitch:le},{default:ye(()=>[e.$slots.viewer?(n(),d("div",F,[_(e.$slots,"viewer")])):y("v-if",!0)]),_:3},8,["z-index","initial-index","infinite","zoom-rate","min-scale","max-scale","url-list","hide-on-click-modal","teleported","close-on-press-escape"])):y("v-if",!0)],64)):y("v-if",!0)],6))}}),[["__file","image.vue"]]))});export{M as E,Ee as __tla};
