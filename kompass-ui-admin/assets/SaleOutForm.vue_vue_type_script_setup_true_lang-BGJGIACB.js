import{d as te,n as oe,I as ue,r as c,f as re,b as de,at as se,dW as ie,o as n,c as f,i as e,w as o,a as t,l as p,j as g,a9 as ce,H as ne,F as v,k as S,y as q,dX as C,Z as me,L as _e,E as pe,M as fe,_ as ve,N as be,J as Ve,K as he,cn as Pe,s as ye,z as Ue,A as Ie,cc as ke,O as we,R as ge,__tla as Se}from"./index-BUSn51wb.js";import{_ as Ce,__tla as Oe}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{_ as xe,__tla as Ne}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{S as O,__tla as Te}from"./index-Cj6fFS0g.js";import{_ as <PERSON>,__tla as Ae}from"./SaleOutItemForm.vue_vue_type_script_setup_true_lang-DRXsm1S3.js";import{C as Le,__tla as Re}from"./index-DYwp4_G0.js";import{A as Ee,__tla as qe}from"./index-LbO7ASKC.js";import{_ as He,__tla as Je}from"./SaleOrderOutEnableList.vue_vue_type_script_setup_true_lang-C2kuHgAL.js";import{g as Me,__tla as We}from"./index-BYXzDB8j.js";let H,Xe=Promise.all([(()=>{try{return Se}catch{}})(),(()=>{try{return Oe}catch{}})(),(()=>{try{return Ne}catch{}})(),(()=>{try{return Te}catch{}})(),(()=>{try{return Ae}catch{}})(),(()=>{try{return Re}catch{}})(),(()=>{try{return qe}catch{}})(),(()=>{try{return Je}catch{}})(),(()=>{try{return We}catch{}})()]).then(async()=>{H=te({name:"SaleOutForm",__name:"SaleOutForm",emits:["success"],setup(je,{expose:J,emit:M}){const{t:b}=oe(),x=ue(),m=c(!1),N=c(""),_=c(!1),V=c(""),l=c({id:void 0,customerId:void 0,accountId:void 0,saleUserId:void 0,outTime:void 0,remark:void 0,fileUrl:"",discountPercent:0,discountPrice:0,totalPrice:0,otherPrice:0,orderNo:void 0,items:[],no:void 0}),W=re({customerId:[{required:!0,message:"\u5BA2\u6237\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],outTime:[{required:!0,message:"\u51FA\u5E93\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),h=de(()=>V.value==="detail"),P=c(),T=c([]),y=c([]),F=c([]),U=c("item"),A=c();se(()=>l.value,r=>{if(!r)return;const a=r.items.reduce((d,s)=>d+s.totalPrice,0),i=r.discountPercent!=null?ie(a,r.discountPercent/100):0;l.value.discountPrice=i,l.value.totalPrice=a-i+r.otherPrice},{deep:!0}),J({open:async(r,a)=>{if(m.value=!0,N.value=b("action."+r),V.value=r,K(),a){_.value=!0;try{l.value=await O.getSaleOut(a)}finally{_.value=!1}}T.value=await Le.getCustomerSimpleList(),F.value=await Me(),y.value=await Ee.getAccountSimpleList();const i=y.value.find(d=>d.defaultStatus);i&&(l.value.accountId=i.id)}});const L=c(),X=()=>{L.value.open()},j=r=>{l.value.orderId=r.id,l.value.orderNo=r.no,l.value.customerId=r.customerId,l.value.accountId=r.accountId,l.value.saleUserId=r.saleUserId,l.value.discountPercent=r.discountPercent,l.value.remark=r.remark,l.value.fileUrl=r.fileUrl,r.items.forEach(a=>{a.totalCount=a.count,a.count=a.totalCount-a.outCount,a.orderItemId=a.id,a.id=void 0}),l.value.items=r.items.filter(a=>a.count>0)},z=M,D=async()=>{await P.value.validate(),await A.value.validate(),_.value=!0;try{const r=l.value;V.value==="create"?(await O.createSaleOut(r),x.success(b("common.createSuccess"))):(await O.updateSaleOut(r),x.success(b("common.updateSuccess"))),m.value=!1,z("success")}finally{_.value=!1}},K=()=>{var r;l.value={id:void 0,customerId:void 0,accountId:void 0,saleUserId:void 0,outTime:void 0,remark:void 0,fileUrl:void 0,discountPercent:0,discountPrice:0,totalPrice:0,otherPrice:0,items:[]},(r=P.value)==null||r.resetFields()};return(r,a)=>{const i=me,d=_e,s=pe,Q=fe,Z=ve,I=be,k=Ve,w=he,B=Pe,R=ye,G=Ue,Y=Ie,$=xe,E=ke,ee=we,ae=Ce,le=ge;return n(),f(v,null,[e(ae,{title:t(N),modelValue:t(m),"onUpdate:modelValue":a[14]||(a[14]=u=>q(m)?m.value=u:null),width:"1440"},{footer:o(()=>[t(h)?ce("",!0):(n(),p(I,{key:0,onClick:D,type:"primary",disabled:t(_)},{default:o(()=>[g(" \u786E \u5B9A ")]),_:1},8,["disabled"])),e(I,{onClick:a[13]||(a[13]=u=>m.value=!1)},{default:o(()=>[g("\u53D6 \u6D88")]),_:1})]),default:o(()=>[ne((n(),p(ee,{ref_key:"formRef",ref:P,model:t(l),rules:t(W),"label-width":"100px",disabled:t(h)},{default:o(()=>[e(R,{gutter:20},{default:o(()=>[e(s,{span:8},{default:o(()=>[e(d,{label:"\u51FA\u5E93\u5355\u53F7",prop:"no"},{default:o(()=>[e(i,{disabled:"",modelValue:t(l).no,"onUpdate:modelValue":a[0]||(a[0]=u=>t(l).no=u),placeholder:"\u4FDD\u5B58\u65F6\u81EA\u52A8\u751F\u6210"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:o(()=>[e(d,{label:"\u51FA\u5E93\u65F6\u95F4",prop:"outTime"},{default:o(()=>[e(Q,{modelValue:t(l).outTime,"onUpdate:modelValue":a[1]||(a[1]=u=>t(l).outTime=u),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u51FA\u5E93\u65F6\u95F4",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:o(()=>[e(d,{label:"\u5173\u8054\u8BA2\u5355",prop:"orderNo"},{default:o(()=>[e(i,{modelValue:t(l).orderNo,"onUpdate:modelValue":a[2]||(a[2]=u=>t(l).orderNo=u),readonly:""},{append:o(()=>[e(I,{onClick:X},{default:o(()=>[e(Z,{icon:"ep:search"}),g(" \u9009\u62E9 ")]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:o(()=>[e(d,{label:"\u5BA2\u6237",prop:"customerId"},{default:o(()=>[e(w,{modelValue:t(l).customerId,"onUpdate:modelValue":a[3]||(a[3]=u=>t(l).customerId=u),clearable:"",filterable:"",disabled:"",placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237",class:"!w-1/1"},{default:o(()=>[(n(!0),f(v,null,S(t(T),u=>(n(),p(k,{key:u.id,label:u.name,value:u.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:o(()=>[e(d,{label:"\u9500\u552E\u4EBA\u5458",prop:"saleUserId"},{default:o(()=>[e(w,{modelValue:t(l).saleUserId,"onUpdate:modelValue":a[4]||(a[4]=u=>t(l).saleUserId=u),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u9500\u552E\u4EBA\u5458",class:"!w-1/1"},{default:o(()=>[(n(!0),f(v,null,S(t(F),u=>(n(),p(k,{key:u.id,label:u.nickname,value:u.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{span:16},{default:o(()=>[e(d,{label:"\u5907\u6CE8",prop:"remark"},{default:o(()=>[e(i,{type:"textarea",modelValue:t(l).remark,"onUpdate:modelValue":a[5]||(a[5]=u=>t(l).remark=u),rows:1,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:o(()=>[e(d,{label:"\u9644\u4EF6",prop:"fileUrl"},{default:o(()=>[e(B,{"is-show-tip":!1,modelValue:t(l).fileUrl,"onUpdate:modelValue":a[6]||(a[6]=u=>t(l).fileUrl=u),limit:1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e($,null,{default:o(()=>[e(Y,{modelValue:t(U),"onUpdate:modelValue":a[7]||(a[7]=u=>q(U)?U.value=u:null),class:"-mt-15px -mb-10px"},{default:o(()=>[e(G,{label:"\u51FA\u5E93\u4EA7\u54C1\u6E05\u5355",name:"item"},{default:o(()=>[e(Fe,{ref_key:"itemFormRef",ref:A,items:t(l).items,disabled:t(h)},null,8,["items","disabled"])]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(R,{gutter:20},{default:o(()=>[e(s,{span:8},{default:o(()=>[e(d,{label:"\u4F18\u60E0\u7387\uFF08%\uFF09",prop:"discountPercent"},{default:o(()=>[e(E,{modelValue:t(l).discountPercent,"onUpdate:modelValue":a[8]||(a[8]=u=>t(l).discountPercent=u),"controls-position":"right",min:0,precision:2,placeholder:"\u8BF7\u8F93\u5165\u4F18\u60E0\u7387",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:o(()=>[e(d,{label:"\u6536\u6B3E\u4F18\u60E0",prop:"discountPrice"},{default:o(()=>[e(i,{disabled:"",modelValue:t(l).discountPrice,"onUpdate:modelValue":a[9]||(a[9]=u=>t(l).discountPrice=u),formatter:t(C)},null,8,["modelValue","formatter"])]),_:1})]),_:1}),e(s,{span:8},{default:o(()=>[e(d,{label:"\u4F18\u60E0\u540E\u91D1\u989D"},{default:o(()=>[e(i,{disabled:"","model-value":t(l).totalPrice-t(l).otherPrice,formatter:t(C)},null,8,["model-value","formatter"])]),_:1})]),_:1}),e(s,{span:8},{default:o(()=>[e(d,{label:"\u5176\u5B83\u8D39\u7528",prop:"otherPrice"},{default:o(()=>[e(E,{modelValue:t(l).otherPrice,"onUpdate:modelValue":a[10]||(a[10]=u=>t(l).otherPrice=u),"controls-position":"right",min:0,precision:2,placeholder:"\u8BF7\u8F93\u5165\u5176\u5B83\u8D39\u7528",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:o(()=>[e(d,{label:"\u7ED3\u7B97\u8D26\u6237",prop:"accountId"},{default:o(()=>[e(w,{modelValue:t(l).accountId,"onUpdate:modelValue":a[11]||(a[11]=u=>t(l).accountId=u),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u7ED3\u7B97\u8D26\u6237",class:"!w-1/1"},{default:o(()=>[(n(!0),f(v,null,S(t(y),u=>(n(),p(k,{key:u.id,label:u.name,value:u.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:o(()=>[e(d,{label:"\u5E94\u6536\u91D1\u989D"},{default:o(()=>[e(i,{disabled:"",modelValue:t(l).totalPrice,"onUpdate:modelValue":a[12]||(a[12]=u=>t(l).totalPrice=u),formatter:t(C)},null,8,["modelValue","formatter"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules","disabled"])),[[le,t(_)]])]),_:1},8,["title","modelValue"]),e(He,{ref_key:"saleOrderOutEnableListRef",ref:L,onSuccess:j},null,512)],64)}}})});export{H as _,Xe as __tla};
