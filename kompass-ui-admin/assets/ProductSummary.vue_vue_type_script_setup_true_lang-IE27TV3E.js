import{d as Y,I as q,r as v,f as G,T as H,o as C,l as S,w as c,g as N,i as a,a as e,H as Z,j as z,ev as f,aF as g,ct as J,_ as K,N as M,E as O,s as Q,__tla as V}from"./index-BUSn51wb.js";import{E as W,__tla as $}from"./el-card-CJbXGyyg.js";import{E as ee,__tla as te}from"./el-skeleton-item-tDN8U6BH.js";import{_ as ae,__tla as re}from"./Echart.vue_vue_type_script_setup_true_lang-DnLZLH3w.js";import{_ as le,__tla as oe}from"./index.vue_vue_type_script_setup_true_lang-BeC3r7Xt.js";import{P as h,__tla as se}from"./product-lJh9Q1vt.js";import{_ as p,__tla as ie}from"./index.vue_vue_type_script_setup_true_lang-CakuHPje.js";import{d as ne}from"./download-e0EdwhTv.js";import{C as ce,__tla as ue}from"./CardTitle-Dm4BG9kg.js";import{n as me,f as de,__tla as fe}from"./formatTime-DWdBpgsM.js";let F,pe=Promise.all([(()=>{try{return V}catch{}})(),(()=>{try{return $}catch{}})(),(()=>{try{return te}catch{}})(),(()=>{try{return re}catch{}})(),(()=>{try{return oe}catch{}})(),(()=>{try{return se}catch{}})(),(()=>{try{return ie}catch{}})(),(()=>{try{return ue}catch{}})(),(()=>{try{return fe}catch{}})()]).then(async()=>{let P;P={class:"flex flex-row items-center justify-between"},F=Y({name:"ProductSummary",__name:"ProductSummary",setup(_e){const A=q(),w=v(!0),b=v(!1),t=v(),_=v(),x=G({dataset:{dimensions:["time","browseCount","browseUserCount","orderPayPrice","afterSaleRefundPrice"],source:[]},grid:{left:20,right:20,bottom:20,top:80,containLabel:!0},legend:{top:50},series:[{name:"\u5546\u54C1\u6D4F\u89C8\u91CF",type:"line",smooth:!0,itemStyle:{color:"#B37FEB"}},{name:"\u5546\u54C1\u8BBF\u5BA2\u6570",type:"line",smooth:!0,itemStyle:{color:"#FFAB2B"}},{name:"\u652F\u4ED8\u91D1\u989D",type:"bar",smooth:!0,yAxisIndex:1,itemStyle:{color:"#1890FF"}},{name:"\u9000\u6B3E\u91D1\u989D",type:"bar",smooth:!0,yAxisIndex:1,itemStyle:{color:"#00C050"}}],toolbox:{feature:{dataZoom:{yAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u5546\u54C1\u72B6\u51B5"}}},tooltip:{trigger:"axis",axisPointer:{type:"cross"},padding:[5,10]},xAxis:{type:"category",boundaryGap:!0,axisTick:{show:!1}},yAxis:[{type:"value",name:"\u91D1\u989D",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{textStyle:{color:"#7F8B9C"}},splitLine:{show:!0,lineStyle:{color:"#F5F7F9"}}},{type:"value",name:"\u6570\u91CF",axisLine:{show:!1},axisTick:{show:!1},axisLabel:{textStyle:{color:"#7F8B9C"}},splitLine:{show:!0,lineStyle:{color:"#F5F7F9"}}}]}),R=async()=>{w.value=!0;const u=_.value.times;me(u[0],u[1])&&(u[0]=de(J(u[0]).subtract(1,"d"))),await Promise.all([k(),L()]),w.value=!1},k=async()=>{const u=_.value.times;t.value=await h.getProductStatisticsAnalyse({times:u})},L=async()=>{const u=_.value.times,y=await h.getProductStatisticsList({times:u});for(let m of y)m.orderPayPrice=g(m.orderPayPrice),m.afterSaleRefundPrice=g(m.afterSaleRefundPrice);x.dataset&&x.dataset.source&&(x.dataset.source=y)},B=async()=>{try{await A.exportConfirm(),b.value=!0;const u=_.value.times,y=await h.exportProductStatisticsExcel({times:u});ne.excel(y,"\u5546\u54C1\u72B6\u51B5.xls")}catch{}finally{b.value=!1}};return(u,y)=>{const m=K,E=M,I=le,d=O,T=Q,U=ae,j=ee,D=W,X=H("hasPermi");return C(),S(D,{shadow:"never"},{header:c(()=>[N("div",P,[a(e(ce),{title:"\u5546\u54C1\u6982\u51B5"}),a(I,{ref_key:"shortcutDateRangePicker",ref:_,onChange:R},{default:c(()=>[Z((C(),S(E,{class:"ml-4",onClick:B,loading:e(b)},{default:c(()=>[a(m,{icon:"ep:download",class:"mr-1"}),z("\u5BFC\u51FA ")]),_:1},8,["loading"])),[[X,["statistics:product:export"]]])]),_:1},512)])]),default:c(()=>[a(T,{gutter:16},{default:c(()=>[a(d,{xl:4,md:8,sm:24},{default:c(()=>{var r,l,o,s,i,n;return[a(p,{title:"\u5546\u54C1\u6D4F\u89C8\u91CF",tooltip:"\u5728\u9009\u5B9A\u6761\u4EF6\u4E0B\uFF0C\u6240\u6709\u5546\u54C1\u8BE6\u60C5\u9875\u88AB\u8BBF\u95EE\u7684\u6B21\u6570\uFF0C\u4E00\u4E2A\u4EBA\u5728\u7EDF\u8BA1\u65F6\u95F4\u5185\u8BBF\u95EE\u591A\u6B21\u8BB0\u4E3A\u591A\u6B21",icon:"ep:view","icon-color":"bg-blue-100","icon-bg-color":"text-blue-500",prefix:"",decimals:0,value:((l=(r=e(t))==null?void 0:r.value)==null?void 0:l.browseCount)||0,percent:e(f)((s=(o=e(t))==null?void 0:o.value)==null?void 0:s.browseCount,(n=(i=e(t))==null?void 0:i.reference)==null?void 0:n.browseCount)},null,8,["value","percent"])]}),_:1}),a(d,{xl:4,md:8,sm:24},{default:c(()=>{var r,l,o,s,i,n;return[a(p,{title:"\u5546\u54C1\u8BBF\u5BA2\u6570",tooltip:"\u5728\u9009\u5B9A\u6761\u4EF6\u4E0B\uFF0C\u8BBF\u95EE\u4EFB\u4F55\u5546\u54C1\u8BE6\u60C5\u9875\u7684\u4EBA\u6570\uFF0C\u4E00\u4E2A\u4EBA\u5728\u7EDF\u8BA1\u65F6\u95F4\u8303\u56F4\u5185\u8BBF\u95EE\u591A\u6B21\u53EA\u8BB0\u4E3A\u4E00\u4E2A",icon:"ep:user-filled","icon-color":"bg-purple-100","icon-bg-color":"text-purple-500",prefix:"",decimals:0,value:((l=(r=e(t))==null?void 0:r.value)==null?void 0:l.browseUserCount)||0,percent:e(f)((s=(o=e(t))==null?void 0:o.value)==null?void 0:s.browseUserCount,(n=(i=e(t))==null?void 0:i.reference)==null?void 0:n.browseUserCount)},null,8,["value","percent"])]}),_:1}),a(d,{xl:4,md:8,sm:24},{default:c(()=>{var r,l,o,s,i,n;return[a(p,{title:"\u652F\u4ED8\u4EF6\u6570",tooltip:"\u5728\u9009\u5B9A\u6761\u4EF6\u4E0B\uFF0C\u6210\u529F\u4ED8\u6B3E\u8BA2\u5355\u7684\u5546\u54C1\u4EF6\u6570\u4E4B\u548C",icon:"fa-solid:money-check-alt","icon-color":"bg-yellow-100","icon-bg-color":"text-yellow-500",prefix:"",decimals:0,value:((l=(r=e(t))==null?void 0:r.value)==null?void 0:l.orderPayCount)||0,percent:e(f)((s=(o=e(t))==null?void 0:o.value)==null?void 0:s.orderPayCount,(n=(i=e(t))==null?void 0:i.reference)==null?void 0:n.orderPayCount)},null,8,["value","percent"])]}),_:1}),a(d,{xl:4,md:8,sm:24},{default:c(()=>{var r,l,o,s,i,n;return[a(p,{title:"\u652F\u4ED8\u91D1\u989D",tooltip:"\u5728\u9009\u5B9A\u6761\u4EF6\u4E0B\uFF0C\u6210\u529F\u4ED8\u6B3E\u8BA2\u5355\u7684\u5546\u54C1\u91D1\u989D\u4E4B\u548C",icon:"ep:warning-filled","icon-color":"bg-green-100","icon-bg-color":"text-green-500",prefix:"\uFFE5",decimals:2,value:e(g)(((l=(r=e(t))==null?void 0:r.value)==null?void 0:l.orderPayPrice)||0),percent:e(f)((s=(o=e(t))==null?void 0:o.value)==null?void 0:s.orderPayPrice,(n=(i=e(t))==null?void 0:i.reference)==null?void 0:n.orderPayPrice)},null,8,["value","percent"])]}),_:1}),a(d,{xl:4,md:8,sm:24},{default:c(()=>{var r,l,o,s,i,n;return[a(p,{title:"\u9000\u6B3E\u4EF6\u6570",tooltip:"\u5728\u9009\u5B9A\u6761\u4EF6\u4E0B\uFF0C\u6210\u529F\u9000\u6B3E\u7684\u5546\u54C1\u4EF6\u6570\u4E4B\u548C",icon:"fa-solid:wallet","icon-color":"bg-cyan-100","icon-bg-color":"text-cyan-500",prefix:"",decimals:0,value:((l=(r=e(t))==null?void 0:r.value)==null?void 0:l.afterSaleCount)||0,percent:e(f)((s=(o=e(t))==null?void 0:o.value)==null?void 0:s.afterSaleCount,(n=(i=e(t))==null?void 0:i.reference)==null?void 0:n.afterSaleCount)},null,8,["value","percent"])]}),_:1}),a(d,{xl:4,md:8,sm:24},{default:c(()=>{var r,l,o,s,i,n;return[a(p,{title:"\u9000\u6B3E\u91D1\u989D",tooltip:"\u5728\u9009\u5B9A\u6761\u4EF6\u4E0B\uFF0C\u6210\u529F\u9000\u6B3E\u7684\u5546\u54C1\u91D1\u989D\u4E4B\u548C",icon:"fa-solid:award","icon-color":"bg-yellow-100","icon-bg-color":"text-yellow-500",prefix:"\uFFE5",decimals:2,value:e(g)(((l=(r=e(t))==null?void 0:r.value)==null?void 0:l.afterSaleRefundPrice)||0),percent:e(f)((s=(o=e(t))==null?void 0:o.value)==null?void 0:s.afterSaleRefundPrice,(n=(i=e(t))==null?void 0:i.reference)==null?void 0:n.afterSaleRefundPrice)},null,8,["value","percent"])]}),_:1})]),_:1}),a(j,{loading:e(w),animated:""},{default:c(()=>[a(U,{height:500,options:e(x)},null,8,["options"])]),_:1},8,["loading"])]),_:1})}}})});export{F as _,pe as __tla};
