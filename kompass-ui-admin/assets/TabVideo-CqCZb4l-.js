import{d as A,I as C,e9 as E,b as F,r as y,f as M,o as h,c as N,i as a,w as r,a as l,l as S,a9 as T,j as V,y as P,Z as W,s as Z,_ as q,N as D,cF as G,E as H,bw as J,B as K,__tla as L}from"./index-BUSn51wb.js";import{_ as O,__tla as Q}from"./main.vue_vue_type_script_setup_true_lang-Bi4NZ0QE.js";import{W as R,__tla as X}from"./main-DvybYriQ.js";import{u as Y,U as $,__tla as tt}from"./useUpload-gjof4KYU.js";import{__tla as at}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{__tla as et}from"./index-Cch5e1V0.js";import{__tla as lt}from"./main-DwQbyLY9.js";import{__tla as rt}from"./el-image-BjHZRFih.js";import{__tla as ot}from"./main-CG5euiEw.js";import{__tla as ut}from"./index-C4ZN3JCQ.js";import{__tla as _t}from"./index-Cqwyhbsb.js";import{__tla as it}from"./formatTime-DWdBpgsM.js";let v,st=Promise.all([(()=>{try{return L}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return tt}catch{}})(),(()=>{try{return at}catch{}})(),(()=>{try{return et}catch{}})(),(()=>{try{return lt}catch{}})(),(()=>{try{return rt}catch{}})(),(()=>{try{return ot}catch{}})(),(()=>{try{return ut}catch{}})(),(()=>{try{return _t}catch{}})(),(()=>{try{return it}catch{}})()]).then(async()=>{v=K(A({__name:"TabVideo",props:{modelValue:{}},emits:["update:modelValue"],setup(I,{emit:b}){const g=C(),w={Authorization:"Bearer "+E()},U=I,k=b,e=F({get:()=>U.modelValue,set:t=>k("update:modelValue",t)}),_=y(!1),c=y([]),i=M({accountId:e.value.accountId,type:"video",title:"",introduction:""}),j=t=>Y($.Video,10)(t),x=t=>{if(t.code!==0)return g.error("\u4E0A\u4F20\u51FA\u9519\uFF1A"+t.msg),!1;c.value=[],i.title="",i.introduction="",n(t.data)},n=t=>{_.value=!1,e.value.mediaId=t.mediaId,e.value.url=t.url,e.value.name=t.name,t.title&&(e.value.title=t.title||""),t.introduction&&(e.value.description=t.introduction||"")};return(t,o)=>{const m=W,s=Z,p=q,f=D,B=G,d=H,z=J;return h(),N("div",null,[a(s,null,{default:r(()=>[a(m,{modelValue:l(e).title,"onUpdate:modelValue":o[0]||(o[0]=u=>l(e).title=u),class:"input-margin-bottom",placeholder:"\u8BF7\u8F93\u5165\u6807\u9898"},null,8,["modelValue"]),a(m,{class:"input-margin-bottom",modelValue:l(e).description,"onUpdate:modelValue":o[1]||(o[1]=u=>l(e).description=u),placeholder:"\u8BF7\u8F93\u5165\u63CF\u8FF0"},null,8,["modelValue"]),a(s,{class:"ope-row",justify:"center"},{default:r(()=>[l(e).url?(h(),S(l(O),{key:0,url:l(e).url},null,8,["url"])):T("",!0)]),_:1}),a(d,null,{default:r(()=>[a(s,{style:{"text-align":"center"},align:"middle"},{default:r(()=>[a(d,{span:12},{default:r(()=>[a(f,{type:"success",onClick:o[2]||(o[2]=u=>_.value=!0)},{default:r(()=>[V(" \u7D20\u6750\u5E93\u9009\u62E9 "),a(p,{icon:"ep:circle-check"})]),_:1}),a(B,{title:"\u9009\u62E9\u89C6\u9891",modelValue:l(_),"onUpdate:modelValue":o[3]||(o[3]=u=>P(_)?_.value=u:null),width:"90%","append-to-body":"","destroy-on-close":""},{default:r(()=>[a(l(R),{type:"video","account-id":l(e).accountId,onSelectMaterial:n},null,8,["account-id"])]),_:1},8,["modelValue"])]),_:1}),a(d,{span:12},{default:r(()=>[a(z,{action:"http://**************:48080/admin-api/mp/material/upload-temporary",headers:w,multiple:"",limit:1,"file-list":l(c),data:l(i),"before-upload":j,"on-success":x},{default:r(()=>[a(f,{type:"primary"},{default:r(()=>[V("\u65B0\u5EFA\u89C6\u9891 "),a(p,{icon:"ep:upload"})]),_:1})]),_:1},8,["file-list","data"])]),_:1})]),_:1})]),_:1})]),_:1})])}}}),[["__scopeId","data-v-4694e799"]])});export{st as __tla,v as default};
