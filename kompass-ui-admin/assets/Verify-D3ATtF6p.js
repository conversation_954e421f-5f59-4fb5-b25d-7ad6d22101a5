import{dr as q,n as ee,c7 as te,as as ne,r as t,f as M,b as re,at as Ie,C as oe,o as T,c as C,a as e,av as w,g as s,H as ie,a8 as ae,i as $e,w as Oe,a0 as Y,t as V,a9 as A,a3 as Ce,ay as ce,ds as ue,dt as pe,F as Be,k as Ne,B as _e,bx as je,j as Ee,l as De,b0 as Je,__tla as Me}from"./index-BUSn51wb.js";let ve,Ve=Promise.all([(()=>{try{return Me}catch{}})()]).then(async()=>{function F(i,f="XwKsGlMcdPMEhR1B"){const l=q.enc.Utf8.parse(f),o=q.enc.Utf8.parse(i);return q.AES.encrypt(o,l,{mode:q.mode.ECB,padding:q.pad.Pkcs7}).toString()}function le(i){let f,l,o,g;const r=window,c=i.$el.parentNode.offsetWidth||r.offsetWidth,y=i.$el.parentNode.offsetHeight||r.offsetHeight;return f=i.imgSize.width.indexOf("%")!=-1?parseInt(i.imgSize.width)/100*c+"px":i.imgSize.width,l=i.imgSize.height.indexOf("%")!=-1?parseInt(i.imgSize.height)/100*y+"px":i.imgSize.height,o=i.barSize.width.indexOf("%")!=-1?parseInt(i.barSize.width)/100*c+"px":i.barSize.width,g=i.barSize.height.indexOf("%")!=-1?parseInt(i.barSize.height)/100*y+"px":i.barSize.height,{imgWidth:f,imgHeight:l,barWidth:o,barHeight:g}}const he={style:{position:"relative"}},de=["src"],ge=[s("i",{class:"iconfont icon-refresh"},null,-1)],fe=["textContent"],ye=["textContent"],me=["src"],be={__name:"VerifySlide",props:{captchaType:{type:String},type:{type:String,default:"1"},mode:{type:String,default:"fixed"},vSpace:{type:Number,default:5},explain:{type:String,default:""},imgSize:{type:Object,default:()=>({width:"310px",height:"155px"})},blockSize:{type:Object,default:()=>({width:"50px",height:"50px"})},barSize:{type:Object,default:()=>({width:"310px",height:"30px"})}},setup(i){const f=i,{t:l}=ee(),{mode:o,captchaType:g,type:r,blockSize:c,explain:y}=te(f),{proxy:p}=ne();let u=t(""),z=t(""),L=t(""),P=t(""),H=t(""),m=t(""),_=t(""),b=t(""),k=t(""),j=t(""),v=M({imgHeight:0,imgWidth:0,barHeight:0,barWidth:0}),B=t(void 0),I=t(void 0),$=t(void 0),N=t("#ddd"),O=t(void 0),E=t("icon-right"),D=t(!1),d=t(!1),n=t(!0),x=t(""),S=t(""),X=t(0);const J=re(()=>p.$el.querySelector(".verify-bar-area")),K=()=>{y.value===""?k.value=l("captcha.slide"):k.value=y.value,se(),ce(()=>{let{imgHeight:a,imgWidth:h,barHeight:W,barWidth:Z}=le(p);v.imgHeight=a,v.imgWidth=h,v.barHeight=W,v.barWidth=Z,p.$parent.$emit("ready",p)}),window.removeEventListener("touchmove",function(a){U(a)}),window.removeEventListener("mousemove",function(a){U(a)}),window.removeEventListener("touchend",function(){G()}),window.removeEventListener("mouseup",function(){G()}),window.addEventListener("touchmove",function(a){U(a)}),window.addEventListener("mousemove",function(a){U(a)}),window.addEventListener("touchend",function(){G()}),window.addEventListener("mouseup",function(){G()})};Ie(r,()=>{K()}),oe(()=>{K(),p.$el.onselectstart=function(){return!1}});const R=a=>{if((a=a||window.event).touches)h=a.touches[0].pageX;else var h=a.clientX;X.value=Math.floor(h-J.value.getBoundingClientRect().left),m.value=+new Date,d.value==0&&(k.value="",$.value="#337ab7",N.value="#337AB7",O.value="#fff",a.stopPropagation(),D.value=!0)},U=a=>{if(a=a||window.event,D.value&&d.value==0){if(a.touches)h=a.touches[0].pageX;else var h=a.clientX;var W=h-J.value.getBoundingClientRect().left;W>=J.value.offsetWidth-parseInt(parseInt(c.value.width)/2)-2&&(W=J.value.offsetWidth-parseInt(parseInt(c.value.width)/2)-2),W<=0&&(W=parseInt(parseInt(c.value.width)/2)),B.value=W-X.value+"px",I.value=W-X.value+"px"}},G=()=>{if(_.value=+new Date,D.value&&d.value==0){var a=parseInt((B.value||"0").replace("px",""));a=310*a/parseInt(v.imgWidth);let h={captchaType:g.value,pointJson:u.value?F(JSON.stringify({x:a,y:5}),u.value):JSON.stringify({x:a,y:5}),token:H.value};ue(h).then(W=>{if(W.repCode=="0000"){$.value="#5cb85c",N.value="#5cb85c",O.value="#fff",E.value="icon-check",n.value=!1,d.value=!0,o.value=="pop"&&setTimeout(()=>{p.$parent.clickShow=!1,Q()},1500),z.value=!0,b.value=`${((_.value-m.value)/1e3).toFixed(2)}s
            ${l("captcha.success")}`;var Z=u.value?F(H.value+"---"+JSON.stringify({x:a,y:5}),u.value):H.value+"---"+JSON.stringify({x:a,y:5});setTimeout(()=>{b.value="",p.$parent.closeBox(),p.$parent.$emit("success",{captchaVerification:Z})},1e3)}else $.value="#d9534f",N.value="#d9534f",O.value="#fff",E.value="icon-close",z.value=!1,setTimeout(function(){Q()},1e3),p.$parent.$emit("error",p),b.value=l("captcha.fail"),setTimeout(()=>{b.value=""},1e3)}),D.value=!1}},Q=async()=>{n.value=!0,j.value="",x.value="left .3s",B.value=0,I.value=void 0,S.value="width .3s",N.value="#ddd",$.value="#fff",O.value="#000",E.value="icon-right",d.value=!1,await se(),setTimeout(()=>{S.value="",x.value="",k.value=y.value},300)},se=async()=>{let a={captchaType:g.value};const h=await pe(a);h.repCode=="0000"?(L.value=h.repData.originalImageBase64,P.value=h.repData.jigsawImageBase64,H.value=h.repData.token,u.value=h.repData.secretKey):b.value=h.repMsg};return(a,h)=>(T(),C("div",he,[e(r)==="2"?(T(),C("div",{key:0,style:w({height:parseInt(e(v).imgHeight)+i.vSpace+"px"}),class:"verify-img-out"},[s("div",{style:w({width:e(v).imgWidth,height:e(v).imgHeight}),class:"verify-img-panel"},[s("img",{src:"data:image/png;base64,"+e(L),alt:"",style:{display:"block",width:"100%",height:"100%"}},null,8,de),ie(s("div",{class:"verify-refresh",onClick:Q},ge,512),[[ae,e(n)]]),$e(Ce,{name:"tips"},{default:Oe(()=>[e(b)?(T(),C("span",{key:0,class:Y([e(z)?"suc-bg":"err-bg","verify-tips"])},V(e(b)),3)):A("",!0)]),_:1})],4)],4)):A("",!0),s("div",{style:w({width:e(v).imgWidth,height:i.barSize.height,"line-height":i.barSize.height}),class:"verify-bar-area"},[s("span",{class:"verify-msg",textContent:V(e(k))},null,8,fe),s("div",{style:w({width:e(I)!==void 0?e(I):i.barSize.height,height:i.barSize.height,"border-color":e(N),transaction:e(S)}),class:"verify-left-bar"},[s("span",{class:"verify-msg",textContent:V(e(j))},null,8,ye),s("div",{style:w({width:i.barSize.height,height:i.barSize.height,"background-color":e($),left:e(B),transition:e(x)}),class:"verify-move-block",onMousedown:R,onTouchstart:R},[s("i",{class:Y(["verify-icon iconfont",e(E)]),style:w({color:e(O)})},null,6),e(r)==="2"?(T(),C("div",{key:0,style:w({width:Math.floor(47*parseInt(e(v).imgWidth)/310)+"px",height:e(v).imgHeight,top:"-"+(parseInt(e(v).imgHeight)+i.vSpace)+"px","background-size":e(v).imgWidth+" "+e(v).imgHeight}),class:"verify-sub-block"},[s("img",{src:"data:image/png;base64,"+e(P),alt:"",style:{display:"block",width:"100%",height:"100%","-webkit-user-drag":"none"}},null,8,me)],4)):A("",!0)],36)],4)],4)]))}},xe={style:{position:"relative"}},Se={class:"verify-img-out"},we=[s("i",{class:"iconfont icon-refresh"},null,-1)],ze=["src"],ke={class:"verify-msg"},We={name:"Vue3Verify",components:{VerifySlide:be,VerifyPoints:{__name:"VerifyPoints",props:{mode:{type:String,default:"fixed"},captchaType:{type:String},vSpace:{type:Number,default:5},imgSize:{type:Object,default:()=>({width:"310px",height:"155px"})},barSize:{type:Object,default:()=>({width:"310px",height:"40px"})}},setup(i){const f=i,{t:l}=ee(),{mode:o,captchaType:g}=te(f),{proxy:r}=ne();let c=t(""),y=t(3),p=M([]),u=M([]),z=t(1),L=t(""),P=M([]),H=t(""),m=M({imgHeight:0,imgWidth:0,barHeight:0,barWidth:0}),_=M([]),b=t(""),k=t(void 0),j=t(void 0),v=t(!0),B=t(!0);oe(()=>{p.splice(0,p.length),u.splice(0,u.length),z.value=1,E(),ce(()=>{let{imgHeight:d,imgWidth:n,barHeight:x,barWidth:S}=le(r);m.imgHeight=d,m.imgWidth=n,m.barHeight=x,m.barWidth=S,r.$parent.$emit("ready",r)}),r.$el.onselectstart=function(){return!1}});const I=t(null),$=function(d,n){return{x:n.offsetX,y:n.offsetY}},N=function(d){return _.push(Object.assign({},d)),z.value+1},O=async function(){_.splice(0,_.length),k.value="#000",j.value="#ddd",B.value=!0,p.splice(0,p.length),u.splice(0,u.length),z.value=1,await E(),v.value=!0},E=async()=>{let d={captchaType:g.value};const n=await pe(d);n.repCode=="0000"?(L.value=n.repData.originalImageBase64,H.value=n.repData.token,c.value=n.repData.secretKey,P.value=n.repData.wordList,b.value=l("captcha.point")+"\u3010"+P.value.join(",")+"\u3011"):b.value=n.repMsg},D=function(d,n){return d.map(x=>({x:Math.round(310*x.x/parseInt(n.imgWidth)),y:Math.round(155*x.y/parseInt(n.imgHeight))}))};return(d,n)=>(T(),C("div",xe,[s("div",Se,[s("div",{style:w({width:e(m).imgWidth,height:e(m).imgHeight,"background-size":e(m).imgWidth+" "+e(m).imgHeight,"margin-bottom":i.vSpace+"px"}),class:"verify-img-panel"},[ie(s("div",{class:"verify-refresh",style:{"z-index":"3"},onClick:O},we,512),[[ae,e(v)]]),s("img",{ref_key:"canvas",ref:I,src:"data:image/png;base64,"+e(L),alt:"",style:{display:"block",width:"100%",height:"100%"},onClick:n[0]||(n[0]=x=>e(B)?(S=>{if(u.push($(I,S)),z.value==y.value){z.value=N($(I,S));let X=D(u,m);u.length=0,u.push(...X),setTimeout(()=>{var J=c.value?F(H.value+"---"+JSON.stringify(u),c.value):H.value+"---"+JSON.stringify(u);let K={captchaType:g.value,pointJson:c.value?F(JSON.stringify(u),c.value):JSON.stringify(u),token:H.value};ue(K).then(R=>{R.repCode=="0000"?(k.value="#4cae4c",j.value="#5cb85c",b.value=l("captcha.success"),B.value=!1,o.value=="pop"&&setTimeout(()=>{r.$parent.clickShow=!1,O()},1500),r.$parent.$emit("success",{captchaVerification:J})):(r.$parent.$emit("error",r),k.value="#d9534f",j.value="#d9534f",b.value=l("captcha.fail"),setTimeout(()=>{O()},700))})},400)}z.value<y.value&&(z.value=N($(I,S)))})(x):void 0)},null,8,ze),(T(!0),C(Be,null,Ne(e(_),(x,S)=>(T(),C("div",{key:S,style:w({"background-color":"#1abd6c",color:"#fff","z-index":9999,width:"20px",height:"20px","text-align":"center","line-height":"20px","border-radius":"50%",position:"absolute",top:parseInt(x.y-10)+"px",left:parseInt(x.x-10)+"px"}),class:"point-area"},V(S+1),5))),128))],4)]),s("div",{style:w({width:e(m).imgWidth,color:e(k),"border-color":e(j),"line-height":i.barSize.height}),class:"verify-bar-area"},[s("span",ke,V(e(b)),1)],4)]))}}},props:{captchaType:{type:String,required:!0},figure:{type:Number},arith:{type:Number},mode:{type:String,default:"pop"},vSpace:{type:Number},explain:{type:String},imgSize:{type:Object,default:()=>({width:"310px",height:"155px"})},blockSize:{type:Object},barSize:{type:Object}},setup(i){const{t:f}=ee(),{captchaType:l,mode:o}=te(i),g=t(!1),r=t(void 0),c=t(void 0),y=t({}),p=re(()=>o.value!="pop"||g.value);return je(()=>{switch(l.value){case"blockPuzzle":r.value="2",c.value="VerifySlide";break;case"clickWord":r.value="",c.value="VerifyPoints"}}),{t:f,clickShow:g,verifyType:r,componentType:c,instance:y,showBox:p,closeBox:()=>{g.value=!1,y.value.refresh&&y.value.refresh()},show:()=>{o.value=="pop"&&(g.value=!0)}}}},Te={key:0,class:"verifybox-top"},He=[s("i",{class:"iconfont icon-close"},null,-1)];ve=_e(We,[["render",function(i,f,l,o,g,r){return ie((T(),C("div",{class:Y(l.mode=="pop"?"mask":"")},[s("div",{class:Y(l.mode=="pop"?"verifybox":""),style:w({"max-width":parseInt(l.imgSize.width)+20+"px"})},[l.mode=="pop"?(T(),C("div",Te,[Ee(V(o.t("captcha.verification"))+" ",1),s("span",{class:"verifybox-close",onClick:f[0]||(f[0]=(...c)=>o.closeBox&&o.closeBox(...c))},He)])):A("",!0),s("div",{style:w({padding:l.mode=="pop"?"10px":"0"}),class:"verifybox-bottom"},[o.componentType?(T(),De(Je(o.componentType),{key:0,ref:"instance",arith:l.arith,barSize:l.barSize,blockSize:l.blockSize,captchaType:l.captchaType,explain:l.explain,figure:l.figure,imgSize:l.imgSize,mode:l.mode,type:o.verifyType,vSpace:l.vSpace},null,8,["arith","barSize","blockSize","captchaType","explain","figure","imgSize","mode","type","vSpace"])):A("",!0)],4)],6)],2)),[[ae,o.showBox]])}]])});export{ve as _,Ve as __tla};
