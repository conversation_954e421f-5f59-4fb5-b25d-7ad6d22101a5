import{_ as w}from"./app-nav-bar-mp-QvSN8lzY.js";import x,{__tla as k}from"./index-GnBGT4K0.js";import{d as C,b as p,o as e,c as r,g as I,F as M,k as B,av as _,t as R,l as F,a as d,a9 as N,B as P,__tla as S}from"./index-BUSn51wb.js";let m,T=Promise.all([(()=>{try{return k}catch{}})(),(()=>{try{return S}catch{}})()]).then(async()=>{let s,o,i,c;s={class:"h-full w-full flex items-center"},o={key:0},i=["src"],c={key:0,src:w,alt:"",class:"h-30px w-86px"},m=P(C({name:"NavigationBar",__name:"index",props:{property:{}},setup(u){const t=u,g=p(()=>({background:t.property.bgType==="img"&&t.property.bgImg?`url(${t.property.bgImg}) no-repeat top center / 100% 100%`:t.property.bgColor})),h=p(()=>{var a;return(a=t.property._local)!=null&&a.previewMp?t.property.mpCells:t.property.otherCells}),y=p(()=>{var a;return(a=t.property._local)!=null&&a.previewMp?209/6:285/8}),f=a=>({width:a.width*y.value+10*(a.width-1)+"px",left:a.left*y.value+10*(a.left+1)+"px",position:"absolute"}),v=a=>({height:30,showScan:!1,placeholder:a.placeholder,borderRadius:a.borderRadius});return(a,U)=>{var n;return e(),r("div",{class:"navigation-bar",style:_(d(g))},[I("div",s,[(e(!0),r(M,null,B(d(h),(l,b)=>(e(),r("div",{key:b,style:_(f(l))},[l.type==="text"?(e(),r("span",o,R(l.text),1)):l.type==="image"?(e(),r("img",{key:1,src:l.imgUrl,alt:"",class:"h-full w-full"},null,8,i)):(e(),F(x,{key:2,property:v}))],4))),128))]),(n=a.property._local)!=null&&n.previewMp?(e(),r("img",c)):N("",!0)],4)}}}),[["__scopeId","data-v-59f247fe"]])});export{T as __tla,m as default};
