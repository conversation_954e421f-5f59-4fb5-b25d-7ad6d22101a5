import{d as n,b as p,o as _,l as V,a as c,y as i,Z as y,__tla as h}from"./index-BUSn51wb.js";let o,x=Promise.all([(()=>{try{return h}catch{}})()]).then(async()=>{o=n({__name:"TabText",props:{modelValue:{}},emits:["update:modelValue","input"],setup(s,{emit:u}){const r=s,l=u,e=p({get:()=>r.modelValue,set:a=>{l("update:modelValue",a),l("input",a)}});return(a,t)=>{const d=y;return _(),V(d,{type:"textarea",rows:5,placeholder:"\u8BF7\u8F93\u5165\u5185\u5BB9",modelValue:c(e),"onUpdate:modelValue":t[0]||(t[0]=m=>i(e)?e.value=m:null)},null,8,["modelValue"])}}})});export{o as _,x as __tla};
