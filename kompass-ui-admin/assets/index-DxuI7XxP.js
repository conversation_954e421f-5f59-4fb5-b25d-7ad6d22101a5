import{d as A,I as B,n as D,r as s,f as G,C as M,T as X,o as p,c as g,i as a,w as t,a as l,F as v,k as z,l as i,j as d,H as w,eo as Y,J as Z,K as $,L as aa,_ as ea,N as la,O as ta,P as ra,Q as oa,R as sa,__tla as pa}from"./index-BUSn51wb.js";import{_ as ca,__tla as na}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as ia,__tla as ua}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as _a,__tla as da}from"./index-COobLwz-.js";import{d as ma}from"./download-e0EdwhTv.js";import{S as L,__tla as fa}from"./index-BCEOZol9.js";import{P as ha,__tla as ya}from"./index-B00QUU3o.js";import{W as ga,__tla as va}from"./index-B5GxX3eg.js";import{__tla as wa}from"./index-Cch5e1V0.js";import{__tla as ba}from"./el-card-CJbXGyyg.js";let q,xa=Promise.all([(()=>{try{return pa}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return ua}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return fa}catch{}})(),(()=>{try{return ya}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return wa}catch{}})(),(()=>{try{return ba}catch{}})()]).then(async()=>{q=A({name:"ErpStock",__name:"index",setup(ka){const R=B();D();const m=s(!0),b=s([]),x=s(0),r=G({pageNo:1,pageSize:10,productId:void 0,warehouseId:void 0}),k=s(),f=s(!1),I=s([]),N=s([]),h=async()=>{m.value=!0;try{const c=await L.getStockPage(r);b.value=c.list,x.value=c.total}finally{m.value=!1}},S=()=>{r.pageNo=1,h()},W=()=>{k.value.resetFields(),S()},j=s(),E=async()=>{try{await R.exportConfirm(),f.value=!0;const c=await L.exportStock(r);ma.excel(c,"\u4EA7\u54C1\u5E93\u5B58.xls")}catch{}finally{f.value=!1}};return M(async()=>{await h(),I.value=await ha.getProductSimpleList(),N.value=await ga.getWarehouseSimpleList()}),(c,o)=>{const H=_a,P=Z,V=$,y=aa,u=ea,_=la,J=ta,C=ia,n=ra,K=oa,O=ca,F=X("hasPermi"),Q=sa;return p(),g(v,null,[a(H,{title:"\u3010\u5E93\u5B58\u3011\u4EA7\u54C1\u5E93\u5B58\u3001\u5E93\u5B58\u660E\u7EC6",url:"https://doc.iocoder.cn/erp/stock/"}),a(C,null,{default:t(()=>[a(J,{class:"-mb-15px",model:l(r),ref_key:"queryFormRef",ref:k,inline:!0,"label-width":"68px"},{default:t(()=>[a(y,{label:"\u4EA7\u54C1",prop:"productId"},{default:t(()=>[a(V,{modelValue:l(r).productId,"onUpdate:modelValue":o[0]||(o[0]=e=>l(r).productId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1",class:"!w-240px"},{default:t(()=>[(p(!0),g(v,null,z(l(I),e=>(p(),i(P,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(y,{label:"\u4ED3\u5E93",prop:"warehouseId"},{default:t(()=>[a(V,{modelValue:l(r).warehouseId,"onUpdate:modelValue":o[1]||(o[1]=e=>l(r).warehouseId=e),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4ED3\u5E93",class:"!w-240px"},{default:t(()=>[(p(!0),g(v,null,z(l(N),e=>(p(),i(P,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(y,null,{default:t(()=>[a(_,{onClick:S},{default:t(()=>[a(u,{icon:"ep:search",class:"mr-5px"}),d(" \u641C\u7D22")]),_:1}),a(_,{onClick:W},{default:t(()=>[a(u,{icon:"ep:refresh",class:"mr-5px"}),d(" \u91CD\u7F6E")]),_:1}),w((p(),i(_,{type:"primary",plain:"",onClick:o[2]||(o[2]=e=>{return U="create",void j.value.open(U,T);var U,T})},{default:t(()=>[a(u,{icon:"ep:plus",class:"mr-5px"}),d(" \u65B0\u589E ")]),_:1})),[[F,["erp:stock:create"]]]),w((p(),i(_,{type:"success",plain:"",onClick:E,loading:l(f)},{default:t(()=>[a(u,{icon:"ep:download",class:"mr-5px"}),d(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[F,["erp:stock:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(C,null,{default:t(()=>[w((p(),i(K,{data:l(b),stripe:!0,"show-overflow-tooltip":!0},{default:t(()=>[a(n,{label:"\u4EA7\u54C1\u540D\u79F0",align:"center",prop:"productName"}),a(n,{label:"\u4EA7\u54C1\u5355\u4F4D",align:"center",prop:"unitName"}),a(n,{label:"\u4EA7\u54C1\u5206\u7C7B",align:"center",prop:"categoryName"}),a(n,{label:"\u5E93\u5B58\u91CF",align:"center",prop:"count",formatter:l(Y)},null,8,["formatter"]),a(n,{label:"\u4ED3\u5E93",align:"center",prop:"warehouseName"})]),_:1},8,["data"])),[[Q,l(m)]]),a(O,{total:l(x),page:l(r).pageNo,"onUpdate:page":o[3]||(o[3]=e=>l(r).pageNo=e),limit:l(r).pageSize,"onUpdate:limit":o[4]||(o[4]=e=>l(r).pageSize=e),onPagination:h},null,8,["total","page","limit"])]),_:1})],64)}}})});export{xa as __tla,q as default};
