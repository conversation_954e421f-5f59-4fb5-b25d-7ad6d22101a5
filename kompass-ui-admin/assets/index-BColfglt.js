import{d as u,o as e,c as s,g as l,F as d,k as m,i as y,w as g,av as n,t as f,_ as v,B as k,__tla as h}from"./index-BUSn51wb.js";import{E as x,__tla as I}from"./el-image-BjHZRFih.js";let i,w=Promise.all([(()=>{try{return h}catch{}})(),(()=>{try{return I}catch{}})()]).then(async()=>{let o,c,p;o={class:"tab-bar"},c={class:"h-full w-full flex items-center justify-center"},p=u({name:"TabBar",__name:"index",props:{property:{}},setup:B=>(r,C)=>{const _=v,b=x;return e(),s("div",o,[l("div",{class:"tab-bar-bg",style:n({background:r.property.style.bgType==="color"?r.property.style.bgColor:`url(${r.property.style.bgImg})`,backgroundSize:"100% 100%",backgroundRepeat:"no-repeat"})},[(e(!0),s(d,null,m(r.property.items,(t,a)=>(e(),s("div",{key:a,class:"tab-bar-item"},[y(b,{src:a===0?t.activeIconUrl:t.iconUrl},{error:g(()=>[l("div",c,[y(_,{icon:"ep:picture"})])]),_:2},1032,["src"]),l("span",{style:n({color:a===0?r.property.style.activeColor:r.property.style.color})},f(t.text),5)]))),128))],4)])}}),i=k(p,[["__scopeId","data-v-7eef33df"]])});export{w as __tla,i as default};
