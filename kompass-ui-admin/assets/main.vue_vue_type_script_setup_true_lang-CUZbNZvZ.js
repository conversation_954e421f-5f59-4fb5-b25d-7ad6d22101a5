import{d as h,f as p,r as v,C as f,o as s,l as u,w as g,c as y,F as x,k as w,a as m,J as V,K as b,__tla as k}from"./index-BUSn51wb.js";import{f as C,__tla as A}from"./index-C-Ee_eqi.js";let c,F=Promise.all([(()=>{try{return k}catch{}})(),(()=>{try{return A}catch{}})()]).then(async()=>{c=h({name:"WxAccountSelect",__name:"main",emits:["change"],setup(J,{emit:r}){const a=p({id:-1,name:""}),e=v([]),d=r,o=i=>{const l=e.value.find(t=>t.id===i);a.id&&(a.name=l?l.name:"",d("change",a.id,a.name))};return f(()=>{(async()=>(e.value=await C(),e.value.length>0&&(a.id=e.value[0].id,a.id&&(a.name=e.value[0].name,d("change",a.id,a.name)))))()}),(i,l)=>{const t=V,_=b;return s(),u(_,{modelValue:m(a).id,"onUpdate:modelValue":l[0]||(l[0]=n=>m(a).id=n),placeholder:"\u8BF7\u9009\u62E9\u516C\u4F17\u53F7",class:"!w-240px",onChange:o},{default:g(()=>[(s(!0),y(x,null,w(m(e),n=>(s(),u(t,{key:n.id,label:n.name,value:n.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])}}})});export{c as _,F as __tla};
