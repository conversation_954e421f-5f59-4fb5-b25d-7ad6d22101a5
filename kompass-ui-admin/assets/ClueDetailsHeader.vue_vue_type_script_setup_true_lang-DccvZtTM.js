import{d as C,o as c,c as o,H as h,g as s,i as a,w as l,t as _,aV as x,a as i,G as E,j as r,F as R,s as g,E as w,R as H,__tla as U}from"./index-BUSn51wb.js";import{_ as j,__tla as D}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{E as M,a as T,__tla as O}from"./el-descriptions-item-dD3qa0ub.js";import{_ as S,__tla as B}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{f as F,__tla as G}from"./formatTime-DWdBpgsM.js";let m,N=Promise.all([(()=>{try{return U}catch{}})(),(()=>{try{return D}catch{}})(),(()=>{try{return O}catch{}})(),(()=>{try{return B}catch{}})(),(()=>{try{return G}catch{}})()]).then(async()=>{let u,n;u={class:"flex items-start justify-between"},n={class:"text-xl font-bold"},m=C({name:"CrmClueDetailsHeader",__name:"ClueDetailsHeader",props:{clue:{},loading:{type:Boolean}},setup:P=>(t,V)=>{const d=g,f=w,p=S,e=M,y=T,b=j,v=H;return c(),o(R,null,[h((c(),o("div",null,[s("div",u,[s("div",null,[a(f,null,{default:l(()=>[a(d,null,{default:l(()=>[s("span",n,_(t.clue.name),1)]),_:1})]),_:1})]),s("div",null,[x(t.$slots,"default")])])])),[[v,t.loading]]),a(b,{class:"mt-10px"},{default:l(()=>[a(y,{column:5,direction:"vertical"},{default:l(()=>[a(e,{label:"\u7EBF\u7D22\u6765\u6E90"},{default:l(()=>[a(p,{type:i(E).CRM_CUSTOMER_SOURCE,value:t.clue.source},null,8,["type","value"])]),_:1}),a(e,{label:"\u624B\u673A"},{default:l(()=>[r(_(t.clue.mobile),1)]),_:1}),a(e,{label:"\u8D1F\u8D23\u4EBA"},{default:l(()=>[r(_(t.clue.ownerUserName),1)]),_:1}),a(e,{label:"\u521B\u5EFA\u65F6\u95F4"},{default:l(()=>[r(_(i(F)(t.clue.createTime)),1)]),_:1})]),_:1})]),_:1})],64)}})});export{m as _,N as __tla};
