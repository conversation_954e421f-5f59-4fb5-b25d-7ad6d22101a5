import{_ as I,__tla as V}from"./Form-DJa9ov9B.js";import{_ as x,__tla as F}from"./XButton-BjahQbul.js";import{d as L,b as U,a,f as q,r as B,H as C,a8 as E,o as S,l as j,w as c,i as t,g as i,n as z,Z as G,__tla as H}from"./index-BUSn51wb.js";import{u as T,__tla as Z}from"./useForm-C3fyhjNV.js";import{u as A,__tla as D}from"./useValidator-C1ftTumK.js";import{u as J,L as K,_ as M,__tla as N}from"./LoginFormTitle.vue_vue_type_script_setup_true_lang-DUoWRVM3.js";let u,O=Promise.all([(()=>{try{return V}catch{}})(),(()=>{try{return F}catch{}})(),(()=>{try{return H}catch{}})(),(()=>{try{return Z}catch{}})(),(()=>{try{return D}catch{}})(),(()=>{try{return N}catch{}})()]).then(async()=>{let d,p,_;d={class:"w-[100%] flex"},p={class:"w-[100%]"},_={class:"mt-15px w-[100%]"},u=L({name:"RegisterForm",__name:"RegisterForm",setup(Q){const{t:e}=z(),{required:s}=A(),{register:h,elFormRef:g}=T(),{handleBackLogin:w,getLoginState:f}=J(),P=U(()=>a(f)===K.REGISTER),y=q([{field:"title",colProps:{span:24}},{field:"username",label:e("login.username"),value:"",component:"Input",colProps:{span:24},componentProps:{placeholder:e("login.usernamePlaceholder")}},{field:"password",label:e("login.password"),value:"",component:"InputPassword",colProps:{span:24},componentProps:{style:{width:"100%"},strength:!0,placeholder:e("login.passwordPlaceholder")}},{field:"check_password",label:e("login.checkPassword"),value:"",component:"InputPassword",colProps:{span:24},componentProps:{style:{width:"100%"},strength:!0,placeholder:e("login.passwordPlaceholder")}},{field:"code",label:e("login.code"),colProps:{span:24}},{field:"register",colProps:{span:24}}]),b={username:[s()],password:[s()],check_password:[s()],code:[s()]},n=B(!1);return(W,r)=>{const v=G,m=x,k=I;return C((S(),j(k,{rules:b,schema:a(y),class:"dark:(border-1 border-[var(--el-border-color)] border-solid)","hide-required-asterisk":"","label-position":"top",size:"large",onRegister:a(h)},{title:c(()=>[t(M,{style:{width:"100%"}})]),code:c(o=>[i("div",d,[t(v,{modelValue:o.code,"onUpdate:modelValue":l=>o.code=l,placeholder:a(e)("login.codePlaceholder")},null,8,["modelValue","onUpdate:modelValue","placeholder"])])]),register:c(()=>[i("div",p,[t(m,{loading:a(n),title:a(e)("login.register"),class:"w-[100%]",type:"primary",onClick:r[0]||(r[0]=o=>(async()=>{const l=a(g);l==null||l.validate(async R=>{if(R)try{n.value=!0}finally{n.value=!1}})})())},null,8,["loading","title"])]),i("div",_,[t(m,{title:a(e)("login.hasUser"),class:"w-[100%]",onClick:r[1]||(r[1]=o=>a(w)())},null,8,["title"])])]),_:1},8,["schema","onRegister"])),[[E,a(P)]])}}})});export{u as _,O as __tla};
