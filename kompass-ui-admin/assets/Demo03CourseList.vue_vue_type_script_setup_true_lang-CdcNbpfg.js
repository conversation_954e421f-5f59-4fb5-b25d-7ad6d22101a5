import{d as D,n as F,I as H,r as _,f as L,at as Q,T as q,o as i,c as A,i as a,w as n,H as c,l as d,j as h,a as r,F as B,_ as E,N as G,P as J,Q as K,R as M,__tla as O}from"./index-BUSn51wb.js";import{_ as V,__tla as W}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as X,__tla as Y}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{d as Z,__tla as $}from"./formatTime-DWdBpgsM.js";import{e as aa,f as ta,__tla as ea}from"./index-ydnYox5L.js";import{_ as ra,__tla as la}from"./Demo03CourseForm.vue_vue_type_script_setup_true_lang-DK5k4Q5o.js";let C,sa=Promise.all([(()=>{try{return O}catch{}})(),(()=>{try{return W}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return $}catch{}})(),(()=>{try{return ea}catch{}})(),(()=>{try{return la}catch{}})()]).then(async()=>{C=D({__name:"Demo03CourseList",props:{studentId:{}},setup(N){const{t:b}=F(),u=H(),m=N,f=_(!1),w=_([]),k=_(0),l=L({pageNo:1,pageSize:10,studentId:void 0});Q(()=>m.studentId,t=>{t&&(l.studentId=t,S())},{immediate:!0,deep:!0});const p=async()=>{f.value=!0;try{const t=await aa(l);w.value=t.list,k.value=t.total}finally{f.value=!1}},S=()=>{l.pageNo=1,p()},I=_(),v=(t,e)=>{m.studentId?I.value.open(t,e,m.studentId):u.error("\u8BF7\u9009\u62E9\u4E00\u4E2A\u5B66\u751F")};return(t,e)=>{const z=E,y=G,o=J,P=K,x=X,U=V,g=q("hasPermi"),R=M;return i(),A(B,null,[a(U,null,{default:n(()=>[c((i(),d(y,{plain:"",type:"primary",onClick:e[0]||(e[0]=s=>v("create"))},{default:n(()=>[a(z,{class:"mr-5px",icon:"ep:plus"}),h(" \u65B0\u589E ")]),_:1})),[[g,["infra:demo03-student:create"]]]),c((i(),d(P,{data:r(w),"show-overflow-tooltip":!0,stripe:!0},{default:n(()=>[a(o,{align:"center",label:"\u7F16\u53F7",prop:"id"}),a(o,{align:"center",label:"\u540D\u5B57",prop:"name"}),a(o,{align:"center",label:"\u5206\u6570",prop:"score"}),a(o,{formatter:r(Z),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"]),a(o,{align:"center",label:"\u64CD\u4F5C"},{default:n(s=>[c((i(),d(y,{link:"",type:"primary",onClick:T=>v("update",s.row.id)},{default:n(()=>[h(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[g,["infra:demo03-student:update"]]]),c((i(),d(y,{link:"",type:"danger",onClick:T=>(async j=>{try{await u.delConfirm(),await ta(j),u.success(b("common.delSuccess")),await p()}catch{}})(s.row.id)},{default:n(()=>[h(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[g,["infra:demo03-student:delete"]]])]),_:1})]),_:1},8,["data"])),[[R,r(f)]]),a(x,{limit:r(l).pageSize,"onUpdate:limit":e[1]||(e[1]=s=>r(l).pageSize=s),page:r(l).pageNo,"onUpdate:page":e[2]||(e[2]=s=>r(l).pageNo=s),total:r(k),onPagination:p},null,8,["limit","page","total"])]),_:1}),a(ra,{ref_key:"formRef",ref:I,onSuccess:p},null,512)],64)}}})});export{C as _,sa as __tla};
