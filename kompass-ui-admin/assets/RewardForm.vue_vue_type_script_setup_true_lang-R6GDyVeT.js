import{by as g,d as ne,C as pe,n as me,I as ce,r as y,f as P,au as ve,o as r,l as c,w as l,i as t,a,j as n,H as ye,c as S,F as E,k as I,V as B,G,t as T,a9 as C,g as K,y as fe,ay as _e,Z as Ve,L as he,M as be,am as ge,an as Te,N as we,E as ke,ai as xe,ca as Ue,s as Pe,J as Se,K as Ee,O as Ie,R as Ce,__tla as De}from"./index-BUSn51wb.js";import{_ as Ae,__tla as Oe}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{i as Re,__tla as Ne}from"./spu-CW3JGweV.js";import{i as Y,h as D}from"./constants-A8BI3pz7.js";let j,H,J,qe=Promise.all([(()=>{try{return De}catch{}})(),(()=>{try{return Oe}catch{}})(),(()=>{try{return Ne}catch{}})()]).then(async()=>{let A,O;J=async w=>await g.get({url:"/promotion/reward-activity/page",params:w}),H=async w=>await g.delete({url:"/promotion/reward-activity/delete?id="+w}),A={style:{float:"left"}},O={style:{float:"right","font-size":"13px",color:"#8492a6"}},j=ne({name:"ProductBrandForm",__name:"RewardForm",emits:["success"],setup(w,{expose:Z,emit:$}){pe(()=>{Re().then(i=>{N.value=i})});const{t:h}=me(),R=ce(),N=y([]),f=y(!1),q=y(""),_=y(!1),F=y(""),d=y({id:void 0,name:void 0,startAndEndTime:void 0,startTime:void 0,endTime:void 0,conditionType:Y.PRICE.type,remark:void 0,productScope:D.ALL.scope,productSpuIds:void 0,rules:[{limit:void 0,discountPrice:void 0,freeDelivery:void 0,point:void 0,couponIds:[],couponCounts:[]}]}),s=P([]),Q=P({name:[{required:!0,message:"\u6D3B\u52A8\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],startAndEndTime:[{required:!0,message:"\u6D3B\u52A8\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],conditionType:[{required:!0,message:"\u6761\u4EF6\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],productScope:[{required:!0,message:"\u5546\u54C1\u8303\u56F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],productSpuIds:[{required:!0,message:"\u5546\u54C1\u8303\u56F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),k=y([]);Z({open:async(i,o)=>{if(f.value=!0,q.value=h("action."+i),F.value=i,le(),o){_.value=!0;try{let p=await(async u=>await g.get({url:"/promotion/reward-activity/get?id="+u}))(o);p.startAndEndTime=[new Date(p.startTime),new Date(p.endTime)],s.splice(0,s.length),p.rules.forEach(u=>{let V=P([]);u.freeDelivery&&V.push("\u5305\u90AE"),u.point&&V.push("\u9001\u79EF\u5206"),u.discountPrice&&V.push("\u8BA2\u5355\u91D1\u989D\u4F18\u60E0"),s.push(V)}),d.value=p}finally{_.value=!1}}}});const W=$,X=async()=>{if(k&&await k.value.validate()){d.value.startTime=+new Date(d.value.startAndEndTime[0]),d.value.endTime=+new Date(d.value.startAndEndTime[1]),s.forEach((i,o)=>{d.value.rules[o].freeDelivery=!!i.includes("\u5305\u90AE"),i.includes("\u9001\u79EF\u5206")||(d.value.rules[o].point=void 0),i.includes("\u8BA2\u5355\u91D1\u989D\u4F18\u60E0")||(d.value.rules[o].discountPrice=void 0)}),_.value=!0;try{const i=d.value;F.value==="create"?(await(async o=>await g.post({url:"/promotion/reward-activity/create",data:o}))(i),R.success(h("common.createSuccess"))):(await(async o=>await g.put({url:"/promotion/reward-activity/update",data:o}))(i),R.success(h("common.updateSuccess"))),f.value=!1,W("success")}finally{_.value=!1}}},ee=()=>{d.value.rules.push({limit:void 0,discountPrice:void 0,freeDelivery:void 0,point:void 0,couponIds:[],couponCounts:[]}),s.push([])},le=()=>{d.value={id:void 0,name:void 0,startAndEndTime:void 0,startTime:void 0,endTime:void 0,conditionType:Y.PRICE.type,remark:void 0,productScope:D.ALL.scope,productSpuIds:void 0,rules:[{limit:void 0,discountPrice:void 0,freeDelivery:void 0,point:void 0,couponIds:[],couponCounts:[]}]},s.splice(0,s.length),s.push(P([])),_e(()=>{var i;(i=k.value)==null||i.resetFields()})};return(i,o)=>{const p=Ve,u=he,V=be,L=ge,M=Te,x=we,b=ke,U=xe,ae=Ue,te=ve("e-form"),de=Pe,oe=Se,ue=Ee,ie=Ie,re=Ae,se=Ce;return r(),c(re,{title:a(q),modelValue:a(f),"onUpdate:modelValue":o[7]||(o[7]=e=>fe(f)?f.value=e:null)},{footer:l(()=>[t(x,{onClick:X,type:"primary",disabled:a(_)},{default:l(()=>[n("\u786E \u5B9A")]),_:1},8,["disabled"]),t(x,{onClick:o[6]||(o[6]=e=>f.value=!1)},{default:l(()=>[n("\u53D6 \u6D88")]),_:1})]),default:l(()=>[ye((r(),c(ie,{ref_key:"formRef",ref:k,model:a(d),rules:a(Q),"label-width":"80px"},{default:l(()=>[t(u,{label:"\u6D3B\u52A8\u540D\u79F0",prop:"name"},{default:l(()=>[t(p,{modelValue:a(d).name,"onUpdate:modelValue":o[0]||(o[0]=e=>a(d).name=e),placeholder:"\u8BF7\u8F93\u5165\u6D3B\u52A8\u540D\u79F0"},null,8,["modelValue"])]),_:1}),t(u,{label:"\u6D3B\u52A8\u65F6\u95F4",prop:"startAndEndTime"},{default:l(()=>[t(V,{modelValue:a(d).startAndEndTime,"onUpdate:modelValue":o[1]||(o[1]=e=>a(d).startAndEndTime=e),type:"datetimerange","range-separator":"-","start-placeholder":a(h)("common.startTimeText"),"end-placeholder":a(h)("common.endTimeText")},null,8,["modelValue","start-placeholder","end-placeholder"])]),_:1}),t(u,{label:"\u6761\u4EF6\u7C7B\u578B",prop:"conditionType"},{default:l(()=>[t(M,{modelValue:a(d).conditionType,"onUpdate:modelValue":o[2]||(o[2]=e=>a(d).conditionType=e)},{default:l(()=>[(r(!0),S(E,null,I(a(B)(a(G).PROMOTION_CONDITION_TYPE),e=>(r(),c(L,{key:e.value,label:e.value},{default:l(()=>[n(T(e.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(u,{label:"\u4F18\u60E0\u8BBE\u7F6E"},{default:l(()=>[(r(!0),S(E,null,I(a(d).rules,(e,v)=>(r(),c(de,{key:v,type:"flex"},{default:l(()=>[t(b,{span:24,style:{"font-weight":"bold",display:"flex"}},{default:l(()=>[n(" \u6D3B\u52A8\u5C42\u7EA7"+T(v+1)+" ",1),v!=0?(r(),c(x,{key:0,link:"",type:"danger",style:{"margin-left":"auto"},onClick:m=>(z=>{d.value.rules.splice(z,1),s.splice(z,1)})(v)},{default:l(()=>[n(" \u5220\u9664 ")]),_:2},1032,["onClick"])):C("",!0)]),_:2},1024),t(te,{ref_for:!0,ref:"formRef"+v,model:e},{default:l(()=>[t(u,{label:"\u4F18\u60E0\u95E8\u69DB:",prop:"limit","label-width":"100px",style:{"padding-left":"50px"}},{default:l(()=>[n(" \u6EE1 "),t(p,{style:{width:"150px",padding:"0 10px"},modelValue:e.limit,"onUpdate:modelValue":m=>e.limit=m,type:"number",placeholder:""},null,8,["modelValue","onUpdate:modelValue"]),n(" \u5143 ")]),_:2},1024),t(u,{label:"\u4F18\u60E0\u5185\u5BB9:","label-width":"100px",style:{"padding-left":"50px"}},{default:l(()=>[t(ae,{modelValue:a(s)[v],"onUpdate:modelValue":m=>a(s)[v]=m,style:{width:"100%"}},{default:l(()=>[t(b,{span:24},{default:l(()=>[t(U,{label:"\u8BA2\u5355\u91D1\u989D\u4F18\u60E0",name:"type"}),a(s)[v].includes("\u8BA2\u5355\u91D1\u989D\u4F18\u60E0")?(r(),c(u,{key:0},{default:l(()=>[n(" \u51CF "),t(p,{style:{width:"150px",padding:"0 20px"},modelValue:e.discountPrice,"onUpdate:modelValue":m=>e.discountPrice=m,type:"number",placeholder:""},null,8,["modelValue","onUpdate:modelValue"]),n(" \u5143 ")]),_:2},1024)):C("",!0)]),_:2},1024),t(b,{span:24},{default:l(()=>[t(U,{modelValue:e.freeDelivery,"onUpdate:modelValue":m=>e.freeDelivery=m,label:"\u5305\u90AE",name:"type"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024),t(b,{span:24},{default:l(()=>[t(U,{label:"\u9001\u79EF\u5206",name:"type"}),a(s)[v].includes("\u9001\u79EF\u5206")?(r(),c(u,{key:0},{default:l(()=>[n(" \u9001 "),t(p,{style:{width:"150px",padding:"0 20px"},modelValue:e.point,"onUpdate:modelValue":m=>e.point=m,type:"number",placeholder:""},null,8,["modelValue","onUpdate:modelValue"]),n(" \u79EF\u5206 ")]),_:2},1024)):C("",!0)]),_:2},1024),t(b,{span:24},{default:l(()=>[t(U,{label:"\u9001\u4F18\u60E0\u5238",name:"type"})]),_:1})]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1032,["model"])]),_:2},1024))),128)),t(x,{type:"primary",onClick:ee},{default:l(()=>[n("\u6DFB\u52A0\u6D3B\u52A8\u5C42\u7EA7")]),_:1})]),_:1}),t(u,{label:"\u6D3B\u52A8\u5546\u54C1",prop:"productScope"},{default:l(()=>[t(M,{modelValue:a(d).productScope,"onUpdate:modelValue":o[3]||(o[3]=e=>a(d).productScope=e)},{default:l(()=>[(r(!0),S(E,null,I(a(B)(a(G).PROMOTION_PRODUCT_SCOPE),e=>(r(),c(L,{key:e.value,label:e.value},{default:l(()=>[n(T(e.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(d).productScope===a(D).SPU.scope?(r(),c(u,{key:0,prop:"productSpuIds"},{default:l(()=>[t(ue,{modelValue:a(d).productSpuIds,"onUpdate:modelValue":o[4]||(o[4]=e=>a(d).productSpuIds=e),placeholder:"\u8BF7\u9009\u62E9\u6D3B\u52A8\u5546\u54C1",clearable:"",size:"small",multiple:"",filterable:"",style:{width:"400px"}},{default:l(()=>[(r(!0),S(E,null,I(a(N),e=>(r(),c(oe,{key:e.id,label:e.name,value:e.id},{default:l(()=>[K("span",A,T(e.name),1),K("span",O," \uFFE5"+T((e.price/100).toFixed(2)),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):C("",!0),t(u,{label:"\u5907\u6CE8",prop:"remark"},{default:l(()=>[t(p,{modelValue:a(d).remark,"onUpdate:modelValue":o[5]||(o[5]=e=>a(d).remark=e),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[se,a(_)]])]),_:1},8,["title","modelValue"])}}})});export{j as _,qe as __tla,H as d,J as g};
