import{_ as P,__tla as A}from"./ComponentContainerProperty-U-5gd_f0.js";import{d as D,r as L,C as N,o as u,l as p,w as r,i as e,a,c as j,F,k as J,g as w,j as K,a9 as M,J as O,K as Q,L as W,_ as X,aM as q,aN as G,an as H,ai as S,ce as Y,cl as Z,cf as $,O as ee,__tla as le}from"./index-BUSn51wb.js";import{_ as ae,__tla as te}from"./index-11u3nuTi.js";import{E as oe,__tla as re}from"./el-card-CJbXGyyg.js";import{u as de,__tla as se}from"./util-Dyp86Gv2.js";import{a as ue,__tla as me}from"./combinationActivity-Jgh6nzIi.js";import{C as pe}from"./constants-A8BI3pz7.js";import"./color-BN7ZL7BD.js";import{__tla as _e}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as ne}from"./Qrcode-CP7wmJi0.js";import{__tla as ie}from"./el-text-CIwNlU-U.js";import{__tla as ce}from"./IFrame.vue_vue_type_script_setup_true_lang-silYuU_p.js";import{__tla as fe}from"./vuedraggable.umd-BTL7hPHv.js";import{__tla as he}from"./el-collapse-item-B_QvnH_b.js";let g,Ve=Promise.all([(()=>{try{return A}catch{}})(),(()=>{try{return le}catch{}})(),(()=>{try{return te}catch{}})(),(()=>{try{return re}catch{}})(),(()=>{try{return se}catch{}})(),(()=>{try{return me}catch{}})(),(()=>{try{return _e}catch{}})(),(()=>{try{return ne}catch{}})(),(()=>{try{return ie}catch{}})(),(()=>{try{return ce}catch{}})(),(()=>{try{return fe}catch{}})(),(()=>{try{return he}catch{}})()]).then(async()=>{let _,n;_={class:"flex gap-8px"},n={class:"flex gap-8px"},g=D({name:"PromotionCombinationProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(U,{emit:x}){const v=U,R=x,{formData:t}=de(v.modelValue,R),i=L([]);return N(async()=>{const{list:c}=await ue({status:pe.ENABLE});i.value=c}),(c,o)=>{const T=O,C=Q,d=W,s=oe,f=X,h=q,V=G,z=H,y=ae,b=S,B=Y,E=Z,m=$,I=ee,k=P;return u(),p(k,{modelValue:a(t).style,"onUpdate:modelValue":o[11]||(o[11]=l=>a(t).style=l)},{default:r(()=>[e(I,{"label-width":"80px",model:a(t)},{default:r(()=>[e(s,{header:"\u62FC\u56E2\u6D3B\u52A8",class:"property-group",shadow:"never"},{default:r(()=>[e(d,{label:"\u62FC\u56E2\u6D3B\u52A8",prop:"activityId"},{default:r(()=>[e(C,{modelValue:a(t).activityId,"onUpdate:modelValue":o[0]||(o[0]=l=>a(t).activityId=l)},{default:r(()=>[(u(!0),j(F,null,J(a(i),l=>(u(),p(T,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{header:"\u5546\u54C1\u6837\u5F0F",class:"property-group",shadow:"never"},{default:r(()=>[e(d,{label:"\u5E03\u5C40",prop:"type"},{default:r(()=>[e(z,{modelValue:a(t).layoutType,"onUpdate:modelValue":o[1]||(o[1]=l=>a(t).layoutType=l)},{default:r(()=>[e(V,{class:"item",content:"\u5355\u5217",placement:"bottom"},{default:r(()=>[e(h,{label:"oneCol"},{default:r(()=>[e(f,{icon:"fluent:text-column-one-24-filled"})]),_:1})]),_:1}),e(V,{class:"item",content:"\u4E09\u5217",placement:"bottom"},{default:r(()=>[e(h,{label:"threeCol"},{default:r(()=>[e(f,{icon:"fluent:text-column-three-24-filled"})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"\u5546\u54C1\u540D\u79F0",prop:"fields.name.show"},{default:r(()=>[w("div",_,[e(y,{modelValue:a(t).fields.name.color,"onUpdate:modelValue":o[2]||(o[2]=l=>a(t).fields.name.color=l)},null,8,["modelValue"]),e(b,{modelValue:a(t).fields.name.show,"onUpdate:modelValue":o[3]||(o[3]=l=>a(t).fields.name.show=l)},null,8,["modelValue"])])]),_:1}),e(d,{label:"\u5546\u54C1\u4EF7\u683C",prop:"fields.price.show"},{default:r(()=>[w("div",n,[e(y,{modelValue:a(t).fields.price.color,"onUpdate:modelValue":o[4]||(o[4]=l=>a(t).fields.price.color=l)},null,8,["modelValue"]),e(b,{modelValue:a(t).fields.price.show,"onUpdate:modelValue":o[5]||(o[5]=l=>a(t).fields.price.show=l)},null,8,["modelValue"])])]),_:1})]),_:1}),e(s,{header:"\u89D2\u6807",class:"property-group",shadow:"never"},{default:r(()=>[e(d,{label:"\u89D2\u6807",prop:"badge.show"},{default:r(()=>[e(B,{modelValue:a(t).badge.show,"onUpdate:modelValue":o[6]||(o[6]=l=>a(t).badge.show=l)},null,8,["modelValue"])]),_:1}),a(t).badge.show?(u(),p(d,{key:0,label:"\u89D2\u6807",prop:"badge.imgUrl"},{default:r(()=>[e(E,{modelValue:a(t).badge.imgUrl,"onUpdate:modelValue":o[7]||(o[7]=l=>a(t).badge.imgUrl=l),height:"44px",width:"72px"},{tip:r(()=>[K(" \u5EFA\u8BAE\u5C3A\u5BF8\uFF1A36 * 22 ")]),_:1},8,["modelValue"])]),_:1})):M("",!0)]),_:1}),e(s,{header:"\u5546\u54C1\u6837\u5F0F",class:"property-group",shadow:"never"},{default:r(()=>[e(d,{label:"\u4E0A\u5706\u89D2",prop:"borderRadiusTop"},{default:r(()=>[e(m,{modelValue:a(t).borderRadiusTop,"onUpdate:modelValue":o[8]||(o[8]=l=>a(t).borderRadiusTop=l),max:100,min:0,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"])]),_:1}),e(d,{label:"\u4E0B\u5706\u89D2",prop:"borderRadiusBottom"},{default:r(()=>[e(m,{modelValue:a(t).borderRadiusBottom,"onUpdate:modelValue":o[9]||(o[9]=l=>a(t).borderRadiusBottom=l),max:100,min:0,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"])]),_:1}),e(d,{label:"\u95F4\u9694",prop:"space"},{default:r(()=>[e(m,{modelValue:a(t).space,"onUpdate:modelValue":o[10]||(o[10]=l=>a(t).space=l),max:100,min:0,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}})});export{Ve as __tla,g as default};
