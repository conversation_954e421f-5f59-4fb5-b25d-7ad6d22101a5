import{d as K,f as M,e as N,r as _,b as O,at as S,C as B,o as h,c as D,i as e,w as l,a as r,F as x,k as G,l as T,j as g,y as W,M as X,L as H,J as Q,K as Z,_ as $,N as ee,O as ae,z as te,A as re,E as le,__tla as se}from"./index-BUSn51wb.js";import{_ as oe,__tla as _e}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{E as ue,__tla as ne}from"./el-tree-select-CBuha0HW.js";import{g as ce,__tla as me}from"./index-Bqt292RI.js";import{g as de,__tla as fe}from"./index-BYXzDB8j.js";import{f as p,e as q,g as R,__tla as pe}from"./formatTime-DWdBpgsM.js";import{h as Y,d as ie}from"./tree-BMa075Oj.js";import{_ as ye,__tla as he}from"./ContractCountPerformance.vue_vue_type_script_setup_true_lang-C6lUnYrj.js";import{_ as be,__tla as ve}from"./ContractPricePerformance.vue_vue_type_script_setup_true_lang-B33-x0S-.js";import{_ as Ce,__tla as Pe}from"./ReceivablePricePerformance.vue_vue_type_script_setup_true_lang-DByFV99v.js";import{__tla as we}from"./el-card-CJbXGyyg.js";import{__tla as Ie}from"./el-skeleton-item-tDN8U6BH.js";import{__tla as ke}from"./Echart.vue_vue_type_script_setup_true_lang-DnLZLH3w.js";import{__tla as Ve}from"./performance-BsLSERfz.js";let F,De=Promise.all([(()=>{try{return se}catch{}})(),(()=>{try{return _e}catch{}})(),(()=>{try{return ne}catch{}})(),(()=>{try{return me}catch{}})(),(()=>{try{return fe}catch{}})(),(()=>{try{return pe}catch{}})(),(()=>{try{return he}catch{}})(),(()=>{try{return ve}catch{}})(),(()=>{try{return Pe}catch{}})(),(()=>{try{return we}catch{}})(),(()=>{try{return Ie}catch{}})(),(()=>{try{return ke}catch{}})(),(()=>{try{return Ve}catch{}})()]).then(async()=>{F=K({name:"CrmStatisticsCustomer",__name:"index",setup(xe){const a=M({deptId:N().getUser.deptId,userId:void 0,times:[p(q(new Date(new Date().getFullYear(),0,1))),p(R(new Date(new Date().getFullYear(),11,31)))]}),b=_(),v=_([]),C=_([]),U=O(()=>a.deptId?C.value.filter(c=>c.deptId===a.deptId):[]),n=_("ContractCountPerformance"),P=_(),w=_(),I=_(),i=()=>{var t,m,o,d,u,f;const c=parseInt(a.times[0]);switch(a.times[0]=p(q(new Date(c,0,1))),a.times[1]=p(R(new Date(c,11,31))),n.value){case"ContractCountPerformance":(m=(t=P.value)==null?void 0:t.loadData)==null||m.call(t);break;case"ContractPricePerformance":(d=(o=w.value)==null?void 0:o.loadData)==null||d.call(o);break;case"ReceivablePricePerformance":(f=(u=I.value)==null?void 0:u.loadData)==null||f.call(u)}};S(n,()=>{i()});const z=()=>{b.value.resetFields(),i()};return B(async()=>{v.value=Y(await ce()),C.value=Y(await de())}),(c,t)=>{const m=X,o=H,d=ue,u=Q,f=Z,k=$,V=ee,A=ae,E=oe,y=te,L=re,j=le;return h(),D(x,null,[e(E,null,{default:l(()=>[e(A,{class:"-mb-15px",model:r(a),ref_key:"queryFormRef",ref:b,inline:!0,"label-width":"68px"},{default:l(()=>[e(o,{label:"\u9009\u62E9\u5E74\u4EFD",prop:"orderDate"},{default:l(()=>[e(m,{modelValue:r(a).times[0],"onUpdate:modelValue":t[0]||(t[0]=s=>r(a).times[0]=s),class:"!w-240px",type:"year","value-format":"YYYY","default-time":[new Date().getFullYear()]},null,8,["modelValue","default-time"])]),_:1}),e(o,{label:"\u5F52\u5C5E\u90E8\u95E8",prop:"deptId"},{default:l(()=>[e(d,{modelValue:r(a).deptId,"onUpdate:modelValue":t[1]||(t[1]=s=>r(a).deptId=s),class:"!w-240px",data:r(v),props:r(ie),"check-strictly":"","node-key":"id",placeholder:"\u8BF7\u9009\u62E9\u5F52\u5C5E\u90E8\u95E8",onChange:t[2]||(t[2]=s=>r(a).userId=void 0)},null,8,["modelValue","data","props"])]),_:1}),e(o,{label:"\u5458\u5DE5",prop:"userId"},{default:l(()=>[e(f,{modelValue:r(a).userId,"onUpdate:modelValue":t[3]||(t[3]=s=>r(a).userId=s),class:"!w-240px",placeholder:"\u5458\u5DE5",clearable:""},{default:l(()=>[(h(!0),D(x,null,G(r(U),(s,J)=>(h(),T(u,{label:s.nickname,value:s.id,key:J},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(o,null,{default:l(()=>[e(V,{onClick:i},{default:l(()=>[e(k,{icon:"ep:search",class:"mr-5px"}),g(" \u641C\u7D22 ")]),_:1}),e(V,{onClick:z},{default:l(()=>[e(k,{icon:"ep:refresh",class:"mr-5px"}),g(" \u91CD\u7F6E ")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(j,null,{default:l(()=>[e(L,{modelValue:r(n),"onUpdate:modelValue":t[4]||(t[4]=s=>W(n)?n.value=s:null)},{default:l(()=>[e(y,{label:"\u5458\u5DE5\u5408\u540C\u6570\u91CF\u7EDF\u8BA1",name:"ContractCountPerformance",lazy:""},{default:l(()=>[e(ye,{"query-params":r(a),ref_key:"ContractCountPerformanceRef",ref:P},null,8,["query-params"])]),_:1}),e(y,{label:"\u5458\u5DE5\u5408\u540C\u91D1\u989D\u7EDF\u8BA1",name:"ContractPricePerformance",lazy:""},{default:l(()=>[e(be,{"query-params":r(a),ref_key:"ContractPricePerformanceRef",ref:w},null,8,["query-params"])]),_:1}),e(y,{label:"\u5458\u5DE5\u56DE\u6B3E\u91D1\u989D\u7EDF\u8BA1",name:"ReceivablePricePerformance",lazy:""},{default:l(()=>[e(Ce,{"query-params":r(a),ref_key:"ReceivablePricePerformanceRef",ref:I},null,8,["query-params"])]),_:1})]),_:1},8,["modelValue"])]),_:1})],64)}}})});export{De as __tla,F as default};
