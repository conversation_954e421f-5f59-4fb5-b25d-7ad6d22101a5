import{by as s,__tla as m}from"./index-BUSn51wb.js";let n,r,c,p,i,l,o,y=Promise.all([(()=>{try{return m}catch{}})()]).then(async()=>{r=async a=>await s.get({url:"/bpm/process-instance/my-page",params:a}),p=async a=>await s.get({url:"/bpm/process-instance/manager-page",params:a}),n=async a=>await s.post({url:"/bpm/process-instance/create",data:a}),c=async(a,e)=>{const t={id:a,reason:e};return await s.delete({url:"/bpm/process-instance/cancel-by-start-user",data:t})},i=async(a,e)=>{const t={id:a,reason:e};return await s.delete({url:"/bpm/process-instance/cancel-by-admin",data:t})},o=async a=>await s.get({url:"/bpm/process-instance/get?id="+a}),l=async a=>await s.get({url:"/bpm/process-instance/copy/page",params:a})});export{y as __tla,n as a,r as b,c,p as d,i as e,l as f,o as g};
