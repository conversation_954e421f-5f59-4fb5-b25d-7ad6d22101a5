import{_ as w,__tla as b}from"./Left.vue_vue_type_script_setup_true_lang-DUWQR3Qm.js";import x,{__tla as S}from"./Right-B0rMdy4G.js";import{W as C,__tla as E}from"./index-CG1HTB0Z.js";import{W as R}from"./constants-C0I8ujwj.js";import{d as W,I as j,r as o,o as k,c as A,i as m,a as _,y as B,ay as I,__tla as J}from"./index-BUSn51wb.js";import{__tla as M}from"./Tag.vue_vue_type_script_setup_true_lang-Bxsa69ZH.js";import{__tla as N}from"./el-card-CJbXGyyg.js";import"./fetch-D5K_4anA.js";let f,O=Promise.all([(()=>{try{return b}catch{}})(),(()=>{try{return S}catch{}})(),(()=>{try{return E}catch{}})(),(()=>{try{return J}catch{}})(),(()=>{try{return M}catch{}})(),(()=>{try{return N}catch{}})()]).then(async()=>{let i;i={class:"absolute top-0 left-0 right-0 bottom-0 flex"},f=W({__name:"index",setup(P){const p=j(),t=o(""),e=o(!1),s=o(),l=()=>{var a;(a=s.value)==null||a.abort(),e.value=!1},u=o(),v=a=>{s.value=new AbortController,t.value="",e.value=!0,C.writeStream({data:a,onMessage:async r=>{var c;const{code:n,data:g,msg:h}=JSON.parse(r.data);if(n!==0)return p.alert(`\u5199\u4F5C\u5F02\u5E38! ${h}`),void l();t.value=t.value+g,await I(),(c=u.value)==null||c.scrollToBottom()},ctrl:s.value,onClose:l,onError:(...r)=>{console.error("\u5199\u4F5C\u5F02\u5E38",...r),l()}})},d=a=>{t.value=R[a].data},y=()=>{t.value=""};return(a,r)=>(k(),A("div",i,[m(w,{"is-writing":_(e),class:"h-full",onSubmit:v,onReset:y,onExample:d},null,8,["is-writing"]),m(x,{"is-writing":_(e),onStopStream:l,ref_key:"rightRef",ref:u,class:"flex-grow",content:_(t),"onUpdate:content":r[0]||(r[0]=n=>B(t)?t.value=n:null)},null,8,["is-writing","content"])]))}})});export{O as __tla,f as default};
