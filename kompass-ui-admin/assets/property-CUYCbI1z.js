import{_ as _t,__tla as mt}from"./ComponentContainerProperty-U-5gd_f0.js";import{fd as ft,fe as dt,ei as ht,d as K,b5 as yt,ff as vt,r as T,o as H,c as b,i as o,w as y,j as D,g as Q,F as I,k as S,a as m,av as tt,t as gt,y as wt,_ as xt,N as et,B as at,cl as kt,L as Vt,O as Tt,__tla as Ht}from"./index-BUSn51wb.js";import{E as bt,__tla as Et}from"./el-text-CIwNlU-U.js";import{u as zt,__tla as Ut}from"./util-Dyp86Gv2.js";import{_ as Dt,__tla as It}from"./AppLinkSelectDialog.vue_vue_type_script_setup_true_lang-CHi5jpXX.js";import{_ as Ct,__tla as Lt}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{E as Pt,__tla as Xt}from"./el-image-BjHZRFih.js";import{__tla as Ft}from"./el-card-CJbXGyyg.js";import{__tla as Mt}from"./index-11u3nuTi.js";import"./color-BN7ZL7BD.js";import{__tla as Ot}from"./Qrcode-CP7wmJi0.js";import{__tla as Wt}from"./IFrame.vue_vue_type_script_setup_true_lang-silYuU_p.js";import{__tla as $t}from"./vuedraggable.umd-BTL7hPHv.js";import{__tla as Gt}from"./el-collapse-item-B_QvnH_b.js";import{__tla as jt}from"./ProductCategorySelect.vue_vue_type_script_setup_true_lang-CJItHfu_.js";import{__tla as At}from"./el-tree-select-CBuha0HW.js";import"./tree-BMa075Oj.js";import{__tla as Bt}from"./category-WzWM3ODe.js";let lt,Rt=Promise.all([(()=>{try{return mt}catch{}})(),(()=>{try{return Ht}catch{}})(),(()=>{try{return Et}catch{}})(),(()=>{try{return Ut}catch{}})(),(()=>{try{return It}catch{}})(),(()=>{try{return Lt}catch{}})(),(()=>{try{return Xt}catch{}})(),(()=>{try{return Ft}catch{}})(),(()=>{try{return Mt}catch{}})(),(()=>{try{return Ot}catch{}})(),(()=>{try{return Wt}catch{}})(),(()=>{try{return $t}catch{}})(),(()=>{try{return Gt}catch{}})(),(()=>{try{return jt}catch{}})(),(()=>{try{return At}catch{}})(),(()=>{try{return Bt}catch{}})()]).then(async()=>{var ot=Array.prototype.splice;function rt(e,f){var v=[];if(!e||!e.length)return v;var d=-1,l=[],s=e.length;for(f=ht(f);++d<s;){var n=e[d];f(n,d,e)&&(v.push(n),l.push(d))}return function(i,r){for(var p=i?r.length:0,g=p-1;p--;){var c=r[p];if(p==g||c!==E){var E=c;ft(c)?ot.call(i,c,1):dt(i,c)}}}(e,l),v}const U=100;var w=(e=>(e[e.LEFT=0]="LEFT",e[e.TOP=1]="TOP",e[e.WIDTH=2]="WIDTH",e[e.HEIGHT=3]="HEIGHT",e))(w||{});let $,C,G,j,A,B;$=[{position:"\u5DE6\u4E0A\u89D2",types:[0,1,2,3],style:{left:"-5px",top:"-5px",cursor:"nwse-resize"}},{position:"\u4E0A\u65B9\u4E2D\u95F4",types:[1,3],style:{left:"50%",top:"-5px",cursor:"n-resize",transform:"translateX(-50%)"}},{position:"\u53F3\u4E0A\u89D2",types:[1,2,3],style:{right:"-5px",top:"-5px",cursor:"nesw-resize"}},{position:"\u53F3\u4FA7\u4E2D\u95F4",types:[2],style:{right:"-5px",top:"50%",cursor:"e-resize",transform:"translateX(-50%)"}},{position:"\u53F3\u4E0B\u89D2",types:[2,3],style:{right:"-5px",bottom:"-5px",cursor:"nwse-resize"}},{position:"\u4E0B\u65B9\u4E2D\u95F4",types:[3],style:{left:"50%",bottom:"-5px",cursor:"s-resize",transform:"translateX(-50%)"}},{position:"\u5DE6\u4E0B\u89D2",types:[0,2,3],style:{left:"-5px",bottom:"-5px",cursor:"nesw-resize"}},{position:"\u5DE6\u4FA7\u4E2D\u95F4",types:[0,2],style:{left:"-5px",top:"50%",cursor:"w-resize",transform:"translateX(-50%)"}}],C=(e,f,v)=>{f.stopPropagation();const{clientX:d,clientY:l}=f,{left:s,top:n,width:i,height:r}=e;document.onmousemove=p=>{const g=p.clientX-d,c=p.clientY-l;v(s,n,i,r,g,c)},document.onmouseup=()=>{document.onmousemove=null,document.onmouseup=null}},G=["onMousedown","onDblclick"],j={class:"pointer-events-none select-none"},A=["onMousedown"],B=at(K({name:"HotZoneEditDialog",__name:"index",props:{modelValue:yt(),imgUrl:vt().def("")},emits:["update:modelValue"],setup(e,{expose:f,emit:v}){const d=e,l=v,s=T([]),n=T(!1);f({open:()=>{var a;s.value=(a=d.modelValue,(a==null?void 0:a.map(t=>({...t,left:t.left*=2,top:t.top*=2,width:t.width*=2,height:t.height*=2})))||[]),n.value=!0}});const i=T(),r=()=>{s.value.push({width:U,height:U,top:0,left:0})},p=(a,t)=>{t>=0&&t<=i.value.offsetWidth-a.width&&(a.left=t)},g=(a,t)=>{t>=0&&t<=i.value.offsetHeight-a.height&&(a.top=t)},c=(a,t)=>{t>=U&&a.left+t<=i.value.offsetWidth&&(a.width=t)},E=(a,t)=>{t>=U&&a.top+t<=i.value.offsetHeight&&(a.height=t)},L=()=>{n.value=!1},P=()=>{const a=(t=>(t==null?void 0:t.map(x=>({...x,left:x.left/=2,top:x.top/=2,width:x.width/=2,height:x.height/=2})))||[])(s.value);l("update:modelValue",a)},h=T(),R=T(),st=a=>{a&&h.value&&(h.value.name=a.name,h.value.url=a.path)};return(a,t)=>{const x=Pt,X=xt,Y=et,nt=Ct,it=Dt;return H(),b(I,null,[o(nt,{modelValue:m(n),"onUpdate:modelValue":t[0]||(t[0]=_=>wt(n)?n.value=_:null),title:"\u8BBE\u7F6E\u70ED\u533A",width:"780",onClose:P},{footer:y(()=>[o(Y,{onClick:r,type:"primary",plain:""},{default:y(()=>[o(X,{icon:"ep:plus",class:"mr-5px"}),D(" \u6DFB\u52A0\u70ED\u533A ")]),_:1}),o(Y,{onClick:L,type:"primary",plain:""},{default:y(()=>[o(X,{icon:"ep:check",class:"mr-5px"}),D(" \u786E\u5B9A ")]),_:1})]),default:y(()=>[Q("div",{ref_key:"container",ref:i,class:"relative h-full w-750px"},[o(x,{src:e.imgUrl,class:"pointer-events-none h-full w-750px select-none"},null,8,["src"]),(H(!0),b(I,null,S(m(s),(_,pt)=>(H(),b("div",{key:pt,class:"hot-zone",style:tt({width:`${_.width}px`,height:`${_.height}px`,top:`${_.top}px`,left:`${_.left}px`}),onMousedown:V=>((u,F)=>{C(u,F,(k,z,Z,N,M,O)=>{p(u,k+M),g(u,z+O)})})(_,V),onDblclick:V=>{return u=_,h.value=u,void R.value.open(u.url);var u}},[Q("span",j,gt(_.name||"\u53CC\u51FB\u9009\u62E9\u94FE\u63A5"),1),o(X,{icon:"ep:close",class:"delete",size:14,onClick:V=>{return u=_,void rt(s.value,u);var u}},null,8,["onClick"]),(H(!0),b(I,null,S(m($),(V,u)=>(H(),b("span",{class:"ctrl-dot",key:u,style:tt(V.style),onMousedown:F=>((k,z,Z)=>{C(k,Z,(N,M,O,ut,q,J)=>{z.types.forEach(ct=>{switch(ct){case w.LEFT:p(k,N+q);break;case w.TOP:g(k,M+J);break;case w.WIDTH:{const W=z.types.includes(w.LEFT)?-1:1;c(k,O+q*W)}break;case w.HEIGHT:{const W=z.types.includes(w.TOP)?-1:1;E(k,ut+J*W)}}})})})(_,V,F)},null,44,A))),128))],44,G))),128))],512)]),_:1},8,["modelValue"]),o(it,{ref_key:"appLinkDialogRef",ref:R,onAppLinkChange:st},null,512)],64)}}}),[["__scopeId","data-v-85e5873f"]]),lt=at(K({name:"HotZoneProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(e,{emit:f}){const v=e,d=f,{formData:l}=zt(v.modelValue,d),s=T(),n=()=>{s.value.open()};return(i,r)=>{const p=bt,g=kt,c=Vt,E=Tt,L=et,P=_t;return H(),b(I,null,[o(P,{modelValue:m(l).style,"onUpdate:modelValue":r[1]||(r[1]=h=>m(l).style=h)},{default:y(()=>[o(E,{"label-width":"80px",model:m(l),class:"m-t-8px"},{default:y(()=>[o(c,{label:"\u4E0A\u4F20\u56FE\u7247",prop:"imgUrl"},{default:y(()=>[o(g,{modelValue:m(l).imgUrl,"onUpdate:modelValue":r[0]||(r[0]=h=>m(l).imgUrl=h),height:"50px",width:"auto",class:"min-w-80px"},{tip:y(()=>[o(p,{type:"info",size:"small"},{default:y(()=>[D(" \u63A8\u8350\u5BBD\u5EA6 750")]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"]),o(L,{type:"primary",plain:"",class:"w-full",onClick:n},{default:y(()=>[D(" \u8BBE\u7F6E\u70ED\u533A ")]),_:1})]),_:1},8,["modelValue"]),o(B,{ref_key:"editDialogRef",ref:s,modelValue:m(l).list,"onUpdate:modelValue":r[2]||(r[2]=h=>m(l).list=h),"img-url":m(l).imgUrl},null,8,["modelValue","img-url"])],64)}}}),[["__scopeId","data-v-053ffb33"]])});export{Rt as __tla,lt as default};
