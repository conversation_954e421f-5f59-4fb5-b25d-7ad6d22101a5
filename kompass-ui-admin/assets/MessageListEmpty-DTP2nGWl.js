import{d as u,o as p,c as i,g as t,F as y,k,t as h,a5 as P,a6 as b,B as f,__tla as g}from"./index-BUSn51wb.js";let m,C=Promise.all([(()=>{try{return g}catch{}})()]).then(async()=>{let e,l,o,r,c;e={class:"chat-empty"},l={class:"center-container"},o=(a=>(P("data-v-18ca52b1"),a=a(),b(),a))(()=>t("div",{class:"title"},"\u828B\u9053 AI",-1)),r={class:"role-list"},c=["onClick"],m=f(u({__name:"MessageListEmpty",emits:["onPrompt"],setup(a,{emit:n}){const d=[{prompt:"\u4ECA\u5929\u6C14\u600E\u4E48\u6837?"},{prompt:"\u5199\u4E00\u9996\u597D\u542C\u7684\u8BD7\u6B4C?"}],_=n;return(I,x)=>(p(),i("div",e,[t("div",l,[o,t("div",r,[(p(),i(y,null,k(d,s=>t("div",{class:"role-item",key:s.prompt,onClick:A=>(async({prompt:v})=>{_("onPrompt",v)})(s)},h(s.prompt),9,c)),64))])])]))}}),[["__scopeId","data-v-18ca52b1"]])});export{C as __tla,m as default};
