import{d as w,r as _,f as b,C as v,o as m,c as P,i as a,w as n,a as t,H as k,l as A,dV as C,F as R,em as q,P as E,Q as I,R as j,__tla as D}from"./index-BUSn51wb.js";import{E as F,__tla as H}from"./el-card-CJbXGyyg.js";import{E as L,__tla as N}from"./el-skeleton-item-tDN8U6BH.js";import{_ as Q,__tla as S}from"./Echart.vue_vue_type_script_setup_true_lang-DnLZLH3w.js";import{S as V,__tla as X}from"./rank-CaJ4xEN0.js";let d,Z=Promise.all([(()=>{try{return D}catch{}})(),(()=>{try{return H}catch{}})(),(()=>{try{return N}catch{}})(),(()=>{try{return S}catch{}})(),(()=>{try{return X}catch{}})()]).then(async()=>{d=w({name:"ContractPriceRank",__name:"ContractPriceRank",props:{queryParams:{}},setup(u,{expose:p}){const h=u,e=_(!1),l=_([]),r=b({dataset:{dimensions:["nickname","count"],source:[]},grid:{left:20,right:20,bottom:20,containLabel:!0},legend:{top:50},series:[{name:"\u5408\u540C\u91D1\u989D\u6392\u884C",type:"bar"}],toolbox:{feature:{dataZoom:{yAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u5408\u540C\u91D1\u989D\u6392\u884C"}}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},xAxis:{type:"value",name:"\u5408\u540C\u91D1\u989D\uFF08\u5143\uFF09"},yAxis:{type:"category",name:"\u7B7E\u8BA2\u4EBA"}}),i=async()=>{e.value=!0;const o=await V.getContractPriceRank(h.queryParams);r.dataset&&r.dataset.source&&(r.dataset.source=q(o).reverse()),l.value=o,e.value=!1};return p({loadData:i}),v(()=>{i()}),(o,z)=>{const y=Q,g=L,c=F,s=E,f=I,x=j;return m(),P(R,null,[a(c,{shadow:"never"},{default:n(()=>[a(g,{loading:t(e),animated:""},{default:n(()=>[a(y,{height:500,options:t(r)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),a(c,{shadow:"never",class:"mt-16px"},{default:n(()=>[k((m(),A(f,{data:t(l)},{default:n(()=>[a(s,{label:"\u516C\u53F8\u6392\u540D",align:"center",type:"index",width:"80"}),a(s,{label:"\u7B7E\u8BA2\u4EBA",align:"center",prop:"nickname","min-width":"200"}),a(s,{label:"\u90E8\u95E8",align:"center",prop:"deptName","min-width":"200"}),a(s,{label:"\u5408\u540C\u91D1\u989D\uFF08\u5143\uFF09",align:"center",prop:"count","min-width":"200",formatter:t(C)},null,8,["formatter"])]),_:1},8,["data"])),[[x,t(e)]])]),_:1})],64)}}})});export{d as _,Z as __tla};
