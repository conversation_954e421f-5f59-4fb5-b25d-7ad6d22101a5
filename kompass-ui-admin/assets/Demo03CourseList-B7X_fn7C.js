import{_ as t,__tla as _}from"./Demo03CourseList.vue_vue_type_script_setup_true_lang-Ce7GTT7a.js";import{__tla as r}from"./index-BUSn51wb.js";import{__tla as a}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{__tla as l}from"./el-card-CJbXGyyg.js";import{__tla as o}from"./formatTime-DWdBpgsM.js";import{__tla as c}from"./index-DrnBZ6x8.js";let m=Promise.all([(()=>{try{return _}catch{}})(),(()=>{try{return r}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return c}catch{}})()]).then(async()=>{});export{m as __tla,t as default};
