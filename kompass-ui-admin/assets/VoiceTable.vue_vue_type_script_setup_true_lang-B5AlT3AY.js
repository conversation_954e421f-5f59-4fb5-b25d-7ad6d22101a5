import{d as b,T as h,H as _,o as r,l as i,w as t,i as a,a as p,a9 as C,g as T,t as x,j as c,P,_ as j,N as B,Q as H,R as I,__tla as N}from"./index-BUSn51wb.js";import Q,{__tla as R}from"./main-CG5euiEw.js";import{d as V,__tla as q}from"./formatTime-DWdBpgsM.js";let m,v=Promise.all([(()=>{try{return N}catch{}})(),(()=>{try{return R}catch{}})(),(()=>{try{return q}catch{}})()]).then(async()=>{m=b({__name:"VoiceTable",props:{list:{},loading:{type:Boolean}},emits:["delete"],setup(u,{emit:f}){const n=u,s=f;return(z,A)=>{const l=P,o=j,d=B,y=H,g=h("hasPermi"),w=I;return _((r(),i(y,{data:n.list,stripe:"",border:"",style:{"margin-top":"10px"}},{default:t(()=>[a(l,{label:"\u7F16\u53F7",align:"center",prop:"mediaId"}),a(l,{label:"\u6587\u4EF6\u540D",align:"center",prop:"name"}),a(l,{label:"\u8BED\u97F3",align:"center"},{default:t(e=>[e.row.url?(r(),i(p(Q),{key:0,url:e.row.url},null,8,["url"])):C("",!0)]),_:1}),a(l,{label:"\u4E0A\u4F20\u65F6\u95F4",align:"center",prop:"createTime",formatter:p(V),width:"180"},{default:t(e=>[T("span",null,x(e.row.createTime),1)]),_:1},8,["formatter"]),a(l,{label:"\u64CD\u4F5C",align:"center","class-name":"small-padding fixed-width"},{default:t(e=>[a(d,{type:"primary",link:"",onClick:k=>s("delete",e.row.id)},{default:t(()=>[a(o,{icon:"ep:download"}),c("\u4E0B\u8F7D ")]),_:2},1032,["onClick"]),_((r(),i(d,{type:"primary",link:"",onClick:k=>s("delete",e.row.id)},{default:t(()=>[a(o,{icon:"ep:delete"}),c("\u5220\u9664 ")]),_:2},1032,["onClick"])),[[g,["mp:material:delete"]]])]),_:1})]),_:1},8,["data"])),[[w,n.loading]])}}})});export{m as _,v as __tla};
