import{d as $,n as ee,I as ae,r as i,f as le,b as te,at as de,dW as re,o as n,l as _,w as t,a as l,j as L,a9 as oe,i as e,H as ue,c as w,F as I,k,y as R,dX as z,Z as se,L as ie,E as ce,M as ne,J as me,K as pe,cn as _e,s as fe,z as ve,A as Ve,cc as be,O as ye,N as Pe,R as Ue,__tla as he}from"./index-BUSn51wb.js";import{_ as we,__tla as Ie}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{_ as ke,__tla as ge}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{S as g,__tla as Se}from"./index-DgsXVLii.js";import{_ as xe,__tla as Oe}from"./SaleOrderItemForm.vue_vue_type_script_setup_true_lang-DlqK8GHl.js";import{C as Te,__tla as Fe}from"./index-DYwp4_G0.js";import{A as Ce,__tla as qe}from"./index-LbO7ASKC.js";import{g as Ae,__tla as Le}from"./index-BYXzDB8j.js";let E,Re=Promise.all([(()=>{try{return he}catch{}})(),(()=>{try{return Ie}catch{}})(),(()=>{try{return ge}catch{}})(),(()=>{try{return Se}catch{}})(),(()=>{try{return Oe}catch{}})(),(()=>{try{return Fe}catch{}})(),(()=>{try{return qe}catch{}})(),(()=>{try{return Le}catch{}})()]).then(async()=>{E=$({name:"SaleOrderForm",__name:"SaleOrderForm",emits:["success"],setup(ze,{expose:H,emit:K}){const{t:f}=ee(),S=ae(),m=i(!1),x=i(""),p=i(!1),v=i(""),r=i({id:void 0,customerId:void 0,accountId:void 0,saleUserId:void 0,orderTime:void 0,remark:void 0,fileUrl:"",discountPercent:0,discountPrice:0,totalPrice:0,depositPrice:0,items:[],no:void 0}),M=le({customerId:[{required:!0,message:"\u5BA2\u6237\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],orderTime:[{required:!0,message:"\u8BA2\u5355\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),V=te(()=>v.value==="detail"),b=i(),O=i([]),y=i([]),T=i([]),P=i("item"),F=i();de(()=>r.value,u=>{if(!u)return;const d=u.items.reduce((o,s)=>o+s.totalPrice,0),c=u.discountPercent!=null?re(d,u.discountPercent/100):0;r.value.discountPrice=c,r.value.totalPrice=d-c},{deep:!0}),H({open:async(u,d)=>{if(m.value=!0,x.value=f("action."+u),v.value=u,B(),d){p.value=!0;try{r.value=await g.getSaleOrder(d)}finally{p.value=!1}}O.value=await Te.getCustomerSimpleList(),T.value=await Ae(),y.value=await Ce.getAccountSimpleList();const c=y.value.find(o=>o.defaultStatus);c&&(r.value.accountId=c.id)}});const W=K,j=async()=>{await b.value.validate(),await F.value.validate(),p.value=!0;try{const u=r.value;v.value==="create"?(await g.createSaleOrder(u),S.success(f("common.createSuccess"))):(await g.updateSaleOrder(u),S.success(f("common.updateSuccess"))),m.value=!1,W("success")}finally{p.value=!1}},B=()=>{var u;r.value={id:void 0,customerId:void 0,accountId:void 0,saleUserId:void 0,orderTime:void 0,remark:void 0,fileUrl:void 0,discountPercent:0,discountPrice:0,totalPrice:0,depositPrice:0,items:[]},(u=b.value)==null||u.resetFields()};return(u,d)=>{const c=se,o=ie,s=ce,J=ne,U=me,h=pe,N=_e,C=fe,X=ve,Z=Ve,D=ke,q=be,G=ye,A=Pe,Q=we,Y=Ue;return n(),_(Q,{title:l(x),modelValue:l(m),"onUpdate:modelValue":d[13]||(d[13]=a=>R(m)?m.value=a:null),width:"1080"},{footer:t(()=>[l(V)?oe("",!0):(n(),_(A,{key:0,onClick:j,type:"primary",disabled:l(p)},{default:t(()=>[L(" \u786E \u5B9A ")]),_:1},8,["disabled"])),e(A,{onClick:d[12]||(d[12]=a=>m.value=!1)},{default:t(()=>[L("\u53D6 \u6D88")]),_:1})]),default:t(()=>[ue((n(),_(G,{ref_key:"formRef",ref:b,model:l(r),rules:l(M),"label-width":"100px",disabled:l(V)},{default:t(()=>[e(C,{gutter:20},{default:t(()=>[e(s,{span:8},{default:t(()=>[e(o,{label:"\u8BA2\u5355\u5355\u53F7",prop:"no"},{default:t(()=>[e(c,{disabled:"",modelValue:l(r).no,"onUpdate:modelValue":d[0]||(d[0]=a=>l(r).no=a),placeholder:"\u4FDD\u5B58\u65F6\u81EA\u52A8\u751F\u6210"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(o,{label:"\u8BA2\u5355\u65F6\u95F4",prop:"orderTime"},{default:t(()=>[e(J,{modelValue:l(r).orderTime,"onUpdate:modelValue":d[1]||(d[1]=a=>l(r).orderTime=a),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u8BA2\u5355\u65F6\u95F4",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(o,{label:"\u5BA2\u6237",prop:"customerId"},{default:t(()=>[e(h,{modelValue:l(r).customerId,"onUpdate:modelValue":d[2]||(d[2]=a=>l(r).customerId=a),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237",class:"!w-1/1"},{default:t(()=>[(n(!0),w(I,null,k(l(O),a=>(n(),_(U,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(o,{label:"\u9500\u552E\u4EBA\u5458",prop:"saleUserId"},{default:t(()=>[e(h,{modelValue:l(r).saleUserId,"onUpdate:modelValue":d[3]||(d[3]=a=>l(r).saleUserId=a),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u9500\u552E\u4EBA\u5458",class:"!w-1/1"},{default:t(()=>[(n(!0),w(I,null,k(l(T),a=>(n(),_(U,{key:a.id,label:a.nickname,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{span:16},{default:t(()=>[e(o,{label:"\u5907\u6CE8",prop:"remark"},{default:t(()=>[e(c,{type:"textarea",modelValue:l(r).remark,"onUpdate:modelValue":d[4]||(d[4]=a=>l(r).remark=a),rows:1,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(o,{label:"\u9644\u4EF6",prop:"fileUrl"},{default:t(()=>[e(N,{"is-show-tip":!1,modelValue:l(r).fileUrl,"onUpdate:modelValue":d[5]||(d[5]=a=>l(r).fileUrl=a),limit:1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(D,null,{default:t(()=>[e(Z,{modelValue:l(P),"onUpdate:modelValue":d[6]||(d[6]=a=>R(P)?P.value=a:null),class:"-mt-15px -mb-10px"},{default:t(()=>[e(X,{label:"\u8BA2\u5355\u4EA7\u54C1\u6E05\u5355",name:"item"},{default:t(()=>[e(xe,{ref_key:"itemFormRef",ref:F,items:l(r).items,disabled:l(V)},null,8,["items","disabled"])]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(C,{gutter:20},{default:t(()=>[e(s,{span:8},{default:t(()=>[e(o,{label:"\u4F18\u60E0\u7387\uFF08%\uFF09",prop:"discountPercent"},{default:t(()=>[e(q,{modelValue:l(r).discountPercent,"onUpdate:modelValue":d[7]||(d[7]=a=>l(r).discountPercent=a),"controls-position":"right",min:0,precision:2,placeholder:"\u8BF7\u8F93\u5165\u4F18\u60E0\u7387",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(o,{label:"\u6536\u6B3E\u4F18\u60E0",prop:"discountPrice"},{default:t(()=>[e(c,{disabled:"",modelValue:l(r).discountPrice,"onUpdate:modelValue":d[8]||(d[8]=a=>l(r).discountPrice=a),formatter:l(z)},null,8,["modelValue","formatter"])]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(o,{label:"\u4F18\u60E0\u540E\u91D1\u989D"},{default:t(()=>[e(c,{disabled:"",modelValue:l(r).totalPrice,"onUpdate:modelValue":d[9]||(d[9]=a=>l(r).totalPrice=a),formatter:l(z)},null,8,["modelValue","formatter"])]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(o,{label:"\u7ED3\u7B97\u8D26\u6237",prop:"accountId"},{default:t(()=>[e(h,{modelValue:l(r).accountId,"onUpdate:modelValue":d[10]||(d[10]=a=>l(r).accountId=a),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u7ED3\u7B97\u8D26\u6237",class:"!w-1/1"},{default:t(()=>[(n(!0),w(I,null,k(l(y),a=>(n(),_(U,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:t(()=>[e(o,{label:"\u6536\u53D6\u8BA2\u91D1",prop:"depositPrice"},{default:t(()=>[e(q,{modelValue:l(r).depositPrice,"onUpdate:modelValue":d[11]||(d[11]=a=>l(r).depositPrice=a),"controls-position":"right",min:0,precision:2,placeholder:"\u8BF7\u8F93\u5165\u6536\u53D6\u8BA2\u91D1",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules","disabled"])),[[Y,l(p)]])]),_:1},8,["title","modelValue"])}}})});export{E as _,Re as __tla};
