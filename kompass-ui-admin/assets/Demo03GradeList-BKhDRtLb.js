import{_ as t,__tla as _}from"./Demo03GradeList.vue_vue_type_script_setup_true_lang-CicCRlXR.js";import{__tla as r}from"./index-BUSn51wb.js";import{__tla as a}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{__tla as l}from"./el-card-CJbXGyyg.js";import{__tla as o}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{__tla as c}from"./index-Cch5e1V0.js";import{__tla as m}from"./formatTime-DWdBpgsM.js";import{__tla as e}from"./index-ydnYox5L.js";import{__tla as s}from"./Demo03GradeForm.vue_vue_type_script_setup_true_lang-DY3byLOU.js";import{__tla as n}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";let f=Promise.all([(()=>{try{return _}catch{}})(),(()=>{try{return r}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return c}catch{}})(),(()=>{try{return m}catch{}})(),(()=>{try{return e}catch{}})(),(()=>{try{return s}catch{}})(),(()=>{try{return n}catch{}})()]).then(async()=>{});export{f as __tla,t as default};
