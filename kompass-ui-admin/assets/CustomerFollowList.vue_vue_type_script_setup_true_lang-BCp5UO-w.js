import{d as V,u as z,r as i,bc as G,C as P,o as _,c as v,i as e,w as r,a as t,F as S,k as j,l as C,H as B,j as x,t as N,G as u,g as q,J as H,K as J,L as K,O as Q,v as Y,P as W,Q as X,R as Z,__tla as $}from"./index-BUSn51wb.js";import{_ as ee,__tla as ae}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as le,__tla as te}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as re,__tla as oe}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{e as ne,__tla as pe}from"./index-CD52sTBY.js";import{d,__tla as ie}from"./formatTime-DWdBpgsM.js";import{F as ue}from"./common-BQQO87UM.js";let R,se=Promise.all([(()=>{try{return $}catch{}})(),(()=>{try{return ae}catch{}})(),(()=>{try{return te}catch{}})(),(()=>{try{return oe}catch{}})(),(()=>{try{return pe}catch{}})(),(()=>{try{return ie}catch{}})()]).then(async()=>{let w;w=q("div",{class:"pb-5 text-xl"},"\u5206\u914D\u7ED9\u6211\u7684\u5BA2\u6237",-1),R=V({name:"CrmCustomerFollowList",__name:"CustomerFollowList",setup(_e){const{push:U}=z(),c=i(!0),f=i(0),g=i([]),o=i({pageNo:1,pageSize:10,sceneType:1,followUpStatus:!1}),T=i(),s=async()=>{c.value=!0;try{const m=await ne(o.value);g.value=m.list,f.value=m.total}finally{c.value=!1}},E=()=>{o.value.pageNo=1,s()};return G(async()=>{await s()}),P(()=>{s()}),(m,n)=>{const L=H,O=J,k=K,F=Q,h=re,I=Y,l=W,p=le,M=X,A=ee,D=Z;return _(),v(S,null,[e(h,null,{default:r(()=>[w,e(F,{ref_key:"queryFormRef",ref:T,inline:!0,model:t(o),class:"-mb-15px","label-width":"68px"},{default:r(()=>[e(k,{label:"\u72B6\u6001",prop:"followUpStatus"},{default:r(()=>[e(O,{modelValue:t(o).followUpStatus,"onUpdate:modelValue":n[0]||(n[0]=a=>t(o).followUpStatus=a),class:"!w-240px",placeholder:"\u72B6\u6001",onChange:E},{default:r(()=>[(_(!0),v(S,null,j(t(ue),(a,y)=>(_(),C(L,{label:a.label,value:a.value,key:y},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),e(h,null,{default:r(()=>[B((_(),C(M,{data:t(g),"show-overflow-tooltip":!0,stripe:!0},{default:r(()=>[e(l,{align:"center",label:"\u5BA2\u6237\u540D\u79F0",fixed:"left",prop:"name",width:"160"},{default:r(a=>[e(I,{underline:!1,type:"primary",onClick:y=>{return b=a.row.id,void U({name:"CrmCustomerDetail",params:{id:b}});var b}},{default:r(()=>[x(N(a.row.name),1)]),_:2},1032,["onClick"])]),_:1}),e(l,{align:"center",label:"\u5BA2\u6237\u6765\u6E90",prop:"source",width:"100"},{default:r(a=>[e(p,{type:t(u).CRM_CUSTOMER_SOURCE,value:a.row.source},null,8,["type","value"])]),_:1}),e(l,{label:"\u624B\u673A",align:"center",prop:"mobile",width:"120"}),e(l,{label:"\u7535\u8BDD",align:"center",prop:"telephone",width:"130"}),e(l,{label:"\u90AE\u7BB1",align:"center",prop:"email",width:"180"}),e(l,{align:"center",label:"\u5BA2\u6237\u7EA7\u522B",prop:"level",width:"135"},{default:r(a=>[e(p,{type:t(u).CRM_CUSTOMER_LEVEL,value:a.row.level},null,8,["type","value"])]),_:1}),e(l,{align:"center",label:"\u5BA2\u6237\u884C\u4E1A",prop:"industryId",width:"100"},{default:r(a=>[e(p,{type:t(u).CRM_CUSTOMER_INDUSTRY,value:a.row.industryId},null,8,["type","value"])]),_:1}),e(l,{formatter:t(d),align:"center",label:"\u4E0B\u6B21\u8054\u7CFB\u65F6\u95F4",prop:"contactNextTime",width:"180px"},null,8,["formatter"]),e(l,{align:"center",label:"\u5907\u6CE8",prop:"remark",width:"200"}),e(l,{align:"center",label:"\u9501\u5B9A\u72B6\u6001",prop:"lockStatus"},{default:r(a=>[e(p,{type:t(u).INFRA_BOOLEAN_STRING,value:a.row.lockStatus},null,8,["type","value"])]),_:1}),e(l,{align:"center",label:"\u6210\u4EA4\u72B6\u6001",prop:"dealStatus"},{default:r(a=>[e(p,{type:t(u).INFRA_BOOLEAN_STRING,value:a.row.dealStatus},null,8,["type","value"])]),_:1}),e(l,{formatter:t(d),align:"center",label:"\u6700\u540E\u8DDF\u8FDB\u65F6\u95F4",prop:"contactLastTime",width:"180px"},null,8,["formatter"]),e(l,{align:"center",label:"\u6700\u540E\u8DDF\u8FDB\u8BB0\u5F55",prop:"contactLastContent",width:"200"}),e(l,{label:"\u5730\u5740",align:"center",prop:"detailAddress",width:"180"}),e(l,{align:"center",label:"\u8DDD\u79BB\u8FDB\u5165\u516C\u6D77\u5929\u6570",prop:"poolDay",width:"140"},{default:r(a=>[x(N(a.row.poolDay)+" \u5929",1)]),_:1}),e(l,{align:"center",label:"\u8D1F\u8D23\u4EBA",prop:"ownerUserName",width:"100px"}),e(l,{align:"center",label:"\u6240\u5C5E\u90E8\u95E8",prop:"ownerUserDeptName",width:"100px"}),e(l,{formatter:t(d),align:"center",label:"\u66F4\u65B0\u65F6\u95F4",prop:"updateTime",width:"180px"},null,8,["formatter"]),e(l,{formatter:t(d),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"]),e(l,{align:"center",label:"\u521B\u5EFA\u4EBA",prop:"creatorName",width:"100px"})]),_:1},8,["data"])),[[D,t(c)]]),e(A,{limit:t(o).pageSize,"onUpdate:limit":n[1]||(n[1]=a=>t(o).pageSize=a),page:t(o).pageNo,"onUpdate:page":n[2]||(n[2]=a=>t(o).pageNo=a),total:t(f),onPagination:s},null,8,["limit","page","total"])]),_:1})],64)}}})});export{R as _,se as __tla};
