import{d as C,r as b,at as T,o as r,c as o,F as R,k as B,av as l,i as w,a9 as s,g as y,a0 as i,t as a,l as I,a as S,__tla as P}from"./index-BUSn51wb.js";import{E as $,__tla as L}from"./el-image-BjHZRFih.js";import{b as U,__tla as E}from"./spu-CW3JGweV.js";let g,j=Promise.all([(()=>{try{return P}catch{}})(),(()=>{try{return L}catch{}})(),(()=>{try{return E}catch{}})()]).then(async()=>{let d,u,x;d={key:0,class:"absolute left-0 top-0 z-1 items-center justify-center"},u={class:"text-12px"},x={class:"absolute bottom-8px right-8px"},g=C({name:"ProductCard",__name:"index",props:{property:{}},setup(k){const p=k,f=b([]);T(()=>p.property.spuIds,async()=>{f.value=await U(p.property.spuIds)},{immediate:!0,deep:!0});const v=t=>{const c=p.property.layoutType==="twoCol"?2:1;return{marginLeft:t%c==0?"0":p.property.space+"px",marginTop:t<c?"0":p.property.space+"px"}},m=b(),_=()=>{let t="100%";return p.property.layoutType==="twoCol"&&(t=(m.value.offsetWidth-p.property.space)/2+"px"),{width:t}};return(t,c)=>{const n=$;return r(),o("div",{class:i("box-content min-h-30px w-full flex flex-row flex-wrap"),ref_key:"containerRef",ref:m},[(r(!0),o(R,null,B(S(f),(e,h)=>(r(),o("div",{class:"relative box-content flex flex-row flex-wrap overflow-hidden bg-white",style:l({...v(h),..._(),borderTopLeftRadius:`${t.property.borderRadiusTop}px`,borderTopRightRadius:`${t.property.borderRadiusTop}px`,borderBottomLeftRadius:`${t.property.borderRadiusBottom}px`,borderBottomRightRadius:`${t.property.borderRadiusBottom}px`}),key:h},[t.property.badge.show?(r(),o("div",d,[w(n,{fit:"cover",src:t.property.badge.imgUrl,class:"h-26px w-38px"},null,8,["src"])])):s("",!0),y("div",{class:i(["h-140px",{"w-full":t.property.layoutType!=="oneColSmallImg","w-140px":t.property.layoutType==="oneColSmallImg"}])},[w(n,{fit:"cover",class:"h-full w-full",src:e.picUrl},null,8,["src"])],2),y("div",{class:i([" flex flex-col gap-8px p-8px box-border",{"w-full":t.property.layoutType!=="oneColSmallImg","w-[calc(100%-140px-16px)]":t.property.layoutType==="oneColSmallImg"}])},[t.property.fields.name.show?(r(),o("div",{key:0,class:i(["text-14px ",{truncate:t.property.layoutType!=="oneColSmallImg","overflow-ellipsis line-clamp-2":t.property.layoutType==="oneColSmallImg"}]),style:l({color:t.property.fields.name.color})},a(e.name),7)):s("",!0),t.property.fields.introduction.show?(r(),o("div",{key:1,class:"truncate text-12px",style:l({color:t.property.fields.introduction.color})},a(e.introduction),5)):s("",!0),y("div",null,[t.property.fields.price.show?(r(),o("span",{key:0,class:"text-16px",style:l({color:t.property.fields.price.color})}," \uFFE5"+a(e.price),5)):s("",!0),t.property.fields.marketPrice.show&&e.marketPrice?(r(),o("span",{key:1,class:"ml-4px text-10px line-through",style:l({color:t.property.fields.marketPrice.color})},"\uFFE5"+a(e.marketPrice),5)):s("",!0)]),y("div",u,[t.property.fields.salesCount.show?(r(),o("span",{key:0,style:l({color:t.property.fields.salesCount.color})}," \u5DF2\u552E"+a((e.salesCount||0)+(e.virtualSalesCount||0))+"\u4EF6 ",5)):s("",!0),t.property.fields.stock.show?(r(),o("span",{key:1,style:l({color:t.property.fields.stock.color})}," \u5E93\u5B58"+a(e.stock||0),5)):s("",!0)])],2),y("div",x,[t.property.btnBuy.type==="text"?(r(),o("span",{key:0,class:"rounded-full p-x-12px p-y-4px text-12px text-white",style:l({background:`linear-gradient(to right, ${t.property.btnBuy.bgBeginColor}, ${t.property.btnBuy.bgEndColor}`})},a(t.property.btnBuy.text),5)):(r(),I(n,{key:1,class:"h-28px w-28px rounded-full",fit:"cover",src:t.property.btnBuy.imgUrl},null,8,["src"]))])],4))),128))],512)}}})});export{j as __tla,g as default};
