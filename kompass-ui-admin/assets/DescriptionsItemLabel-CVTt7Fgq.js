import{d as i,o as t,c as r,l as c,a9 as n,j as o,t as _,_ as d,B as p,__tla as m}from"./index-BUSn51wb.js";let l,u=Promise.all([(()=>{try{return m}catch{}})()]).then(async()=>{let e;e={class:"cell-item"},l=p(i({__name:"DescriptionsItemLabel",props:{label:{type:String,required:!0},icon:{type:String,required:!1}},setup:a=>(y,b)=>{const s=d;return t(),r("div",e,[a.icon?(t(),c(s,{key:0,icon:a.icon,style:{"vertical-align":"middle"},size:18},null,8,["icon"])):n("",!0),o(" "+_(a.label),1)])}}),[["__scopeId","data-v-ebcbf65a"]])});export{l as D,u as __tla};
