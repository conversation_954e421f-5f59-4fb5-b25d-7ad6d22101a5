import{d as R,r as n,f as S,o as V,l as b,w as s,i as u,j as k,a,H as j,y as H,n as D,I as L,Z as N,L as O,O as Z,N as z,R as A,__tla as B}from"./index-BUSn51wb.js";import{_ as E,__tla as G}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{e as J,c as K,u as M,__tla as Q}from"./property-BdOytbZT.js";let g,T=Promise.all([(()=>{try{return B}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return Q}catch{}})()]).then(async()=>{g=R({name:"ProductPropertyValueForm",__name:"ValueForm",emits:["success"],setup(W,{expose:h,emit:I}){const{t:p}=D(),_=L(),o=n(!1),y=n(""),d=n(!1),v=n(""),l=n({id:void 0,propertyId:void 0,name:"",remark:""}),w=S({propertyId:[{required:!0,message:"\u5C5E\u6027\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],name:[{required:!0,message:"\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),i=n();h({open:async(r,e,m)=>{if(o.value=!0,y.value=p("action."+r),v.value=r,F(),l.value.propertyId=e,m){d.value=!0;try{l.value=await J(m)}finally{d.value=!1}}}});const x=I,U=async()=>{if(i&&await i.value.validate()){d.value=!0;try{const r=l.value;v.value==="create"?(await K(r),_.success(p("common.createSuccess"))):(await M(r),_.success(p("common.updateSuccess"))),o.value=!1,x("success")}finally{d.value=!1}}},F=()=>{var r;l.value={id:void 0,propertyId:void 0,name:"",remark:""},(r=i.value)==null||r.resetFields()};return(r,e)=>{const m=N,c=O,P=Z,f=z,q=E,C=A;return V(),b(q,{modelValue:a(o),"onUpdate:modelValue":e[4]||(e[4]=t=>H(o)?o.value=t:null),title:a(y)},{footer:s(()=>[u(f,{disabled:a(d),type:"primary",onClick:U},{default:s(()=>[k("\u786E \u5B9A")]),_:1},8,["disabled"]),u(f,{onClick:e[3]||(e[3]=t=>o.value=!1)},{default:s(()=>[k("\u53D6 \u6D88")]),_:1})]),default:s(()=>[j((V(),b(P,{ref_key:"formRef",ref:i,model:a(l),rules:a(w),"label-width":"80px"},{default:s(()=>[u(c,{label:"\u5C5E\u6027\u7F16\u53F7",prop:"category"},{default:s(()=>[u(m,{modelValue:a(l).propertyId,"onUpdate:modelValue":e[0]||(e[0]=t=>a(l).propertyId=t),disabled:""},null,8,["modelValue"])]),_:1}),u(c,{label:"\u540D\u79F0",prop:"name"},{default:s(()=>[u(m,{modelValue:a(l).name,"onUpdate:modelValue":e[1]||(e[1]=t=>a(l).name=t),placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0"},null,8,["modelValue"])]),_:1}),u(c,{label:"\u5907\u6CE8",prop:"remark"},{default:s(()=>[u(m,{modelValue:a(l).remark,"onUpdate:modelValue":e[2]||(e[2]=t=>a(l).remark=t),placeholder:"\u8BF7\u8F93\u5165\u5185\u5BB9",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[C,a(d)]])]),_:1},8,["modelValue","title"])}}})});export{g as _,T as __tla};
