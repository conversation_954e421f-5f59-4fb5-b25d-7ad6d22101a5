import{by as f,d as F,n as I,I as L,r as s,b as R,f as j,C as A,o as y,c as H,i as e,w as a,H as b,l as N,a as u,a8 as O,j as n,y as S,F as Z,Z as q,L as B,ce as J,cc as K,z as Q,A as W,N as X,O as Y,R as $,__tla as ee}from"./index-BUSn51wb.js";import{_ as ae,__tla as le}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{E as te,__tla as ue}from"./el-text-CIwNlU-U.js";import{_ as de,__tla as re}from"./index-COobLwz-.js";import{__tla as oe}from"./el-card-CJbXGyyg.js";let v,ne=Promise.all([(()=>{try{return ee}catch{}})(),(()=>{try{return le}catch{}})(),(()=>{try{return ue}catch{}})(),(()=>{try{return re}catch{}})(),(()=>{try{return oe}catch{}})()]).then(async()=>{v=F({name:"MemberConfig",__name:"index",setup(ie){const{t:T}=I(),V=L(),h=s(!1),c=s(!1),l=s({id:void 0,pointTradeDeductEnable:!0,pointTradeDeductUnitPrice:0,pointTradeDeductMaxPrice:0,pointTradeGivePoint:0}),p=R({get:()=>(l.value.pointTradeDeductUnitPrice/100).toFixed(2),set:r=>{l.value.pointTradeDeductUnitPrice=Math.round(100*r)}}),P=j({}),_=s(),D=async()=>{if(_&&await _.value.validate()){c.value=!0;try{const r=l.value;await(async t=>await f.put({url:"/member/config/save",data:t}))(r),V.success(T("common.updateSuccess")),h.value=!1}finally{c.value=!1}}},w=async()=>{try{const r=await(async()=>await f.get({url:"/member/config/get"}))();if(r===null)return;l.value=r}finally{}};return A(()=>{w()}),(r,t)=>{const U=de,x=q,o=B,E=J,i=te,m=K,g=Q,M=W,z=X,G=Y,C=ae,k=$;return y(),H(Z,null,[e(U,{title:"\u4F1A\u5458\u624B\u518C\uFF08\u529F\u80FD\u5F00\u542F\uFF09",url:"https://doc.iocoder.cn/member/build/"}),e(C,null,{default:a(()=>[b((y(),N(G,{ref_key:"formRef",ref:_,model:u(l),rules:u(P),"label-width":"120px"},{default:a(()=>[b(e(o,{label:"hideId"},{default:a(()=>[e(x,{modelValue:u(l).id,"onUpdate:modelValue":t[0]||(t[0]=d=>u(l).id=d)},null,8,["modelValue"])]),_:1},512),[[O,!1]]),e(M,null,{default:a(()=>[e(g,{label:"\u79EF\u5206"},{default:a(()=>[e(o,{label:"\u79EF\u5206\u62B5\u6263",prop:"pointTradeDeductEnable"},{default:a(()=>[e(E,{modelValue:u(l).pointTradeDeductEnable,"onUpdate:modelValue":t[1]||(t[1]=d=>u(l).pointTradeDeductEnable=d),style:{"user-select":"none"}},null,8,["modelValue"]),e(i,{class:"w-full",size:"small",type:"info"},{default:a(()=>[n("\u4E0B\u5355\u79EF\u5206\u662F\u5426\u62B5\u7528\u8BA2\u5355\u91D1\u989D")]),_:1})]),_:1}),e(o,{label:"\u79EF\u5206\u62B5\u6263",prop:"pointTradeDeductUnitPrice"},{default:a(()=>[e(m,{modelValue:u(p),"onUpdate:modelValue":t[2]||(t[2]=d=>S(p)?p.value=d:null),placeholder:"\u8BF7\u8F93\u5165\u79EF\u5206\u62B5\u6263\u91D1\u989D",precision:2},null,8,["modelValue"]),e(i,{class:"w-full",size:"small",type:"info"},{default:a(()=>[n(" \u79EF\u5206\u62B5\u7528\u6BD4\u4F8B(1 \u79EF\u5206\u62B5\u591A\u5C11\u91D1\u989D)\uFF0C\u5355\u4F4D\uFF1A\u5143 ")]),_:1})]),_:1}),e(o,{label:"\u79EF\u5206\u62B5\u6263\u6700\u5927\u503C",prop:"pointTradeDeductMaxPrice"},{default:a(()=>[e(m,{modelValue:u(l).pointTradeDeductMaxPrice,"onUpdate:modelValue":t[3]||(t[3]=d=>u(l).pointTradeDeductMaxPrice=d),placeholder:"\u8BF7\u8F93\u5165\u79EF\u5206\u62B5\u6263\u6700\u5927\u503C"},null,8,["modelValue"]),e(i,{class:"w-full",size:"small",type:"info"},{default:a(()=>[n(" \u5355\u6B21\u4E0B\u5355\u79EF\u5206\u4F7F\u7528\u4E0A\u9650\uFF0C0 \u4E0D\u9650\u5236 ")]),_:1})]),_:1}),e(o,{label:"1 \u5143\u8D60\u9001\u591A\u5C11\u5206",prop:"pointTradeGivePoint"},{default:a(()=>[e(m,{modelValue:u(l).pointTradeGivePoint,"onUpdate:modelValue":t[4]||(t[4]=d=>u(l).pointTradeGivePoint=d),placeholder:"\u8BF7\u8F93\u5165 1 \u5143\u8D60\u9001\u591A\u5C11\u79EF\u5206"},null,8,["modelValue"]),e(i,{class:"w-full",size:"small",type:"info"},{default:a(()=>[n(" \u4E0B\u5355\u652F\u4ED8\u91D1\u989D\u6309\u6BD4\u4F8B\u8D60\u9001\u79EF\u5206\uFF08\u5B9E\u9645\u652F\u4ED8 1 \u5143\u8D60\u9001\u591A\u5C11\u79EF\u5206\uFF09 ")]),_:1})]),_:1})]),_:1})]),_:1}),e(o,null,{default:a(()=>[e(z,{type:"primary",onClick:D},{default:a(()=>[n("\u4FDD\u5B58")]),_:1})]),_:1})]),_:1},8,["model","rules"])),[[k,u(c)]])]),_:1})],64)}}})});export{ne as __tla,v as default};
