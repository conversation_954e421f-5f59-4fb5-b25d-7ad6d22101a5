import{by as a,__tla as t}from"./index-BUSn51wb.js";let e,o=Promise.all([(()=>{try{return t}catch{}})()]).then(async()=>{e={getOrderConfirmPage:async r=>await a.get({url:"/als/order-confirm/page",params:r}),getOrderConfirm:async r=>await a.get({url:"/als/order-confirm/get?id="+r}),createOrderConfirm:async r=>await a.post({url:"/als/order-confirm/create",data:r}),updateOrderConfirm:async r=>await a.put({url:"/als/order-confirm/update",data:r}),deleteOrderConfirm:async r=>await a.delete({url:"/als/order-confirm/delete?id="+r}),exportOrderConfirm:async r=>await a.download({url:"/als/order-confirm/export-excel",params:r}),confirm:async r=>await a.get({url:"/als/order-confirm/confirm?id="+r}),reject:async r=>await a.post({url:"/als/order-confirm/reject",data:r})}});export{e as O,o as __tla};
