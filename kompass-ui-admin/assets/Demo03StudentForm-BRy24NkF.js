import{_ as t,__tla as _}from"./Demo03StudentForm.vue_vue_type_script_setup_true_lang-bK-LoLDo.js";import{__tla as r}from"./index-BUSn51wb.js";import{__tla as a}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as l}from"./index-LkK3YDGb.js";import{__tla as o}from"./Demo03CourseForm.vue_vue_type_script_setup_true_lang-l9i70whM.js";import{__tla as c}from"./Demo03GradeForm.vue_vue_type_script_setup_true_lang-Dj4gh0up.js";let m=Promise.all([(()=>{try{return _}catch{}})(),(()=>{try{return r}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return c}catch{}})()]).then(async()=>{});export{m as __tla,t as default};
