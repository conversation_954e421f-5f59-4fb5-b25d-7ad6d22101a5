import{by as e,__tla as c}from"./index-BUSn51wb.js";let t,s,l,r,y,o,m,i=Promise.all([(()=>{try{return c}catch{}})()]).then(async()=>{s=async a=>await e.get({url:"/system/role/page",params:a}),o=async()=>await e.get({url:"/system/role/simple-list"}),t=async a=>await e.get({url:"/system/role/get?id="+a}),l=async a=>await e.post({url:"/system/role/create",data:a}),m=async a=>await e.put({url:"/system/role/update",data:a}),r=async a=>await e.delete({url:"/system/role/delete?id="+a}),y=a=>e.download({url:"/system/role/export-excel",params:a})});export{i as __tla,t as a,s as b,l as c,r as d,y as e,o as g,m as u};
