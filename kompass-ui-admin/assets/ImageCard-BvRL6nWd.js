import{d as $,I as q,r as h,c7 as z,at as D,af as H,C as J,o as c,l as n,w as r,g as o,a,j as p,a9 as A,i as u,ec as K,e6 as Q,ed as T,e4 as V,c as M,t as v,F as W,k as X,N as Y,B as Z,__tla as aa}from"./index-BUSn51wb.js";import{E as ta,__tla as ea}from"./el-card-CJbXGyyg.js";import{E as sa,__tla as la}from"./el-image-BjHZRFih.js";import{A as m}from"./constants-C0I8ujwj.js";let N,ca=Promise.all([(()=>{try{return aa}catch{}})(),(()=>{try{return ea}catch{}})(),(()=>{try{return la}catch{}})()]).then(async()=>{let b,f,g;b={class:"image-operation"},f={key:0},g={class:"image-mj-btns"},N=Z($({__name:"ImageCard",props:{detail:{type:Object,require:!0}},emits:["onBtnClick","onMjBtnClick"],setup(O,{emit:P}){const U=q(),y=O,C=h(),d=h(),_=async(i,e)=>{x("onBtnClick",i,e)},x=P,{detail:t}=z(y);D(t,async(i,e)=>{await w(i.status)});const w=async i=>{i===m.IN_PROGRESS?d.value=H.service({target:C.value,text:"\u751F\u6210\u4E2D..."}):d.value&&(d.value.close(),d.value=null)};return J(async()=>{await w(y.detail.status)}),(i,e)=>{const s=Y,G=sa,L=ta;return c(),n(L,{"body-class":"",class:"image-card"},{default:r(()=>{var I,j,S,B,E,R,F;return[o("div",b,[o("div",null,[((I=a(t))==null?void 0:I.status)===a(m).IN_PROGRESS?(c(),n(s,{key:0,type:"primary",text:"",bg:""},{default:r(()=>[p(" \u751F\u6210\u4E2D ")]),_:1})):((j=a(t))==null?void 0:j.status)===a(m).SUCCESS?(c(),n(s,{key:1,text:"",bg:""},{default:r(()=>[p(" \u5DF2\u5B8C\u6210 ")]),_:1})):((S=a(t))==null?void 0:S.status)===a(m).FAIL?(c(),n(s,{key:2,type:"danger",text:"",bg:""},{default:r(()=>[p(" \u5F02\u5E38 ")]),_:1})):A("",!0)]),o("div",null,[u(s,{class:"btn",text:"",icon:a(K),onClick:e[0]||(e[0]=l=>_("download",a(t)))},null,8,["icon"]),u(s,{class:"btn",text:"",icon:a(Q),onClick:e[1]||(e[1]=l=>_("regeneration",a(t)))},null,8,["icon"]),u(s,{class:"btn",text:"",icon:a(T),onClick:e[2]||(e[2]=l=>_("delete",a(t)))},null,8,["icon"]),u(s,{class:"btn",text:"",icon:a(V),onClick:e[3]||(e[3]=l=>_("more",a(t)))},null,8,["icon"])])]),o("div",{class:"image-wrapper",ref_key:"cardImageRef",ref:C},[u(G,{class:"image",src:(B=a(t))==null?void 0:B.picUrl,"preview-src-list":[a(t).picUrl],"preview-teleported":""},null,8,["src","preview-src-list"]),((E=a(t))==null?void 0:E.status)===a(m).FAIL?(c(),M("div",f,v((R=a(t))==null?void 0:R.errorMessage),1)):A("",!0)],512),o("div",g,[(c(!0),M(W,null,X((F=a(t))==null?void 0:F.buttons,l=>(c(),n(s,{size:"small",key:l,class:"min-w-40px ml-0 mr-10px mt-5px",onClick:ia=>(async k=>{await U.confirm(`\u786E\u8BA4\u64CD\u4F5C "${k.label} ${k.emoji}" ?`),x("onMjBtnClick",k,y.detail)})(l)},{default:r(()=>[p(v(l.label)+v(l.emoji),1)]),_:2},1032,["onClick"]))),128))])]}),_:1})}}}),[["__scopeId","data-v-b5b6c7a7"]])});export{ca as __tla,N as default};
