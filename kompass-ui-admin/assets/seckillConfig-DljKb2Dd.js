import{by as i,__tla as o}from"./index-BUSn51wb.js";let e,c=Promise.all([(()=>{try{return o}catch{}})()]).then(async()=>{e={getSeckillConfigPage:async t=>await i.get({url:"/promotion/seckill-config/page",params:t}),getSimpleSeckillConfigList:async()=>await i.get({url:"/promotion/seckill-config/list"}),getSeckillConfig:async t=>await i.get({url:"/promotion/seckill-config/get?id="+t}),createSeckillConfig:async t=>await i.post({url:"/promotion/seckill-config/create",data:t}),updateSeckillConfig:async t=>await i.put({url:"/promotion/seckill-config/update",data:t}),deleteSeckillConfig:async t=>await i.delete({url:"/promotion/seckill-config/delete?id="+t}),updateSeckillConfigStatus:async(t,a)=>{const l={id:t,status:a};return i.put({url:"/promotion/seckill-config/update-status",data:l})}}});export{e as S,c as __tla};
