import{d as q,I as J,n as K,u as Q,r as f,f as B,C as X,T as Z,o as p,c as b,i as a,w as t,a as l,F as I,k as V,l as i,V as $,G as u,dR as ee,j as c,H as y,g as ae,t as le,J as te,K as re,L as oe,M as ne,_ as pe,N as ie,O as ue,P as se,Q as de,R as _e,__tla as ce}from"./index-BUSn51wb.js";import{_ as me,__tla as fe}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as ye,__tla as ge}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as we,__tla as ve}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{d as he,__tla as be}from"./formatTime-DWdBpgsM.js";import{W as L,__tla as Ie}from"./index-CG1HTB0Z.js";import{g as Te,__tla as ke}from"./index-BYXzDB8j.js";import{__tla as xe}from"./index-Cch5e1V0.js";import"./color-BN7ZL7BD.js";import{__tla as Ae}from"./el-card-CJbXGyyg.js";import"./fetch-D5K_4anA.js";let U,Ce=Promise.all([(()=>{try{return ce}catch{}})(),(()=>{try{return fe}catch{}})(),(()=>{try{return ge}catch{}})(),(()=>{try{return ve}catch{}})(),(()=>{try{return be}catch{}})(),(()=>{try{return Ie}catch{}})(),(()=>{try{return ke}catch{}})(),(()=>{try{return xe}catch{}})(),(()=>{try{return Ae}catch{}})()]).then(async()=>{U=q({name:"AiWriteManager",__name:"index",setup(Ve){const E=J(),{t:O}=K(),Y=Q(),T=f(!0),R=f([]),W=f(0),r=B({pageNo:1,pageSize:10,userId:void 0,type:void 0,platform:void 0,createTime:void 0}),P=f(),k=f([]),g=async()=>{T.value=!0;try{const s=await L.getWritePage(r);R.value=s.list,W.value=s.total}finally{T.value=!1}},M=()=>{r.pageNo=1,g()},D=()=>{P.value.resetFields(),M()},N=(s,o)=>{s==="create"&&Y.push("/ai/write")};return X(async()=>{g(),k.value=await Te()}),(s,o)=>{const x=te,A=re,m=oe,G=ne,w=pe,d=ie,H=ue,F=we,n=se,_=ye,S=de,z=me,v=Z("hasPermi"),j=_e;return p(),b(I,null,[a(F,null,{default:t(()=>[a(H,{class:"-mb-15px",model:l(r),ref_key:"queryFormRef",ref:P,inline:!0,"label-width":"68px"},{default:t(()=>[a(m,{label:"\u7528\u6237\u7F16\u53F7",prop:"userId"},{default:t(()=>[a(A,{modelValue:l(r).userId,"onUpdate:modelValue":o[0]||(o[0]=e=>l(r).userId=e),clearable:"",placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u7F16\u53F7",class:"!w-240px"},{default:t(()=>[(p(!0),b(I,null,V(l(k),e=>(p(),i(x,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(m,{label:"\u5199\u4F5C\u7C7B\u578B",prop:"type"},{default:t(()=>[a(A,{modelValue:l(r).type,"onUpdate:modelValue":o[1]||(o[1]=e=>l(r).type=e),placeholder:"\u8BF7\u9009\u62E9\u5199\u4F5C\u7C7B\u578B",clearable:"",class:"!w-240px"},{default:t(()=>[(p(!0),b(I,null,V(l($)(l(u).AI_WRITE_TYPE),e=>(p(),i(x,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(m,{label:"\u5E73\u53F0",prop:"platform"},{default:t(()=>[a(A,{modelValue:l(r).platform,"onUpdate:modelValue":o[2]||(o[2]=e=>l(r).platform=e),placeholder:"\u8BF7\u9009\u62E9\u5E73\u53F0",clearable:"",class:"!w-240px"},{default:t(()=>[(p(!0),b(I,null,V(l(ee)(l(u).AI_PLATFORM),e=>(p(),i(x,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(m,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:t(()=>[a(G,{modelValue:l(r).createTime,"onUpdate:modelValue":o[3]||(o[3]=e=>l(r).createTime=e),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),a(m,null,{default:t(()=>[a(d,{onClick:M},{default:t(()=>[a(w,{icon:"ep:search",class:"mr-5px"}),c(" \u641C\u7D22")]),_:1}),a(d,{onClick:D},{default:t(()=>[a(w,{icon:"ep:refresh",class:"mr-5px"}),c(" \u91CD\u7F6E")]),_:1}),y((p(),i(d,{type:"primary",plain:"",onClick:o[4]||(o[4]=e=>N("create"))},{default:t(()=>[a(w,{icon:"ep:plus",class:"mr-5px"}),c(" \u65B0\u589E ")]),_:1})),[[v,["ai:write:create"]]]),y((p(),i(d,{type:"success",plain:"",onClick:s.handleExport,loading:s.exportLoading},{default:t(()=>[a(w,{icon:"ep:download",class:"mr-5px"}),c(" \u5BFC\u51FA ")]),_:1},8,["onClick","loading"])),[[v,["ai:write:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(F,null,{default:t(()=>[y((p(),i(S,{data:l(R),stripe:!0,"show-overflow-tooltip":!0},{default:t(()=>[a(n,{label:"\u7F16\u53F7",align:"center",prop:"id",width:"120",fixed:"left"}),a(n,{label:"\u7528\u6237",align:"center",prop:"userId",width:"180"},{default:t(e=>{var h;return[ae("span",null,le((h=l(k).find(C=>C.id===e.row.userId))==null?void 0:h.nickname),1)]}),_:1}),a(n,{label:"\u5199\u4F5C\u7C7B\u578B",align:"center",prop:"type"},{default:t(e=>[a(_,{type:l(u).AI_WRITE_TYPE,value:e.row.type},null,8,["type","value"])]),_:1}),a(n,{label:"\u5E73\u53F0",align:"center",prop:"platform",width:"120"},{default:t(e=>[a(_,{type:l(u).AI_PLATFORM,value:e.row.platform},null,8,["type","value"])]),_:1}),a(n,{label:"\u6A21\u578B",align:"center",prop:"model",width:"180"}),a(n,{label:"\u751F\u6210\u5185\u5BB9\u63D0\u793A",align:"center",prop:"prompt",width:"180","show-overflow-tooltip":""}),a(n,{label:"\u751F\u6210\u7684\u5185\u5BB9",align:"center",prop:"generatedContent",width:"180"}),a(n,{label:"\u539F\u6587",align:"center",prop:"originalContent",width:"180"}),a(n,{label:"\u957F\u5EA6",align:"center",prop:"length"},{default:t(e=>[a(_,{type:l(u).AI_WRITE_LENGTH,value:e.row.length},null,8,["type","value"])]),_:1}),a(n,{label:"\u683C\u5F0F",align:"center",prop:"format"},{default:t(e=>[a(_,{type:l(u).AI_WRITE_FORMAT,value:e.row.format},null,8,["type","value"])]),_:1}),a(n,{label:"\u8BED\u6C14",align:"center",prop:"tone"},{default:t(e=>[a(_,{type:l(u).AI_WRITE_TONE,value:e.row.tone},null,8,["type","value"])]),_:1}),a(n,{label:"\u8BED\u8A00",align:"center",prop:"language"},{default:t(e=>[a(_,{type:l(u).AI_WRITE_LANGUAGE,value:e.row.language},null,8,["type","value"])]),_:1}),a(n,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:l(he),width:"180px"},null,8,["formatter"]),a(n,{label:"\u9519\u8BEF\u4FE1\u606F",align:"center",prop:"errorMessage"}),a(n,{label:"\u64CD\u4F5C",align:"center"},{default:t(e=>[y((p(),i(d,{link:"",type:"primary",onClick:h=>N("update",e.row.id)},{default:t(()=>[c(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[v,["ai:write:update"]]]),y((p(),i(d,{link:"",type:"danger",onClick:h=>(async C=>{try{await E.delConfirm(),await L.deleteWrite(C),E.success(O("common.delSuccess")),await g()}catch{}})(e.row.id)},{default:t(()=>[c(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[v,["ai:write:delete"]]])]),_:1})]),_:1},8,["data"])),[[j,l(T)]]),a(z,{total:l(W),page:l(r).pageNo,"onUpdate:page":o[5]||(o[5]=e=>l(r).pageNo=e),limit:l(r).pageSize,"onUpdate:limit":o[6]||(o[6]=e=>l(r).pageSize=e),onPagination:g},null,8,["total","page","limit"])]),_:1})],64)}}})});export{Ce as __tla,U as default};
