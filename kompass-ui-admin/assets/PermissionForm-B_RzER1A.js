import{_ as t,__tla as _}from"./PermissionForm.vue_vue_type_script_setup_true_lang-oI9oCvWg.js";import{__tla as r}from"./index-BUSn51wb.js";import{__tla as a}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as l}from"./index-BYXzDB8j.js";import{__tla as o}from"./index-pKzyIv29.js";let c=Promise.all([(()=>{try{return _}catch{}})(),(()=>{try{return r}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})()]).then(async()=>{});export{c as __tla,t as default};
