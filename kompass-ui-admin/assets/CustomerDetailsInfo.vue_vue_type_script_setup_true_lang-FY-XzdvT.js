import{_ as E,__tla as I}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{d as R,r as T,o as U,l as M,w as e,i as t,j as s,t as u,a as r,G as c,y as x,g as p,__tla as N}from"./index-BUSn51wb.js";import{E as S,a as L,__tla as O}from"./el-collapse-item-B_QvnH_b.js";import{E as V,a as j,__tla as w}from"./el-descriptions-item-dD3qa0ub.js";import{_ as D,__tla as q}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{f as _,__tla as Q}from"./formatTime-DWdBpgsM.js";let y,g=Promise.all([(()=>{try{return I}catch{}})(),(()=>{try{return N}catch{}})(),(()=>{try{return O}catch{}})(),(()=>{try{return w}catch{}})(),(()=>{try{return q}catch{}})(),(()=>{try{return Q}catch{}})()]).then(async()=>{let n,f;n=p("span",{class:"text-base font-bold"},"\u57FA\u672C\u4FE1\u606F",-1),f=p("span",{class:"text-base font-bold"},"\u7CFB\u7EDF\u4FE1\u606F",-1),y=R({name:"CrmCustomerDetailsInfo",__name:"CustomerDetailsInfo",props:{customer:{}},setup(k){const o=T(["basicInfo","systemInfo"]);return(a,d)=>{const l=V,m=D,b=j,i=S,C=L,h=E;return U(),M(h,null,{default:e(()=>[t(C,{modelValue:r(o),"onUpdate:modelValue":d[0]||(d[0]=v=>x(o)?o.value=v:null),class:""},{default:e(()=>[t(i,{name:"basicInfo"},{title:e(()=>[n]),default:e(()=>[t(b,{column:4},{default:e(()=>[t(l,{label:"\u5BA2\u6237\u540D\u79F0"},{default:e(()=>[s(u(a.customer.name),1)]),_:1}),t(l,{label:"\u5BA2\u6237\u6765\u6E90"},{default:e(()=>[t(m,{type:r(c).CRM_CUSTOMER_SOURCE,value:a.customer.source},null,8,["type","value"])]),_:1}),t(l,{label:"\u624B\u673A"},{default:e(()=>[s(u(a.customer.mobile),1)]),_:1}),t(l,{label:"\u7535\u8BDD"},{default:e(()=>[s(u(a.customer.telephone),1)]),_:1}),t(l,{label:"\u90AE\u7BB1"},{default:e(()=>[s(u(a.customer.email),1)]),_:1}),t(l,{label:"\u5730\u5740"},{default:e(()=>[s(u(a.customer.areaName)+" "+u(a.customer.detailAddress),1)]),_:1}),t(l,{label:"QQ"},{default:e(()=>[s(u(a.customer.qq),1)]),_:1}),t(l,{label:"\u5FAE\u4FE1"},{default:e(()=>[s(u(a.customer.wechat),1)]),_:1}),t(l,{label:"\u5BA2\u6237\u884C\u4E1A"},{default:e(()=>[t(m,{type:r(c).CRM_CUSTOMER_INDUSTRY,value:a.customer.industryId},null,8,["type","value"])]),_:1}),t(l,{label:"\u5BA2\u6237\u7EA7\u522B"},{default:e(()=>[t(m,{type:r(c).CRM_CUSTOMER_LEVEL,value:a.customer.level},null,8,["type","value"])]),_:1}),t(l,{label:"\u4E0B\u6B21\u8054\u7CFB\u65F6\u95F4"},{default:e(()=>[s(u(r(_)(a.customer.contactNextTime)),1)]),_:1}),t(l,{label:"\u5907\u6CE8"},{default:e(()=>[s(u(a.customer.remark),1)]),_:1})]),_:1})]),_:1}),t(i,{name:"systemInfo"},{title:e(()=>[f]),default:e(()=>[t(b,{column:4},{default:e(()=>[t(l,{label:"\u8D1F\u8D23\u4EBA"},{default:e(()=>[s(u(a.customer.ownerUserName),1)]),_:1}),t(l,{label:"\u6700\u540E\u8DDF\u8FDB\u8BB0\u5F55"},{default:e(()=>[s(u(a.customer.contactLastContent),1)]),_:1}),t(l,{label:"\u6700\u540E\u8DDF\u8FDB\u65F6\u95F4"},{default:e(()=>[s(u(r(_)(a.customer.contactLastTime)),1)]),_:1}),t(l,{label:""},{default:e(()=>[s("\xA0")]),_:1}),t(l,{label:"\u521B\u5EFA\u4EBA"},{default:e(()=>[s(u(a.customer.creatorName),1)]),_:1}),t(l,{label:"\u521B\u5EFA\u65F6\u95F4"},{default:e(()=>[s(u(r(_)(a.customer.createTime)),1)]),_:1}),t(l,{label:"\u66F4\u65B0\u65F6\u95F4"},{default:e(()=>[s(u(r(_)(a.customer.updateTime)),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})}}})});export{y as _,g as __tla};
