import{d as q,u as K,I as L,n as O,r as _,f as Q,C as Z,T as A,o as u,c as B,i as a,w as e,a as t,U as E,j as p,H as d,l as f,F as G,Z as J,L as W,M as X,_ as $,N as aa,O as ea,P as ta,Q as la,R as ra,__tla as oa}from"./index-BUSn51wb.js";import{_ as na,__tla as pa}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as sa,__tla as ia}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as ca,__tla as _a}from"./index-COobLwz-.js";import{d as ua,__tla as ma}from"./formatTime-DWdBpgsM.js";import{h as da,i as fa,__tla as ya}from"./property-BdOytbZT.js";import{_ as ha,__tla as ga}from"./PropertyForm.vue_vue_type_script_setup_true_lang-DeU9UZ5a.js";import{__tla as wa}from"./index-Cch5e1V0.js";import{__tla as ka}from"./el-card-CJbXGyyg.js";import{__tla as va}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";let N,Ca=Promise.all([(()=>{try{return oa}catch{}})(),(()=>{try{return pa}catch{}})(),(()=>{try{return ia}catch{}})(),(()=>{try{return _a}catch{}})(),(()=>{try{return ma}catch{}})(),(()=>{try{return ya}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return wa}catch{}})(),(()=>{try{return ka}catch{}})(),(()=>{try{return va}catch{}})()]).then(async()=>{N=q({name:"ProductProperty",__name:"index",setup(ba){const{push:U}=K(),v=L(),{t:Y}=O(),y=_(!0),C=_(0),b=_([]),o=Q({pageNo:1,pageSize:10,name:void 0,createTime:[]}),x=_(),s=async()=>{y.value=!0;try{const i=await da(o);b.value=i.list,C.value=i.total}finally{y.value=!1}},h=()=>{o.pageNo=1,s()},D=()=>{x.value.resetFields(),h()},P=_(),V=(i,l)=>{P.value.open(i,l)};return Z(()=>{s()}),(i,l)=>{const z=ca,F=J,g=W,H=X,w=$,n=aa,M=ea,S=sa,c=ta,R=la,I=na,k=A("hasPermi"),j=ra;return u(),B(G,null,[a(z,{title:"\u3010\u5546\u54C1\u3011\u5546\u54C1\u5C5E\u6027",url:"https://doc.iocoder.cn/mall/product-property/"}),a(S,null,{default:e(()=>[a(M,{ref_key:"queryFormRef",ref:x,inline:!0,model:t(o),class:"-mb-15px","label-width":"68px"},{default:e(()=>[a(g,{label:"\u540D\u79F0",prop:"name"},{default:e(()=>[a(F,{modelValue:t(o).name,"onUpdate:modelValue":l[0]||(l[0]=r=>t(o).name=r),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0",onKeyup:E(h,["enter"])},null,8,["modelValue"])]),_:1}),a(g,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:e(()=>[a(H,{modelValue:t(o).createTime,"onUpdate:modelValue":l[1]||(l[1]=r=>t(o).createTime=r),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),a(g,null,{default:e(()=>[a(n,{onClick:h},{default:e(()=>[a(w,{class:"mr-5px",icon:"ep:search"}),p(" \u641C\u7D22 ")]),_:1}),a(n,{onClick:D},{default:e(()=>[a(w,{class:"mr-5px",icon:"ep:refresh"}),p(" \u91CD\u7F6E ")]),_:1}),d((u(),f(n,{plain:"",type:"primary",onClick:l[2]||(l[2]=r=>V("create"))},{default:e(()=>[a(w,{class:"mr-5px",icon:"ep:plus"}),p(" \u65B0\u589E ")]),_:1})),[[k,["product:property:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(S,null,{default:e(()=>[d((u(),f(R,{data:t(b)},{default:e(()=>[a(c,{align:"center",label:"\u7F16\u53F7","min-width":"60",prop:"id"}),a(c,{align:"center",label:"\u5C5E\u6027\u540D\u79F0",prop:"name","min-width":"150"}),a(c,{"show-overflow-tooltip":!0,align:"center",label:"\u5907\u6CE8",prop:"remark"}),a(c,{formatter:t(ua),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180"},null,8,["formatter"]),a(c,{align:"center",label:"\u64CD\u4F5C"},{default:e(r=>[d((u(),f(n,{link:"",type:"primary",onClick:T=>V("update",r.row.id)},{default:e(()=>[p(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[k,["product:property:update"]]]),a(n,{link:"",type:"primary",onClick:T=>{return m=r.row.id,void U({name:"ProductPropertyValue",params:{propertyId:m}});var m}},{default:e(()=>[p("\u5C5E\u6027\u503C")]),_:2},1032,["onClick"]),d((u(),f(n,{link:"",type:"danger",onClick:T=>(async m=>{try{await v.delConfirm(),await fa(m),v.success(Y("common.delSuccess")),await s()}catch{}})(r.row.id)},{default:e(()=>[p(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[k,["product:property:delete"]]])]),_:1})]),_:1},8,["data"])),[[j,t(y)]]),a(I,{limit:t(o).pageSize,"onUpdate:limit":l[3]||(l[3]=r=>t(o).pageSize=r),page:t(o).pageNo,"onUpdate:page":l[4]||(l[4]=r=>t(o).pageNo=r),total:t(C),onPagination:s},null,8,["limit","page","total"])]),_:1}),a(ha,{ref_key:"formRef",ref:P,onSuccess:s},null,512)],64)}}})});export{Ca as __tla,N as default};
