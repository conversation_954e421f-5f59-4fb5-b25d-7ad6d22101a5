import{_ as V,__tla as q}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{i as M,a as T,b as l,c as K,d as z,f as H,e as J,g as Q,P as L,_ as W,h as Y,__tla as Z}from"./bpmn-embedded-D6vUWKn8.js";import{g as tt,u as et,c as nt,__tla as at}from"./index-DzsWMVUv.js";import{d as ot,u as it,S as rt,r as N,C as ct,o as j,l as G,w as st,a as f,ao as pt,y as lt,a9 as mt,i as dt,I as ut,__tla as _t}from"./index-BUSn51wb.js";import{__tla as bt}from"./el-card-CJbXGyyg.js";import{__tla as vt}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as ht}from"./XTextButton-DMuYh5Ak.js";import{__tla as ft}from"./XButton-BjahQbul.js";import{__tla as gt}from"./el-collapse-item-B_QvnH_b.js";import{__tla as yt}from"./index-COJ8hy-t.js";import{__tla as Et}from"./el-tree-select-CBuha0HW.js";import"./tree-BMa075Oj.js";import{__tla as wt}from"./index-CCFX7HyJ.js";import{__tla as xt}from"./index-Bqt292RI.js";import{__tla as kt}from"./index-D6tFY92u.js";import{__tla as Ct}from"./index-BYXzDB8j.js";import{__tla as Pt}from"./index-xiOMzVtR.js";import{__tla as Dt}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{__tla as Tt}from"./index-Cch5e1V0.js";import"./constants-A8BI3pz7.js";import{__tla as Nt}from"./index-BEeS1wHc.js";import{__tla as St}from"./el-drawer-DMK0hKF6.js";import{__tla as Mt}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import"./color-BN7ZL7BD.js";import{__tla as At}from"./index-CRkUQbt2.js";import{__tla as It}from"./formatTime-DWdBpgsM.js";let O,Bt=Promise.all([(()=>{try{return q}catch{}})(),(()=>{try{return Z}catch{}})(),(()=>{try{return at}catch{}})(),(()=>{try{return _t}catch{}})(),(()=>{try{return bt}catch{}})(),(()=>{try{return vt}catch{}})(),(()=>{try{return ht}catch{}})(),(()=>{try{return ft}catch{}})(),(()=>{try{return gt}catch{}})(),(()=>{try{return yt}catch{}})(),(()=>{try{return Et}catch{}})(),(()=>{try{return wt}catch{}})(),(()=>{try{return xt}catch{}})(),(()=>{try{return kt}catch{}})(),(()=>{try{return Ct}catch{}})(),(()=>{try{return Pt}catch{}})(),(()=>{try{return Dt}catch{}})(),(()=>{try{return Tt}catch{}})(),(()=>{try{return Nt}catch{}})(),(()=>{try{return St}catch{}})(),(()=>{try{return Mt}catch{}})(),(()=>{try{return At}catch{}})(),(()=>{try{return It}catch{}})()]).then(async()=>{function A(n){return n.originalEvent||n.srcEvent}function $(n){return function(i,r){return(A(i)||i).button===r}(n,0)}function X(n){var i=A(n)||n;return!!$(n)&&(/mac/i.test(navigator.platform)?i.metaKey:i.ctrlKey)}function S(n,i,r,s,p,v,u,o,_,g,a,t){n=n||{},s.registerProvider(this),this._contextPad=s,this._modeling=p,this._elementFactory=v,this._connect=u,this._create=o,this._popupMenu=_,this._canvas=g,this._rules=a,this._translate=t,n.autoPlace!==!1&&(this._autoPlace=i.get("autoPlace",!1)),r.on("create.end",250,function(e){const m=e.context.shape;if(!X(e)||!s.isOpen(m))return;const c=s.getEntries(m);c.replace&&c.replace.action.click(e,m)})}function I(n,i,r){const s=n.$instanceOf(i);let p=!1;const v=n.eventDefinitions||[];return H(v,function(u){u.$type===r&&(p=!0)}),s&&p}S.$inject=["config.contextPad","injector","eventBus","contextPad","modeling","elementFactory","connect","create","popupMenu","canvas","rules","translate","elementRegistry"],S.prototype.getContextPadEntries=function(n){const i=this._contextPad,r=this._modeling,s=this._elementFactory,p=this._connect,v=this._create,u=this._popupMenu,o=this._canvas,_=this._rules,g=this._autoPlace,a=this._translate,t={};if(n.type==="label")return t;const e=n.businessObject;function m(b,h){p.start(b,h)}function c(b,h,d,x){function D(k,E){const C=s.createShape(l({type:b},x));v.start(k,C,{source:E})}return typeof d!="string"&&(x=d,d=a("Append {type}",{type:b.replace(/^bpmn:/,"")})),{group:"model",className:h,title:d,action:{dragstart:D,click:g?function(k,E){const C=s.createShape(l({type:b},x));g.append(E,C)}:D}}}function P(b){return function(h,d){r.splitLane(d,b),i.open(d,!0)}}if(M(e,["bpmn:Lane","bpmn:Participant"])&&J(e)){const b=Q(n);l(t,{"lane-insert-above":{group:"lane-insert-above",className:"bpmn-icon-lane-insert-above",title:a("Add Lane above"),action:{click:function(h,d){r.addLane(d,"top")}}}}),b.length<2&&(n.height>=120&&l(t,{"lane-divide-two":{group:"lane-divide",className:"bpmn-icon-lane-divide-two",title:a("Divide into two Lanes"),action:{click:P(2)}}}),n.height>=180&&l(t,{"lane-divide-three":{group:"lane-divide",className:"bpmn-icon-lane-divide-three",title:a("Divide into three Lanes"),action:{click:P(3)}}})),l(t,{"lane-insert-below":{group:"lane-insert-below",className:"bpmn-icon-lane-insert-below",title:a("Add Lane below"),action:{click:function(h,d){r.addLane(d,"bottom")}}}})}T(e,"bpmn:FlowNode")&&(T(e,"bpmn:EventBasedGateway")?l(t,{"append.receive-task":c("bpmn:ReceiveTask","bpmn-icon-receive-task",a("Append ReceiveTask")),"append.message-intermediate-event":c("bpmn:IntermediateCatchEvent","bpmn-icon-intermediate-event-catch-message",a("Append MessageIntermediateCatchEvent"),{eventDefinitionType:"bpmn:MessageEventDefinition"}),"append.timer-intermediate-event":c("bpmn:IntermediateCatchEvent","bpmn-icon-intermediate-event-catch-timer",a("Append TimerIntermediateCatchEvent"),{eventDefinitionType:"bpmn:TimerEventDefinition"}),"append.condition-intermediate-event":c("bpmn:IntermediateCatchEvent","bpmn-icon-intermediate-event-catch-condition",a("Append ConditionIntermediateCatchEvent"),{eventDefinitionType:"bpmn:ConditionalEventDefinition"}),"append.signal-intermediate-event":c("bpmn:IntermediateCatchEvent","bpmn-icon-intermediate-event-catch-signal",a("Append SignalIntermediateCatchEvent"),{eventDefinitionType:"bpmn:SignalEventDefinition"})}):I(e,"bpmn:BoundaryEvent","bpmn:CompensateEventDefinition")?l(t,{"append.compensation-activity":c("bpmn:Task","bpmn-icon-task",a("Append compensation activity"),{isForCompensation:!0})}):T(e,"bpmn:EndEvent")||e.isForCompensation||I(e,"bpmn:IntermediateThrowEvent","bpmn:LinkEventDefinition")||K(e)||l(t,{"append.end-event":c("bpmn:EndEvent","bpmn-icon-end-event-none",a("Append EndEvent")),"append.gateway":c("bpmn:ExclusiveGateway","bpmn-icon-gateway-none",a("Append Gateway")),"append.append-task":c("bpmn:UserTask","bpmn-icon-user-task",a("Append Task")),"append.intermediate-event":c("bpmn:IntermediateThrowEvent","bpmn-icon-intermediate-event-none",a("Append Intermediate/Boundary Event"))})),u.isEmpty(n,"bpmn-replace")||l(t,{replace:{group:"edit",className:"bpmn-icon-screw-wrench",title:"\u4FEE\u6539\u7C7B\u578B",action:{click:function(b,h){const d=l(function(x){const D=o.getContainer(),F=i.getPad(x).html,k=D.getBoundingClientRect(),E=F.getBoundingClientRect(),C=E.top-k.top;return{x:E.left-k.left,y:C+E.height+5}}(h),{cursor:{x:b.x,y:b.y}});u.open(h,"bpmn-replace",d)}}}}),M(e,["bpmn:FlowNode","bpmn:InteractionNode","bpmn:DataObjectReference","bpmn:DataStoreReference"])&&l(t,{"append.text-annotation":c("bpmn:TextAnnotation","bpmn-icon-text-annotation"),connect:{group:"connect",className:"bpmn-icon-connection-multi",title:a("Connect using "+(e.isForCompensation?"":"Sequence/MessageFlow or ")+"Association"),action:{click:m,dragstart:m}}}),M(e,["bpmn:DataObjectReference","bpmn:DataStoreReference"])&&l(t,{connect:{group:"connect",className:"bpmn-icon-connection-multi",title:a("Connect using DataInputAssociation"),action:{click:m,dragstart:m}}}),T(e,"bpmn:Group")&&l(t,{"append.text-annotation":c("bpmn:TextAnnotation","bpmn-icon-text-annotation")});let y=_.allowed("elements.delete",{elements:[n]});return z(y)&&(y=y[0]===n),y&&l(t,{delete:{group:"edit",className:"bpmn-icon-trash",title:a("Remove"),action:{click:function(){r.removeElements([n])}}}}),t};const U={__init__:["contextPadProvider"],contextPadProvider:["type",S]};function w(n,i,r,s,p,v,u,o){L.call(this,n,i,r,s,p,v,u,o,2e3)}const B=function(){};(B.prototype=L.prototype).getPaletteEntries=function(){const n={},i=this._create,r=this._elementFactory,s=this._spaceTool,p=this._lassoTool,v=this._handTool,u=this._globalConnect,o=this._translate;function _(t,e,m,c,P){function y(h){const d=r.createShape(l({type:t},P));i.start(h,d)}const b=t.replace(/^bpmn:/,"");return{group:e,className:m,title:c||o("Create {type}",{type:b}),action:{dragstart:y,click:y}}}function g(t){const e=r.createShape({type:"bpmn:SubProcess",x:0,y:0,isExpanded:!0}),m=r.createShape({type:"bpmn:StartEvent",x:40,y:82,parent:e});i.start(t,[e,m],{hints:{autoSelect:[m]}})}function a(t){i.start(t,r.createParticipantShape())}return l(n,{"hand-tool":{group:"tools",className:"bpmn-icon-hand-tool",title:"\u6FC0\u6D3B\u6293\u624B\u5DE5\u5177",action:{click:function(t){v.activateHand(t)}}},"lasso-tool":{group:"tools",className:"bpmn-icon-lasso-tool",title:o("Activate the lasso tool"),action:{click:function(t){p.activateSelection(t)}}},"space-tool":{group:"tools",className:"bpmn-icon-space-tool",title:o("Activate the create/remove space tool"),action:{click:function(t){s.activateSelection(t)}}},"global-connect-tool":{group:"tools",className:"bpmn-icon-connection-multi",title:o("Activate the global connect tool"),action:{click:function(t){u.toggle(t)}}},"tool-separator":{group:"tools",separator:!0},"create.start-event":_("bpmn:StartEvent","event","bpmn-icon-start-event-none",o("Create StartEvent")),"create.intermediate-event":_("bpmn:IntermediateThrowEvent","event","bpmn-icon-intermediate-event-none",o("Create Intermediate/Boundary Event")),"create.end-event":_("bpmn:EndEvent","event","bpmn-icon-end-event-none",o("Create EndEvent")),"create.exclusive-gateway":_("bpmn:ExclusiveGateway","gateway","bpmn-icon-gateway-none",o("Create Gateway")),"create.user-task":_("bpmn:UserTask","activity","bpmn-icon-user-task",o("Create User Task")),"create.data-object":_("bpmn:DataObjectReference","data-object","bpmn-icon-data-object",o("Create DataObjectReference")),"create.data-store":_("bpmn:DataStoreReference","data-store","bpmn-icon-data-store",o("Create DataStoreReference")),"create.subprocess-expanded":{group:"activity",className:"bpmn-icon-subprocess-expanded",title:o("Create expanded SubProcess"),action:{dragstart:g,click:g}},"create.participant-expanded":{group:"collaboration",className:"bpmn-icon-participant",title:o("Create Pool/Participant"),action:{dragstart:a,click:a}},"create.group":_("bpmn:Group","artifact","bpmn-icon-group",o("Create Group"))}),n},w.$inject=["palette","create","elementFactory","spaceTool","lassoTool","handTool","globalConnect","translate"],w.prototype=new B,w.prototype.constructor=w;let R;R={__init__:["paletteProvider"],paletteProvider:["type",w]},O=ot({name:"BpmModelEditor",__name:"index",setup(n){const i=it(),{query:r}=rt(),s=ut(),p=N(void 0),v=N(null),u=N({simulation:!0,labelEditing:!1,labelVisible:!1,prefix:"flowable",headerButtonSize:"mini",additionalModel:[U,R]}),o=N(),_=t=>{setTimeout(()=>{v.value=t},10)},g=async t=>{const e={...o.value,bpmnXml:t};e.id?(await et(e),s.success("\u4FEE\u6539\u6210\u529F")):(await nt(e),s.success("\u65B0\u589E\u6210\u529F")),a()},a=()=>{i.push({path:"/bpm/manager/model"})};return ct(async()=>{const t=r.modelId;if(!t)return void s.error("\u7F3A\u5C11\u6A21\u578B modelId \u7F16\u53F7");const e=await tt(t);e.bpmnXml||(e.bpmnXml=` <?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://www.activiti.org/processdef">
  <process id="${e.key}" name="${e.name}" isExecutable="true" />
  <bpmndi:BPMNDiagram id="BPMNDiagram">
    <bpmndi:BPMNPlane id="${e.key}_di" bpmnElement="${e.key}" />
  </bpmndi:BPMNDiagram>
</definitions>`),o.value={...e,bpmnXml:void 0},p.value=e.bpmnXml}),(t,e)=>{const m=V;return j(),G(m,null,{default:st(()=>[f(p)!==void 0?(j(),G(f(W),pt({key:"designer",modelValue:f(p),"onUpdate:modelValue":e[0]||(e[0]=c=>lt(p)?p.value=c:null),value:f(p)},f(u),{keyboard:"",ref:"processDesigner",onInitFinished:_,additionalModel:f(u).additionalModel,onSave:g}),null,16,["modelValue","value","additionalModel"])):mt("",!0),dt(f(Y),{key:"penal",bpmnModeler:f(v),prefix:f(u).prefix,class:"process-panel",model:f(o)},null,8,["bpmnModeler","prefix","model"])]),_:1})}}})});export{Bt as __tla,O as default};
