import{d as z,n as A,I as G,r as n,f as J,o as r,l as o,w as a,i as t,a as l,j as c,H as M,t as g,a9 as F,y as T,Z as Y,L as W,eh as X,P as ee,cc as ae,N as te,Q as le,O as se,R as ue,__tla as de}from"./index-BUSn51wb.js";import{_ as re,__tla as oe}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{E as ne,__tla as ce}from"./el-text-CIwNlU-U.js";import{_ as ie,__tla as me}from"./Tooltip.vue_vue_type_script_setup_true_lang-CBw08m0_.js";import{D as _e,b as pe,c as fe,u as ye,__tla as ve}from"./index-HLeyY-fc.js";import{d as he,h as ke}from"./tree-BMa075Oj.js";import{g as we,__tla as be}from"./index-Bqt292RI.js";let N,ge=Promise.all([(()=>{try{return de}catch{}})(),(()=>{try{return oe}catch{}})(),(()=>{try{return ce}catch{}})(),(()=>{try{return me}catch{}})(),(()=>{try{return ve}catch{}})(),(()=>{try{return be}catch{}})()]).then(async()=>{N=z({__name:"BusinessStatusForm",emits:["success"],setup(Ve,{expose:$,emit:q}){const{t:h}=A(),V=G(),i=n(!1),C=n(""),m=n(!1),x=n(""),s=n({id:void 0,name:"",deptIds:[],statuses:[]}),B=J({name:[{required:!0,message:"\u72B6\u6001\u7EC4\u540D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),k=n(),S=n([]),p=n(),I=n(!0);$({open:async(d,u)=>{if(i.value=!0,C.value=h("action."+d),x.value=d,P(),u){m.value=!0;try{s.value=await pe(u),p.value.setCheckedKeys(s.value.deptIds),s.value.statuses.length==0&&w()}finally{m.value=!1}}else w();S.value=ke(await we())}});const D=q,K=async()=>{await k.value.validate(),m.value=!0;try{const d=s.value;d.deptIds=p.value.getCheckedKeys(!1),x.value==="create"?(await fe(d),V.success(h("common.createSuccess"))):(await ye(d),V.success(h("common.updateSuccess"))),i.value=!1,D("success")}finally{m.value=!1}},P=()=>{var d,u;I.value=!0,s.value={id:void 0,name:"",deptIds:[],statuses:[]},(d=p.value)==null||d.setCheckedNodes([]),(u=k.value)==null||u.resetFields()},w=()=>{s.value.statuses.push({name:"",percent:void 0})};return(d,u)=>{const U=Y,b=W,j=ie,E=X,f=ne,y=ee,H=ae,v=te,L=le,O=se,Q=re,Z=ue;return r(),o(Q,{title:l(C),modelValue:l(i),"onUpdate:modelValue":u[2]||(u[2]=e=>T(i)?i.value=e:null)},{footer:a(()=>[t(v,{onClick:K,type:"primary",disabled:l(m)},{default:a(()=>[c("\u786E \u5B9A")]),_:1},8,["disabled"]),t(v,{onClick:u[1]||(u[1]=e=>i.value=!1)},{default:a(()=>[c("\u53D6 \u6D88")]),_:1})]),default:a(()=>[M((r(),o(O,{ref_key:"formRef",ref:k,model:l(s),rules:l(B),"label-width":"100px"},{default:a(()=>[t(b,{label:"\u72B6\u6001\u7EC4\u540D",prop:"name"},{default:a(()=>[t(U,{modelValue:l(s).name,"onUpdate:modelValue":u[0]||(u[0]=e=>l(s).name=e),placeholder:"\u8BF7\u8F93\u5165\u72B6\u6001\u7EC4\u540D"},null,8,["modelValue"])]),_:1}),t(b,{label:"\u5E94\u7528\u90E8\u95E8",prop:"deptIds"},{label:a(()=>[t(j,{message:"\u4E0D\u9009\u62E9\u90E8\u95E8\u65F6\uFF0C\u9ED8\u8BA4\u5168\u516C\u53F8\u751F\u6548",title:"\u5E94\u7528\u90E8\u95E8"})]),default:a(()=>[t(E,{ref_key:"treeRef",ref:p,data:l(S),props:l(he),"check-strictly":!l(I),"node-key":"id",placeholder:"\u8BF7\u9009\u62E9\u5F52\u5C5E\u90E8\u95E8","show-checkbox":""},null,8,["data","props","check-strictly"])]),_:1}),t(b,{label:"\u9636\u6BB5\u8BBE\u7F6E",prop:"statuses"},{default:a(()=>[t(L,{border:"",style:{width:"100%"},data:l(s).statuses.concat(_e)},{default:a(()=>[t(y,{align:"center",label:"\u9636\u6BB5",width:"70"},{default:a(e=>[e.row.defaultStatus?(r(),o(f,{key:1},{default:a(()=>[c("\u7ED3\u675F")]),_:1})):(r(),o(f,{key:0},{default:a(()=>[c("\u9636\u6BB5 "+g(e.$index+1),1)]),_:2},1024))]),_:1}),t(y,{align:"center",label:"\u9636\u6BB5\u540D\u79F0",width:"160",prop:"name"},{default:a(({row:e})=>[e.endStatus?(r(),o(f,{key:1},{default:a(()=>[c(g(e.name),1)]),_:2},1024)):(r(),o(U,{key:0,modelValue:e.name,"onUpdate:modelValue":_=>e.name=_,placeholder:"\u8BF7\u8F93\u5165\u72B6\u6001\u540D\u79F0"},null,8,["modelValue","onUpdate:modelValue"]))]),_:1}),t(y,{width:"140",align:"center",label:"\u8D62\u5355\u7387\uFF08%\uFF09",prop:"percent"},{default:a(({row:e})=>[e.endStatus?(r(),o(f,{key:1},{default:a(()=>[c(g(e.percent),1)]),_:2},1024)):(r(),o(H,{key:0,modelValue:e.percent,"onUpdate:modelValue":_=>e.percent=_,placeholder:"\u8BF7\u8F93\u5165\u8D62\u5355\u7387","controls-position":"right",min:0,max:100,precision:2,class:"!w-1/1"},null,8,["modelValue","onUpdate:modelValue"]))]),_:1}),t(y,{label:"\u64CD\u4F5C",width:"110",align:"center"},{default:a(e=>[e.row.endStatus?F("",!0):(r(),o(v,{key:0,link:"",type:"primary",onClick:_=>w(e.$index)},{default:a(()=>[c(" \u6DFB\u52A0 ")]),_:2},1032,["onClick"])),e.row.endStatus?F("",!0):(r(),o(v,{key:1,link:"",type:"danger",onClick:_=>{return R=e.$index,void s.value.statuses.splice(R,1);var R},disabled:l(s).statuses.length<=1},{default:a(()=>[c(" \u5220\u9664 ")]),_:2},1032,["onClick","disabled"]))]),_:1})]),_:1},8,["data"])]),_:1})]),_:1},8,["model","rules"])),[[Z,l(m)]])]),_:1},8,["title","modelValue"])}}})});export{N as _,ge as __tla};
