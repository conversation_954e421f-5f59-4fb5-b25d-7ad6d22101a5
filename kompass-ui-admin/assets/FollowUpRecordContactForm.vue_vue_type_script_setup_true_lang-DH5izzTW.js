import{d as g,r as w,at as y,o as h,l as v,w as l,i as a,j as s,t as b,a as k,G as C,v as N,P as x,N as F,Q as R,__tla as A}from"./index-BUSn51wb.js";import{_ as G,__tla as I}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";let i,O=Promise.all([(()=>{try{return A}catch{}})(),(()=>{try{return I}catch{}})()]).then(async()=>{i=g({__name:"FollowUpRecordContactForm",props:{contacts:{}},setup(p){const c=p,n=w([]);return y(()=>c.contacts,async r=>{n.value=r},{immediate:!0}),(r,P)=>{const _=N,t=x,d=G,u=F,m=R;return h(),v(m,{data:r.contacts,"show-overflow-tooltip":!0,stripe:!0,height:"150"},{default:l(()=>[a(t,{label:"\u59D3\u540D",fixed:"left",align:"center",prop:"name"},{default:l(e=>[a(_,{type:"primary",underline:!1,onClick:f=>r.openDetail(e.row.id)},{default:l(()=>[s(b(e.row.name),1)]),_:2},1032,["onClick"])]),_:1}),a(t,{label:"\u624B\u673A\u53F7",align:"center",prop:"mobile"}),a(t,{label:"\u804C\u4F4D",align:"center",prop:"post"}),a(t,{label:"\u76F4\u5C5E\u4E0A\u7EA7",align:"center",prop:"parentName"}),a(t,{label:"\u662F\u5426\u5173\u952E\u51B3\u7B56\u4EBA",align:"center",prop:"master","min-width":"100"},{default:l(e=>[a(d,{type:k(C).INFRA_BOOLEAN_STRING,value:e.row.master},null,8,["type","value"])]),_:1}),a(t,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"130"},{default:l(e=>[a(u,{link:"",type:"danger",onClick:f=>{return o=e.row.id,void n.value.splice(o,1);var o}},{default:l(()=>[s(" \u79FB\u9664")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])}}})});export{i as _,O as __tla};
