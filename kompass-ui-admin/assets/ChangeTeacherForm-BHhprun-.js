import{_ as t,__tla as _}from"./ChangeTeacherForm.vue_vue_type_script_setup_true_lang-z5Rcubd_.js";import{__tla as r}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as a}from"./index-BUSn51wb.js";import{__tla as l}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{__tla as o}from"./el-card-CJbXGyyg.js";import{__tla as c}from"./index-T-3poKZQ.js";let m=Promise.all([(()=>{try{return _}catch{}})(),(()=>{try{return r}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return c}catch{}})()]).then(async()=>{});export{m as __tla,t as default};
