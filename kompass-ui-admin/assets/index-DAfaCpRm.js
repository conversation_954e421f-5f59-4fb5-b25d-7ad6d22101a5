import{d as na,n as sa,I as ia,u as ca,r as b,f as _a,C as ua,T as pa,o as s,c as L,i as a,w as e,a as l,U as B,F as E,k as da,V as ma,G as K,l as i,j as c,H as d,t as fa,a9 as V,Z as ya,L as ha,J as ba,K as ga,_ as wa,N as ka,O as va,P as ja,Q as xa,R as Ca,__tla as Na}from"./index-BUSn51wb.js";import{_ as Sa,__tla as Ra}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{E as Va,a as Oa,b as Pa,__tla as Ta}from"./el-dropdown-item-CIJXMVYa.js";import{_ as Ua,__tla as qa}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as Ja,__tla as Aa}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as Fa,__tla as za}from"./index-COobLwz-.js";import{c as O,__tla as Ia}from"./permission-DQXm2BCV.js";import{_ as La,__tla as Ba}from"./JobForm.vue_vue_type_script_setup_true_lang-Xq2tLv6G.js";import{_ as Ea,__tla as Ka}from"./JobDetail.vue_vue_type_script_setup_true_lang-CrMZrHkP.js";import{d as Da}from"./download-e0EdwhTv.js";import{b as Ga,e as Qa,d as Ha,f as Ma,r as Xa,__tla as Ya}from"./index-DUdkpwVb.js";import{c as w}from"./constants-A8BI3pz7.js";import{__tla as Za}from"./index-Cch5e1V0.js";import"./color-BN7ZL7BD.js";import{__tla as Wa}from"./el-card-CJbXGyyg.js";import{__tla as $a}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as ae}from"./el-descriptions-item-dD3qa0ub.js";import{__tla as ee}from"./el-timeline-item-D8aDRTsd.js";import{__tla as te}from"./formatTime-DWdBpgsM.js";let D,le=Promise.all([(()=>{try{return Na}catch{}})(),(()=>{try{return Ra}catch{}})(),(()=>{try{return Ta}catch{}})(),(()=>{try{return qa}catch{}})(),(()=>{try{return Aa}catch{}})(),(()=>{try{return za}catch{}})(),(()=>{try{return Ia}catch{}})(),(()=>{try{return Ba}catch{}})(),(()=>{try{return Ka}catch{}})(),(()=>{try{return Ya}catch{}})(),(()=>{try{return Za}catch{}})(),(()=>{try{return Wa}catch{}})(),(()=>{try{return $a}catch{}})(),(()=>{try{return ae}catch{}})(),(()=>{try{return ee}catch{}})(),(()=>{try{return te}catch{}})()]).then(async()=>{D=na({name:"InfraJob",__name:"index",setup(re){const{t:x}=sa(),m=ia(),{push:G}=ca(),C=b(!0),P=b(0),T=b([]),n=_a({pageNo:1,pageSize:10,name:void 0,status:void 0,handlerName:void 0}),U=b(),N=b(!1),f=async()=>{C.value=!0;try{const r=await Ga(n);T.value=r.list,P.value=r.total}finally{C.value=!1}},k=()=>{n.pageNo=1,f()},Q=()=>{U.value.resetFields(),k()},H=async()=>{try{await m.exportConfirm(),N.value=!0;const r=await Qa(n);Da.excel(r,"\u5B9A\u65F6\u4EFB\u52A1.xls")}catch{}finally{N.value=!1}},q=b(),J=(r,o)=>{q.value.open(r,o)},M=async r=>{try{await m.confirm("\u786E\u8BA4\u8981\u7ACB\u5373\u6267\u884C\u4E00\u6B21"+r.name+"?",x("common.reminder")),await Xa(r.id),m.success("\u6267\u884C\u6210\u529F"),await f()}catch{}},A=b(),X=r=>{A.value.open(r)},F=r=>{G(r&&r>0?"/job/job-log?id="+r:"/job/job-log")};return ua(()=>{f()}),(r,o)=>{const S=Fa,z=ya,v=ha,Y=ba,Z=ga,g=wa,_=ka,W=va,I=Ja,y=ja,$=Ua,R=Va,aa=Oa,ea=Pa,ta=xa,la=Sa,h=pa("hasPermi"),ra=Ca;return s(),L(E,null,[a(S,{title:"\u5B9A\u65F6\u4EFB\u52A1",url:"https://doc.iocoder.cn/job/"}),a(S,{title:"\u5F02\u6B65\u4EFB\u52A1",url:"https://doc.iocoder.cn/async-task/"}),a(S,{title:"\u6D88\u606F\u961F\u5217",url:"https://doc.iocoder.cn/message-queue/"}),a(I,null,{default:e(()=>[a(W,{class:"-mb-15px",model:l(n),ref_key:"queryFormRef",ref:U,inline:!0,"label-width":"100px"},{default:e(()=>[a(v,{label:"\u4EFB\u52A1\u540D\u79F0",prop:"name"},{default:e(()=>[a(z,{modelValue:l(n).name,"onUpdate:modelValue":o[0]||(o[0]=t=>l(n).name=t),placeholder:"\u8BF7\u8F93\u5165\u4EFB\u52A1\u540D\u79F0",clearable:"",onKeyup:B(k,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(v,{label:"\u4EFB\u52A1\u72B6\u6001",prop:"status"},{default:e(()=>[a(Z,{modelValue:l(n).status,"onUpdate:modelValue":o[1]||(o[1]=t=>l(n).status=t),placeholder:"\u8BF7\u9009\u62E9\u4EFB\u52A1\u72B6\u6001",clearable:"",class:"!w-240px"},{default:e(()=>[(s(!0),L(E,null,da(l(ma)(l(K).INFRA_JOB_STATUS),t=>(s(),i(Y,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(v,{label:"\u5904\u7406\u5668\u7684\u540D\u5B57",prop:"handlerName"},{default:e(()=>[a(z,{modelValue:l(n).handlerName,"onUpdate:modelValue":o[2]||(o[2]=t=>l(n).handlerName=t),placeholder:"\u8BF7\u8F93\u5165\u5904\u7406\u5668\u7684\u540D\u5B57",clearable:"",onKeyup:B(k,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(v,null,{default:e(()=>[a(_,{onClick:k},{default:e(()=>[a(g,{icon:"ep:search",class:"mr-5px"}),c(" \u641C\u7D22")]),_:1}),a(_,{onClick:Q},{default:e(()=>[a(g,{icon:"ep:refresh",class:"mr-5px"}),c(" \u91CD\u7F6E")]),_:1}),d((s(),i(_,{type:"primary",plain:"",onClick:o[3]||(o[3]=t=>J("create"))},{default:e(()=>[a(g,{icon:"ep:plus",class:"mr-5px"}),c(" \u65B0\u589E ")]),_:1})),[[h,["infra:job:create"]]]),d((s(),i(_,{type:"success",plain:"",onClick:H,loading:l(N)},{default:e(()=>[a(g,{icon:"ep:download",class:"mr-5px"}),c(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[h,["infra:job:export"]]]),d((s(),i(_,{type:"info",plain:"",onClick:o[4]||(o[4]=t=>F())},{default:e(()=>[a(g,{icon:"ep:zoom-in",class:"mr-5px"}),c(" \u6267\u884C\u65E5\u5FD7 ")]),_:1})),[[h,["infra:job:query"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(I,null,{default:e(()=>[d((s(),i(ta,{data:l(T)},{default:e(()=>[a(y,{label:"\u4EFB\u52A1\u7F16\u53F7",align:"center",prop:"id"}),a(y,{label:"\u4EFB\u52A1\u540D\u79F0",align:"center",prop:"name"}),a(y,{label:"\u4EFB\u52A1\u72B6\u6001",align:"center",prop:"status"},{default:e(t=>[a($,{type:l(K).INFRA_JOB_STATUS,value:t.row.status},null,8,["type","value"])]),_:1}),a(y,{label:"\u5904\u7406\u5668\u7684\u540D\u5B57",align:"center",prop:"handlerName"}),a(y,{label:"\u5904\u7406\u5668\u7684\u53C2\u6570",align:"center",prop:"handlerParam"}),a(y,{label:"CRON \u8868\u8FBE\u5F0F",align:"center",prop:"cronExpression"}),a(y,{label:"\u64CD\u4F5C",align:"center",width:"200"},{default:e(t=>[d((s(),i(_,{type:"primary",link:"",onClick:j=>J("update",t.row.id)},{default:e(()=>[c(" \u4FEE\u6539 ")]),_:2},1032,["onClick"])),[[h,["infra:job:update"]]]),d((s(),i(_,{type:"primary",link:"",onClick:j=>(async u=>{try{const p=u.status===w.STOP?"\u5F00\u542F":"\u5173\u95ED";await m.confirm("\u786E\u8BA4\u8981"+p+'\u5B9A\u65F6\u4EFB\u52A1\u7F16\u53F7\u4E3A"'+u.id+'"\u7684\u6570\u636E\u9879?',x("common.reminder"));const oa=u.status===w.STOP?w.NORMAL:w.STOP;await Ha(u.id,oa),m.success(p+"\u6210\u529F"),await f()}catch{}})(t.row)},{default:e(()=>[c(fa(t.row.status===l(w).STOP?"\u5F00\u542F":"\u6682\u505C"),1)]),_:2},1032,["onClick"])),[[h,["infra:job:update"]]]),d((s(),i(_,{type:"danger",link:"",onClick:j=>(async u=>{try{await m.delConfirm(),await Ma(u),m.success(x("common.delSuccess")),await f()}catch{}})(t.row.id)},{default:e(()=>[c(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[h,["infra:job:delete"]]]),d((s(),i(ea,{onCommand:j=>((u,p)=>{switch(u){case"handleRun":M(p);break;case"openDetail":X(p.id);break;case"handleJobLog":F(p==null?void 0:p.id)}})(j,t.row)},{dropdown:e(()=>[a(aa,null,{default:e(()=>[l(O)(["infra:job:trigger"])?(s(),i(R,{key:0,command:"handleRun"},{default:e(()=>[c(" \u6267\u884C\u4E00\u6B21 ")]),_:1})):V("",!0),l(O)(["infra:job:query"])?(s(),i(R,{key:1,command:"openDetail"},{default:e(()=>[c(" \u4EFB\u52A1\u8BE6\u7EC6 ")]),_:1})):V("",!0),l(O)(["infra:job:query"])?(s(),i(R,{key:2,command:"handleJobLog"},{default:e(()=>[c(" \u8C03\u5EA6\u65E5\u5FD7 ")]),_:1})):V("",!0)]),_:1})]),default:e(()=>[a(_,{type:"primary",link:""},{default:e(()=>[a(g,{icon:"ep:d-arrow-right"}),c(" \u66F4\u591A")]),_:1})]),_:2},1032,["onCommand"])),[[h,["infra:job:trigger","infra:job:query"]]])]),_:1})]),_:1},8,["data"])),[[ra,l(C)]]),a(la,{total:l(P),page:l(n).pageNo,"onUpdate:page":o[5]||(o[5]=t=>l(n).pageNo=t),limit:l(n).pageSize,"onUpdate:limit":o[6]||(o[6]=t=>l(n).pageSize=t),onPagination:f},null,8,["total","page","limit"])]),_:1}),a(La,{ref_key:"formRef",ref:q,onSuccess:f},null,512),a(Ea,{ref_key:"detailRef",ref:A},null,512)],64)}}})});export{le as __tla,D as default};
