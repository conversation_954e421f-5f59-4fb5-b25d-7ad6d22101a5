import{_ as t,__tla as _}from"./CustomerForm.vue_vue_type_script_setup_true_lang-C1RWhfNe.js";import{__tla as r}from"./index-BUSn51wb.js";import{__tla as a}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as l}from"./index-D8hnRknQ.js";import{__tla as o}from"./index-BYXzDB8j.js";import{__tla as c}from"./index-CyP7ZSdX.js";let m=Promise.all([(()=>{try{return _}catch{}})(),(()=>{try{return r}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return c}catch{}})()]).then(async()=>{});export{m as __tla,t as default};
