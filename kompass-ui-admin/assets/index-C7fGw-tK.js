import{d as _,b as g,o as a,c as p,F as b,k as f,av as s,i as d,w as R,g as c,a as v,_ as w,__tla as $}from"./index-BUSn51wb.js";import{E as B,__tla as T}from"./el-image-BjHZRFih.js";let h,k=Promise.all([(()=>{try{return $}catch{}})(),(()=>{try{return T}catch{}})()]).then(async()=>{let o,r;o={class:"image-slot"},r=93.75,h=_({name:"MagicCube",__name:"index",props:{property:{}},setup(u){const i=u,y=g(()=>{let t=0;return i.property.list.length>0&&(t=Math.max(...i.property.list.map(l=>l.bottom))),t+1});return(t,l)=>{const x=w,m=B;return a(),p("div",{class:"relative",style:s({height:v(y)*r+"px",width:"375px"})},[(a(!0),p(b,null,f(t.property.list,(e,n)=>(a(),p("div",{key:n,class:"absolute",style:s({width:e.width*r-2*t.property.space+"px",height:e.height*r-2*t.property.space+"px",margin:`${t.property.space}px`,top:e.top*r+"px",left:e.left*r+"px"})},[d(m,{class:"h-full w-full",fit:"cover",src:e.imgUrl,style:s({borderTopLeftRadius:`${t.property.borderRadiusTop}px`,borderTopRightRadius:`${t.property.borderRadiusTop}px`,borderBottomLeftRadius:`${t.property.borderRadiusBottom}px`,borderBottomRightRadius:`${t.property.borderRadiusBottom}px`})},{error:R(()=>[c("div",o,[c("div",{class:"flex items-center justify-center",style:s({width:e.width*r+"px",height:e.height*r+"px"})},[d(x,{icon:"ep-picture",color:"gray",size:r})],4)])]),_:2},1032,["src","style"])],4))),128))],4)}}})});export{k as __tla,h as default};
