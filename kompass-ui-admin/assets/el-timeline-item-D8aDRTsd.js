import{d as m,bf as u,c8 as w,h as T,aV as c,bd as S,bo as $,b as B,o as l,c as o,g as r,a0 as s,a,av as E,l as f,w as x,b0 as z,br as I,a9 as n,t as y,bg as C,bh as P,bk as V,__tla as j}from"./index-BUSn51wb.js";let v,b,q=Promise.all([(()=>{try{return j}catch{}})()]).then(async()=>{const g=m({name:"ElTimeline",setup(d,{slots:i}){const e=u("timeline");return w("timeline",i),()=>T("ul",{class:[e.b()]},[c(i,"default")])}}),h=S({timestamp:{type:String,default:""},hideTimestamp:{type:Boolean,default:!1},center:{type:Boolean,default:!1},placement:{type:String,values:["top","bottom"],default:"bottom"},type:{type:String,values:["primary","success","warning","danger","info"],default:""},color:{type:String,default:""},size:{type:String,values:["normal","large"],default:"normal"},icon:{type:$},hollow:{type:Boolean,default:!1}}),_=m({name:"ElTimelineItem"});var p=C(m({..._,props:h,setup(d){const i=d,e=u("timeline-item"),k=B(()=>[e.e("node"),e.em("node",i.size||""),e.em("node",i.type||""),e.is("hollow",i.hollow)]);return(t,A)=>(l(),o("li",{class:s([a(e).b(),{[a(e).e("center")]:t.center}])},[r("div",{class:s(a(e).e("tail"))},null,2),t.$slots.dot?n("v-if",!0):(l(),o("div",{key:0,class:s(a(k)),style:E({backgroundColor:t.color})},[t.icon?(l(),f(a(I),{key:0,class:s(a(e).e("icon"))},{default:x(()=>[(l(),f(z(t.icon)))]),_:1},8,["class"])):n("v-if",!0)],6)),t.$slots.dot?(l(),o("div",{key:1,class:s(a(e).e("dot"))},[c(t.$slots,"dot")],2)):n("v-if",!0),r("div",{class:s(a(e).e("wrapper"))},[t.hideTimestamp||t.placement!=="top"?n("v-if",!0):(l(),o("div",{key:0,class:s([a(e).e("timestamp"),a(e).is("top")])},y(t.timestamp),3)),r("div",{class:s(a(e).e("content"))},[c(t.$slots,"default")],2),t.hideTimestamp||t.placement!=="bottom"?n("v-if",!0):(l(),o("div",{key:1,class:s([a(e).e("timestamp"),a(e).is("bottom")])},y(t.timestamp),3))],2)],2))}}),[["__file","timeline-item.vue"]]);b=P(g,{TimelineItem:p}),v=V(p)});export{v as E,q as __tla,b as a};
