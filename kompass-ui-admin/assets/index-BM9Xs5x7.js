import{d as u,o as t,c as l,F as y,k as d,g as s,l as v,a9 as g,av as p,t as i,i as h,_ as w,B as b,__tla as k}from"./index-BUSn51wb.js";import{E as j,__tla as C}from"./el-image-BjHZRFih.js";let x,U=Promise.all([(()=>{try{return k}catch{}})(),(()=>{try{return C}catch{}})()]).then(async()=>{let a,r,o,c;a={class:"min-h-42px flex flex-col"},r={class:"flex flex-1 flex-row items-center gap-8px"},o={class:"item-center flex flex-row justify-center gap-4px"},c=u({name:"MenuList",__name:"index",props:{property:{}},setup:z=>(n,B)=>{const f=j,_=w;return t(),l("div",a,[(t(!0),l(y,null,d(n.property.list,(e,m)=>(t(),l("div",{key:m,class:"item h-42px flex flex-row items-center justify-between gap-4px p-x-12px"},[s("div",r,[e.iconUrl?(t(),v(f,{key:0,class:"h-16px w-16px",src:e.iconUrl},null,8,["src"])):g("",!0),s("span",{class:"text-16px",style:p({color:e.titleColor})},i(e.title),5)]),s("div",o,[s("span",{class:"text-12px",style:p({color:e.subtitleColor})},i(e.subtitle),5),h(_,{icon:"ep-arrow-right",color:"#000",size:16})])]))),128))])}}),x=b(c,[["__scopeId","data-v-c9f14f99"]])});export{U as __tla,x as default};
