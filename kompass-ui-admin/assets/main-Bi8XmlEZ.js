import{d as o,o as p,c as m,i as v,w as g,g as t,t as u,v as y,B as h,__tla as f}from"./index-BUSn51wb.js";let c,U=Promise.all([(()=>{try{return f}catch{}})()]).then(async()=>{let a,e,s,i,l,d;a={class:"avue-card__body",style:{padding:"10px","background-color":"#fff","border-radius":"5px"}},e={class:"avue-card__avatar"},s=["src"],i={class:"avue-card__detail"},l={class:"avue-card__title",style:{"margin-bottom":"unset"}},d={class:"avue-card__info",style:{height:"unset"}},c=h(o({name:"WxMusic",__name:"main",props:{title:{required:!1,type:String},description:{required:!1,type:String},musicUrl:{required:!1,type:String},hqMusicUrl:{required:!1,type:String},thumbMediaUrl:{required:!0,type:String}},setup:(r,{expose:n})=>(n({musicUrl:r.musicUrl}),(b,q)=>{const _=y;return p(),m("div",null,[v(_,{type:"success",underline:!1,target:"_blank",href:r.hqMusicUrl?r.hqMusicUrl:r.musicUrl},{default:g(()=>[t("div",a,[t("div",e,[t("img",{src:r.thumbMediaUrl,alt:""},null,8,s)]),t("div",i,[t("div",l,u(r.title),1),t("div",d,u(r.description),1)])])]),_:1},8,["href"])])})}),[["__scopeId","data-v-2209b357"]])});export{U as __tla,c as default};
