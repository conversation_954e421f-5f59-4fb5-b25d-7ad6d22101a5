import{d as te,n as re,I as ue,r as n,f as oe,b as de,at as se,dW as ie,o as c,c as I,i as e,w as r,a as t,l as _,j as U,a9 as ne,H as ce,F as w,k as E,y as O,dX as k,Z as me,L as pe,E as _e,M as fe,_ as ve,N as Ve,J as be,K as he,cn as Pe,s as ye,z as Ie,A as Ue,cc as we,O as ke,R as ge,__tla as Re}from"./index-BUSn51wb.js";import{_ as Se,__tla as xe}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{_ as Ne,__tla as Te}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{P as g,__tla as Ce}from"./index-fx39jzH8.js";import{_ as Fe,__tla as Le}from"./PurchaseReturnItemForm.vue_vue_type_script_setup_true_lang-AYDAk4sD.js";import{S as Ae,__tla as Ee}from"./index-CncHngEK.js";import{A as Oe,__tla as qe}from"./index-LbO7ASKC.js";import{_ as ze,__tla as He}from"./PurchaseOrderReturnEnableList.vue_vue_type_script_setup_true_lang-C6VDRUvA.js";import{g as Je,__tla as Me}from"./index-BYXzDB8j.js";let q,We=Promise.all([(()=>{try{return Re}catch{}})(),(()=>{try{return xe}catch{}})(),(()=>{try{return Te}catch{}})(),(()=>{try{return Ce}catch{}})(),(()=>{try{return Le}catch{}})(),(()=>{try{return Ee}catch{}})(),(()=>{try{return qe}catch{}})(),(()=>{try{return He}catch{}})(),(()=>{try{return Me}catch{}})()]).then(async()=>{q=te({name:"PurchaseReturnForm",__name:"PurchaseReturnForm",emits:["success"],setup(Xe,{expose:z,emit:H}){const{t:f}=re(),R=ue(),m=n(!1),S=n(""),p=n(!1),v=n(""),l=n({id:void 0,supplierId:void 0,accountId:void 0,returnTime:void 0,remark:void 0,fileUrl:"",discountPercent:0,discountPrice:0,totalPrice:0,otherPrice:0,orderNo:void 0,items:[],no:void 0}),J=oe({supplierId:[{required:!0,message:"\u4F9B\u5E94\u5546\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],returnTime:[{required:!0,message:"\u9000\u8D27\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),V=de(()=>v.value==="detail"),b=n(),x=n([]),h=n([]),M=n([]),P=n("item"),N=n();se(()=>l.value,o=>{if(!o)return;const a=o.items.reduce((d,s)=>d+s.totalPrice,0),i=o.discountPercent!=null?ie(a,o.discountPercent/100):0;l.value.discountPrice=i,l.value.totalPrice=a-i+o.otherPrice},{deep:!0}),z({open:async(o,a)=>{if(m.value=!0,S.value=f("action."+o),v.value=o,Q(),a){p.value=!0;try{l.value=await g.getPurchaseReturn(a)}finally{p.value=!1}}x.value=await Ae.getSupplierSimpleList(),M.value=await Je(),h.value=await Oe.getAccountSimpleList();const i=h.value.find(d=>d.defaultStatus);i&&(l.value.accountId=i.id)}});const T=n(),W=()=>{T.value.open()},X=o=>{l.value.orderId=o.id,l.value.orderNo=o.no,l.value.supplierId=o.supplierId,l.value.accountId=o.accountId,l.value.discountPercent=o.discountPercent,l.value.remark=o.remark,l.value.fileUrl=o.fileUrl,o.items.forEach(a=>{a.count=a.inCount-a.returnCount,a.orderItemId=a.id,a.id=void 0}),l.value.items=o.items.filter(a=>a.count>0)},j=H,K=async()=>{await b.value.validate(),await N.value.validate(),p.value=!0;try{const o=l.value;v.value==="create"?(await g.createPurchaseReturn(o),R.success(f("common.createSuccess"))):(await g.updatePurchaseReturn(o),R.success(f("common.updateSuccess"))),m.value=!1,j("success")}finally{p.value=!1}},Q=()=>{var o;l.value={id:void 0,supplierId:void 0,accountId:void 0,returnTime:void 0,remark:void 0,fileUrl:void 0,discountPercent:0,discountPrice:0,totalPrice:0,otherPrice:0,items:[]},(o=b.value)==null||o.resetFields()};return(o,a)=>{const i=me,d=pe,s=_e,Z=fe,B=ve,y=Ve,C=be,F=he,D=Pe,L=ye,G=Ie,Y=Ue,$=Ne,A=we,ee=ke,ae=Se,le=ge;return c(),I(w,null,[e(ae,{title:t(S),modelValue:t(m),"onUpdate:modelValue":a[13]||(a[13]=u=>O(m)?m.value=u:null),width:"1440"},{footer:r(()=>[t(V)?ne("",!0):(c(),_(y,{key:0,onClick:K,type:"primary",disabled:t(p)},{default:r(()=>[U(" \u786E \u5B9A ")]),_:1},8,["disabled"])),e(y,{onClick:a[12]||(a[12]=u=>m.value=!1)},{default:r(()=>[U("\u53D6 \u6D88")]),_:1})]),default:r(()=>[ce((c(),_(ee,{ref_key:"formRef",ref:b,model:t(l),rules:t(J),"label-width":"100px",disabled:t(V)},{default:r(()=>[e(L,{gutter:20},{default:r(()=>[e(s,{span:8},{default:r(()=>[e(d,{label:"\u9000\u8D27\u5355\u53F7",prop:"no"},{default:r(()=>[e(i,{disabled:"",modelValue:t(l).no,"onUpdate:modelValue":a[0]||(a[0]=u=>t(l).no=u),placeholder:"\u4FDD\u5B58\u65F6\u81EA\u52A8\u751F\u6210"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:r(()=>[e(d,{label:"\u9000\u8D27\u65F6\u95F4",prop:"returnTime"},{default:r(()=>[e(Z,{modelValue:t(l).returnTime,"onUpdate:modelValue":a[1]||(a[1]=u=>t(l).returnTime=u),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u9000\u8D27\u65F6\u95F4",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:r(()=>[e(d,{label:"\u5173\u8054\u8BA2\u5355",prop:"orderNo"},{default:r(()=>[e(i,{modelValue:t(l).orderNo,"onUpdate:modelValue":a[2]||(a[2]=u=>t(l).orderNo=u),readonly:""},{append:r(()=>[e(y,{onClick:W},{default:r(()=>[e(B,{icon:"ep:search"}),U(" \u9009\u62E9 ")]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:r(()=>[e(d,{label:"\u4F9B\u5E94\u5546",prop:"supplierId"},{default:r(()=>[e(F,{modelValue:t(l).supplierId,"onUpdate:modelValue":a[3]||(a[3]=u=>t(l).supplierId=u),clearable:"",filterable:"",disabled:"",placeholder:"\u8BF7\u9009\u62E9\u4F9B\u5E94\u5546",class:"!w-1/1"},{default:r(()=>[(c(!0),I(w,null,E(t(x),u=>(c(),_(C,{key:u.id,label:u.name,value:u.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{span:16},{default:r(()=>[e(d,{label:"\u5907\u6CE8",prop:"remark"},{default:r(()=>[e(i,{type:"textarea",modelValue:t(l).remark,"onUpdate:modelValue":a[4]||(a[4]=u=>t(l).remark=u),rows:1,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:r(()=>[e(d,{label:"\u9644\u4EF6",prop:"fileUrl"},{default:r(()=>[e(D,{"is-show-tip":!1,modelValue:t(l).fileUrl,"onUpdate:modelValue":a[5]||(a[5]=u=>t(l).fileUrl=u),limit:1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e($,null,{default:r(()=>[e(Y,{modelValue:t(P),"onUpdate:modelValue":a[6]||(a[6]=u=>O(P)?P.value=u:null),class:"-mt-15px -mb-10px"},{default:r(()=>[e(G,{label:"\u9000\u8D27\u4EA7\u54C1\u6E05\u5355",name:"item"},{default:r(()=>[e(Fe,{ref_key:"itemFormRef",ref:N,items:t(l).items,disabled:t(V)},null,8,["items","disabled"])]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(L,{gutter:20},{default:r(()=>[e(s,{span:8},{default:r(()=>[e(d,{label:"\u4F18\u60E0\u7387\uFF08%\uFF09",prop:"discountPercent"},{default:r(()=>[e(A,{modelValue:t(l).discountPercent,"onUpdate:modelValue":a[7]||(a[7]=u=>t(l).discountPercent=u),"controls-position":"right",min:0,precision:2,placeholder:"\u8BF7\u8F93\u5165\u4F18\u60E0\u7387",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:r(()=>[e(d,{label:"\u9000\u6B3E\u4F18\u60E0",prop:"discountPrice"},{default:r(()=>[e(i,{disabled:"",modelValue:t(l).discountPrice,"onUpdate:modelValue":a[8]||(a[8]=u=>t(l).discountPrice=u),formatter:t(k)},null,8,["modelValue","formatter"])]),_:1})]),_:1}),e(s,{span:8},{default:r(()=>[e(d,{label:"\u4F18\u60E0\u540E\u91D1\u989D"},{default:r(()=>[e(i,{disabled:"","model-value":t(l).totalPrice-t(l).otherPrice,formatter:t(k)},null,8,["model-value","formatter"])]),_:1})]),_:1}),e(s,{span:8},{default:r(()=>[e(d,{label:"\u5176\u5B83\u8D39\u7528",prop:"otherPrice"},{default:r(()=>[e(A,{modelValue:t(l).otherPrice,"onUpdate:modelValue":a[9]||(a[9]=u=>t(l).otherPrice=u),"controls-position":"right",min:0,precision:2,placeholder:"\u8BF7\u8F93\u5165\u5176\u5B83\u8D39\u7528",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:r(()=>[e(d,{label:"\u7ED3\u7B97\u8D26\u6237",prop:"accountId"},{default:r(()=>[e(F,{modelValue:t(l).accountId,"onUpdate:modelValue":a[10]||(a[10]=u=>t(l).accountId=u),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u7ED3\u7B97\u8D26\u6237",class:"!w-1/1"},{default:r(()=>[(c(!0),I(w,null,E(t(h),u=>(c(),_(C,{key:u.id,label:u.name,value:u.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:r(()=>[e(d,{label:"\u5E94\u9000\u91D1\u989D",prop:"totalPrice"},{default:r(()=>[e(i,{disabled:"",modelValue:t(l).totalPrice,"onUpdate:modelValue":a[11]||(a[11]=u=>t(l).totalPrice=u),formatter:t(k)},null,8,["modelValue","formatter"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules","disabled"])),[[le,t(p)]])]),_:1},8,["title","modelValue"]),e(ze,{ref_key:"purchaseOrderReturnEnableListRef",ref:T,onSuccess:X},null,512)],64)}}})});export{q as _,We as __tla};
