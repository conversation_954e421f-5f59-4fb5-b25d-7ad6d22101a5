import{by as t,__tla as i}from"./index-BUSn51wb.js";let s,e,l,r,p,y,o,m=Promise.all([(()=>{try{return i}catch{}})()]).then(async()=>{e=async a=>await t.get({url:"/system/post/page",params:a}),y=async()=>await t.get({url:"/system/post/simple-list"}),s=async a=>await t.get({url:"/system/post/get?id="+a}),l=async a=>await t.post({url:"/system/post/create",data:a}),o=async a=>await t.put({url:"/system/post/update",data:a}),r=async a=>await t.delete({url:"/system/post/delete?id="+a}),p=async a=>await t.download({url:"/system/post/export",params:a})});export{m as __tla,s as a,e as b,l as c,r as d,p as e,y as g,o as u};
