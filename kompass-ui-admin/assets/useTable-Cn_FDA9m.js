import{d as ae,p as c,r as _,C as pe,a,b as R,at as $,H as ue,R as ge,i as g,Q as me,ao as Q,x as he,P as W,B as fe,n as ne,aD as ye,cr as we,o as P,c as X,c9 as xe,k as be,w as A,aV as Y,l as B,j as M,t as N,a9 as z,av as ve,F as _e,_ as Pe,N as Se,f as ke,aK as oe,dz as re,ay as Ae,__tla as ze}from"./index-BUSn51wb.js";import{g as D,_ as Le,__tla as Oe}from"./Form-DJa9ov9B.js";import{E as Te,__tla as Ce}from"./index-Cch5e1V0.js";import{u as je,__tla as Fe}from"./useForm-C3fyhjNV.js";import{d as Re}from"./download-e0EdwhTv.js";let le,se,ie,$e=Promise.all([(()=>{try{return ze}catch{}})(),(()=>{try{return Oe}catch{}})(),(()=>{try{return Ce}catch{}})(),(()=>{try{return Fe}catch{}})()]).then(async()=>{let Z,y;se=fe(ae({name:"Table",props:{pageSize:c.number.def(10),currentPage:c.number.def(1),selection:c.bool.def(!1),showOverflowTooltip:c.bool.def(!0),columns:{type:Array,default:()=>[]},expand:c.bool.def(!1),pagination:{type:Object,default:()=>{}},reserveSelection:c.bool.def(!1),loading:c.bool.def(!1),reserveIndex:c.bool.def(!1),align:c.string.validate(e=>["left","center","right"].includes(e)).def("center"),headerAlign:c.string.validate(e=>["left","center","right"].includes(e)).def("center"),data:{type:Array,default:()=>[]}},emits:["update:pageSize","update:currentPage","register"],setup(e,{attrs:o,slots:p,emit:m,expose:L}){const u=_();pe(()=>{const t=a(u);m("register",t==null?void 0:t.$parent,u)});const x=_(e.pageSize),h=_(e.currentPage),n=_({}),r=_({}),l=R(()=>{const t={...e};return Object.assign(t,a(r)),t}),b=(t,O)=>{var S;const{columns:T}=a(l);for(const C of O||T)for(const j of t)C.field===j.field?he(C,j.path,j.value):(S=C.children)!=null&&S.length&&b(t,C.children)},q=_([]),U=t=>{q.value=t};L({setProps:(t={})=>{r.value=Object.assign(a(r),t),n.value=t},setColumn:b,selections:q});const s=R(()=>Object.assign({small:!1,background:!0,pagerCount:document.body.clientWidth<992?5:7,layout:"total, sizes, prev, pager, next, jumper",pageSizes:[10,20,30,50,100],disabled:!1,hideOnSinglePage:!1,total:10},a(l).pagination));$(()=>a(l).pageSize,t=>{x.value=t}),$(()=>a(l).currentPage,t=>{h.value=t}),$(()=>x.value,t=>{m("update:pageSize",t)}),$(()=>h.value,t=>{m("update:currentPage",t)});const v=R(()=>{const t={...o,...e};return delete t.columns,delete t.data,t}),d=()=>{const{selection:t,reserveSelection:O,align:T,headerAlign:S}=a(l);return t?g(W,{type:"selection",reserveSelection:O,align:T,headerAlign:S,width:"50"},null):void 0},w=()=>{const{align:t,headerAlign:O,expand:T}=a(l);return T?g(W,{type:"expand",align:t,headerAlign:O},{default:S=>D(p,"expand",S)}):void 0},E=t=>{const{columns:O,reserveIndex:T,pageSize:S,currentPage:C,align:j,headerAlign:ee,showOverflowTooltip:ce}=a(l);return[w(),d()].concat((t||O).map(i=>{if(i.type==="index")return g(W,{type:"index",index:i.index?i.index:I=>((k,H,V,G)=>{const K=H+1;return k?V*(G-1)+K:K})(T,I,S,C),align:i.align||j,headerAlign:i.headerAlign||ee,label:i.label,width:"65px"},null);{const I={...i};return I.children&&delete I.children,g(W,Q({showOverflowTooltip:ce,align:j,headerAlign:ee},I,{prop:i.field}),{default:k=>{var H;return i.children&&i.children.length?(V=>{const{align:G,headerAlign:K,showOverflowTooltip:de}=a(l);return V.map(f=>{const J={...f};return J.children&&delete J.children,g(W,Q({showOverflowTooltip:de,align:G,headerAlign:K},J,{prop:f.field}),{default:F=>{var te;return f.children&&f.children.length?E(f.children):D(p,f.field,F)||((te=f==null?void 0:f.formatter)==null?void 0:te.call(f,F.row,F.column,F.row[f.field],F.$index))||F.row[f.field]},header:D(p,`${f.field}-header`)})})})(i.children):D(p,i.field,k)||((H=i==null?void 0:i.formatter)==null?void 0:H.call(i,k.row,k.column,k.row[i.field],k.$index))||k.row[i.field]},header:()=>D(p,`${i.field}-header`)||i.label})}}))};return()=>ue(g("div",null,[g(me,Q({ref:u,data:a(l).data,"onSelection-change":U},a(v)),{default:()=>E(),append:()=>D(p,"append")}),a(l).pagination?g(Te,Q({pageSize:x.value,"onUpdate:pageSize":t=>x.value=t,currentPage:h.value,"onUpdate:currentPage":t=>h.value=t,class:"float-right mb-15px mt-15px"},a(s)),null):void 0]),[[ge,a(l).loading]])}}),[["__scopeId","data-v-5d0e7868"]]),Z={key:0},le=ae({name:"Search",__name:"Search",props:{schema:{type:Array,default:()=>[]},isCol:c.bool.def(!1),labelWidth:c.oneOfType([String,Number]).def("auto"),layout:c.string.validate(e=>["inline","bottom"].includes(e)).def("inline"),buttomPosition:c.string.validate(e=>["left","center","right"].includes(e)).def("center"),showSearch:c.bool.def(!0),showReset:c.bool.def(!0),expand:c.bool.def(!1),expandField:c.string.def(""),inline:c.bool.def(!0),model:{type:Object,default:()=>({})}},emits:["search","reset"],setup(e,{emit:o}){const{t:p}=ne(),m=e,L=o,u=_(!0),x=R(()=>{let s=ye(m.schema);if(m.expand&&m.expandField&&!a(u)){const v=we(s,d=>d.field===m.expandField);if(v>-1){const d=s.length;s.splice(v+1,d)}}return m.layout==="inline"&&(s=s.concat([{field:"action",formItemProps:{labelWidth:"0px"}}])),s}),{register:h,elFormRef:n,methods:r}=je({model:m.model||{}}),l=async()=>{var s;await((s=a(n))==null?void 0:s.validate(async v=>{if(v){const{getFormData:d}=r,w=await d();L("search",w)}}))},b=async()=>{var d;(d=a(n))==null||d.resetFields();const{getFormData:s}=r,v=await s();L("reset",v)},q=R(()=>({textAlign:m.buttomPosition})),U=()=>{var s;(s=a(n))==null||s.resetFields(),u.value=!a(u)};return(s,v)=>{const d=Pe,w=Se,E=Le;return P(),X(_e,null,[g(E,{inline:e.inline,"is-col":e.isCol,"is-custom":!1,"label-width":e.labelWidth,schema:a(x),class:"-mb-15px","hide-required-asterisk":"",onRegister:a(h)},xe({action:A(()=>[e.layout==="inline"?(P(),X("div",Z,[e.showSearch?(P(),B(w,{key:0,onClick:l},{default:A(()=>[g(d,{class:"mr-5px",icon:"ep:search"}),M(" "+N(a(p)("common.query")),1)]),_:1})):z("",!0),e.showReset?(P(),B(w,{key:1,onClick:b},{default:A(()=>[g(d,{class:"mr-5px",icon:"ep:refresh"}),M(" "+N(a(p)("common.reset")),1)]),_:1})):z("",!0),e.expand?(P(),B(w,{key:2,text:"",onClick:U},{default:A(()=>[M(N(a(p)(a(u)?"common.shrink":"common.expand"))+" ",1),g(d,{icon:a(u)?"ep:arrow-up":"ep:arrow-down"},null,8,["icon"])]),_:1})):z("",!0),Y(s.$slots,"actionMore")])):z("",!0)]),_:2},[be(Object.keys(s.$slots),t=>({name:t,fn:A(()=>[Y(s.$slots,t)])}))]),1032,["inline","is-col","label-width","schema","onRegister"]),e.layout==="bottom"?(P(),X("div",{key:0,style:ve(a(q))},[e.showSearch?(P(),B(w,{key:0,type:"primary",onClick:l},{default:A(()=>[g(d,{class:"mr-5px",icon:"ep:search"}),M(" "+N(a(p)("common.query")),1)]),_:1})):z("",!0),e.showReset?(P(),B(w,{key:1,onClick:b},{default:A(()=>[g(d,{class:"mr-5px",icon:"ep:refresh-right"}),M(" "+N(a(p)("common.reset")),1)]),_:1})):z("",!0),e.expand?(P(),B(w,{key:2,text:"",onClick:U},{default:A(()=>[M(N(a(p)(a(u)?"common.shrink":"common.expand"))+" ",1),g(d,{icon:a(u)?"ep:arrow-up":"ep:arrow-down"},null,8,["icon"])]),_:1})):z("",!0),Y(s.$slots,"actionMore")],4)):z("",!0)],64)}}}),{t:y}=ne(),ie=e=>{const o=ke({pageSize:10,currentPage:1,total:10,tableList:[],params:{...(e==null?void 0:e.defaultParams)||{}},loading:!0,exportLoading:!1,currentRow:null}),p=R(()=>({...o.params,pageSize:o.pageSize,pageNo:o.currentPage}));$(()=>o.currentPage,()=>{h.getList()}),$(()=>o.pageSize,()=>{o.currentPage===1||(o.currentPage=1),h.getList()});const m=_(),L=_(),u=async()=>{await Ae();const n=a(m);return n||console.error("The table is not registered. Please use the register method to register"),n},x=async n=>{let r=1;n instanceof Array?(r=n.length,await Promise.all(n.map(async l=>{await((e==null?void 0:e.delListApi)&&(e==null?void 0:e.delListApi(l)))}))):await((e==null?void 0:e.delListApi)&&(e==null?void 0:e.delListApi(n))),oe.success(y("common.delSuccess")),o.currentPage=(o.total%o.pageSize===r||o.pageSize===1)&&o.currentPage>1?o.currentPage-1:o.currentPage,await h.getList()},h={getList:async()=>{o.loading=!0;const n=await(e==null?void 0:e.getListApi(a(p)).finally(()=>{o.loading=!1}));n&&(o.tableList=n.list,o.total=n.total??0)},setProps:async(n={})=>{const r=await u();r==null||r.setProps(n)},setColumn:async n=>{const r=await u();r==null||r.setColumn(n)},getSelections:async()=>{const n=await u();return(n==null?void 0:n.selections)||[]},setSearchParams:n=>{o.params=Object.assign(o.params,{pageSize:o.pageSize,pageNo:1,...n}),o.currentPage!==1?o.currentPage=1:h.getList()},delList:async(n,r,l=!0)=>{const b=await u();!r||b!=null&&b.selections.length?l?re.confirm(y("common.delMessage"),y("common.confirmTitle"),{confirmButtonText:y("common.ok"),cancelButtonText:y("common.cancel"),type:"warning"}).then(async()=>{await x(n)}):await x(n):oe.warning(y("common.delNoData"))},exportList:async n=>{o.exportLoading=!0,re.confirm(y("common.exportMessage"),y("common.confirmTitle"),{confirmButtonText:y("common.ok"),cancelButtonText:y("common.cancel"),type:"warning"}).then(async()=>{var l;const r=await((l=e==null?void 0:e.exportListApi)==null?void 0:l.call(e,a(p)));r&&Re.excel(r,n)}).finally(()=>{o.exportLoading=!1})}};return e!=null&&e.props&&h.setProps(e.props),{register:(n,r)=>{m.value=n,L.value=r},elTableRef:L,tableObject:o,methods:h,tableMethods:h}}});export{le as _,$e as __tla,se as a,ie as u};
