import{d as f,n as y,I as h,r as s,C as b,o as n,l as o,w as _,H as v,a as e,i as t,P as g,Q as w,R as I,__tla as x}from"./index-BUSn51wb.js";import{_ as C,__tla as P}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{d as j,__tla as D}from"./formatTime-DWdBpgsM.js";import{a as H,__tla as L}from"./index-DrnBZ6x8.js";let p,Q=Promise.all([(()=>{try{return x}catch{}})(),(()=>{try{return P}catch{}})(),(()=>{try{return D}catch{}})(),(()=>{try{return L}catch{}})()]).then(async()=>{p=f({__name:"Demo03CourseList",props:{studentId:{}},setup(c){y(),h();const i=c,r=s(!1),l=s([]);return b(()=>{(async()=>{r.value=!0;try{l.value=await H(i.studentId)}finally{r.value=!1}})()}),(R,T)=>{const a=g,u=w,m=C,d=I;return n(),o(m,null,{default:_(()=>[v((n(),o(u,{data:e(l),stripe:!0,"show-overflow-tooltip":!0},{default:_(()=>[t(a,{label:"\u7F16\u53F7",align:"center",prop:"id"}),t(a,{label:"\u540D\u5B57",align:"center",prop:"name"}),t(a,{label:"\u5206\u6570",align:"center",prop:"score"}),t(a,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:e(j),width:"180px"},null,8,["formatter"])]),_:1},8,["data"])),[[d,e(r)]])]),_:1})}}})});export{p as _,Q as __tla};
