import{_ as t,__tla as r}from"./ProductDetailsHeader.vue_vue_type_script_setup_true_lang-B8cOSsVX.js";import{__tla as _}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{__tla as a}from"./index-BUSn51wb.js";import{__tla as l}from"./el-card-CJbXGyyg.js";import{__tla as o}from"./el-descriptions-item-dD3qa0ub.js";import{__tla as m}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import"./color-BN7ZL7BD.js";import{__tla as c}from"./ProductForm.vue_vue_type_script_setup_true_lang-G9Bcq_BM.js";import{__tla as e}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as s}from"./index-CaE_tgzr.js";import{__tla as i}from"./index-V4315SLT.js";import"./tree-BMa075Oj.js";import{__tla as p}from"./index-BYXzDB8j.js";let n=Promise.all([(()=>{try{return r}catch{}})(),(()=>{try{return _}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return m}catch{}})(),(()=>{try{return c}catch{}})(),(()=>{try{return e}catch{}})(),(()=>{try{return s}catch{}})(),(()=>{try{return i}catch{}})(),(()=>{try{return p}catch{}})()]).then(async()=>{});export{n as __tla,t as default};
