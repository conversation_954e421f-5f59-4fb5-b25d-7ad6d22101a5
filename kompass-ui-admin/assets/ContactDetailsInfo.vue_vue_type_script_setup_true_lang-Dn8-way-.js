import{_ as T,__tla as x}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{d as E,r as v,o as S,l as w,w as a,i as t,j as c,t as _,a as o,G as p,y as A,g as i,__tla as L}from"./index-BUSn51wb.js";import{E as R,a as U,__tla as V}from"./el-collapse-item-B_QvnH_b.js";import{E as g,a as j,__tla as q}from"./el-descriptions-item-dD3qa0ub.js";import{_ as C,__tla as G}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{f as n,__tla as O}from"./formatTime-DWdBpgsM.js";let y,Q=Promise.all([(()=>{try{return x}catch{}})(),(()=>{try{return L}catch{}})(),(()=>{try{return V}catch{}})(),(()=>{try{return q}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return O}catch{}})()]).then(async()=>{let u,r;u=i("span",{class:"text-base font-bold"},"\u57FA\u672C\u4FE1\u606F",-1),r=i("span",{class:"text-base font-bold"},"\u7CFB\u7EDF\u4FE1\u606F",-1),y=E({__name:"ContactDetailsInfo",props:{contact:{}},setup(k){const s=v(["basicInfo","systemInfo"]);return(e,f)=>{const l=g,d=C,m=j,b=R,N=U,h=T;return S(),w(h,null,{default:a(()=>[t(N,{modelValue:o(s),"onUpdate:modelValue":f[0]||(f[0]=I=>A(s)?s.value=I:null)},{default:a(()=>[t(b,{name:"basicInfo"},{title:a(()=>[u]),default:a(()=>[t(m,{column:4},{default:a(()=>[t(l,{label:"\u59D3\u540D"},{default:a(()=>[c(_(e.contact.name),1)]),_:1}),t(l,{label:"\u5BA2\u6237\u540D\u79F0"},{default:a(()=>[c(_(e.contact.customerName),1)]),_:1}),t(l,{label:"\u624B\u673A"},{default:a(()=>[c(_(e.contact.mobile),1)]),_:1}),t(l,{label:"\u7535\u8BDD"},{default:a(()=>[c(_(e.contact.telephone),1)]),_:1}),t(l,{label:"\u90AE\u7BB1"},{default:a(()=>[c(_(e.contact.email),1)]),_:1}),t(l,{label:"QQ"},{default:a(()=>[c(_(e.contact.qq),1)]),_:1}),t(l,{label:"\u5FAE\u4FE1"},{default:a(()=>[c(_(e.contact.wechat),1)]),_:1}),t(l,{label:"\u5730\u5740"},{default:a(()=>[c(_(e.contact.areaName)+" "+_(e.contact.detailAddress),1)]),_:1}),t(l,{label:"\u804C\u52A1"},{default:a(()=>[c(_(e.contact.post),1)]),_:1}),t(l,{label:"\u76F4\u5C5E\u4E0A\u7EA7"},{default:a(()=>[c(_(e.contact.parentName),1)]),_:1}),t(l,{label:"\u5173\u952E\u51B3\u7B56\u4EBA"},{default:a(()=>[t(d,{type:o(p).INFRA_BOOLEAN_STRING,value:e.contact.master},null,8,["type","value"])]),_:1}),t(l,{label:"\u6027\u522B"},{default:a(()=>[t(d,{type:o(p).SYSTEM_USER_SEX,value:e.contact.sex},null,8,["type","value"])]),_:1}),t(l,{label:"\u4E0B\u6B21\u8054\u7CFB\u65F6\u95F4"},{default:a(()=>[c(_(o(n)(e.contact.contactNextTime)),1)]),_:1}),t(l,{label:"\u5907\u6CE8"},{default:a(()=>[c(_(e.contact.remark),1)]),_:1})]),_:1})]),_:1}),t(b,{name:"systemInfo"},{title:a(()=>[r]),default:a(()=>[t(m,{column:4},{default:a(()=>[t(l,{label:"\u8D1F\u8D23\u4EBA"},{default:a(()=>[c(_(e.contact.ownerUserName),1)]),_:1}),t(l,{label:"\u6700\u540E\u8DDF\u8FDB\u8BB0\u5F55"},{default:a(()=>[c(_(e.contact.contactLastContent),1)]),_:1}),t(l,{label:"\u6700\u540E\u8DDF\u8FDB\u65F6\u95F4"},{default:a(()=>[c(_(o(n)(e.contact.contactLastTime)),1)]),_:1}),t(l,{label:""},{default:a(()=>[c("\xA0")]),_:1}),t(l,{label:"\u521B\u5EFA\u4EBA"},{default:a(()=>[c(_(e.contact.creatorName),1)]),_:1}),t(l,{label:"\u521B\u5EFA\u65F6\u95F4"},{default:a(()=>[c(_(o(n)(e.contact.createTime)),1)]),_:1}),t(l,{label:"\u66F4\u65B0\u65F6\u95F4"},{default:a(()=>[c(_(o(n)(e.contact.updateTime)),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1})}}})});export{y as _,Q as __tla};
