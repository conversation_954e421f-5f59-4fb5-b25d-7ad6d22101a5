import{d as U,f as b,r as c,at as j,u as L,C as E,o as v,c as F,i as a,w as n,a as t,H,l as O,j as P,t as N,dV as Q,F as R,P as V,v as X,Q as Z,R as G,__tla as J}from"./index-BUSn51wb.js";import{_ as K,__tla as M}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{E as W,__tla as Y}from"./el-card-CJbXGyyg.js";import{E as $,__tla as aa}from"./el-skeleton-item-tDN8U6BH.js";import{_ as ea,__tla as ta}from"./Echart.vue_vue_type_script_setup_true_lang-DnLZLH3w.js";import{S as A,__tla as ra}from"./funnel-B_PNiNbM.js";import{d as p,__tla as ia}from"./formatTime-DWdBpgsM.js";let C,la=Promise.all([(()=>{try{return J}catch{}})(),(()=>{try{return M}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return aa}catch{}})(),(()=>{try{return ta}catch{}})(),(()=>{try{return ra}catch{}})(),(()=>{try{return ia}catch{}})()]).then(async()=>{C=U({name:"BusinessSummary",__name:"BusinessSummary",props:{queryParams:{}},setup(S,{expose:B}){const u=S,o=b({pageNo:1,pageSize:10}),d=c(!1),_=c([]),y=c(0);j(()=>u.queryParams,l=>{if(!l)return;const i={...o,...l};Object.assign(o,i)},{immediate:!0});const r=b({grid:{left:30,right:30,bottom:20,containLabel:!0},legend:{},series:[{name:"\u65B0\u589E\u5546\u673A\u6570\u91CF",type:"bar",yAxisIndex:0,data:[]},{name:"\u65B0\u589E\u5546\u673A\u91D1\u989D",type:"bar",yAxisIndex:1,data:[]}],toolbox:{feature:{dataZoom:{xAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u65B0\u589E\u5546\u673A\u5206\u6790"}}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},yAxis:[{type:"value",name:"\u65B0\u589E\u5546\u673A\u6570\u91CF",min:0,minInterval:1},{type:"value",name:"\u65B0\u589E\u5546\u673A\u91D1\u989D",min:0,minInterval:1,splitLine:{lineStyle:{type:"dotted",opacity:.7}}}],xAxis:{type:"category",name:"\u65E5\u671F",data:[]}}),g=async()=>{const l=await A.getBusinessPageByDate(u.queryParams);_.value=l.list,y.value=l.total},{push:f}=L(),x=async()=>{d.value=!0;try{await(async()=>{const l=await A.getBusinessSummaryByDate(u.queryParams);r.xAxis&&r.xAxis.data&&(r.xAxis.data=l.map(i=>i.time)),r.series&&r.series[0]&&r.series[0].data&&(r.series[0].data=l.map(i=>i.businessCreateCount)),r.series&&r.series[1]&&r.series[1].data&&(r.series[1].data=l.map(i=>i.totalPrice)),await g()})()}finally{d.value=!1}};return B({loadData:x}),E(()=>{x()}),(l,i)=>{const I=ea,D=$,h=W,e=V,w=X,T=Z,k=K,q=G;return v(),F(R,null,[a(h,{shadow:"never"},{default:n(()=>[a(D,{loading:t(d),animated:""},{default:n(()=>[a(I,{height:500,options:t(r)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),a(h,{class:"mt-16px",shadow:"never"},{default:n(()=>[H((v(),O(T,{data:t(_)},{default:n(()=>[a(e,{align:"center",fixed:"left",label:"\u5E8F\u53F7",type:"index",width:"80"}),a(e,{align:"center",fixed:"left",label:"\u5546\u673A\u540D\u79F0",prop:"name",width:"160"},{default:n(s=>[a(w,{underline:!1,type:"primary",onClick:z=>{return m=s.row.id,void f({name:"CrmBusinessDetail",params:{id:m}});var m}},{default:n(()=>[P(N(s.row.name),1)]),_:2},1032,["onClick"])]),_:1}),a(e,{align:"center",fixed:"left",label:"\u5BA2\u6237\u540D\u79F0",prop:"customerName",width:"120"},{default:n(s=>[a(w,{underline:!1,type:"primary",onClick:z=>{return m=s.row.customerId,void f({name:"CrmCustomerDetail",params:{id:m}});var m}},{default:n(()=>[P(N(s.row.customerName),1)]),_:2},1032,["onClick"])]),_:1}),a(e,{formatter:t(Q),align:"center",label:"\u5546\u673A\u91D1\u989D\uFF08\u5143\uFF09",prop:"totalPrice",width:"140"},null,8,["formatter"]),a(e,{formatter:t(p),align:"center",label:"\u9884\u8BA1\u6210\u4EA4\u65E5\u671F",prop:"dealTime",width:"180px"},null,8,["formatter"]),a(e,{align:"center",label:"\u5907\u6CE8",prop:"remark",width:"200"}),a(e,{formatter:t(p),align:"center",label:"\u4E0B\u6B21\u8054\u7CFB\u65F6\u95F4",prop:"contactNextTime",width:"180px"},null,8,["formatter"]),a(e,{align:"center",label:"\u8D1F\u8D23\u4EBA",prop:"ownerUserName",width:"100px"}),a(e,{align:"center",label:"\u6240\u5C5E\u90E8\u95E8",prop:"ownerUserDeptName",width:"100px"}),a(e,{formatter:t(p),align:"center",label:"\u6700\u540E\u8DDF\u8FDB\u65F6\u95F4",prop:"contactLastTime",width:"180px"},null,8,["formatter"]),a(e,{formatter:t(p),align:"center",label:"\u66F4\u65B0\u65F6\u95F4",prop:"updateTime",width:"180px"},null,8,["formatter"]),a(e,{formatter:t(p),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"]),a(e,{align:"center",label:"\u521B\u5EFA\u4EBA",prop:"creatorName",width:"100px"}),a(e,{align:"center",fixed:"right",label:"\u5546\u673A\u72B6\u6001\u7EC4",prop:"statusTypeName",width:"140"}),a(e,{align:"center",fixed:"right",label:"\u5546\u673A\u9636\u6BB5",prop:"statusName",width:"120"})]),_:1},8,["data"])),[[q,t(d)]]),a(k,{limit:t(o).pageSize,"onUpdate:limit":i[0]||(i[0]=s=>t(o).pageSize=s),page:t(o).pageNo,"onUpdate:page":i[1]||(i[1]=s=>t(o).pageNo=s),total:t(y),onPagination:g},null,8,["limit","page","total"])]),_:1})],64)}}})});export{C as _,la as __tla};
