import{_ as y,__tla as g}from"./AppLinkSelectDialog.vue_vue_type_script_setup_true_lang-CHi5jpXX.js";import{d as k,p as j,r as n,at as o,o as x,c as C,i as l,w as r,j as w,a as A,y as F,F as I,N as L,Z as N,__tla as P}from"./index-BUSn51wb.js";let d,R=Promise.all([(()=>{try{return g}catch{}})(),(()=>{try{return P}catch{}})()]).then(async()=>{d=k({name:"AppLinkInput",__name:"index",props:{modelValue:j.string.def("")},emits:["update:modelValue"],setup(m,{emit:_}){const t=m,a=n(""),u=n(),p=()=>{var e;return(e=u.value)==null?void 0:e.open(a.value)},i=e=>a.value=e;o(()=>t.modelValue,()=>a.value=t.modelValue,{immediate:!0});const c=_;return o(()=>a.value,()=>c("update:modelValue",a.value)),(e,s)=>{const v=L,V=N,f=y;return x(),C(I,null,[l(V,{modelValue:A(a),"onUpdate:modelValue":s[0]||(s[0]=h=>F(a)?a.value=h:null),placeholder:"\u8F93\u5165\u6216\u9009\u62E9\u94FE\u63A5"},{append:r(()=>[l(v,{onClick:p},{default:r(()=>[w("\u9009\u62E9")]),_:1})]),_:1},8,["modelValue"]),l(f,{ref_key:"dialogRef",ref:u,onChange:i},null,512)],64)}}})});export{d as _,R as __tla};
