import{U as a}from"./constants-A8BI3pz7.js";import{d as m,o as r,c as l,aV as p,a0 as o,a as e,__tla as _}from"./index-BUSn51wb.js";let t,n=Promise.all([(()=>{try{return _}catch{}})()]).then(async()=>{t=m({name:"MessageItem",__name:"MessageItem",props:{message:{}},setup:d=>(s,c)=>(r(),l("div",{class:o([s.message.senderType===e(a).MEMBER?"ml-10px":s.message.senderType===e(a).ADMIN?"mr-10px":""])},[p(s.$slots,"default")],2))})});export{n as __tla,t as default};
