import{d as G,n as H,I as J,r as m,f as Z,o as s,l as p,w as u,i as o,a as l,j as f,H as z,c as V,F as b,k as y,dR as D,G as M,V as Q,t as W,y as X,J as Y,K as $,L as ee,Z as le,cc as ae,am as te,an as oe,O as ue,N as de,R as re,__tla as se}from"./index-BUSn51wb.js";import{_ as me,__tla as ne}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{C as x,__tla as ie}from"./index-DrcFYyNA.js";import{A as pe,__tla as ce}from"./index-BRuDnVkN.js";import{C as g}from"./constants-A8BI3pz7.js";let q,_e=Promise.all([(()=>{try{return se}catch{}})(),(()=>{try{return ne}catch{}})(),(()=>{try{return ie}catch{}})(),(()=>{try{return ce}catch{}})()]).then(async()=>{q=G({name:"ChatModelForm",__name:"ChatModelForm",emits:["success"],setup(ve,{expose:E,emit:L}){const{t:_}=H(),k=J(),n=m(!1),h=m(""),i=m(!1),C=m(""),t=m({id:void 0,keyId:void 0,name:void 0,model:void 0,platform:void 0,sort:void 0,status:g.ENABLE,temperature:void 0,maxTokens:void 0,maxContexts:void 0}),N=Z({keyId:[{required:!0,message:"API \u79D8\u94A5\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],name:[{required:!0,message:"\u6A21\u578B\u540D\u5B57\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],model:[{required:!0,message:"\u6A21\u578B\u6807\u8BC6\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],platform:[{required:!0,message:"\u6240\u5C5E\u5E73\u53F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],sort:[{required:!0,message:"\u6392\u5E8F\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),v=m(),A=m([]);E({open:async(d,a)=>{if(n.value=!0,h.value=_("action."+d),C.value=d,P(),a){i.value=!0;try{t.value=await x.getChatModel(a)}finally{i.value=!1}}A.value=await pe.getApiKeySimpleList(g.ENABLE)}});const S=L,F=async()=>{await v.value.validate(),i.value=!0;try{const d=t.value;C.value==="create"?(await x.createChatModel(d),k.success(_("common.createSuccess"))):(await x.updateChatModel(d),k.success(_("common.updateSuccess"))),n.value=!1,S("success")}finally{i.value=!1}},P=()=>{var d;t.value={id:void 0,keyId:void 0,name:void 0,model:void 0,platform:void 0,sort:void 0,status:g.ENABLE,temperature:void 0,maxTokens:void 0,maxContexts:void 0},(d=v.value)==null||d.resetFields()};return(d,a)=>{const I=Y,U=$,r=ee,T=le,c=ae,B=te,O=oe,R=ue,w=de,j=me,K=re;return s(),p(j,{title:l(h),modelValue:l(n),"onUpdate:modelValue":a[10]||(a[10]=e=>X(n)?n.value=e:null)},{footer:u(()=>[o(w,{onClick:F,type:"primary",disabled:l(i)},{default:u(()=>[f("\u786E \u5B9A")]),_:1},8,["disabled"]),o(w,{onClick:a[9]||(a[9]=e=>n.value=!1)},{default:u(()=>[f("\u53D6 \u6D88")]),_:1})]),default:u(()=>[z((s(),p(R,{ref_key:"formRef",ref:v,model:l(t),rules:l(N),"label-width":"120px"},{default:u(()=>[o(r,{label:"\u6240\u5C5E\u5E73\u53F0",prop:"platform"},{default:u(()=>[o(U,{modelValue:l(t).platform,"onUpdate:modelValue":a[0]||(a[0]=e=>l(t).platform=e),placeholder:"\u8BF7\u8F93\u5165\u5E73\u53F0",clearable:""},{default:u(()=>[(s(!0),V(b,null,y(l(D)(l(M).AI_PLATFORM),e=>(s(),p(I,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(r,{label:"API \u79D8\u94A5",prop:"keyId"},{default:u(()=>[o(U,{modelValue:l(t).keyId,"onUpdate:modelValue":a[1]||(a[1]=e=>l(t).keyId=e),placeholder:"\u8BF7\u9009\u62E9 API \u79D8\u94A5",clearable:""},{default:u(()=>[(s(!0),V(b,null,y(l(A),e=>(s(),p(I,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(r,{label:"\u6A21\u578B\u540D\u5B57",prop:"name"},{default:u(()=>[o(T,{modelValue:l(t).name,"onUpdate:modelValue":a[2]||(a[2]=e=>l(t).name=e),placeholder:"\u8BF7\u8F93\u5165\u6A21\u578B\u540D\u5B57"},null,8,["modelValue"])]),_:1}),o(r,{label:"\u6A21\u578B\u6807\u8BC6",prop:"model"},{default:u(()=>[o(T,{modelValue:l(t).model,"onUpdate:modelValue":a[3]||(a[3]=e=>l(t).model=e),placeholder:"\u8BF7\u8F93\u5165\u6A21\u578B\u6807\u8BC6"},null,8,["modelValue"])]),_:1}),o(r,{label:"\u6A21\u578B\u6392\u5E8F",prop:"sort"},{default:u(()=>[o(c,{modelValue:l(t).sort,"onUpdate:modelValue":a[4]||(a[4]=e=>l(t).sort=e),placeholder:"\u8BF7\u8F93\u5165\u6A21\u578B\u6392\u5E8F",class:"!w-1/1"},null,8,["modelValue"])]),_:1}),o(r,{label:"\u5F00\u542F\u72B6\u6001",prop:"status"},{default:u(()=>[o(O,{modelValue:l(t).status,"onUpdate:modelValue":a[5]||(a[5]=e=>l(t).status=e)},{default:u(()=>[(s(!0),V(b,null,y(l(Q)(l(M).COMMON_STATUS),e=>(s(),p(B,{key:e.value,label:e.value},{default:u(()=>[f(W(e.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(r,{label:"\u6E29\u5EA6\u53C2\u6570",prop:"temperature"},{default:u(()=>[o(c,{modelValue:l(t).temperature,"onUpdate:modelValue":a[6]||(a[6]=e=>l(t).temperature=e),placeholder:"\u8BF7\u8F93\u5165\u6E29\u5EA6\u53C2\u6570",min:0,max:2,precision:2},null,8,["modelValue"])]),_:1}),o(r,{label:"\u56DE\u590D\u6570 Token \u6570",prop:"maxTokens"},{default:u(()=>[o(c,{modelValue:l(t).maxTokens,"onUpdate:modelValue":a[7]||(a[7]=e=>l(t).maxTokens=e),placeholder:"\u8BF7\u8F93\u5165\u56DE\u590D\u6570 Token \u6570",min:0,max:4096},null,8,["modelValue"])]),_:1}),o(r,{label:"\u4E0A\u4E0B\u6587\u6570\u91CF",prop:"maxContexts"},{default:u(()=>[o(c,{modelValue:l(t).maxContexts,"onUpdate:modelValue":a[8]||(a[8]=e=>l(t).maxContexts=e),placeholder:"\u8BF7\u8F93\u5165\u4E0A\u4E0B\u6587\u6570\u91CF",min:0,max:20},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[K,l(i)]])]),_:1},8,["title","modelValue"])}}})});export{q as _,_e as __tla};
