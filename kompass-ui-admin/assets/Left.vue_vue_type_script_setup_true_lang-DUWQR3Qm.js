import{d as Z,I as J,r as G,ee as N,o as d,c as m,i as a,w as p,g as u,a0 as j,t as V,a as t,j as w,a9 as K,F as f,k as Q,V as v,G as g,ao as S,ef as F,_ as X,Z as ee,N as le,__tla as ae}from"./index-BUSn51wb.js";import{_ as x,__tla as te}from"./Tag.vue_vue_type_script_setup_true_lang-Bxsa69ZH.js";import{j as h,W as oe}from"./constants-C0I8ujwj.js";let P,ne=Promise.all([(()=>{try{return ae}catch{}})(),(()=>{try{return te}catch{}})()]).then(async()=>{let y,C,I,W,E,R,T;y=["onClick"],C={class:"mt-5 mb-3 flex items-center justify-between text-[14px]"},I=["onClick"],W={class:"w-full pt-2 bg-[#f5f7f9] flex justify-center"},E={class:"w-[303px] rounded-full bg-[#DDDFE3] p-1 z-10"},R={class:"px-7 pb-2 flex-grow overflow-y-auto lg:block w-[380px] box-border bg-[#f5f7f9] h-full"},T={class:"flex items-center justify-center mt-3"},P=Z({__name:"Left",props:{isWriting:{type:Boolean}},emits:["submit","example","reset"],setup(ue,{emit:z}){const L=J(),_=z,U=i=>{e.value={...c,...F(oe[i],["data"])},_("example",i)},O=()=>{e.value={...c},_("reset")},n=G(h.WRITING),Y=[{text:"\u64B0\u5199",value:h.WRITING},{text:"\u56DE\u590D",value:h.REPLY}],[$,q]=N(),[B,s]=N(),c={type:1,prompt:"",originalContent:"",tone:1,language:1,length:1,format:1},e=G({...c}),A={},H=()=>{n.value!==2||e.value.originalContent?e.value.prompt?_("submit",{...n.value===1?F(e.value,["originalContent"]):e.value,type:n.value}):L.warning(`\u8BF7\u8F93\u5165${n.value===1?"\u5199\u4F5C":"\u56DE\u590D"}\u5185\u5BB9`):L.warning("\u8BF7\u8F93\u5165\u539F\u6587")};return(i,o)=>{const M=X,b=ee,D=le;return d(),m(f,null,[a(t($),null,{default:p(({active:l,text:r,itemClick:k})=>[u("span",{class:j(["inline-block w-1/2 rounded-full cursor-pointer text-center leading-[30px] relative z-1 text-[5C6370] hover:text-black",l?"text-black shadow-md":"hover:bg-[#DDDFE3]"]),onClick:k},V(r),11,y)]),_:1}),a(t(B),null,{default:p(({label:l,hint:r,hintClick:k})=>[u("h3",C,[u("span",null,V(l),1),r?(d(),m("span",{key:0,onClick:k,class:"flex items-center text-[12px] text-[#846af7] cursor-pointer select-none"},[a(M,{icon:"ep:question-filled"}),w(" "+V(r),1)],8,I)):K("",!0)])]),_:1}),u("div",S({class:"flex flex-col"},i.$attrs),[u("div",W,[u("div",E,[u("div",{class:j(["flex items-center relative after:content-[''] after:block after:bg-white after:h-[30px] after:w-1/2 after:absolute after:top-0 after:left-0 after:transition-transform after:rounded-full",n.value===t(h).REPLY&&"after:transform after:translate-x-[100%]"])},[(d(),m(f,null,Q(Y,l=>a(t(q),{key:l.value,text:l.text,active:l.value===n.value,itemClick:()=>{var r;(r=l.value)!==n.value&&(A[n.value]=e.value,n.value=r,e.value={...c,...A[r]})}},null,8,["text","active","itemClick"])),64))],2)])]),u("div",R,[u("div",null,[n.value===1?(d(),m(f,{key:0},[a(t(s),{label:"\u5199\u4F5C\u5185\u5BB9",hint:"\u793A\u4F8B","hint-click":()=>U("write")},null,8,["hint-click"]),a(b,{type:"textarea",rows:5,maxlength:500,modelValue:e.value.prompt,"onUpdate:modelValue":o[0]||(o[0]=l=>e.value.prompt=l),placeholder:"\u8BF7\u8F93\u5165\u5199\u4F5C\u5185\u5BB9",showWordLimit:""},null,8,["modelValue"])],64)):(d(),m(f,{key:1},[a(t(s),{label:"\u539F\u6587",hint:"\u793A\u4F8B","hint-click":()=>U("reply")},null,8,["hint-click"]),a(b,{type:"textarea",rows:5,maxlength:500,modelValue:e.value.originalContent,"onUpdate:modelValue":o[1]||(o[1]=l=>e.value.originalContent=l),placeholder:"\u8BF7\u8F93\u5165\u539F\u6587",showWordLimit:""},null,8,["modelValue"]),a(t(s),{label:"\u56DE\u590D\u5185\u5BB9"}),a(b,{type:"textarea",rows:5,maxlength:500,modelValue:e.value.prompt,"onUpdate:modelValue":o[2]||(o[2]=l=>e.value.prompt=l),placeholder:"\u8BF7\u8F93\u5165\u56DE\u590D\u5185\u5BB9",showWordLimit:""},null,8,["modelValue"])],64)),a(t(s),{label:"\u957F\u5EA6"}),a(x,{modelValue:e.value.length,"onUpdate:modelValue":o[3]||(o[3]=l=>e.value.length=l),tags:t(v)(t(g).AI_WRITE_LENGTH)},null,8,["modelValue","tags"]),a(t(s),{label:"\u683C\u5F0F"}),a(x,{modelValue:e.value.format,"onUpdate:modelValue":o[4]||(o[4]=l=>e.value.format=l),tags:t(v)(t(g).AI_WRITE_FORMAT)},null,8,["modelValue","tags"]),a(t(s),{label:"\u8BED\u6C14"}),a(x,{modelValue:e.value.tone,"onUpdate:modelValue":o[5]||(o[5]=l=>e.value.tone=l),tags:t(v)(t(g).AI_WRITE_TONE)},null,8,["modelValue","tags"]),a(t(s),{label:"\u8BED\u8A00"}),a(x,{modelValue:e.value.language,"onUpdate:modelValue":o[6]||(o[6]=l=>e.value.language=l),tags:t(v)(t(g).AI_WRITE_LANGUAGE)},null,8,["modelValue","tags"]),u("div",T,[a(D,{disabled:i.isWriting,onClick:O},{default:p(()=>[w("\u91CD\u7F6E")]),_:1},8,["disabled"]),a(D,{loading:i.isWriting,onClick:H,color:"#846af7"},{default:p(()=>[w("\u751F\u6210")]),_:1},8,["loading"])])])])],16)],64)}}})});export{P as _,ne as __tla};
