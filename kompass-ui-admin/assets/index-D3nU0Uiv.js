import{_ as t,__tla as _}from"./index.vue_vue_type_script_setup_true_lang-DUWkJHYe.js";import{__tla as r}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{__tla as a}from"./index-BUSn51wb.js";import{__tla as l}from"./el-card-CJbXGyyg.js";import{__tla as o}from"./desc.vue_vue_type_script_setup_true_lang-DTnf_h5g.js";import{__tla as c}from"./index.vue_vue_type_script_setup_true_lang-CIHu5un_.js";import{__tla as m}from"./lyric.vue_vue_type_script_setup_true_lang-DglaqPdL.js";import{__tla as e}from"./el-space-Dxj8A-LJ.js";let s=Promise.all([(()=>{try{return _}catch{}})(),(()=>{try{return r}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return c}catch{}})(),(()=>{try{return m}catch{}})(),(()=>{try{return e}catch{}})()]).then(async()=>{});export{s as __tla,t as default};
