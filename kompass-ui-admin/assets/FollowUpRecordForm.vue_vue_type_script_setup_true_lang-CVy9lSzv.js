import{by as w,d as A,r as d,f as Q,o as _,c as T,i as e,w as t,a as l,j as y,H as X,l as v,F as x,k as $,V as ee,G as ae,a9 as F,y as le,n as se,I as te,J as oe,K as ue,L as re,E as ce,M as ne,Z as de,cm as ie,cn as pe,_ as _e,N as me,s as fe,O as be,R as ye,__tla as ve}from"./index-BUSn51wb.js";import{_ as Ue,__tla as we}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{B as k,__tla as Ve}from"./index-pKzyIv29.js";import{_ as he,__tla as Re}from"./FollowUpRecordBusinessForm.vue_vue_type_script_setup_true_lang-CrOiARp4.js";import{_ as ge,__tla as Te}from"./FollowUpRecordContactForm.vue_vue_type_script_setup_true_lang-DH5izzTW.js";import{_ as xe,__tla as Fe}from"./BusinessListModal.vue_vue_type_script_setup_true_lang-BvJ2tBPi.js";import{_ as ke,__tla as ze}from"./ContactListModal.vue_vue_type_script_setup_true_lang-Ba5Cb5nV.js";let V,z,Ie=Promise.all([(()=>{try{return ve}catch{}})(),(()=>{try{return we}catch{}})(),(()=>{try{return Ve}catch{}})(),(()=>{try{return Re}catch{}})(),(()=>{try{return Te}catch{}})(),(()=>{try{return Fe}catch{}})(),(()=>{try{return ze}catch{}})()]).then(async()=>{V={getFollowUpRecordPage:async i=>await w.get({url:"/crm/follow-up-record/page",params:i}),createFollowUpRecord:async i=>await w.post({url:"/crm/follow-up-record/create",data:i}),deleteFollowUpRecord:async i=>await w.delete({url:"/crm/follow-up-record/delete?id="+i})},z=A({name:"FollowUpRecordForm",__name:"FollowUpRecordForm",emits:["success"],setup(i,{expose:I,emit:C}){const{t:S}=se(),E=te(),p=d(!1);d("");const f=d(!1),a=d({bizType:void 0,bizId:void 0,businesses:[],contacts:[]}),M=Q({type:[{required:!0,message:"\u8DDF\u8FDB\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],content:[{required:!0,message:"\u8DDF\u8FDB\u5185\u5BB9\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],nextTime:[{required:!0,message:"\u4E0B\u6B21\u8054\u7CFB\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),U=d();I({open:async(u,s)=>{p.value=!0,Y(),a.value.bizType=u,a.value.bizId=s}});const O=C,P=async()=>{await U.value.validate(),f.value=!0;try{const u={...a.value,contactIds:a.value.contacts.map(s=>s.id),businessIds:a.value.businesses.map(s=>s.id)};await V.createFollowUpRecord(u),E.success(S("common.createSuccess")),p.value=!1,O("success")}finally{f.value=!1}},h=d(),q=()=>{var u;(u=h.value)==null||u.open()},L=(u,s)=>{s.forEach(r=>{a.value.contacts.some(m=>m.id===r.id)||a.value.contacts.push(r)})},R=d(),B=()=>{var u;(u=R.value)==null||u.open()},G=(u,s)=>{s.forEach(r=>{a.value.businesses.some(m=>m.id===r.id)||a.value.businesses.push(r)})},Y=()=>{var u;(u=U.value)==null||u.resetFields(),a.value={bizId:void 0,bizType:void 0,businesses:[],contacts:[]}};return(u,s)=>{const r=oe,m=ue,c=re,n=ce,j=ne,D=de,H=ie,J=pe,g=_e,b=me,K=fe,N=be,W=Ue,Z=ye;return _(),T(x,null,[e(W,{modelValue:l(p),"onUpdate:modelValue":s[6]||(s[6]=o=>le(p)?p.value=o:null),title:"\u6DFB\u52A0\u8DDF\u8FDB\u8BB0\u5F55",width:"50%"},{footer:t(()=>[e(b,{disabled:l(f),type:"primary",onClick:P},{default:t(()=>[y("\u786E \u5B9A")]),_:1},8,["disabled"]),e(b,{onClick:s[5]||(s[5]=o=>p.value=!1)},{default:t(()=>[y("\u53D6 \u6D88")]),_:1})]),default:t(()=>[X((_(),v(N,{ref_key:"formRef",ref:U,model:l(a),rules:l(M),"label-width":"120px"},{default:t(()=>[e(K,null,{default:t(()=>[e(n,{span:12},{default:t(()=>[e(c,{label:"\u8DDF\u8FDB\u7C7B\u578B",prop:"type"},{default:t(()=>[e(m,{modelValue:l(a).type,"onUpdate:modelValue":s[0]||(s[0]=o=>l(a).type=o),placeholder:"\u8BF7\u9009\u62E9\u8DDF\u8FDB\u7C7B\u578B"},{default:t(()=>[(_(!0),T(x,null,$(l(ee)(l(ae).CRM_FOLLOW_UP_TYPE),o=>(_(),v(r,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(n,{span:12},{default:t(()=>[e(c,{label:"\u4E0B\u6B21\u8054\u7CFB\u65F6\u95F4",prop:"nextTime"},{default:t(()=>[e(j,{modelValue:l(a).nextTime,"onUpdate:modelValue":s[1]||(s[1]=o=>l(a).nextTime=o),placeholder:"\u9009\u62E9\u4E0B\u6B21\u8054\u7CFB\u65F6\u95F4",type:"date","value-format":"x"},null,8,["modelValue"])]),_:1})]),_:1}),e(n,{span:24},{default:t(()=>[e(c,{label:"\u8DDF\u8FDB\u5185\u5BB9",prop:"content"},{default:t(()=>[e(D,{modelValue:l(a).content,"onUpdate:modelValue":s[2]||(s[2]=o=>l(a).content=o),rows:3,type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1}),e(n,{span:12},{default:t(()=>[e(c,{label:"\u56FE\u7247",prop:"picUrls"},{default:t(()=>[e(H,{modelValue:l(a).picUrls,"onUpdate:modelValue":s[3]||(s[3]=o=>l(a).picUrls=o),class:"min-w-80px"},null,8,["modelValue"])]),_:1})]),_:1}),e(n,{span:12},{default:t(()=>[e(c,{label:"\u9644\u4EF6",prop:"fileUrls"},{default:t(()=>[e(J,{modelValue:l(a).fileUrls,"onUpdate:modelValue":s[4]||(s[4]=o=>l(a).fileUrls=o),class:"min-w-80px"},null,8,["modelValue"])]),_:1})]),_:1}),l(a).bizType==l(k).CRM_CUSTOMER?(_(),v(n,{key:0,span:24},{default:t(()=>[e(c,{label:"\u5173\u8054\u8054\u7CFB\u4EBA",prop:"contactIds"},{default:t(()=>[e(b,{onClick:q},{default:t(()=>[e(g,{class:"mr-5px",icon:"ep:plus"}),y(" \u6DFB\u52A0\u8054\u7CFB\u4EBA ")]),_:1}),e(ge,{contacts:l(a).contacts},null,8,["contacts"])]),_:1})]),_:1})):F("",!0),l(a).bizType==l(k).CRM_CUSTOMER?(_(),v(n,{key:1,span:24},{default:t(()=>[e(c,{label:"\u5173\u8054\u5546\u673A",prop:"businessIds"},{default:t(()=>[e(b,{onClick:B},{default:t(()=>[e(g,{class:"mr-5px",icon:"ep:plus"}),y(" \u6DFB\u52A0\u5546\u673A ")]),_:1}),e(he,{businesses:l(a).businesses},null,8,["businesses"])]),_:1})]),_:1})):F("",!0)]),_:1})]),_:1},8,["model","rules"])),[[Z,l(f)]])]),_:1},8,["modelValue"]),e(ke,{ref_key:"contactTableSelectRef",ref:h,"customer-id":l(a).bizId,onSuccess:L},null,8,["customer-id"]),e(xe,{ref_key:"businessTableSelectRef",ref:R,"customer-id":l(a).bizId,onSuccess:G},null,8,["customer-id"])],64)}}})});export{V as F,z as _,Ie as __tla};
