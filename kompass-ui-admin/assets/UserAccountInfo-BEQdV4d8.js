import{d as p,r as f,at as v,o as h,l as g,w as a,i as e,a as l,j as r,t as _,aF as i,B as y,__tla as x}from"./index-BUSn51wb.js";import{E,a as I,__tla as w}from"./el-descriptions-item-dD3qa0ub.js";import{__tla as P}from"./Descriptions.vue_vue_type_style_index_0_scoped_30b8da63_lang-DDC-j81O.js";import{D as n,__tla as R}from"./DescriptionsItemLabel-CVTt7Fgq.js";import{g as j,__tla as A}from"./index-BThBT0Wa.js";import{__tla as B}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import"./color-BN7ZL7BD.js";let m,D=Promise.all([(()=>{try{return x}catch{}})(),(()=>{try{return w}catch{}})(),(()=>{try{return P}catch{}})(),(()=>{try{return R}catch{}})(),(()=>{try{return A}catch{}})(),(()=>{try{return B}catch{}})()]).then(async()=>{m=y(p({__name:"UserAccountInfo",props:{user:{}},setup(b){const c=b,u={balance:0,totalExpense:0,totalRecharge:0},o=f(u);return v(()=>c.user.id,()=>(async()=>{if(!c.user.id)return void(o.value=u);const s={userId:c.user.id};o.value=await j(s)||u})(),{immediate:!0}),(s,F)=>{const t=E,d=I;return h(),g(d,{column:2},{default:a(()=>[e(t,null,{label:a(()=>[e(l(n),{label:" \u7B49\u7EA7 ",icon:"svg-icon:member_level"})]),default:a(()=>[r(" "+_(s.user.levelName||"\u65E0"),1)]),_:1}),e(t,null,{label:a(()=>[e(l(n),{label:" \u6210\u957F\u503C ",icon:"ep:suitcase"})]),default:a(()=>[r(" "+_(s.user.experience||0),1)]),_:1}),e(t,null,{label:a(()=>[e(l(n),{label:" \u5F53\u524D\u79EF\u5206 ",icon:"ep:coin"})]),default:a(()=>[r(" "+_(s.user.point||0),1)]),_:1}),e(t,null,{label:a(()=>[e(l(n),{label:" \u603B\u79EF\u5206 ",icon:"ep:coin"})]),default:a(()=>[r(" "+_(s.user.totalPoint||0),1)]),_:1}),e(t,null,{label:a(()=>[e(l(n),{label:" \u5F53\u524D\u4F59\u989D ",icon:"svg-icon:member_balance"})]),default:a(()=>[r(" "+_(l(i)(l(o).balance||0)),1)]),_:1}),e(t,null,{label:a(()=>[e(l(n),{label:" \u652F\u51FA\u91D1\u989D ",icon:"svg-icon:member_expenditure_balance"})]),default:a(()=>[r(" "+_(l(i)(l(o).totalExpense||0)),1)]),_:1}),e(t,null,{label:a(()=>[e(l(n),{label:" \u5145\u503C\u91D1\u989D ",icon:"svg-icon:member_recharge_balance"})]),default:a(()=>[r(" "+_(l(i)(l(o).totalRecharge||0)),1)]),_:1})]),_:1})}}}),[["__scopeId","data-v-db451df2"]])});export{D as __tla,m as default};
