import{d as G,I as K,n as j,r as n,f as q,C as E,T as J,o as i,c as V,i as e,w as t,a as l,U as Q,F as N,k as Z,l as w,j as y,H as T,g as W,t as X,G as $,Z as ee,L as ae,J as le,K as te,M as re,_ as se,N as oe,O as ne,P as ie,Q as de,R as ce,__tla as pe}from"./index-BUSn51wb.js";import{_ as ue,__tla as _e}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as me,__tla as fe}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as ge,__tla as he}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{d as we,__tla as ye}from"./formatTime-DWdBpgsM.js";import{C as M,__tla as be}from"./index-CBZlCJOz.js";import{g as ve,__tla as Ie}from"./index-BYXzDB8j.js";let U,xe=Promise.all([(()=>{try{return pe}catch{}})(),(()=>{try{return _e}catch{}})(),(()=>{try{return fe}catch{}})(),(()=>{try{return he}catch{}})(),(()=>{try{return ye}catch{}})(),(()=>{try{return be}catch{}})(),(()=>{try{return Ie}catch{}})()]).then(async()=>{U=G({__name:"ChatMessageList",setup(Ce){const b=K(),{t:P}=j(),p=n(!0),v=n([]),I=n(0),r=q({pageNo:1,pageSize:10,conversationId:void 0,userId:void 0,content:void 0,createTime:[]}),x=n(),u=n([]),d=async()=>{p.value=!0;try{const m=await M.getChatMessagePage(r);v.value=m.list,I.value=m.total}finally{p.value=!1}},_=()=>{r.pageNo=1,d()},S=()=>{x.value.resetFields(),_()};return E(async()=>{d(),u.value=await ve()}),(m,o)=>{const Y=ee,c=ae,z=le,D=te,F=re,C=se,f=oe,R=ne,k=ge,s=ie,A=me,H=de,L=ue,O=J("hasPermi"),B=ce;return i(),V(N,null,[e(k,null,{default:t(()=>[e(R,{class:"-mb-15px",model:l(r),ref_key:"queryFormRef",ref:x,inline:!0,"label-width":"68px"},{default:t(()=>[e(c,{label:"\u5BF9\u8BDD\u7F16\u53F7",prop:"conversationId"},{default:t(()=>[e(Y,{modelValue:l(r).conversationId,"onUpdate:modelValue":o[0]||(o[0]=a=>l(r).conversationId=a),placeholder:"\u8BF7\u8F93\u5165\u5BF9\u8BDD\u7F16\u53F7",clearable:"",onKeyup:Q(_,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(c,{label:"\u7528\u6237\u7F16\u53F7",prop:"userId"},{default:t(()=>[e(D,{modelValue:l(r).userId,"onUpdate:modelValue":o[1]||(o[1]=a=>l(r).userId=a),clearable:"",placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u7F16\u53F7",class:"!w-240px"},{default:t(()=>[(i(!0),V(N,null,Z(l(u),a=>(i(),w(z,{key:a.id,label:a.nickname,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(c,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:t(()=>[e(F,{modelValue:l(r).createTime,"onUpdate:modelValue":o[2]||(o[2]=a=>l(r).createTime=a),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),e(c,null,{default:t(()=>[e(f,{onClick:_},{default:t(()=>[e(C,{icon:"ep:search",class:"mr-5px"}),y(" \u641C\u7D22")]),_:1}),e(f,{onClick:S},{default:t(()=>[e(C,{icon:"ep:refresh",class:"mr-5px"}),y(" \u91CD\u7F6E")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(k,null,{default:t(()=>[T((i(),w(H,{data:l(v),stripe:!0,"show-overflow-tooltip":!0},{default:t(()=>[e(s,{label:"\u6D88\u606F\u7F16\u53F7",align:"center",prop:"id",width:"180",fixed:"left"}),e(s,{label:"\u5BF9\u8BDD\u7F16\u53F7",align:"center",prop:"conversationId",width:"180",fixed:"left"}),e(s,{label:"\u7528\u6237",align:"center",prop:"userId",width:"180"},{default:t(a=>{var g;return[W("span",null,X((g=l(u).find(h=>h.id===a.row.userId))==null?void 0:g.nickname),1)]}),_:1}),e(s,{label:"\u89D2\u8272",align:"center",prop:"roleName",width:"180"}),e(s,{label:"\u6D88\u606F\u7C7B\u578B",align:"center",prop:"type",width:"100"}),e(s,{label:"\u6A21\u578B\u6807\u8BC6",align:"center",prop:"model",width:"180"}),e(s,{label:"\u6D88\u606F\u5185\u5BB9",align:"center",prop:"content",width:"300"}),e(s,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:l(we),width:"180px"},null,8,["formatter"]),e(s,{label:"\u56DE\u590D\u6D88\u606F\u7F16\u53F7",align:"center",prop:"replyId",width:"180"}),e(s,{label:"\u643A\u5E26\u4E0A\u4E0B\u6587",align:"center",prop:"useContext",width:"100"},{default:t(a=>[e(A,{type:l($).INFRA_BOOLEAN_STRING,value:a.row.useContext},null,8,["type","value"])]),_:1}),e(s,{label:"\u64CD\u4F5C",align:"center",fixed:"right"},{default:t(a=>[T((i(),w(f,{link:"",type:"danger",onClick:g=>(async h=>{try{await b.delConfirm(),await M.deleteChatMessageByAdmin(h),b.success(P("common.delSuccess")),await d()}catch{}})(a.row.id)},{default:t(()=>[y(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[O,["ai:chat-message:delete"]]])]),_:1})]),_:1},8,["data"])),[[B,l(p)]]),e(L,{total:l(I),page:l(r).pageNo,"onUpdate:page":o[3]||(o[3]=a=>l(r).pageNo=a),limit:l(r).pageSize,"onUpdate:limit":o[4]||(o[4]=a=>l(r).pageSize=a),onPagination:d},null,8,["total","page","limit"])]),_:1})],64)}}})});export{U as _,xe as __tla};
