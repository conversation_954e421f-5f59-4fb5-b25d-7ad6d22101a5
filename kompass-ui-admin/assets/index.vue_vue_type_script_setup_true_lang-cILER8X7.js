import{d as Q,r as m,f as W,u as Y,at as q,o as n,c as g,i as a,w as l,j as y,H as A,a as e,l as c,G as J,F as k,k as I,t as R,a9 as U,I as K,n as V,_ as X,N as Z,s as aa,P as ea,v as ta,Q as la,R as ra,__tla as sa}from"./index-BUSn51wb.js";import{_ as ia,__tla as na}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as oa,__tla as pa}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as _a,__tla as ca}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{d as N,__tla as ua}from"./formatTime-DWdBpgsM.js";import{_ as da,F as S,__tla as ma}from"./FollowUpRecordForm.vue_vue_type_script_setup_true_lang-CVy9lSzv.js";import{B as F,__tla as ya}from"./index-pKzyIv29.js";let P,fa=Promise.all([(()=>{try{return sa}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return pa}catch{}})(),(()=>{try{return ca}catch{}})(),(()=>{try{return ua}catch{}})(),(()=>{try{return ma}catch{}})(),(()=>{try{return ya}catch{}})()]).then(async()=>{P=Q({name:"FollowUpRecord",__name:"index",props:{bizType:{},bizId:{}},setup(M){const p=M,w=K(),{t:O}=V(),f=m(!0),z=m([]),C=m(0),o=W({pageNo:1,pageSize:10,bizType:0,bizId:0}),u=async()=>{f.value=!0;try{const r=await S.getFollowUpRecordPage(o);z.value=r.list,C.value=r.total}finally{f.value=!1}},v=m(),E=()=>{var r;(r=v.value)==null||r.open(p.bizType,p.bizId)},{push:h}=Y();return q(()=>p.bizId,()=>{o.bizType=p.bizType,o.bizId=p.bizId,u()}),(r,d)=>{const j=X,T=Z,B=aa,s=ea,D=_a,x=ta,L=la,$=oa,G=ia,H=ra;return n(),g(k,null,[a(B,{class:"mb-10px",justify:"end"},{default:l(()=>[a(T,{onClick:E},{default:l(()=>[a(j,{class:"mr-5px",icon:"ep:edit"}),y(" \u5199\u8DDF\u8FDB ")]),_:1})]),_:1}),a(G,null,{default:l(()=>[A((n(),c(L,{data:e(z),"show-overflow-tooltip":!0,stripe:!0},{default:l(()=>[a(s,{formatter:e(N),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"]),a(s,{align:"center",label:"\u8DDF\u8FDB\u4EBA",prop:"creatorName"}),a(s,{align:"center",label:"\u8DDF\u8FDB\u7C7B\u578B",prop:"type"},{default:l(t=>[a(D,{type:e(J).CRM_FOLLOW_UP_TYPE,value:t.row.type},null,8,["type","value"])]),_:1}),a(s,{align:"center",label:"\u8DDF\u8FDB\u5185\u5BB9",prop:"content"}),a(s,{formatter:e(N),align:"center",label:"\u4E0B\u6B21\u8054\u7CFB\u65F6\u95F4",prop:"nextTime",width:"180px"},null,8,["formatter"]),r.bizType===e(F).CRM_CUSTOMER?(n(),c(s,{key:0,align:"center",label:"\u5173\u8054\u8054\u7CFB\u4EBA",prop:"contactIds"},{default:l(t=>[(n(!0),g(k,null,I(t.row.contacts,i=>(n(),c(x,{key:`key-${i.id}`,underline:!1,type:"primary",onClick:b=>{return _=i.id,void h({name:"CrmContactDetail",params:{id:_}});var _},class:"ml-5px"},{default:l(()=>[y(R(i.name),1)]),_:2},1032,["onClick"]))),128))]),_:1})):U("",!0),r.bizType===e(F).CRM_CUSTOMER?(n(),c(s,{key:1,align:"center",label:"\u5173\u8054\u5546\u673A",prop:"businessIds"},{default:l(t=>[(n(!0),g(k,null,I(t.row.businesses,i=>(n(),c(x,{key:`key-${i.id}`,underline:!1,type:"primary",onClick:b=>{return _=i.id,void h({name:"CrmBusinessDetail",params:{id:_}});var _},class:"ml-5px"},{default:l(()=>[y(R(i.name),1)]),_:2},1032,["onClick"]))),128))]),_:1})):U("",!0),a(s,{align:"center",label:"\u64CD\u4F5C"},{default:l(t=>[a(T,{link:"",type:"danger",onClick:i=>(async b=>{try{await w.delConfirm(),await S.deleteFollowUpRecord(b),w.success(O("common.delSuccess")),await u()}catch{}})(t.row.id)},{default:l(()=>[y(" \u5220\u9664 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[H,e(f)]]),a($,{limit:e(o).pageSize,"onUpdate:limit":d[0]||(d[0]=t=>e(o).pageSize=t),page:e(o).pageNo,"onUpdate:page":d[1]||(d[1]=t=>e(o).pageNo=t),total:e(C),onPagination:u},null,8,["limit","page","total"])]),_:1}),a(da,{ref_key:"formRef",ref:v,onSuccess:u},null,512)],64)}}})});export{P as _,fa as __tla};
