import{d as m,r as _,C as y,o as l,c as p,i as n,w as f,a as r,H as h,l as k,a9 as d,F as g,R as v,__tla as w}from"./index-BUSn51wb.js";import{_ as x,__tla as b}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as j,__tla as C}from"./IFrame.vue_vue_type_script_setup_true_lang-silYuU_p.js";import{_ as F,__tla as H}from"./index-COobLwz-.js";import{b as I,__tla as P}from"./index-BXfU_lLO.js";import{__tla as R}from"./el-card-CJbXGyyg.js";let e,S=Promise.all([(()=>{try{return w}catch{}})(),(()=>{try{return b}catch{}})(),(()=>{try{return C}catch{}})(),(()=>{try{return H}catch{}})(),(()=>{try{return P}catch{}})(),(()=>{try{return R}catch{}})()]).then(async()=>{e=m({name:"InfraSkyWalking",__name:"index",setup(W){const t=_(!0),s=_("http://skywalking.shop.iocoder.cn");return y(async()=>{try{const a=await I("url.skywalking");a&&a.length>0&&(s.value=a)}finally{t.value=!1}}),(a,q)=>{const o=F,c=j,i=x,u=v;return l(),p(g,null,[n(o,{title:"\u670D\u52A1\u76D1\u63A7",url:"https://doc.iocoder.cn/server-monitor/"}),n(i,null,{default:f(()=>[r(t)?d("",!0):h((l(),k(c,{key:0,src:r(s)},null,8,["src"])),[[u,r(t)]])]),_:1})],64)}}})});export{S as __tla,e as default};
