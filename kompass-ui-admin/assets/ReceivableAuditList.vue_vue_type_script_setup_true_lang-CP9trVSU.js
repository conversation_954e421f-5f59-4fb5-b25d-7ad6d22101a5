import{d as j,r as c,f as G,u as H,bc as J,C as K,T as O,o as s,c as k,i as a,w as l,a as t,F as N,k as Q,l as g,H as S,j as m,t as h,dV as T,G as R,g as Y,J as W,K as X,L as Z,O as $,v as aa,P as ea,N as ta,Q as la,R as ra,__tla as ia}from"./index-BUSn51wb.js";import{_ as na,__tla as oa}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as pa,__tla as sa}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as ua,__tla as da}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{b as ca,d as U,__tla as ma}from"./formatTime-DWdBpgsM.js";import{b as _a,__tla as fa}from"./index-D3Ji6shA.js";import{A as wa}from"./common-BQQO87UM.js";let I,ga=Promise.all([(()=>{try{return ia}catch{}})(),(()=>{try{return oa}catch{}})(),(()=>{try{return sa}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return ma}catch{}})(),(()=>{try{return fa}catch{}})()]).then(async()=>{let y;y=Y("div",{class:"pb-5 text-xl"}," \u5F85\u5BA1\u6838\u56DE\u6B3E ",-1),I=j({name:"CrmReceivableAuditList",__name:"ReceivableAuditList",setup(ha){const _=c(!0),b=c(0),v=c([]),n=G({pageNo:1,pageSize:10,auditStatus:10}),P=c(),u=async()=>{_.value=!0;try{const f=await _a(n);v.value=f.list,b.value=f.total}finally{_.value=!1}},A=()=>{n.pageNo=1,u()},{push:d}=H();return J(async()=>{await u()}),K(()=>{u()}),(f,o)=>{const D=W,E=X,V=Z,z=$,C=ua,w=aa,r=ea,x=pa,L=ta,q=la,B=na,F=O("hasPermi"),M=ra;return s(),k(N,null,[a(C,null,{default:l(()=>[y,a(z,{class:"-mb-15px",model:t(n),ref_key:"queryFormRef",ref:P,inline:!0,"label-width":"68px"},{default:l(()=>[a(V,{label:"\u5408\u540C\u72B6\u6001",prop:"auditStatus"},{default:l(()=>[a(E,{modelValue:t(n).auditStatus,"onUpdate:modelValue":o[0]||(o[0]=e=>t(n).auditStatus=e),class:"!w-240px",placeholder:"\u72B6\u6001",onChange:A},{default:l(()=>[(s(!0),k(N,null,Q(t(wa),(e,p)=>(s(),g(D,{label:e.label,value:e.value,key:p},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),a(C,null,{default:l(()=>[S((s(),g(q,{data:t(v),stripe:!0,"show-overflow-tooltip":!0},{default:l(()=>[a(r,{align:"center",fixed:"left",label:"\u56DE\u6B3E\u7F16\u53F7",prop:"no",width:"180"},{default:l(e=>[a(w,{underline:!1,type:"primary",onClick:p=>{return i=e.row.id,void d({name:"CrmReceivableDetail",params:{id:i}});var i}},{default:l(()=>[m(h(e.row.no),1)]),_:2},1032,["onClick"])]),_:1}),a(r,{align:"center",label:"\u5BA2\u6237\u540D\u79F0",prop:"customerName",width:"120"},{default:l(e=>[a(w,{underline:!1,type:"primary",onClick:p=>{return i=e.row.customerId,void d({name:"CrmCustomerDetail",params:{id:i}});var i}},{default:l(()=>[m(h(e.row.customerName),1)]),_:2},1032,["onClick"])]),_:1}),a(r,{align:"center",label:"\u5408\u540C\u7F16\u53F7",prop:"contractNo",width:"180"},{default:l(e=>[a(w,{underline:!1,type:"primary",onClick:p=>{return i=e.row.contractId,void d({name:"CrmContractDetail",params:{id:i}});var i}},{default:l(()=>[m(h(e.row.contract.no),1)]),_:2},1032,["onClick"])]),_:1}),a(r,{formatter:t(ca),align:"center",label:"\u56DE\u6B3E\u65E5\u671F",prop:"returnTime",width:"150px"},null,8,["formatter"]),a(r,{align:"center",label:"\u56DE\u6B3E\u91D1\u989D(\u5143)",prop:"price",width:"140",formatter:t(T)},null,8,["formatter"]),a(r,{align:"center",label:"\u56DE\u6B3E\u65B9\u5F0F",prop:"returnType",width:"130px"},{default:l(e=>[a(x,{type:t(R).CRM_RECEIVABLE_RETURN_TYPE,value:e.row.returnType},null,8,["type","value"])]),_:1}),a(r,{align:"center",label:"\u5907\u6CE8",prop:"remark",width:"200"}),a(r,{align:"center",label:"\u5408\u540C\u91D1\u989D\uFF08\u5143\uFF09",prop:"contract.totalPrice",width:"140",formatter:t(T)},null,8,["formatter"]),a(r,{align:"center",label:"\u8D1F\u8D23\u4EBA",prop:"ownerUserName",width:"120"}),a(r,{align:"center",label:"\u6240\u5C5E\u90E8\u95E8",prop:"ownerUserDeptName",width:"100px"}),a(r,{formatter:t(U),align:"center",label:"\u66F4\u65B0\u65F6\u95F4",prop:"updateTime",width:"180px"},null,8,["formatter"]),a(r,{formatter:t(U),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"]),a(r,{align:"center",label:"\u521B\u5EFA\u4EBA",prop:"creatorName",width:"120"}),a(r,{align:"center",fixed:"right",label:"\u56DE\u6B3E\u72B6\u6001",prop:"auditStatus",width:"120"},{default:l(e=>[a(x,{type:t(R).CRM_AUDIT_STATUS,value:e.row.auditStatus},null,8,["type","value"])]),_:1}),a(r,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"180px"},{default:l(e=>[S((s(),g(L,{link:"",type:"primary",onClick:p=>{return i=e.row,void d({name:"BpmProcessInstanceDetail",query:{id:i.processInstanceId}});var i}},{default:l(()=>[m(" \u67E5\u770B\u5BA1\u6279 ")]),_:2},1032,["onClick"])),[[F,["crm:receivable:update"]]])]),_:1})]),_:1},8,["data"])),[[M,t(_)]]),a(B,{total:t(b),page:t(n).pageNo,"onUpdate:page":o[1]||(o[1]=e=>t(n).pageNo=e),limit:t(n).pageSize,"onUpdate:limit":o[2]||(o[2]=e=>t(n).pageSize=e),onPagination:u},null,8,["total","page","limit"])]),_:1})],64)}}})});export{I as _,ga as __tla};
