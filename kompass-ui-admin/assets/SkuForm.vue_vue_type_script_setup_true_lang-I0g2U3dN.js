import{d as A,p as q,r as _,f as C,at as I,o as i,c as P,i as t,w as s,a as e,F as D,O as K,j as u,l as k,a9 as n,I as z,dK as E,am as G,an as H,L as J,N as M,__tla as Q}from"./index-BUSn51wb.js";import{g as W,__tla as X}from"./index-CjyLHUq3.js";import{_ as Y,__tla as Z}from"./ProductAttributes.vue_vue_type_script_setup_true_lang-HWrQWq3l.js";import{_ as $,__tla as ee}from"./ProductPropertyAddForm.vue_vue_type_script_setup_true_lang-BR4HNBkA.js";import{r as w,__tla as ae}from"./formRules-CA9eXdcX.js";import b,{__tla as te}from"./SkuList-DG93D6KA.js";let F,re=Promise.all([(()=>{try{return Q}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return Z}catch{}})(),(()=>{try{return ee}catch{}})(),(()=>{try{return ae}catch{}})(),(()=>{try{return te}catch{}})()]).then(async()=>{F=A({name:"ProductSpuSkuForm",__name:"SkuForm",props:{propFormData:{type:Object,default:()=>{}},isDetail:q.bool.def(!1)},emits:["update:activeName"],setup(p,{expose:V,emit:x}){const g=[{name:"stock",rule:a=>a>=0,message:"\u5546\u54C1\u5E93\u5B58\u5FC5\u987B\u5927\u4E8E\u7B49\u4E8E 1 \uFF01\uFF01\uFF01"},{name:"price",rule:a=>a>=.01,message:"\u5546\u54C1\u9500\u552E\u4EF7\u683C\u5FC5\u987B\u5927\u4E8E\u7B49\u4E8E 0.01 \u5143\uFF01\uFF01\uFF01"},{name:"marketPrice",rule:a=>a>=.01,message:"\u5546\u54C1\u5E02\u573A\u4EF7\u683C\u5FC5\u987B\u5927\u4E8E\u7B49\u4E8E 0.01 \u5143\uFF01\uFF01\uFF01"},{name:"costPrice",rule:a=>a>=.01,message:"\u5546\u54C1\u6210\u672C\u4EF7\u683C\u5FC5\u987B\u5927\u4E8E\u7B49\u4E8E 0.00 \u5143\uFF01\uFF01\uFF01"}],L=z(),h=p,T=_(),f=_(),l=_([]),m=_(),r=C({specType:!1,subCommissionType:!1,skus:[]}),S=C({specType:[w],subCommissionType:[w]});I(()=>h.propFormData,a=>{a&&(E(r,a),l.value=W(a))},{immediate:!0});const B=x;V({validate:async()=>{if(f)try{m.value.validateSku(),await e(f).validate(),Object.assign(h.propFormData,r)}catch(a){throw L.error("\u3010\u5E93\u5B58\u4EF7\u683C\u3011\u4E0D\u5B8C\u5584\uFF0C\u8BF7\u586B\u5199\u76F8\u5173\u4FE1\u606F"),B("update:activeName","sku"),a}}});const R=()=>{for(const a of r.skus)a.firstBrokeragePrice=0,a.secondBrokeragePrice=0},j=()=>{l.value=[],r.skus=[{price:0,marketPrice:0,costPrice:0,barCode:"",picUrl:"",stock:0,weight:0,volume:0,firstBrokeragePrice:0,secondBrokeragePrice:0}]},N=a=>{m.value.generateTableData(a)};return(a,c)=>{const d=G,v=H,o=J,O=M,U=K;return i(),P(D,null,[t(U,{ref_key:"formRef",ref:f,disabled:p.isDetail,model:e(r),rules:e(S),"label-width":"120px"},{default:s(()=>[t(o,{label:"\u5206\u9500\u7C7B\u578B",props:"subCommissionType"},{default:s(()=>[t(v,{modelValue:e(r).subCommissionType,"onUpdate:modelValue":c[0]||(c[0]=y=>e(r).subCommissionType=y),class:"w-80",onChange:R},{default:s(()=>[t(d,{label:!1},{default:s(()=>[u("\u9ED8\u8BA4\u8BBE\u7F6E")]),_:1}),t(d,{label:!0,class:"radio"},{default:s(()=>[u("\u5355\u72EC\u8BBE\u7F6E")]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(o,{label:"\u5546\u54C1\u89C4\u683C",props:"specType"},{default:s(()=>[t(v,{modelValue:e(r).specType,"onUpdate:modelValue":c[1]||(c[1]=y=>e(r).specType=y),class:"w-80",onChange:j},{default:s(()=>[t(d,{label:!1,class:"radio"},{default:s(()=>[u("\u5355\u89C4\u683C")]),_:1}),t(d,{label:!0},{default:s(()=>[u("\u591A\u89C4\u683C")]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(r).specType?n("",!0):(i(),k(o,{key:0},{default:s(()=>[t(e(b),{ref_key:"skuListRef",ref:m,"prop-form-data":e(r),"property-list":e(l),"rule-config":g},null,8,["prop-form-data","property-list"])]),_:1})),e(r).specType?(i(),k(o,{key:1,label:"\u5546\u54C1\u5C5E\u6027"},{default:s(()=>[t(O,{class:"mb-10px mr-15px",onClick:e(T).open},{default:s(()=>[u("\u6DFB\u52A0\u5C5E\u6027")]),_:1},8,["onClick"]),t(Y,{"is-detail":p.isDetail,"property-list":e(l),onSuccess:N},null,8,["is-detail","property-list"])]),_:1})):n("",!0),e(r).specType&&e(l).length>0?(i(),P(D,{key:2},[p.isDetail?n("",!0):(i(),k(o,{key:0,label:"\u6279\u91CF\u8BBE\u7F6E"},{default:s(()=>[t(e(b),{"is-batch":!0,"prop-form-data":e(r),"property-list":e(l)},null,8,["prop-form-data","property-list"])]),_:1})),t(o,{label:"\u89C4\u683C\u5217\u8868"},{default:s(()=>[t(e(b),{ref_key:"skuListRef",ref:m,"is-detail":p.isDetail,"prop-form-data":e(r),"property-list":e(l),"rule-config":g},null,8,["is-detail","prop-form-data","property-list"])]),_:1})],64)):n("",!0)]),_:1},8,["disabled","model","rules"]),t($,{ref_key:"attributesAddFormRef",ref:T,propertyList:e(l)},null,8,["propertyList"])],64)}}})});export{F as _,re as __tla};
