import{d as ee,r as le,o as u,l as A,w as i,g as e,av as f,a,i as t,t as s,G as m,c as h,a9 as I,k as ae,j as R,F as te,aB as X,aV as se,aJ as re,_ as ie,aN as pe,ax as ne,P as oe,Q as de,a5 as ce,a6 as ue,B as fe,__tla as ye}from"./index-BUSn51wb.js";import{E as me,__tla as we}from"./el-image-BjHZRFih.js";import{_ as _e,__tla as xe}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{D as G}from"./constants-A8BI3pz7.js";import{f as H,__tla as ve}from"./formatTime-DWdBpgsM.js";import"./color-BN7ZL7BD.js";let J,he=Promise.all([(()=>{try{return ye}catch{}})(),(()=>{try{return we}catch{}})(),(()=>{try{return xe}catch{}})(),(()=>{try{return ve}catch{}})()]).then(async()=>{let S,D,U,C,N,g,j,P,L,O,Y,M,B,F,Q,V,W;S=w=>(ce("data-v-6a0a3cbd"),w=w(),ue(),w),D={class:"flex items-center",style:{width:"100%"}},U={class:"mr-[20px] h-[35px] flex items-center pl-[10px] pr-[10px]",style:{"background-color":"#f7f7f7"}},C={class:"mr-20px"},N={class:"mr-20px"},g=S(()=>e("span",null,"\u8BA2\u5355\u6765\u6E90\uFF1A",-1)),j=S(()=>e("span",null,"\u652F\u4ED8\u65B9\u5F0F\uFF1A",-1)),P={key:1,class:"mr-20px"},L={key:2,class:"mr-20px"},O=S(()=>e("span",null,"\u8BA2\u5355\u7C7B\u578B\uFF1A",-1)),Y={class:"flex flex-wrap"},M={class:"mb-[10px] mr-[10px] flex items-start"},B={class:"mr-[10px]"},F={class:"image-slot"},Q={class:"overflow-ellipsis max-h-[45px] overflow-hidden"},V={key:0,class:"flex flex-col"},W={key:1,class:"flex flex-col"},J=fe(ee({name:"OrderTableColumn",__name:"OrderTableColumn",props:{list:{},pickUpStoreList:{}},setup(w){const K=w,$=({row:r,columnIndex:c})=>{if(c!==0)return r[c].colSpan=0,{display:"none"};r[c].colSpan=8},q=({row:r,rowIndex:c,columnIndex:o})=>{var E,T;const _=(T=(E=K.list.find(b=>{var n;return((n=b.items)==null?void 0:n.findIndex(k=>k.id===r.id))!==-1}))==null?void 0:E.items)==null?void 0:T.length;if([3,4,5,6,7].includes(o))return c!==0?{rowspan:0,colspan:0}:{rowspan:_,colspan:1}},z=r=>{r&&r.tableId==="el-table_2"&&Z(r)},d=le([300,150,120,120,160,120,120,160]),Z=r=>{const c=r.store.states.columns.value;c.length!==0&&c.forEach((o,_)=>{o.realWidth&&(d.value[_]=o.realWidth)})};return(r,c)=>{const o=_e,_=ie,E=me,T=pe,b=ne,n=oe,k=de;return u(),A(n,{"class-name":"order-table-col"},{header:i(()=>[e("div",D,[e("div",{style:f({width:a(d)[0]+"px"}),class:"flex justify-center"}," \u5546\u54C1\u4FE1\u606F ",4),e("div",{style:f({width:a(d)[1]+"px"}),class:"flex justify-center"}," \u5355\u4EF7(\u5143)/\u6570\u91CF ",4),e("div",{style:f({width:a(d)[2]+"px"}),class:"flex justify-center"}," \u552E\u540E\u72B6\u6001 ",4),e("div",{style:f({width:a(d)[3]+"px"}),class:"flex justify-center"}," \u5B9E\u4ED8\u91D1\u989D(\u5143) ",4),e("div",{style:f({width:a(d)[4]+"px"}),class:"flex justify-center"}," \u4E70\u5BB6/\u6536\u8D27\u4EBA ",4),e("div",{style:f({width:a(d)[5]+"px"}),class:"flex justify-center"}," \u914D\u9001\u65B9\u5F0F ",4),e("div",{style:f({width:a(d)[6]+"px"}),class:"flex justify-center"}," \u8BA2\u5355\u72B6\u6001 ",4),e("div",{style:f({width:a(d)[7]+"px"}),class:"flex justify-center"}," \u64CD\u4F5C ",4)])]),default:i(l=>[t(k,{ref:z,border:!0,data:l.row.items,"header-cell-style":$,"span-method":q,style:{width:"100%"}},{default:i(()=>[t(n,{"min-width":"300",prop:"spuName"},{header:i(()=>[e("div",U,[e("span",C,"\u8BA2\u5355\u53F7\uFF1A"+s(l.row.no),1),e("span",N,"\u4E0B\u5355\u65F6\u95F4\uFF1A"+s(a(H)(l.row.createTime)),1),g,t(o,{type:a(m).TERMINAL,value:l.row.terminal,class:"mr-20px"},null,8,["type","value"]),j,l.row.payChannelCode?(u(),A(o,{key:0,type:a(m).PAY_CHANNEL_CODE,value:l.row.payChannelCode,class:"mr-20px"},null,8,["type","value"])):(u(),h("span",P,"\u672A\u652F\u4ED8")),l.row.payTime?(u(),h("span",L," \u652F\u4ED8\u65F6\u95F4\uFF1A"+s(a(H)(l.row.payTime)),1)):I("",!0),O,t(o,{type:a(m).TRADE_ORDER_TYPE,value:l.row.type},null,8,["type","value"])])]),default:i(({row:p})=>[e("div",Y,[e("div",M,[e("div",B,[t(E,{src:p.picUrl,class:"!h-[45px] !w-[45px]",fit:"contain",onClick:y=>{return x=p.picUrl,void re({urlList:[x]});var x}},{error:i(()=>[e("div",F,[t(_,{icon:"ep:picture"})])]),_:2},1032,["src","onClick"])]),t(T,{content:p.spuName,placement:"top"},{default:i(()=>[e("span",Q,s(p.spuName),1)]),_:2},1032,["content"])]),(u(!0),h(te,null,ae(p.properties,y=>(u(),A(b,{key:y.propertyId,class:"mb-[10px] mr-[10px]"},{default:i(()=>[R(s(y.propertyName)+": "+s(y.valueName),1)]),_:2},1024))),128))])]),_:2},1024),t(n,{label:"\u5546\u54C1\u539F\u4EF7*\u6570\u91CF",prop:"price",width:"150"},{default:i(({row:p})=>[R(s(a(X)(p.price))+" \u5143 / "+s(p.count),1)]),_:1}),t(n,{label:"\u552E\u540E\u72B6\u6001",prop:"afterSaleStatus",width:"120"},{default:i(({row:p})=>[t(o,{type:a(m).TRADE_ORDER_ITEM_AFTER_SALE_STATUS,value:p.afterSaleStatus},null,8,["type","value"])]),_:1}),t(n,{align:"center",label:"\u5B9E\u9645\u652F\u4ED8","min-width":"120",prop:"payPrice"},{default:i(()=>[R(s(a(X)(l.row.payPrice)+"\u5143"),1)]),_:2},1024),t(n,{label:"\u4E70\u5BB6/\u6536\u8D27\u4EBA","min-width":"160"},{default:i(()=>{var p,y,x;return[l.row.deliveryType===a(G).EXPRESS.type?(u(),h("div",V,[e("span",null,"\u4E70\u5BB6\uFF1A"+s(l.row.user.nickname),1),e("span",null," \u6536\u8D27\u4EBA\uFF1A"+s(l.row.receiverName)+" "+s(l.row.receiverMobile)+" "+s(l.row.receiverAreaName)+" "+s(l.row.receiverDetailAddress),1)])):I("",!0),l.row.deliveryType===a(G).PICK_UP.type?(u(),h("div",W,[e("span",null," \u95E8\u5E97\u540D\u79F0\uFF1A "+s((p=r.pickUpStoreList.find(v=>v.id===l.row.pickUpStoreId))==null?void 0:p.name),1),e("span",null," \u95E8\u5E97\u624B\u673A\uFF1A "+s((y=r.pickUpStoreList.find(v=>v.id===l.row.pickUpStoreId))==null?void 0:y.phone),1),e("span",null," \u81EA\u63D0\u95E8\u5E97: "+s((x=r.pickUpStoreList.find(v=>v.id===l.row.pickUpStoreId))==null?void 0:x.detailAddress),1)])):I("",!0)]}),_:2},1024),t(n,{align:"center",label:"\u914D\u9001\u65B9\u5F0F",width:"120"},{default:i(()=>[t(o,{type:a(m).TRADE_DELIVERY_TYPE,value:l.row.deliveryType},null,8,["type","value"])]),_:2},1024),t(n,{align:"center",label:"\u8BA2\u5355\u72B6\u6001",width:"120"},{default:i(()=>[t(o,{type:a(m).TRADE_ORDER_STATUS,value:l.row.status},null,8,["type","value"])]),_:2},1024),t(n,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"160"},{default:i(()=>[se(r.$slots,"default",{row:l.row},void 0,!0)]),_:2},1024)]),_:2},1032,["data"])]),_:3})}}}),[["__scopeId","data-v-6a0a3cbd"]])});export{he as __tla,J as default};
