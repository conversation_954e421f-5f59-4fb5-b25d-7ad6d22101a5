import{_ as t,__tla as _}from"./SkuForm.vue_vue_type_script_setup_true_lang-I0g2U3dN.js";import{__tla as r}from"./index-BUSn51wb.js";import{__tla as a}from"./index-CjyLHUq3.js";import{__tla as l}from"./SkuList-DG93D6KA.js";import{__tla as o}from"./el-image-BjHZRFih.js";import{__tla as c}from"./ProductAttributes.vue_vue_type_script_setup_true_lang-HWrQWq3l.js";import{__tla as m}from"./el-text-CIwNlU-U.js";import{__tla as e}from"./property-BdOytbZT.js";import{__tla as s}from"./ProductPropertyAddForm.vue_vue_type_script_setup_true_lang-BR4HNBkA.js";import{__tla as n}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as f}from"./formRules-CA9eXdcX.js";let h=Promise.all([(()=>{try{return _}catch{}})(),(()=>{try{return r}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return c}catch{}})(),(()=>{try{return m}catch{}})(),(()=>{try{return e}catch{}})(),(()=>{try{return s}catch{}})(),(()=>{try{return n}catch{}})(),(()=>{try{return f}catch{}})()]).then(async()=>{});export{h as __tla,t as default};
