import{bd as q,bm as V,bn as p,bo as x,be as w,d as b,bf as A,r as B,b as i,bp as F,bq as N,at as P,o as c,c as v,av as f,a as l,l as m,w as $,b0 as C,br as D,aV as G,a0 as H,bg as I,bh as J,__tla as K}from"./index-BUSn51wb.js";let y,L=Promise.all([(()=>{try{return K}catch{}})()]).then(async()=>{const _=q({size:{type:[Number,String],values:V,default:"",validator:s=>p(s)},shape:{type:String,values:["circle","square"],default:"circle"},icon:{type:x},src:{type:String,default:""},alt:String,srcSet:String,fit:{type:w(String),default:"cover"}}),d={error:s=>s instanceof Event},S=["src","alt","srcset"],g=b({name:"ElAvatar"});y=J(I(b({...g,props:_,emits:d,setup(s,{emit:h}){const e=s,t=A("avatar"),n=B(!1),z=i(()=>{const{size:a,icon:o,shape:u}=e,r=[t.b()];return F(a)&&r.push(t.m(a)),o&&r.push(t.m("icon")),u&&r.push(t.m(u)),r}),E=i(()=>{const{size:a}=e;return p(a)?t.cssVarBlock({size:N(a)||""}):void 0}),k=i(()=>({objectFit:e.fit}));function j(a){n.value=!0,h("error",a)}return P(()=>e.src,()=>n.value=!1),(a,o)=>(c(),v("span",{class:H(l(z)),style:f(l(E))},[!a.src&&!a.srcSet||n.value?a.icon?(c(),m(l(D),{key:1},{default:$(()=>[(c(),m(C(a.icon)))]),_:1})):G(a.$slots,"default",{key:2}):(c(),v("img",{key:0,src:a.src,alt:a.alt,srcset:a.srcSet,style:f(l(k)),onError:j},null,44,S))],6))}}),[["__file","avatar.vue"]]))});export{y as E,L as __tla};
