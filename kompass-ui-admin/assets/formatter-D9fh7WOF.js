import{e as a,f as e}from"./constants-A8BI3pz7.js";import{f as i,__tla as l}from"./formatTime-DWdBpgsM.js";import{aB as r,__tla as p}from"./index-BUSn51wb.js";let s,o,n,d,y,c=Promise.all([(()=>{try{return l}catch{}})(),(()=>{try{return p}catch{}})()]).then(async()=>{s=t=>t.discountType===a.PRICE.type?`\uFFE5${r(t.discountPrice)}`:t.discountType===a.PERCENT.type?`${t.discountPercent}%`:"\u672A\u77E5\u3010"+t.discountType+"\u3011",n=t=>t.takeLimitCount===-1?"\u65E0\u9886\u53D6\u9650\u5236":`${t.takeLimitCount} \u5F20/\u4EBA`,y=t=>t.validityType===e.DATE.type?`${i(t.validStartTime)} \u81F3 ${i(t.validEndTime)}`:t.validityType===e.TERM.type?`\u9886\u53D6\u540E\u7B2C ${t.fixedStartTerm} - ${t.fixedEndTerm} \u5929\u5185\u53EF\u7528`:"\u672A\u77E5\u3010"+t.validityType+"\u3011",o=t=>t.totalCount-t.takeCount,d=t=>`\uFFE5${r(t.usePrice)}`});export{c as __tla,s as d,o as r,n as t,d as u,y as v};
