import{d as X,r as b,f as Z,at as $,dW as k,C as z,o as P,c as C,H as A,a as c,l as v,w as l,i as e,F as I,k as D,en as N,dX as V,j as B,a9 as G,el as M,P as T,J as Y,K as ee,L as ae,Z as le,cc as te,N as oe,Q as de,O as re,s as ue,R as ie,__tla as ce}from"./index-BUSn51wb.js";import{P as ne,__tla as se}from"./index-B00QUU3o.js";import{S as pe,__tla as me}from"./index-BCEOZol9.js";let S,fe=Promise.all([(()=>{try{return ce}catch{}})(),(()=>{try{return se}catch{}})(),(()=>{try{return me}catch{}})()]).then(async()=>{S=X({__name:"PurchaseOrderItemForm",props:{items:{},disabled:{type:Boolean}},setup(j,{expose:q}){const E=j,F=b(!1),s=b([]),h=Z({productId:[{required:!0,message:"\u4EA7\u54C1\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],count:[{required:!0,message:"\u4EA7\u54C1\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),U=b([]),w=b([]);$(()=>E.items,async i=>{s.value=i},{immediate:!0}),$(()=>s.value,i=>{i&&i.length!==0&&i.forEach(r=>{r.totalProductPrice=k(r.productPrice,r.count),r.taxPrice=k(r.totalProductPrice,r.taxPercent/100),r.totalProductPrice!=null?r.totalPrice=r.totalProductPrice+(r.taxPrice||0):r.totalPrice=void 0})},{deep:!0});const L=i=>{const{columns:r,data:u}=i,p=[];return r.forEach((m,d)=>{if(d!==0)if(["count","totalProductPrice","taxPrice","totalPrice"].includes(m.property)){const n=M(u.map(f=>Number(f[m.property])));p[d]=m.property==="count"?N(n):V(n)}else p[d]="";else p[d]="\u5408\u8BA1"}),p},g=()=>{s.value.push({id:void 0,productId:void 0,productUnitName:void 0,productBarCode:void 0,productPrice:void 0,stockCount:void 0,count:1,totalProductPrice:void 0,taxPercent:void 0,taxPrice:void 0,totalPrice:void 0,remark:void 0})},O=async i=>{if(!i.productId)return;const r=await pe.getStockCount(i.productId);i.stockCount=r||0};return q({validate:()=>U.value.validate()}),z(async()=>{w.value=await ne.getProductSimpleList(),s.value.length===0&&g()}),(i,r)=>{const u=T,p=Y,m=ee,d=ae,n=le,f=te,y=oe,R=de,H=re,J=ue,K=ie;return P(),C(I,null,[A((P(),v(H,{ref_key:"formRef",ref:U,model:c(s),rules:c(h),"label-width":"0px","inline-message":!0,disabled:i.disabled},{default:l(()=>[e(R,{data:c(s),"show-summary":"","summary-method":L,class:"-mt-10px"},{default:l(()=>[e(u,{label:"\u5E8F\u53F7",type:"index",align:"center",width:"60"}),e(u,{label:"\u4EA7\u54C1\u540D\u79F0","min-width":"180"},{default:l(({row:a,$index:o})=>[e(d,{prop:`${o}.productId`,rules:c(h).productId,class:"mb-0px!"},{default:l(()=>[e(m,{modelValue:a.productId,"onUpdate:modelValue":t=>a.productId=t,clearable:"",filterable:"",onChange:t=>((Q,_)=>{const x=w.value.find(W=>W.id===Q);x&&(_.productUnitName=x.unitName,_.productBarCode=x.barCode,_.productPrice=x.purchasePrice),O(_)})(t,a),placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1"},{default:l(()=>[(P(!0),C(I,null,D(c(w),t=>(P(),v(p,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["prop","rules"])]),_:1}),e(u,{label:"\u5E93\u5B58","min-width":"100"},{default:l(({row:a})=>[e(d,{class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.stockCount,"onUpdate:modelValue":o=>a.stockCount=o,formatter:c(N)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1024)]),_:1}),e(u,{label:"\u6761\u7801","min-width":"150"},{default:l(({row:a})=>[e(d,{class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.productBarCode,"onUpdate:modelValue":o=>a.productBarCode=o},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),e(u,{label:"\u5355\u4F4D","min-width":"80"},{default:l(({row:a})=>[e(d,{class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.productUnitName,"onUpdate:modelValue":o=>a.productUnitName=o},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:1}),e(u,{label:"\u6570\u91CF",prop:"count",fixed:"right","min-width":"140"},{default:l(({row:a,$index:o})=>[e(d,{prop:`${o}.count`,rules:c(h).count,class:"mb-0px!"},{default:l(()=>[e(f,{modelValue:a.count,"onUpdate:modelValue":t=>a.count=t,"controls-position":"right",min:.001,precision:3,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"])]),_:1}),e(u,{label:"\u4EA7\u54C1\u5355\u4EF7",fixed:"right","min-width":"120"},{default:l(({row:a,$index:o})=>[e(d,{prop:`${o}.productPrice`,class:"mb-0px!"},{default:l(()=>[e(f,{modelValue:a.productPrice,"onUpdate:modelValue":t=>a.productPrice=t,"controls-position":"right",min:.01,precision:2,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(u,{label:"\u91D1\u989D",prop:"totalProductPrice",fixed:"right","min-width":"100"},{default:l(({row:a,$index:o})=>[e(d,{prop:`${o}.totalProductPrice`,class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.totalProductPrice,"onUpdate:modelValue":t=>a.totalProductPrice=t,formatter:c(V)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1032,["prop"])]),_:1}),e(u,{label:"\u7A0E\u7387\uFF08%\uFF09",fixed:"right","min-width":"115"},{default:l(({row:a,$index:o})=>[e(d,{prop:`${o}.taxPercent`,class:"mb-0px!"},{default:l(()=>[e(f,{modelValue:a.taxPercent,"onUpdate:modelValue":t=>a.taxPercent=t,"controls-position":"right",min:0,precision:2,class:"!w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(u,{label:"\u7A0E\u989D",prop:"taxPrice",fixed:"right","min-width":"120"},{default:l(({row:a,$index:o})=>[e(d,{prop:`${o}.taxPrice`,class:"mb-0px!"},{default:l(()=>[e(d,{prop:`${o}.taxPrice`,class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.taxPrice,"onUpdate:modelValue":t=>a.taxPrice=t,formatter:c(V)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1032,["prop"])]),_:2},1032,["prop"])]),_:1}),e(u,{label:"\u7A0E\u989D\u5408\u8BA1",prop:"totalPrice",fixed:"right","min-width":"100"},{default:l(({row:a,$index:o})=>[e(d,{prop:`${o}.totalPrice`,class:"mb-0px!"},{default:l(()=>[e(n,{disabled:"",modelValue:a.totalPrice,"onUpdate:modelValue":t=>a.totalPrice=t,formatter:c(V)},null,8,["modelValue","onUpdate:modelValue","formatter"])]),_:2},1032,["prop"])]),_:1}),e(u,{label:"\u5907\u6CE8","min-width":"150"},{default:l(({row:a,$index:o})=>[e(d,{prop:`${o}.remark`,class:"mb-0px!"},{default:l(()=>[e(n,{modelValue:a.remark,"onUpdate:modelValue":t=>a.remark=t,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:1}),e(u,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"60"},{default:l(({$index:a})=>[e(y,{onClick:o=>{return t=a,void s.value.splice(t,1);var t},link:""},{default:l(()=>[B("\u2014")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),_:1},8,["model","rules","disabled"])),[[K,c(F)]]),i.disabled?G("",!0):(P(),v(J,{key:0,justify:"center",class:"mt-3"},{default:l(()=>[e(y,{onClick:g,round:""},{default:l(()=>[B("+ \u6DFB\u52A0\u91C7\u8D2D\u4EA7\u54C1")]),_:1})]),_:1}))],64)}}})});export{S as _,fe as __tla};
