import{by as t,__tla as w}from"./index-BUSn51wb.js";let c,r,s,e,n,l,u,i,m,o,y,p,d,g=Promise.all([(()=>{try{return w}catch{}})()]).then(async()=>{n=async a=>await t.get({url:"/crm/contract/page",params:a}),c=async a=>await t.get({url:"/crm/contract/page-by-customer",params:a}),u=async a=>await t.get({url:"/crm/contract/page-by-business",params:a}),r=async a=>await t.get({url:"/crm/contract/get?id="+a}),e=async a=>await t.get({url:`/crm/contract/simple-list?customerId=${a}`}),s=async a=>await t.post({url:"/crm/contract/create",data:a}),d=async a=>await t.put({url:"/crm/contract/update",data:a}),m=async a=>await t.delete({url:"/crm/contract/delete?id="+a}),o=async a=>await t.download({url:"/crm/contract/export-excel",params:a}),y=async a=>await t.put({url:`/crm/contract/submit?id=${a}`}),p=async a=>await t.put({url:"/crm/contract/transfer",data:a}),l=async()=>await t.get({url:"/crm/contract/audit-count"}),i=async()=>await t.get({url:"/crm/contract/remind-count"})});export{g as __tla,c as a,r as b,s as c,e as d,n as e,l as f,u as g,i as h,m as i,o as j,y as s,p as t,d as u};
