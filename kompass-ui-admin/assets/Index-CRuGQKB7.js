import{d as ca,p as ma,b as ja,h as pa,a as t,e as Da,r as Va,f as _,o as f,c as k,g as s,i as a,w as e,t as l,j as D,F as V,k as W,l as ya,m as ga,n as Aa,E as Ca,q as Ea,s as Ma,v as Oa,_ as $a,x as H,__tla as za}from"./index-BUSn51wb.js";import{_ as Ba,__tla as Sa}from"./Echart.vue_vue_type_script_setup_true_lang-DnLZLH3w.js";import{E as Wa,__tla as Ha}from"./el-card-CJbXGyyg.js";import{E as Pa,__tla as Ra}from"./el-skeleton-item-tDN8U6BH.js";import{_ as Ua,__tla as Ya}from"./CountTo.vue_vue_type_script_setup_true_lang-BUNK7cmG.js";import{E as qa,__tla as Fa}from"./el-avatar-Da2TGjmj.js";import{a as xa}from"./avatar-BG6NdH5s.js";import{u as Ga}from"./useWatermark-BmKapRf9.js";import{p as Ia,b as Ja,__tla as Ka}from"./echarts-data-COHI-sZM.js";let va,Qa=Promise.all([(()=>{try{return za}catch{}})(),(()=>{try{return Sa}catch{}})(),(()=>{try{return Ha}catch{}})(),(()=>{try{return Ra}catch{}})(),(()=>{try{return Ya}catch{}})(),(()=>{try{return Fa}catch{}})(),(()=>{try{return Ka}catch{}})()]).then(async()=>{let P,R,U,Y,q,F,G,I,J,K,Q,L,N,T,X,Z,aa,ea,ta,sa,la,na,ia,oa;P=ca({name:"Highlight",props:{tag:ma.string.def("span"),keys:{type:Array,default:()=>[]},color:ma.string.def("var(--el-color-primary)")},emits:["click"],setup(h,{emit:n,slots:o}){const A=ja(()=>h.keys.map(c=>pa("span",{onClick:()=>{n("click",c)},style:{color:h.color,cursor:"pointer"}},c))),u=()=>{if(!(o!=null&&o.default))return null;const c=o==null?void 0:o.default()[0].children;if(!c)return o==null?void 0:o.default()[0];const C=(m=c,h.keys.forEach((r,M)=>{const b=new RegExp(r,"g");m=m.replace(b,`{{${M}}}`)}),m.split(/{{|}}/));var m;const x=/^[0-9]*$/,E=C.map(r=>x.test(r)&&t(A)[r]||r);return pa(h.tag,E)};return()=>u()}}),R={class:"flex items-center"},U=s("img",{src:xa,alt:""},null,-1),Y={class:"text-20px"},q={class:"mt-10px text-14px text-gray-500"},F={class:"h-70px flex items-center justify-end lt-sm:mt-10px"},G={class:"px-8px text-right"},I={class:"mb-16px text-14px text-gray-400"},J={class:"px-8px text-right"},K={class:"mb-16px text-14px text-gray-400"},Q={class:"px-8px text-right"},L={class:"mb-16px text-14px text-gray-400"},N={class:"h-3 flex justify-between"},T={class:"flex items-center"},X={class:"text-16px"},Z={class:"mt-12px text-9px text-gray-400"},aa={class:"mt-12px flex justify-between text-12px text-gray-400"},ea={class:"h-3 flex justify-between"},ta={class:"flex items-center"},sa={class:"h-3 flex justify-between"},la={class:"flex items-center"},na=s("img",{src:xa,alt:""},null,-1),ia={class:"text-14px"},oa={class:"mt-16px text-12px text-gray-400"},va=ca({name:"Home",__name:"Index",setup(h){const{t:n}=Aa(),o=Da(),{setWatermark:A}=Ga(),u=Va(!0),c=o.getUser.avatar,C=o.getUser.nickname,m=_(Ia);let x=_({project:0,access:0,todo:0});const E=async()=>{x=Object.assign(x,{project:40,access:2340,todo:10})};let r=_([]);const M=async()=>{r=Object.assign(r,[{name:"ruoyi-vue-pro",icon:"akar-icons:github-fill",message:"https://github.com/YunaiV/ruoyi-vue-pro",personal:"Spring Boot \u5355\u4F53\u67B6\u6784",time:new Date},{name:"yudao-ui-admin-vue3",icon:"logos:vue",message:"https://github.com/yudaocode/yudao-ui-admin-vue3",personal:"Vue3 + element-plus",time:new Date},{name:"yudao-ui-admin-vben",icon:"logos:vue",message:"https://github.com/yudaocode/yudao-ui-admin-vben",personal:"Vue3 + vben(antd)",time:new Date},{name:"yudao-cloud",icon:"akar-icons:github",message:"https://github.com/YunaiV/yudao-cloud",personal:"Spring Cloud \u5FAE\u670D\u52A1\u67B6\u6784",time:new Date},{name:"yudao-ui-mall-uniapp",icon:"logos:vue",message:"https://github.com/yudaocode/yudao-ui-admin-uniapp",personal:"Vue3 + uniapp",time:new Date},{name:"yudao-ui-admin-vue2",icon:"logos:vue",message:"https://github.com/yudaocode/yudao-ui-admin-vue2",personal:"Vue2 + element-ui",time:new Date}])};let b=_([]);const fa=async()=>{b=Object.assign(b,[{title:"\u7CFB\u7EDF\u652F\u6301 JDK 8/17/21\uFF0CVue 2/3",type:"\u901A\u77E5",keys:["\u901A\u77E5","8","17","21","2","3"],date:new Date},{title:"\u540E\u7AEF\u63D0\u4F9B Spring Boot 2.7/3.2 + Cloud \u53CC\u67B6\u6784",type:"\u516C\u544A",keys:["\u516C\u544A","Boot","Cloud"],date:new Date},{title:"\u5168\u90E8\u5F00\u6E90\uFF0C\u4E2A\u4EBA\u4E0E\u4F01\u4E1A\u53EF 100% \u76F4\u63A5\u4F7F\u7528\uFF0C\u65E0\u9700\u6388\u6743",type:"\u901A\u77E5",keys:["\u901A\u77E5","\u65E0\u9700\u6388\u6743"],date:new Date},{title:"\u56FD\u5185\u4F7F\u7528\u6700\u5E7F\u6CDB\u7684\u5FEB\u901F\u5F00\u53D1\u5E73\u53F0\uFF0C\u8D85 300+ \u4EBA\u8D21\u732E",type:"\u516C\u544A",keys:["\u516C\u544A","\u6700\u5E7F\u6CDB"],date:new Date}])};let O=_([]);const _a=async()=>{O=Object.assign(O,[{name:"Github",icon:"akar-icons:github-fill",url:"github.io"},{name:"Vue",icon:"logos:vue",url:"vuejs.org"},{name:"Vite",icon:"vscode-icons:file-type-vite",url:"https://vitejs.dev/"},{name:"Angular",icon:"logos:angular-icon",url:"github.io"},{name:"React",icon:"logos:react",url:"github.io"},{name:"Webpack",icon:"logos:webpack",url:"github.io"}])},ha=async()=>{const p=[{value:335,name:"analysis.directAccess"},{value:310,name:"analysis.mailMarketing"},{value:234,name:"analysis.allianceAdvertising"},{value:135,name:"analysis.videoAdvertising"},{value:1548,name:"analysis.searchEngines"}];H(m,"legend.data",p.map(d=>n(d.name))),m.series[0].data=p.map(d=>({name:n(d.name),value:d.value}))},$=_(Ja),ba=async()=>{const p=[{value:13253,name:"analysis.monday"},{value:34235,name:"analysis.tuesday"},{value:26321,name:"analysis.wednesday"},{value:12340,name:"analysis.thursday"},{value:24643,name:"analysis.friday"},{value:1322,name:"analysis.saturday"},{value:1324,name:"analysis.sunday"}];H($,"xAxis.data",p.map(d=>n(d.name))),H($,"series",[{name:n("analysis.activeQuantity"),data:p.map(d=>d.value),type:"bar"}])};return(async()=>(await Promise.all([E(),M(),fa(),_a(),ha(),ba()]),u.value=!1))(),(p,d)=>{const ua=qa,y=Ca,z=Ua,B=Ea,w=Ma,v=Pa,g=Wa,S=Oa,ra=$a,da=Ba,wa=P;return f(),k(V,null,[s("div",null,[a(g,{shadow:"never"},{default:e(()=>[a(v,{loading:t(u),animated:""},{default:e(()=>[a(w,{gutter:16,justify:"space-between"},{default:e(()=>[a(y,{xl:12,lg:12,md:12,sm:24,xs:24},{default:e(()=>[s("div",R,[a(ua,{src:t(c),size:70,class:"mr-16px"},{default:e(()=>[U]),_:1},8,["src"]),s("div",null,[s("div",Y,l(t(n)("workplace.welcome"))+" "+l(t(C))+" "+l(t(n)("workplace.happyDay")),1),s("div",q,l(t(n)("workplace.toady"))+"\uFF0C20\u2103 - 32\u2103\uFF01 ",1)])])]),_:1}),a(y,{xl:12,lg:12,md:12,sm:24,xs:24},{default:e(()=>[s("div",F,[s("div",G,[s("div",I,l(t(n)("workplace.project")),1),a(z,{class:"text-20px","start-val":0,"end-val":t(x).project,duration:2600},null,8,["end-val"])]),a(B,{direction:"vertical"}),s("div",J,[s("div",K,l(t(n)("workplace.toDo")),1),a(z,{class:"text-20px","start-val":0,"end-val":t(x).todo,duration:2600},null,8,["end-val"])]),a(B,{direction:"vertical","border-style":"dashed"}),s("div",Q,[s("div",L,l(t(n)("workplace.access")),1),a(z,{class:"text-20px","start-val":0,"end-val":t(x).access,duration:2600},null,8,["end-val"])])])]),_:1})]),_:1})]),_:1},8,["loading"])]),_:1})]),a(w,{class:"mt-8px",gutter:8,justify:"space-between"},{default:e(()=>[a(y,{xl:16,lg:16,md:24,sm:24,xs:24,class:"mb-8px"},{default:e(()=>[a(g,{shadow:"never"},{header:e(()=>[s("div",N,[s("span",null,l(t(n)("workplace.project")),1),a(S,{type:"primary",underline:!1,href:"https://github.com/yudaocode",target:"_blank"},{default:e(()=>[D(l(t(n)("action.more")),1)]),_:1})])]),default:e(()=>[a(v,{loading:t(u),animated:""},{default:e(()=>[a(w,null,{default:e(()=>[(f(!0),k(V,null,W(t(r),(i,j)=>(f(),ya(y,{key:`card-${j}`,xl:8,lg:8,md:8,sm:24,xs:24},{default:e(()=>[a(g,{shadow:"hover",class:"mr-5px mt-5px"},{default:e(()=>[s("div",T,[a(ra,{icon:i.icon,size:25,class:"mr-8px"},null,8,["icon"]),s("span",X,l(i.name),1)]),s("div",Z,l(t(n)(i.message)),1),s("div",aa,[s("span",null,l(i.personal),1),s("span",null,l(t(ga)(i.time,"yyyy-MM-dd")),1)])]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1},8,["loading"])]),_:1}),a(g,{shadow:"never",class:"mt-8px"},{default:e(()=>[a(v,{loading:t(u),animated:""},{default:e(()=>[a(w,{gutter:20,justify:"space-between"},{default:e(()=>[a(y,{xl:10,lg:10,md:24,sm:24,xs:24},{default:e(()=>[a(g,{shadow:"hover",class:"mb-8px"},{default:e(()=>[a(v,{loading:t(u),animated:""},{default:e(()=>[a(da,{options:t(m),height:280},null,8,["options"])]),_:1},8,["loading"])]),_:1})]),_:1}),a(y,{xl:14,lg:14,md:24,sm:24,xs:24},{default:e(()=>[a(g,{shadow:"hover",class:"mb-8px"},{default:e(()=>[a(v,{loading:t(u),animated:""},{default:e(()=>[a(da,{options:t($),height:280},null,8,["options"])]),_:1},8,["loading"])]),_:1})]),_:1})]),_:1})]),_:1},8,["loading"])]),_:1})]),_:1}),a(y,{xl:8,lg:8,md:24,sm:24,xs:24,class:"mb-8px"},{default:e(()=>[a(g,{shadow:"never"},{header:e(()=>[s("div",ea,[s("span",null,l(t(n)("workplace.shortcutOperation")),1)])]),default:e(()=>[a(v,{loading:t(u),animated:""},{default:e(()=>[a(w,null,{default:e(()=>[(f(!0),k(V,null,W(t(O),i=>(f(),ya(y,{key:`team-${i.name}`,span:8,class:"mb-8px"},{default:e(()=>[s("div",ta,[a(ra,{icon:i.icon,class:"mr-8px"},null,8,["icon"]),a(S,{type:"default",underline:!1,onClick:j=>t(A)(i.name)},{default:e(()=>[D(l(i.name),1)]),_:2},1032,["onClick"])])]),_:2},1024))),128))]),_:1})]),_:1},8,["loading"])]),_:1}),a(g,{shadow:"never",class:"mt-8px"},{header:e(()=>[s("div",sa,[s("span",null,l(t(n)("workplace.notice")),1),a(S,{type:"primary",underline:!1},{default:e(()=>[D(l(t(n)("action.more")),1)]),_:1})])]),default:e(()=>[a(v,{loading:t(u),animated:""},{default:e(()=>[(f(!0),k(V,null,W(t(b),(i,j)=>(f(),k("div",{key:`dynamics-${j}`},[s("div",la,[a(ua,{src:t(c),size:35,class:"mr-16px"},{default:e(()=>[na]),_:1},8,["src"]),s("div",null,[s("div",ia,[a(wa,{keys:i.keys.map(ka=>t(n)(ka))},{default:e(()=>[D(l(i.type)+" : "+l(i.title),1)]),_:2},1032,["keys"])]),s("div",oa,l(t(ga)(i.date,"yyyy-MM-dd")),1)])]),a(B)]))),128))]),_:1},8,["loading"])]),_:1})]),_:1})]),_:1})],64)}}})});export{Qa as __tla,va as default};
