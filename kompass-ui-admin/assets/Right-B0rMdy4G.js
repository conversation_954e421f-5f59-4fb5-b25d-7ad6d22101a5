import{d as C,I as H,e5 as R,b,r as T,at as E,o as N,l as P,w as s,g as n,H as h,i as l,j as y,a8 as g,a as x,y as U,_ as Z,N as q,Z as A,a5 as D,a6 as F,B as G,__tla as J}from"./index-BUSn51wb.js";import{E as K,__tla as L}from"./el-card-CJbXGyyg.js";let v,M=Promise.all([(()=>{try{return J}catch{}})(),(()=>{try{return L}catch{}})()]).then(async()=>{let i,u,d;i={class:"m-0 px-7 shrink-0 flex items-center justify-between"},u=(a=>(D("data-v-39dbb5c0"),a=a(),F(),a))(()=>n("span",null,"\u9884\u89C8",-1)),d={class:"w-full min-h-full relative flex-grow bg-white box-border p-3 sm:p-7"},v=G(C({__name:"Right",props:{content:{type:String,default:""},isWriting:{type:Boolean,default:!1}},emits:["update:content","stopStream"],setup(a,{expose:w,emit:z}){const S=H(),{copied:k,copy:B}=R(),o=a,p=z,r=b({get:()=>o.content,set(e){p("update:content",e)}}),c=T();w({scrollToBottom(){var e,t;(t=c.value)==null||t.scrollTo(0,(e=c.value)==null?void 0:e.scrollHeight)}});const I=b(()=>o.content&&!o.isWriting),V=()=>{B(o.content)};return E(k,e=>{e&&S.success("\u590D\u5236\u6210\u529F")}),(e,t)=>{const m=Z,_=q,W=A,j=K;return N(),P(j,{class:"my-card h-full"},{header:s(()=>[n("h3",i,[u,h(l(_,{color:"#846af7",onClick:V,size:"small"},{icon:s(()=>[l(m,{icon:"ph:copy-bold"})]),default:s(()=>[y(" \u590D\u5236 ")]),_:1},512),[[g,x(I)]])])]),default:s(()=>[n("div",{ref_key:"contentRef",ref:c,class:"hide-scroll-bar h-full box-border overflow-y-auto"},[n("div",d,[h(l(_,{class:"absolute bottom-2 sm:bottom-5 left-1/2 -translate-x-1/2 z-36",onClick:t[0]||(t[0]=f=>p("stopStream")),size:"small"},{icon:s(()=>[l(m,{icon:"material-symbols:stop"})]),default:s(()=>[y(" \u7EC8\u6B62\u751F\u6210 ")]),_:1},512),[[g,a.isWriting]]),l(W,{id:"inputId",type:"textarea",modelValue:x(r),"onUpdate:modelValue":t[1]||(t[1]=f=>U(r)?r.value=f:null),autosize:"","input-style":{boxShadow:"none"},resize:"none",placeholder:"\u751F\u6210\u7684\u5185\u5BB9\u2026\u2026"},null,8,["modelValue"])])],512)]),_:1})}}}),[["__scopeId","data-v-39dbb5c0"]])});export{M as __tla,v as default};
