import{d as g,I as F,r as i,f as U,o as c,l as f,w as t,i as s,a as e,j as y,H as j,y as A,Z as C,L as R,O as q,N as H,R as L,__tla as N}from"./index-BUSn51wb.js";import{_ as O,__tla as S}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{c as Z,__tla as z}from"./index-CyP7ZSdX.js";let v,B=Promise.all([(()=>{try{return N}catch{}})(),(()=>{try{return S}catch{}})(),(()=>{try{return z}catch{}})()]).then(async()=>{v=g({name:"SystemAreaForm",__name:"AreaForm",setup(D,{expose:V}){const b=F(),u=i(!1),o=i(!1),a=i({ip:"",result:void 0}),h=U({ip:[{required:!0,message:"IP \u5730\u5740\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),d=i();V({open:async()=>{u.value=!0,P()}});const I=async()=>{if(d&&await d.value.validate()){o.value=!0;try{a.value.result=await Z(a.value.ip.trim()),b.success("\u67E5\u8BE2\u6210\u529F")}finally{o.value=!1}}},P=()=>{var _;a.value={ip:"",result:void 0},(_=d.value)==null||_.resetFields()};return(_,l)=>{const m=C,n=R,k=q,p=H,w=O,x=L;return c(),f(w,{modelValue:e(u),"onUpdate:modelValue":l[3]||(l[3]=r=>A(u)?u.value=r:null),title:"IP \u67E5\u8BE2"},{footer:t(()=>[s(p,{disabled:e(o),type:"primary",onClick:I},{default:t(()=>[y("\u786E \u5B9A")]),_:1},8,["disabled"]),s(p,{onClick:l[2]||(l[2]=r=>u.value=!1)},{default:t(()=>[y("\u53D6 \u6D88")]),_:1})]),default:t(()=>[j((c(),f(k,{ref_key:"formRef",ref:d,model:e(a),rules:e(h),"label-width":"80px"},{default:t(()=>[s(n,{label:"IP",prop:"ip"},{default:t(()=>[s(m,{modelValue:e(a).ip,"onUpdate:modelValue":l[0]||(l[0]=r=>e(a).ip=r),placeholder:"\u8BF7\u8F93\u5165 IP \u5730\u5740"},null,8,["modelValue"])]),_:1}),s(n,{label:"\u5730\u5740",prop:"result"},{default:t(()=>[s(m,{modelValue:e(a).result,"onUpdate:modelValue":l[1]||(l[1]=r=>e(a).result=r),placeholder:"\u5C55\u793A\u67E5\u8BE2 IP \u7ED3\u679C",readonly:""},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[x,e(o)]])]),_:1},8,["modelValue"])}}})});export{v as _,B as __tla};
