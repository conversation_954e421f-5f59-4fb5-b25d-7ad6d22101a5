import{d as K,I as Q,r as d,o as p,c as V,g as o,i as l,w as e,j as n,a as t,y as f,F as k,k as U,l as S,t as B,Z as q,N as M,J as R,K as W,B as X,__tla as $}from"./index-BUSn51wb.js";import{E as aa,__tla as la}from"./el-space-Dxj8A-LJ.js";import{E as ea,__tla as ta}from"./el-text-CIwNlU-U.js";import{I as ua,__tla as sa}from"./index-Cjd1fP7g.js";import{a as c,T as G,I as oa,O as ra,Q as da,C as na}from"./constants-C0I8ujwj.js";let L,pa=Promise.all([(()=>{try{return $}catch{}})(),(()=>{try{return la}catch{}})(),(()=>{try{return ta}catch{}})(),(()=>{try{return sa}catch{}})()]).then(async()=>{let C,D,N,F,O,T;C={class:"prompt"},D={class:"hot-words"},N={class:"group-item"},F={class:"group-item"},O={class:"group-item"},T={class:"btns"},L=X(K({__name:"index",emits:["onDrawStart","onDrawComplete"],setup(ma,{expose:P,emit:Z}){const j=Q(),g=d(!1),w=d(""),m=d(""),v=d(512),y=d(512),h=d(c.TONG_YI),r=d(G),i=d(r.value[0].key),E=Z,H=async()=>{await j.confirm("\u786E\u8BA4\u751F\u6210\u5185\u5BB9?");try{g.value=!0,E("onDrawStart",c.STABLE_DIFFUSION);const u={platform:h.value,model:i.value,prompt:m.value,width:v.value,height:y.value,options:{}};await ua.drawImage(u)}finally{E("onDrawComplete",c.STABLE_DIFFUSION),g.value=!1}},J=async u=>{c.TONG_YI===u?r.value=G:c.YI_YAN===u?r.value=da:c.ZHI_PU===u?r.value=na:r.value=[],r.value.length>0?i.value=r.value[0].key:i.value=""};return P({settingValues:async u=>{m.value=u.prompt,v.value=u.width,y.value=u.height}}),(u,s)=>{const _=ea,x=q,Y=M,b=aa,z=R,A=W;return p(),V(k,null,[o("div",C,[l(_,{tag:"b"},{default:e(()=>[n("\u753B\u9762\u63CF\u8FF0")]),_:1}),l(_,{tag:"p"},{default:e(()=>[n("\u5EFA\u8BAE\u4F7F\u7528\u201C\u5F62\u5BB9\u8BCD+\u52A8\u8BCD+\u98CE\u683C\u201D\u7684\u683C\u5F0F\uFF0C\u4F7F\u7528\u201C\uFF0C\u201D\u9694\u5F00")]),_:1}),l(x,{modelValue:t(m),"onUpdate:modelValue":s[0]||(s[0]=a=>f(m)?m.value=a:null),maxlength:"1024",rows:"5",class:"w-100% mt-15px","input-style":"border-radius: 7px;",placeholder:"\u4F8B\u5982\uFF1A\u7AE5\u8BDD\u91CC\u7684\u5C0F\u5C4B\u5E94\u8BE5\u662F\u4EC0\u4E48\u6837\u5B50\uFF1F","show-word-limit":"",type:"textarea"},null,8,["modelValue"])]),o("div",D,[o("div",null,[l(_,{tag:"b"},{default:e(()=>[n("\u968F\u673A\u70ED\u8BCD")]),_:1})]),l(b,{wrap:"",class:"word-list"},{default:e(()=>[(p(!0),V(k,null,U(t(oa),a=>(p(),S(Y,{round:"",class:"btn",type:t(w)===a?"primary":"default",key:a,onClick:ia=>(async I=>{w.value!=I?(w.value=I,m.value=I):w.value=""})(a)},{default:e(()=>[n(B(a),1)]),_:2},1032,["type","onClick"]))),128))]),_:1})]),o("div",N,[o("div",null,[l(_,{tag:"b"},{default:e(()=>[n("\u5E73\u53F0")]),_:1})]),l(b,{wrap:"",class:"group-item-body"},{default:e(()=>[l(A,{modelValue:t(h),"onUpdate:modelValue":s[1]||(s[1]=a=>f(h)?h.value=a:null),placeholder:"Select",size:"large",class:"!w-350px",onChange:J},{default:e(()=>[(p(!0),V(k,null,U(t(ra),a=>(p(),S(z,{key:a.key,label:a.name,value:a.key},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),o("div",F,[o("div",null,[l(_,{tag:"b"},{default:e(()=>[n("\u6A21\u578B")]),_:1})]),l(b,{wrap:"",class:"group-item-body"},{default:e(()=>[l(A,{modelValue:t(i),"onUpdate:modelValue":s[2]||(s[2]=a=>f(i)?i.value=a:null),placeholder:"Select",size:"large",class:"!w-350px"},{default:e(()=>[(p(!0),V(k,null,U(t(r),a=>(p(),S(z,{key:a.key,label:a.name,value:a.key},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),o("div",O,[o("div",null,[l(_,{tag:"b"},{default:e(()=>[n("\u56FE\u7247\u5C3A\u5BF8")]),_:1})]),l(b,{wrap:"",class:"group-item-body"},{default:e(()=>[l(x,{modelValue:t(v),"onUpdate:modelValue":s[3]||(s[3]=a=>f(v)?v.value=a:null),type:"number",class:"w-170px",placeholder:"\u56FE\u7247\u5BBD\u5EA6"},null,8,["modelValue"]),l(x,{modelValue:t(y),"onUpdate:modelValue":s[4]||(s[4]=a=>f(y)?y.value=a:null),type:"number",class:"w-170px",placeholder:"\u56FE\u7247\u9AD8\u5EA6"},null,8,["modelValue"])]),_:1})]),o("div",T,[l(Y,{type:"primary",size:"large",round:"",loading:t(g),onClick:H},{default:e(()=>[n(B(t(g)?"\u751F\u6210\u4E2D":"\u751F\u6210\u5185\u5BB9"),1)]),_:1},8,["loading"])])],64)}}}),[["__scopeId","data-v-df488f59"]])});export{pa as __tla,L as default};
