import{d as B,b as D,o,c as u,i as m,w as E,g as k,a0 as w,j as F,t as U,a as p,a9 as h,y as L,F as S,_ as q,B as z,__tla as G}from"./index-BUSn51wb.js";import{V as $,__tla as H}from"./vuedraggable.umd-BTL7hPHv.js";let j,J=Promise.all([(()=>{try{return G}catch{}})(),(()=>{try{return H}catch{}})()]).then(async()=>{let I,x,g,y,b,V;I={class:"menu_bottom"},x=["onClick"],g={key:0,class:"submenu"},y={class:"menu_bottom subtitle"},b=["onClick"],V=["onClick"],j=z(B({__name:"MenuPreviewer",props:{modelValue:{},activeIndex:{},parentIndex:{},accountId:{}},emits:["update:modelValue","menu-clicked","submenu-clicked"],setup(M,{emit:N}){const d=M,i=N,s=D({get:()=>d.modelValue,set:a=>i("update:modelValue",a)}),O=()=>{const a=s.value.length,l={name:"\u83DC\u5355\u540D\u79F0",children:[],reply:{type:"text",accountId:d.accountId}};s.value[a]=l,f(l,a-1)},f=(a,l)=>{i("menu-clicked",a,l)},C=(a,l,n)=>{i("submenu-clicked",a,l,n)},P=({oldIndex:a,newIndex:l})=>{if(d.activeIndex==="__MENU_NOT_SELECTED__")return;let n=new Array(s.value.length).fill(!1);n[d.parentIndex]=!0;const[e]=n.splice(a,1);n.splice(l,0,e);const t=n.indexOf(!0),c=s.value[t];i("menu-clicked",c,t)},T=({newIndex:a})=>{var t;const l=d.parentIndex,n=a,e=(t=s.value[l])==null?void 0:t.children;if(e&&(e==null?void 0:e.length)>0){const c=e[n];i("submenu-clicked",c,l,n)}};return(a,l)=>{const n=q;return o(),u(S,null,[m(p($),{modelValue:p(s),"onUpdate:modelValue":l[0]||(l[0]=e=>L(s)?s.value=e:null),"item-key":"id","ghost-class":"draggable-ghost",animation:400,onEnd:P},{item:E(({element:e,index:t})=>[k("div",I,[k("div",{onClick:c=>f(e,t),class:w(["menu_item",{active:d.activeIndex===`${t}`}])},[m(n,{icon:"ep:fold",color:"black"}),F(U(e.name),1)],10,x),d.parentIndex===t&&e.children?(o(),u("div",g,[m(p($),{modelValue:e.children,"onUpdate:modelValue":c=>e.children=c,"item-key":"id","ghost-class":"draggable-ghost",animation:400,onEnd:T},{item:E(({element:c,index:r})=>[k("div",y,[e.children?(o(),u("div",{key:0,class:w(["menu_subItem",{active:d.activeIndex===`${t}-${r}`}]),onClick:_=>C(c,t,r)},U(c.name),11,b)):h("",!0)])]),_:2},1032,["modelValue","onUpdate:modelValue"]),!e.children||e.children.length<5?(o(),u("div",{key:0,class:"menu_bottom menu_addicon",onClick:c=>((r,_)=>{const v=_.children.length,A={name:"\u5B50\u83DC\u5355\u540D\u79F0",reply:{type:"text",accountId:d.accountId}};_.children[v]=A,C(_.children[v],r,v)})(t,e)},[m(n,{icon:"ep:plus",class:"plus"})],8,V)):h("",!0)])):h("",!0)])]),_:1},8,["modelValue"]),p(s).length<3?(o(),u("div",{key:0,class:"menu_bottom menu_addicon",onClick:O},[m(n,{icon:"ep:plus",class:"plus"})])):h("",!0)],64)}}}),[["__scopeId","data-v-7c5212d5"]])});export{J as __tla,j as default};
