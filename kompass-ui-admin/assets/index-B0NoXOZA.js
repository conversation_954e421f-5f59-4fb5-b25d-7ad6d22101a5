import{d as Q,u as Z,I as $,n as W,r as p,f as X,bc as aa,C as ea,T as la,o,c,i as e,w as t,a as l,F as m,k as y,l as i,U as D,V as ta,G as B,H as k,j as V,t as E,g as ra,a9 as sa,dz as oa,J as na,K as ia,L as ua,Z as pa,M as da,O as ca,P as ma,N as _a,Q as fa,R as ya,__tla as ha}from"./index-BUSn51wb.js";import{_ as wa,__tla as ba}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as ga,__tla as va}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as ka,__tla as Va}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as xa,__tla as Sa}from"./index-COobLwz-.js";import{d as z,a as Ua,__tla as Ia}from"./formatTime-DWdBpgsM.js";import{d as Ta,e as Ca,__tla as Na}from"./index-BtD-8VxR.js";import{C as Pa,__tla as Ma}from"./index-B5YaQXtD.js";import{g as Da,__tla as Ba}from"./index-BYXzDB8j.js";import{__tla as Ea}from"./index-Cch5e1V0.js";import"./color-BN7ZL7BD.js";import{__tla as za}from"./el-card-CJbXGyyg.js";let A,Aa=Promise.all([(()=>{try{return ha}catch{}})(),(()=>{try{return ba}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return Va}catch{}})(),(()=>{try{return Sa}catch{}})(),(()=>{try{return Ia}catch{}})(),(()=>{try{return Na}catch{}})(),(()=>{try{return Ma}catch{}})(),(()=>{try{return Ba}catch{}})(),(()=>{try{return Ea}catch{}})(),(()=>{try{return za}catch{}})()]).then(async()=>{A=Q({name:"BpmProcessInstanceManager",__name:"index",setup(Ha){const H=Z(),O=$(),{t:x}=W(),h=p(!0),S=p(0),U=p([]),r=X({pageNo:1,pageSize:10,startUserId:void 0,name:"",processDefinitionId:void 0,category:void 0,status:void 0,createTime:[]}),R=p(),I=p([]),T=p([]),d=async()=>{h.value=!0;try{const w=await Ta(r);U.value=w.list,S.value=w.total}finally{h.value=!1}},C=()=>{r.pageNo=1,d()};return aa(()=>{d()}),ea(async()=>{await d(),I.value=await Pa.getCategorySimpleList(),T.value=await Da()}),(w,s)=>{const Y=xa,b=na,g=ia,u=ua,N=pa,q=da,F=ca,P=ka,n=ma,K=ga,v=_a,L=fa,j=wa,M=la("hasPermi"),G=ya;return o(),c(m,null,[e(Y,{title:"\u5DE5\u4F5C\u6D41\u624B\u518C",url:"https://doc.iocoder.cn/bpm/"}),e(P,null,{default:t(()=>[e(F,{class:"-mb-15px",model:l(r),ref_key:"queryFormRef",ref:R,inline:!0,"label-width":"68px"},{default:t(()=>[e(u,{label:"\u53D1\u8D77\u4EBA",prop:"startUserId"},{default:t(()=>[e(g,{modelValue:l(r).startUserId,"onUpdate:modelValue":s[0]||(s[0]=a=>l(r).startUserId=a),placeholder:"\u8BF7\u9009\u62E9\u53D1\u8D77\u4EBA",class:"!w-240px"},{default:t(()=>[(o(!0),c(m,null,y(l(T),a=>(o(),i(b,{key:a.id,label:a.nickname,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"\u6D41\u7A0B\u540D\u79F0",prop:"name"},{default:t(()=>[e(N,{modelValue:l(r).name,"onUpdate:modelValue":s[1]||(s[1]=a=>l(r).name=a),placeholder:"\u8BF7\u8F93\u5165\u6D41\u7A0B\u540D\u79F0",clearable:"",onKeyup:D(C,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(u,{label:"\u6240\u5C5E\u6D41\u7A0B",prop:"processDefinitionId"},{default:t(()=>[e(N,{modelValue:l(r).processDefinitionId,"onUpdate:modelValue":s[2]||(s[2]=a=>l(r).processDefinitionId=a),placeholder:"\u8BF7\u8F93\u5165\u6D41\u7A0B\u5B9A\u4E49\u7684\u7F16\u53F7",clearable:"",onKeyup:D(C,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(u,{label:"\u6D41\u7A0B\u5206\u7C7B",prop:"category"},{default:t(()=>[e(g,{modelValue:l(r).category,"onUpdate:modelValue":s[3]||(s[3]=a=>l(r).category=a),placeholder:"\u8BF7\u9009\u62E9\u6D41\u7A0B\u5206\u7C7B",clearable:"",class:"!w-240px"},{default:t(()=>[(o(!0),c(m,null,y(l(I),a=>(o(),i(b,{key:a.code,label:a.name,value:a.code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"\u6D41\u7A0B\u72B6\u6001",prop:"status"},{default:t(()=>[e(g,{modelValue:l(r).status,"onUpdate:modelValue":s[4]||(s[4]=a=>l(r).status=a),placeholder:"\u8BF7\u9009\u62E9\u6D41\u7A0B\u72B6\u6001",clearable:"",class:"!w-240px"},{default:t(()=>[(o(!0),c(m,null,y(l(ta)(l(B).BPM_PROCESS_INSTANCE_STATUS),a=>(o(),i(b,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"\u53D1\u8D77\u65F6\u95F4",prop:"createTime"},{default:t(()=>[e(q,{modelValue:l(r).createTime,"onUpdate:modelValue":s[5]||(s[5]=a=>l(r).createTime=a),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-220px"},null,8,["modelValue","default-time"])]),_:1})]),_:1},8,["model"])]),_:1}),e(P,null,{default:t(()=>[k((o(),i(L,{data:l(U)},{default:t(()=>[e(n,{label:"\u6D41\u7A0B\u540D\u79F0",align:"center",prop:"name","min-width":"200px",fixed:"left"}),e(n,{label:"\u6D41\u7A0B\u5206\u7C7B",align:"center",prop:"categoryName","min-width":"100",fixed:"left"}),e(n,{label:"\u6D41\u7A0B\u53D1\u8D77\u4EBA",align:"center",prop:"startUser.nickname",width:"120"}),e(n,{label:"\u53D1\u8D77\u90E8\u95E8",align:"center",prop:"startUser.deptName",width:"120"}),e(n,{label:"\u6D41\u7A0B\u72B6\u6001",prop:"status",width:"120"},{default:t(a=>[e(K,{type:l(B).BPM_PROCESS_INSTANCE_STATUS,value:a.row.status},null,8,["type","value"])]),_:1}),e(n,{label:"\u53D1\u8D77\u65F6\u95F4",align:"center",prop:"startTime",width:"180",formatter:l(z)},null,8,["formatter"]),e(n,{label:"\u7ED3\u675F\u65F6\u95F4",align:"center",prop:"endTime",width:"180",formatter:l(z)},null,8,["formatter"]),e(n,{align:"center",label:"\u8017\u65F6",prop:"durationInMillis",width:"169"},{default:t(a=>[V(E(a.row.durationInMillis>0?l(Ua)(a.row.durationInMillis):"-"),1)]),_:1}),e(n,{label:"\u5F53\u524D\u5BA1\u6279\u4EFB\u52A1",align:"center",prop:"tasks","min-width":"120px"},{default:t(a=>[(o(!0),c(m,null,y(a.row.tasks,_=>(o(),i(v,{type:"primary",key:_.id,link:""},{default:t(()=>[ra("span",null,E(_.name),1)]),_:2},1024))),128))]),_:1}),e(n,{label:"\u6D41\u7A0B\u7F16\u53F7",align:"center",prop:"id","min-width":"320px"}),e(n,{label:"\u64CD\u4F5C",align:"center",fixed:"right",width:"180"},{default:t(a=>[k((o(),i(v,{link:"",type:"primary",onClick:_=>{return f=a.row,void H.push({name:"BpmProcessInstanceDetail",query:{id:f.id}});var f}},{default:t(()=>[V(" \u8BE6\u60C5 ")]),_:2},1032,["onClick"])),[[M,["bpm:process-instance:cancel"]]]),a.row.status===1?k((o(),i(v,{key:0,link:"",type:"primary",onClick:_=>(async f=>{const{value:J}=await oa.prompt("\u8BF7\u8F93\u5165\u53D6\u6D88\u539F\u56E0","\u53D6\u6D88\u6D41\u7A0B",{confirmButtonText:x("common.ok"),cancelButtonText:x("common.cancel"),inputPattern:/^[\s\S]*.*\S[\s\S]*$/,inputErrorMessage:"\u53D6\u6D88\u539F\u56E0\u4E0D\u80FD\u4E3A\u7A7A"});await Ca(f.id,J),O.success("\u53D6\u6D88\u6210\u529F"),await d()})(a.row)},{default:t(()=>[V(" \u53D6\u6D88 ")]),_:2},1032,["onClick"])),[[M,["bpm:process-instance:query"]]]):sa("",!0)]),_:1})]),_:1},8,["data"])),[[G,l(h)]]),e(j,{total:l(S),page:l(r).pageNo,"onUpdate:page":s[6]||(s[6]=a=>l(r).pageNo=a),limit:l(r).pageSize,"onUpdate:limit":s[7]||(s[7]=a=>l(r).pageSize=a),onPagination:d},null,8,["total","page","limit"])]),_:1})],64)}}})});export{Aa as __tla,A as default};
