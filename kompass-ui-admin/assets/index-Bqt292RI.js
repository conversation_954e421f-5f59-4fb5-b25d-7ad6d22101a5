import{by as a,__tla as i}from"./index-BUSn51wb.js";let s,e,l,d,r,y,p=Promise.all([(()=>{try{return i}catch{}})()]).then(async()=>{r=async()=>await a.get({url:"/system/dept/simple-list"}),e=async t=>await a.get({url:"/system/dept/list",params:t}),s=async t=>await a.get({url:"/system/dept/get?id="+t}),l=async t=>await a.post({url:"/system/dept/create",data:t}),y=async t=>await a.put({url:"/system/dept/update",data:t}),d=async t=>await a.delete({url:"/system/dept/delete?id="+t})});export{p as __tla,s as a,e as b,l as c,d,r as g,y as u};
