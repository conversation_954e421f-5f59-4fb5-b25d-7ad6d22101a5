import{d as x,b as f,r as P,o as t,c as p,i as o,w as r,a as l,l as d,F as i,k as h,cJ as R,G as q,a9 as y,V as A,y as S,J as G,K as J,L,Z as O,O as Y,__tla as C}from"./index-BUSn51wb.js";import H,{__tla as Q}from"./main-DgL9CntZ.js";import{M as c}from"./types-CAO1T7C7.js";let M,Z=Promise.all([(()=>{try{return C}catch{}})(),(()=>{try{return Q}catch{}})()]).then(async()=>{M=x({name:"ReplyForm",__name:"ReplyForm",props:{modelValue:{},reply:{},msgType:{}},emits:["update:reply","update:modelValue"],setup(T,{expose:k,emit:w}){const v=T,g=w,_=f({get:()=>v.reply,set:a=>g("update:reply",a)}),s=f({get:()=>v.modelValue,set:a=>g("update:modelValue",a)}),n=P(null),K=["text","image","voice","video","shortvideo","location","link"],E={requestKeyword:[{required:!0,message:"\u8BF7\u6C42\u7684\u5173\u952E\u5B57\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],requestMatch:[{required:!0,message:"\u8BF7\u6C42\u7684\u5173\u952E\u5B57\u7684\u5339\u914D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]};return k({resetFields:()=>{var a;return(a=n.value)==null?void 0:a.resetFields()},validate:async()=>{var a;return(a=n.value)==null?void 0:a.validate()}}),(a,u)=>{const V=G,b=J,m=L,U=O,F=Y;return t(),p("div",null,[o(F,{ref_key:"formRef",ref:n,model:l(s),rules:E,"label-width":"80px"},{default:r(()=>[a.msgType===l(c).Message?(t(),d(m,{key:0,label:"\u6D88\u606F\u7C7B\u578B",prop:"requestMessageType"},{default:r(()=>[o(b,{modelValue:l(s).requestMessageType,"onUpdate:modelValue":u[0]||(u[0]=e=>l(s).requestMessageType=e),placeholder:"\u8BF7\u9009\u62E9"},{default:r(()=>[(t(!0),p(i,null,h(l(R)(l(q).MP_MESSAGE_TYPE),e=>(t(),p(i,{key:e.value},[K.includes(e.value)?(t(),d(V,{key:0,label:e.label,value:e.value},null,8,["label","value"])):y("",!0)],64))),128))]),_:1},8,["modelValue"])]),_:1})):y("",!0),a.msgType===l(c).Keyword?(t(),d(m,{key:1,label:"\u5339\u914D\u7C7B\u578B",prop:"requestMatch"},{default:r(()=>[o(b,{modelValue:l(s).requestMatch,"onUpdate:modelValue":u[1]||(u[1]=e=>l(s).requestMatch=e),placeholder:"\u8BF7\u9009\u62E9\u5339\u914D\u7C7B\u578B",clearable:""},{default:r(()=>[(t(!0),p(i,null,h(l(A)(l(q).MP_AUTO_REPLY_REQUEST_MATCH),e=>(t(),d(V,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):y("",!0),a.msgType===l(c).Keyword?(t(),d(m,{key:2,label:"\u5173\u952E\u8BCD",prop:"requestKeyword"},{default:r(()=>[o(U,{modelValue:l(s).requestKeyword,"onUpdate:modelValue":u[2]||(u[2]=e=>l(s).requestKeyword=e),placeholder:"\u8BF7\u8F93\u5165\u5185\u5BB9",clearable:""},null,8,["modelValue"])]),_:1})):y("",!0),o(m,{label:"\u56DE\u590D\u6D88\u606F"},{default:r(()=>[o(l(H),{modelValue:l(_),"onUpdate:modelValue":u[3]||(u[3]=e=>S(_)?_.value=e:null)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])])}}})});export{M as _,Z as __tla};
