import{d as oe,I as ie,n as ce,r as u,f as ne,u as pe,C as se,T as _e,o as c,c as H,i as e,w as l,a as t,U as L,F as M,k as ue,l as p,j as n,H as f,y as me,t as y,dV as de,G as fe,dX as k,J as ye,K as be,L as he,Z as we,_ as ve,N as ge,O as ke,z as xe,A as Ce,v as Ne,P as Te,Q as Ve,R as Re,__tla as Ie}from"./index-BUSn51wb.js";import{_ as Ue,__tla as Pe}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{E as Ee,__tla as Se}from"./el-text-CIwNlU-U.js";import{_ as ze,__tla as Fe}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as De,__tla as Ke}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as je,__tla as Ae}from"./index-COobLwz-.js";import{b as R,d as Q,__tla as Ge}from"./formatTime-DWdBpgsM.js";import{d as He}from"./download-e0EdwhTv.js";import{e as Le,d as Me,h as Qe,__tla as Ye}from"./index-Uo5NQqNb.js";import{_ as qe,__tla as Be}from"./ReceivablePlanForm.vue_vue_type_script_setup_true_lang-BzUD040F.js";import{a as Je,__tla as Oe}from"./index-CD52sTBY.js";import{_ as Xe,__tla as Ze}from"./ReceivableForm.vue_vue_type_script_setup_true_lang-37IhTDRa.js";import{__tla as We}from"./index-Cch5e1V0.js";import"./color-BN7ZL7BD.js";import{__tla as $e}from"./el-card-CJbXGyyg.js";import{__tla as ea}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as aa}from"./index-BYXzDB8j.js";import{__tla as la}from"./index-DrB1WZUR.js";import{__tla as ta}from"./index-D3Ji6shA.js";let Y,ra=Promise.all([(()=>{try{return Ie}catch{}})(),(()=>{try{return Pe}catch{}})(),(()=>{try{return Se}catch{}})(),(()=>{try{return Fe}catch{}})(),(()=>{try{return Ke}catch{}})(),(()=>{try{return Ae}catch{}})(),(()=>{try{return Ge}catch{}})(),(()=>{try{return Ye}catch{}})(),(()=>{try{return Be}catch{}})(),(()=>{try{return Oe}catch{}})(),(()=>{try{return Ze}catch{}})(),(()=>{try{return We}catch{}})(),(()=>{try{return $e}catch{}})(),(()=>{try{return ea}catch{}})(),(()=>{try{return aa}catch{}})(),(()=>{try{return la}catch{}})(),(()=>{try{return ta}catch{}})()]).then(async()=>{Y=oe({name:"ReceivablePlan",__name:"index",setup(oa){const x=ie(),{t:q}=ce(),C=u(!0),I=u(0),U=u([]),i=ne({pageNo:1,pageSize:10,sceneType:"1",customerId:void 0,contractNo:void 0}),P=u(),N=u(!1),T=u("1"),E=u([]),B=_=>{i.sceneType=_.paneName,b()},d=async()=>{C.value=!0;try{const _=await Le(i);U.value=_.list,I.value=_.total}finally{C.value=!1}},b=()=>{i.pageNo=1,d()},J=()=>{P.value.resetFields(),b()},S=u(),z=(_,o)=>{S.value.open(_,o)},F=u(),O=async()=>{try{await x.exportConfirm(),N.value=!0;const _=await Qe(i);He.excel(_,"\u56DE\u6B3E\u8BA1\u5212.xls")}catch{}finally{N.value=!1}},{push:D}=pe();return se(async()=>{await d(),E.value=await Je()}),(_,o)=>{const K=je,X=ye,Z=be,V=he,W=we,w=ve,m=ge,$=ke,j=De,A=xe,ee=Ce,G=Ne,r=Te,ae=ze,v=Ee,le=Ve,te=Ue,h=_e("hasPermi"),re=Re;return c(),H(M,null,[e(K,{title:"\u3010\u56DE\u6B3E\u3011\u56DE\u6B3E\u7BA1\u7406\u3001\u56DE\u6B3E\u8BA1\u5212",url:"https://doc.iocoder.cn/crm/receivable/"}),e(K,{title:"\u3010\u901A\u7528\u3011\u6570\u636E\u6743\u9650",url:"https://doc.iocoder.cn/crm/permission/"}),e(j,null,{default:l(()=>[e($,{ref_key:"queryFormRef",ref:P,inline:!0,model:t(i),class:"-mb-15px","label-width":"68px"},{default:l(()=>[e(V,{label:"\u5BA2\u6237\u540D\u79F0",prop:"customerId"},{default:l(()=>[e(Z,{modelValue:t(i).customerId,"onUpdate:modelValue":o[0]||(o[0]=a=>t(i).customerId=a),class:"!w-240px",placeholder:"\u8BF7\u9009\u62E9\u5BA2\u6237",onKeyup:L(b,["enter"])},{default:l(()=>[(c(!0),H(M,null,ue(t(E),a=>(c(),p(X,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(V,{label:"\u5408\u540C\u7F16\u53F7",prop:"contractNo"},{default:l(()=>[e(W,{modelValue:t(i).contractNo,"onUpdate:modelValue":o[1]||(o[1]=a=>t(i).contractNo=a),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u5408\u540C\u7F16\u53F7",onKeyup:L(b,["enter"])},null,8,["modelValue"])]),_:1}),e(V,null,{default:l(()=>[e(m,{onClick:b},{default:l(()=>[e(w,{class:"mr-5px",icon:"ep:search"}),n(" \u641C\u7D22 ")]),_:1}),e(m,{onClick:J},{default:l(()=>[e(w,{class:"mr-5px",icon:"ep:refresh"}),n(" \u91CD\u7F6E ")]),_:1}),f((c(),p(m,{plain:"",type:"primary",onClick:o[2]||(o[2]=a=>z("create"))},{default:l(()=>[e(w,{class:"mr-5px",icon:"ep:plus"}),n(" \u65B0\u589E ")]),_:1})),[[h,["crm:receivable-plan:create"]]]),f((c(),p(m,{loading:t(N),plain:"",type:"success",onClick:O},{default:l(()=>[e(w,{class:"mr-5px",icon:"ep:download"}),n(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[h,["crm:receivable-plan:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(j,null,{default:l(()=>[e(ee,{modelValue:t(T),"onUpdate:modelValue":o[3]||(o[3]=a=>me(T)?T.value=a:null),onTabClick:B},{default:l(()=>[e(A,{label:"\u6211\u8D1F\u8D23\u7684",name:"1"}),e(A,{label:"\u4E0B\u5C5E\u8D1F\u8D23\u7684",name:"3"})]),_:1},8,["modelValue"]),f((c(),p(le,{data:t(U),"show-overflow-tooltip":!0,stripe:!0},{default:l(()=>[e(r,{align:"center",fixed:"left",label:"\u5BA2\u6237\u540D\u79F0",prop:"customerName",width:"150"},{default:l(a=>[e(G,{underline:!1,type:"primary",onClick:g=>{return s=a.row.customerId,void D({name:"CrmCustomerDetail",params:{id:s}});var s}},{default:l(()=>[n(y(a.row.customerName),1)]),_:2},1032,["onClick"])]),_:1}),e(r,{align:"center",label:"\u5408\u540C\u7F16\u53F7",prop:"contractNo",width:"200px"}),e(r,{align:"center",label:"\u671F\u6570",prop:"period"},{default:l(a=>[e(G,{underline:!1,type:"primary",onClick:g=>{return s=a.row.id,void D({name:"CrmReceivablePlanDetail",params:{id:s}});var s}},{default:l(()=>[n(y(a.row.period),1)]),_:2},1032,["onClick"])]),_:1}),e(r,{align:"center",label:"\u8BA1\u5212\u56DE\u6B3E\u91D1\u989D\uFF08\u5143\uFF09",prop:"price",width:"160",formatter:t(de)},null,8,["formatter"]),e(r,{formatter:t(R),align:"center",label:"\u8BA1\u5212\u56DE\u6B3E\u65E5\u671F",prop:"returnTime",width:"180px"},null,8,["formatter"]),e(r,{align:"center",label:"\u63D0\u524D\u51E0\u5929\u63D0\u9192",prop:"remindDays",width:"150"}),e(r,{align:"center",label:"\u63D0\u9192\u65E5\u671F",prop:"remindTime",width:"180px",formatter:t(R)},null,8,["formatter"]),e(r,{align:"center",label:"\u56DE\u6B3E\u65B9\u5F0F",prop:"returnType",width:"130px"},{default:l(a=>[e(ae,{type:t(fe).CRM_RECEIVABLE_RETURN_TYPE,value:a.row.returnType},null,8,["type","value"])]),_:1}),e(r,{align:"center",label:"\u5907\u6CE8",prop:"remark"}),e(r,{label:"\u8D1F\u8D23\u4EBA",prop:"ownerUserName",width:"120"}),e(r,{align:"center",label:"\u5B9E\u9645\u56DE\u6B3E\u91D1\u989D\uFF08\u5143\uFF09",prop:"receivable.price",width:"160"},{default:l(a=>[a.row.receivable?(c(),p(v,{key:0},{default:l(()=>[n(y(t(k)(a.row.receivable.price)),1)]),_:2},1024)):(c(),p(v,{key:1},{default:l(()=>[n(y(t(k)(0)),1)]),_:1}))]),_:1}),e(r,{align:"center",label:"\u5B9E\u9645\u56DE\u6B3E\u65E5\u671F",prop:"receivable.returnTime",width:"180px",formatter:t(R)},null,8,["formatter"]),e(r,{align:"center",label:"\u5B9E\u9645\u56DE\u6B3E\u91D1\u989D\uFF08\u5143\uFF09",prop:"receivable.price",width:"160"},{default:l(a=>[a.row.receivable?(c(),p(v,{key:0},{default:l(()=>[n(y(t(k)(a.row.price-a.row.receivable.price)),1)]),_:2},1024)):(c(),p(v,{key:1},{default:l(()=>[n(y(t(k)(a.row.price)),1)]),_:2},1024))]),_:1}),e(r,{formatter:t(Q),align:"center",label:"\u66F4\u65B0\u65F6\u95F4",prop:"updateTime",width:"180px"},null,8,["formatter"]),e(r,{formatter:t(Q),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"]),e(r,{align:"center",label:"\u521B\u5EFA\u4EBA",prop:"creatorName",width:"100px"}),e(r,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"180px"},{default:l(a=>[f((c(),p(m,{link:"",type:"success",onClick:g=>{return s=a.row,void F.value.open("create",void 0,s);var s},disabled:a.row.receivableId},{default:l(()=>[n(" \u521B\u5EFA\u56DE\u6B3E ")]),_:2},1032,["onClick","disabled"])),[[h,["crm:receivable:create"]]]),f((c(),p(m,{link:"",type:"primary",onClick:g=>z("update",a.row.id)},{default:l(()=>[n(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[h,["crm:receivable-plan:update"]]]),f((c(),p(m,{link:"",type:"danger",onClick:g=>(async s=>{try{await x.delConfirm(),await Me(s),x.success(q("common.delSuccess")),await d()}catch{}})(a.row.id)},{default:l(()=>[n(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[h,["crm:receivable-plan:delete"]]])]),_:1})]),_:1},8,["data"])),[[re,t(C)]]),e(te,{limit:t(i).pageSize,"onUpdate:limit":o[4]||(o[4]=a=>t(i).pageSize=a),page:t(i).pageNo,"onUpdate:page":o[5]||(o[5]=a=>t(i).pageNo=a),total:t(I),onPagination:d},null,8,["limit","page","total"])]),_:1}),e(qe,{ref_key:"formRef",ref:S,onSuccess:d},null,512),e(Xe,{ref_key:"receivableFormRef",ref:F,onSuccess:d},null,512)],64)}}})});export{ra as __tla,Y as default};
