import{d as y,p as s,b as w,ev as C,o as h,c as j,g as a,t as l,i as r,w as q,j as u,a0 as R,a as t,aL as i,ax as L,_ as M,q as P,__tla as k}from"./index-BUSn51wb.js";import{_ as z,__tla as A}from"./CountTo.vue_vue_type_script_setup_true_lang-BUNK7cmG.js";let x,B=Promise.all([(()=>{try{return k}catch{}})(),(()=>{try{return A}catch{}})()]).then(async()=>{let c,f,o,d,m;c={class:"flex flex-col gap-2 bg-[var(--el-bg-color-overlay)] p-6"},f={class:"flex items-center justify-between text-gray-500"},o={class:"flex flex-row items-baseline justify-between"},d={class:"flex flex-row items-center justify-between text-sm"},m=a("span",{class:"text-gray-500"},"\u6628\u65E5\u6570\u636E",-1),x=y({name:"ComparisonCard",__name:"ComparisonCard",props:{title:s.string.def("").isRequired,tag:s.string.def(""),prefix:s.string.def(""),value:s.number.def(0).isRequired,reference:s.number.def(0).isRequired,decimals:s.number.def(0)},setup(e){const p=e,n=w(()=>C(p.value,p.reference));return(D,E)=>{const _=L,b=z,g=M,v=P;return h(),j("div",c,[a("div",f,[a("span",null,l(e.title),1),r(_,null,{default:q(()=>[u(l(e.tag),1)]),_:1})]),a("div",o,[r(b,{prefix:e.prefix,"end-val":e.value,decimals:e.decimals,class:"text-3xl"},null,8,["prefix","end-val","decimals"]),a("span",{class:R(t(i)(t(n))>0?"text-red-500":"text-green-500")},[u(l(Math.abs(t(i)(t(n))))+"% ",1),r(g,{icon:t(i)(t(n))>0?"ep:caret-top":"ep:caret-bottom",class:"!text-sm"},null,8,["icon"])],2)]),r(v,{class:"mb-1! mt-2!"}),a("div",d,[m,a("span",null,l(e.prefix||"")+l(e.reference),1)])])}}})});export{x as _,B as __tla};
