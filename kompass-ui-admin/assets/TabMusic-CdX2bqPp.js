import{_ as t,__tla as _}from"./TabMusic.vue_vue_type_script_setup_true_lang-D0UZjdHy.js";import{__tla as r}from"./index-BUSn51wb.js";import{__tla as a}from"./main-DvybYriQ.js";import{__tla as l}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{__tla as o}from"./index-Cch5e1V0.js";import{__tla as c}from"./main-DwQbyLY9.js";import{__tla as m}from"./el-image-BjHZRFih.js";import{__tla as e}from"./main-CG5euiEw.js";import{__tla as s}from"./main.vue_vue_type_script_setup_true_lang-Bi4NZ0QE.js";import{__tla as i}from"./index-C4ZN3JCQ.js";import{__tla as n}from"./index-Cqwyhbsb.js";import{__tla as f}from"./formatTime-DWdBpgsM.js";import{__tla as h}from"./useUpload-gjof4KYU.js";let p=Promise.all([(()=>{try{return _}catch{}})(),(()=>{try{return r}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return c}catch{}})(),(()=>{try{return m}catch{}})(),(()=>{try{return e}catch{}})(),(()=>{try{return s}catch{}})(),(()=>{try{return i}catch{}})(),(()=>{try{return n}catch{}})(),(()=>{try{return f}catch{}})(),(()=>{try{return h}catch{}})()]).then(async()=>{});export{p as __tla,t as default};
