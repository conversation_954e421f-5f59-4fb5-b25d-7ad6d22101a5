import{d as Q,I as Z,n as B,r as p,f as D,C as J,T as W,o as i,c as X,i as a,w as t,a as e,U as Y,j as _,H as d,l as m,dV as P,G as $,F as aa,Z as ea,L as ta,_ as la,N as ra,O as oa,P as ca,Q as na,R as pa,__tla as sa}from"./index-BUSn51wb.js";import{_ as ia,__tla as _a}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as ua,__tla as da}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as ma,__tla as fa}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{E as ya,__tla as ga}from"./el-tree-select-CBuha0HW.js";import{_ as ha,__tla as wa}from"./index-COobLwz-.js";import{d as ba,__tla as xa}from"./formatTime-DWdBpgsM.js";import{d as Pa}from"./download-e0EdwhTv.js";import{P as v,__tla as va}from"./index-B00QUU3o.js";import{P as Ca,__tla as ka}from"./index-_v3tH2a8.js";import{_ as Na,__tla as Sa}from"./ProductForm.vue_vue_type_script_setup_true_lang-zadbhaBx.js";import{h as Va,d as Ia}from"./tree-BMa075Oj.js";import{__tla as Ua}from"./index-Cch5e1V0.js";import"./color-BN7ZL7BD.js";import{__tla as Fa}from"./el-card-CJbXGyyg.js";import{__tla as Oa}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as Ta}from"./index-Bsu_xqlj.js";import"./constants-A8BI3pz7.js";let F,za=Promise.all([(()=>{try{return sa}catch{}})(),(()=>{try{return _a}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return fa}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return wa}catch{}})(),(()=>{try{return xa}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return ka}catch{}})(),(()=>{try{return Sa}catch{}})(),(()=>{try{return Ua}catch{}})(),(()=>{try{return Fa}catch{}})(),(()=>{try{return Oa}catch{}})(),(()=>{try{return Ta}catch{}})()]).then(async()=>{F=Q({name:"ErpProduct",__name:"index",setup(Ea){const g=Z(),{t:O}=B(),h=p(!0),C=p([]),k=p(0),r=D({pageNo:1,pageSize:10,name:void 0,categoryId:void 0}),N=p(),w=p(!1),S=p([]),u=async()=>{h.value=!0;try{const n=await v.getProductPage(r);C.value=n.list,k.value=n.total}finally{h.value=!1}},b=()=>{r.pageNo=1,u()},T=()=>{N.value.resetFields(),b()},V=p(),I=(n,o)=>{V.value.open(n,o)},z=async()=>{try{await g.exportConfirm(),w.value=!0;const n=await v.exportProduct(r);Pa.excel(n,"\u4EA7\u54C1.xls")}catch{}finally{w.value=!1}};return J(async()=>{await u();const n=await Ca.getProductCategorySimpleList();S.value=Va(n,"id","parentId")}),(n,o)=>{const E=ha,R=ea,x=ta,q=ya,f=la,s=ra,L=oa,U=ma,c=ca,M=ua,j=na,A=ia,y=W("hasPermi"),G=pa;return i(),X(aa,null,[a(E,{title:"\u3010\u4EA7\u54C1\u3011\u4EA7\u54C1\u4FE1\u606F\u3001\u5206\u7C7B\u3001\u5355\u4F4D",url:"https://doc.iocoder.cn/erp/product/"}),a(U,null,{default:t(()=>[a(L,{class:"-mb-15px",model:e(r),ref_key:"queryFormRef",ref:N,inline:!0,"label-width":"68px"},{default:t(()=>[a(x,{label:"\u540D\u79F0",prop:"name"},{default:t(()=>[a(R,{modelValue:e(r).name,"onUpdate:modelValue":o[0]||(o[0]=l=>e(r).name=l),placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0",clearable:"",onKeyup:Y(b,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(x,{label:"\u5206\u7C7B",prop:"categoryId"},{default:t(()=>[a(q,{modelValue:e(r).categoryId,"onUpdate:modelValue":o[1]||(o[1]=l=>e(r).categoryId=l),data:e(S),props:e(Ia),"check-strictly":"","default-expand-all":"",placeholder:"\u8BF7\u8F93\u5165\u5206\u7C7B",class:"!w-240px"},null,8,["modelValue","data","props"])]),_:1}),a(x,null,{default:t(()=>[a(s,{onClick:b},{default:t(()=>[a(f,{icon:"ep:search",class:"mr-5px"}),_(" \u641C\u7D22")]),_:1}),a(s,{onClick:T},{default:t(()=>[a(f,{icon:"ep:refresh",class:"mr-5px"}),_(" \u91CD\u7F6E")]),_:1}),d((i(),m(s,{type:"primary",plain:"",onClick:o[2]||(o[2]=l=>I("create"))},{default:t(()=>[a(f,{icon:"ep:plus",class:"mr-5px"}),_(" \u65B0\u589E ")]),_:1})),[[y,["erp:product:create"]]]),d((i(),m(s,{type:"success",plain:"",onClick:z,loading:e(w)},{default:t(()=>[a(f,{icon:"ep:download",class:"mr-5px"}),_(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[y,["erp:product:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(U,null,{default:t(()=>[d((i(),m(j,{data:e(C),stripe:!0,"show-overflow-tooltip":!0},{default:t(()=>[a(c,{label:"\u6761\u7801",align:"center",prop:"barCode"}),a(c,{label:"\u540D\u79F0",align:"center",prop:"name"}),a(c,{label:"\u89C4\u683C",align:"center",prop:"standard"}),a(c,{label:"\u5206\u7C7B",align:"center",prop:"categoryName"}),a(c,{label:"\u5355\u4F4D",align:"center",prop:"unitName"}),a(c,{label:"\u91C7\u8D2D\u4EF7\u683C",align:"center",prop:"purchasePrice",formatter:e(P)},null,8,["formatter"]),a(c,{label:"\u9500\u552E\u4EF7\u683C",align:"center",prop:"salePrice",formatter:e(P)},null,8,["formatter"]),a(c,{label:"\u6700\u4F4E\u4EF7\u683C",align:"center",prop:"minPrice",formatter:e(P)},null,8,["formatter"]),a(c,{label:"\u72B6\u6001",align:"center",prop:"status"},{default:t(l=>[a(M,{type:e($).COMMON_STATUS,value:l.row.status},null,8,["type","value"])]),_:1}),a(c,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:e(ba),width:"180px"},null,8,["formatter"]),a(c,{label:"\u64CD\u4F5C",align:"center",width:"110"},{default:t(l=>[d((i(),m(s,{link:"",type:"primary",onClick:H=>I("update",l.row.id)},{default:t(()=>[_(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[y,["erp:product:update"]]]),d((i(),m(s,{link:"",type:"danger",onClick:H=>(async K=>{try{await g.delConfirm(),await v.deleteProduct(K),g.success(O("common.delSuccess")),await u()}catch{}})(l.row.id)},{default:t(()=>[_(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[y,["erp:product:delete"]]])]),_:1})]),_:1},8,["data"])),[[G,e(h)]]),a(A,{total:e(k),page:e(r).pageNo,"onUpdate:page":o[3]||(o[3]=l=>e(r).pageNo=l),limit:e(r).pageSize,"onUpdate:limit":o[4]||(o[4]=l=>e(r).pageSize=l),onPagination:u},null,8,["total","page","limit"])]),_:1}),a(Na,{ref_key:"formRef",ref:V,onSuccess:u},null,512)],64)}}})});export{za as __tla,F as default};
