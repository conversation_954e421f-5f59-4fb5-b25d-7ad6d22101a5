function D(a){let e,t,n,r=!1;return function(v){e===void 0?(e=v,t=0,n=-1):e=function(i,d){const u=new Uint8Array(i.length+d.length);return u.set(i),u.set(d,i.length),u}(e,v);const l=e.length;let c=0;for(;t<l;){r&&(e[t]===10&&(c=++t),r=!1);let i=-1;for(;t<l&&i===-1;++t)switch(e[t]){case 58:n===-1&&(n=t-c);break;case 13:r=!0;case 10:i=t}if(i===-1)break;a(e.subarray(c,i),n),c=t,n=-1}c===l?e=void 0:c!==0&&(e=e.subarray(c),t-=c)}}var R=function(a,e){var t={};for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&e.indexOf(n)<0&&(t[n]=a[n]);if(a!=null&&typeof Object.getOwnPropertySymbols=="function"){var r=0;for(n=Object.getOwnPropertySymbols(a);r<n.length;r++)e.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(a,n[r])&&(t[n[r]]=a[n[r]])}return t};const k="text/event-stream",H="last-event-id";function U(a,e){var{signal:t,headers:n,onopen:r,onmessage:v,onclose:l,onerror:c,openWhenHidden:i,fetch:d}=e,u=R(e,["signal","headers","onopen","onmessage","onclose","onerror","openWhenHidden","fetch"]);return new Promise((P,I)=>{const f=Object.assign({},n);let b;function T(){b.abort(),document.hidden||j()}f.accept||(f.accept=k),i||document.addEventListener("visibilitychange",T);let A=1e3,m=0;function O(){document.removeEventListener("visibilitychange",T),window.clearTimeout(m),b.abort()}t==null||t.addEventListener("abort",()=>{O(),P()});const N=d??window.fetch,S=r??q;async function j(){var x;b=new AbortController;try{const h=await N(a,Object.assign(Object.assign({},u),{headers:f,signal:b.signal}));await S(h),await async function(o,E){const w=o.getReader();let s;for(;!(s=await w.read()).done;)E(s.value)}(h.body,D(function(o,E,w){let s={data:"",event:"",id:"",retry:void 0};const L=new TextDecoder;return function(g,p){if(g.length===0)w==null||w(s),s={data:"",event:"",id:"",retry:void 0};else if(p>0){const $=L.decode(g.subarray(0,p)),C=p+(g[p+1]===32?2:1),y=L.decode(g.subarray(C));switch($){case"data":s.data=s.data?s.data+`
`+y:y;break;case"event":s.event=y;break;case"id":o(s.id=y);break;case"retry":const W=parseInt(y,10);isNaN(W)||E(s.retry=W)}}}}(o=>{o?f[H]=o:delete f[H]},o=>{A=o},v))),l==null||l(),O(),P()}catch(h){if(!b.signal.aborted)try{const o=(x=c==null?void 0:c(h))!==null&&x!==void 0?x:A;window.clearTimeout(m),m=window.setTimeout(j,o)}catch(o){O(),I(o)}}}j()})}function q(a){const e=a.headers.get("content-type");if(!(e!=null&&e.startsWith(k)))throw new Error(`Expected content-type to be ${k}, Actual: ${e}`)}export{U as f};
