import{_ as t,__tla as r}from"./ProcessInstanceTaskList.vue_vue_type_script_setup_true_lang-Baejc4xj.js";import{__tla as _}from"./index-BUSn51wb.js";import{__tla as a}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as l}from"./el-timeline-item-D8aDRTsd.js";import{__tla as o}from"./el-card-CJbXGyyg.js";import{__tla as m}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import"./color-BN7ZL7BD.js";import{__tla as c}from"./formatTime-DWdBpgsM.js";import{__tla as e}from"./TaskSignList.vue_vue_type_script_setup_true_lang-BoJw9trq.js";import{__tla as s}from"./el-drawer-DMK0hKF6.js";import{__tla as i}from"./TaskSignDeleteForm.vue_vue_type_script_setup_true_lang-S0I1ToNC.js";import{__tla as n}from"./index-OMcsJcjy.js";import{__tla as p}from"./formCreate-DDLxm5B5.js";let f=Promise.all([(()=>{try{return r}catch{}})(),(()=>{try{return _}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return m}catch{}})(),(()=>{try{return c}catch{}})(),(()=>{try{return e}catch{}})(),(()=>{try{return s}catch{}})(),(()=>{try{return i}catch{}})(),(()=>{try{return n}catch{}})(),(()=>{try{return p}catch{}})()]).then(async()=>{});export{f as __tla,t as default};
