import{d as q,I as L,n as O,r as p,f as Q,C as Z,T as B,o as c,c as E,i as e,w as r,a,U as k,j as d,H as f,l as g,F as G,Z as J,L as W,M as X,_ as $,N as ee,O as ae,P as le,Q as te,R as re,__tla as ne}from"./index-BUSn51wb.js";import{_ as oe,__tla as ie}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as se,__tla as pe}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{d as ce,__tla as de}from"./formatTime-DWdBpgsM.js";import{d as ue}from"./download-e0EdwhTv.js";import{_ as me,A as V,__tla as _e}from"./AgreementForm.vue_vue_type_script_setup_true_lang-CCEneRNY.js";import{__tla as fe}from"./index-Cch5e1V0.js";import{__tla as ge}from"./el-card-CJbXGyyg.js";import{__tla as ye}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";let D,he=Promise.all([(()=>{try{return ne}catch{}})(),(()=>{try{return ie}catch{}})(),(()=>{try{return pe}catch{}})(),(()=>{try{return de}catch{}})(),(()=>{try{return _e}catch{}})(),(()=>{try{return fe}catch{}})(),(()=>{try{return ge}catch{}})(),(()=>{try{return ye}catch{}})()]).then(async()=>{D=q({name:"Agreement",__name:"index",setup(we){const w=L(),{t:N}=O(),x=p(!0),C=p([]),K=p(0),l=Q({pageNo:1,pageSize:10,unionKey:void 0,title:void 0,content:void 0,version:void 0,createTime:[]}),U=p(),v=p(!1),u=async()=>{x.value=!0;try{const o=await V.getAgreementPage(l);C.value=o.list,K.value=o.total}finally{x.value=!1}},m=()=>{l.pageNo=1,u()},P=()=>{U.value.resetFields(),m()},I=p(),T=(o,t)=>{I.value.open(o,t)},S=async()=>{try{await w.exportConfirm(),v.value=!0;const o=await V.exportAgreement(l);ue.excel(o,"\u534F\u8BAE.xls")}catch{}finally{v.value=!1}};return Z(()=>{u()}),(o,t)=>{const b=J,_=W,Y=X,y=$,s=ee,z=ae,A=se,i=le,F=te,H=oe,h=B("hasPermi"),M=re;return c(),E(G,null,[e(A,null,{default:r(()=>[e(z,{class:"-mb-15px",model:a(l),ref_key:"queryFormRef",ref:U,inline:!0,"label-width":"68px"},{default:r(()=>[e(_,{label:"\u552F\u4E00\u6807\u8BC6",prop:"unionKey"},{default:r(()=>[e(b,{modelValue:a(l).unionKey,"onUpdate:modelValue":t[0]||(t[0]=n=>a(l).unionKey=n),clearable:"",onKeyup:k(m,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(_,{label:"\u534F\u8BAE\u6807\u9898",prop:"title"},{default:r(()=>[e(b,{modelValue:a(l).title,"onUpdate:modelValue":t[1]||(t[1]=n=>a(l).title=n),clearable:"",onKeyup:k(m,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(_,{label:"\u7248\u672C\u53F7",prop:"version"},{default:r(()=>[e(b,{modelValue:a(l).version,"onUpdate:modelValue":t[2]||(t[2]=n=>a(l).version=n),clearable:"",onKeyup:k(m,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),e(_,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:r(()=>[e(Y,{modelValue:a(l).createTime,"onUpdate:modelValue":t[3]||(t[3]=n=>a(l).createTime=n),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-220px"},null,8,["modelValue","default-time"])]),_:1}),e(_,null,{default:r(()=>[e(s,{onClick:m},{default:r(()=>[e(y,{icon:"ep:search",class:"mr-5px"}),d(" \u641C\u7D22")]),_:1}),e(s,{onClick:P},{default:r(()=>[e(y,{icon:"ep:refresh",class:"mr-5px"}),d(" \u91CD\u7F6E")]),_:1}),f((c(),g(s,{type:"primary",plain:"",onClick:t[4]||(t[4]=n=>T("create"))},{default:r(()=>[e(y,{icon:"ep:plus",class:"mr-5px"}),d(" \u65B0\u589E ")]),_:1})),[[h,["als:agreement:create"]]]),f((c(),g(s,{type:"success",plain:"",onClick:S,loading:a(v)},{default:r(()=>[e(y,{icon:"ep:download",class:"mr-5px"}),d(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[h,["als:agreement:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(A,null,{default:r(()=>[f((c(),g(F,{data:a(C),stripe:!0,"show-overflow-tooltip":!0,border:""},{default:r(()=>[e(i,{label:"\u4E3B\u952EID",align:"center",prop:"agreementId",width:"100"}),e(i,{label:"\u534F\u8BAE\u552F\u4E00\u6807\u8BC6",align:"center",prop:"unionKey",width:"250"}),e(i,{label:"\u534F\u8BAE\u6807\u9898",align:"center",prop:"title",width:"120"}),e(i,{label:"\u534F\u8BAE\u5185\u5BB9",align:"center",prop:"content",width:"400"}),e(i,{label:"\u534F\u8BAE\u7248\u672C\u53F7",align:"center",prop:"version",width:"100"}),e(i,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:a(ce),width:"180px"},null,8,["formatter"]),e(i,{label:"\u64CD\u4F5C",align:"center","min-width":"120px",fixed:"right"},{default:r(n=>[f((c(),g(s,{link:"",type:"primary",onClick:R=>T("update",n.row.agreementId)},{default:r(()=>[d(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[h,["als:agreement:update"]]]),f((c(),g(s,{link:"",type:"danger",onClick:R=>(async j=>{try{await w.delConfirm(),await V.deleteAgreement(j),w.success(N("common.delSuccess")),await u()}catch{}})(n.row.agreementId)},{default:r(()=>[d(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[h,["als:agreement:delete"]]])]),_:1})]),_:1},8,["data"])),[[M,a(x)]]),e(H,{total:a(K),page:a(l).pageNo,"onUpdate:page":t[5]||(t[5]=n=>a(l).pageNo=n),limit:a(l).pageSize,"onUpdate:limit":t[6]||(t[6]=n=>a(l).pageSize=n),onPagination:u},null,8,["total","page","limit"])]),_:1}),e(me,{ref_key:"formRef",ref:I,onSuccess:u},null,512)],64)}}})});export{he as __tla,D as default};
