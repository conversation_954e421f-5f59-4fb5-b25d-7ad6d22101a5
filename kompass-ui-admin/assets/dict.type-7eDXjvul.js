import{by as e,__tla as i}from"./index-BUSn51wb.js";let s,a,l,d,p,r,y,m=Promise.all([(()=>{try{return i}catch{}})()]).then(async()=>{r=()=>e.get({url:"/system/dict-type/list-all-simple"}),a=t=>e.get({url:"/system/dict-type/page",params:t}),s=t=>e.get({url:"/system/dict-type/get?id="+t}),l=t=>e.post({url:"/system/dict-type/create",data:t}),y=t=>e.put({url:"/system/dict-type/update",data:t}),d=t=>e.delete({url:"/system/dict-type/delete?id="+t}),p=t=>e.download({url:"/system/dict-type/export",params:t})});export{m as __tla,s as a,a as b,l as c,d,p as e,r as g,y as u};
