import{_ as s,__tla as l}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as o,__tla as c}from"./IFrame.vue_vue_type_script_setup_true_lang-silYuU_p.js";import{_ as n,__tla as m}from"./index-COobLwz-.js";import{d as i,o as u,l as p,w as h,i as t,__tla as f}from"./index-BUSn51wb.js";import{__tla as d}from"./el-card-CJbXGyyg.js";let a,y=Promise.all([(()=>{try{return l}catch{}})(),(()=>{try{return c}catch{}})(),(()=>{try{return m}catch{}})(),(()=>{try{return f}catch{}})(),(()=>{try{return d}catch{}})()]).then(async()=>{a=i({name:"GoView",__name:"index",setup:w=>(x,G)=>{const r=n,_=o,e=s;return u(),p(e,null,{default:h(()=>[t(r,{title:"\u5927\u5C4F\u8BBE\u8BA1\u5668",url:"https://doc.iocoder.cn/report/screen/"}),t(_,{src:"http://127.0.0.1:3000"})]),_:1})}})});export{y as __tla,a as default};
