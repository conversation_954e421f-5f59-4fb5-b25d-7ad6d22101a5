import{d as D,I as E,n as W,r as d,f as X,C as Y,T as $,o as n,c as S,i as a,w as l,a as t,U as aa,F as x,k as M,V as ea,G as P,l as u,j as i,H as f,g as la,t as v,Z as ta,L as ra,J as sa,K as oa,_ as na,N as ia,O as ua,P as ca,ax as _a,Q as pa,R as da,__tla as ma}from"./index-BUSn51wb.js";import{_ as fa,__tla as ya}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as ha,__tla as ga}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as ba,__tla as ka}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as wa,__tla as Sa}from"./index-COobLwz-.js";import{d as xa,__tla as va}from"./formatTime-DWdBpgsM.js";import{C as Ca,g as Ta,d as Va,__tla as Oa}from"./ClientForm-5xwz7-M5.js";import{__tla as Na}from"./index-Cch5e1V0.js";import"./color-BN7ZL7BD.js";import{__tla as Ua}from"./el-card-CJbXGyyg.js";import{__tla as za}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import"./constants-A8BI3pz7.js";let F,Aa=Promise.all([(()=>{try{return ma}catch{}})(),(()=>{try{return ya}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return ka}catch{}})(),(()=>{try{return Sa}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return Oa}catch{}})(),(()=>{try{return Na}catch{}})(),(()=>{try{return Ua}catch{}})(),(()=>{try{return za}catch{}})()]).then(async()=>{let C;C=["src"],F=D({name:"SystemOAuth2Client",__name:"index",setup(Ma){const T=E(),{t:G}=W(),y=d(!0),V=d(0),O=d([]),s=X({pageNo:1,pageSize:10,name:null,status:void 0}),N=d(),c=async()=>{y.value=!0;try{const _=await Ta(s);O.value=_.list,V.value=_.total}finally{y.value=!1}},h=()=>{s.pageNo=1,c()},K=()=>{N.value.resetFields(),h()},U=d(),z=(_,r)=>{U.value.open(_,r)};return Y(()=>{c()}),(_,r)=>{const R=wa,q=ta,g=ra,I=sa,j=oa,b=na,p=ia,H=ua,A=ba,o=ca,J=ha,L=_a,Q=pa,Z=fa,k=$("hasPermi"),B=da;return n(),S(x,null,[a(R,{title:"OAuth 2.0\uFF08SSO \u5355\u70B9\u767B\u5F55)",url:"https://doc.iocoder.cn/oauth2/"}),a(A,null,{default:l(()=>[a(H,{class:"-mb-15px",model:t(s),ref_key:"queryFormRef",ref:N,inline:!0,"label-width":"68px"},{default:l(()=>[a(g,{label:"\u5E94\u7528\u540D",prop:"name"},{default:l(()=>[a(q,{modelValue:t(s).name,"onUpdate:modelValue":r[0]||(r[0]=e=>t(s).name=e),placeholder:"\u8BF7\u8F93\u5165\u5E94\u7528\u540D",clearable:"",onKeyup:aa(h,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(g,{label:"\u72B6\u6001",prop:"status"},{default:l(()=>[a(j,{modelValue:t(s).status,"onUpdate:modelValue":r[1]||(r[1]=e=>t(s).status=e),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",clearable:"",class:"!w-240px"},{default:l(()=>[(n(!0),S(x,null,M(t(ea)(t(P).COMMON_STATUS),e=>(n(),u(I,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(g,null,{default:l(()=>[a(p,{onClick:h},{default:l(()=>[a(b,{icon:"ep:search",class:"mr-5px"}),i(" \u641C\u7D22")]),_:1}),a(p,{onClick:K},{default:l(()=>[a(b,{icon:"ep:refresh",class:"mr-5px"}),i(" \u91CD\u7F6E")]),_:1}),f((n(),u(p,{plain:"",type:"primary",onClick:r[2]||(r[2]=e=>z("create"))},{default:l(()=>[a(b,{icon:"ep:plus",class:"mr-5px"}),i(" \u65B0\u589E ")]),_:1})),[[k,["system:oauth2-client:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(A,null,{default:l(()=>[f((n(),u(Q,{data:t(O)},{default:l(()=>[a(o,{label:"\u5BA2\u6237\u7AEF\u7F16\u53F7",align:"center",prop:"clientId"}),a(o,{label:"\u5BA2\u6237\u7AEF\u5BC6\u94A5",align:"center",prop:"secret"}),a(o,{label:"\u5E94\u7528\u540D",align:"center",prop:"name"}),a(o,{label:"\u5E94\u7528\u56FE\u6807",align:"center",prop:"logo"},{default:l(e=>[la("img",{width:"40px",height:"40px",src:e.row.logo},null,8,C)]),_:1}),a(o,{label:"\u72B6\u6001",align:"center",prop:"status"},{default:l(e=>[a(J,{type:t(P).COMMON_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),a(o,{label:"\u8BBF\u95EE\u4EE4\u724C\u7684\u6709\u6548\u671F",align:"center",prop:"accessTokenValiditySeconds"},{default:l(e=>[i(v(e.row.accessTokenValiditySeconds)+" \u79D2",1)]),_:1}),a(o,{label:"\u5237\u65B0\u4EE4\u724C\u7684\u6709\u6548\u671F",align:"center",prop:"refreshTokenValiditySeconds"},{default:l(e=>[i(v(e.row.refreshTokenValiditySeconds)+" \u79D2",1)]),_:1}),a(o,{label:"\u6388\u6743\u7C7B\u578B",align:"center",prop:"authorizedGrantTypes"},{default:l(e=>[(n(!0),S(x,null,M(e.row.authorizedGrantTypes,(w,m)=>(n(),u(L,{"disable-transitions":!0,key:m,index:m,class:"mr-5px"},{default:l(()=>[i(v(w),1)]),_:2},1032,["index"]))),128))]),_:1}),a(o,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",width:"180",formatter:t(xa)},null,8,["formatter"]),a(o,{label:"\u64CD\u4F5C",align:"center"},{default:l(e=>[f((n(),u(p,{link:"",type:"primary",onClick:w=>z("update",e.row.id)},{default:l(()=>[i(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[k,["system:oauth2-client:update"]]]),f((n(),u(p,{link:"",type:"danger",onClick:w=>(async m=>{try{await T.delConfirm(),await Va(m),T.success(G("common.delSuccess")),await c()}catch{}})(e.row.id)},{default:l(()=>[i(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[k,["system:oauth2-client:delete"]]])]),_:1})]),_:1},8,["data"])),[[B,t(y)]]),a(Z,{total:t(V),page:t(s).pageNo,"onUpdate:page":r[3]||(r[3]=e=>t(s).pageNo=e),limit:t(s).pageSize,"onUpdate:limit":r[4]||(r[4]=e=>t(s).pageSize=e),onPagination:c},null,8,["total","page","limit"])]),_:1}),a(Ca,{ref_key:"formRef",ref:U,onSuccess:c},null,512)],64)}}})});export{Aa as __tla,F as default};
