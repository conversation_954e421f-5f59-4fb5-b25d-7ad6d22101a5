import{f as Y,G as H,d as q,n as J,I as K,r as i,o as N,c as Q,i as n,w as c,a as r,j as S,H as W,l as X,y as Z,F as $,ay as ee,aD as E,aE as ae,N as te,cc as oe,P as le,R as re,aC as se,__tla as ie}from"./index-BUSn51wb.js";import{_ as ne,__tla as ce}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{_ as ue,__tla as me}from"./Form-DJa9ov9B.js";import{g as pe,c as _e,u as de,__tla as fe}from"./combinationActivity-Jgh6nzIi.js";import{b as L,__tla as be}from"./formatTime-DWdBpgsM.js";import{r as u,__tla as he}from"./formRules-CA9eXdcX.js";import{u as ve,__tla as ye}from"./useCrudSchemas-hBakuBRx.js";import{_ as Pe,__tla as ge}from"./SpuSelect.vue_vue_type_script_setup_true_lang-Q9Ka9uY_.js";import{_ as Se,__tla as ke}from"./SpuAndSkuList.vue_vue_type_script_setup_true_lang-BlbORlY6.js";import{g as Ce,__tla as Ie}from"./index-CjyLHUq3.js";import{b as we,__tla as De}from"./spu-CW3JGweV.js";let M,Fe=Promise.all([(()=>{try{return ie}catch{}})(),(()=>{try{return ce}catch{}})(),(()=>{try{return me}catch{}})(),(()=>{try{return fe}catch{}})(),(()=>{try{return be}catch{}})(),(()=>{try{return he}catch{}})(),(()=>{try{return ye}catch{}})(),(()=>{try{return ge}catch{}})(),(()=>{try{return ke}catch{}})(),(()=>{try{return Ie}catch{}})(),(()=>{try{return De}catch{}})()]).then(async()=>{let k,C,I;k=Y({name:[u],totalLimitCount:[u],singleLimitCount:[u],startTime:[u],endTime:[u],userSize:[u],limitDuration:[u],virtualGroup:[u]}),C=Y([{label:"\u62FC\u56E2\u540D\u79F0",field:"name",isSearch:!0,isTable:!1,form:{colProps:{span:24}}},{label:"\u6D3B\u52A8\u5F00\u59CB\u65F6\u95F4",field:"startTime",formatter:L,isSearch:!0,search:{component:"DatePicker",componentProps:{valueFormat:"YYYY-MM-DD",type:"daterange"}},form:{component:"DatePicker",componentProps:{type:"date",valueFormat:"x"}},table:{width:120}},{label:"\u6D3B\u52A8\u7ED3\u675F\u65F6\u95F4",field:"endTime",formatter:L,isSearch:!0,search:{component:"DatePicker",componentProps:{valueFormat:"YYYY-MM-DD",type:"daterange"}},form:{component:"DatePicker",componentProps:{type:"date",valueFormat:"x"}},table:{width:120}},{label:"\u53C2\u4E0E\u4EBA\u6570",field:"userSize",isSearch:!1,form:{component:"InputNumber",labelMessage:"\u53C2\u4E0E\u4EBA\u6570\u4E0D\u80FD\u5C11\u4E8E\u4E24\u4EBA",value:2}},{label:"\u9650\u5236\u65F6\u957F",field:"limitDuration",isSearch:!1,isTable:!1,form:{component:"InputNumber",labelMessage:"\u9650\u5236\u65F6\u957F(\u5C0F\u65F6)",componentProps:{placeholder:"\u8BF7\u8F93\u5165\u9650\u5236\u65F6\u957F(\u5C0F\u65F6)"}}},{label:"\u603B\u9650\u8D2D\u6570\u91CF",field:"totalLimitCount",isSearch:!1,isTable:!1,form:{component:"InputNumber",value:0}},{label:"\u5355\u6B21\u9650\u8D2D\u6570\u91CF",field:"singleLimitCount",isSearch:!1,isTable:!1,form:{component:"InputNumber",value:0}},{label:"\u865A\u62DF\u6210\u56E2",field:"virtualGroup",dictType:H.INFRA_BOOLEAN_STRING,dictClass:"boolean",isSearch:!0,form:{component:"Radio",value:!1}},{label:"\u62FC\u56E2\u5546\u54C1",field:"spuId",isSearch:!1,form:{colProps:{span:24}}}]),{allSchemas:I}=ve(C),M=q({name:"PromotionCombinationActivityForm",__name:"CombinationActivityForm",emits:["success"],setup(Te,{expose:x,emit:A}){const{t:P}=J(),w=K(),p=i(!1),D=i(""),_=i(!1),F=i(""),m=i(),T=i(),R=i(),h=i([]),g=i([]),G=[{name:"productConfig.combinationPrice",rule:t=>t>=.01,message:"\u5546\u54C1\u62FC\u56E2\u4EF7\u683C\u4E0D\u80FD\u5C0F\u4E8E0.01 \uFF01\uFF01\uFF01"}],U=(t,e)=>{m.value.setValues({spuId:t}),V(t,e)},V=async(t,e,o)=>{var v;const s=[],d=await we([t]);if(d.length==0)return;h.value=[];const a=d[0],f=e===void 0?a==null?void 0:a.skus:(v=a==null?void 0:a.skus)==null?void 0:v.filter(l=>e.includes(l.id));f==null||f.forEach(l=>{let b={spuId:a.id,skuId:l.id,combinationPrice:0};if(o!==void 0){const y=o.find(B=>B.skuId===l.id);y&&(y.combinationPrice=se(y.combinationPrice)),b=y||b}l.productConfig=b}),a.skus=f,s.push({spuId:a.id,spuDetail:a,propertyList:Ce(a)}),h.value.push(a),g.value=s};x({open:async(t,e)=>{var o;if(p.value=!0,D.value=P("action."+t),F.value=t,await z(),e){_.value=!0;try{const s=await pe(e);await V(s.spuId,(o=s.products)==null?void 0:o.map(d=>d.skuId),s.products),m.value.setValues(s)}finally{_.value=!1}}}});const z=async()=>{h.value=[],g.value=[],await ee(),m.value.getElFormRef().resetFields()},O=A,j=async()=>{if(m&&await m.value.getElFormRef().validate()){_.value=!0;try{const t=E(R.value.getSkuConfigs("productConfig"));t.forEach(o=>{o.combinationPrice=ae(o.combinationPrice)});const e=E(m.value.formModel);e.products=t,F.value==="create"?(await _e(e),w.success(P("common.createSuccess"))):(await de(e),w.success(P("common.updateSuccess"))),p.value=!1,O("success")}finally{_.value=!1}}};return(t,e)=>{const o=te,s=oe,d=le,a=ue,f=ne,v=re;return N(),Q($,null,[n(f,{modelValue:r(p),"onUpdate:modelValue":e[2]||(e[2]=l=>Z(p)?p.value=l:null),title:r(D),width:"65%"},{footer:c(()=>[n(o,{disabled:r(_),type:"primary",onClick:j},{default:c(()=>[S("\u786E \u5B9A")]),_:1},8,["disabled"]),n(o,{onClick:e[1]||(e[1]=l=>p.value=!1)},{default:c(()=>[S("\u53D6 \u6D88")]),_:1})]),default:c(()=>[W((N(),X(a,{ref_key:"formRef",ref:m,"is-col":!0,rules:r(k),schema:r(I).formSchema,class:"mt-10px"},{spuId:c(()=>[n(o,{onClick:e[0]||(e[0]=l=>r(T).open())},{default:c(()=>[S("\u9009\u62E9\u5546\u54C1")]),_:1}),n(r(Se),{ref_key:"spuAndSkuListRef",ref:R,"rule-config":G,"spu-list":r(h),"spu-property-list-p":r(g)},{default:c(()=>[n(d,{align:"center",label:"\u62FC\u56E2\u4EF7\u683C(\u5143)","min-width":"168"},{default:c(({row:l})=>[n(s,{modelValue:l.productConfig.combinationPrice,"onUpdate:modelValue":b=>l.productConfig.combinationPrice=b,min:0,precision:2,step:.1,class:"w-100%"},null,8,["modelValue","onUpdate:modelValue"])]),_:1})]),_:1},8,["spu-list","spu-property-list-p"])]),_:1},8,["rules","schema"])),[[v,r(_)]])]),_:1},8,["modelValue","title"]),n(r(Pe),{ref_key:"spuSelectRef",ref:T,isSelectSku:!0,onConfirm:U},null,512)],64)}}})});export{M as _,Fe as __tla};
