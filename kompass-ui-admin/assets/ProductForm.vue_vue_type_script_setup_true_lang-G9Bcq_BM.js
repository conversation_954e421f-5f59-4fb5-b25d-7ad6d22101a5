import{d as L,r as i,e as Z,f as z,C as Q,o as n,l as m,w as s,i as a,j as P,a as l,H as W,c as V,k as y,F as U,V as R,G as F,y as X,n as Y,I as $,Z as B,L as ee,E as le,J as ae,K as se,cd as te,cc as re,s as ue,O as de,N as oe,R as ie,__tla as ne}from"./index-BUSn51wb.js";import{_ as ce,__tla as pe}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{a as me,c as _e,u as ve,__tla as fe}from"./index-CaE_tgzr.js";import{g as be,__tla as ge}from"./index-V4315SLT.js";import{h as Ve,d as ye}from"./tree-BMa075Oj.js";import{g as Ue,__tla as we}from"./index-BYXzDB8j.js";let N,he=Promise.all([(()=>{try{return ne}catch{}})(),(()=>{try{return pe}catch{}})(),(()=>{try{return fe}catch{}})(),(()=>{try{return ge}catch{}})(),(()=>{try{return we}catch{}})()]).then(async()=>{N=L({name:"CrmProductForm",__name:"ProductForm",emits:["success"],setup(Ie,{expose:S,emit:T}){const{t:v}=Y(),w=$(),c=i(!1),h=i(""),p=i(!1),I=i(""),D=Z().getUser.id,r=i({id:void 0,name:void 0,no:void 0,unit:void 0,price:NaN,status:void 0,categoryId:void 0,description:void 0,ownerUserId:-1}),x=z({name:[{required:!0,message:"\u4EA7\u54C1\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],no:[{required:!0,message:"\u4EA7\u54C1\u7F16\u7801\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],categoryId:[{required:!0,message:"\u4EA7\u54C1\u5206\u7C7BID\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],ownerUserId:[{required:!0,message:"\u8D1F\u8D23\u4EBA\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],price:[{required:!0,message:"\u4EF7\u683C\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),_=i();S({open:async(u,t)=>{if(c.value=!0,h.value=v("action."+u),I.value=u,A(),t){p.value=!0;try{r.value=await me(t)}finally{p.value=!1}}else r.value.ownerUserId=D}});const M=T,O=async()=>{if(_&&await _.value.validate()){p.value=!0;try{const u=r.value;I.value==="create"?(await _e(u),w.success(v("common.createSuccess"))):(await ve(u),w.success(v("common.updateSuccess"))),c.value=!1,M("success")}finally{p.value=!1}}},A=()=>{var u;r.value={id:void 0,name:void 0,no:void 0,unit:void 0,price:NaN,status:void 0,categoryId:void 0,description:void 0,ownerUserId:-1},(u=_.value)==null||u.resetFields()},k=i([]),C=i([]);return Q(async()=>{const u=await be({});k.value=Ve(u,"id","parentId"),C.value=await Ue()}),(u,t)=>{const f=B,d=ee,o=le,b=ae,g=se,j=te,E=re,G=ue,H=de,q=oe,J=ce,K=ie;return n(),m(J,{title:l(h),modelValue:l(c),"onUpdate:modelValue":t[9]||(t[9]=e=>X(c)?c.value=e:null)},{footer:s(()=>[a(q,{onClick:O,type:"primary",disabled:l(p)},{default:s(()=>[P("\u786E \u5B9A")]),_:1},8,["disabled"]),a(q,{onClick:t[8]||(t[8]=e=>c.value=!1)},{default:s(()=>[P("\u53D6 \u6D88")]),_:1})]),default:s(()=>[W((n(),m(H,{ref_key:"formRef",ref:_,model:l(r),rules:l(x),"label-width":"100px"},{default:s(()=>[a(G,null,{default:s(()=>[a(o,{span:12},{default:s(()=>[a(d,{label:"\u4EA7\u54C1\u540D\u79F0",prop:"name"},{default:s(()=>[a(f,{modelValue:l(r).name,"onUpdate:modelValue":t[0]||(t[0]=e=>l(r).name=e),placeholder:"\u8BF7\u8F93\u5165\u4EA7\u54C1\u540D\u79F0"},null,8,["modelValue"])]),_:1})]),_:1}),a(o,{span:12},{default:s(()=>[a(d,{label:"\u8D1F\u8D23\u4EBA",prop:"ownerUserId"},{default:s(()=>[a(g,{modelValue:l(r).ownerUserId,"onUpdate:modelValue":t[1]||(t[1]=e=>l(r).ownerUserId=e),placeholder:"\u8BF7\u9009\u62E9\u8D1F\u8D23\u4EBA",disabled:l(r).id,class:"w-1/1"},{default:s(()=>[(n(!0),V(U,null,y(l(C),e=>(n(),m(b,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),a(o,{span:12},{default:s(()=>[a(d,{label:"\u4EA7\u54C1\u7C7B\u578B",prop:"categoryId"},{default:s(()=>[a(j,{modelValue:l(r).categoryId,"onUpdate:modelValue":t[2]||(t[2]=e=>l(r).categoryId=e),options:l(k),props:l(ye),class:"w-1/1",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u4EA7\u54C1\u7C7B\u578B",filterable:""},null,8,["modelValue","options","props"])]),_:1})]),_:1}),a(o,{span:12},{default:s(()=>[a(d,{label:"\u4EA7\u54C1\u5355\u4F4D",prop:"unit"},{default:s(()=>[a(g,{modelValue:l(r).unit,"onUpdate:modelValue":t[3]||(t[3]=e=>l(r).unit=e),class:"w-1/1",placeholder:"\u8BF7\u9009\u62E9\u5355\u4F4D"},{default:s(()=>[(n(!0),V(U,null,y(l(R)(l(F).CRM_PRODUCT_UNIT),e=>(n(),m(b,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(o,{span:12},{default:s(()=>[a(d,{label:"\u4EA7\u54C1\u7F16\u7801",prop:"no"},{default:s(()=>[a(f,{modelValue:l(r).no,"onUpdate:modelValue":t[4]||(t[4]=e=>l(r).no=e),placeholder:"\u8BF7\u8F93\u5165\u4EA7\u54C1\u7F16\u7801"},null,8,["modelValue"])]),_:1})]),_:1}),a(o,{span:12},{default:s(()=>[a(d,{label:"\u4EF7\u683C",prop:"price"},{default:s(()=>[a(E,{modelValue:l(r).price,"onUpdate:modelValue":t[5]||(t[5]=e=>l(r).price=e),placeholder:"\u8BF7\u8F93\u5165\u4EF7\u683C",min:0,precision:2,step:.1,class:"w-full!"},null,8,["modelValue"])]),_:1})]),_:1}),a(o,{span:12},{default:s(()=>[a(d,{label:"\u4EA7\u54C1\u63CF\u8FF0",prop:"description"},{default:s(()=>[a(f,{modelValue:l(r).description,"onUpdate:modelValue":t[6]||(t[6]=e=>l(r).description=e),placeholder:"\u8BF7\u8F93\u5165\u4EA7\u54C1\u63CF\u8FF0"},null,8,["modelValue"])]),_:1})]),_:1}),a(o,{span:12},{default:s(()=>[a(d,{label:"\u4E0A\u67B6\u72B6\u6001",prop:"status"},{default:s(()=>[a(g,{modelValue:l(r).status,"onUpdate:modelValue":t[7]||(t[7]=e=>l(r).status=e),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",class:"w-1/1"},{default:s(()=>[(n(!0),V(U,null,y(l(R)(l(F).CRM_PRODUCT_STATUS),e=>(n(),m(b,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])),[[K,l(p)]])]),_:1},8,["title","modelValue"])}}})});export{N as _,he as __tla};
