import{d as V,r as p,f as A,at as B,T as F,o as d,c as G,i as a,w as s,j as f,H as g,a as l,l as I,G as H,dV as M,F as Q,I as D,n as Y,_ as q,N as J,s as K,P as O,Q as W,R as X,__tla as Z}from"./index-BUSn51wb.js";import{_ as $,__tla as aa}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as ta,__tla as ea}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as ra,__tla as la}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{g as oa,d as ca,__tla as na}from"./index-D3Ji6shA.js";import{_ as sa,__tla as ia}from"./ReceivableForm.vue_vue_type_script_setup_true_lang-37IhTDRa.js";import{b as pa,__tla as da}from"./formatTime-DWdBpgsM.js";let k,_a=Promise.all([(()=>{try{return Z}catch{}})(),(()=>{try{return aa}catch{}})(),(()=>{try{return ea}catch{}})(),(()=>{try{return la}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return ia}catch{}})(),(()=>{try{return da}catch{}})()]).then(async()=>{k=V({name:"CrmReceivableList",__name:"ReceivableList",props:{customerId:{},contractId:{}},setup(C,{expose:R}){const t=C,y=D(),{t:x}=Y(),_=p(!0),v=p(0),h=p([]),e=A({pageNo:1,pageSize:10,customerId:void 0,contractId:void 0}),i=async()=>{_.value=!0;try{t.customerId&&!t.contractId?e.customerId=t.customerId:t.customerId&&t.contractId&&(e.customerId=t.customerId,e.contractId=t.contractId);const o=await oa(e);h.value=o.list,v.value=o.total}finally{_.value=!1}},m=p(),b=(o,r)=>{m.value.open(o,r,{customerId:t.customerId,contractId:t.contractId})};return R({createReceivable:o=>{const r=o;m.value.open("create",void 0,r)}}),B(()=>[t.customerId,t.contractId],o=>{o[0]&&(e.pageNo=1,e.customerId=void 0,e.contractId=void 0,i())},{immediate:!0,deep:!0}),(o,r)=>{const N=q,u=J,P=K,c=O,S=ra,T=W,E=ta,U=$,w=F("hasPermi"),z=X;return d(),G(Q,null,[a(P,{justify:"end"},{default:s(()=>[a(u,{onClick:r[0]||(r[0]=n=>b("create"))},{default:s(()=>[a(N,{class:"mr-5px",icon:"icon-park:income-one"}),f(" \u521B\u5EFA\u56DE\u6B3E ")]),_:1})]),_:1}),a(U,{class:"mt-10px"},{default:s(()=>[g((d(),I(T,{data:l(h),"show-overflow-tooltip":!0,stripe:!0},{default:s(()=>[a(c,{align:"center",label:"\u56DE\u6B3E\u7F16\u53F7",prop:"no"}),a(c,{align:"center",label:"\u5BA2\u6237",prop:"customerName"}),a(c,{align:"center",label:"\u5408\u540C",prop:"contract.no"}),a(c,{formatter:l(pa),align:"center",label:"\u56DE\u6B3E\u65E5\u671F",prop:"returnTime",width:"150px"},null,8,["formatter"]),a(c,{align:"center",label:"\u56DE\u6B3E\u65B9\u5F0F",prop:"returnType",width:"130px"},{default:s(n=>[a(S,{type:l(H).CRM_RECEIVABLE_RETURN_TYPE,value:n.row.returnType},null,8,["type","value"])]),_:1}),a(c,{align:"center",label:"\u56DE\u6B3E\u91D1\u989D(\u5143)",prop:"price",formatter:l(M)},null,8,["formatter"]),a(c,{align:"center",label:"\u8D1F\u8D23\u4EBA",prop:"ownerUserName"}),a(c,{align:"center",label:"\u5907\u6CE8",prop:"remark"}),a(c,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"130px"},{default:s(n=>[g((d(),I(u,{link:"",type:"primary",onClick:L=>b("update",n.row.id)},{default:s(()=>[f(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[w,["crm:receivable:update"]]]),g((d(),I(u,{link:"",type:"danger",onClick:L=>(async j=>{try{await y.delConfirm(),await ca(j),y.success(x("common.delSuccess")),await i()}catch{}})(n.row.id)},{default:s(()=>[f(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[w,["crm:receivable:delete"]]])]),_:1})]),_:1},8,["data"])),[[z,l(_)]]),a(E,{limit:l(e).pageSize,"onUpdate:limit":r[1]||(r[1]=n=>l(e).pageSize=n),page:l(e).pageNo,"onUpdate:page":r[2]||(r[2]=n=>l(e).pageNo=n),total:l(v),onPagination:i},null,8,["limit","page","total"])]),_:1}),a(sa,{ref_key:"formRef",ref:m,onSuccess:i},null,512)],64)}}})});export{k as _,_a as __tla};
