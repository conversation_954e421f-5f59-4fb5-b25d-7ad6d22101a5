import{d as v,S as C,r as c,u as R,C as z,a as r,o as P,c as x,i as t,w as l,F as D,I,z as T,A as B,E as j,__tla as A}from"./index-BUSn51wb.js";import{g as E,_ as F,__tla as M}from"./index-CpmUC5sy.js";import{u as N,__tla as O}from"./tagsView-BOOrxb3Q.js";import{a as S,__tla as U}from"./index-CaE_tgzr.js";import{_ as V,__tla as W}from"./ProductDetailsHeader.vue_vue_type_script_setup_true_lang-B8cOSsVX.js";import{_ as k,__tla as q}from"./ProductDetailsInfo.vue_vue_type_script_setup_true_lang-DYMuFsaD.js";import{B as G,__tla as H}from"./index-pKzyIv29.js";import{__tla as J}from"./el-timeline-item-D8aDRTsd.js";import{__tla as K}from"./formatTime-DWdBpgsM.js";import{__tla as L}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{__tla as Q}from"./el-card-CJbXGyyg.js";import{__tla as X}from"./el-descriptions-item-dD3qa0ub.js";import{__tla as Y}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import"./color-BN7ZL7BD.js";import{__tla as Z}from"./ProductForm.vue_vue_type_script_setup_true_lang-G9Bcq_BM.js";import{__tla as $}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as tt}from"./index-V4315SLT.js";import"./tree-BMa075Oj.js";import{__tla as at}from"./index-BYXzDB8j.js";import{__tla as rt}from"./el-collapse-item-B_QvnH_b.js";let i,_t=Promise.all([(()=>{try{return A}catch{}})(),(()=>{try{return M}catch{}})(),(()=>{try{return O}catch{}})(),(()=>{try{return U}catch{}})(),(()=>{try{return W}catch{}})(),(()=>{try{return q}catch{}})(),(()=>{try{return H}catch{}})(),(()=>{try{return J}catch{}})(),(()=>{try{return K}catch{}})(),(()=>{try{return L}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return Z}catch{}})(),(()=>{try{return $}catch{}})(),(()=>{try{return tt}catch{}})(),(()=>{try{return at}catch{}})(),(()=>{try{return rt}catch{}})()]).then(async()=>{i=v({name:"CrmProductDetail",__name:"index",setup(lt){const p=C(),f=I(),o=Number(p.params.id),s=c(!0),e=c({}),u=async a=>{s.value=!0;try{e.value=await S(a),await y(a)}finally{s.value=!1}},n=c([]),y=async a=>{if(!a)return;const _=await E({bizType:G.CRM_PRODUCT,bizId:a});n.value=_.list},{delView:h}=N(),{currentRoute:d}=R();return z(async()=>{if(!o)return f.warning("\u53C2\u6570\u9519\u8BEF\uFF0C\u4EA7\u54C1\u4E0D\u80FD\u4E3A\u7A7A\uFF01"),void h(r(d));await u(o)}),(a,_)=>{const m=T,w=F,g=B,b=j;return P(),x(D,null,[t(V,{loading:r(s),product:r(e),onRefresh:_[0]||(_[0]=ot=>u(r(o)))},null,8,["loading","product"]),t(b,null,{default:l(()=>[t(g,null,{default:l(()=>[t(m,{label:"\u8BE6\u7EC6\u8D44\u6599"},{default:l(()=>[t(k,{product:r(e)},null,8,["product"])]),_:1}),t(m,{label:"\u64CD\u4F5C\u65E5\u5FD7"},{default:l(()=>[t(w,{"log-list":r(n)},null,8,["log-list"])]),_:1})]),_:1})]),_:1})],64)}}})});export{_t as __tla,i as default};
