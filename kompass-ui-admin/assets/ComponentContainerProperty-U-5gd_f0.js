import{d as U,o as u,l as i,w as e,aV as _,a9 as z,i as l,a as o,j as s,av as $,z as j,am as P,an as A,L as D,cl as E,cf as O,eh as q,O as F,A as G,B as H,__tla as J}from"./index-BUSn51wb.js";import{E as K,__tla as M}from"./el-card-CJbXGyyg.js";import{_ as N,__tla as Q}from"./index-11u3nuTi.js";import{u as S,__tla as W}from"./util-Dyp86Gv2.js";let f,X=Promise.all([(()=>{try{return J}catch{}})(),(()=>{try{return M}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return W}catch{}})()]).then(async()=>{f=H(U({name:"ComponentContainer",__name:"ComponentContainerProperty",props:{modelValue:{}},emits:["update:modelValue"],setup(c,{emit:v}){const R=c,h=v,{formData:a}=S(R.modelValue,h),y=[{label:"\u5916\u90E8\u8FB9\u8DDD",prop:"margin",children:[{label:"\u4E0A",prop:"marginTop"},{label:"\u53F3",prop:"marginRight"},{label:"\u4E0B",prop:"marginBottom"},{label:"\u5DE6",prop:"marginLeft"}]},{label:"\u5185\u90E8\u8FB9\u8DDD",prop:"padding",children:[{label:"\u4E0A",prop:"paddingTop"},{label:"\u53F3",prop:"paddingRight"},{label:"\u4E0B",prop:"paddingBottom"},{label:"\u5DE6",prop:"paddingLeft"}]},{label:"\u8FB9\u6846\u5706\u89D2",prop:"borderRadius",children:[{label:"\u4E0A\u5DE6",prop:"borderTopLeftRadius"},{label:"\u4E0A\u53F3",prop:"borderTopRightRadius"},{label:"\u4E0B\u53F3",prop:"borderBottomRightRadius"},{label:"\u4E0B\u5DE6",prop:"borderBottomLeftRadius"}]}];return(n,d)=>{const m=j,b=P,V=A,p=D,T=N,x=E,B=O,L=q,w=F,C=K,k=G;return u(),i(k,{stretch:""},{default:e(()=>[n.$slots.default?(u(),i(m,{key:0,label:"\u5185\u5BB9"},{default:e(()=>[_(n.$slots,"default",{},void 0,!0)]),_:3})):z("",!0),l(m,{label:"\u6837\u5F0F",lazy:""},{default:e(()=>[l(C,{header:"\u7EC4\u4EF6\u6837\u5F0F",class:"property-group"},{default:e(()=>[l(w,{model:o(a),"label-width":"80px"},{default:e(()=>[l(p,{label:"\u7EC4\u4EF6\u80CC\u666F",prop:"bgType"},{default:e(()=>[l(V,{modelValue:o(a).bgType,"onUpdate:modelValue":d[0]||(d[0]=t=>o(a).bgType=t)},{default:e(()=>[l(b,{label:"color"},{default:e(()=>[s("\u7EAF\u8272")]),_:1}),l(b,{label:"img"},{default:e(()=>[s("\u56FE\u7247")]),_:1})]),_:1},8,["modelValue"])]),_:1}),o(a).bgType==="color"?(u(),i(p,{key:0,label:"\u9009\u62E9\u989C\u8272",prop:"bgColor"},{default:e(()=>[l(T,{modelValue:o(a).bgColor,"onUpdate:modelValue":d[1]||(d[1]=t=>o(a).bgColor=t)},null,8,["modelValue"])]),_:1})):(u(),i(p,{key:1,label:"\u4E0A\u4F20\u56FE\u7247",prop:"bgImg"},{default:e(()=>[l(x,{modelValue:o(a).bgImg,"onUpdate:modelValue":d[2]||(d[2]=t=>o(a).bgImg=t),limit:1},{tip:e(()=>[s("\u5EFA\u8BAE\u5BBD\u5EA6 750px")]),_:1},8,["modelValue"])]),_:1})),l(L,{data:y,"expand-on-click-node":!1,"default-expand-all":""},{default:e(({node:t,data:r})=>[l(p,{label:r.label,prop:r.prop,"label-width":t.level===1?"80px":"62px",class:"w-full m-b-0!"},{default:e(()=>[l(B,{modelValue:o(a)[r.prop],"onUpdate:modelValue":g=>o(a)[r.prop]=g,max:100,min:0,"show-input":"","input-size":"small","show-input-controls":!1,onInput:g=>(I=>{switch(I){case"margin":a.value.marginTop=a.value.margin,a.value.marginRight=a.value.margin,a.value.marginBottom=a.value.margin,a.value.marginLeft=a.value.margin;break;case"padding":a.value.paddingTop=a.value.padding,a.value.paddingRight=a.value.padding,a.value.paddingBottom=a.value.padding,a.value.paddingLeft=a.value.padding;break;case"borderRadius":a.value.borderTopLeftRadius=a.value.borderRadius,a.value.borderTopRightRadius=a.value.borderRadius,a.value.borderBottomRightRadius=a.value.borderRadius,a.value.borderBottomLeftRadius=a.value.borderRadius}})(r.prop)},null,8,["modelValue","onUpdate:modelValue","onInput"])]),_:2},1032,["label","prop","label-width"])]),_:1}),_(n.$slots,"style",{style:$(o(a))},void 0,!0)]),_:3},8,["model"])]),_:3})]),_:3})]),_:3})}}}),[["__scopeId","data-v-3df4060d"]])});export{f as _,X as __tla};
