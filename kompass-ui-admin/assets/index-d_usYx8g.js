import{by as a,__tla as t}from"./index-BUSn51wb.js";let p,l=Promise.all([(()=>{try{return t}catch{}})()]).then(async()=>{p={getDepositRefundApplyPage:async e=>await a.get({url:"/als/deposit-refund-apply/page",params:e}),getDepositRefundApply:async e=>await a.get({url:"/als/deposit-refund-apply/get?id="+e}),createDepositRefundApply:async e=>await a.post({url:"/als/deposit-refund-apply/create",data:e}),updateDepositRefundApply:async e=>await a.put({url:"/als/deposit-refund-apply/update",data:e}),deleteDepositRefundApply:async e=>await a.delete({url:"/als/deposit-refund-apply/delete?id="+e}),exportDepositRefundApply:async e=>await a.download({url:"/als/deposit-refund-apply/export-excel",params:e}),dealDepositRefund:async e=>await a.put({url:"/als/deposit-refund-apply/dealDepositRefund",data:e})}});export{p as D,l as __tla};
