import{n as I,f as A,cJ as P,ew as C,cr as D,V as H,D as M,h as x,__tla as J}from"./index-BUSn51wb.js";import{e as _,b as N,f as V}from"./tree-BMa075Oj.js";import{_ as g,__tla as j}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";let E,k=Promise.all([(()=>{try{return J}catch{}})(),(()=>{try{return j}catch{}})()]).then(async()=>{let y,T,w,F,v,S;({t:y}=I()),E=l=>{const a=A({searchSchema:[],tableColumns:[],formSchema:[],detailSchema:[]}),o=T(l,a);a.searchSchema=o||[];const s=w(l);a.tableColumns=s||[];const t=F(l,a);a.formSchema=t;const r=v(l);return a.detailSchema=r,{allSchemas:a}},T=(l,a)=>{const o=[],s=[];_(l,t=>{var r,p,e,m;if(t!=null&&t.isSearch||(r=t.search)!=null&&r.show){let u=((p=t==null?void 0:t.search)==null?void 0:p.component)||"Input";const f=[];let h={};if(t.dictType){const n={label:"\u5168\u90E8",value:""};f.push(n),P(t.dictType).forEach(c=>{f.push(c)}),h={options:f},(e=t.search)!=null&&e.component||(u="Select")}const d=C({component:u,...t.search,field:t.field,label:((m=t.search)==null?void 0:m.label)||t.label},{componentProps:h});d.api&&s.push(async()=>{var c;const n=await d.api();if(n){const i=D(a.searchSchema,b=>b.field===d.field);i!==-1&&(a.searchSchema[i].componentProps.options=S(n,(c=d.componentProps.optionsAlias)==null?void 0:c.labelField))}}),delete d.show,o.push(d)}});for(const t of s)t();return o},w=l=>{const a=N(l,{conversion:o=>{var s;if((o==null?void 0:o.isTable)!==!1&&((s=o==null?void 0:o.table)==null?void 0:s.show)!==!1)return!o.formatter&&o.dictType&&(o.formatter=(t,r,p)=>x(g,{type:o.dictType,value:p})),{...o.table,...o}}});return V(a,o=>(o.children===void 0&&delete o.children,!!o.field))},F=(l,a)=>{const o=[],s=[];_(l,t=>{var r,p,e,m,u;if((t==null?void 0:t.isForm)!==!1&&((r=t==null?void 0:t.form)==null?void 0:r.show)!==!1){let f=((p=t==null?void 0:t.form)==null?void 0:p.component)||"Input",h="";(e=t.form)!=null&&e.value?h=(m=t.form)==null?void 0:m.value:f==="InputNumber"&&(h=0);let d={};if(t.dictType){const c=[];t.dictClass&&t.dictClass==="number"?H(t.dictType).forEach(i=>{c.push(i)}):t.dictClass&&t.dictClass==="boolean"?M(t.dictType).forEach(i=>{c.push(i)}):P(t.dictType).forEach(i=>{c.push(i)}),d={options:c},t.form&&t.form.component||(f="Select")}const n=C({component:f,value:h,...t.form,field:t.field,label:((u=t.form)==null?void 0:u.label)||t.label},{componentProps:d});n.api&&s.push(async()=>{var i;const c=await n.api();if(c){const b=D(a.formSchema,Y=>Y.field===n.field);b!==-1&&(a.formSchema[b].componentProps.options=S(c,(i=n.componentProps.optionsAlias)==null?void 0:i.labelField))}}),delete n.show,o.push(n)}});for(const t of s)t();return o},v=l=>{const a=[];return _(l,o=>{var s,t,r,p,e;if((o==null?void 0:o.isDetail)!==!1&&((s=o.detail)==null?void 0:s.show)!==!1){const m={...o.detail,field:o.field,label:((t=o.detail)==null?void 0:t.label)||o.label};o.dictType&&(m.dictType=o.dictType),((r=o.detail)!=null&&r.dateFormat||o.formatter=="formatDate")&&(m.dateFormat=(p=o==null?void 0:o.detail)!=null&&p.dateFormat?(e=o==null?void 0:o.detail)==null?void 0:e.dateFormat:"YYYY-MM-DD HH:mm:ss"),delete m.show,a.push(m)}}),a},S=(l,a)=>l==null?void 0:l.map(o=>(a?o.labelField=y(o.labelField):o.label=y(o.label),o))});export{k as __tla,E as u};
