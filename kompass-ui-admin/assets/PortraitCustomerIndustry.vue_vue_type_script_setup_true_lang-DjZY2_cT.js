import{d as O,r as h,f as y,C as D,o as g,c as L,i as t,w as r,a as o,H as W,l as A,G as m,F as N,aG as b,aA as Y,el as C,ej as w,E as j,s as q,P as z,Q as G,R as F,__tla as H}from"./index-BUSn51wb.js";import{_ as Q,__tla as k}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{E as B,__tla as J}from"./el-card-CJbXGyyg.js";import{E as K,__tla as V}from"./el-skeleton-item-tDN8U6BH.js";import{_ as X,__tla as Z}from"./Echart.vue_vue_type_script_setup_true_lang-DnLZLH3w.js";import{S as $,__tla as tt}from"./portrait-BcNwms8P.js";let v,et=Promise.all([(()=>{try{return H}catch{}})(),(()=>{try{return k}catch{}})(),(()=>{try{return J}catch{}})(),(()=>{try{return V}catch{}})(),(()=>{try{return Z}catch{}})(),(()=>{try{return tt}catch{}})()]).then(async()=>{v=O({name:"PortraitCustomerIndustry",__name:"PortraitCustomerIndustry",props:{queryParams:{}},setup(I,{expose:R}){const P=I,n=h(!1),p=h([]),i=y({title:{text:"\u5168\u90E8\u5BA2\u6237",left:"center"},tooltip:{trigger:"item"},legend:{orient:"vertical",left:"left"},toolbox:{feature:{saveAsImage:{show:!0,name:"\u5168\u90E8\u5BA2\u6237"}}},series:[{name:"\u5168\u90E8\u5BA2\u6237",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:40,fontWeight:"bold"}},labelLine:{show:!1},data:[]}]}),d=y({title:{text:"\u6210\u4EA4\u5BA2\u6237",left:"center"},tooltip:{trigger:"item"},legend:{orient:"vertical",left:"left"},toolbox:{feature:{saveAsImage:{show:!0,name:"\u6210\u4EA4\u5BA2\u6237"}}},series:[{name:"\u6210\u4EA4\u5BA2\u6237",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:40,fontWeight:"bold"}},labelLine:{show:!1},data:[]}]}),c=async()=>{n.value=!0;const s=await $.getCustomerIndustry(P.queryParams);i.series&&i.series[0]&&i.series[0].data&&(i.series[0].data=s.map(a=>({name:b(m.CRM_CUSTOMER_INDUSTRY,a.industryId),value:a.customerCount}))),d.series&&d.series[0]&&d.series[0].data&&(d.series[0].data=s.map(a=>({name:b(m.CRM_CUSTOMER_INDUSTRY,a.industryId),value:a.dealCount}))),S(s),p.value=s,n.value=!1};R({loadData:c});const S=s=>{if(Y(s))return;const a=s,u=C(a.map(e=>e.customerCount)),_=C(a.map(e=>e.dealCount));a.forEach(e=>{e.industryPortion=e.customerCount===0?0:w(e.customerCount,u),e.dealPortion=e.dealCount===0?0:w(e.dealCount,_)})};return D(()=>{c()}),(s,a)=>{const u=X,_=K,e=j,x=q,f=B,l=z,E=Q,M=G,T=F;return g(),L(N,null,[t(f,{shadow:"never"},{default:r(()=>[t(x,{gutter:20},{default:r(()=>[t(e,{span:12},{default:r(()=>[t(_,{loading:o(n),animated:""},{default:r(()=>[t(u,{height:500,options:o(i)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),t(e,{span:12},{default:r(()=>[t(_,{loading:o(n),animated:""},{default:r(()=>[t(u,{height:500,options:o(d)},null,8,["options"])]),_:1},8,["loading"])]),_:1})]),_:1})]),_:1}),t(f,{class:"mt-16px",shadow:"never"},{default:r(()=>[W((g(),A(M,{data:o(p)},{default:r(()=>[t(l,{align:"center",label:"\u5E8F\u53F7",type:"index",width:"80"}),t(l,{align:"center",label:"\u5BA2\u6237\u884C\u4E1A",prop:"industryId",width:"100"},{default:r(U=>[t(E,{type:o(m).CRM_CUSTOMER_INDUSTRY,value:U.row.industryId},null,8,["type","value"])]),_:1}),t(l,{align:"center",label:"\u5BA2\u6237\u4E2A\u6570","min-width":"200",prop:"customerCount"}),t(l,{align:"center",label:"\u6210\u4EA4\u4E2A\u6570","min-width":"200",prop:"dealCount"}),t(l,{align:"center",label:"\u884C\u4E1A\u5360\u6BD4(%)","min-width":"200",prop:"industryPortion"}),t(l,{align:"center",label:"\u6210\u4EA4\u5360\u6BD4(%)","min-width":"200",prop:"dealPortion"})]),_:1},8,["data"])),[[T,o(n)]])]),_:1})],64)}}})});export{v as _,et as __tla};
