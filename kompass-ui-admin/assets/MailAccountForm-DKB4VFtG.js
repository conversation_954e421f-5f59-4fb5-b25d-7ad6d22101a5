import{_ as t,__tla as r}from"./MailAccountForm.vue_vue_type_script_setup_true_lang-CfXUy8Wq.js";import{__tla as _}from"./index-BUSn51wb.js";import{__tla as a}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as l}from"./Form-DJa9ov9B.js";import{__tla as o}from"./el-virtual-list-4L-8WDNg.js";import{__tla as m}from"./el-tree-select-CBuha0HW.js";import{__tla as c}from"./el-time-select-C-_NEIfl.js";import{__tla as e}from"./InputPassword-RefetKoR.js";import{__tla as s}from"./index-Dtskw_Cu.js";import{__tla as i}from"./account.data-fya8ok4m.js";import{__tla as p}from"./formatTime-DWdBpgsM.js";import{__tla as n}from"./formRules-CA9eXdcX.js";import{__tla as f}from"./useCrudSchemas-hBakuBRx.js";import"./tree-BMa075Oj.js";import{__tla as h}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import"./color-BN7ZL7BD.js";let u=Promise.all([(()=>{try{return r}catch{}})(),(()=>{try{return _}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return m}catch{}})(),(()=>{try{return c}catch{}})(),(()=>{try{return e}catch{}})(),(()=>{try{return s}catch{}})(),(()=>{try{return i}catch{}})(),(()=>{try{return p}catch{}})(),(()=>{try{return n}catch{}})(),(()=>{try{return f}catch{}})(),(()=>{try{return h}catch{}})()]).then(async()=>{});export{u as __tla,t as default};
