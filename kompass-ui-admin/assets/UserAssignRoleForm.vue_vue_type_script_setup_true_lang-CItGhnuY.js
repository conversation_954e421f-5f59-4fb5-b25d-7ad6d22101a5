import{d as C,n as S,I as H,r as o,o as i,l as c,w as t,i as d,a as l,j as y,H as J,c as K,F as L,k as D,y as N,Z as O,L as P,J as Z,K as q,O as z,N as B,R as E,__tla as G}from"./index-BUSn51wb.js";import{_ as M,__tla as Q}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{c as T,d as W,__tla as X}from"./index-CODXyRlK.js";import{g as Y,__tla as $}from"./index-CCFX7HyJ.js";let V,ee=Promise.all([(()=>{try{return G}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return $}catch{}})()]).then(async()=>{V=C({name:"SystemUserAssignRoleForm",__name:"UserAssignRoleForm",emits:["success"],setup(ae,{expose:k,emit:b}){const{t:I}=S(),h=H(),r=o(!1),n=o(!1),e=o({id:-1,nickname:"",username:"",roleIds:[]}),m=o(),f=o([]);k({open:async u=>{r.value=!0,R(),e.value.id=u.id,e.value.username=u.username,e.value.nickname=u.nickname,n.value=!0;try{e.value.roleIds=await T(u.id)}finally{n.value=!1}f.value=await Y()}});const w=b,U=async()=>{if(m&&await m.value.validate()){n.value=!0;try{await W({userId:e.value.id,roleIds:e.value.roleIds}),h.success(I("common.updateSuccess")),r.value=!1,w("success",!0)}finally{n.value=!1}}},R=()=>{var u;e.value={id:-1,nickname:"",username:"",roleIds:[]},(u=m.value)==null||u.resetFields()};return(u,s)=>{const v=O,_=P,F=Z,g=q,x=z,p=B,j=M,A=E;return i(),c(j,{modelValue:l(r),"onUpdate:modelValue":s[4]||(s[4]=a=>N(r)?r.value=a:null),title:"\u5206\u914D\u89D2\u8272"},{footer:t(()=>[d(p,{disabled:l(n),type:"primary",onClick:U},{default:t(()=>[y("\u786E \u5B9A")]),_:1},8,["disabled"]),d(p,{onClick:s[3]||(s[3]=a=>r.value=!1)},{default:t(()=>[y("\u53D6 \u6D88")]),_:1})]),default:t(()=>[J((i(),c(x,{ref_key:"formRef",ref:m,model:l(e),"label-width":"80px"},{default:t(()=>[d(_,{label:"\u7528\u6237\u540D\u79F0"},{default:t(()=>[d(v,{modelValue:l(e).username,"onUpdate:modelValue":s[0]||(s[0]=a=>l(e).username=a),disabled:!0},null,8,["modelValue"])]),_:1}),d(_,{label:"\u7528\u6237\u6635\u79F0"},{default:t(()=>[d(v,{modelValue:l(e).nickname,"onUpdate:modelValue":s[1]||(s[1]=a=>l(e).nickname=a),disabled:!0},null,8,["modelValue"])]),_:1}),d(_,{label:"\u89D2\u8272"},{default:t(()=>[d(g,{modelValue:l(e).roleIds,"onUpdate:modelValue":s[2]||(s[2]=a=>l(e).roleIds=a),multiple:"",placeholder:"\u8BF7\u9009\u62E9\u89D2\u8272"},{default:t(()=>[(i(!0),K(L,null,D(l(f),a=>(i(),c(F,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])),[[A,l(n)]])]),_:1},8,["modelValue"])}}})});export{V as _,ee as __tla};
