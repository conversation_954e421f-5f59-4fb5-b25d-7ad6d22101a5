import{d as va,I as da,r as m,c7 as pa,at as ma,C as ya,o as v,l as E,w as e,g as u,i as t,j as T,a as o,y as X,a9 as D,c as V,F as Y,k as aa,t as ta,a0 as Ca,H as fa,b1 as $,e0 as ha,e1 as wa,a8 as ka,dz as ga,aK as ba,_ as Ma,N as xa,Z as Va,br as za,b3 as Ia,a5 as Ea,a6 as Ta,B as Da,__tla as Sa}from"./index-BUSn51wb.js";import{E as La,__tla as Ua}from"./el-drawer-DMK0hKF6.js";import{E as ja,__tla as Ba}from"./el-text-CIwNlU-U.js";import{E as Ka,__tla as Pa}from"./el-empty-DomufbmG.js";import{C as w,__tla as $a}from"./index-UejJy_db.js";import qa,{__tla as Aa}from"./RoleRepository-C2mkS3L0.js";import{r as Fa}from"./gpt-WhTktY3S.js";import{__tla as Ha}from"./RoleHeader-BBBy8NqQ.js";import{__tla as Na}from"./RoleList-DrJHeS8f.js";import{__tla as Oa}from"./el-card-CJbXGyyg.js";import{__tla as Qa}from"./el-dropdown-item-CIJXMVYa.js";import{__tla as Za}from"./ChatRoleForm.vue_vue_type_script_setup_true_lang-DAKVMBtZ.js";import{__tla as Ga}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import"./constants-A8BI3pz7.js";import{__tla as Ja}from"./index-DrcFYyNA.js";import{__tla as Ra}from"./RoleCategoryList-Dzs_rZGP.js";let ea,Wa=Promise.all([(()=>{try{return Sa}catch{}})(),(()=>{try{return Ua}catch{}})(),(()=>{try{return Ba}catch{}})(),(()=>{try{return Pa}catch{}})(),(()=>{try{return $a}catch{}})(),(()=>{try{return Aa}catch{}})(),(()=>{try{return Ha}catch{}})(),(()=>{try{return Na}catch{}})(),(()=>{try{return Oa}catch{}})(),(()=>{try{return Qa}catch{}})(),(()=>{try{return Za}catch{}})(),(()=>{try{return Ga}catch{}})(),(()=>{try{return Ja}catch{}})(),(()=>{try{return Ra}catch{}})()]).then(async()=>{let q,A,F,H,N,O,Q,Z,G,J;q={class:"h-100%"},A={class:"conversation-list"},F={key:0,class:"conversation-item classify-title"},H=["onClick","onMouseover"],N={class:"title-wrapper"},O=["src"],Q={class:"title"},Z={class:"button-wrapper"},G=(k=>(Ea("data-v-c852f433"),k=k(),Ta(),k))(()=>u("div",{class:"h-160px w-100%"},null,-1)),J={class:"tool-box"},ea=Da(va({__name:"ConversationList",props:{activeId:{type:String||null,required:!0}},emits:["onConversationCreate","onConversationClick","onConversationClear","onConversationDelete"],setup(k,{expose:la,emit:sa}){const z=da(),g=m(""),d=m(null),S=m(null),r=m([]),y=m({}),I=m(!1),L=m(),U=k,C=sa,ia=async s=>{if(g.value.trim().length){const a=r.value.filter(i=>i.title.includes(g.value.trim()));y.value=await j(a)}else y.value=await j(r.value)},R=async s=>{const a=r.value.filter(i=>i.id===s);C("onConversationClick",a[0])&&(d.value=s)},f=async()=>{try{if(L.value=setTimeout(()=>{I.value=!0},50),r.value=await w.getChatConversationMyList(),r.value.sort((s,a)=>a.createTime-s.createTime),r.value.length===0)return d.value=null,void(y.value={});y.value=await j(r.value)}finally{L.value&&clearTimeout(L.value),I.value=!1}},j=async s=>{const a={\u7F6E\u9876:[],\u4ECA\u5929:[],\u4E00\u5929\u524D:[],\u4E09\u5929\u524D:[],\u4E03\u5929\u524D:[],\u4E09\u5341\u5929\u524D:[]},i=Date.now(),_=864e5,B=3*_,K=7*_,M=30*_;for(const l of s){if(l.pinned){a.\u7F6E\u9876.push(l);continue}const h=i-l.createTime;h<_?a.\u4ECA\u5929.push(l):h<B?a.\u4E00\u5929\u524D.push(l):h<K?a.\u4E09\u5929\u524D.push(l):h<M?a.\u4E03\u5929\u524D.push(l):a.\u4E09\u5341\u5929\u524D.push(l)}return a},W=async()=>{const s=await w.createChatConversationMy({});await f(),await R(s),C("onConversationCreate")},na=async()=>{try{await z.confirm("\u786E\u8BA4\u540E\u5BF9\u8BDD\u4F1A\u5168\u90E8\u6E05\u7A7A\uFF0C\u7F6E\u9876\u7684\u5BF9\u8BDD\u9664\u5916\u3002"),await w.deleteChatConversationMyByUnpinned(),ba({message:"\u64CD\u4F5C\u6210\u529F!",type:"success"}),d.value=null,await f(),C("onConversationClear")}catch{}},b=m(!1),oa=async()=>{b.value=!b.value},{activeId:ra}=pa(U);return ma(ra,async(s,a)=>{d.value=s}),la({createConversation:W}),ya(async()=>{await f(),U.activeId?d.value=U.activeId:r.value.length&&(d.value=r.value[0].id,await C("onConversationClick",r.value[0]))}),(s,a)=>{const i=Ma,_=xa,B=Va,K=Ka,M=ja,l=za,h=La,ca=Ia;return v(),E(ca,{width:"260px",class:"conversation-container h-100%"},{default:e(()=>[u("div",q,[t(_,{class:"w-1/1 btn-new-conversation",type:"primary",onClick:W},{default:e(()=>[t(i,{icon:"ep:plus",class:"mr-5px"}),T(" \u65B0\u5EFA\u5BF9\u8BDD ")]),_:1}),t(B,{modelValue:o(g),"onUpdate:modelValue":a[0]||(a[0]=p=>X(g)?g.value=p:null),size:"large",class:"mt-10px search-input",placeholder:"\u641C\u7D22\u5386\u53F2\u8BB0\u5F55",onKeyup:ia},{prefix:e(()=>[t(i,{icon:"ep:search"})]),_:1},8,["modelValue"]),u("div",A,[o(I)?(v(),E(K,{key:0,description:".","v-loading":o(I)},null,8,["v-loading"])):D("",!0),(v(!0),V(Y,null,aa(Object.keys(o(y)),p=>(v(),V("div",{key:p},[o(y)[p].length?(v(),V("div",F,[t(M,{class:"mx-1",size:"small",tag:"b"},{default:e(()=>[T(ta(p),1)]),_:2},1024)])):D("",!0),(v(!0),V(Y,null,aa(o(y)[p],n=>(v(),V("div",{class:"conversation-item",key:n.id,onClick:x=>R(n.id),onMouseover:x=>S.value=n.id,onMouseout:a[1]||(a[1]=x=>S.value="")},[u("div",{class:Ca(n.id===o(d)?"conversation active":"conversation")},[u("div",N,[u("img",{class:"avatar",src:n.roleAvatar||o(Fa)},null,8,O),u("span",Q,ta(n.title),1)]),fa(u("div",Z,[t(_,{class:"btn",link:"",onClick:$(x=>(async c=>{c.pinned=!c.pinned,await w.updateChatConversationMy(c),await f()})(n),["stop"])},{default:e(()=>[n.pinned?D("",!0):(v(),E(l,{key:0,title:"\u7F6E\u9876"},{default:e(()=>[t(o(ha))]),_:1})),n.pinned?(v(),E(l,{key:1,title:"\u7F6E\u9876"},{default:e(()=>[t(o(wa))]),_:1})):D("",!0)]),_:2},1032,["onClick"]),t(_,{class:"btn",link:"",onClick:$(x=>(async c=>{const{value:ua}=await ga.prompt("\u4FEE\u6539\u6807\u9898",{inputPattern:/^[\s\S]*.*\S[\s\S]*$/,inputErrorMessage:"\u6807\u9898\u4E0D\u80FD\u4E3A\u7A7A",inputValue:c.title});await w.updateChatConversationMy({id:c.id,title:ua}),z.success("\u91CD\u547D\u540D\u6210\u529F"),await f();const P=r.value.filter(_a=>_a.id===c.id);P.length>0&&d.value===P[0].id&&C("onConversationClick",P[0])})(n),["stop"])},{default:e(()=>[t(l,{title:"\u7F16\u8F91"},{default:e(()=>[t(i,{icon:"ep:edit"})]),_:1})]),_:2},1032,["onClick"]),t(_,{class:"btn",link:"",onClick:$(x=>(async c=>{try{await z.delConfirm(`\u662F\u5426\u786E\u8BA4\u5220\u9664\u5BF9\u8BDD - ${c.title}?`),await w.deleteChatConversationMy(c.id),z.success("\u5BF9\u8BDD\u5DF2\u5220\u9664"),await f(),C("onConversationDelete",c)}catch{}})(n),["stop"])},{default:e(()=>[t(l,{title:"\u5220\u9664\u5BF9\u8BDD"},{default:e(()=>[t(i,{icon:"ep:delete"})]),_:1})]),_:2},1032,["onClick"])],512),[[ka,o(S)===n.id]])],2)],40,H))),128))]))),128)),G])]),u("div",J,[u("div",{onClick:oa},[t(i,{icon:"ep:user"}),t(M,{size:"small"},{default:e(()=>[T("\u89D2\u8272\u4ED3\u5E93")]),_:1})]),u("div",{onClick:na},[t(i,{icon:"ep:delete"}),t(M,{size:"small"},{default:e(()=>[T("\u6E05\u7A7A\u672A\u7F6E\u9876\u5BF9\u8BDD")]),_:1})])]),t(h,{modelValue:o(b),"onUpdate:modelValue":a[2]||(a[2]=p=>X(b)?b.value=p:null),title:"\u89D2\u8272\u4ED3\u5E93",size:"754px"},{default:e(()=>[t(qa)]),_:1},8,["modelValue"])]),_:1})}}}),[["__scopeId","data-v-c852f433"]])});export{Wa as __tla,ea as default};
