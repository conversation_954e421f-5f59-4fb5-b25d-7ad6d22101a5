import{d as F,r as o,ba as I,o as m,c as L,H as v,a8 as w,a as r,aA as b,g as y,i as _,w as g,y as T,l as H,a9 as k,F as z,ay as x,z as C,A as E,b2 as N,a5 as P,a6 as U,B as q,__tla as D}from"./index-BUSn51wb.js";import{E as G,__tla as J}from"./el-empty-DomufbmG.js";import{_ as K,__tla as O}from"./ProductBrowsingHistory.vue_vue_type_script_setup_true_lang-2Em6KJrO.js";import{_ as Q,__tla as W}from"./OrderBrowsingHistory.vue_vue_type_script_setup_true_lang-v98FEZn0.js";import{__tla as X}from"./ProductItem-bFAWKK8H.js";import{__tla as Y}from"./el-image-BjHZRFih.js";import{__tla as Z}from"./concat-MbtHYl7y.js";import{__tla as $}from"./OrderItem-DUnNh_aP.js";import{__tla as aa}from"./index-BQq32Shw.js";let B,ta=Promise.all([(()=>{try{return D}catch{}})(),(()=>{try{return J}catch{}})(),(()=>{try{return O}catch{}})(),(()=>{try{return W}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return Z}catch{}})(),(()=>{try{return $}catch{}})(),(()=>{try{return aa}catch{}})()]).then(async()=>{let f,d;f={class:"kefu"},d=(c=>(P("data-v-7eec1623"),c=c(),U(),c))(()=>y("div",{class:"header-title h-60px flex justify-center items-center"},"\u4ED6\u7684\u8DB3\u8FF9",-1)),B=q(F({name:"MemberBrowsingHistory",__name:"MemberBrowsingHistory",setup(c,{expose:M}){const t=o("a"),n=o(),u=o(),R=async a=>{t.value=a.paneName,await x(),await p()},p=async()=>{var a,e;switch(t.value){case"a":await((a=n.value)==null?void 0:a.getHistoryList(l.value));break;case"b":await((e=u.value)==null?void 0:e.getHistoryList(l.value))}},l=o({});M({initHistory:async a=>{t.value="a",l.value=a,await x(),await p()}});const h=o(),S=I(()=>{var e;const a=(e=h.value)==null?void 0:e.wrapRef;Math.abs(a.scrollHeight-a.clientHeight-a.scrollTop)<1&&(async()=>{var s,i;switch(t.value){case"a":await((s=n.value)==null?void 0:s.loadMore());break;case"b":await((i=u.value)==null?void 0:i.loadMore())}})()},200);return(a,e)=>{const s=C,i=E,V=N,j=G;return m(),L(z,null,[v(y("div",f,[d,_(i,{modelValue:r(t),"onUpdate:modelValue":e[0]||(e[0]=A=>T(t)?t.value=A:null),class:"demo-tabs",onTabClick:R},{default:g(()=>[_(s,{label:"\u6700\u8FD1\u6D4F\u89C8",name:"a"}),_(s,{label:"\u8BA2\u5355\u5217\u8868",name:"b"})]),_:1},8,["modelValue"]),y("div",null,[_(V,{ref_key:"scrollbarRef",ref:h,always:"",height:"calc(100vh - 400px)",onScroll:r(S)},{default:g(()=>[r(t)==="a"?(m(),H(K,{key:0,ref_key:"productBrowsingHistoryRef",ref:n},null,512)):k("",!0),r(t)==="b"?(m(),H(Q,{key:1,ref_key:"orderBrowsingHistoryRef",ref:u},null,512)):k("",!0)]),_:1},8,["onScroll"])])],512),[[w,!r(b)(r(l))]]),v(_(j,{description:"\u8BF7\u9009\u62E9\u5DE6\u4FA7\u7684\u4E00\u4E2A\u4F1A\u8BDD\u540E\u5F00\u59CB"},null,512),[[w,r(b)(r(l))]])],64)}}}),[["__scopeId","data-v-7eec1623"]])});export{ta as __tla,B as default};
