import{d as J,I as K,n as Q,r as _,f as W,C as X,T as Z,o,c as m,i as e,w as t,a as l,F as f,k as h,l as d,dR as $,G as b,V as aa,D as ea,j as x,H as F,a9 as la,g as ta,t as ra,J as ua,K as sa,L as oa,M as ia,_ as pa,N as da,O as na,P as ca,ce as _a,Q as ma,R as fa,__tla as ba}from"./index-BUSn51wb.js";import{_ as ga,__tla as wa}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as ha,__tla as va}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{E as ya,__tla as Sa}from"./el-image-BjHZRFih.js";import{_ as Ia,__tla as Va}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{d as xa,__tla as Ua}from"./formatTime-DWdBpgsM.js";import{I as U,__tla as ka}from"./index-Cjd1fP7g.js";import{g as Aa,__tla as Ta}from"./index-BYXzDB8j.js";import{A as Ca}from"./constants-C0I8ujwj.js";import{__tla as Ma}from"./index-Cch5e1V0.js";import"./color-BN7ZL7BD.js";import{__tla as Na}from"./el-card-CJbXGyyg.js";let R,Pa=Promise.all([(()=>{try{return ba}catch{}})(),(()=>{try{return wa}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return Sa}catch{}})(),(()=>{try{return Va}catch{}})(),(()=>{try{return Ua}catch{}})(),(()=>{try{return ka}catch{}})(),(()=>{try{return Ta}catch{}})(),(()=>{try{return Ma}catch{}})(),(()=>{try{return Na}catch{}})()]).then(async()=>{R=J({name:"AiImageManager",__name:"index",setup(Fa){const v=K(),{t:D}=Q(),y=_(!0),k=_([]),A=_(0),r=W({pageNo:1,pageSize:10,userId:void 0,platform:void 0,status:void 0,publicStatus:void 0,createTime:[]}),T=_(),S=_([]),c=async()=>{y.value=!0;try{const I=await U.getImagePage(r);k.value=I.list,A.value=I.total}finally{y.value=!1}},C=()=>{r.pageNo=1,c()},E=()=>{T.value.resetFields(),C()};return X(async()=>{c(),S.value=await Aa()}),(I,u)=>{const g=ua,w=sa,n=oa,G=ia,M=pa,V=da,O=na,N=Ia,s=ca,Y=ya,P=ha,z=_a,L=ma,H=ga,j=Z("hasPermi"),q=fa;return o(),m(f,null,[e(N,null,{default:t(()=>[e(O,{class:"-mb-15px",model:l(r),ref_key:"queryFormRef",ref:T,inline:!0,"label-width":"68px"},{default:t(()=>[e(n,{label:"\u7528\u6237\u7F16\u53F7",prop:"userId"},{default:t(()=>[e(w,{modelValue:l(r).userId,"onUpdate:modelValue":u[0]||(u[0]=a=>l(r).userId=a),clearable:"",placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u7F16\u53F7",class:"!w-240px"},{default:t(()=>[(o(!0),m(f,null,h(l(S),a=>(o(),d(g,{key:a.id,label:a.nickname,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"\u5E73\u53F0",prop:"platform"},{default:t(()=>[e(w,{modelValue:l(r).status,"onUpdate:modelValue":u[1]||(u[1]=a=>l(r).status=a),placeholder:"\u8BF7\u9009\u62E9\u5E73\u53F0",clearable:"",class:"!w-240px"},{default:t(()=>[(o(!0),m(f,null,h(l($)(l(b).AI_PLATFORM),a=>(o(),d(g,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"\u7ED8\u753B\u72B6\u6001",prop:"status"},{default:t(()=>[e(w,{modelValue:l(r).status,"onUpdate:modelValue":u[2]||(u[2]=a=>l(r).status=a),placeholder:"\u8BF7\u9009\u62E9\u7ED8\u753B\u72B6\u6001",clearable:"",class:"!w-240px"},{default:t(()=>[(o(!0),m(f,null,h(l(aa)(l(b).AI_IMAGE_STATUS),a=>(o(),d(g,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"\u662F\u5426\u53D1\u5E03",prop:"publicStatus"},{default:t(()=>[e(w,{modelValue:l(r).publicStatus,"onUpdate:modelValue":u[3]||(u[3]=a=>l(r).publicStatus=a),placeholder:"\u8BF7\u9009\u62E9\u662F\u5426\u53D1\u5E03",clearable:"",class:"!w-240px"},{default:t(()=>[(o(!0),m(f,null,h(l(ea)(l(b).INFRA_BOOLEAN_STRING),a=>(o(),d(g,{key:a.value,label:a.label,value:a.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:t(()=>[e(G,{modelValue:l(r).createTime,"onUpdate:modelValue":u[4]||(u[4]=a=>l(r).createTime=a),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-220px"},null,8,["modelValue","default-time"])]),_:1}),e(n,null,{default:t(()=>[e(V,{onClick:C},{default:t(()=>[e(M,{icon:"ep:search",class:"mr-5px"}),x(" \u641C\u7D22")]),_:1}),e(V,{onClick:E},{default:t(()=>[e(M,{icon:"ep:refresh",class:"mr-5px"}),x(" \u91CD\u7F6E")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(N,null,{default:t(()=>[F((o(),d(L,{data:l(k),stripe:!0,"show-overflow-tooltip":!0},{default:t(()=>[e(s,{label:"\u7F16\u53F7",align:"center",prop:"id",width:"180",fixed:"left"}),e(s,{label:"\u56FE\u7247",align:"center",prop:"picUrl",width:"110px",fixed:"left"},{default:t(({row:a})=>{var i;return[((i=a.picUrl)==null?void 0:i.length)>0?(o(),d(Y,{key:0,class:"h-80px w-80px",lazy:"",src:a.picUrl,"preview-src-list":[a.picUrl],"preview-teleported":"",fit:"cover"},null,8,["src","preview-src-list"])):la("",!0)]}),_:1}),e(s,{label:"\u7528\u6237",align:"center",prop:"userId",width:"180"},{default:t(a=>{var i;return[ta("span",null,ra((i=l(S).find(p=>p.id===a.row.userId))==null?void 0:i.nickname),1)]}),_:1}),e(s,{label:"\u5E73\u53F0",align:"center",prop:"platform",width:"120"},{default:t(a=>[e(P,{type:l(b).AI_PLATFORM,value:a.row.platform},null,8,["type","value"])]),_:1}),e(s,{label:"\u6A21\u578B",align:"center",prop:"model",width:"180"}),e(s,{label:"\u7ED8\u753B\u72B6\u6001",align:"center",prop:"status",width:"100"},{default:t(a=>[e(P,{type:l(b).AI_IMAGE_STATUS,value:a.row.status},null,8,["type","value"])]),_:1}),e(s,{label:"\u662F\u5426\u53D1\u5E03",align:"center",prop:"publicStatus"},{default:t(a=>[e(z,{modelValue:a.row.publicStatus,"onUpdate:modelValue":i=>a.row.publicStatus=i,"active-value":!0,"inactive-value":!1,onChange:i=>(async p=>{try{const B=p.publicStatus?"\u516C\u5F00":"\u79C1\u6709";await v.confirm('\u786E\u8BA4\u8981"'+B+'"\u8BE5\u56FE\u7247\u5417?'),await U.updateImage({id:p.id,publicStatus:p.publicStatus}),await c()}catch{p.publicStatus=!p.publicStatus}})(a.row),disabled:a.row.status!==l(Ca).SUCCESS},null,8,["modelValue","onUpdate:modelValue","onChange","disabled"])]),_:1}),e(s,{label:"\u63D0\u793A\u8BCD",align:"center",prop:"prompt",width:"180"}),e(s,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:l(xa),width:"180px"},null,8,["formatter"]),e(s,{label:"\u5BBD\u5EA6",align:"center",prop:"width"}),e(s,{label:"\u9AD8\u5EA6",align:"center",prop:"height"}),e(s,{label:"\u9519\u8BEF\u4FE1\u606F",align:"center",prop:"errorMessage"}),e(s,{label:"\u4EFB\u52A1\u7F16\u53F7",align:"center",prop:"taskId"}),e(s,{label:"\u64CD\u4F5C",align:"center",width:"100",fixed:"right"},{default:t(a=>[F((o(),d(V,{link:"",type:"danger",onClick:i=>(async p=>{try{await v.delConfirm(),await U.deleteImage(p),v.success(D("common.delSuccess")),await c()}catch{}})(a.row.id)},{default:t(()=>[x(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[j,["ai:image:delete"]]])]),_:1})]),_:1},8,["data"])),[[q,l(y)]]),e(H,{total:l(A),page:l(r).pageNo,"onUpdate:page":u[5]||(u[5]=a=>l(r).pageNo=a),limit:l(r).pageSize,"onUpdate:limit":u[6]||(u[6]=a=>l(r).pageSize=a),onPagination:c},null,8,["total","page","limit"])]),_:1})],64)}}})});export{Pa as __tla,R as default};
