import{d as q,r as o,o as _,l as c,w as t,i as u,j as p,a as l,H,c as R,k as S,F as D,y as J,I as K,J as L,K as N,L as O,Z as P,O as Z,N as z,R as A,__tla as B}from"./index-BUSn51wb.js";import{_ as E,__tla as G}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{s as M,__tla as Q}from"./index-OMcsJcjy.js";import{g as W,__tla as X}from"./index-BYXzDB8j.js";let b,Y=Promise.all([(()=>{try{return B}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return X}catch{}})()]).then(async()=>{b=q({name:"TaskSignCreateForm",__name:"TaskSignCreateForm",emits:["success"],setup($,{expose:g,emit:h}){const k=K(),d=o(!1),i=o(!1),s=o({id:"",userIds:[],type:"",reason:""}),V=o({userIds:[{required:!0,message:"\u52A0\u7B7E\u5904\u7406\u4EBA\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}],reason:[{required:!0,message:"\u52A0\u7B7E\u7406\u7531\u4E0D\u80FD\u4E3A\u7A7A",trigger:"change"}]}),n=o(),f=o([]);g({open:async r=>{d.value=!0,I(),s.value.id=r,f.value=await W()}});const w=h,y=async r=>{if(n&&await n.value.validate()){i.value=!0,s.value.type=r;try{await M(s.value),k.success("\u52A0\u7B7E\u6210\u529F"),d.value=!1,w("success")}finally{i.value=!1}}},I=()=>{var r;s.value={id:"",userIds:[],type:"",reason:""},(r=n.value)==null||r.resetFields()};return(r,e)=>{const C=L,x=N,v=O,F=P,T=Z,m=z,U=E,j=A;return _(),c(U,{modelValue:l(d),"onUpdate:modelValue":e[5]||(e[5]=a=>J(d)?d.value=a:null),title:"\u52A0\u7B7E",width:"500"},{footer:t(()=>[u(m,{disabled:l(i),type:"primary",onClick:e[2]||(e[2]=a=>y("before"))},{default:t(()=>[p(" \u5411\u524D\u52A0\u7B7E ")]),_:1},8,["disabled"]),u(m,{disabled:l(i),type:"primary",onClick:e[3]||(e[3]=a=>y("after"))},{default:t(()=>[p(" \u5411\u540E\u52A0\u7B7E ")]),_:1},8,["disabled"]),u(m,{onClick:e[4]||(e[4]=a=>d.value=!1)},{default:t(()=>[p("\u53D6 \u6D88")]),_:1})]),default:t(()=>[H((_(),c(T,{ref_key:"formRef",ref:n,model:l(s),rules:l(V),"label-width":"110px"},{default:t(()=>[u(v,{label:"\u52A0\u7B7E\u5904\u7406\u4EBA",prop:"userIds"},{default:t(()=>[u(x,{modelValue:l(s).userIds,"onUpdate:modelValue":e[0]||(e[0]=a=>l(s).userIds=a),multiple:"",clearable:"",style:{width:"100%"}},{default:t(()=>[(_(!0),R(D,null,S(l(f),a=>(_(),c(C,{key:a.id,label:a.nickname,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),u(v,{label:"\u52A0\u7B7E\u7406\u7531",prop:"reason"},{default:t(()=>[u(F,{modelValue:l(s).reason,"onUpdate:modelValue":e[1]||(e[1]=a=>l(s).reason=a),clearable:"",placeholder:"\u8BF7\u8F93\u5165\u52A0\u7B7E\u7406\u7531"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[j,l(i)]])]),_:1},8,["modelValue"])}}})});export{b as _,Y as __tla};
