import{d as A,r as y,f as D,C as U,o as f,c as E,i as a,w as n,a as e,H as I,l as N,dV as h,G as g,F as M,P as T,Q as q,R as O,__tla as F}from"./index-BUSn51wb.js";import{_ as j,__tla as B}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{E as G,__tla as H}from"./el-card-CJbXGyyg.js";import{E as L,__tla as Q}from"./el-skeleton-item-tDN8U6BH.js";import{_ as V,__tla as X}from"./Echart.vue_vue_type_script_setup_true_lang-DnLZLH3w.js";import{S as x,__tla as Y}from"./customer-DXRFD9ec.js";import{d as w,__tla as Z}from"./formatTime-DWdBpgsM.js";let C,k=Promise.all([(()=>{try{return F}catch{}})(),(()=>{try{return B}catch{}})(),(()=>{try{return H}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return Z}catch{}})()]).then(async()=>{C=A({name:"CustomerConversionStat",__name:"CustomerConversionStat",props:{queryParams:{}},setup(b,{expose:v}){const m=b,o=y(!1),u=y([]),r=D({grid:{left:20,right:40,bottom:20,containLabel:!0},legend:{},series:[{name:"\u5BA2\u6237\u8F6C\u5316\u7387",type:"line",data:[]}],toolbox:{feature:{dataZoom:{xAxisIndex:!1},brush:{type:["lineX","clear"]},saveAsImage:{show:!0,name:"\u5BA2\u6237\u8F6C\u5316\u7387\u5206\u6790"}}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},yAxis:{type:"value",name:"\u8F6C\u5316\u7387(%)"},xAxis:{type:"category",name:"\u65E5\u671F",data:[]}}),_=async()=>{o.value=!0;try{await(async()=>{const s=await x.getCustomerSummaryByDate(m.queryParams),c=await x.getContractSummary(m.queryParams);r.xAxis&&r.xAxis.data&&(r.xAxis.data=s.map(l=>l.time)),r.series&&r.series[0]&&r.series[0].data&&(r.series[0].data=s.map(l=>({name:l.time,value:l.customerCreateCount?(l.customerDealCount/l.customerCreateCount*100).toFixed(2):0}))),u.value=c})()}finally{o.value=!1}};return v({loadData:_}),U(()=>{_()}),(s,c)=>{const l=V,S=L,d=G,t=T,p=j,P=q,R=O;return f(),E(M,null,[a(d,{shadow:"never"},{default:n(()=>[a(S,{loading:e(o),animated:""},{default:n(()=>[a(l,{height:500,options:e(r)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),a(d,{shadow:"never",class:"mt-16px"},{default:n(()=>[I((f(),N(P,{data:e(u)},{default:n(()=>[a(t,{label:"\u5E8F\u53F7",align:"center",type:"index",width:"80",fixed:"left"}),a(t,{label:"\u5BA2\u6237\u540D\u79F0",align:"center",prop:"customerName","min-width":"200",fixed:"left"}),a(t,{label:"\u5408\u540C\u540D\u79F0",align:"center",prop:"contractName","min-width":"200"}),a(t,{label:"\u5408\u540C\u603B\u91D1\u989D",align:"center",prop:"totalPrice","min-width":"200",formatter:e(h)},null,8,["formatter"]),a(t,{label:"\u56DE\u6B3E\u91D1\u989D",align:"center",prop:"receivablePrice","min-width":"200",formatter:e(h)},null,8,["formatter"]),a(t,{align:"center",label:"\u5BA2\u6237\u6765\u6E90",prop:"source",width:"100"},{default:n(i=>[a(p,{type:e(g).CRM_CUSTOMER_SOURCE,value:i.row.source},null,8,["type","value"])]),_:1}),a(t,{align:"center",label:"\u5BA2\u6237\u884C\u4E1A",prop:"industryId",width:"100"},{default:n(i=>[a(p,{type:e(g).CRM_CUSTOMER_INDUSTRY,value:i.row.industryId},null,8,["type","value"])]),_:1}),a(t,{label:"\u8D1F\u8D23\u4EBA",align:"center",prop:"ownerUserName","min-width":"200"}),a(t,{label:"\u521B\u5EFA\u4EBA",align:"center",prop:"creatorUserName","min-width":"200"}),a(t,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:e(w),"min-width":"200"},null,8,["formatter"]),a(t,{label:"\u4E0B\u5355\u65E5\u671F",align:"center",prop:"orderDate",formatter:e(w),"min-width":"200",fixed:"right"},null,8,["formatter"])]),_:1},8,["data"])),[[R,e(o)]])]),_:1})],64)}}})});export{C as _,k as __tla};
