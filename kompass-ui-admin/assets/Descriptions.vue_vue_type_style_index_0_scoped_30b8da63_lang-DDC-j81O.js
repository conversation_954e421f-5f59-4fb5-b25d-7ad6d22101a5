import{d as B,p,$ as G,b as $,bs as I,cE as J,r as K,o as r,c as v,a0 as f,a as t,g as x,j as m,t as u,l as g,w as s,a9 as h,i as _,a4 as L,_ as Q,aN as R,dS as U,H as W,a8 as X,ao as F,c9 as Y,aV as d,F as Z,k as aa,ct as ea,__tla as ta}from"./index-BUSn51wb.js";import{E as la,a as ra,__tla as sa}from"./el-descriptions-item-dD3qa0ub.js";import{_ as oa,__tla as da}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";let j,ia=Promise.all([(()=>{try{return ta}catch{}})(),(()=>{try{return sa}catch{}})(),(()=>{try{return da}catch{}})()]).then(async()=>{let k;k={class:"flex items-center"},j=B({name:"Descriptions",__name:"Descriptions",props:{title:p.string.def(""),message:p.string.def(""),collapse:p.bool.def(!0),columns:p.number.def(1),schema:{type:Array,default:()=>[]},data:{type:Object,default:()=>({})}},setup(e){const O=G(),C=$(()=>O.getMobile),D=I(),E=J(),b=e,{getPrefixCls:H}=L(),i=H("descriptions"),M=$(()=>{const l=["title","message","collapse","schema","data","class"],c={...D,...b};for(const o in c)l.indexOf(o)!==-1&&delete c[o];return c}),n=K(!0),P=()=>{b.collapse&&(n.value=!t(n))};return(l,c)=>{const o=Q,T=R,z=oa,A=la,N=ra,S=U;return r(),v("div",{class:f([t(i),"bg-[var(--el-color-white)] dark:bg-[var(--el-bg-color)] dark:border-[var(--el-border-color)] dark:border-1px"])},[e.title?(r(),v("div",{key:0,class:f([`${t(i)}-header`,"h-50px flex justify-between items-center b-b-1 border-solid border-[var(--el-border-color)] px-10px cursor-pointer dark:border-[var(--el-border-color)]"]),onClick:P},[x("div",{class:f([`${t(i)}-header__title`,"relative font-18px font-bold ml-10px"])},[x("div",k,[m(u(e.title)+" ",1),e.message?(r(),g(T,{key:0,content:e.message,placement:"right"},{default:s(()=>[_(o,{class:"ml-5px",icon:"ep:warning"})]),_:1},8,["content"])):h("",!0)])],2),e.collapse?(r(),g(o,{key:0,icon:t(n)?"ep:arrow-down":"ep:arrow-up"},null,8,["icon"])):h("",!0)],2)):h("",!0),_(S,null,{default:s(()=>[W(x("div",{class:f([`${t(i)}-content`,"p-10px"])},[_(N,F({column:b.columns,direction:t(C)?"vertical":"horizontal",border:""},t(M)),Y({default:s(()=>[(r(!0),v(Z,null,aa(e.schema,a=>(r(),g(A,F({key:a.field,"min-width":"80"},(V=>{const q=["field"],y={...V};for(const w in y)q.indexOf(w)!==-1&&delete y[w];return y})(a)),{label:s(()=>[d(l.$slots,`${a.field}-label`,{row:{label:a.label}},()=>[m(u(a.label),1)],!0)]),default:s(()=>[a.dateFormat?d(l.$slots,"default",{key:0},()=>[m(u(e.data[a.field]!==null?t(ea)(e.data[a.field]).format(a.dateFormat):""),1)],!0):a.dictType?d(l.$slots,"default",{key:1},()=>[_(z,{type:a.dictType,value:e.data[a.field]+""},null,8,["type","value"])],!0):d(l.$slots,a.field,{key:2,row:e.data},()=>[m(u(a.mappedField?e.data[a.mappedField]:e.data[a.field]),1)],!0)]),_:2},1040))),128))]),_:2},[t(E).extra?{name:"extra",fn:s(()=>[d(l.$slots,"extra",{},void 0,!0)]),key:"0"}:void 0]),1040,["column","direction"])],2),[[X,t(n)]])]),_:3})],2)}}})});export{j as _,ia as __tla};
