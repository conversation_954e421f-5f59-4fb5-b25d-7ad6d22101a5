import{d as b,T as I,H as l,o as t,c as s,F as f,k as g,i as e,a as v,w as c,l as _,a9 as x,_ as B,N as P,s as T,R as j,B as D,__tla as F}from"./index-BUSn51wb.js";import H,{__tla as N}from"./main-DwQbyLY9.js";import{__tla as R}from"./el-image-BjHZRFih.js";let y,q=Promise.all([(()=>{try{return F}catch{}})(),(()=>{try{return N}catch{}})(),(()=>{try{return R}catch{}})()]).then(async()=>{let d,p;d={class:"waterfall"},p={key:0,class:"waterfall-item"},y=D(b({__name:"DraftTable",props:{list:{},loading:{type:Boolean}},emits:["publish","update","delete"],setup(k,{emit:h}){const u=k,r=h;return(z,A)=>{const i=B,n=P,w=T,o=I("hasPermi"),C=j;return l((t(),s("div",d,[(t(!0),s(f,null,g(u.list,a=>(t(),s(f,{key:a.articleId},[a.content&&a.content.newsItem?(t(),s("div",p,[e(v(H),{articles:a.content.newsItem},null,8,["articles"]),e(w,null,{default:c(()=>[l((t(),_(n,{type:"success",circle:"",onClick:m=>r("publish",a)},{default:c(()=>[e(i,{icon:"fa:upload"})]),_:2},1032,["onClick"])),[[o,["mp:free-publish:submit"]]]),l((t(),_(n,{type:"primary",circle:"",onClick:m=>r("update",a)},{default:c(()=>[e(i,{icon:"ep:edit"})]),_:2},1032,["onClick"])),[[o,["mp:draft:update"]]]),l((t(),_(n,{type:"danger",circle:"",onClick:m=>r("delete",a)},{default:c(()=>[e(i,{icon:"ep:delete"})]),_:2},1032,["onClick"])),[[o,["mp:draft:delete"]]])]),_:2},1024)])):x("",!0)],64))),128))])),[[C,u.loading]])}}}),[["__scopeId","data-v-4c41ce2e"]])});export{q as __tla,y as default};
