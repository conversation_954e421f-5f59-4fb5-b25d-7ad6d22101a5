import{d as i,p as V,b as c,o as f,l as v,w as y,i as b,a as t,y as s,ci as g,Z as h,B as x,__tla as I}from"./index-BUSn51wb.js";import{P}from"./color-BN7ZL7BD.js";let d,U=Promise.all([(()=>{try{return I}catch{}})()]).then(async()=>{d=x(i({name:"ColorInput",__name:"index",props:{modelValue:V.string.def("")},emits:["update:modelValue"],setup(u,{emit:r}){const n=u,m=r,e=c({get:()=>n.modelValue,set:o=>{m("update:modelValue",o)}});return(o,a)=>{const p=g,_=h;return f(),v(_,{modelValue:t(e),"onUpdate:modelValue":a[1]||(a[1]=l=>s(e)?e.value=l:null)},{prepend:y(()=>[b(p,{modelValue:t(e),"onUpdate:modelValue":a[0]||(a[0]=l=>s(e)?e.value=l:null),predefine:t(P)},null,8,["modelValue","predefine"])]),_:1},8,["modelValue"])}}}),[["__scopeId","data-v-cfa48b67"]])});export{d as _,U as __tla};
