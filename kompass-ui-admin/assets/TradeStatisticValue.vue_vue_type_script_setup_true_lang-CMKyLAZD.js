import{_ as g,__tla as v}from"./CountTo.vue_vue_type_script_setup_true_lang-BUNK7cmG.js";import{d as b,p as a,o as f,c as y,g as t,t as m,l as w,w as h,i as s,a9 as S,a0 as T,a as l,aL as r,j,_ as N,aN as V,__tla as k}from"./index-BUSn51wb.js";let x,L=Promise.all([(()=>{try{return v}catch{}})(),(()=>{try{return k}catch{}})()]).then(async()=>{let n,c,i,p,o;n={class:"flex flex-col gap-2 bg-[var(--el-bg-color-overlay)] p-6"},c={class:"flex items-center justify-between text-gray-500"},i={class:"mb-4 text-3xl"},p={class:"flex flex-row gap-1 text-sm"},o=t("span",{class:"text-gray-500"},"\u73AF\u6BD4",-1),x=b({name:"TradeStatisticValue",__name:"TradeStatisticValue",props:{tooltip:a.string.def(""),title:a.string.def(""),prefix:a.string.def(""),value:a.number.def(0),decimals:a.number.def(0),percent:a.oneOfType([Number,String]).def(0)},setup:e=>(M,O)=>{const d=N,_=V,u=g;return f(),y("div",n,[t("div",c,[t("span",null,m(e.title),1),e.tooltip?(f(),w(_,{key:0,content:e.tooltip,placement:"top-start"},{default:h(()=>[s(d,{icon:"ep:warning"})]),_:1},8,["content"])):S("",!0)]),t("div",i,[s(u,{prefix:e.prefix,"end-val":e.value,decimals:e.decimals},null,8,["prefix","end-val","decimals"])]),t("div",p,[o,t("span",{class:T(l(r)(e.percent)>0?"text-red-500":"text-green-500")},[j(m(Math.abs(l(r)(e.percent)))+"% ",1),s(d,{icon:l(r)(e.percent)>0?"ep:caret-top":"ep:caret-bottom",class:"!text-sm"},null,8,["icon"])],2)])])}})});export{x as _,L as __tla};
