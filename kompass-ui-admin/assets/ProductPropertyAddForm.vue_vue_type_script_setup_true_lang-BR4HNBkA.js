import{d as A,r as o,f as C,at as K,o as c,l as y,w as s,i as m,j as f,a as l,H as R,U as q,b1 as H,y as I,n as N,I as O,Z as S,L as Z,O as z,N as B,R as D,__tla as E}from"./index-BUSn51wb.js";import{_ as G,__tla as J}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{b as M,__tla as Q}from"./property-BdOytbZT.js";let v,T=Promise.all([(()=>{try{return E}catch{}})(),(()=>{try{return J}catch{}})(),(()=>{try{return Q}catch{}})()]).then(async()=>{v=A({name:"ProductPropertyForm",__name:"ProductPropertyAddForm",props:{propertyList:{type:Array,default:()=>{}}},setup(b,{expose:h}){const{t:w}=N(),V=O(),t=o(!1),u=o(!1),r=o({name:""}),P=C({name:[{required:!0,message:"\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),n=o(),p=o([]),x=b;K(()=>x.propertyList,e=>{e&&(p.value=e)},{deep:!0,immediate:!0}),h({open:async()=>{t.value=!0,g()}});const _=async()=>{if(n&&await n.value.validate()){u.value=!0;try{const e=r.value,a=await M(e);p.value.push({id:a,...r.value,values:[]}),V.success(w("common.createSuccess")),t.value=!1}finally{u.value=!1}}},g=()=>{var e;r.value={name:""},(e=n.value)==null||e.resetFields()};return(e,a)=>{const k=S,F=Z,L=z,i=B,U=G,j=D;return c(),y(U,{modelValue:l(t),"onUpdate:modelValue":a[2]||(a[2]=d=>I(t)?t.value=d:null),title:"\u6DFB\u52A0\u5546\u54C1\u5C5E\u6027"},{footer:s(()=>[m(i,{disabled:l(u),type:"primary",onClick:_},{default:s(()=>[f("\u786E \u5B9A")]),_:1},8,["disabled"]),m(i,{onClick:a[1]||(a[1]=d=>t.value=!1)},{default:s(()=>[f("\u53D6 \u6D88")]),_:1})]),default:s(()=>[R((c(),y(L,{ref_key:"formRef",ref:n,model:l(r),rules:l(P),"label-width":"80px",onKeydown:q(H(_,["prevent"]),["enter"])},{default:s(()=>[m(F,{label:"\u5C5E\u6027\u540D\u79F0",prop:"name"},{default:s(()=>[m(k,{modelValue:l(r).name,"onUpdate:modelValue":a[0]||(a[0]=d=>l(r).name=d),placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules","onKeydown"])),[[j,l(u)]])]),_:1},8,["modelValue"])}}})});export{v as _,T as __tla};
