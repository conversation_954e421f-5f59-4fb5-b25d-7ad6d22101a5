import{d as j,f as b,r as u,at as q,u as U,C as z,o as B,c as R,i as a,w as n,a as i,H as E,l as F,j as D,t as v,dV as H,F as O,P as Q,v as V,Q as G,R as J,ej as K,__tla as M}from"./index-BUSn51wb.js";import{_ as X,__tla as Y}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{E as Z,__tla as $}from"./el-card-CJbXGyyg.js";import{E as aa,__tla as ea}from"./el-skeleton-item-tDN8U6BH.js";import{_ as ta,__tla as ra}from"./Echart.vue_vue_type_script_setup_true_lang-DnLZLH3w.js";import{S as C,__tla as ia}from"./funnel-B_PNiNbM.js";import{d as m,__tla as la}from"./formatTime-DWdBpgsM.js";let L,sa=Promise.all([(()=>{try{return M}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return $}catch{}})(),(()=>{try{return ea}catch{}})(),(()=>{try{return ra}catch{}})(),(()=>{try{return ia}catch{}})(),(()=>{try{return la}catch{}})()]).then(async()=>{L=j({name:"BusinessSummary",__name:"BusinessInversionRateSummary",props:{queryParams:{}},setup(S,{expose:N}){const c=S,o=b({pageNo:1,pageSize:10}),d=u(!1),x=u([]),_=u(0);q(()=>c.queryParams,l=>{if(!l)return;const t={...o,...l};Object.assign(o,t)},{immediate:!0});const e=b({color:["#6ca2ff","#6ac9d7","#ff7474"],tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{data:["\u8D62\u5355\u8F6C\u5316\u7387","\u5546\u673A\u603B\u6570","\u8D62\u5355\u5546\u673A\u6570"],bottom:"0px",itemWidth:14},grid:{top:"40px",left:"40px",right:"40px",bottom:"40px",containLabel:!0,borderColor:"#fff"},xAxis:[{type:"category",data:[],axisTick:{alignWithLabel:!0,lineStyle:{width:0}},axisLabel:{color:"#BDBDBD"},axisLine:{lineStyle:{color:"#BDBDBD"}},splitLine:{show:!1}}],yAxis:[{type:"value",name:"\u8D62\u5355\u8F6C\u5316\u7387",axisTick:{alignWithLabel:!0,lineStyle:{width:0}},axisLabel:{color:"#BDBDBD",formatter:"{value}%"},axisLine:{lineStyle:{color:"#BDBDBD"}},splitLine:{show:!1}},{type:"value",name:"\u5546\u673A\u6570",axisTick:{alignWithLabel:!0,lineStyle:{width:0}},axisLabel:{color:"#BDBDBD",formatter:"{value}\u4E2A"},axisLine:{lineStyle:{color:"#BDBDBD"}},splitLine:{show:!1}}],series:[{name:"\u8D62\u5355\u8F6C\u5316\u7387",type:"line",yAxisIndex:0,data:[]},{name:"\u5546\u673A\u603B\u6570",type:"bar",yAxisIndex:1,barWidth:15,data:[]},{name:"\u8D62\u5355\u5546\u673A\u6570",type:"bar",yAxisIndex:1,barWidth:15,data:[]}]}),f=async()=>{const l=await C.getBusinessPageByDate(c.queryParams);x.value=l.list,_.value=l.total},{push:y}=U(),h=async()=>{d.value=!0;try{await(async()=>{const l=await C.getBusinessInversionRateSummaryByDate(c.queryParams);e.xAxis&&e.xAxis[0]&&e.xAxis[0].data&&(e.xAxis[0].data=l.map(t=>t.time)),e.series&&e.series[0]&&e.series[0].data&&(e.series[0].data=l.map(t=>K(t.businessWinCount,t.businessCount))),e.series&&e.series[1]&&e.series[1].data&&(e.series[1].data=l.map(t=>t.businessCount)),e.series&&e.series[2]&&e.series[2].data&&(e.series[2].data=l.map(t=>t.businessWinCount)),await f()})()}finally{d.value=!1}};return N({loadData:h}),z(()=>{h()}),(l,t)=>{const P=ta,A=aa,g=Z,r=Q,w=V,T=G,k=X,W=J;return B(),R(O,null,[a(g,{shadow:"never"},{default:n(()=>[a(A,{loading:i(d),animated:""},{default:n(()=>[a(P,{height:500,options:i(e)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),a(g,{class:"mt-16px",shadow:"never"},{default:n(()=>[E((B(),F(T,{data:i(x)},{default:n(()=>[a(r,{align:"center",fixed:"left",label:"\u5E8F\u53F7",type:"index",width:"80"}),a(r,{align:"center",fixed:"left",label:"\u5546\u673A\u540D\u79F0",prop:"name",width:"160"},{default:n(s=>[a(w,{underline:!1,type:"primary",onClick:I=>{return p=s.row.id,void y({name:"CrmBusinessDetail",params:{id:p}});var p}},{default:n(()=>[D(v(s.row.name),1)]),_:2},1032,["onClick"])]),_:1}),a(r,{align:"center",fixed:"left",label:"\u5BA2\u6237\u540D\u79F0",prop:"customerName",width:"120"},{default:n(s=>[a(w,{underline:!1,type:"primary",onClick:I=>{return p=s.row.customerId,void y({name:"CrmCustomerDetail",params:{id:p}});var p}},{default:n(()=>[D(v(s.row.customerName),1)]),_:2},1032,["onClick"])]),_:1}),a(r,{formatter:i(H),align:"center",label:"\u5546\u673A\u91D1\u989D\uFF08\u5143\uFF09",prop:"totalPrice",width:"140"},null,8,["formatter"]),a(r,{formatter:i(m),align:"center",label:"\u9884\u8BA1\u6210\u4EA4\u65E5\u671F",prop:"dealTime",width:"180px"},null,8,["formatter"]),a(r,{align:"center",label:"\u5907\u6CE8",prop:"remark",width:"200"}),a(r,{formatter:i(m),align:"center",label:"\u4E0B\u6B21\u8054\u7CFB\u65F6\u95F4",prop:"contactNextTime",width:"180px"},null,8,["formatter"]),a(r,{align:"center",label:"\u8D1F\u8D23\u4EBA",prop:"ownerUserName",width:"100px"}),a(r,{align:"center",label:"\u6240\u5C5E\u90E8\u95E8",prop:"ownerUserDeptName",width:"100px"}),a(r,{formatter:i(m),align:"center",label:"\u6700\u540E\u8DDF\u8FDB\u65F6\u95F4",prop:"contactLastTime",width:"180px"},null,8,["formatter"]),a(r,{formatter:i(m),align:"center",label:"\u66F4\u65B0\u65F6\u95F4",prop:"updateTime",width:"180px"},null,8,["formatter"]),a(r,{formatter:i(m),align:"center",label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"]),a(r,{align:"center",label:"\u521B\u5EFA\u4EBA",prop:"creatorName",width:"100px"}),a(r,{align:"center",fixed:"right",label:"\u5546\u673A\u72B6\u6001\u7EC4",prop:"statusTypeName",width:"140"}),a(r,{align:"center",fixed:"right",label:"\u5546\u673A\u9636\u6BB5",prop:"statusName",width:"120"})]),_:1},8,["data"])),[[W,i(d)]]),a(k,{limit:i(o).pageSize,"onUpdate:limit":t[0]||(t[0]=s=>i(o).pageSize=s),page:i(o).pageNo,"onUpdate:page":t[1]||(t[1]=s=>i(o).pageNo=s),total:i(_),onPagination:f},null,8,["limit","page","total"])]),_:1})],64)}}})});export{L as _,sa as __tla};
