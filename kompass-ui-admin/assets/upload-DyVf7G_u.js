import{e9 as l,__tla as m}from"./index-BUSn51wb.js";import{u as t,U as r,__tla as p}from"./useUpload-gjof4KYU.js";let e,s,o,_,i,n=Promise.all([(()=>{try{return m}catch{}})(),(()=>{try{return p}catch{}})()]).then(async()=>{e={Authorization:"Bearer "+l()},s="http://118.195.130.96:48080/admin-api/mp/material/upload-permanent",_=a=>t(r.Image,2)(a),o=a=>t(r.Voice,2)(a),i=a=>t(r.Video,10)(a)});export{e as H,s as U,n as __tla,o as a,_ as b,i as c};
