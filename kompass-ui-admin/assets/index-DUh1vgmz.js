import{_ as B,__tla as L}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{d as R,u as T,S as z,r as m,f as G,C as I,o as p,c as b,i as e,w as l,a as t,al as P,j as n,F as k,k as Z,l as D,t as H,V as J,G as K,y as Q,n as W,I as X,_ as Y,N as $,Z as aa,L as ea,am as ta,an as la,O as ra,__tla as sa}from"./index-BUSn51wb.js";import{_ as ua,__tla as oa}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{C as _a}from"./constants-A8BI3pz7.js";import{g as ma,c as na,u as da,__tla as ca}from"./index-COJ8hy-t.js";import{s as ia,e as fa,a as pa,__tla as ya}from"./formCreate-DDLxm5B5.js";import{u as ha,__tla as Va}from"./tagsView-BOOrxb3Q.js";import{u as ba,__tla as ka}from"./useFormCreateDesigner-C9MJ3oSM.js";import{__tla as va}from"./el-card-CJbXGyyg.js";import{__tla as ga}from"./dict.type-7eDXjvul.js";let v,wa=Promise.all([(()=>{try{return L}catch{}})(),(()=>{try{return sa}catch{}})(),(()=>{try{return oa}catch{}})(),(()=>{try{return ca}catch{}})(),(()=>{try{return ya}catch{}})(),(()=>{try{return Va}catch{}})(),(()=>{try{return ka}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return ga}catch{}})()]).then(async()=>{v=R({name:"BpmFormEditor",__name:"index",setup(xa){const{t:y}=W(),h=X(),{push:g,currentRoute:w}=T(),{query:x}=z(),{delView:C}=ha(),_=m();ba(_);const o=m(!1),d=m(!1),s=m({name:"",status:_a.ENABLE,remark:""}),S=G({name:[{required:!0,message:"\u8868\u5355\u540D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u5F00\u542F\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),c=m(),U=()=>{o.value=!0},N=async()=>{if(c&&await c.value.validate()){d.value=!0;try{const u=s.value;u.conf=fa(_),u.fields=pa(_),u.id?(await da(u),h.success(y("common.updateSuccess"))):(await na(u),h.success(y("common.createSuccess"))),o.value=!1,O()}finally{d.value=!1}}},O=()=>{C(t(w)),g("/bpm/manager/form")};return I(async()=>{const u=x.id;if(!u)return;const a=await ma(u);s.value=a,ia(_,a.conf,a.fields)}),(u,a)=>{const q=Y,i=$,E=ua,V=aa,f=ea,F=ta,M=la,j=ra,A=B;return p(),b(k,null,[e(E,null,{default:l(()=>[e(t(P),{ref_key:"designer",ref:_,height:"780px"},{handle:l(()=>[e(i,{round:"",size:"small",type:"primary",onClick:U},{default:l(()=>[e(q,{class:"mr-5px",icon:"ep:plus"}),n(" \u4FDD\u5B58 ")]),_:1})]),_:1},512)]),_:1}),e(A,{modelValue:t(o),"onUpdate:modelValue":a[4]||(a[4]=r=>Q(o)?o.value=r:null),title:"\u4FDD\u5B58\u8868\u5355",width:"600"},{footer:l(()=>[e(i,{disabled:t(d),type:"primary",onClick:N},{default:l(()=>[n("\u786E \u5B9A")]),_:1},8,["disabled"]),e(i,{onClick:a[3]||(a[3]=r=>o.value=!1)},{default:l(()=>[n("\u53D6 \u6D88")]),_:1})]),default:l(()=>[e(j,{ref_key:"formRef",ref:c,model:t(s),rules:t(S),"label-width":"80px"},{default:l(()=>[e(f,{label:"\u8868\u5355\u540D",prop:"name"},{default:l(()=>[e(V,{modelValue:t(s).name,"onUpdate:modelValue":a[0]||(a[0]=r=>t(s).name=r),placeholder:"\u8BF7\u8F93\u5165\u8868\u5355\u540D"},null,8,["modelValue"])]),_:1}),e(f,{label:"\u72B6\u6001",prop:"status"},{default:l(()=>[e(M,{modelValue:t(s).status,"onUpdate:modelValue":a[1]||(a[1]=r=>t(s).status=r)},{default:l(()=>[(p(!0),b(k,null,Z(t(J)(t(K).COMMON_STATUS),r=>(p(),D(F,{key:r.value,label:r.value},{default:l(()=>[n(H(r.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(f,{label:"\u5907\u6CE8",prop:"remark"},{default:l(()=>[e(V,{modelValue:t(s).remark,"onUpdate:modelValue":a[2]||(a[2]=r=>t(s).remark=r),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])],64)}}})});export{wa as __tla,v as default};
