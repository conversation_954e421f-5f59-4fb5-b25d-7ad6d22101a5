import{d as $,n as ee,I as ae,r as i,f as le,b as te,at as re,dW as de,o as n,l as _,w as r,a,j as F,a9 as ue,i as e,H as oe,c as q,F as A,k as L,y as R,dX as z,Z as se,L as ie,E as ce,M as ne,J as me,K as pe,cn as _e,s as fe,z as ve,A as Ve,cc as be,O as Pe,N as he,R as ye,__tla as we}from"./index-BUSn51wb.js";import{_ as Ue,__tla as ge}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{_ as ke,__tla as Ie}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{P as y,__tla as xe}from"./index-BRyXhR7Z.js";import{_ as Se,__tla as Oe}from"./PurchaseOrderItemForm.vue_vue_type_script_setup_true_lang-D1Veqrb-.js";import{S as Te,__tla as Fe}from"./index-CncHngEK.js";import{g as qe,__tla as Ae}from"./index-BYXzDB8j.js";import{A as Le,__tla as Re}from"./index-LbO7ASKC.js";let C,ze=Promise.all([(()=>{try{return we}catch{}})(),(()=>{try{return ge}catch{}})(),(()=>{try{return Ie}catch{}})(),(()=>{try{return xe}catch{}})(),(()=>{try{return Oe}catch{}})(),(()=>{try{return Fe}catch{}})(),(()=>{try{return Ae}catch{}})(),(()=>{try{return Re}catch{}})()]).then(async()=>{C=$({name:"PurchaseOrderForm",__name:"PurchaseOrderForm",emits:["success"],setup(Ce,{expose:E,emit:H}){const{t:f}=ee(),w=ae(),m=i(!1),U=i(""),p=i(!1),v=i(""),d=i({id:void 0,supplierId:void 0,accountId:void 0,orderTime:void 0,remark:void 0,fileUrl:"",discountPercent:0,discountPrice:0,totalPrice:0,depositPrice:0,items:[],no:void 0}),K=le({supplierId:[{required:!0,message:"\u4F9B\u5E94\u5546\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],orderTime:[{required:!0,message:"\u8BA2\u5355\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),V=te(()=>v.value==="detail"),b=i(),g=i([]),P=i([]),M=i([]),h=i("item"),k=i();re(()=>d.value,u=>{if(!u)return;const l=u.items.reduce((o,s)=>o+s.totalPrice,0),c=u.discountPercent!=null?de(l,u.discountPercent/100):0;d.value.discountPrice=c,d.value.totalPrice=l-c},{deep:!0}),E({open:async(u,l)=>{if(m.value=!0,U.value=f("action."+u),v.value=u,D(),l){p.value=!0;try{d.value=await y.getPurchaseOrder(l)}finally{p.value=!1}}g.value=await Te.getSupplierSimpleList(),M.value=await qe(),P.value=await Le.getAccountSimpleList();const c=P.value.find(o=>o.defaultStatus);c&&(d.value.accountId=c.id)}});const j=H,B=async()=>{await b.value.validate(),await k.value.validate(),p.value=!0;try{const u=d.value;v.value==="create"?(await y.createPurchaseOrder(u),w.success(f("common.createSuccess"))):(await y.updatePurchaseOrder(u),w.success(f("common.updateSuccess"))),m.value=!1,j("success")}finally{p.value=!1}},D=()=>{var u;d.value={id:void 0,supplierId:void 0,accountId:void 0,orderTime:void 0,remark:void 0,fileUrl:void 0,discountPercent:0,discountPrice:0,totalPrice:0,depositPrice:0,items:[]},(u=b.value)==null||u.resetFields()};return(u,l)=>{const c=se,o=ie,s=ce,J=ne,I=me,x=pe,N=_e,S=fe,W=ve,X=Ve,Z=ke,O=be,G=Pe,T=he,Q=Ue,Y=ye;return n(),_(Q,{title:a(U),modelValue:a(m),"onUpdate:modelValue":l[12]||(l[12]=t=>R(m)?m.value=t:null),width:"1080"},{footer:r(()=>[a(V)?ue("",!0):(n(),_(T,{key:0,onClick:B,type:"primary",disabled:a(p)},{default:r(()=>[F(" \u786E \u5B9A ")]),_:1},8,["disabled"])),e(T,{onClick:l[11]||(l[11]=t=>m.value=!1)},{default:r(()=>[F("\u53D6 \u6D88")]),_:1})]),default:r(()=>[oe((n(),_(G,{ref_key:"formRef",ref:b,model:a(d),rules:a(K),"label-width":"100px",disabled:a(V)},{default:r(()=>[e(S,{gutter:20},{default:r(()=>[e(s,{span:8},{default:r(()=>[e(o,{label:"\u8BA2\u5355\u5355\u53F7",prop:"no"},{default:r(()=>[e(c,{disabled:"",modelValue:a(d).no,"onUpdate:modelValue":l[0]||(l[0]=t=>a(d).no=t),placeholder:"\u4FDD\u5B58\u65F6\u81EA\u52A8\u751F\u6210"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:r(()=>[e(o,{label:"\u8BA2\u5355\u65F6\u95F4",prop:"orderTime"},{default:r(()=>[e(J,{modelValue:a(d).orderTime,"onUpdate:modelValue":l[1]||(l[1]=t=>a(d).orderTime=t),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u8BA2\u5355\u65F6\u95F4",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:r(()=>[e(o,{label:"\u4F9B\u5E94\u5546",prop:"supplierId"},{default:r(()=>[e(x,{modelValue:a(d).supplierId,"onUpdate:modelValue":l[2]||(l[2]=t=>a(d).supplierId=t),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u4F9B\u5E94\u5546",class:"!w-1/1"},{default:r(()=>[(n(!0),q(A,null,L(a(g),t=>(n(),_(I,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{span:16},{default:r(()=>[e(o,{label:"\u5907\u6CE8",prop:"remark"},{default:r(()=>[e(c,{type:"textarea",modelValue:a(d).remark,"onUpdate:modelValue":l[3]||(l[3]=t=>a(d).remark=t),rows:1,placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:r(()=>[e(o,{label:"\u9644\u4EF6",prop:"fileUrl"},{default:r(()=>[e(N,{"is-show-tip":!1,modelValue:a(d).fileUrl,"onUpdate:modelValue":l[4]||(l[4]=t=>a(d).fileUrl=t),limit:1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(Z,null,{default:r(()=>[e(X,{modelValue:a(h),"onUpdate:modelValue":l[5]||(l[5]=t=>R(h)?h.value=t:null),class:"-mt-15px -mb-10px"},{default:r(()=>[e(W,{label:"\u8BA2\u5355\u4EA7\u54C1\u6E05\u5355",name:"item"},{default:r(()=>[e(Se,{ref_key:"itemFormRef",ref:k,items:a(d).items,disabled:a(V)},null,8,["items","disabled"])]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(S,{gutter:20},{default:r(()=>[e(s,{span:8},{default:r(()=>[e(o,{label:"\u4F18\u60E0\u7387\uFF08%\uFF09",prop:"discountPercent"},{default:r(()=>[e(O,{modelValue:a(d).discountPercent,"onUpdate:modelValue":l[6]||(l[6]=t=>a(d).discountPercent=t),"controls-position":"right",min:0,precision:2,placeholder:"\u8BF7\u8F93\u5165\u4F18\u60E0\u7387",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:r(()=>[e(o,{label:"\u4ED8\u6B3E\u4F18\u60E0",prop:"discountPrice"},{default:r(()=>[e(c,{disabled:"",modelValue:a(d).discountPrice,"onUpdate:modelValue":l[7]||(l[7]=t=>a(d).discountPrice=t),formatter:a(z)},null,8,["modelValue","formatter"])]),_:1})]),_:1}),e(s,{span:8},{default:r(()=>[e(o,{label:"\u4F18\u60E0\u540E\u91D1\u989D"},{default:r(()=>[e(c,{disabled:"",modelValue:a(d).totalPrice,"onUpdate:modelValue":l[8]||(l[8]=t=>a(d).totalPrice=t),formatter:a(z)},null,8,["modelValue","formatter"])]),_:1})]),_:1}),e(s,{span:8},{default:r(()=>[e(o,{label:"\u7ED3\u7B97\u8D26\u6237",prop:"accountId"},{default:r(()=>[e(x,{modelValue:a(d).accountId,"onUpdate:modelValue":l[9]||(l[9]=t=>a(d).accountId=t),clearable:"",filterable:"",placeholder:"\u8BF7\u9009\u62E9\u7ED3\u7B97\u8D26\u6237",class:"!w-1/1"},{default:r(()=>[(n(!0),q(A,null,L(a(P),t=>(n(),_(I,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(s,{span:8},{default:r(()=>[e(o,{label:"\u652F\u4ED8\u8BA2\u91D1",prop:"depositPrice"},{default:r(()=>[e(O,{modelValue:a(d).depositPrice,"onUpdate:modelValue":l[10]||(l[10]=t=>a(d).depositPrice=t),"controls-position":"right",min:0,precision:2,placeholder:"\u8BF7\u8F93\u5165\u652F\u4ED8\u8BA2\u91D1",class:"!w-1/1"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules","disabled"])),[[Y,a(p)]])]),_:1},8,["title","modelValue"])}}})});export{C as _,ze as __tla};
