import{d as H,n as L,I as N,r as p,f as D,b as O,o as V,l as g,w as n,i as l,a as t,j as m,H as S,y as b,Z,L as z,cc as A,am as B,an as E,O as G,N as J,R as K,__tla as M}from"./index-BUSn51wb.js";import{_ as Q,__tla as W}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{g as X,b as Y,__tla as $}from"./index-CBYHFFsC.js";let P,ee=Promise.all([(()=>{try{return M}catch{}})(),(()=>{try{return W}catch{}})(),(()=>{try{return $}catch{}})()]).then(async()=>{P=H({name:"UpdatePointForm",__name:"UserPointUpdateForm",emits:["success"],setup(ae,{expose:w,emit:U}){const{t:x}=L(),_=N(),u=p(!1),d=p(!1),e=p({id:void 0,nickname:void 0,point:0,changePoint:0,changeType:1}),k=D({changePoint:[{required:!0,message:"\u53D8\u52A8\u79EF\u5206\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),r=p();w({open:async s=>{if(u.value=!0,C(),s){d.value=!0;try{e.value=await X(s),e.value.changeType=1,e.value.changePoint=0}finally{d.value=!1}}}});const T=U,F=async()=>{if(r&&await r.value.validate())if(e.value.changePoint<1)_.error("\u53D8\u52A8\u79EF\u5206\u4E0D\u80FD\u5C0F\u4E8E 1");else if(c.value<0)_.error("\u53D8\u52A8\u540E\u7684\u79EF\u5206\u4E0D\u80FD\u5C0F\u4E8E 0");else{d.value=!0;try{await Y({id:e.value.id,point:e.value.changePoint*e.value.changeType}),_.success(x("common.updateSuccess")),u.value=!1,T("success")}finally{d.value=!1}}},C=()=>{var s;e.value={id:void 0,nickname:void 0,levelId:void 0,reason:void 0},(s=r.value)==null||s.resetFields()},c=O(()=>e.value.point+e.value.changePoint*e.value.changeType);return(s,a)=>{const f=Z,i=z,v=A,h=B,I=E,R=G,y=J,j=Q,q=K;return V(),g(j,{title:"\u4FEE\u6539\u7528\u6237\u79EF\u5206",modelValue:t(u),"onUpdate:modelValue":a[7]||(a[7]=o=>b(u)?u.value=o:null),width:"600"},{footer:n(()=>[l(y,{onClick:F,type:"primary",disabled:t(d)},{default:n(()=>[m("\u786E \u5B9A")]),_:1},8,["disabled"]),l(y,{onClick:a[6]||(a[6]=o=>u.value=!1)},{default:n(()=>[m("\u53D6 \u6D88")]),_:1})]),default:n(()=>[S((V(),g(R,{ref_key:"formRef",ref:r,model:t(e),rules:t(k),"label-width":"100px"},{default:n(()=>[l(i,{label:"\u7528\u6237\u7F16\u53F7",prop:"id"},{default:n(()=>[l(f,{modelValue:t(e).id,"onUpdate:modelValue":a[0]||(a[0]=o=>t(e).id=o),class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),l(i,{label:"\u7528\u6237\u6635\u79F0",prop:"nickname"},{default:n(()=>[l(f,{modelValue:t(e).nickname,"onUpdate:modelValue":a[1]||(a[1]=o=>t(e).nickname=o),class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),l(i,{label:"\u53D8\u52A8\u524D\u79EF\u5206",prop:"point"},{default:n(()=>[l(v,{modelValue:t(e).point,"onUpdate:modelValue":a[2]||(a[2]=o=>t(e).point=o),class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1}),l(i,{label:"\u53D8\u52A8\u7C7B\u578B",prop:"changeType"},{default:n(()=>[l(I,{modelValue:t(e).changeType,"onUpdate:modelValue":a[3]||(a[3]=o=>t(e).changeType=o)},{default:n(()=>[l(h,{label:1},{default:n(()=>[m("\u589E\u52A0")]),_:1}),l(h,{label:-1},{default:n(()=>[m("\u51CF\u5C11")]),_:1})]),_:1},8,["modelValue"])]),_:1}),l(i,{label:"\u53D8\u52A8\u79EF\u5206",prop:"changePoint"},{default:n(()=>[l(v,{modelValue:t(e).changePoint,"onUpdate:modelValue":a[4]||(a[4]=o=>t(e).changePoint=o),class:"!w-240px",min:0,precision:0},null,8,["modelValue"])]),_:1}),l(i,{label:"\u53D8\u52A8\u540E\u79EF\u5206"},{default:n(()=>[l(v,{modelValue:t(c),"onUpdate:modelValue":a[5]||(a[5]=o=>b(c)?c.value=o:null),class:"!w-240px",disabled:""},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[q,t(d)]])]),_:1},8,["modelValue"])}}})});export{P as _,ee as __tla};
