import{_ as t,__tla as _}from"./CustomerFollowUpSummary.vue_vue_type_script_setup_true_lang-B0vPl9Y4.js";import{__tla as r}from"./index-BUSn51wb.js";import{__tla as a}from"./el-card-CJbXGyyg.js";import{__tla as l}from"./el-skeleton-item-tDN8U6BH.js";import{__tla as o}from"./customer-DXRFD9ec.js";import{__tla as c}from"./Echart.vue_vue_type_script_setup_true_lang-DnLZLH3w.js";let m=Promise.all([(()=>{try{return _}catch{}})(),(()=>{try{return r}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return c}catch{}})()]).then(async()=>{});export{m as __tla,t as default};
