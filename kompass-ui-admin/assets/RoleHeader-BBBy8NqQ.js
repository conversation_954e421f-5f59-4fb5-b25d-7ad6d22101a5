import{d as i,o,l as _,w as c,g as e,t as n,aV as p,b9 as u,B as h,__tla as f}from"./index-BUSn51wb.js";let s,m=Promise.all([(()=>{try{return f}catch{}})()]).then(async()=>{let t,a;t={class:"title"},a={class:"title-right"},s=h(i({__name:"RoleHeader",props:{title:{type:String,required:!0}},setup:l=>(r,v)=>{const d=u;return o(),_(d,{class:"chat-header"},{default:c(()=>[e("div",t,n(l.title),1),e("div",a,[p(r.$slots,"default",{},void 0,!0)])]),_:3})}}),[["__scopeId","data-v-c9584b09"]])});export{m as __tla,s as default};
