import{u as M,_ as w,a as A,__tla as F}from"./useTable-Cn_FDA9m.js";import{_ as T,__tla as j}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{d as U,r as P,C as H,T as N,o as s,c as O,i as e,w as _,a as t,H as i,l as m,j as p,F as q,_ as B,N as D,__tla as E}from"./index-BUSn51wb.js";import{_ as G,__tla as I}from"./index-COobLwz-.js";import{a as v,_ as J,__tla as K}from"./MailTemplateForm.vue_vue_type_script_setup_true_lang-Chm1nFpw.js";import{a as Q,d as V,__tla as W}from"./index-CcfUqabz.js";import{_ as X,__tla as Y}from"./MailTemplateSendForm.vue_vue_type_script_setup_true_lang-B4R2xZ5q.js";import{__tla as Z}from"./Form-DJa9ov9B.js";import{__tla as $}from"./el-virtual-list-4L-8WDNg.js";import{__tla as tt}from"./el-tree-select-CBuha0HW.js";import{__tla as at}from"./el-time-select-C-_NEIfl.js";import{__tla as rt}from"./InputPassword-RefetKoR.js";import{__tla as et}from"./index-Cch5e1V0.js";import{__tla as _t}from"./useForm-C3fyhjNV.js";import"./download-e0EdwhTv.js";import{__tla as lt}from"./el-card-CJbXGyyg.js";import{__tla as ot}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as st}from"./formatTime-DWdBpgsM.js";import{__tla as ct}from"./index-Dtskw_Cu.js";import{__tla as nt}from"./formRules-CA9eXdcX.js";import{__tla as it}from"./useCrudSchemas-hBakuBRx.js";import"./tree-BMa075Oj.js";import{__tla as mt}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import"./color-BN7ZL7BD.js";let b,pt=Promise.all([(()=>{try{return F}catch{}})(),(()=>{try{return j}catch{}})(),(()=>{try{return E}catch{}})(),(()=>{try{return I}catch{}})(),(()=>{try{return K}catch{}})(),(()=>{try{return W}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return Z}catch{}})(),(()=>{try{return $}catch{}})(),(()=>{try{return tt}catch{}})(),(()=>{try{return at}catch{}})(),(()=>{try{return rt}catch{}})(),(()=>{try{return et}catch{}})(),(()=>{try{return _t}catch{}})(),(()=>{try{return lt}catch{}})(),(()=>{try{return ot}catch{}})(),(()=>{try{return st}catch{}})(),(()=>{try{return ct}catch{}})(),(()=>{try{return nt}catch{}})(),(()=>{try{return it}catch{}})(),(()=>{try{return mt}catch{}})()]).then(async()=>{b=U({name:"SystemMailTemplate",__name:"index",setup(ut){const{tableObject:l,tableMethods:u}=M({getListApi:Q,delListApi:V}),{getList:y,setSearchParams:f}=u,d=P(),h=(k,a)=>{d.value.open(k,a)},g=P();return H(()=>{y()}),(k,a)=>{const z=G,L=B,c=D,x=w,S=T,R=A,n=N("hasPermi");return s(),O(q,null,[e(z,{title:"\u90AE\u4EF6\u914D\u7F6E",url:"https://doc.iocoder.cn/mail"}),e(S,null,{default:_(()=>[e(x,{schema:t(v).searchSchema,onSearch:t(f),onReset:t(f)},{actionMore:_(()=>[i((s(),m(c,{type:"primary",plain:"",onClick:a[0]||(a[0]=r=>h("create"))},{default:_(()=>[e(L,{icon:"ep:plus",class:"mr-5px"}),p(" \u65B0\u589E ")]),_:1})),[[n,["system:mail-template:create"]]])]),_:1},8,["schema","onSearch","onReset"])]),_:1}),e(S,null,{default:_(()=>[e(R,{columns:t(v).tableColumns,data:t(l).tableList,loading:t(l).loading,pagination:{total:t(l).total},pageSize:t(l).pageSize,"onUpdate:pageSize":a[1]||(a[1]=r=>t(l).pageSize=r),currentPage:t(l).currentPage,"onUpdate:currentPage":a[2]||(a[2]=r=>t(l).currentPage=r)},{action:_(({row:r})=>[i((s(),m(c,{link:"",type:"primary",onClick:C=>{return o=r.id,void g.value.open(o);var o}},{default:_(()=>[p(" \u6D4B\u8BD5 ")]),_:2},1032,["onClick"])),[[n,["system:mail-template:send-mail"]]]),i((s(),m(c,{link:"",type:"primary",onClick:C=>h("update",r.id)},{default:_(()=>[p(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[n,["system:mail-template:update"]]]),i((s(),m(c,{link:"",type:"danger",onClick:C=>{return o=r.id,void u.delList(o,!1);var o}},{default:_(()=>[p(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[n,["system:mail-template:delete"]]])]),_:1},8,["columns","data","loading","pagination","pageSize","currentPage"])]),_:1}),e(J,{ref_key:"formRef",ref:d,onSuccess:t(y)},null,8,["onSuccess"]),e(X,{ref_key:"sendFormRef",ref:g},null,512)],64)}}})});export{pt as __tla,b as default};
