import{d as W,r as h,f as g,C as T,o as C,c as A,i as e,w as r,a as s,H as j,l as q,G as m,F as z,aG as b,aA as G,el as w,ej as v,E as I,s as D,P as F,Q as H,R as Q,__tla as k}from"./index-BUSn51wb.js";import{_ as B,__tla as J}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{E as K,__tla as N}from"./el-card-CJbXGyyg.js";import{E as V,__tla as X}from"./el-skeleton-item-tDN8U6BH.js";import{_ as Y,__tla as Z}from"./Echart.vue_vue_type_script_setup_true_lang-DnLZLH3w.js";import{S as $,__tla as ee}from"./portrait-BcNwms8P.js";let y,ae=Promise.all([(()=>{try{return k}catch{}})(),(()=>{try{return J}catch{}})(),(()=>{try{return N}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return Z}catch{}})(),(()=>{try{return ee}catch{}})()]).then(async()=>{y=W({name:"PortraitCustomerSource",__name:"PortraitCustomerSource",props:{queryParams:{}},setup(S,{expose:R}){const P=S,i=h(!1),c=h([]),n=g({title:{text:"\u5168\u90E8\u5BA2\u6237",left:"center"},tooltip:{trigger:"item"},legend:{orient:"vertical",left:"left"},toolbox:{feature:{saveAsImage:{show:!0,name:"\u5168\u90E8\u5BA2\u6237"}}},series:[{name:"\u5168\u90E8\u5BA2\u6237",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:40,fontWeight:"bold"}},labelLine:{show:!1},data:[]}]}),u=g({title:{text:"\u6210\u4EA4\u5BA2\u6237",left:"center"},tooltip:{trigger:"item"},legend:{orient:"vertical",left:"left"},toolbox:{feature:{saveAsImage:{show:!0,name:"\u6210\u4EA4\u5BA2\u6237"}}},series:[{name:"\u6210\u4EA4\u5BA2\u6237",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:40,fontWeight:"bold"}},labelLine:{show:!1},data:[]}]}),p=async()=>{i.value=!0;const o=await $.getCustomerSource(P.queryParams);n.series&&n.series[0]&&n.series[0].data&&(n.series[0].data=o.map(t=>({name:b(m.CRM_CUSTOMER_SOURCE,t.source),value:t.customerCount}))),u.series&&u.series[0]&&u.series[0].data&&(u.series[0].data=o.map(t=>({name:b(m.CRM_CUSTOMER_SOURCE,t.source),value:t.dealCount}))),E(o),c.value=o,i.value=!1};R({loadData:p});const E=o=>{if(G(o))return;const t=o,d=w(t.map(a=>a.customerCount)),_=w(t.map(a=>a.dealCount));t.forEach(a=>{a.sourcePortion=a.customerCount===0?0:v(a.customerCount,d),a.dealPortion=a.dealCount===0?0:v(a.dealCount,_)})};return T(()=>{p()}),(o,t)=>{const d=Y,_=V,a=I,x=D,f=K,l=F,O=B,M=H,U=Q;return C(),A(z,null,[e(f,{shadow:"never"},{default:r(()=>[e(x,{gutter:20},{default:r(()=>[e(a,{span:12},{default:r(()=>[e(_,{loading:s(i),animated:""},{default:r(()=>[e(d,{height:500,options:s(n)},null,8,["options"])]),_:1},8,["loading"])]),_:1}),e(a,{span:12},{default:r(()=>[e(_,{loading:s(i),animated:""},{default:r(()=>[e(d,{height:500,options:s(u)},null,8,["options"])]),_:1},8,["loading"])]),_:1})]),_:1})]),_:1}),e(f,{class:"mt-16px",shadow:"never"},{default:r(()=>[j((C(),q(M,{data:s(c)},{default:r(()=>[e(l,{align:"center",label:"\u5E8F\u53F7",type:"index",width:"80"}),e(l,{align:"center",label:"\u5BA2\u6237\u6765\u6E90",prop:"source",width:"100"},{default:r(L=>[e(O,{type:s(m).CRM_CUSTOMER_SOURCE,value:L.row.source},null,8,["type","value"])]),_:1}),e(l,{align:"center",label:"\u5BA2\u6237\u4E2A\u6570","min-width":"200",prop:"customerCount"}),e(l,{align:"center",label:"\u6210\u4EA4\u4E2A\u6570","min-width":"200",prop:"dealCount"}),e(l,{align:"center",label:"\u6765\u6E90\u5360\u6BD4(%)","min-width":"200",prop:"sourcePortion"}),e(l,{align:"center",label:"\u6210\u4EA4\u5360\u6BD4(%)","min-width":"200",prop:"dealPortion"})]),_:1},8,["data"])),[[U,s(i)]])]),_:1})],64)}}})});export{y as _,ae as __tla};
