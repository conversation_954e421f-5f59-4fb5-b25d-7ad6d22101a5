import{d as E,I as W,n as X,r as _,f as Y,u as $,bc as aa,C as ea,T as ta,o as s,c as D,i as a,w as l,a as t,U as la,F as O,k as ra,V as oa,G as x,l as m,j as c,H as d,t as na,dV as sa,Z as ia,L as ca,J as pa,K as ua,_ as _a,N as ma,O as da,v as fa,P as ya,Q as ha,R as wa,__tla as ga}from"./index-BUSn51wb.js";import{_ as ba,__tla as va}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as Ca,__tla as xa}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{_ as ka,__tla as Ua}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as Ta,__tla as Ra}from"./index-COobLwz-.js";import{d as z,__tla as Sa}from"./formatTime-DWdBpgsM.js";import{d as Na}from"./download-e0EdwhTv.js";import{b as Pa,d as Va,e as Da,__tla as Oa}from"./index-CaE_tgzr.js";import{_ as za,__tla as Fa}from"./ProductForm.vue_vue_type_script_setup_true_lang-G9Bcq_BM.js";import{__tla as Ia}from"./index-Cch5e1V0.js";import"./color-BN7ZL7BD.js";import{__tla as Ka}from"./el-card-CJbXGyyg.js";import{__tla as Ma}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as Aa}from"./index-V4315SLT.js";import"./tree-BMa075Oj.js";import{__tla as Ha}from"./index-BYXzDB8j.js";let F,ja=Promise.all([(()=>{try{return ga}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return xa}catch{}})(),(()=>{try{return Ua}catch{}})(),(()=>{try{return Ra}catch{}})(),(()=>{try{return Sa}catch{}})(),(()=>{try{return Oa}catch{}})(),(()=>{try{return Fa}catch{}})(),(()=>{try{return Ia}catch{}})(),(()=>{try{return Ka}catch{}})(),(()=>{try{return Ma}catch{}})(),(()=>{try{return Aa}catch{}})(),(()=>{try{return Ha}catch{}})()]).then(async()=>{F=E({name:"CrmProduct",__name:"index",setup(qa){const w=W(),{t:I}=X(),g=_(!0),k=_(0),U=_([]),r=Y({pageNo:1,pageSize:10,name:void 0,status:void 0}),T=_(),b=_(!1),p=async()=>{g.value=!0;try{const i=await Pa(r);U.value=i.list,k.value=i.total}finally{g.value=!1}},v=()=>{r.pageNo=1,p()},K=()=>{T.value.resetFields(),v()},R=_(),S=(i,o)=>{R.value.open(i,o)},{currentRoute:Ba,push:M}=$(),A=async()=>{try{await w.exportConfirm(),b.value=!0;const i=await Da(r);Na.excel(i,"\u4EA7\u54C1.xls")}catch{}finally{b.value=!1}};return aa(()=>{p()}),ea(()=>{p()}),(i,o)=>{const H=Ta,j=ia,C=ca,q=pa,B=ua,f=_a,u=ma,G=da,N=ka,J=fa,n=ya,P=Ca,L=ha,Q=ba,y=ta("hasPermi"),Z=wa;return s(),D(O,null,[a(H,{title:"\u3010\u4EA7\u54C1\u3011\u4EA7\u54C1\u7BA1\u7406\u3001\u4EA7\u54C1\u5206\u7C7B",url:"https://doc.iocoder.cn/crm/product/"}),a(N,null,{default:l(()=>[a(G,{class:"-mb-15px",model:t(r),ref_key:"queryFormRef",ref:T,inline:!0,"label-width":"68px"},{default:l(()=>[a(C,{label:"\u4EA7\u54C1\u540D\u79F0",prop:"name"},{default:l(()=>[a(j,{modelValue:t(r).name,"onUpdate:modelValue":o[0]||(o[0]=e=>t(r).name=e),placeholder:"\u8BF7\u8F93\u5165\u4EA7\u54C1\u540D\u79F0",clearable:"",onKeyup:la(v,["enter"]),class:"!w-240px"},null,8,["modelValue"])]),_:1}),a(C,{label:"\u72B6\u6001",prop:"status"},{default:l(()=>[a(B,{modelValue:t(r).status,"onUpdate:modelValue":o[1]||(o[1]=e=>t(r).status=e),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",clearable:"",class:"!w-240px"},{default:l(()=>[(s(!0),D(O,null,ra(t(oa)(t(x).CRM_PRODUCT_STATUS),e=>(s(),m(q,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(C,null,{default:l(()=>[a(u,{onClick:v},{default:l(()=>[a(f,{icon:"ep:search",class:"mr-5px"}),c(" \u641C\u7D22 ")]),_:1}),a(u,{onClick:K},{default:l(()=>[a(f,{icon:"ep:refresh",class:"mr-5px"}),c(" \u91CD\u7F6E ")]),_:1}),d((s(),m(u,{type:"primary",onClick:o[2]||(o[2]=e=>S("create"))},{default:l(()=>[a(f,{icon:"ep:plus",class:"mr-5px"}),c(" \u65B0\u589E ")]),_:1})),[[y,["crm:product:create"]]]),d((s(),m(u,{type:"success",plain:"",onClick:A,loading:t(b)},{default:l(()=>[a(f,{icon:"ep:download",class:"mr-5px"}),c(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[y,["crm:product:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(N,null,{default:l(()=>[d((s(),m(L,{data:t(U),stripe:!0,"show-overflow-tooltip":!0},{default:l(()=>[a(n,{label:"\u4EA7\u54C1\u540D\u79F0",align:"center",prop:"name",width:"160"},{default:l(e=>[a(J,{underline:!1,type:"primary",onClick:V=>{return h=e.row.id,void M({name:"CustomerDetail",params:{id:h}});var h}},{default:l(()=>[c(na(e.row.name),1)]),_:2},1032,["onClick"])]),_:1}),a(n,{label:"\u4EA7\u54C1\u7C7B\u578B",align:"center",prop:"categoryName",width:"160"}),a(n,{label:"\u4EA7\u54C1\u5355\u4F4D",align:"center",prop:"unit"},{default:l(e=>[a(P,{type:t(x).CRM_PRODUCT_UNIT,value:e.row.unit},null,8,["type","value"])]),_:1}),a(n,{label:"\u4EA7\u54C1\u7F16\u7801",align:"center",prop:"no"}),a(n,{label:"\u4EF7\u683C\uFF08\u5143\uFF09",align:"center",prop:"price",formatter:t(sa),width:"100"},null,8,["formatter"]),a(n,{label:"\u4EA7\u54C1\u63CF\u8FF0",align:"center",prop:"description",width:"150"}),a(n,{label:"\u4E0A\u67B6\u72B6\u6001",align:"center",prop:"status",width:"120"},{default:l(e=>[a(P,{type:t(x).CRM_PRODUCT_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),a(n,{label:"\u8D1F\u8D23\u4EBA",align:"center",prop:"ownerUserName",width:"120"}),a(n,{label:"\u66F4\u65B0\u65F6\u95F4",align:"center",prop:"updateTime",formatter:t(z),width:"180px"},null,8,["formatter"]),a(n,{label:"\u521B\u5EFA\u4EBA",align:"center",prop:"creatorName",width:"120"}),a(n,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:t(z),width:"180px"},null,8,["formatter"]),a(n,{label:"\u64CD\u4F5C",align:"center",fixed:"right",width:"160"},{default:l(e=>[d((s(),m(u,{link:"",type:"primary",onClick:V=>S("update",e.row.id)},{default:l(()=>[c(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[y,["crm:product:update"]]]),d((s(),m(u,{link:"",type:"danger",onClick:V=>(async h=>{try{await w.delConfirm(),await Va(h),w.success(I("common.delSuccess")),await p()}catch{}})(e.row.id)},{default:l(()=>[c(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[y,["crm:product:delete"]]])]),_:1})]),_:1},8,["data"])),[[Z,t(g)]]),a(Q,{total:t(k),page:t(r).pageNo,"onUpdate:page":o[3]||(o[3]=e=>t(r).pageNo=e),limit:t(r).pageSize,"onUpdate:limit":o[4]||(o[4]=e=>t(r).pageSize=e),onPagination:p},null,8,["total","page","limit"])]),_:1}),a(za,{ref_key:"formRef",ref:R,onSuccess:p},null,512)],64)}}})});export{ja as __tla,F as default};
