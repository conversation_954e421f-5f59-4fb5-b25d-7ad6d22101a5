var St=Object.defineProperty;var It=(b,x,$)=>x in b?St(b,x,{enumerable:!0,configurable:!0,writable:!0,value:$}):b[x]=$;var f=(b,x,$)=>(It(b,typeof x!="symbol"?x+"":x,$),$),Ct=(b,x,$)=>{if(!x.has(b))throw TypeError("Cannot "+$)};var ue=(b,x,$)=>{if(x.has(b))throw TypeError("Cannot add the same private member more than once");x instanceof WeakSet?x.add(b):x.set(b,$)};var te=(b,x,$)=>(Ct(b,x,"access private method"),$);import{d as Xe,I as We,e5 as Ye,r as ne,c7 as Je,at as Et,C as Ke,o as q,c as P,a as I,e as qt,b as et,g as w,F as tt,k as Mt,i as y,w as C,j as nt,t as ge,l as Lt,a9 as se,e6 as Ht,e7 as Zt,e8 as Pt,ay as Dt,N as Bt,br as Qt,a5 as Ot,a6 as Vt,B as jt,__tla as Gt}from"./index-BUSn51wb.js";import{E as Ut,__tla as Nt}from"./el-text-CIwNlU-U.js";import{E as Ft,__tla as Xt}from"./el-avatar-Da2TGjmj.js";import{f as st,__tla as Wt}from"./formatTime-DWdBpgsM.js";import{H as Yt,__tla as Jt}from"./index-DCh5hX9K.js";import{C as Kt,__tla as en}from"./index-CBZlCJOz.js";import{a as tn}from"./avatar-BG6NdH5s.js";import{r as nn}from"./gpt-WhTktY3S.js";import"./fetch-D5K_4anA.js";let rt,sn=Promise.all([(()=>{try{return Gt}catch{}})(),(()=>{try{return Nt}catch{}})(),(()=>{try{return Xt}catch{}})(),(()=>{try{return Wt}catch{}})(),(()=>{try{return Jt}catch{}})(),(()=>{try{return en}catch{}})()]).then(async()=>{var G,ke,ee,it,Ne;const b="data:image/svg+xml,%3c?xml%20version='1.0'%20standalone='no'?%3e%3c!DOCTYPE%20svg%20PUBLIC%20'-//W3C//DTD%20SVG%201.1//EN'%20'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3e%3csvg%20t='1715352878351'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='1499'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20width='200'%20height='200'%3e%3cpath%20d='M624.5%20786.3c92.9%200%20168.2-75.3%20168.2-168.2V309c0-92.4-75.3-168.2-168.2-168.2H303.6c-92.4%200-168.2%2075.3-168.2%20168.2v309.1c0%2092.4%2075.3%20168.2%20168.2%20168.2h320.9zM178.2%20618.1V309c0-69.4%2056.1-125.5%20125.5-125.5h320.9c69.4%200%20125.5%2056.1%20125.5%20125.5v309.1c0%2069.4-56.1%20125.5-125.5%20125.5h-321c-69.4%200-125.4-56.1-125.4-125.5z'%20p-id='1500'%20fill='%238a8a8a'%3e%3c/path%3e%3cpath%20d='M849.8%20295.1v361.5c0%20102.7-83.6%20186.3-186.3%20186.3H279.1v42.7h384.4c126.3%200%20229.1-102.8%20229.1-229.1V295.1h-42.8zM307.9%20361.8h312.3c11.8%200%2021.4-9.6%2021.4-21.4%200-11.8-9.6-21.4-21.4-21.4H307.9c-11.8%200-21.4%209.6-21.4%2021.4%200%2011.9%209.6%2021.4%2021.4%2021.4zM307.9%20484.6h312.3c11.8%200%2021.4-9.6%2021.4-21.4%200-11.8-9.6-21.4-21.4-21.4H307.9c-11.8%200-21.4%209.6-21.4%2021.4%200%2011.9%209.6%2021.4%2021.4%2021.4z'%20p-id='1501'%20fill='%238a8a8a'%3e%3c/path%3e%3cpath%20d='M620.2%20607.4c11.8%200%2021.4-9.6%2021.4-21.4%200-11.8-9.6-21.4-21.4-21.4H307.9c-11.8%200-21.4%209.6-21.4%2021.4%200%2011.8%209.6%2021.4%2021.4%2021.4h312.3z'%20p-id='1502'%20fill='%238a8a8a'%3e%3c/path%3e%3c/svg%3e",x="data:image/svg+xml,%3c?xml%20version='1.0'%20standalone='no'?%3e%3c!DOCTYPE%20svg%20PUBLIC%20'-//W3C//DTD%20SVG%201.1//EN'%20'http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd'%3e%3csvg%20t='1715354120346'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='3256'%20xmlns:xlink='http://www.w3.org/1999/xlink'%20width='200'%20height='200'%3e%3cpath%20d='M907.1%20263.7H118.9c-9.1%200-16.4-7.3-16.4-16.4s7.3-16.4%2016.4-16.4H907c9.1%200%2016.4%207.3%2016.4%2016.4s-7.3%2016.4-16.3%2016.4z'%20fill='%238a8a8a'%20p-id='3257'%3e%3c/path%3e%3cpath%20d='M772.5%20928.3H257.4c-27.7%200-50.2-22.5-50.2-50.2V247.2c0-9.1%207.3-16.4%2016.4-16.4H801c12.1%200%2021.9%209.8%2021.9%2021.9v625.2c0%2027.8-22.6%2050.4-50.4%2050.4zM240%20263.7v614.4c0%209.6%207.8%2017.4%2017.4%2017.4h515.2c9.7%200%2017.5-7.9%2017.5-17.5V263.7H240zM657.4%20131.1H368.6c-9.1%200-16.4-7.3-16.4-16.4s7.3-16.4%2016.4-16.4h288.7c9.1%200%2016.4%207.3%2016.4%2016.4s-7.3%2016.4-16.3%2016.4z'%20fill='%238a8a8a'%20p-id='3258'%3e%3c/path%3e%3cpath%20d='M416%20754.5c-9.1%200-16.4-7.3-16.4-16.4V517.8c0-9.1%207.3-16.4%2016.4-16.4s16.4%207.3%2016.4%2016.4V738c0.1%209.1-7.3%2016.5-16.4%2016.5z'%20fill='%238a8a8a'%20p-id='3259'%3e%3c/path%3e%3cpath%20d='M416%20465.2c-9.1%200-16.4-7.3-16.4-16.4v-59.4c0-9.1%207.3-16.4%2016.4-16.4s16.4%207.3%2016.4%2016.4v59.4c0.1%209.1-7.3%2016.4-16.4%2016.4zM604.9%20754.5c-9.1%200-16.4-7.3-16.4-16.4v-67.2c0-9.1%207.3-16.4%2016.4-16.4s16.4%207.3%2016.4%2016.4V738c0%209.1-7.3%2016.5-16.4%2016.5z'%20fill='%238a8a8a'%20opacity='.4'%20p-id='3260'%3e%3c/path%3e%3cpath%20d='M604.9%20619.1c-9.1%200-16.4-7.3-16.4-16.4V389.4c0-9.1%207.3-16.4%2016.4-16.4s16.4%207.3%2016.4%2016.4v213.3c0%209.1-7.3%2016.4-16.4%2016.4z'%20fill='%238a8a8a'%20p-id='3261'%3e%3c/path%3e%3c/svg%3e";function $(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let M={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function de(c){M=c}const fe=/[&<>"']/,lt=new RegExp(fe.source,"g"),xe=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,at=new RegExp(xe.source,"g"),ot={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},me=c=>ot[c];function _(c,t){if(t){if(fe.test(c))return c.replace(lt,me)}else if(xe.test(c))return c.replace(at,me);return c}const ct=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function ht(c){return c.replace(ct,(t,e)=>(e=e.toLowerCase())==="colon"?":":e.charAt(0)==="#"?e.charAt(1)==="x"?String.fromCharCode(parseInt(e.substring(2),16)):String.fromCharCode(+e.substring(1)):"")}const pt=/(^|[^\[])\^/g;function d(c,t){let e=typeof c=="string"?c:c.source;t=t||"";const n={replace:(s,r)=>{let i=typeof r=="string"?r:r.source;return i=i.replace(pt,"$1"),e=e.replace(s,i),n},getRegex:()=>new RegExp(e,t)};return n}function be(c){try{c=encodeURI(c).replace(/%25/g,"%")}catch{return null}return c}const D={exec:()=>null};function we(c,t){const e=c.replace(/\|/g,(s,r,i)=>{let l=!1,h=r;for(;--h>=0&&i[h]==="\\";)l=!l;return l?"|":" |"}).split(/ \|/);let n=0;if(e[0].trim()||e.shift(),e.length>0&&!e[e.length-1].trim()&&e.pop(),t)if(e.length>t)e.splice(t);else for(;e.length<t;)e.push("");for(;n<e.length;n++)e[n]=e[n].trim().replace(/\\\|/g,"|");return e}function F(c,t,e){const n=c.length;if(n===0)return"";let s=0;for(;s<n;){const r=c.charAt(n-s-1);if(r!==t||e){if(r===t||!e)break;s++}else s++}return c.slice(0,n-s)}function ye(c,t,e,n){const s=t.href,r=t.title?_(t.title):null,i=c[1].replace(/\\([\[\]])/g,"$1");if(c[0].charAt(0)!=="!"){n.state.inLink=!0;const l={type:"link",raw:e,href:s,title:r,text:i,tokens:n.inlineTokens(i)};return n.state.inLink=!1,l}return{type:"image",raw:e,href:s,title:r,text:_(i)}}class X{constructor(t){f(this,"options");f(this,"rules");f(this,"lexer");this.options=t||M}space(t){const e=this.rules.block.newline.exec(t);if(e&&e[0].length>0)return{type:"space",raw:e[0]}}code(t){const e=this.rules.block.code.exec(t);if(e){const n=e[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:e[0],codeBlockStyle:"indented",text:this.options.pedantic?n:F(n,`
`)}}}fences(t){const e=this.rules.block.fences.exec(t);if(e){const n=e[0],s=function(r,i){const l=r.match(/^(\s+)(?:```)/);if(l===null)return i;const h=l[1];return i.split(`
`).map(o=>{const a=o.match(/^\s+/);if(a===null)return o;const[u]=a;return u.length>=h.length?o.slice(h.length):o}).join(`
`)}(n,e[3]||"");return{type:"code",raw:n,lang:e[2]?e[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):e[2],text:s}}}heading(t){const e=this.rules.block.heading.exec(t);if(e){let n=e[2].trim();if(/#$/.test(n)){const s=F(n,"#");this.options.pedantic?n=s.trim():s&&!/ $/.test(s)||(n=s.trim())}return{type:"heading",raw:e[0],depth:e[1].length,text:n,tokens:this.lexer.inline(n)}}}hr(t){const e=this.rules.block.hr.exec(t);if(e)return{type:"hr",raw:e[0]}}blockquote(t){const e=this.rules.block.blockquote.exec(t);if(e){let n=e[0].replace(/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,`
    $1`);n=F(n.replace(/^ *>[ \t]?/gm,""),`
`);const s=this.lexer.state.top;this.lexer.state.top=!0;const r=this.lexer.blockTokens(n);return this.lexer.state.top=s,{type:"blockquote",raw:e[0],tokens:r,text:n}}}list(t){let e=this.rules.block.list.exec(t);if(e){let n=e[1].trim();const s=n.length>1,r={type:"list",raw:"",ordered:s,start:s?+n.slice(0,-1):"",loose:!1,items:[]};n=s?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=s?n:"[*+-]");const i=new RegExp(`^( {0,3}${n})((?:[	 ][^\\n]*)?(?:\\n|$))`);let l="",h="",o=!1;for(;t;){let a=!1;if(!(e=i.exec(t))||this.rules.block.hr.test(t))break;l=e[0],t=t.substring(l.length);let u=e[2].split(`
`,1)[0].replace(/^\t+/,m=>" ".repeat(3*m.length)),p=t.split(`
`,1)[0],k=0;this.options.pedantic?(k=2,h=u.trimStart()):(k=e[2].search(/[^ ]/),k=k>4?1:k,h=u.slice(k),k+=e[1].length);let S=!1;if(!u&&/^ *$/.test(p)&&(l+=p+`
`,t=t.substring(p.length+1),a=!0),!a){const m=new RegExp(`^ {0,${Math.min(3,k-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),U=new RegExp(`^ {0,${Math.min(3,k-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),H=new RegExp(`^ {0,${Math.min(3,k-1)}}(?:\`\`\`|~~~)`),Z=new RegExp(`^ {0,${Math.min(3,k-1)}}#`);for(;t;){const T=t.split(`
`,1)[0];if(p=T,this.options.pedantic&&(p=p.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),H.test(p)||Z.test(p)||m.test(p)||U.test(t))break;if(p.search(/[^ ]/)>=k||!p.trim())h+=`
`+p.slice(k);else{if(S||u.search(/[^ ]/)>=4||H.test(u)||Z.test(u)||U.test(u))break;h+=`
`+p}S||p.trim()||(S=!0),l+=T+`
`,t=t.substring(T.length+1),u=p.slice(k)}}r.loose||(o?r.loose=!0:/\n *\n *$/.test(l)&&(o=!0));let E,v=null;this.options.gfm&&(v=/^\[[ xX]\] /.exec(h),v&&(E=v[0]!=="[ ] ",h=h.replace(/^\[[ xX]\] +/,""))),r.items.push({type:"list_item",raw:l,task:!!v,checked:E,loose:!1,text:h,tokens:[]}),r.raw+=l}r.items[r.items.length-1].raw=l.trimEnd(),r.items[r.items.length-1].text=h.trimEnd(),r.raw=r.raw.trimEnd();for(let a=0;a<r.items.length;a++)if(this.lexer.state.top=!1,r.items[a].tokens=this.lexer.blockTokens(r.items[a].text,[]),!r.loose){const u=r.items[a].tokens.filter(k=>k.type==="space"),p=u.length>0&&u.some(k=>/\n.*\n/.test(k.raw));r.loose=p}if(r.loose)for(let a=0;a<r.items.length;a++)r.items[a].loose=!0;return r}}html(t){const e=this.rules.block.html.exec(t);if(e)return{type:"html",block:!0,raw:e[0],pre:e[1]==="pre"||e[1]==="script"||e[1]==="style",text:e[0]}}def(t){const e=this.rules.block.def.exec(t);if(e){const n=e[1].toLowerCase().replace(/\s+/g," "),s=e[2]?e[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",r=e[3]?e[3].substring(1,e[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):e[3];return{type:"def",tag:n,raw:e[0],href:s,title:r}}}table(t){const e=this.rules.block.table.exec(t);if(!e||!/[:|]/.test(e[2]))return;const n=we(e[1]),s=e[2].replace(/^\||\| *$/g,"").split("|"),r=e[3]&&e[3].trim()?e[3].replace(/\n[ \t]*$/,"").split(`
`):[],i={type:"table",raw:e[0],header:[],align:[],rows:[]};if(n.length===s.length){for(const l of s)/^ *-+: *$/.test(l)?i.align.push("right"):/^ *:-+: *$/.test(l)?i.align.push("center"):/^ *:-+ *$/.test(l)?i.align.push("left"):i.align.push(null);for(const l of n)i.header.push({text:l,tokens:this.lexer.inline(l)});for(const l of r)i.rows.push(we(l,i.header.length).map(h=>({text:h,tokens:this.lexer.inline(h)})));return i}}lheading(t){const e=this.rules.block.lheading.exec(t);if(e)return{type:"heading",raw:e[0],depth:e[2].charAt(0)==="="?1:2,text:e[1],tokens:this.lexer.inline(e[1])}}paragraph(t){const e=this.rules.block.paragraph.exec(t);if(e){const n=e[1].charAt(e[1].length-1)===`
`?e[1].slice(0,-1):e[1];return{type:"paragraph",raw:e[0],text:n,tokens:this.lexer.inline(n)}}}text(t){const e=this.rules.block.text.exec(t);if(e)return{type:"text",raw:e[0],text:e[0],tokens:this.lexer.inline(e[0])}}escape(t){const e=this.rules.inline.escape.exec(t);if(e)return{type:"escape",raw:e[0],text:_(e[1])}}tag(t){const e=this.rules.inline.tag.exec(t);if(e)return!this.lexer.state.inLink&&/^<a /i.test(e[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(e[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(e[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(e[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:e[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:e[0]}}link(t){const e=this.rules.inline.link.exec(t);if(e){const n=e[2].trim();if(!this.options.pedantic&&/^</.test(n)){if(!/>$/.test(n))return;const i=F(n.slice(0,-1),"\\");if((n.length-i.length)%2==0)return}else{const i=function(l,h){if(l.indexOf(h[1])===-1)return-1;let o=0;for(let a=0;a<l.length;a++)if(l[a]==="\\")a++;else if(l[a]===h[0])o++;else if(l[a]===h[1]&&(o--,o<0))return a;return-1}(e[2],"()");if(i>-1){const l=(e[0].indexOf("!")===0?5:4)+e[1].length+i;e[2]=e[2].substring(0,i),e[0]=e[0].substring(0,l).trim(),e[3]=""}}let s=e[2],r="";if(this.options.pedantic){const i=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(s);i&&(s=i[1],r=i[3])}else r=e[3]?e[3].slice(1,-1):"";return s=s.trim(),/^</.test(s)&&(s=this.options.pedantic&&!/>$/.test(n)?s.slice(1):s.slice(1,-1)),ye(e,{href:s&&s.replace(this.rules.inline.anyPunctuation,"$1"),title:r&&r.replace(this.rules.inline.anyPunctuation,"$1")},e[0],this.lexer)}}reflink(t,e){let n;if((n=this.rules.inline.reflink.exec(t))||(n=this.rules.inline.nolink.exec(t))){const s=e[(n[2]||n[1]).replace(/\s+/g," ").toLowerCase()];if(!s){const r=n[0].charAt(0);return{type:"text",raw:r,text:r}}return ye(n,s,n[0],this.lexer)}}emStrong(t,e,n=""){let s=this.rules.inline.emStrongLDelim.exec(t);if(s&&!(s[3]&&n.match(/[\p{L}\p{N}]/u))&&(!(s[1]||s[2])||!n||this.rules.inline.punctuation.exec(n))){const r=[...s[0]].length-1;let i,l,h=r,o=0;const a=s[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(a.lastIndex=0,e=e.slice(-1*t.length+r);(s=a.exec(e))!=null;){if(i=s[1]||s[2]||s[3]||s[4]||s[5]||s[6],!i)continue;if(l=[...i].length,s[3]||s[4]){h+=l;continue}if((s[5]||s[6])&&r%3&&!((r+l)%3)){o+=l;continue}if(h-=l,h>0)continue;l=Math.min(l,l+h+o);const u=[...s[0]][0].length,p=t.slice(0,r+s.index+u+l);if(Math.min(r,l)%2){const S=p.slice(1,-1);return{type:"em",raw:p,text:S,tokens:this.lexer.inlineTokens(S)}}const k=p.slice(2,-2);return{type:"strong",raw:p,text:k,tokens:this.lexer.inlineTokens(k)}}}}codespan(t){const e=this.rules.inline.code.exec(t);if(e){let n=e[2].replace(/\n/g," ");const s=/[^ ]/.test(n),r=/^ /.test(n)&&/ $/.test(n);return s&&r&&(n=n.substring(1,n.length-1)),n=_(n,!0),{type:"codespan",raw:e[0],text:n}}}br(t){const e=this.rules.inline.br.exec(t);if(e)return{type:"br",raw:e[0]}}del(t){const e=this.rules.inline.del.exec(t);if(e)return{type:"del",raw:e[0],text:e[2],tokens:this.lexer.inlineTokens(e[2])}}autolink(t){const e=this.rules.inline.autolink.exec(t);if(e){let n,s;return e[2]==="@"?(n=_(e[1]),s="mailto:"+n):(n=_(e[1]),s=n),{type:"link",raw:e[0],text:n,href:s,tokens:[{type:"text",raw:n,text:n}]}}}url(t){var n;let e;if(e=this.rules.inline.url.exec(t)){let s,r;if(e[2]==="@")s=_(e[0]),r="mailto:"+s;else{let i;do i=e[0],e[0]=((n=this.rules.inline._backpedal.exec(e[0]))==null?void 0:n[0])??"";while(i!==e[0]);s=_(e[0]),r=e[1]==="www."?"http://"+e[0]:e[0]}return{type:"link",raw:e[0],text:s,href:r,tokens:[{type:"text",raw:s,text:s}]}}}inlineText(t){const e=this.rules.inline.text.exec(t);if(e){let n;return n=this.lexer.state.inRawBlock?e[0]:_(e[0]),{type:"text",raw:e[0],text:n}}}}const B=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,$e=/(?:[*+-]|\d{1,9}[.)])/,_e=d(/^(?!bull |blockCode|fences|blockquote|heading|html)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,$e).replace(/blockCode/g,/ {4}/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).getRegex(),re=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,ie=/(?!\s*\])(?:\\.|[^\[\]\\])+/,ut=d(/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/).replace("label",ie).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),gt=d(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,$e).getRegex(),W="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",le=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,kt=d("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))","i").replace("comment",le).replace("tag",W).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),ze=d(re).replace("hr",B).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",W).getRegex(),ae={blockquote:d(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",ze).getRegex(),code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,def:ut,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:B,html:kt,lheading:_e,list:gt,newline:/^(?: *(?:\n|$))+/,paragraph:ze,table:D,text:/^[^\n]+/},ve=d("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",B).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",W).getRegex(),dt={...ae,table:ve,paragraph:d(re).replace("hr",B).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",ve).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",W).getRegex()},ft={...ae,html:d(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",le).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:D,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:d(re).replace("hr",B).replace("heading",` *#{1,6} *[^
]`).replace("lheading",_e).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},Te=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,Re=/^( {2,}|\\)\n(?!\s*$)/,Q="\\p{P}\\p{S}",xt=d(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,Q).getRegex(),mt=d(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,Q).getRegex(),bt=d("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,Q).getRegex(),wt=d("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,Q).getRegex(),yt=d(/\\([punct])/,"gu").replace(/punct/g,Q).getRegex(),$t=d(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),_t=d(le).replace("(?:-->|$)","-->").getRegex(),zt=d("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",_t).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),Y=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,vt=d(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",Y).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Ae=d(/^!?\[(label)\]\[(ref)\]/).replace("label",Y).replace("ref",ie).getRegex(),Se=d(/^!?\[(ref)\](?:\[\])?/).replace("ref",ie).getRegex(),oe={_backpedal:D,anyPunctuation:yt,autolink:$t,blockSkip:/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,br:Re,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:D,emStrongLDelim:mt,emStrongRDelimAst:bt,emStrongRDelimUnd:wt,escape:Te,link:vt,nolink:Se,punctuation:xt,reflink:Ae,reflinkSearch:d("reflink|nolink(?!\\()","g").replace("reflink",Ae).replace("nolink",Se).getRegex(),tag:zt,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:D},Tt={...oe,link:d(/^!?\[(label)\]\((.*?)\)/).replace("label",Y).getRegex(),reflink:d(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",Y).getRegex()},ce={...oe,escape:d(Te).replace("])","~|])").getRegex(),url:d(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Rt={...ce,br:d(Re).replace("{2,}","*").getRegex(),text:d(ce.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},J={normal:ae,gfm:dt,pedantic:ft},O={normal:oe,gfm:ce,breaks:Rt,pedantic:Tt};class R{constructor(t){f(this,"tokens");f(this,"options");f(this,"state");f(this,"tokenizer");f(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=t||M,this.options.tokenizer=this.options.tokenizer||new X,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const e={block:J.normal,inline:O.normal};this.options.pedantic?(e.block=J.pedantic,e.inline=O.pedantic):this.options.gfm&&(e.block=J.gfm,this.options.breaks?e.inline=O.breaks:e.inline=O.gfm),this.tokenizer.rules=e}static get rules(){return{block:J,inline:O}}static lex(t,e){return new R(e).lex(t)}static lexInline(t,e){return new R(e).inlineTokens(t)}lex(t){t=t.replace(/\r\n|\r/g,`
`),this.blockTokens(t,this.tokens);for(let e=0;e<this.inlineQueue.length;e++){const n=this.inlineQueue[e];this.inlineTokens(n.src,n.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(t,e=[]){let n,s,r,i;for(t=this.options.pedantic?t.replace(/\t/g,"    ").replace(/^ +$/gm,""):t.replace(/^( *)(\t+)/gm,(l,h,o)=>h+"    ".repeat(o.length));t;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(l=>!!(n=l.call({lexer:this},t,e))&&(t=t.substring(n.raw.length),e.push(n),!0))))if(n=this.tokenizer.space(t))t=t.substring(n.raw.length),n.raw.length===1&&e.length>0?e[e.length-1].raw+=`
`:e.push(n);else if(n=this.tokenizer.code(t))t=t.substring(n.raw.length),s=e[e.length-1],!s||s.type!=="paragraph"&&s.type!=="text"?e.push(n):(s.raw+=`
`+n.raw,s.text+=`
`+n.text,this.inlineQueue[this.inlineQueue.length-1].src=s.text);else if(n=this.tokenizer.fences(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.heading(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.hr(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.blockquote(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.list(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.html(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.def(t))t=t.substring(n.raw.length),s=e[e.length-1],!s||s.type!=="paragraph"&&s.type!=="text"?this.tokens.links[n.tag]||(this.tokens.links[n.tag]={href:n.href,title:n.title}):(s.raw+=`
`+n.raw,s.text+=`
`+n.raw,this.inlineQueue[this.inlineQueue.length-1].src=s.text);else if(n=this.tokenizer.table(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.lheading(t))t=t.substring(n.raw.length),e.push(n);else{if(r=t,this.options.extensions&&this.options.extensions.startBlock){let l=1/0;const h=t.slice(1);let o;this.options.extensions.startBlock.forEach(a=>{o=a.call({lexer:this},h),typeof o=="number"&&o>=0&&(l=Math.min(l,o))}),l<1/0&&l>=0&&(r=t.substring(0,l+1))}if(this.state.top&&(n=this.tokenizer.paragraph(r)))s=e[e.length-1],i&&s.type==="paragraph"?(s.raw+=`
`+n.raw,s.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=s.text):e.push(n),i=r.length!==t.length,t=t.substring(n.raw.length);else if(n=this.tokenizer.text(t))t=t.substring(n.raw.length),s=e[e.length-1],s&&s.type==="text"?(s.raw+=`
`+n.raw,s.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=s.text):e.push(n);else if(t){const l="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(l);break}throw new Error(l)}}return this.state.top=!0,e}inline(t,e=[]){return this.inlineQueue.push({src:t,tokens:e}),e}inlineTokens(t,e=[]){let n,s,r,i,l,h,o=t;if(this.tokens.links){const a=Object.keys(this.tokens.links);if(a.length>0)for(;(i=this.tokenizer.rules.inline.reflinkSearch.exec(o))!=null;)a.includes(i[0].slice(i[0].lastIndexOf("[")+1,-1))&&(o=o.slice(0,i.index)+"["+"a".repeat(i[0].length-2)+"]"+o.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(i=this.tokenizer.rules.inline.blockSkip.exec(o))!=null;)o=o.slice(0,i.index)+"["+"a".repeat(i[0].length-2)+"]"+o.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(i=this.tokenizer.rules.inline.anyPunctuation.exec(o))!=null;)o=o.slice(0,i.index)+"++"+o.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;t;)if(l||(h=""),l=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(a=>!!(n=a.call({lexer:this},t,e))&&(t=t.substring(n.raw.length),e.push(n),!0))))if(n=this.tokenizer.escape(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.tag(t))t=t.substring(n.raw.length),s=e[e.length-1],s&&n.type==="text"&&s.type==="text"?(s.raw+=n.raw,s.text+=n.text):e.push(n);else if(n=this.tokenizer.link(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.reflink(t,this.tokens.links))t=t.substring(n.raw.length),s=e[e.length-1],s&&n.type==="text"&&s.type==="text"?(s.raw+=n.raw,s.text+=n.text):e.push(n);else if(n=this.tokenizer.emStrong(t,o,h))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.codespan(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.br(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.del(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.autolink(t))t=t.substring(n.raw.length),e.push(n);else if(this.state.inLink||!(n=this.tokenizer.url(t))){if(r=t,this.options.extensions&&this.options.extensions.startInline){let a=1/0;const u=t.slice(1);let p;this.options.extensions.startInline.forEach(k=>{p=k.call({lexer:this},u),typeof p=="number"&&p>=0&&(a=Math.min(a,p))}),a<1/0&&a>=0&&(r=t.substring(0,a+1))}if(n=this.tokenizer.inlineText(r))t=t.substring(n.raw.length),n.raw.slice(-1)!=="_"&&(h=n.raw.slice(-1)),l=!0,s=e[e.length-1],s&&s.type==="text"?(s.raw+=n.raw,s.text+=n.text):e.push(n);else if(t){const a="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(a);break}throw new Error(a)}}else t=t.substring(n.raw.length),e.push(n);return e}}class K{constructor(t){f(this,"options");this.options=t||M}code(t,e,n){var r;const s=(r=(e||"").match(/^\S*/))==null?void 0:r[0];return t=t.replace(/\n$/,"")+`
`,s?'<pre><code class="language-'+_(s)+'">'+(n?t:_(t,!0))+`</code></pre>
`:"<pre><code>"+(n?t:_(t,!0))+`</code></pre>
`}blockquote(t){return`<blockquote>
${t}</blockquote>
`}html(t,e){return t}heading(t,e,n){return`<h${e}>${t}</h${e}>
`}hr(){return`<hr>
`}list(t,e,n){const s=e?"ol":"ul";return"<"+s+(e&&n!==1?' start="'+n+'"':"")+`>
`+t+"</"+s+`>
`}listitem(t,e,n){return`<li>${t}</li>
`}checkbox(t){return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(t){return`<p>${t}</p>
`}table(t,e){return e&&(e=`<tbody>${e}</tbody>`),`<table>
<thead>
`+t+`</thead>
`+e+`</table>
`}tablerow(t){return`<tr>
${t}</tr>
`}tablecell(t,e){const n=e.header?"th":"td";return(e.align?`<${n} align="${e.align}">`:`<${n}>`)+t+`</${n}>
`}strong(t){return`<strong>${t}</strong>`}em(t){return`<em>${t}</em>`}codespan(t){return`<code>${t}</code>`}br(){return"<br>"}del(t){return`<del>${t}</del>`}link(t,e,n){const s=be(t);if(s===null)return n;let r='<a href="'+(t=s)+'"';return e&&(r+=' title="'+e+'"'),r+=">"+n+"</a>",r}image(t,e,n){const s=be(t);if(s===null)return n;let r=`<img src="${t=s}" alt="${n}"`;return e&&(r+=` title="${e}"`),r+=">",r}text(t){return t}}class he{strong(t){return t}em(t){return t}codespan(t){return t}del(t){return t}html(t){return t}text(t){return t}link(t,e,n){return""+n}image(t,e,n){return""+n}br(){return""}}class A{constructor(t){f(this,"options");f(this,"renderer");f(this,"textRenderer");this.options=t||M,this.options.renderer=this.options.renderer||new K,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new he}static parse(t,e){return new A(e).parse(t)}static parseInline(t,e){return new A(e).parseInline(t)}parse(t,e=!0){let n="";for(let s=0;s<t.length;s++){const r=t[s];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[r.type]){const i=r,l=this.options.extensions.renderers[i.type].call({parser:this},i);if(l!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(i.type)){n+=l||"";continue}}switch(r.type){case"space":continue;case"hr":n+=this.renderer.hr();continue;case"heading":{const i=r;n+=this.renderer.heading(this.parseInline(i.tokens),i.depth,ht(this.parseInline(i.tokens,this.textRenderer)));continue}case"code":{const i=r;n+=this.renderer.code(i.text,i.lang,!!i.escaped);continue}case"table":{const i=r;let l="",h="";for(let a=0;a<i.header.length;a++)h+=this.renderer.tablecell(this.parseInline(i.header[a].tokens),{header:!0,align:i.align[a]});l+=this.renderer.tablerow(h);let o="";for(let a=0;a<i.rows.length;a++){const u=i.rows[a];h="";for(let p=0;p<u.length;p++)h+=this.renderer.tablecell(this.parseInline(u[p].tokens),{header:!1,align:i.align[p]});o+=this.renderer.tablerow(h)}n+=this.renderer.table(l,o);continue}case"blockquote":{const i=r,l=this.parse(i.tokens);n+=this.renderer.blockquote(l);continue}case"list":{const i=r,l=i.ordered,h=i.start,o=i.loose;let a="";for(let u=0;u<i.items.length;u++){const p=i.items[u],k=p.checked,S=p.task;let E="";if(p.task){const v=this.renderer.checkbox(!!k);o?p.tokens.length>0&&p.tokens[0].type==="paragraph"?(p.tokens[0].text=v+" "+p.tokens[0].text,p.tokens[0].tokens&&p.tokens[0].tokens.length>0&&p.tokens[0].tokens[0].type==="text"&&(p.tokens[0].tokens[0].text=v+" "+p.tokens[0].tokens[0].text)):p.tokens.unshift({type:"text",text:v+" "}):E+=v+" "}E+=this.parse(p.tokens,o),a+=this.renderer.listitem(E,S,!!k)}n+=this.renderer.list(a,l,h);continue}case"html":{const i=r;n+=this.renderer.html(i.text,i.block);continue}case"paragraph":{const i=r;n+=this.renderer.paragraph(this.parseInline(i.tokens));continue}case"text":{let i=r,l=i.tokens?this.parseInline(i.tokens):i.text;for(;s+1<t.length&&t[s+1].type==="text";)i=t[++s],l+=`
`+(i.tokens?this.parseInline(i.tokens):i.text);n+=e?this.renderer.paragraph(l):l;continue}default:{const i='Token with "'+r.type+'" type was not found.';if(this.options.silent)return console.error(i),"";throw new Error(i)}}}return n}parseInline(t,e){e=e||this.renderer;let n="";for(let s=0;s<t.length;s++){const r=t[s];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[r.type]){const i=this.options.extensions.renderers[r.type].call({parser:this},r);if(i!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(r.type)){n+=i||"";continue}}switch(r.type){case"escape":{const i=r;n+=e.text(i.text);break}case"html":{const i=r;n+=e.html(i.text);break}case"link":{const i=r;n+=e.link(i.href,i.title,this.parseInline(i.tokens,e));break}case"image":{const i=r;n+=e.image(i.href,i.title,i.text);break}case"strong":{const i=r;n+=e.strong(this.parseInline(i.tokens,e));break}case"em":{const i=r;n+=e.em(this.parseInline(i.tokens,e));break}case"codespan":{const i=r;n+=e.codespan(i.text);break}case"br":n+=e.br();break;case"del":{const i=r;n+=e.del(this.parseInline(i.tokens,e));break}case"text":{const i=r;n+=e.text(i.text);break}default:{const i='Token with "'+r.type+'" type was not found.';if(this.options.silent)return console.error(i),"";throw new Error(i)}}}return n}}class V{constructor(t){f(this,"options");this.options=t||M}preprocess(t){return t}postprocess(t){return t}processAllTokens(t){return t}}f(V,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));const L=new(Ne=class{constructor(...c){ue(this,G);ue(this,ee);f(this,"defaults",{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null});f(this,"options",this.setOptions);f(this,"parse",te(this,G,ke).call(this,R.lex,A.parse));f(this,"parseInline",te(this,G,ke).call(this,R.lexInline,A.parseInline));f(this,"Parser",A);f(this,"Renderer",K);f(this,"TextRenderer",he);f(this,"Lexer",R);f(this,"Tokenizer",X);f(this,"Hooks",V);this.use(...c)}walkTokens(c,t){var n,s;let e=[];for(const r of c)switch(e=e.concat(t.call(this,r)),r.type){case"table":{const i=r;for(const l of i.header)e=e.concat(this.walkTokens(l.tokens,t));for(const l of i.rows)for(const h of l)e=e.concat(this.walkTokens(h.tokens,t));break}case"list":{const i=r;e=e.concat(this.walkTokens(i.items,t));break}default:{const i=r;(s=(n=this.defaults.extensions)==null?void 0:n.childTokens)!=null&&s[i.type]?this.defaults.extensions.childTokens[i.type].forEach(l=>{const h=i[l].flat(1/0);e=e.concat(this.walkTokens(h,t))}):i.tokens&&(e=e.concat(this.walkTokens(i.tokens,t)))}}return e}use(...c){const t=this.defaults.extensions||{renderers:{},childTokens:{}};return c.forEach(e=>{const n={...e};if(n.async=this.defaults.async||n.async||!1,e.extensions&&(e.extensions.forEach(s=>{if(!s.name)throw new Error("extension name required");if("renderer"in s){const r=t.renderers[s.name];t.renderers[s.name]=r?function(...i){let l=s.renderer.apply(this,i);return l===!1&&(l=r.apply(this,i)),l}:s.renderer}if("tokenizer"in s){if(!s.level||s.level!=="block"&&s.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const r=t[s.level];r?r.unshift(s.tokenizer):t[s.level]=[s.tokenizer],s.start&&(s.level==="block"?t.startBlock?t.startBlock.push(s.start):t.startBlock=[s.start]:s.level==="inline"&&(t.startInline?t.startInline.push(s.start):t.startInline=[s.start]))}"childTokens"in s&&s.childTokens&&(t.childTokens[s.name]=s.childTokens)}),n.extensions=t),e.renderer){const s=this.defaults.renderer||new K(this.defaults);for(const r in e.renderer){if(!(r in s))throw new Error(`renderer '${r}' does not exist`);if(r==="options")continue;const i=r,l=e.renderer[i],h=s[i];s[i]=(...o)=>{let a=l.apply(s,o);return a===!1&&(a=h.apply(s,o)),a||""}}n.renderer=s}if(e.tokenizer){const s=this.defaults.tokenizer||new X(this.defaults);for(const r in e.tokenizer){if(!(r in s))throw new Error(`tokenizer '${r}' does not exist`);if(["options","rules","lexer"].includes(r))continue;const i=r,l=e.tokenizer[i],h=s[i];s[i]=(...o)=>{let a=l.apply(s,o);return a===!1&&(a=h.apply(s,o)),a}}n.tokenizer=s}if(e.hooks){const s=this.defaults.hooks||new V;for(const r in e.hooks){if(!(r in s))throw new Error(`hook '${r}' does not exist`);if(r==="options")continue;const i=r,l=e.hooks[i],h=s[i];V.passThroughHooks.has(r)?s[i]=o=>{if(this.defaults.async)return Promise.resolve(l.call(s,o)).then(u=>h.call(s,u));const a=l.call(s,o);return h.call(s,a)}:s[i]=(...o)=>{let a=l.apply(s,o);return a===!1&&(a=h.apply(s,o)),a}}n.hooks=s}if(e.walkTokens){const s=this.defaults.walkTokens,r=e.walkTokens;n.walkTokens=function(i){let l=[];return l.push(r.call(this,i)),s&&(l=l.concat(s.call(this,i))),l}}this.defaults={...this.defaults,...n}}),this}setOptions(c){return this.defaults={...this.defaults,...c},this}lexer(c,t){return R.lex(c,t??this.defaults)}parser(c,t){return A.parse(c,t??this.defaults)}},G=new WeakSet,ke=function(c,t){return(e,n)=>{const s={...n},r={...this.defaults,...s};this.defaults.async===!0&&s.async===!1&&(r.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),r.async=!0);const i=te(this,ee,it).call(this,!!r.silent,!!r.async);if(e==null)return i(new Error("marked(): input parameter is undefined or null"));if(typeof e!="string")return i(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(e)+", string expected"));if(r.hooks&&(r.hooks.options=r),r.async)return Promise.resolve(r.hooks?r.hooks.preprocess(e):e).then(l=>c(l,r)).then(l=>r.hooks?r.hooks.processAllTokens(l):l).then(l=>r.walkTokens?Promise.all(this.walkTokens(l,r.walkTokens)).then(()=>l):l).then(l=>t(l,r)).then(l=>r.hooks?r.hooks.postprocess(l):l).catch(i);try{r.hooks&&(e=r.hooks.preprocess(e));let l=c(e,r);r.hooks&&(l=r.hooks.processAllTokens(l)),r.walkTokens&&this.walkTokens(l,r.walkTokens);let h=t(l,r);return r.hooks&&(h=r.hooks.postprocess(h)),h}catch(l){return i(l)}}},ee=new WeakSet,it=function(c,t){return e=>{if(e.message+=`
Please report this to https://github.com/markedjs/marked.`,c){const n="<p>An error occurred:</p><pre>"+_(e.message+"",!0)+"</pre>";return t?Promise.resolve(n):n}if(t)return Promise.reject(e);throw e}},Ne);function g(c,t){return L.parse(c,t)}g.options=g.setOptions=function(c){return L.setOptions(c),g.defaults=L.defaults,de(g.defaults),g},g.getDefaults=$,g.defaults=M,g.use=function(...c){return L.use(...c),g.defaults=L.defaults,de(g.defaults),g},g.walkTokens=function(c,t){return L.walkTokens(c,t)},g.parseInline=L.parseInline,g.Parser=A,g.parser=A.parse,g.Renderer=K,g.TextRenderer=he,g.Lexer=R,g.lexer=R.lex,g.Tokenizer=X,g.Hooks=V,g.parse=g,g.options,g.setOptions,g.use,g.walkTokens,g.parseInline,A.parse,R.lex;let Ie,Ce,j,Ee,qe,Me,Le,He,Ze,Pe,De,Be,Qe,Oe,Ve,je,Ge,Ue;Ie=["innerHTML"],Ce=Xe({__name:"index",props:{content:{type:String,required:!0}},setup(c){const t=c,e=We(),{copy:n}=Ye(),s=ne(),r=ne(),{content:i}=Je(t),l={code(o,a,u){let p;try{p=Yt.highlight(o,{language:a,ignoreIllegals:!0}).value}catch{}return`<pre style="position: relative;">${`<div id="copy" data-copy='${o}' style="position: absolute; right: 10px; top: 5px; color: #fff;cursor: pointer;">\u590D\u5236</div>`}<code class="hljs">${p}</code></pre>`}};g.use({renderer:l}),Et(i,async(o,a)=>{await h(o)});const h=async o=>{r.value=await g(o)};return Ke(async()=>{await h(t.content),s.value.addEventListener("click",o=>{var a,u;o.target.id==="copy"&&(n((u=(a=o.target)==null?void 0:a.dataset)==null?void 0:u.copy),e.success("\u590D\u5236\u6210\u529F!"))})}),(o,a)=>(q(),P("div",{ref_key:"contentRef",ref:s,class:"markdown-view",innerHTML:I(r)},null,8,Ie))}}),j=c=>(Ot("data-v-63b42ba6"),c=c(),Vt(),c),Ee={key:0,class:"left-message message-item"},qe={class:"avatar"},Me={class:"message"},Le={class:"left-btns"},He=j(()=>w("img",{class:"btn-image",src:b},null,-1)),Ze=j(()=>w("img",{class:"btn-image h-17px",src:x},null,-1)),Pe={key:1,class:"right-message message-item"},De={class:"avatar"},Be={class:"message"},Qe={class:"right-text-container"},Oe={class:"right-text"},Ve={class:"right-btns"},je=j(()=>w("img",{class:"btn-image",src:b},null,-1)),Ge=j(()=>w("img",{class:"btn-image h-17px mr-12px",src:x},null,-1)),Ue=Xe({__name:"MessageList",props:{conversation:{type:Object,required:!0},list:{type:Array,required:!0}},emits:["onDeleteSuccess","onRefresh","onEdit"],setup(c,{expose:t,emit:e}){const n=We(),{copy:s}=Ye(),r=qt(),i=ne(null),l=ne(!1),h=et(()=>r.user.avatar??tn),o=et(()=>a.conversation.roleAvatar??nn),a=c,{list:u}=Je(a),p=e;function k(){const m=i.value,U=m.scrollTop,H=m.scrollHeight,Z=m.offsetHeight;l.value=U+Z<H-100}const S=async()=>{const m=i.value;m.scrollTop=m.scrollHeight};t({scrollToBottom:async m=>{await Dt(),!m&&l.value||(i.value.scrollTop=i.value.scrollHeight-i.value.offsetHeight)},handlerGoTop:async()=>{i.value.scrollTop=0}});const E=async m=>{await s(m),n.success("\u590D\u5236\u6210\u529F\uFF01")},v=async m=>{await Kt.deleteChatMessage(m),n.success("\u5220\u9664\u6210\u529F\uFF01"),p("onDeleteSuccess")};return Ke(async()=>{i.value.addEventListener("scroll",k)}),(m,U)=>{const H=Ft,Z=Ut,T=Bt,Fe=Qt;return q(),P(tt,null,[w("div",{ref_key:"messageContainer",ref:i,class:"h-100% overflow-y-auto relative"},[(q(!0),P(tt,null,Mt(I(u),(z,At)=>(q(),P("div",{class:"chat-list",key:At},[z.type!=="user"?(q(),P("div",Ee,[w("div",qe,[y(H,{src:I(o)},null,8,["src"])]),w("div",Me,[w("div",null,[y(Z,{class:"time"},{default:C(()=>[nt(ge(I(st)(z.createTime)),1)]),_:2},1024)]),w("div",{class:"left-text-container",ref_for:!0,ref:"markdownViewRef"},[y(Ce,{class:"left-text",content:z.content},null,8,["content"])],512),w("div",Le,[y(T,{class:"btn-cus",link:"",onClick:N=>E(z.content)},{default:C(()=>[He]),_:2},1032,["onClick"]),z.id>0?(q(),Lt(T,{key:0,class:"btn-cus",link:"",onClick:N=>v(z.id)},{default:C(()=>[Ze]),_:2},1032,["onClick"])):se("",!0)])])])):se("",!0),z.type==="user"?(q(),P("div",Pe,[w("div",De,[y(H,{src:I(h)},null,8,["src"])]),w("div",Be,[w("div",null,[y(Z,{class:"time"},{default:C(()=>[nt(ge(I(st)(z.createTime)),1)]),_:2},1024)]),w("div",Qe,[w("div",Oe,ge(z.content),1)]),w("div",Ve,[y(T,{class:"btn-cus",link:"",onClick:N=>E(z.content)},{default:C(()=>[je]),_:2},1032,["onClick"]),y(T,{class:"btn-cus",link:"",onClick:N=>v(z.id)},{default:C(()=>[Ge]),_:2},1032,["onClick"]),y(T,{class:"btn-cus",link:"",onClick:N=>(async pe=>{p("onRefresh",pe)})(z)},{default:C(()=>[y(Fe,{size:"17"},{default:C(()=>[y(I(Ht))]),_:1})]),_:2},1032,["onClick"]),y(T,{class:"btn-cus",link:"",onClick:N=>(async pe=>{p("onEdit",pe)})(z)},{default:C(()=>[y(Fe,{size:"17"},{default:C(()=>[y(I(Zt))]),_:1})]),_:2},1032,["onClick"])])])])):se("",!0)]))),128))],512),I(l)?(q(),P("div",{key:0,class:"to-bottom",onClick:S},[y(T,{icon:I(Pt),circle:""},null,8,["icon"])])):se("",!0)],64)}}}),rt=jt(Ue,[["__scopeId","data-v-63b42ba6"]])});export{sn as __tla,rt as default};
