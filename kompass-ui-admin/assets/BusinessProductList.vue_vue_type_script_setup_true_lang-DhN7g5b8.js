import{_ as w,__tla as b}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{d as h,o as P,l as g,w as e,i as t,j as o,t as i,a as r,G as y,dV as l,dX as p,P as x,Q as v,E as N,s as U,__tla as j}from"./index-BUSn51wb.js";import{_ as C,__tla as R}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";let c,T=Promise.all([(()=>{try{return b}catch{}})(),(()=>{try{return j}catch{}})(),(()=>{try{return R}catch{}})()]).then(async()=>{c=h({__name:"BusinessProductList",props:{business:{}},setup:B=>(s,D)=>{const a=x,d=C,_=v,u=N,m=U,f=w;return P(),g(f,null,{default:e(()=>[t(_,{data:s.business.products,stripe:!0,"show-overflow-tooltip":!0},{default:e(()=>[t(a,{align:"center",label:"\u4EA7\u54C1\u540D\u79F0",fixed:"left",prop:"productName","min-width":"160"},{default:e(n=>[o(i(n.row.productName),1)]),_:1}),t(a,{label:"\u4EA7\u54C1\u6761\u7801",align:"center",prop:"productNo","min-width":"120"}),t(a,{align:"center",label:"\u4EA7\u54C1\u5355\u4F4D",prop:"productUnit","min-width":"160"},{default:e(({row:n})=>[t(d,{type:r(y).CRM_PRODUCT_UNIT,value:n.productUnit},null,8,["type","value"])]),_:1}),t(a,{label:"\u4EA7\u54C1\u4EF7\u683C\uFF08\u5143\uFF09",align:"center",prop:"productPrice","min-width":"140",formatter:r(l)},null,8,["formatter"]),t(a,{label:"\u5546\u673A\u4EF7\u683C\uFF08\u5143\uFF09",align:"center",prop:"businessPrice","min-width":"140",formatter:r(l)},null,8,["formatter"]),t(a,{align:"center",label:"\u6570\u91CF",prop:"count","min-width":"100px",formatter:r(l)},null,8,["formatter"]),t(a,{label:"\u5408\u8BA1\u91D1\u989D\uFF08\u5143\uFF09",align:"center",prop:"totalPrice","min-width":"140",formatter:r(l)},null,8,["formatter"])]),_:1},8,["data"]),t(m,{class:"mt-10px",justify:"end"},{default:e(()=>[t(u,{span:3},{default:e(()=>[o(" \u6574\u5355\u6298\u6263\uFF1A"+i(r(p)(s.business.discountPercent))+"% ",1)]),_:1}),t(u,{span:4},{default:e(()=>[o(" \u4EA7\u54C1\u603B\u91D1\u989D\uFF1A"+i(r(p)(s.business.totalProductPrice))+" \u5143 ",1)]),_:1})]),_:1})]),_:1})}})});export{c as _,T as __tla};
