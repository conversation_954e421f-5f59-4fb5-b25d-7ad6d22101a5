import{by as m,d as F,n as j,I as C,r as p,f as H,o as I,l as b,w as u,i as t,a,j as U,H as M,y as N,Z as O,L as Z,M as z,O as B,N as E,R as G,__tla as J}from"./index-BUSn51wb.js";import{_ as Q,__tla as T}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";let c,S,W=Promise.all([(()=>{try{return J}catch{}})(),(()=>{try{return T}catch{}})()]).then(async()=>{c={getAgreementSignaturePage:async d=>await m.get({url:"/als/agreement-signature/page",params:d}),getAgreementSignature:async d=>await m.get({url:"/als/agreement-signature/get?id="+d}),createAgreementSignature:async d=>await m.post({url:"/als/agreement-signature/create",data:d}),updateAgreementSignature:async d=>await m.put({url:"/als/agreement-signature/update",data:d}),deleteAgreementSignature:async d=>await m.delete({url:"/als/agreement-signature/delete?id="+d}),exportAgreementSignature:async d=>await m.download({url:"/als/agreement-signature/export-excel",params:d})},S=F({name:"AgreementSignatureForm",__name:"AgreementSignatureForm",emits:["success"],setup(d,{expose:w,emit:h}){const{t:v}=j(),_=C(),i=p(!1),A=p(""),g=p(!1),y=p(""),l=p({agreementSignatureId:void 0,agreementId:void 0,agreementKey:void 0,agreementVersion:void 0,userId:void 0,signatureUrl:void 0,ipAddress:void 0,userAgent:void 0,signedAt:void 0}),x=H({agreementId:[{required:!0,message:"\u534F\u8BAEID\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],agreementKey:[{required:!0,message:"\u534F\u8BAE\u552F\u4E00\u6807\u8BC6\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],agreementVersion:[{required:!0,message:"\u7B7E\u7F72\u65F6\u7684\u534F\u8BAE\u7248\u672C\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],userId:[{required:!0,message:"\u7528\u6237ID\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],signatureUrl:[{required:!0,message:"\u7B7E\u540D\u56FE\u7247URL\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],signedAt:[{required:!0,message:"\u7B7E\u7F72\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),V=p();w({open:async(s,e)=>{if(i.value=!0,A.value=v("action."+s),y.value=s,K(),e){g.value=!0;try{l.value=await c.getAgreementSignature(e)}finally{g.value=!1}}}});const q=h,D=async()=>{await V.value.validate(),g.value=!0;try{const s=l.value;y.value==="create"?(await c.createAgreementSignature(s),_.success(v("common.createSuccess"))):(await c.updateAgreementSignature(s),_.success(v("common.updateSuccess"))),i.value=!1,q("success")}finally{g.value=!1}},K=()=>{var s;l.value={agreementSignatureId:void 0,agreementId:void 0,agreementKey:void 0,agreementVersion:void 0,userId:void 0,signatureUrl:void 0,ipAddress:void 0,userAgent:void 0,signedAt:void 0},(s=V.value)==null||s.resetFields()};return(s,e)=>{const o=O,n=Z,R=z,L=B,f=E,k=Q,P=G;return I(),b(k,{title:a(A),modelValue:a(i),"onUpdate:modelValue":e[9]||(e[9]=r=>N(i)?i.value=r:null)},{footer:u(()=>[t(f,{onClick:D,type:"primary",disabled:a(g)},{default:u(()=>[U("\u786E \u5B9A")]),_:1},8,["disabled"]),t(f,{onClick:e[8]||(e[8]=r=>i.value=!1)},{default:u(()=>[U("\u53D6 \u6D88")]),_:1})]),default:u(()=>[M((I(),b(L,{ref_key:"formRef",ref:V,model:a(l),rules:a(x),"label-width":"100px"},{default:u(()=>[t(n,{label:"\u534F\u8BAEID",prop:"agreementId"},{default:u(()=>[t(o,{modelValue:a(l).agreementId,"onUpdate:modelValue":e[0]||(e[0]=r=>a(l).agreementId=r),placeholder:"\u8BF7\u8F93\u5165\u534F\u8BAEID"},null,8,["modelValue"])]),_:1}),t(n,{label:"\u534F\u8BAE\u552F\u4E00\u6807\u8BC6",prop:"agreementKey"},{default:u(()=>[t(o,{modelValue:a(l).agreementKey,"onUpdate:modelValue":e[1]||(e[1]=r=>a(l).agreementKey=r),placeholder:"\u8BF7\u8F93\u5165\u534F\u8BAE\u552F\u4E00\u6807\u8BC6"},null,8,["modelValue"])]),_:1}),t(n,{label:"\u7B7E\u7F72\u65F6\u7684\u534F\u8BAE\u7248\u672C",prop:"agreementVersion"},{default:u(()=>[t(o,{modelValue:a(l).agreementVersion,"onUpdate:modelValue":e[2]||(e[2]=r=>a(l).agreementVersion=r),placeholder:"\u8BF7\u8F93\u5165\u7B7E\u7F72\u65F6\u7684\u534F\u8BAE\u7248\u672C"},null,8,["modelValue"])]),_:1}),t(n,{label:"\u7528\u6237ID",prop:"userId"},{default:u(()=>[t(o,{modelValue:a(l).userId,"onUpdate:modelValue":e[3]||(e[3]=r=>a(l).userId=r),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237ID"},null,8,["modelValue"])]),_:1}),t(n,{label:"\u7B7E\u540D\u56FE\u7247URL",prop:"signatureUrl"},{default:u(()=>[t(o,{modelValue:a(l).signatureUrl,"onUpdate:modelValue":e[4]||(e[4]=r=>a(l).signatureUrl=r),placeholder:"\u8BF7\u8F93\u5165\u7B7E\u540D\u56FE\u7247URL"},null,8,["modelValue"])]),_:1}),t(n,{label:"\u7B7E\u7F72IP\u5730\u5740",prop:"ipAddress"},{default:u(()=>[t(o,{modelValue:a(l).ipAddress,"onUpdate:modelValue":e[5]||(e[5]=r=>a(l).ipAddress=r),placeholder:"\u8BF7\u8F93\u5165\u7B7E\u7F72IP\u5730\u5740"},null,8,["modelValue"])]),_:1}),t(n,{label:"\u7528\u6237\u4EE3\u7406\u4FE1\u606F",prop:"userAgent"},{default:u(()=>[t(o,{modelValue:a(l).userAgent,"onUpdate:modelValue":e[6]||(e[6]=r=>a(l).userAgent=r),placeholder:"\u8BF7\u8F93\u5165\u7528\u6237\u4EE3\u7406\u4FE1\u606F"},null,8,["modelValue"])]),_:1}),t(n,{label:"\u7B7E\u7F72\u65F6\u95F4",prop:"signedAt"},{default:u(()=>[t(R,{modelValue:a(l).signedAt,"onUpdate:modelValue":e[7]||(e[7]=r=>a(l).signedAt=r),type:"date","value-format":"x",placeholder:"\u9009\u62E9\u7B7E\u7F72\u65F6\u95F4"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[P,a(g)]])]),_:1},8,["title","modelValue"])}}})});export{c as A,S as _,W as __tla};
