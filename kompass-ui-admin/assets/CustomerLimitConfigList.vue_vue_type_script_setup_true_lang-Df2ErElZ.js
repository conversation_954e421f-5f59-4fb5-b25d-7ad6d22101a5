import{d as M,I as O,n as P,r as f,f as z,C as Q,T as F,o as c,c as G,i as e,w as o,j as d,H as g,l as _,a as t,G as Y,a9 as B,F as H,_ as q,N as D,P as J,Q as K,R as V,__tla as W}from"./index-BUSn51wb.js";import{_ as X,__tla as Z}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as $,__tla as aa}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{d as ea,__tla as ta}from"./formatTime-DWdBpgsM.js";import{L as S,_ as la,g as ra,d as na,__tla as ia}from"./CustomerLimitConfigForm.vue_vue_type_script_setup_true_lang-qqr48voj.js";let v,oa=Promise.all([(()=>{try{return W}catch{}})(),(()=>{try{return Z}catch{}})(),(()=>{try{return aa}catch{}})(),(()=>{try{return ta}catch{}})(),(()=>{try{return ia}catch{}})()]).then(async()=>{v=M({name:"CustomerLimitConfigList",__name:"CustomerLimitConfigList",props:{confType:{}},setup(L){const T=O(),{t:U}=P(),{confType:h}=L,y=f(!0),w=f(0),k=f([]),s=z({pageNo:1,pageSize:10,type:h}),p=async()=>{y.value=!0;try{const r=await ra(s);k.value=r.list,w.value=r.total}finally{y.value=!1}},b=f(),N=(r,l)=>{b.value.open(r,h,l)},x=()=>{s.pageNo=1,p()};return Q(()=>{p()}),(r,l)=>{const I=q,u=D,n=J,R=$,E=K,j=X,C=F("hasPermi"),A=V;return c(),G(H,null,[e(u,{plain:"",onClick:x},{default:o(()=>[e(I,{icon:"ep:refresh",class:"mr-5px"}),d(" \u5237\u65B0 ")]),_:1}),g((c(),_(u,{type:"primary",plain:"",onClick:l[0]||(l[0]=a=>N("create"))},{default:o(()=>[e(I,{icon:"ep:plus",class:"mr-5px"}),d(" \u65B0\u589E ")]),_:1})),[[C,["crm:customer-limit-config:create"]]]),g((c(),_(E,{data:t(k),stripe:!0,"show-overflow-tooltip":!0,class:"mt-4"},{default:o(()=>[e(n,{label:"\u7F16\u53F7",align:"center",prop:"id"}),e(n,{label:"\u89C4\u5219\u9002\u7528\u4EBA\u7FA4",align:"center",formatter:a=>{var i;return(i=a.users)==null?void 0:i.map(m=>m.nickname).join("\uFF0C")}},null,8,["formatter"]),e(n,{label:"\u89C4\u5219\u9002\u7528\u90E8\u95E8",align:"center",formatter:a=>{var i;return(i=a.depts)==null?void 0:i.map(m=>m.name).join("\uFF0C")}},null,8,["formatter"]),e(n,{label:r.confType===t(S).CUSTOMER_QUANTITY_LIMIT?"\u62E5\u6709\u5BA2\u6237\u6570\u4E0A\u9650":"\u9501\u5B9A\u5BA2\u6237\u6570\u4E0A\u9650",align:"center",prop:"maxCount"},null,8,["label"]),r.confType===t(S).CUSTOMER_QUANTITY_LIMIT?(c(),_(n,{key:0,label:"\u6210\u4EA4\u5BA2\u6237\u662F\u5426\u5360\u7528\u62E5\u6709\u5BA2\u6237\u6570",align:"center",prop:"dealCountEnabled","min-width":"100"},{default:o(a=>[e(R,{type:t(Y).INFRA_BOOLEAN_STRING,value:a.row.dealCountEnabled},null,8,["type","value"])]),_:1})):B("",!0),e(n,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:t(ea),width:"180px"},null,8,["formatter"]),e(n,{label:"\u64CD\u4F5C",align:"center","min-width":"110",fixed:"right"},{default:o(a=>[g((c(),_(u,{link:"",type:"primary",onClick:i=>N("update",a.row.id)},{default:o(()=>[d(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[C,["crm:customer-limit-config:update"]]]),g((c(),_(u,{link:"",type:"danger",onClick:i=>(async m=>{try{await T.delConfirm(),await na(m),T.success(U("common.delSuccess")),await p()}catch{}})(a.row.id)},{default:o(()=>[d(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[C,["crm:customer-limit-config:delete"]]])]),_:1})]),_:1},8,["data"])),[[A,t(y)]]),e(j,{total:t(w),page:t(s).pageNo,"onUpdate:page":l[1]||(l[1]=a=>t(s).pageNo=a),limit:t(s).pageSize,"onUpdate:limit":l[2]||(l[2]=a=>t(s).pageSize=a),onPagination:p},null,8,["total","page","limit"]),e(la,{ref_key:"formRef",ref:b,onSuccess:p},null,512)],64)}}})});export{v as _,oa as __tla};
