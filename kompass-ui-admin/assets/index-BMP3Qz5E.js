import{d as B,I as W,n as X,r as s,f as $,C as aa,T as ta,o as n,c as x,i as a,w as l,a as e,U as Y,F as C,k as D,l as c,V as ea,G as R,j as u,H as w,t as la,aJ as ra,J as oa,K as ia,L as na,Z as sa,M as _a,_ as ca,N as ua,O as pa,P as ma,Q as da,R as fa,__tla as ya}from"./index-BUSn51wb.js";import{_ as ha,__tla as wa}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{_ as ga,__tla as ba}from"./DictTag.vue_vue_type_script_lang-BnD52MNX.js";import{E as va,__tla as ka}from"./el-image-BjHZRFih.js";import{_ as xa,__tla as Ca}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{_ as Va,__tla as Ua}from"./index-COobLwz-.js";import{d as Ta,__tla as Sa}from"./formatTime-DWdBpgsM.js";import{a as Ia,d as Ma,__tla as Na}from"./index-CvQPhDtJ.js";import{_ as Oa,__tla as Pa}from"./ArticleForm.vue_vue_type_script_setup_true_lang-CSvKWM5O.js";import{g as Ya,__tla as Da}from"./index-CxVPlatM.js";import{i as Ra,__tla as za}from"./spu-CW3JGweV.js";import{__tla as Aa}from"./index-Cch5e1V0.js";import"./color-BN7ZL7BD.js";import{__tla as Fa}from"./el-card-CJbXGyyg.js";import{__tla as Ha}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as Ka}from"./SpuSelect.vue_vue_type_script_setup_true_lang-Q9Ka9uY_.js";import{__tla as La}from"./el-tree-select-CBuha0HW.js";import{__tla as Ga}from"./index-CjyLHUq3.js";import{__tla as Ja}from"./SkuList-DG93D6KA.js";import"./tree-BMa075Oj.js";import{__tla as ja}from"./category-WzWM3ODe.js";let z,qa=Promise.all([(()=>{try{return ya}catch{}})(),(()=>{try{return wa}catch{}})(),(()=>{try{return ba}catch{}})(),(()=>{try{return ka}catch{}})(),(()=>{try{return Ca}catch{}})(),(()=>{try{return Ua}catch{}})(),(()=>{try{return Sa}catch{}})(),(()=>{try{return Na}catch{}})(),(()=>{try{return Pa}catch{}})(),(()=>{try{return Da}catch{}})(),(()=>{try{return za}catch{}})(),(()=>{try{return Aa}catch{}})(),(()=>{try{return Fa}catch{}})(),(()=>{try{return Ha}catch{}})(),(()=>{try{return Ka}catch{}})(),(()=>{try{return La}catch{}})(),(()=>{try{return Ga}catch{}})(),(()=>{try{return Ja}catch{}})(),(()=>{try{return ja}catch{}})()]).then(async()=>{z=B({name:"PromotionArticle",__name:"index",setup(Ea){const V=W(),{t:A}=X(),g=s(!0),U=s(0),T=s([]),o=$({pageNo:1,pageSize:10,categoryId:void 0,title:null,status:void 0,spuId:void 0,createTime:[]}),S=s(),p=async()=>{g.value=!0;try{const m=await Ia(o);T.value=m.list,U.value=m.total}finally{g.value=!1}},h=()=>{o.pageNo=1,p()},F=()=>{S.value.resetFields(),h()},I=s(),M=(m,r)=>{I.value.open(m,r)},b=s([]),H=s([]);return aa(async()=>{await p(),b.value=await Ya(),H.value=await Ra()}),(m,r)=>{const K=Va,N=oa,O=ia,d=na,L=sa,G=_a,v=ca,f=ua,J=pa,P=xa,j=va,i=ma,q=ga,E=da,Q=ha,k=ta("hasPermi"),Z=fa;return n(),x(C,null,[a(K,{title:"\u3010\u8425\u9500\u3011\u5185\u5BB9\u7BA1\u7406",url:"https://doc.iocoder.cn/mall/promotion-content/"}),a(P,null,{default:l(()=>[a(J,{ref_key:"queryFormRef",ref:S,inline:!0,model:e(o),class:"-mb-15px","label-width":"80px"},{default:l(()=>[a(d,{label:"\u6587\u7AE0\u5206\u7C7B",prop:"categoryId"},{default:l(()=>[a(O,{modelValue:e(o).categoryId,"onUpdate:modelValue":r[0]||(r[0]=t=>e(o).categoryId=t),class:"!w-240px",placeholder:"\u5168\u90E8",onKeyup:Y(h,["enter"])},{default:l(()=>[(n(!0),x(C,null,D(e(b),t=>(n(),c(N,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(d,{label:"\u6587\u7AE0\u6807\u9898",prop:"title"},{default:l(()=>[a(L,{modelValue:e(o).title,"onUpdate:modelValue":r[1]||(r[1]=t=>e(o).title=t),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u6587\u7AE0\u6807\u9898",onKeyup:Y(h,["enter"])},null,8,["modelValue"])]),_:1}),a(d,{label:"\u72B6\u6001",prop:"status"},{default:l(()=>[a(O,{modelValue:e(o).status,"onUpdate:modelValue":r[2]||(r[2]=t=>e(o).status=t),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001"},{default:l(()=>[(n(!0),x(C,null,D(e(ea)(e(R).COMMON_STATUS),t=>(n(),c(N,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(d,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:l(()=>[a(G,{modelValue:e(o).createTime,"onUpdate:modelValue":r[3]||(r[3]=t=>e(o).createTime=t),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px","end-placeholder":"\u7ED3\u675F\u65E5\u671F","start-placeholder":"\u5F00\u59CB\u65E5\u671F",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),a(d,null,{default:l(()=>[a(f,{onClick:h},{default:l(()=>[a(v,{class:"mr-5px",icon:"ep:search"}),u(" \u641C\u7D22 ")]),_:1}),a(f,{onClick:F},{default:l(()=>[a(v,{class:"mr-5px",icon:"ep:refresh"}),u(" \u91CD\u7F6E ")]),_:1}),w((n(),c(f,{plain:"",type:"primary",onClick:r[4]||(r[4]=t=>M("create"))},{default:l(()=>[a(v,{class:"mr-5px",icon:"ep:plus"}),u(" \u65B0\u589E ")]),_:1})),[[k,["promotion:article:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(P,null,{default:l(()=>[w((n(),c(E,{data:e(T),"show-overflow-tooltip":!0,stripe:!0},{default:l(()=>[a(i,{align:"center",label:"\u5C01\u9762","min-width":"80",prop:"picUrl"},{default:l(({row:t})=>[a(j,{src:t.picUrl,class:"h-30px w-30px",onClick:y=>{return _=t.picUrl,void ra({urlList:[_]});var _}},null,8,["src","onClick"])]),_:1}),a(i,{align:"center",label:"\u6807\u9898","min-width":"180",prop:"title"}),a(i,{align:"center",label:"\u5206\u7C7B","min-width":"180",prop:"categoryId"},{default:l(t=>{var y;return[u(la((y=e(b).find(_=>_.id===t.row.categoryId))==null?void 0:y.name),1)]}),_:1}),a(i,{align:"center",label:"\u6D4F\u89C8\u91CF","min-width":"180",prop:"browseCount"}),a(i,{align:"center",label:"\u4F5C\u8005","min-width":"180",prop:"author"}),a(i,{align:"center",label:"\u6587\u7AE0\u7B80\u4ECB","min-width":"250",prop:"introduction"}),a(i,{align:"center",label:"\u6392\u5E8F","min-width":"60",prop:"sort"}),a(i,{align:"center",label:"\u72B6\u6001","min-width":"60",prop:"status"},{default:l(t=>[a(q,{type:e(R).COMMON_STATUS,value:t.row.status},null,8,["type","value"])]),_:1}),a(i,{formatter:e(Ta),align:"center",label:"\u53D1\u5E03\u65F6\u95F4",prop:"createTime",width:"180px"},null,8,["formatter"]),a(i,{align:"center",fixed:"right",label:"\u64CD\u4F5C",width:"120"},{default:l(t=>[w((n(),c(f,{link:"",type:"primary",onClick:y=>M("update",t.row.id)},{default:l(()=>[u(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[k,["promotion:article:update"]]]),w((n(),c(f,{link:"",type:"danger",onClick:y=>(async _=>{try{await V.delConfirm(),await Ma(_),V.success(A("common.delSuccess")),await p()}catch{}})(t.row.id)},{default:l(()=>[u(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[k,["promotion:article:delete"]]])]),_:1})]),_:1},8,["data"])),[[Z,e(g)]]),a(Q,{limit:e(o).pageSize,"onUpdate:limit":r[5]||(r[5]=t=>e(o).pageSize=t),page:e(o).pageNo,"onUpdate:page":r[6]||(r[6]=t=>e(o).pageNo=t),total:e(U),onPagination:p},null,8,["limit","page","total"])]),_:1}),a(Oa,{ref_key:"formRef",ref:I,onSuccess:p},null,512)],64)}}})});export{qa as __tla,z as default};
