import{by as i,d as P,n as H,I as L,r as m,f as N,o as A,l as b,w as s,i as n,a,j as h,H as O,y as Z,Z as z,L as B,ck as D,O as E,N as G,R as J,__tla as M}from"./index-BUSn51wb.js";import{_ as Q,__tla as T}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";let c,x,W=Promise.all([(()=>{try{return M}catch{}})(),(()=>{try{return T}catch{}})()]).then(async()=>{c={getAgreementPage:async t=>await i.get({url:"/als/agreement/page",params:t}),getAgreement:async t=>await i.get({url:"/als/agreement/get?id="+t}),createAgreement:async t=>await i.post({url:"/als/agreement/create",data:t}),updateAgreement:async t=>await i.put({url:"/als/agreement/update",data:t}),deleteAgreement:async t=>await i.delete({url:"/als/agreement/delete?id="+t}),exportAgreement:async t=>await i.download({url:"/als/agreement/export-excel",params:t})},x=P({name:"AgreementForm",__name:"AgreementForm",emits:["success"],setup(t,{expose:K,emit:U}){const{t:p}=H(),_=L(),u=m(!1),f=m(""),d=m(!1),V=m(""),l=m({agreementId:void 0,unionKey:void 0,title:void 0,content:void 0,version:void 0}),k=N({unionKey:[{required:!0,message:"\u534F\u8BAE\u552F\u4E00\u6807\u8BC6\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],title:[{required:!0,message:"\u534F\u8BAE\u6807\u9898\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],content:[{required:!0,message:"\u534F\u8BAE\u5185\u5BB9\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],version:[{required:!0,message:"\u534F\u8BAE\u7248\u672C\u53F7\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),v=m();K({open:async(o,e)=>{if(u.value=!0,f.value=p("action."+o),V.value=o,I(),e){d.value=!0;try{l.value=await c.getAgreement(e)}finally{d.value=!1}}}});const q=U,F=async()=>{await v.value.validate(),d.value=!0;try{const o=l.value;V.value==="create"?(await c.createAgreement(o),_.success(p("common.createSuccess"))):(await c.updateAgreement(o),_.success(p("common.updateSuccess"))),u.value=!1,q("success")}finally{d.value=!1}},I=()=>{var o;l.value={agreementId:void 0,unionKey:void 0,title:void 0,content:void 0,version:void 0},(o=v.value)==null||o.resetFields()};return(o,e)=>{const y=z,g=B,C=D,R=E,w=G,S=Q,j=J;return A(),b(S,{title:a(f),modelValue:a(u),"onUpdate:modelValue":e[5]||(e[5]=r=>Z(u)?u.value=r:null)},{footer:s(()=>[n(w,{onClick:F,type:"primary",disabled:a(d)},{default:s(()=>[h("\u786E \u5B9A")]),_:1},8,["disabled"]),n(w,{onClick:e[4]||(e[4]=r=>u.value=!1)},{default:s(()=>[h("\u53D6 \u6D88")]),_:1})]),default:s(()=>[O((A(),b(R,{ref_key:"formRef",ref:v,model:a(l),rules:a(k),"label-width":"100px"},{default:s(()=>[n(g,{label:"\u534F\u8BAE\u552F\u4E00\u6807\u8BC6",prop:"unionKey"},{default:s(()=>[n(y,{modelValue:a(l).unionKey,"onUpdate:modelValue":e[0]||(e[0]=r=>a(l).unionKey=r),placeholder:"\u8BF7\u8F93\u5165\u534F\u8BAE\u552F\u4E00\u6807\u8BC6"},null,8,["modelValue"])]),_:1}),n(g,{label:"\u534F\u8BAE\u6807\u9898",prop:"title"},{default:s(()=>[n(y,{modelValue:a(l).title,"onUpdate:modelValue":e[1]||(e[1]=r=>a(l).title=r),placeholder:"\u8BF7\u8F93\u5165\u534F\u8BAE\u6807\u9898"},null,8,["modelValue"])]),_:1}),n(g,{label:"\u534F\u8BAE\u5185\u5BB9",prop:"content"},{default:s(()=>[n(C,{modelValue:a(l).content,"onUpdate:modelValue":e[2]||(e[2]=r=>a(l).content=r),height:"150px"},null,8,["modelValue"])]),_:1}),n(g,{label:"\u534F\u8BAE\u7248\u672C\u53F7",prop:"version"},{default:s(()=>[n(y,{modelValue:a(l).version,"onUpdate:modelValue":e[3]||(e[3]=r=>a(l).version=r),placeholder:"\u8BF7\u8F93\u5165\u534F\u8BAE\u7248\u672C\u53F7"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[j,a(d)]])]),_:1},8,["title","modelValue"])}}})});export{c as A,x as _,W as __tla};
