import{d as Ot,p as A,bs as ji,r as $,b as Ft,C as Pi,bt as Ui,a as v,o as q,c as pt,H as $i,a8 as qi,g as nt,av as ti,a0 as F,a4 as Qt,bu as Vi,bv as ei,i as B,w as S,y as Fi,j as ii,t as ai,l as _t,a9 as ut,aN as Qi,bw as Ki,N as ni,bx as Zi,at as Gi,I as Ji,B as ri,e as ta,__tla as ea}from"./index-BUSn51wb.js";import{b as ia,__tla as aa}from"./profile-BQCm_-PE.js";import{E as oi,__tla as na}from"./el-avatar-Da2TGjmj.js";import{_ as ra,__tla as oa}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{E as sa,__tla as ha}from"./el-space-Dxj8A-LJ.js";import{_ as ca,__tla as la}from"./XButton-BjahQbul.js";import{a as da}from"./avatar-BG6NdH5s.js";let si,pa=Promise.all([(()=>{try{return ea}catch{}})(),(()=>{try{return aa}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return oa}catch{}})(),(()=>{try{return ha}catch{}})(),(()=>{try{return la}catch{}})()]).then(async()=>{function Kt(t,i){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);i&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),e.push.apply(e,n)}return e}function Zt(t){for(var i=1;i<arguments.length;i++){var e=arguments[i]!=null?arguments[i]:{};i%2?Kt(Object(e),!0).forEach(function(n){hi(t,n,e[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):Kt(Object(e)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))})}return t}function Gt(t){var i=function(e,n){if(typeof e!="object"||!e)return e;var a=e[Symbol.toPrimitive];if(a!==void 0){var r=a.call(e,n||"default");if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(n==="string"?String:Number)(e)}(t,"string");return typeof i=="symbol"?i:i+""}function Tt(t){return Tt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(i){return typeof i}:function(i){return i&&typeof Symbol=="function"&&i.constructor===Symbol&&i!==Symbol.prototype?"symbol":typeof i},Tt(t)}function Jt(t,i){for(var e=0;e<i.length;e++){var n=i[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Gt(n.key),n)}}function hi(t,i,e){return(i=Gt(i))in t?Object.defineProperty(t,i,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[i]=e,t}function te(t){return function(i){if(Array.isArray(i))return Et(i)}(t)||function(i){if(typeof Symbol<"u"&&i[Symbol.iterator]!=null||i["@@iterator"]!=null)return Array.from(i)}(t)||function(i,e){if(i){if(typeof i=="string")return Et(i,e);var n=Object.prototype.toString.call(i).slice(8,-1);if(n==="Object"&&i.constructor&&(n=i.constructor.name),n==="Map"||n==="Set")return Array.from(i);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Et(i,e)}}(t)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function Et(t,i){(i==null||i>t.length)&&(i=t.length);for(var e=0,n=new Array(i);e<i;e++)n[e]=t[e];return n}var Mt=typeof window<"u"&&window.document!==void 0,P=Mt?window:{},zt=!(!Mt||!P.document.documentElement)&&"ontouchstart"in P.document.documentElement,Wt=!!Mt&&"PointerEvent"in P,O="cropper",Ht="all",ee="crop",ie="move",ae="zoom",J="e",tt="w",rt="s",Q="n",mt="ne",gt="nw",ft="se",vt="sw",Nt="".concat(O,"-crop"),ne="".concat(O,"-disabled"),R="".concat(O,"-hidden"),re="".concat(O,"-hide"),ci="".concat(O,"-invisible"),Ct="".concat(O,"-modal"),Rt="".concat(O,"-move"),bt="".concat(O,"Action"),Dt="".concat(O,"Preview"),Lt="crop",oe="move",se="none",St="crop",Yt="cropend",Xt="cropmove",At="cropstart",he="dblclick",ce=Wt?"pointerdown":zt?"touchstart":"mousedown",le=Wt?"pointermove":zt?"touchmove":"mousemove",de=Wt?"pointerup pointercancel":zt?"touchend touchcancel":"mouseup",pe="ready",ue="resize",me="wheel",It="zoom",ge="image/jpeg",li=/^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/,di=/^data:/,pi=/^data:image\/jpeg;base64,/,ui=/^img|canvas$/i,fe={viewMode:0,dragMode:Lt,initialAspectRatio:NaN,aspectRatio:NaN,data:null,preview:"",responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:.8,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,wheelZoomRatio:.1,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:200,minContainerHeight:100,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null},mi=Number.isNaN||P.isNaN;function x(t){return typeof t=="number"&&!mi(t)}var ve=function(t){return t>0&&t<1/0};function jt(t){return t===void 0}function et(t){return Tt(t)==="object"&&t!==null}var gi=Object.prototype.hasOwnProperty;function ot(t){if(!et(t))return!1;try{var i=t.constructor,e=i.prototype;return i&&e&&gi.call(e,"isPrototypeOf")}catch{return!1}}function L(t){return typeof t=="function"}var fi=Array.prototype.slice;function be(t){return Array.from?Array.from(t):fi.call(t)}function E(t,i){return t&&L(i)&&(Array.isArray(t)||x(t.length)?be(t).forEach(function(e,n){i.call(t,e,n,t)}):et(t)&&Object.keys(t).forEach(function(e){i.call(t,t[e],e,t)})),t}var _=Object.assign||function(t){for(var i=arguments.length,e=new Array(i>1?i-1:0),n=1;n<i;n++)e[n-1]=arguments[n];return et(t)&&e.length>0&&e.forEach(function(a){et(a)&&Object.keys(a).forEach(function(r){t[r]=a[r]})}),t},vi=/\.\d*(?:0|9){12}\d*$/;function st(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1e11;return vi.test(t)?Math.round(t*i)/i:t}var bi=/^width|height|left|top|marginLeft|marginTop$/;function K(t,i){var e=t.style;E(i,function(n,a){bi.test(a)&&x(n)&&(n="".concat(n,"px")),e[a]=n})}function z(t,i){if(i)if(x(t.length))E(t,function(n){z(n,i)});else if(t.classList)t.classList.add(i);else{var e=t.className.trim();e?e.indexOf(i)<0&&(t.className="".concat(e," ").concat(i)):t.className=i}}function U(t,i){i&&(x(t.length)?E(t,function(e){U(e,i)}):t.classList?t.classList.remove(i):t.className.indexOf(i)>=0&&(t.className=t.className.replace(i,"")))}function ht(t,i,e){i&&(x(t.length)?E(t,function(n){ht(n,i,e)}):e?z(t,i):U(t,i))}var wi=/([a-z\d])([A-Z])/g;function Pt(t){return t.replace(wi,"$1-$2").toLowerCase()}function Ut(t,i){return et(t[i])?t[i]:t.dataset?t.dataset[i]:t.getAttribute("data-".concat(Pt(i)))}function wt(t,i,e){et(e)?t[i]=e:t.dataset?t.dataset[i]=e:t.setAttribute("data-".concat(Pt(i)),e)}var we=/\s\s*/,ye=function(){var t=!1;if(Mt){var i=!1,e=function(){},n=Object.defineProperty({},"once",{get:function(){return t=!0,i},set:function(a){i=a}});P.addEventListener("test",e,n),P.removeEventListener("test",e,n)}return t}();function I(t,i,e){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},a=e;i.trim().split(we).forEach(function(r){if(!ye){var c=t.listeners;c&&c[r]&&c[r][e]&&(a=c[r][e],delete c[r][e],Object.keys(c[r]).length===0&&delete c[r],Object.keys(c).length===0&&delete t.listeners)}t.removeEventListener(r,a,n)})}function X(t,i,e){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},a=e;i.trim().split(we).forEach(function(r){if(n.once&&!ye){var c=t.listeners,s=c===void 0?{}:c;a=function(){delete s[r][e],t.removeEventListener(r,a,n);for(var h=arguments.length,o=new Array(h),u=0;u<h;u++)o[u]=arguments[u];e.apply(t,o)},s[r]||(s[r]={}),s[r][e]&&t.removeEventListener(r,s[r][e],n),s[r][e]=a,t.listeners=s}t.addEventListener(r,a,n)})}function ct(t,i,e){var n;return L(Event)&&L(CustomEvent)?n=new CustomEvent(i,{detail:e,bubbles:!0,cancelable:!0}):(n=document.createEvent("CustomEvent")).initCustomEvent(i,!0,!0,e),t.dispatchEvent(n)}function xe(t){var i=t.getBoundingClientRect();return{left:i.left+(window.pageXOffset-document.documentElement.clientLeft),top:i.top+(window.pageYOffset-document.documentElement.clientTop)}}var $t=P.location,yi=/^(\w+:)\/\/([^:/?#]*):?(\d*)/i;function Me(t){var i=t.match(yi);return i!==null&&(i[1]!==$t.protocol||i[2]!==$t.hostname||i[3]!==$t.port)}function Ce(t){var i="timestamp=".concat(new Date().getTime());return t+(t.indexOf("?")===-1?"?":"&")+i}function yt(t){var i=t.rotate,e=t.scaleX,n=t.scaleY,a=t.translateX,r=t.translateY,c=[];x(a)&&a!==0&&c.push("translateX(".concat(a,"px)")),x(r)&&r!==0&&c.push("translateY(".concat(r,"px)")),x(i)&&i!==0&&c.push("rotate(".concat(i,"deg)")),x(e)&&e!==1&&c.push("scaleX(".concat(e,")")),x(n)&&n!==1&&c.push("scaleY(".concat(n,")"));var s=c.length?c.join(" "):"none";return{WebkitTransform:s,msTransform:s,transform:s}}function kt(t,i){var e=t.pageX,n=t.pageY,a={endX:e,endY:n};return i?a:Zt({startX:e,startY:n},a)}function Z(t){var i=t.aspectRatio,e=t.height,n=t.width,a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"contain",r=ve(n),c=ve(e);if(r&&c){var s=e*i;a==="contain"&&s>n||a==="cover"&&s<n?e=n/i:n=e*i}else r?e=n/i:c&&(n=e*i);return{width:n,height:e}}var De=String.fromCharCode,xi=/^data:.*,/;function Mi(t){var i,e=new DataView(t);try{var n,a,r;if(e.getUint8(0)===255&&e.getUint8(1)===216)for(var c=e.byteLength,s=2;s+1<c;){if(e.getUint8(s)===255&&e.getUint8(s+1)===225){a=s;break}s+=1}if(a){var h=a+10;if(function(f,g,b){var C="";b+=g;for(var y=g;y<b;y+=1)C+=De(f.getUint8(y));return C}(e,a+4,4)==="Exif"){var o=e.getUint16(h);if(((n=o===18761)||o===19789)&&e.getUint16(h+2,n)===42){var u=e.getUint32(h+4,n);u>=8&&(r=h+u)}}}if(r){var p,l,m=e.getUint16(r,n);for(l=0;l<m;l+=1)if(p=r+12*l+2,e.getUint16(p,n)===274){p+=8,i=e.getUint16(p,n),e.setUint16(p,1,n);break}}}catch{i=1}return i}var Ci={render:function(){this.initContainer(),this.initCanvas(),this.initCropBox(),this.renderCanvas(),this.cropped&&this.renderCropBox()},initContainer:function(){var t=this.element,i=this.options,e=this.container,n=this.cropper,a=Number(i.minContainerWidth),r=Number(i.minContainerHeight);z(n,R),U(t,R);var c={width:Math.max(e.offsetWidth,a>=0?a:200),height:Math.max(e.offsetHeight,r>=0?r:100)};this.containerData=c,K(n,{width:c.width,height:c.height}),z(t,R),U(n,R)},initCanvas:function(){var t=this.containerData,i=this.imageData,e=this.options.viewMode,n=Math.abs(i.rotate)%180==90,a=n?i.naturalHeight:i.naturalWidth,r=n?i.naturalWidth:i.naturalHeight,c=a/r,s=t.width,h=t.height;t.height*c>t.width?e===3?s=t.height*c:h=t.width/c:e===3?h=t.width/c:s=t.height*c;var o={aspectRatio:c,naturalWidth:a,naturalHeight:r,width:s,height:h};this.canvasData=o,this.limited=e===1||e===2,this.limitCanvas(!0,!0),o.width=Math.min(Math.max(o.width,o.minWidth),o.maxWidth),o.height=Math.min(Math.max(o.height,o.minHeight),o.maxHeight),o.left=(t.width-o.width)/2,o.top=(t.height-o.height)/2,o.oldLeft=o.left,o.oldTop=o.top,this.initialCanvasData=_({},o)},limitCanvas:function(t,i){var e=this.options,n=this.containerData,a=this.canvasData,r=this.cropBoxData,c=e.viewMode,s=a.aspectRatio,h=this.cropped&&r;if(t){var o=Number(e.minCanvasWidth)||0,u=Number(e.minCanvasHeight)||0;c>1?(o=Math.max(o,n.width),u=Math.max(u,n.height),c===3&&(u*s>o?o=u*s:u=o/s)):c>0&&(o?o=Math.max(o,h?r.width:0):u?u=Math.max(u,h?r.height:0):h&&(o=r.width,(u=r.height)*s>o?o=u*s:u=o/s));var p=Z({aspectRatio:s,width:o,height:u});o=p.width,u=p.height,a.minWidth=o,a.minHeight=u,a.maxWidth=1/0,a.maxHeight=1/0}if(i)if(c>(h?0:1)){var l=n.width-a.width,m=n.height-a.height;a.minLeft=Math.min(0,l),a.minTop=Math.min(0,m),a.maxLeft=Math.max(0,l),a.maxTop=Math.max(0,m),h&&this.limited&&(a.minLeft=Math.min(r.left,r.left+(r.width-a.width)),a.minTop=Math.min(r.top,r.top+(r.height-a.height)),a.maxLeft=r.left,a.maxTop=r.top,c===2&&(a.width>=n.width&&(a.minLeft=Math.min(0,l),a.maxLeft=Math.max(0,l)),a.height>=n.height&&(a.minTop=Math.min(0,m),a.maxTop=Math.max(0,m))))}else a.minLeft=-a.width,a.minTop=-a.height,a.maxLeft=n.width,a.maxTop=n.height},renderCanvas:function(t,i){var e=this.canvasData,n=this.imageData;if(i){var a=function(o){var u=o.width,p=o.height,l=o.degree;if((l=Math.abs(l)%180)==90)return{width:p,height:u};var m=l%90*Math.PI/180,f=Math.sin(m),g=Math.cos(m),b=u*g+p*f,C=u*f+p*g;return l>90?{width:C,height:b}:{width:b,height:C}}({width:n.naturalWidth*Math.abs(n.scaleX||1),height:n.naturalHeight*Math.abs(n.scaleY||1),degree:n.rotate||0}),r=a.width,c=a.height,s=e.width*(r/e.naturalWidth),h=e.height*(c/e.naturalHeight);e.left-=(s-e.width)/2,e.top-=(h-e.height)/2,e.width=s,e.height=h,e.aspectRatio=r/c,e.naturalWidth=r,e.naturalHeight=c,this.limitCanvas(!0,!1)}(e.width>e.maxWidth||e.width<e.minWidth)&&(e.left=e.oldLeft),(e.height>e.maxHeight||e.height<e.minHeight)&&(e.top=e.oldTop),e.width=Math.min(Math.max(e.width,e.minWidth),e.maxWidth),e.height=Math.min(Math.max(e.height,e.minHeight),e.maxHeight),this.limitCanvas(!1,!0),e.left=Math.min(Math.max(e.left,e.minLeft),e.maxLeft),e.top=Math.min(Math.max(e.top,e.minTop),e.maxTop),e.oldLeft=e.left,e.oldTop=e.top,K(this.canvas,_({width:e.width,height:e.height},yt({translateX:e.left,translateY:e.top}))),this.renderImage(t),this.cropped&&this.limited&&this.limitCropBox(!0,!0)},renderImage:function(t){var i=this.canvasData,e=this.imageData,n=e.naturalWidth*(i.width/i.naturalWidth),a=e.naturalHeight*(i.height/i.naturalHeight);_(e,{width:n,height:a,left:(i.width-n)/2,top:(i.height-a)/2}),K(this.image,_({width:e.width,height:e.height},yt(_({translateX:e.left,translateY:e.top},e)))),t&&this.output()},initCropBox:function(){var t=this.options,i=this.canvasData,e=t.aspectRatio||t.initialAspectRatio,n=Number(t.autoCropArea)||.8,a={width:i.width,height:i.height};e&&(i.height*e>i.width?a.height=a.width/e:a.width=a.height*e),this.cropBoxData=a,this.limitCropBox(!0,!0),a.width=Math.min(Math.max(a.width,a.minWidth),a.maxWidth),a.height=Math.min(Math.max(a.height,a.minHeight),a.maxHeight),a.width=Math.max(a.minWidth,a.width*n),a.height=Math.max(a.minHeight,a.height*n),a.left=i.left+(i.width-a.width)/2,a.top=i.top+(i.height-a.height)/2,a.oldLeft=a.left,a.oldTop=a.top,this.initialCropBoxData=_({},a)},limitCropBox:function(t,i){var e=this.options,n=this.containerData,a=this.canvasData,r=this.cropBoxData,c=this.limited,s=e.aspectRatio;if(t){var h=Number(e.minCropBoxWidth)||0,o=Number(e.minCropBoxHeight)||0,u=c?Math.min(n.width,a.width,a.width+a.left,n.width-a.left):n.width,p=c?Math.min(n.height,a.height,a.height+a.top,n.height-a.top):n.height;h=Math.min(h,n.width),o=Math.min(o,n.height),s&&(h&&o?o*s>h?o=h/s:h=o*s:h?o=h/s:o&&(h=o*s),p*s>u?p=u/s:u=p*s),r.minWidth=Math.min(h,u),r.minHeight=Math.min(o,p),r.maxWidth=u,r.maxHeight=p}i&&(c?(r.minLeft=Math.max(0,a.left),r.minTop=Math.max(0,a.top),r.maxLeft=Math.min(n.width,a.left+a.width)-r.width,r.maxTop=Math.min(n.height,a.top+a.height)-r.height):(r.minLeft=0,r.minTop=0,r.maxLeft=n.width-r.width,r.maxTop=n.height-r.height))},renderCropBox:function(){var t=this.options,i=this.containerData,e=this.cropBoxData;(e.width>e.maxWidth||e.width<e.minWidth)&&(e.left=e.oldLeft),(e.height>e.maxHeight||e.height<e.minHeight)&&(e.top=e.oldTop),e.width=Math.min(Math.max(e.width,e.minWidth),e.maxWidth),e.height=Math.min(Math.max(e.height,e.minHeight),e.maxHeight),this.limitCropBox(!1,!0),e.left=Math.min(Math.max(e.left,e.minLeft),e.maxLeft),e.top=Math.min(Math.max(e.top,e.minTop),e.maxTop),e.oldLeft=e.left,e.oldTop=e.top,t.movable&&t.cropBoxMovable&&wt(this.face,bt,e.width>=i.width&&e.height>=i.height?ie:Ht),K(this.cropBox,_({width:e.width,height:e.height},yt({translateX:e.left,translateY:e.top}))),this.cropped&&this.limited&&this.limitCanvas(!0,!0),this.disabled||this.output()},output:function(){this.preview(),ct(this.element,St,this.getData())}},Di={initPreview:function(){var t=this.element,i=this.crossOrigin,e=this.options.preview,n=i?this.crossOriginUrl:this.url,a=t.alt||"The image to preview",r=document.createElement("img");if(i&&(r.crossOrigin=i),r.src=n,r.alt=a,this.viewBox.appendChild(r),this.viewBoxImage=r,e){var c=e;typeof e=="string"?c=t.ownerDocument.querySelectorAll(e):e.querySelector&&(c=[e]),this.previews=c,E(c,function(s){var h=document.createElement("img");wt(s,Dt,{width:s.offsetWidth,height:s.offsetHeight,html:s.innerHTML}),i&&(h.crossOrigin=i),h.src=n,h.alt=a,h.style.cssText='display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"',s.innerHTML="",s.appendChild(h)})}},resetPreview:function(){E(this.previews,function(t){var i=Ut(t,Dt);K(t,{width:i.width,height:i.height}),t.innerHTML=i.html,function(e,n){if(et(e[n]))try{delete e[n]}catch{e[n]=void 0}else if(e.dataset)try{delete e.dataset[n]}catch{e.dataset[n]=void 0}else e.removeAttribute("data-".concat(Pt(n)))}(t,Dt)})},preview:function(){var t=this.imageData,i=this.canvasData,e=this.cropBoxData,n=e.width,a=e.height,r=t.width,c=t.height,s=e.left-i.left-t.left,h=e.top-i.top-t.top;this.cropped&&!this.disabled&&(K(this.viewBoxImage,_({width:r,height:c},yt(_({translateX:-s,translateY:-h},t)))),E(this.previews,function(o){var u=Ut(o,Dt),p=u.width,l=u.height,m=p,f=l,g=1;n&&(f=a*(g=p/n)),a&&f>l&&(m=n*(g=l/a),f=l),K(o,{width:m,height:f}),K(o.getElementsByTagName("img")[0],_({width:r*g,height:c*g},yt(_({translateX:-s*g,translateY:-h*g},t))))}))}},ki={bind:function(){var t=this.element,i=this.options,e=this.cropper;L(i.cropstart)&&X(t,At,i.cropstart),L(i.cropmove)&&X(t,Xt,i.cropmove),L(i.cropend)&&X(t,Yt,i.cropend),L(i.crop)&&X(t,St,i.crop),L(i.zoom)&&X(t,It,i.zoom),X(e,ce,this.onCropStart=this.cropStart.bind(this)),i.zoomable&&i.zoomOnWheel&&X(e,me,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),i.toggleDragModeOnDblclick&&X(e,he,this.onDblclick=this.dblclick.bind(this)),X(t.ownerDocument,le,this.onCropMove=this.cropMove.bind(this)),X(t.ownerDocument,de,this.onCropEnd=this.cropEnd.bind(this)),i.responsive&&X(window,ue,this.onResize=this.resize.bind(this))},unbind:function(){var t=this.element,i=this.options,e=this.cropper;L(i.cropstart)&&I(t,At,i.cropstart),L(i.cropmove)&&I(t,Xt,i.cropmove),L(i.cropend)&&I(t,Yt,i.cropend),L(i.crop)&&I(t,St,i.crop),L(i.zoom)&&I(t,It,i.zoom),I(e,ce,this.onCropStart),i.zoomable&&i.zoomOnWheel&&I(e,me,this.onWheel,{passive:!1,capture:!0}),i.toggleDragModeOnDblclick&&I(e,he,this.onDblclick),I(t.ownerDocument,le,this.onCropMove),I(t.ownerDocument,de,this.onCropEnd),i.responsive&&I(window,ue,this.onResize)}},Bi={resize:function(){if(!this.disabled){var t,i,e=this.options,n=this.container,a=this.containerData,r=n.offsetWidth/a.width,c=n.offsetHeight/a.height,s=Math.abs(r-1)>Math.abs(c-1)?r:c;s!==1&&(e.restore&&(t=this.getCanvasData(),i=this.getCropBoxData()),this.render(),e.restore&&(this.setCanvasData(E(t,function(h,o){t[o]=h*s})),this.setCropBoxData(E(i,function(h,o){i[o]=h*s}))))}},dblclick:function(){var t,i;this.disabled||this.options.dragMode===se||this.setDragMode((t=this.dragBox,i=Nt,(t.classList?t.classList.contains(i):t.className.indexOf(i)>-1)?oe:Lt))},wheel:function(t){var i=this,e=Number(this.options.wheelZoomRatio)||.1,n=1;this.disabled||(t.preventDefault(),this.wheeling||(this.wheeling=!0,setTimeout(function(){i.wheeling=!1},50),t.deltaY?n=t.deltaY>0?1:-1:t.wheelDelta?n=-t.wheelDelta/120:t.detail&&(n=t.detail>0?1:-1),this.zoom(-n*e,t)))},cropStart:function(t){var i=t.buttons,e=t.button;if(!(this.disabled||(t.type==="mousedown"||t.type==="pointerdown"&&t.pointerType==="mouse")&&(x(i)&&i!==1||x(e)&&e!==0||t.ctrlKey))){var n,a=this.options,r=this.pointers;t.changedTouches?E(t.changedTouches,function(c){r[c.identifier]=kt(c)}):r[t.pointerId||0]=kt(t),n=Object.keys(r).length>1&&a.zoomable&&a.zoomOnTouch?ae:Ut(t.target,bt),li.test(n)&&ct(this.element,At,{originalEvent:t,action:n})!==!1&&(t.preventDefault(),this.action=n,this.cropping=!1,n===ee&&(this.cropping=!0,z(this.dragBox,Ct)))}},cropMove:function(t){var i=this.action;if(!this.disabled&&i){var e=this.pointers;t.preventDefault(),ct(this.element,Xt,{originalEvent:t,action:i})!==!1&&(t.changedTouches?E(t.changedTouches,function(n){_(e[n.identifier]||{},kt(n,!0))}):_(e[t.pointerId||0]||{},kt(t,!0)),this.change(t))}},cropEnd:function(t){if(!this.disabled){var i=this.action,e=this.pointers;t.changedTouches?E(t.changedTouches,function(n){delete e[n.identifier]}):delete e[t.pointerId||0],i&&(t.preventDefault(),Object.keys(e).length||(this.action=""),this.cropping&&(this.cropping=!1,ht(this.dragBox,Ct,this.cropped&&this.options.modal)),ct(this.element,Yt,{originalEvent:t,action:i}))}}},Oi={change:function(t){var i,e=this.options,n=this.canvasData,a=this.containerData,r=this.cropBoxData,c=this.pointers,s=this.action,h=e.aspectRatio,o=r.left,u=r.top,p=r.width,l=r.height,m=o+p,f=u+l,g=0,b=0,C=a.width,y=a.height,D=!0;!h&&t.shiftKey&&(h=p&&l?p/l:1),this.limited&&(g=r.minLeft,b=r.minTop,C=g+Math.min(a.width,n.width,n.left+n.width),y=b+Math.min(a.height,n.height,n.top+n.height));var M=c[Object.keys(c)[0]],d={x:M.endX-M.startX,y:M.endY-M.startY},w=function(k){switch(k){case J:m+d.x>C&&(d.x=C-m);break;case tt:o+d.x<g&&(d.x=g-o);break;case Q:u+d.y<b&&(d.y=b-u);break;case rt:f+d.y>y&&(d.y=y-f)}};switch(s){case Ht:o+=d.x,u+=d.y;break;case J:if(d.x>=0&&(m>=C||h&&(u<=b||f>=y))){D=!1;break}w(J),(p+=d.x)<0&&(s=tt,o-=p=-p),h&&(l=p/h,u+=(r.height-l)/2);break;case Q:if(d.y<=0&&(u<=b||h&&(o<=g||m>=C))){D=!1;break}w(Q),l-=d.y,u+=d.y,l<0&&(s=rt,u-=l=-l),h&&(p=l*h,o+=(r.width-p)/2);break;case tt:if(d.x<=0&&(o<=g||h&&(u<=b||f>=y))){D=!1;break}w(tt),p-=d.x,o+=d.x,p<0&&(s=J,o-=p=-p),h&&(l=p/h,u+=(r.height-l)/2);break;case rt:if(d.y>=0&&(f>=y||h&&(o<=g||m>=C))){D=!1;break}w(rt),(l+=d.y)<0&&(s=Q,u-=l=-l),h&&(p=l*h,o+=(r.width-p)/2);break;case mt:if(h){if(d.y<=0&&(u<=b||m>=C)){D=!1;break}w(Q),l-=d.y,u+=d.y,p=l*h}else w(Q),w(J),d.x>=0?m<C?p+=d.x:d.y<=0&&u<=b&&(D=!1):p+=d.x,d.y<=0?u>b&&(l-=d.y,u+=d.y):(l-=d.y,u+=d.y);p<0&&l<0?(s=vt,u-=l=-l,o-=p=-p):p<0?(s=gt,o-=p=-p):l<0&&(s=ft,u-=l=-l);break;case gt:if(h){if(d.y<=0&&(u<=b||o<=g)){D=!1;break}w(Q),l-=d.y,u+=d.y,p=l*h,o+=r.width-p}else w(Q),w(tt),d.x<=0?o>g?(p-=d.x,o+=d.x):d.y<=0&&u<=b&&(D=!1):(p-=d.x,o+=d.x),d.y<=0?u>b&&(l-=d.y,u+=d.y):(l-=d.y,u+=d.y);p<0&&l<0?(s=ft,u-=l=-l,o-=p=-p):p<0?(s=mt,o-=p=-p):l<0&&(s=vt,u-=l=-l);break;case vt:if(h){if(d.x<=0&&(o<=g||f>=y)){D=!1;break}w(tt),p-=d.x,o+=d.x,l=p/h}else w(rt),w(tt),d.x<=0?o>g?(p-=d.x,o+=d.x):d.y>=0&&f>=y&&(D=!1):(p-=d.x,o+=d.x),d.y>=0?f<y&&(l+=d.y):l+=d.y;p<0&&l<0?(s=mt,u-=l=-l,o-=p=-p):p<0?(s=ft,o-=p=-p):l<0&&(s=gt,u-=l=-l);break;case ft:if(h){if(d.x>=0&&(m>=C||f>=y)){D=!1;break}w(J),l=(p+=d.x)/h}else w(rt),w(J),d.x>=0?m<C?p+=d.x:d.y>=0&&f>=y&&(D=!1):p+=d.x,d.y>=0?f<y&&(l+=d.y):l+=d.y;p<0&&l<0?(s=gt,u-=l=-l,o-=p=-p):p<0?(s=vt,o-=p=-p):l<0&&(s=mt,u-=l=-l);break;case ie:this.move(d.x,d.y),D=!1;break;case ae:this.zoom(function(k){var Y=Zt({},k),N=0;return E(k,function(W,G){delete Y[G],E(Y,function(H){var T=Math.abs(W.startX-H.startX),xt=Math.abs(W.startY-H.startY),it=Math.abs(W.endX-H.endX),lt=Math.abs(W.endY-H.endY),V=Math.sqrt(T*T+xt*xt),dt=(Math.sqrt(it*it+lt*lt)-V)/V;Math.abs(dt)>Math.abs(N)&&(N=dt)})}),N}(c),t),D=!1;break;case ee:if(!d.x||!d.y){D=!1;break}i=xe(this.cropper),o=M.startX-i.left,u=M.startY-i.top,p=r.minWidth,l=r.minHeight,d.x>0?s=d.y>0?ft:mt:d.x<0&&(o-=p,s=d.y>0?vt:gt),d.y<0&&(u-=l),this.cropped||(U(this.cropBox,R),this.cropped=!0,this.limited&&this.limitCropBox(!0,!0))}D&&(r.width=p,r.height=l,r.left=o,r.top=u,this.action=s,this.renderCropBox()),E(c,function(k){k.startX=k.endX,k.startY=k.endY})}},_i={crop:function(){return!this.ready||this.cropped||this.disabled||(this.cropped=!0,this.limitCropBox(!0,!0),this.options.modal&&z(this.dragBox,Ct),U(this.cropBox,R),this.setCropBoxData(this.initialCropBoxData)),this},reset:function(){return this.ready&&!this.disabled&&(this.imageData=_({},this.initialImageData),this.canvasData=_({},this.initialCanvasData),this.cropBoxData=_({},this.initialCropBoxData),this.renderCanvas(),this.cropped&&this.renderCropBox()),this},clear:function(){return this.cropped&&!this.disabled&&(_(this.cropBoxData,{left:0,top:0,width:0,height:0}),this.cropped=!1,this.renderCropBox(),this.limitCanvas(!0,!0),this.renderCanvas(),U(this.dragBox,Ct),z(this.cropBox,R)),this},replace:function(t){var i=arguments.length>1&&arguments[1]!==void 0&&arguments[1];return!this.disabled&&t&&(this.isImg&&(this.element.src=t),i?(this.url=t,this.image.src=t,this.ready&&(this.viewBoxImage.src=t,E(this.previews,function(e){e.getElementsByTagName("img")[0].src=t}))):(this.isImg&&(this.replaced=!0),this.options.data=null,this.uncreate(),this.load(t))),this},enable:function(){return this.ready&&this.disabled&&(this.disabled=!1,U(this.cropper,ne)),this},disable:function(){return this.ready&&!this.disabled&&(this.disabled=!0,z(this.cropper,ne)),this},destroy:function(){var t=this.element;return t[O]?(t[O]=void 0,this.isImg&&this.replaced&&(t.src=this.originalUrl),this.uncreate(),this):this},move:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.canvasData,n=e.left,a=e.top;return this.moveTo(jt(t)?t:n+Number(t),jt(i)?i:a+Number(i))},moveTo:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.canvasData,n=!1;return t=Number(t),i=Number(i),this.ready&&!this.disabled&&this.options.movable&&(x(t)&&(e.left=t,n=!0),x(i)&&(e.top=i,n=!0),n&&this.renderCanvas(!0)),this},zoom:function(t,i){var e=this.canvasData;return t=(t=Number(t))<0?1/(1-t):1+t,this.zoomTo(e.width*t/e.naturalWidth,null,i)},zoomTo:function(t,i,e){var n=this.options,a=this.canvasData,r=a.width,c=a.height,s=a.naturalWidth,h=a.naturalHeight;if((t=Number(t))>=0&&this.ready&&!this.disabled&&n.zoomable){var o=s*t,u=h*t;if(ct(this.element,It,{ratio:t,oldRatio:r/s,originalEvent:e})===!1)return this;if(e){var p=this.pointers,l=xe(this.cropper),m=p&&Object.keys(p).length?function(f){var g=0,b=0,C=0;return E(f,function(y){var D=y.startX,M=y.startY;g+=D,b+=M,C+=1}),{pageX:g/=C,pageY:b/=C}}(p):{pageX:e.pageX,pageY:e.pageY};a.left-=(o-r)*((m.pageX-l.left-a.left)/r),a.top-=(u-c)*((m.pageY-l.top-a.top)/c)}else ot(i)&&x(i.x)&&x(i.y)?(a.left-=(o-r)*((i.x-a.left)/r),a.top-=(u-c)*((i.y-a.top)/c)):(a.left-=(o-r)/2,a.top-=(u-c)/2);a.width=o,a.height=u,this.renderCanvas(!0)}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t))},rotateTo:function(t){return x(t=Number(t))&&this.ready&&!this.disabled&&this.options.rotatable&&(this.imageData.rotate=t%360,this.renderCanvas(!0,!0)),this},scaleX:function(t){var i=this.imageData.scaleY;return this.scale(t,x(i)?i:1)},scaleY:function(t){var i=this.imageData.scaleX;return this.scale(x(i)?i:1,t)},scale:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.imageData,n=!1;return t=Number(t),i=Number(i),this.ready&&!this.disabled&&this.options.scalable&&(x(t)&&(e.scaleX=t,n=!0),x(i)&&(e.scaleY=i,n=!0),n&&this.renderCanvas(!0,!0)),this},getData:function(){var t,i=arguments.length>0&&arguments[0]!==void 0&&arguments[0],e=this.options,n=this.imageData,a=this.canvasData,r=this.cropBoxData;if(this.ready&&this.cropped){t={x:r.left-a.left,y:r.top-a.top,width:r.width,height:r.height};var c=n.width/n.naturalWidth;if(E(t,function(o,u){t[u]=o/c}),i){var s=Math.round(t.y+t.height),h=Math.round(t.x+t.width);t.x=Math.round(t.x),t.y=Math.round(t.y),t.width=h-t.x,t.height=s-t.y}}else t={x:0,y:0,width:0,height:0};return e.rotatable&&(t.rotate=n.rotate||0),e.scalable&&(t.scaleX=n.scaleX||1,t.scaleY=n.scaleY||1),t},setData:function(t){var i=this.options,e=this.imageData,n=this.canvasData,a={};if(this.ready&&!this.disabled&&ot(t)){var r=!1;i.rotatable&&x(t.rotate)&&t.rotate!==e.rotate&&(e.rotate=t.rotate,r=!0),i.scalable&&(x(t.scaleX)&&t.scaleX!==e.scaleX&&(e.scaleX=t.scaleX,r=!0),x(t.scaleY)&&t.scaleY!==e.scaleY&&(e.scaleY=t.scaleY,r=!0)),r&&this.renderCanvas(!0,!0);var c=e.width/e.naturalWidth;x(t.x)&&(a.left=t.x*c+n.left),x(t.y)&&(a.top=t.y*c+n.top),x(t.width)&&(a.width=t.width*c),x(t.height)&&(a.height=t.height*c),this.setCropBoxData(a)}return this},getContainerData:function(){return this.ready?_({},this.containerData):{}},getImageData:function(){return this.sized?_({},this.imageData):{}},getCanvasData:function(){var t=this.canvasData,i={};return this.ready&&E(["left","top","width","height","naturalWidth","naturalHeight"],function(e){i[e]=t[e]}),i},setCanvasData:function(t){var i=this.canvasData,e=i.aspectRatio;return this.ready&&!this.disabled&&ot(t)&&(x(t.left)&&(i.left=t.left),x(t.top)&&(i.top=t.top),x(t.width)?(i.width=t.width,i.height=t.width/e):x(t.height)&&(i.height=t.height,i.width=t.height*e),this.renderCanvas(!0)),this},getCropBoxData:function(){var t,i=this.cropBoxData;return this.ready&&this.cropped&&(t={left:i.left,top:i.top,width:i.width,height:i.height}),t||{}},setCropBoxData:function(t){var i,e,n=this.cropBoxData,a=this.options.aspectRatio;return this.ready&&this.cropped&&!this.disabled&&ot(t)&&(x(t.left)&&(n.left=t.left),x(t.top)&&(n.top=t.top),x(t.width)&&t.width!==n.width&&(i=!0,n.width=t.width),x(t.height)&&t.height!==n.height&&(e=!0,n.height=t.height),a&&(i?n.height=n.width/a:e&&(n.width=n.height*a)),this.renderCropBox()),this},getCroppedCanvas:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!this.ready||!window.HTMLCanvasElement)return null;var i=this.canvasData,e=function(lt,V,dt,at){var We=V.aspectRatio,Ei=V.naturalWidth,zi=V.naturalHeight,He=V.rotate,Wi=He===void 0?0:He,Ne=V.scaleX,Hi=Ne===void 0?1:Ne,Re=V.scaleY,Ni=Re===void 0?1:Re,Le=dt.aspectRatio,Ri=dt.naturalWidth,Li=dt.naturalHeight,Se=at.fillColor,Si=Se===void 0?"transparent":Se,Ye=at.imageSmoothingEnabled,Yi=Ye===void 0||Ye,Xe=at.imageSmoothingQuality,Xi=Xe===void 0?"low":Xe,Ae=at.maxWidth,Ie=Ae===void 0?1/0:Ae,je=at.maxHeight,Pe=je===void 0?1/0:je,Ue=at.minWidth,$e=Ue===void 0?0:Ue,qe=at.minHeight,Ve=qe===void 0?0:qe,Bt=document.createElement("canvas"),j=Bt.getContext("2d"),Fe=Z({aspectRatio:Le,width:Ie,height:Pe}),Qe=Z({aspectRatio:Le,width:$e,height:Ve},"cover"),qt=Math.min(Fe.width,Math.max(Qe.width,Ri)),Vt=Math.min(Fe.height,Math.max(Qe.height,Li)),Ke=Z({aspectRatio:We,width:Ie,height:Pe}),Ze=Z({aspectRatio:We,width:$e,height:Ve},"cover"),Ge=Math.min(Ke.width,Math.max(Ze.width,Ei)),Je=Math.min(Ke.height,Math.max(Ze.height,zi)),Ai=[-Ge/2,-Je/2,Ge,Je];return Bt.width=st(qt),Bt.height=st(Vt),j.fillStyle=Si,j.fillRect(0,0,qt,Vt),j.save(),j.translate(qt/2,Vt/2),j.rotate(Wi*Math.PI/180),j.scale(Hi,Ni),j.imageSmoothingEnabled=Yi,j.imageSmoothingQuality=Xi,j.drawImage.apply(j,[lt].concat(te(Ai.map(function(Ii){return Math.floor(st(Ii))})))),j.restore(),Bt}(this.image,this.imageData,i,t);if(!this.cropped)return e;var n=this.getData(t.rounded),a=n.x,r=n.y,c=n.width,s=n.height,h=e.width/Math.floor(i.naturalWidth);h!==1&&(a*=h,r*=h,c*=h,s*=h);var o=c/s,u=Z({aspectRatio:o,width:t.maxWidth||1/0,height:t.maxHeight||1/0}),p=Z({aspectRatio:o,width:t.minWidth||0,height:t.minHeight||0},"cover"),l=Z({aspectRatio:o,width:t.width||(h!==1?e.width:c),height:t.height||(h!==1?e.height:s)}),m=l.width,f=l.height;m=Math.min(u.width,Math.max(p.width,m)),f=Math.min(u.height,Math.max(p.height,f));var g=document.createElement("canvas"),b=g.getContext("2d");g.width=st(m),g.height=st(f),b.fillStyle=t.fillColor||"transparent",b.fillRect(0,0,m,f);var C=t.imageSmoothingEnabled,y=C===void 0||C,D=t.imageSmoothingQuality;b.imageSmoothingEnabled=y,D&&(b.imageSmoothingQuality=D);var M,d,w,k,Y,N,W=e.width,G=e.height,H=a,T=r;H<=-c||H>W?(H=0,M=0,w=0,Y=0):H<=0?(w=-H,H=0,Y=M=Math.min(W,c+H)):H<=W&&(w=0,Y=M=Math.min(c,W-H)),M<=0||T<=-s||T>G?(T=0,d=0,k=0,N=0):T<=0?(k=-T,T=0,N=d=Math.min(G,s+T)):T<=G&&(k=0,N=d=Math.min(s,G-T));var xt=[H,T,M,d];if(Y>0&&N>0){var it=m/c;xt.push(w*it,k*it,Y*it,N*it)}return b.drawImage.apply(b,[e].concat(te(xt.map(function(lt){return Math.floor(st(lt))})))),g},setAspectRatio:function(t){var i=this.options;return this.disabled||jt(t)||(i.aspectRatio=Math.max(0,t)||NaN,this.ready&&(this.initCropBox(),this.cropped&&this.renderCropBox())),this},setDragMode:function(t){var i=this.options,e=this.dragBox,n=this.face;if(this.ready&&!this.disabled){var a=t===Lt,r=i.movable&&t===oe;t=a||r?t:se,i.dragMode=t,wt(e,bt,t),ht(e,Nt,a),ht(e,Rt,r),i.cropBoxMovable||(wt(n,bt,t),ht(n,Nt,a),ht(n,Rt,r))}return this}},Ti=P.Cropper,ke=function(){function t(a){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(function(c,s){if(!(c instanceof s))throw new TypeError("Cannot call a class as a function")}(this,t),!a||!ui.test(a.tagName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=a,this.options=_({},fe,ot(r)&&r),this.cropped=!1,this.disabled=!1,this.pointers={},this.ready=!1,this.reloading=!1,this.replaced=!1,this.sized=!1,this.sizing=!1,this.init()}return i=t,n=[{key:"noConflict",value:function(){return window.Cropper=Ti,t}},{key:"setDefaults",value:function(a){_(fe,ot(a)&&a)}}],(e=[{key:"init",value:function(){var a,r=this.element,c=r.tagName.toLowerCase();if(!r[O]){if(r[O]=this,c==="img"){if(this.isImg=!0,a=r.getAttribute("src")||"",this.originalUrl=a,!a)return;a=r.src}else c==="canvas"&&window.HTMLCanvasElement&&(a=r.toDataURL());this.load(a)}}},{key:"load",value:function(a){var r=this;if(a){this.url=a,this.imageData={};var c=this.element,s=this.options;if(s.rotatable||s.scalable||(s.checkOrientation=!1),s.checkOrientation&&window.ArrayBuffer)if(di.test(a))pi.test(a)?this.read((h=a.replace(xi,""),o=atob(h),u=new ArrayBuffer(o.length),E(p=new Uint8Array(u),function(f,g){p[g]=o.charCodeAt(g)}),u)):this.clone();else{var h,o,u,p,l=new XMLHttpRequest,m=this.clone.bind(this);this.reloading=!0,this.xhr=l,l.onabort=m,l.onerror=m,l.ontimeout=m,l.onprogress=function(){l.getResponseHeader("content-type")!==ge&&l.abort()},l.onload=function(){r.read(l.response)},l.onloadend=function(){r.reloading=!1,r.xhr=null},s.checkCrossOrigin&&Me(a)&&c.crossOrigin&&(a=Ce(a)),l.open("GET",a,!0),l.responseType="arraybuffer",l.withCredentials=c.crossOrigin==="use-credentials",l.send()}else this.clone()}}},{key:"read",value:function(a){var r=this.options,c=this.imageData,s=Mi(a),h=0,o=1,u=1;if(s>1){this.url=function(l,m){for(var f=[],g=new Uint8Array(l);g.length>0;)f.push(De.apply(null,be(g.subarray(0,8192)))),g=g.subarray(8192);return"data:".concat(m,";base64,").concat(btoa(f.join("")))}(a,ge);var p=function(l){var m=0,f=1,g=1;switch(l){case 2:f=-1;break;case 3:m=-180;break;case 4:g=-1;break;case 5:m=90,g=-1;break;case 6:m=90;break;case 7:m=90,f=-1;break;case 8:m=-90}return{rotate:m,scaleX:f,scaleY:g}}(s);h=p.rotate,o=p.scaleX,u=p.scaleY}r.rotatable&&(c.rotate=h),r.scalable&&(c.scaleX=o,c.scaleY=u),this.clone()}},{key:"clone",value:function(){var a=this.element,r=this.url,c=a.crossOrigin,s=r;this.options.checkCrossOrigin&&Me(r)&&(c||(c="anonymous"),s=Ce(r)),this.crossOrigin=c,this.crossOriginUrl=s;var h=document.createElement("img");c&&(h.crossOrigin=c),h.src=s||r,h.alt=a.alt||"The image to crop",this.image=h,h.onload=this.start.bind(this),h.onerror=this.stop.bind(this),z(h,re),a.parentNode.insertBefore(h,a.nextSibling)}},{key:"start",value:function(){var a=this,r=this.image;r.onload=null,r.onerror=null,this.sizing=!0;var c=P.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(P.navigator.userAgent),s=function(u,p){_(a.imageData,{naturalWidth:u,naturalHeight:p,aspectRatio:u/p}),a.initialImageData=_({},a.imageData),a.sizing=!1,a.sized=!0,a.build()};if(!r.naturalWidth||c){var h=document.createElement("img"),o=document.body||document.documentElement;this.sizingImage=h,h.onload=function(){s(h.width,h.height),c||o.removeChild(h)},h.src=r.src,c||(h.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",o.appendChild(h))}else s(r.naturalWidth,r.naturalHeight)}},{key:"stop",value:function(){var a=this.image;a.onload=null,a.onerror=null,a.parentNode.removeChild(a),this.image=null}},{key:"build",value:function(){if(this.sized&&!this.ready){var a=this.element,r=this.options,c=this.image,s=a.parentNode,h=document.createElement("div");h.innerHTML='<div class="cropper-container" touch-action="none"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-cropper-action="e"></span><span class="cropper-line line-n" data-cropper-action="n"></span><span class="cropper-line line-w" data-cropper-action="w"></span><span class="cropper-line line-s" data-cropper-action="s"></span><span class="cropper-point point-e" data-cropper-action="e"></span><span class="cropper-point point-n" data-cropper-action="n"></span><span class="cropper-point point-w" data-cropper-action="w"></span><span class="cropper-point point-s" data-cropper-action="s"></span><span class="cropper-point point-ne" data-cropper-action="ne"></span><span class="cropper-point point-nw" data-cropper-action="nw"></span><span class="cropper-point point-sw" data-cropper-action="sw"></span><span class="cropper-point point-se" data-cropper-action="se"></span></div></div>';var o=h.querySelector(".".concat(O,"-container")),u=o.querySelector(".".concat(O,"-canvas")),p=o.querySelector(".".concat(O,"-drag-box")),l=o.querySelector(".".concat(O,"-crop-box")),m=l.querySelector(".".concat(O,"-face"));this.container=s,this.cropper=o,this.canvas=u,this.dragBox=p,this.cropBox=l,this.viewBox=o.querySelector(".".concat(O,"-view-box")),this.face=m,u.appendChild(c),z(a,R),s.insertBefore(o,a.nextSibling),U(c,re),this.initPreview(),this.bind(),r.initialAspectRatio=Math.max(0,r.initialAspectRatio)||NaN,r.aspectRatio=Math.max(0,r.aspectRatio)||NaN,r.viewMode=Math.max(0,Math.min(3,Math.round(r.viewMode)))||0,z(l,R),r.guides||z(l.getElementsByClassName("".concat(O,"-dashed")),R),r.center||z(l.getElementsByClassName("".concat(O,"-center")),R),r.background&&z(o,"".concat(O,"-bg")),r.highlight||z(m,ci),r.cropBoxMovable&&(z(m,Rt),wt(m,bt,Ht)),r.cropBoxResizable||(z(l.getElementsByClassName("".concat(O,"-line")),R),z(l.getElementsByClassName("".concat(O,"-point")),R)),this.render(),this.ready=!0,this.setDragMode(r.dragMode),r.autoCrop&&this.crop(),this.setData(r.data),L(r.ready)&&X(a,pe,r.ready,{once:!0}),ct(a,pe)}}},{key:"unbuild",value:function(){if(this.ready){this.ready=!1,this.unbind(),this.resetPreview();var a=this.cropper.parentNode;a&&a.removeChild(this.cropper),U(this.element,R)}}},{key:"uncreate",value:function(){this.ready?(this.unbuild(),this.ready=!1,this.cropped=!1):this.sizing?(this.sizingImage.onload=null,this.sizing=!1,this.sized=!1):this.reloading?(this.xhr.onabort=null,this.xhr.abort()):this.image&&this.stop()}}])&&Jt(i.prototype,e),n&&Jt(i,n),Object.defineProperty(i,"prototype",{writable:!1}),i;var i,e,n}();_(ke.prototype,Ci,Di,ki,Bi,Oi,_i);let Be,Oe,_e,Te,Ee,ze;Be=["alt","crossorigin","src"],Oe=Ot({name:"Cropper",__name:"Cropper",props:{src:A.string.def(""),alt:A.string.def(""),circled:A.bool.def(!1),realTimePreview:A.bool.def(!0),height:A.string.def("360px"),crossorigin:{type:String,default:void 0},imageStyle:{type:Object,default:()=>({})},options:{type:Object,default:()=>({})}},emits:["cropend","ready","cropendError"],setup(t,{emit:i}){const e={aspectRatio:1,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,autoCrop:!0,background:!0,highlight:!0,center:!0,responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,scalable:!0,modal:!0,guides:!0,movable:!0,rotatable:!0},n=t,a=i,r=ji(),c=$(),s=$(),h=$(!1),{getPrefixCls:o}=Qt(),u=o("cropper-image"),p=Vi(g,80),l=Ft(()=>({height:n.height,maxWidth:"100%",...n.imageStyle})),m=Ft(()=>[u,r.class,{[`${u}--circled`]:n.circled}]),f=Ft(()=>({height:`${n.height}`.replace(/px/,"")+"px"}));function g(){n.realTimePreview&&function(){if(!s.value)return;let b=s.value.getData();(n.circled?function(){const C=s.value.getCroppedCanvas(),y=document.createElement("canvas"),D=y.getContext("2d"),M=C.width,d=C.height;return y.width=M,y.height=d,D.imageSmoothingEnabled=!0,D.drawImage(C,0,0,M,d),D.globalCompositeOperation="destination-in",D.beginPath(),D.arc(M/2,d/2,Math.min(M,d)/2,0,2*Math.PI,!0),D.fill(),y}():s.value.getCroppedCanvas()).toBlob(C=>{if(!C)return;let y=new FileReader;y.readAsDataURL(C),y.onloadend=D=>{var M;a("cropend",{imgBase64:((M=D.target)==null?void 0:M.result)??"",imgInfo:b})},y.onerror=()=>{a("cropendError")}},"image/png")}()}return Pi(async function(){const b=v(c);b&&(s.value=new ke(b,{...e,ready:()=>{h.value=!0,g(),a("ready",s.value)},crop(){p()},zoom(){p()},cropmove(){p()},...n.options}))}),Ui(()=>{var b;(b=s.value)==null||b.destroy()}),(b,C)=>(q(),pt("div",{class:F(v(m)),style:ti(v(f))},[$i(nt("img",{ref_key:"imgElRef",ref:c,alt:t.alt,crossorigin:t.crossorigin,src:t.src,style:ti(v(l))},null,12,Be),[[qi,v(h)]])],6))}}),_e=["alt","src"],Te=Ot({name:"CopperModal",__name:"CopperModal",props:{srcValue:A.string.def(""),circled:A.bool.def(!0)},emits:["uploadSuccess"],setup(t,{expose:i,emit:e}){const n=t,a=e,{t:r}=ei.useI18n(),{getPrefixCls:c}=Qt(),s=c("cropper-am"),h=$(n.srcValue),o=$(""),u=$(),p=$(!1);let l="",m=1,f=1;function g(M){const d=new FileReader;return d.readAsDataURL(M),h.value="",o.value="",d.onload=function(w){var k;h.value=((k=w.target)==null?void 0:k.result)??"",l=M.name},!1}function b({imgBase64:M}){o.value=M}function C(M){u.value=M}function y(M,d){var w,k;M==="scaleX"&&(m=d=m===-1?1:-1),M==="scaleY"&&(f=d=f===-1?1:-1),(k=(w=u==null?void 0:u.value)==null?void 0:w[M])==null||k.call(w,d)}async function D(){const M=(d=>{const w=d.split(","),k=w[0].match(/:(.*?);/)[1],Y=window.atob(w[1]);let N=Y.length;const W=new Uint8Array(N);for(;N--;)W[N]=Y.charCodeAt(N);return new Blob([W],{type:k})})(o.value);a("uploadSuccess",{source:o.value,data:M,filename:l})}return i({openModal:function(){p.value=!0},closeModal:function(){p.value=!1}}),(M,d)=>{const w=ca,k=Qi,Y=Ki,N=sa,W=oi,G=ni,H=ra;return q(),pt("div",null,[B(H,{modelValue:v(p),"onUpdate:modelValue":d[7]||(d[7]=T=>Fi(p)?p.value=T:null),canFullscreen:!1,title:v(r)("cropper.modalTitle"),maxHeight:"380px",width:"800px"},{footer:S(()=>[B(G,{type:"primary",onClick:D},{default:S(()=>[ii(ai(v(r)("cropper.okText")),1)]),_:1})]),default:S(()=>[nt("div",{class:F(v(s))},[nt("div",{class:F(`${v(s)}-left`)},[nt("div",{class:F(`${v(s)}-cropper`)},[v(h)?(q(),_t(v(Oe),{key:0,circled:t.circled,src:v(h),height:"300px",onCropend:b,onReady:C},null,8,["circled","src"])):ut("",!0)],2),nt("div",{class:F(`${v(s)}-toolbar`)},[B(Y,{beforeUpload:g,fileList:[],accept:"image/*"},{default:S(()=>[B(k,{content:v(r)("cropper.selectImage"),placement:"bottom"},{default:S(()=>[B(w,{preIcon:"ant-design:upload-outlined",type:"primary"})]),_:1},8,["content"])]),_:1}),B(N,null,{default:S(()=>[B(k,{content:v(r)("cropper.btn_reset"),placement:"bottom"},{default:S(()=>[B(w,{disabled:!v(h),preIcon:"ant-design:reload-outlined",size:"small",type:"primary",onClick:d[0]||(d[0]=T=>y("reset"))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_rotate_left"),placement:"bottom"},{default:S(()=>[B(w,{disabled:!v(h),preIcon:"ant-design:rotate-left-outlined",size:"small",type:"primary",onClick:d[1]||(d[1]=T=>y("rotate",-45))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_rotate_right"),placement:"bottom"},{default:S(()=>[B(w,{disabled:!v(h),preIcon:"ant-design:rotate-right-outlined",size:"small",type:"primary",onClick:d[2]||(d[2]=T=>y("rotate",45))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_scale_x"),placement:"bottom"},{default:S(()=>[B(w,{disabled:!v(h),preIcon:"vaadin:arrows-long-h",size:"small",type:"primary",onClick:d[3]||(d[3]=T=>y("scaleX"))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_scale_y"),placement:"bottom"},{default:S(()=>[B(w,{disabled:!v(h),preIcon:"vaadin:arrows-long-v",size:"small",type:"primary",onClick:d[4]||(d[4]=T=>y("scaleY"))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_zoom_in"),placement:"bottom"},{default:S(()=>[B(w,{disabled:!v(h),preIcon:"ant-design:zoom-in-outlined",size:"small",type:"primary",onClick:d[5]||(d[5]=T=>y("zoom",.1))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_zoom_out"),placement:"bottom"},{default:S(()=>[B(w,{disabled:!v(h),preIcon:"ant-design:zoom-out-outlined",size:"small",type:"primary",onClick:d[6]||(d[6]=T=>y("zoom",-.1))},null,8,["disabled"])]),_:1},8,["content"])]),_:1})],2)],2),nt("div",{class:F(`${v(s)}-right`)},[nt("div",{class:F(`${v(s)}-preview`)},[v(o)?(q(),pt("img",{key:0,alt:v(r)("cropper.preview"),src:v(o)},null,8,_e)):ut("",!0)],2),v(o)?(q(),pt("div",{key:0,class:F(`${v(s)}-group`)},[B(W,{src:v(o),size:"large"},null,8,["src"]),B(W,{size:48,src:v(o)},null,8,["src"]),B(W,{size:64,src:v(o)},null,8,["src"]),B(W,{size:80,src:v(o)},null,8,["src"])],2)):ut("",!0)],2)],2)]),_:1},8,["modelValue","title"])])}}}),Ee=ri(Ot({name:"CropperAvatar",__name:"CropperAvatar",props:{width:A.string.def("200px"),value:A.string.def(""),showBtn:A.bool.def(!0),btnText:A.string.def("")},emits:["update:value","change"],setup(t,{expose:i,emit:e}){const n=t,a=e,r=$(n.value),{getPrefixCls:c}=Qt(),s=c("cropper-avatar"),h=Ji(),{t:o}=ei.useI18n(),u=$();function p({source:m,data:f,filename:g}){r.value=m,a("change",{source:m,data:f,filename:g}),h.success(o("cropper.uploadSuccess"))}function l(){u.value.openModal()}return Zi(()=>{r.value=n.value}),Gi(()=>r.value,m=>{a("update:value",m)}),i({open:l,close:function(){u.value.closeModal()}}),(m,f)=>{const g=oi,b=ni;return q(),pt("div",{class:"user-info-head",onClick:f[1]||(f[1]=C=>l())},[v(r)?(q(),_t(g,{key:0,src:v(r),alt:"avatar",class:"img-circle img-lg"},null,8,["src"])):ut("",!0),v(r)?ut("",!0):(q(),_t(g,{key:1,src:v(da),alt:"avatar",class:"img-circle img-lg"},null,8,["src"])),t.showBtn?(q(),_t(b,{key:2,class:F(`${v(s)}-upload-btn`),onClick:f[0]||(f[0]=C=>l())},{default:S(()=>[ii(ai(t.btnText?t.btnText:v(o)("cropper.selectImage")),1)]),_:1},8,["class"])):ut("",!0),B(Te,{ref_key:"cropperModelRef",ref:u,srcValue:v(r),onUploadSuccess:p},null,8,["srcValue"])])}}}),[["__scopeId","data-v-557ea3c3"]]),ze={class:"change-avatar"},si=ri(Ot({name:"UserAvatar",__name:"UserAvatar",props:{img:A.string.def("")},setup(t){const i=ta(),e=$(),n=async({data:a})=>{const r=await ia({avatarFile:a});e.value.close(),i.setUserAvatarAction(r.data)};return(a,r)=>(q(),pt("div",ze,[B(v(Ee),{ref_key:"cropperRef",ref:e,btnProps:{preIcon:"ant-design:cloud-upload-outlined"},showBtn:!1,value:t.img,width:"120px",onChange:n},null,8,["value"])]))}}),[["__scopeId","data-v-bb1e03cd"]])});export{pa as __tla,si as default};
