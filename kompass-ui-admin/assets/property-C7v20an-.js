import{_ as P,__tla as C}from"./ComponentContainerProperty-U-5gd_f0.js";import{d as I,o as h,l as V,w as r,i as e,a as l,g as b,j as L,a9 as j,_ as k,aM as D,aN as E,an as S,L as M,ai as N,ce as O,cl as W,cf as X,O as q,__tla as A}from"./index-BUSn51wb.js";import{_ as F,__tla as G}from"./index-11u3nuTi.js";import{E as H,__tla as J}from"./el-card-CJbXGyyg.js";import{u as K,__tla as Q}from"./util-Dyp86Gv2.js";import Y,{__tla as Z}from"./SpuShowcase-HyjHBJVE.js";import"./color-BN7ZL7BD.js";import{__tla as $}from"./Dialog.vue_vue_type_style_index_0_lang-Z7vvsdzS.js";import{__tla as ee}from"./Qrcode-CP7wmJi0.js";import{__tla as ae}from"./el-text-CIwNlU-U.js";import{__tla as le}from"./IFrame.vue_vue_type_script_setup_true_lang-silYuU_p.js";import{__tla as te}from"./vuedraggable.umd-BTL7hPHv.js";import{__tla as oe}from"./el-collapse-item-B_QvnH_b.js";import{__tla as re}from"./el-image-BjHZRFih.js";import{__tla as _e}from"./spu-CW3JGweV.js";import{__tla as se}from"./SpuTableSelect.vue_vue_type_script_setup_true_lang-lBhZ06IN.js";import{__tla as ue}from"./ContentWrap.vue_vue_type_script_setup_true_lang-CjmZFFVQ.js";import{__tla as de}from"./index.vue_vue_type_script_setup_true_lang-BqL8mSOW.js";import{__tla as me}from"./index-Cch5e1V0.js";import{__tla as pe}from"./el-tree-select-CBuha0HW.js";import"./tree-BMa075Oj.js";import{__tla as ne}from"./category-WzWM3ODe.js";let w,ce=Promise.all([(()=>{try{return C}catch{}})(),(()=>{try{return A}catch{}})(),(()=>{try{return G}catch{}})(),(()=>{try{return J}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return Z}catch{}})(),(()=>{try{return $}catch{}})(),(()=>{try{return ee}catch{}})(),(()=>{try{return ae}catch{}})(),(()=>{try{return le}catch{}})(),(()=>{try{return te}catch{}})(),(()=>{try{return oe}catch{}})(),(()=>{try{return re}catch{}})(),(()=>{try{return _e}catch{}})(),(()=>{try{return se}catch{}})(),(()=>{try{return ue}catch{}})(),(()=>{try{return de}catch{}})(),(()=>{try{return me}catch{}})(),(()=>{try{return pe}catch{}})(),(()=>{try{return ne}catch{}})()]).then(async()=>{let n,c;n={class:"flex gap-8px"},c={class:"flex gap-8px"},w=I({name:"ProductListProperty",__name:"property",props:{modelValue:{}},emits:["update:modelValue"],setup(g,{emit:U}){const x=g,y=U,{formData:a}=K(x.modelValue,y);return(ie,t)=>{const s=H,u=k,d=D,m=E,R=S,_=M,i=F,f=N,v=O,T=W,p=X,z=q,B=P;return h(),V(B,{modelValue:l(a).style,"onUpdate:modelValue":t[11]||(t[11]=o=>l(a).style=o)},{default:r(()=>[e(z,{"label-width":"80px",model:l(a)},{default:r(()=>[e(s,{header:"\u5546\u54C1\u5217\u8868",class:"property-group",shadow:"never"},{default:r(()=>[e(Y,{modelValue:l(a).spuIds,"onUpdate:modelValue":t[0]||(t[0]=o=>l(a).spuIds=o)},null,8,["modelValue"])]),_:1}),e(s,{header:"\u5546\u54C1\u6837\u5F0F",class:"property-group",shadow:"never"},{default:r(()=>[e(_,{label:"\u5E03\u5C40",prop:"type"},{default:r(()=>[e(R,{modelValue:l(a).layoutType,"onUpdate:modelValue":t[1]||(t[1]=o=>l(a).layoutType=o)},{default:r(()=>[e(m,{class:"item",content:"\u53CC\u5217",placement:"bottom"},{default:r(()=>[e(d,{label:"twoCol"},{default:r(()=>[e(u,{icon:"fluent:text-column-two-24-filled"})]),_:1})]),_:1}),e(m,{class:"item",content:"\u4E09\u5217",placement:"bottom"},{default:r(()=>[e(d,{label:"threeCol"},{default:r(()=>[e(u,{icon:"fluent:text-column-three-24-filled"})]),_:1})]),_:1}),e(m,{class:"item",content:"\u6C34\u5E73\u6ED1\u52A8",placement:"bottom"},{default:r(()=>[e(d,{label:"horizSwiper"},{default:r(()=>[e(u,{icon:"system-uicons:carousel"})]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}),e(_,{label:"\u5546\u54C1\u540D\u79F0",prop:"fields.name.show"},{default:r(()=>[b("div",n,[e(i,{modelValue:l(a).fields.name.color,"onUpdate:modelValue":t[2]||(t[2]=o=>l(a).fields.name.color=o)},null,8,["modelValue"]),e(f,{modelValue:l(a).fields.name.show,"onUpdate:modelValue":t[3]||(t[3]=o=>l(a).fields.name.show=o)},null,8,["modelValue"])])]),_:1}),e(_,{label:"\u5546\u54C1\u4EF7\u683C",prop:"fields.price.show"},{default:r(()=>[b("div",c,[e(i,{modelValue:l(a).fields.price.color,"onUpdate:modelValue":t[4]||(t[4]=o=>l(a).fields.price.color=o)},null,8,["modelValue"]),e(f,{modelValue:l(a).fields.price.show,"onUpdate:modelValue":t[5]||(t[5]=o=>l(a).fields.price.show=o)},null,8,["modelValue"])])]),_:1})]),_:1}),e(s,{header:"\u89D2\u6807",class:"property-group",shadow:"never"},{default:r(()=>[e(_,{label:"\u89D2\u6807",prop:"badge.show"},{default:r(()=>[e(v,{modelValue:l(a).badge.show,"onUpdate:modelValue":t[6]||(t[6]=o=>l(a).badge.show=o)},null,8,["modelValue"])]),_:1}),l(a).badge.show?(h(),V(_,{key:0,label:"\u89D2\u6807",prop:"badge.imgUrl"},{default:r(()=>[e(T,{modelValue:l(a).badge.imgUrl,"onUpdate:modelValue":t[7]||(t[7]=o=>l(a).badge.imgUrl=o),height:"44px",width:"72px"},{tip:r(()=>[L(" \u5EFA\u8BAE\u5C3A\u5BF8\uFF1A36 * 22 ")]),_:1},8,["modelValue"])]),_:1})):j("",!0)]),_:1}),e(s,{header:"\u5546\u54C1\u6837\u5F0F",class:"property-group",shadow:"never"},{default:r(()=>[e(_,{label:"\u4E0A\u5706\u89D2",prop:"borderRadiusTop"},{default:r(()=>[e(p,{modelValue:l(a).borderRadiusTop,"onUpdate:modelValue":t[8]||(t[8]=o=>l(a).borderRadiusTop=o),max:100,min:0,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"])]),_:1}),e(_,{label:"\u4E0B\u5706\u89D2",prop:"borderRadiusBottom"},{default:r(()=>[e(p,{modelValue:l(a).borderRadiusBottom,"onUpdate:modelValue":t[9]||(t[9]=o=>l(a).borderRadiusBottom=o),max:100,min:0,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"])]),_:1}),e(_,{label:"\u95F4\u9694",prop:"space"},{default:r(()=>[e(p,{modelValue:l(a).space,"onUpdate:modelValue":t[10]||(t[10]=o=>l(a).space=o),max:100,min:0,"show-input":"","input-size":"small","show-input-controls":!1},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}}})});export{ce as __tla,w as default};
