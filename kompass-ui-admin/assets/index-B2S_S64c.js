import{d as x,a$ as v,aB as g,i as t,r as c,at as I,C as A,o as u,l as B,w as F,g as l,av as i,a as n,c as d,F as V,k as W,t as j,b2 as q,__tla as G}from"./index-BUSn51wb.js";import{e as H,__tla as J}from"./couponTemplate-CyEEfDVt.js";import{e as E,f as K}from"./constants-A8BI3pz7.js";import{f as M,__tla as L}from"./formatTime-DWdBpgsM.js";let R,N=Promise.all([(()=>{try{return G}catch{}})(),(()=>{try{return J}catch{}})(),(()=>{try{return L}catch{}})()]).then(async()=>{let y,b,_,w,h,C,$,k,P,T;y=x({name:"CouponDiscount",props:{coupon:v()},setup(s){const e=s.coupon;let p=e.discountPercent+"",r=" \u6298";return e.discountType===E.PRICE.type&&(p=g(e.discountPrice),r=" \u5143"),()=>t("div",null,[t("span",{class:"text-20px font-bold"},[p]),t("span",null,[r])])}}),b=x({name:"CouponDiscountDesc",props:{coupon:v()},setup(s){const e=s.coupon,p=e.usePrice>0?`\u6EE1${g(e.usePrice)}\u5143\uFF0C`:"",r=e.discountType===E.PRICE.type?`\u51CF${g(e.discountPrice)}\u5143`:`\u6253${e.discountPercent}\u6298`;return()=>t("div",null,[t("span",null,[p]),t("span",null,[r])])}}),_=x({name:"CouponValidTerm",props:{coupon:v()},setup(s){const e=s.coupon,p=e.validityType===K.DATE.type?`\u6709\u6548\u671F\uFF1A${M(e.validStartTime,"YYYY-MM-DD")} \u81F3 ${M(e.validEndTime,"YYYY-MM-DD")}`:`\u9886\u53D6\u540E\u7B2C ${e.fixedStartTerm} - ${e.fixedEndTerm} \u5929\u5185\u53EF\u7528`;return()=>t("div",null,[p])}}),w={key:0,class:"m-l-16px flex flex-row justify-between p-8px"},h={class:"flex flex-col justify-evenly gap-4px"},C={class:"flex flex-col justify-evenly"},$={key:1,class:"m-l-16px flex flex-row justify-between p-8px"},k={class:"flex flex-col justify-evenly gap-4px"},P={class:"flex flex-col"},T={key:2,class:"flex flex-col items-center justify-around gap-4px p-4px"},R=x({name:"CouponCard",__name:"index",props:{property:{}},setup(s){const e=s,p=c([]);I(()=>e.property.couponIds,async()=>{var o;((o=e.property.couponIds)==null?void 0:o.length)>0&&(p.value=await H(e.property.couponIds))},{immediate:!0,deep:!0});const r=c(375),D=c(),Y=c("100%"),f=c(375);return I(()=>[e.property,r,p.value.length],()=>{f.value=(.95*r.value-e.property.space*(e.property.columns-1))/e.property.columns,Y.value=f.value*p.value.length+e.property.space*(p.value.length-1)+"px"},{immediate:!0,deep:!0}),A(()=>{var o,m;r.value=((m=(o=D.value)==null?void 0:o.wrapRef)==null?void 0:m.offsetWidth)||375}),(o,m)=>{const S=q;return u(),B(S,{class:"z-1 min-h-30px","wrap-class":"w-full",ref_key:"containerRef",ref:D},{default:F(()=>[l("div",{class:"flex flex-row text-12px",style:i({gap:`${o.property.space}px`,width:n(Y)})},[(u(!0),d(V,null,W(n(p),(a,z)=>(u(),d("div",{class:"box-content",style:i({background:o.property.bgImg?`url(${o.property.bgImg}) 100% center / 100% 100% no-repeat`:"#fff",width:`${n(f)}px`,color:o.property.textColor}),key:z},[o.property.columns===1?(u(),d("div",w,[l("div",h,[t(n(y),{coupon:a},null,8,["coupon"]),t(n(b),{coupon:a},null,8,["coupon"]),t(n(_),{coupon:a},null,8,["coupon"])]),l("div",C,[l("div",{class:"rounded-20px p-x-8px p-y-2px",style:i({color:o.property.button.color,background:o.property.button.bgColor})}," \u7ACB\u5373\u9886\u53D6 ",4)])])):o.property.columns===2?(u(),d("div",$,[l("div",k,[t(n(y),{coupon:a},null,8,["coupon"]),l("div",null,j(a.name),1)]),l("div",P,[l("div",{class:"h-full w-20px rounded-20px p-x-2px p-y-8px text-center",style:i({color:o.property.button.color,background:o.property.button.bgColor})}," \u7ACB\u5373\u9886\u53D6 ",4)])])):(u(),d("div",T,[t(n(y),{coupon:a},null,8,["coupon"]),l("div",null,j(a.name),1),l("div",{class:"rounded-20px p-x-8px p-y-2px",style:i({color:o.property.button.color,background:o.property.button.bgColor})}," \u7ACB\u5373\u9886\u53D6 ",4)]))],4))),128))],4)]),_:1},512)}}})});export{N as __tla,R as default};
